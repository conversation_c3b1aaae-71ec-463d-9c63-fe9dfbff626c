package com.xiao.temu.modules.sync.dto;

import lombok.Data;

import java.util.List;

/**
 * 销售数据同步DTO
 */
@Data
public class SalesSyncDTO {

    /**
     * 要同步的店铺ID列表
     */
    private List<Long> shopIds;

    /**
     * 分页页码
     */
    private Integer pageNum;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方向，"asc"升序，"desc"降序
     */
    private String sortOrder;
    
    /**
     * 线程数量
     */
    private Integer threadCount;
    
    /**
     * 是否全量同步,true为全量,false为增量
     */
    private Boolean isFullSync;
    
    /**
     * 全量同步时的最大同步数量限制
     */
    private Integer limit;
} 