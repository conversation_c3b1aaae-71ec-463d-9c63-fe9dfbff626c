package com.xiao.temu.modules.sync.job;

import com.xiao.temu.modules.sync.controller.SalesSyncController;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

/**
 * 销售数据同步定时任务
 */
@Slf4j
@Component
public class SalesSyncQuartzJob extends QuartzJobBean {

    @Autowired
    private SalesSyncController salesSyncController;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        String triggerName = context.getTrigger().getKey().getName();
        log.info("销售数据同步任务开始执行, 触发器: {}", triggerName);
        
        try {
            // 根据触发器名称判断是全量同步还是增量同步
            if ("salesFullSyncTrigger".equals(triggerName)) {
                salesSyncController.syncSalesDataFull();
            } else {
                salesSyncController.syncSalesDataIncremental();
            }
            log.info("销售数据同步任务执行成功, 触发器: {}", triggerName);
        } catch (Exception e) {
            log.error("销售数据同步任务执行失败, 触发器: {}, 错误: {}", triggerName, e.getMessage(), e);
            throw new JobExecutionException(e);
        }
    }
} 