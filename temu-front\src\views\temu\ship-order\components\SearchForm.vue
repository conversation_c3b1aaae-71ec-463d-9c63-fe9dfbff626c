<template>
  <div class="search-form-container">
    <el-card class="search-card" shadow="never" :body-style="{ padding: '5px 20px' }">
      <div class="search-container">
        <!-- 第一行 -->
        <div class="search-row">
          <!-- 店铺 -->
          <div class="search-item" v-if="showShopSelector">
            <div class="search-label">店铺</div>
            <el-select 
              v-model="queryParams.shopIds" 
              placeholder="请选择店铺" 
              clearable
              filterable
              multiple
              collapse-tags
              collapse-tags-tooltip
              size="small"
              class="search-input"
              @change="handleShopSelectChange"
              @clear="isAllSelected = false"
            >
              <!-- 全选选项 -->
              <!-- <el-option 
                key="all"
                label="全选"
                :value="'all'"
              /> -->
              <el-option 
                v-for="shop in shops" 
                :key="shop.shopId" 
                :label="`${shop.shopName}${shop.shopRemark ? ' (' + shop.shopRemark + ')' : ''}`" 
                :value="shop.shopId" 
              />
            </el-select>
          </div>
          
          <!-- 发货单号 -->
          <div class="search-item">
            <div class="search-label">发货单</div>
            <el-input 
              v-model="deliveryOrderSn" 
              placeholder="请输入发货单号，多个用逗号或空格分隔" 
              clearable 
              size="small"
              class="search-input"
              @blur="handleDeliveryOrderSnChange"
            />
          </div>
          
          <!-- 备货单号 -->
          <div class="search-item">
            <div class="search-label">备货单号</div>
            <el-input 
              v-model="subPurchaseOrderSn" 
              placeholder="请输入备货单号，多个用逗号或空格分隔" 
              clearable 
              size="small"
              class="search-input"
              @blur="handleSubPurchaseOrderSnChange"
            />
          </div>
          
          <!-- 物流单号 -->
          <div class="search-item">
            <div class="search-label">物流单号</div>
            <el-input 
              v-model="expressDeliverySn" 
              placeholder="请输入物流单号，多个用逗号或空格分隔" 
              clearable 
              size="small"
              class="search-input"
              @blur="handleExpressDeliverySnChange"
            />
          </div>
        </div>

        <!-- 第二行 -->
        <div class="search-row">
          <!-- 运单重量异常 -->
          <div class="search-item">
            <div class="search-label">运单重量异常</div>
            <el-select 
              v-model="queryParams.expressWeightFeedbackStatus" 
              placeholder="请选择异常状态" 
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              size="small"
              class="search-input"
            >
              <el-option :value="0" label="无异常" />
              <el-option :value="1" label="异常待确认" />
              <el-option :value="2" label="已提交异常反馈，待物流商处理" />
              <el-option :value="3" label="物流商处理完成" />
              <el-option :value="4" label="平台介入处理中" />
              <el-option :value="5" label="平台处理完成" />
              <el-option :value="6" label="卖家已确认" />
              <el-option :value="7" label="卖家超期自动确认" />
              <el-option :value="8" label="物流商介入处理，卖家确认或超时自动确认" />
              <el-option :value="9" label="结算消息驱动卖家确认" />
              <el-option :value="10" label="无需公示" />
              <el-option :value="11" label="结算物流单计算重量查询失败" />
              <el-option :value="12" label="结算理论计费重拦截" />
              <el-option :value="13" label="SKU重量体积拦截" />
            </el-select>
          </div>
          
          <!-- SKC -->
          <div class="search-item">
            <div class="search-label">SKC</div>
            <el-input 
              v-model="productSkcId" 
              placeholder="请输入SKC，多个用逗号或空格分隔" 
              clearable 
              size="small"
              class="search-input"
              @blur="handleProductSkcIdChange"
            />
          </div>
          
          <!-- 发货时间 -->
          <div class="search-item">
            <div class="search-label">发货时间</div>
            <el-date-picker
              v-model="deliverTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="x"
              size="small"
              class="search-input"
              @change="handleDeliverTimeChange"
            />
          </div>

          <!-- 是否加急 -->
          <div class="search-item">
            <div class="search-label">是否加急</div>
            <el-select 
              v-model="queryParams.urgencyType" 
              placeholder="请选择是否加急" 
              clearable
              size="small"
              class="search-input"
            >
              <el-option :value="0" label="普通" />
              <el-option :value="1" label="急采" />
            </el-select>
          </div>
        </div>

        <!-- 第三行 -->
        <div class="search-row">
          <!-- 收货仓库 -->
          <div class="search-item">
            <div class="search-label">收货仓库</div>
            <el-select 
              v-model="queryParams.subWarehouseIdList" 
              placeholder="请选择收货仓库" 
              clearable
              filterable
              multiple
              collapse-tags
              collapse-tags-tooltip
              size="small"
              class="search-input"
            >
              <el-option 
                v-for="warehouse in warehouseList" 
                :key="warehouse.id" 
                :label="`${warehouse.name} (${warehouse.receiveAddress})`" 
                :value="Number(warehouse.subWid)" 
              />
            </el-select>
          </div>
          
          <!-- 收货仓库地址 -->
          <div class="search-item">
            <div class="search-label">收货仓库地址</div>
            <el-input 
              v-model="queryParams.targetReceiveAddress" 
              placeholder="请输入收货仓库地址" 
              clearable 
              size="small"
              class="search-input"
            />
          </div>
          
          <!-- 发货仓库地址 -->
          <div class="search-item">
            <div class="search-label">发货仓库地址</div>
            <el-input 
              v-model="queryParams.targetDeliveryAddress" 
              placeholder="请输入发货仓库地址" 
              clearable 
              size="small"
              class="search-input"
            />
          </div>
          
          <!-- 是否定制品 -->
          <div class="search-item">
            <div class="search-label">是否定制品</div>
            <el-select 
              v-model="queryParams.isCustomProduct" 
              placeholder="请选择是否定制品" 
              clearable
              size="small"
              class="search-input"
            >
              <el-option :value="true" label="是" />
              <el-option :value="false" label="否" />
            </el-select>
          </div>
        </div>

        <!-- 第四行 -->
        <div class="search-row">
          <!-- 物流反馈异常状态 -->
          <div class="search-item">
            <div class="search-label">物流反馈异常状态</div>
            <el-select 
              v-model="queryParams.latestFeedbackStatusList" 
              placeholder="请选择异常状态" 
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              size="small"
              class="search-input"
            >
              <el-option :value="0" label="当前无异常" />
              <el-option :value="1" label="已提交" />
              <el-option :value="2" label="物流商处理中" />
              <el-option :value="4" label="已反馈" />
              <el-option :value="3" label="已撤销" />
            </el-select>
          </div>
          
          <!-- 备货区域 -->
          <div class="search-item">
            <div class="search-label">备货区域</div>
            <el-select 
              v-model="queryParams.inventoryRegion" 
              placeholder="请选择备货区域" 
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              size="small"
              class="search-input"
            >
              <el-option :value="1" label="国内备货" />
              <el-option :value="2" label="海外备货" />
              <el-option :value="3" label="保税仓备货" />
            </el-select>
          </div>
          
          <!-- 货号 -->
          <div class="search-item">
            <div class="search-label">货号</div>
            <el-input 
              v-model="skcExtCode" 
              placeholder="请输入货号，多个用逗号或空格分隔" 
              clearable 
              size="small"
              class="search-input"
              @blur="handleSkcExtCodeChange"
            />
          </div>

          <!-- 操作按钮 -->
          <div class="search-item search-buttons">
            <el-button type="primary" @click="handleQuery" size="small">
              <el-icon><Search /></el-icon>查询
            </el-button>
            <el-button @click="resetQuery" size="small">
              <el-icon><RefreshRight /></el-icon>重置
            </el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { Search, RefreshRight } from '@element-plus/icons-vue'
import type { Shop } from '@/types/shipOrder'
import type { WarehouseInfo } from '@/types/warehouse'
import { listAllWarehouses } from '@/api/temu/warehouse'
import { ElMessage } from 'element-plus'

// 定义props
const props = defineProps({
  shops: {
    type: Array as () => Shop[],
    default: () => []
  },
  isJit: {
    type: Boolean,
    default: false
  },
  showShopSelector: {
    type: Boolean,
    default: true
  }
})

// 定义emit
const emit = defineEmits(['query', 'reset'])

// 临时存储多值输入的字段
const deliveryOrderSn = ref('')
const subPurchaseOrderSn = ref('')
const expressDeliverySn = ref('')
const productSkcId = ref('')
const skcExtCode = ref('')
const deliverTimeRange = ref<[number, number] | null>(null)
// 全选标记
const isAllSelected = ref(false)

// 查询参数对象
const queryParams = reactive({
  shopIds: [] as (number | string)[],
  deliveryOrderSnList: [] as number[],
  subPurchaseOrderSnList: [] as string[],
  expressDeliverySnList: [] as string[],
  expressWeightFeedbackStatus: [] as number[],
  productSkcIdList: [] as number[],
  deliverTimeFrom: undefined as number | undefined,
  deliverTimeTo: undefined as number | undefined,
  urgencyType: undefined as number | undefined,
  subWarehouseIdList: [] as number[],
  targetReceiveAddress: undefined as string | undefined,
  targetDeliveryAddress: undefined as string | undefined,
  isCustomProduct: undefined as boolean | undefined,
  latestFeedbackStatusList: [] as number[],
  inventoryRegion: [] as number[],
  skcExtCodeList: [] as string[],
  isJit: props.isJit // 继承JIT标志
})

// 仓库列表
const warehouseList = ref<WarehouseInfo[]>([])

// 处理店铺选择变化
const handleShopSelectChange = (value: (string | number)[]) => {
  // 检查是否包含全选选项
  const allOptionIndex = value.indexOf('all')
  
  if (allOptionIndex > -1) {
    // 移除"all"选项，只保留真实的店铺ID
    value.splice(allOptionIndex, 1)
    
    // 判断当前是否处于全选状态
    if (isAllSelected.value) {
      // 如果已经是全选状态，此次操作视为取消全选
      queryParams.shopIds = []
      isAllSelected.value = false
    } else {
      // 否则执行全选
      queryParams.shopIds = props.shops.map(item => item.shopId)
      isAllSelected.value = true
    }
  } else {
    // 正常选择，直接使用传入的值
    queryParams.shopIds = value
    
    // 检查是否等于全选状态
    isAllSelected.value = props.shops.length > 0 && 
                       queryParams.shopIds.length === props.shops.length
  }
}

// 处理多值输入的方法，将逗号或空格分隔的字符串转换为数组
const splitMultipleValues = (input: string): string[] => {
  if (!input) return []
  return input.trim().split(/[,，\s]+/).filter(Boolean)
}

// 处理发货单号变化
const handleDeliveryOrderSnChange = () => {
  const values = splitMultipleValues(deliveryOrderSn.value)
  queryParams.deliveryOrderSnList = values.map(Number).filter(n => !isNaN(n))
}

// 处理备货单号变化
const handleSubPurchaseOrderSnChange = () => {
  queryParams.subPurchaseOrderSnList = splitMultipleValues(subPurchaseOrderSn.value)
}

// 处理物流单号变化
const handleExpressDeliverySnChange = () => {
  queryParams.expressDeliverySnList = splitMultipleValues(expressDeliverySn.value)
}

// 处理SKC变化
const handleProductSkcIdChange = () => {
  const values = splitMultipleValues(productSkcId.value)
  queryParams.productSkcIdList = values.map(Number).filter(n => !isNaN(n))
}

// 处理货号变化
const handleSkcExtCodeChange = () => {
  queryParams.skcExtCodeList = splitMultipleValues(skcExtCode.value)
}

// 处理发货时间变化
const handleDeliverTimeChange = (val: [number, number] | null) => {
  if (val) {
    queryParams.deliverTimeFrom = val[0]
    queryParams.deliverTimeTo = val[1]
  } else {
    queryParams.deliverTimeFrom = undefined
    queryParams.deliverTimeTo = undefined
  }
}

// 处理查询
const handleQuery = () => {
  emit('query', { ...queryParams })
}

// 重置查询
const resetQuery = () => {
  // 重置所有临时值
  deliveryOrderSn.value = ''
  subPurchaseOrderSn.value = ''
  expressDeliverySn.value = ''
  productSkcId.value = ''
  skcExtCode.value = ''
  deliverTimeRange.value = null
  isAllSelected.value = false
  
  // 重置查询参数
  queryParams.shopIds = []
  queryParams.deliveryOrderSnList = []
  queryParams.subPurchaseOrderSnList = []
  queryParams.expressDeliverySnList = []
  queryParams.expressWeightFeedbackStatus = []
  queryParams.productSkcIdList = []
  queryParams.deliverTimeFrom = undefined
  queryParams.deliverTimeTo = undefined
  queryParams.urgencyType = undefined
  queryParams.subWarehouseIdList = []
  queryParams.targetReceiveAddress = undefined
  queryParams.targetDeliveryAddress = undefined
  queryParams.isCustomProduct = undefined
  queryParams.latestFeedbackStatusList = []
  queryParams.inventoryRegion = []
  queryParams.skcExtCodeList = []
  
  emit('reset')
}

// 监听isJit属性变化
watch(() => props.isJit, (newVal) => {
  queryParams.isJit = newVal
})

// 获取仓库列表
const fetchWarehouseList = async () => {
  try {
    const response = await listAllWarehouses()
    if (response && response.data) {
      warehouseList.value = response.data
    } else {
      console.error('获取仓库列表失败', response)
    }
  } catch (error) {
    console.error('获取仓库列表异常', error)
    ElMessage.error('获取仓库列表失败')
  }
}

// 组件挂载时获取仓库列表
onMounted(() => {
  fetchWarehouseList()
})
</script>

<style scoped>
.search-form-container {
  margin-bottom: 0;
}

.search-card {
  border-radius: 4px 4px 0 0;
  margin-bottom: 0;
  border-bottom: none;
}

.search-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px 0;
}

.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.search-item {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 250px;
  max-width: calc(25% - 12px);
  margin-bottom: 5px;
}

.search-label {
  width: 100px;
  text-align: right;
  margin-right: 10px;
  white-space: nowrap;
  color: #606266;
  font-size: 14px;
}

.search-input {
  flex: 1;
  width: 100%;
}

.search-buttons {
  justify-content: flex-end;
  display: flex;
  gap: 10px;
  flex: 1;
}

.warehouse-select-dropdown .el-select-dropdown__item {
  padding: 8px 10px;
  white-space: normal;
  height: auto;
  line-height: 1.5;
}

.warehouse-select-dropdown .el-select-dropdown__item .warehouse-option {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.warehouse-select-dropdown .el-select-dropdown__item .warehouse-name {
  font-weight: bold;
}

.warehouse-select-dropdown .el-select-dropdown__item .warehouse-address {
  font-size: 0.9em;
  color: #909399;
  margin-top: 2px;
}
</style> 