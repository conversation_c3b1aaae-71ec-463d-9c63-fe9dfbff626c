package com.xiao.temu.modules.sales.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.sales.entity.SalesWarehouseInventory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 销售仓库库存详情Mapper接口
 */
@Mapper
public interface SalesWarehouseInventoryMapper extends BaseMapper<SalesWarehouseInventory> {
    
    /**
     * 统计指定店铺的仓库库存记录数
     * 
     * @param shopId 店铺ID
     * @return 记录数
     */
    @Select("SELECT COUNT(*) FROM sales_warehouse_inventory WHERE shop_id = #{shopId}")
    Integer countByShopId(@Param("shopId") Long shopId);
} 