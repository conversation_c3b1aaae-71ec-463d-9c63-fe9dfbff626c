<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.violation.mapper.ShopViolationDetailMapper">

    <select id="checkDetailExists" resultType="java.lang.Integer">
        select count(1) from shop_violation_detail where shop_id = #{shopId} and punish_sn = #{punishSn} limit 1
    </select>
    
    <delete id="deleteByShopIdAndPunishSn">
        delete from shop_violation_detail where shop_id = #{shopId} and punish_sn = #{punishSn}
    </delete>
    
</mapper> 