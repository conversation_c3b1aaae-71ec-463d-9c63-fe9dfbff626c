<template>
  <!-- 紧急备货建议表格 -->
  <TableCard class="table-card">
    <!-- 表格容器 -->
    <div class="table-wrapper">
      <el-table v-loading="loading" :data="flattenedPurchaseOrders" border style="width: 100%;" size="small"
        class="compact-table" :header-cell-style="{ backgroundColor: '#f5f7fa' }" :height="'auto'"
        :span-method="spanMethod" @selection-change="handleSelectionChange">
        <template #empty>
          <EmptyTips :message="hasSearched ? '没有查询到数据' : '请选择店铺后点击搜索按钮查询数据'" />
        </template>
        <!-- 修改：将序号列改为多选框 -->
        <el-table-column type="selection" width="50" align="center" :selectable="isRowSelectable" />
        <el-table-column label="店铺信息" width="140">
          <template #default="scope">
            <div class="shop-info">
              <div class="shop-name">{{ scope.row.shopName }}</div>
              <div class="shop-remark" style="color: #409EFF; font-size: 13px;">{{ scope.row.shopRemark }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="备货单号" width="220">
          <template #default="scope">
            <div class="purchase-order-container">
              <!-- 备货单号 -->
              <div class="purchase-order-sn">
                {{ scope.row.subPurchaseOrderSn }}
                <el-tooltip content="复制备货单号" placement="top" effect="light">
                  <i class="el-icon-document-copy copy-icon" @click="copyToClipboard(scope.row.subPurchaseOrderSn)"></i>
                </el-tooltip>
              </div>

              <!-- 状态标签 -->
              <div class="order-tags">
                <!-- 修改：使用 lackOrSoldOutTagList 判断并添加 tooltip -->
                <el-tooltip v-if="scope.row.lackOrSoldOutTagList && scope.row.lackOrSoldOutTagList.length > 0"
                  placement="top" effect="light" popper-class="sku-lack-tooltip">
                  <template #content>
                    <div class="sku-lack-tooltip-content">
                      <div class="tooltip-title">此备货单创建时，包含的SKU存在缺货/售罄的情况，缺货/售罄SKU如下</div>
                      <table class="tooltip-table">
                        <thead>
                          <tr>
                            <th>SKU属性</th>
                            <th>缺货情况</th>
                            <th>售罄情况</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(item, index) in scope.row.lackOrSoldOutTagList" :key="index">
                            <td>{{ item.skuDisplay }}</td>
                            <td :class="{ 'is-lack': item.isLack }">{{ item.isLack ? '缺货' : '-' }}</td>
                            <!-- 假设 soldOut 为 true 表示售罄 -->
                            <td>{{ item.soldOut ? '售罄' : '-' }}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </template>
                  <el-tag type="danger" size="mini">
                    <!-- 动态显示标签文本 -->
                    {{ getLackSoldOutTagText(scope.row.lackOrSoldOutTagList) }} {{ scope.row.lackOrSoldOutTagList.length
                    }}
                  </el-tag>
                </el-tooltip>

                <el-tag v-if="scope.row.settlementType === 1" type="primary" size="mini">履约考核中</el-tag>
              </div>

              <!-- 使用新组件替换原有时间线 -->
              <DeliveryTimeline v-if="scope.row.deliverInfo"
                :expect-latest-deliver-time="scope.row.deliverInfo.expectLatestDeliverTimeOrDefault"
                :expect-latest-arrival-time="scope.row.deliverInfo.expectLatestArrivalTimeOrDefault"
                :deliver-time="scope.row.deliverInfo?.deliverTime"
                :real-receive-time="scope.row.deliverInfo?.receiveTime" :status="scope.row.status" />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="商品信息" min-width="220">
          <template #default="scope">
            <div class="product-info">
              <img :src="scope.row.productSkcPicture" class="product-image" alt="商品图片"
                @click="handlePreviewImage(scope.row.productSkcPicture)">
              <div class="product-details">
                <!-- 新增：显示备货母单号 -->
                <div class="product-parent-sn">备货母单号: {{ scope.row.originalPurchaseOrderSn }}</div>
                <div class="product-name">{{ scope.row.productName }}</div>
                <div class="product-skc">SKC: {{ scope.row.productSkcId }}</div>
                <div class="product-sn">货号: {{ scope.row.productSn }}</div>
                <!-- 新增：商品标签 -->
                <div class="product-tags" style="margin-top: 5px; display: flex; gap: 5px;">
                  <el-tag v-if="scope.row.purchaseStockType === 1" type="warning" size="mini">JIT</el-tag>
                  <el-tag v-if="scope.row.settlementType === 1" type="warning" size="mini">VMI</el-tag>
                  <el-tag v-if="scope.row.isFirst === true" type="success" size="mini">首单</el-tag>
                  <el-tag v-else-if="scope.row.isFirst === false" type="warning" size="mini">返</el-tag>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>



        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ formatStatus(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="SKU信息" width="240">
          <template #default="scope">
            <div v-if="scope.row._isSummaryRow" class="sku-summary-cell">
              <span style="font-weight: bold;">合计</span>
            </div>
            <div v-else-if="scope.row.skuDetail" class="sku-cell-content">
              <img v-if="scope.row.skuDetail.thumbUrlList && scope.row.skuDetail.thumbUrlList.length > 0"
                :src="scope.row.skuDetail.thumbUrlList[0]" class="sku-image" alt="SKU图片"
                @click="handlePreviewImage(scope.row.skuDetail.thumbUrlList[0])" />
              <div v-else class="sku-image-placeholder">无图</div>
              <div class="sku-text-details">
                <div>属性: {{ scope.row.skuDetail.className || '-' }}</div>
                <div>SKU ID: {{ scope.row.skuDetail.productSkuId || '-' }}</div>
                <div>SKU 货号: {{ scope.row.skuDetail.extCode || '-' }}</div>
              </div>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>

        <el-table-column label="申报价(CNY)" width="100" align="center">
          <template #default="scope">
            <!-- 显示合计值或SKU值 -->
            <span v-if="scope.row._isSummaryRow" style="font-weight: bold;">
              <!-- ¥{{ scope.row._totalDeclarePrice }} -->
              -
            </span>
            <span v-else-if="scope.row.skuDetail">
              <!-- TODO: 确认单价字段 -->
              {{ scope.row.skuDetail.declarePrice || '-' }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="备货件数" width="90" align="center">
          <template #default="scope">
            <!-- 显示合计值或SKU值 -->
            <span v-if="scope.row._isSummaryRow" style="font-weight: bold;">
              {{ scope.row._totalPurchaseQuantity }}
            </span>
            <span v-else-if="scope.row.skuDetail">
              {{ scope.row.skuDetail?.purchaseQuantity || 0 }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="送货/入库数" width="100" align="center">
          <template #default="scope">
            <!-- 显示合计值或SKU值 -->
            <span v-if="scope.row._isSummaryRow" style="font-weight: bold;">
              {{ scope.row._totalDeliverQuantity }} / {{ scope.row._totalRealReceiveQuantity }}
            </span>
            <span v-else-if="scope.row.skuDetail">
              {{ scope.row.skuDetail?.deliverQuantity || 0 }} / {{ scope.row.skuDetail?.realReceiveAuthenticQuantity ||
              0 }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column prop="purchaseTime" label="备货单创建时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.purchaseTime) }}
          </template>
        </el-table-column>

        <!-- 新增：发货信息列 -->
        <el-table-column label="发货信息" width="200">
          <template #default="scope">
            <div class="deliver-info">

              <div class="info-item">
                <span class="info-label">发货时间:</span>
                <span>{{ scope.row.deliverInfo?.deliverTime ? formatTime(scope.row.deliverInfo.deliverTime) : '-'
                  }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">发货单号:</span>
                <span>{{ (scope.row.deliverInfo?.deliveryOrderSn || scope.row.deliveryOrderSn) || '-' }}</span>
                <el-tooltip v-if="scope.row.deliverInfo?.deliveryOrderSn || scope.row.deliveryOrderSn" content="复制发货单号"
                  placement="top" effect="light">
                  <i class="el-icon-document-copy copy-icon-small"
                    @click="copyToClipboard(scope.row.deliverInfo?.deliveryOrderSn || scope.row.deliveryOrderSn)"></i>
                </el-tooltip>
              </div>
              <div class="info-item">
                <span class="info-label">到货时间:</span>
                <span>{{ scope.row.deliverInfo?.receiveTime ? formatTime(scope.row.deliverInfo.receiveTime) : '-'
                  }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">收货仓库:</span>
                <span>{{ scope.row.deliverInfo?.receiveWarehouseName || '-' }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- 新增：二维码列 -->
        <el-table-column label="二维码" width="80" align="center">
          <template #default="scope">
            <!-- 确保是首个SKU行且不是合计行 -->
            <div v-if="!scope.row._isSummaryRow && scope.row._isFirstSku">
              <!-- 传递 qrCodeDataJson 给 QrCodeViewer 组件 -->
              <!-- 假设 QrCodeViewer 接受 dataString prop -->
              <QrCodeViewer :data-string="scope.row.qrCodeDataJson" :purchase-order-sn="scope.row.subPurchaseOrderSn" />
            </div>
          </template>
        </el-table-column>

        <!-- 新增：生产进度列 -->
        <el-table-column label="生产进度" min-width="240">
          <template #default="scope">
            <div v-if="!scope.row._isSummaryRow && scope.row._isFirstSku" class="progress-cell">
              <ProductionProgressViewer :ref="el => setProgressRef(el, scope.row.subPurchaseOrderSn)"
                :sub-purchase-order-sn="scope.row.subPurchaseOrderSn" :shop-id="scope.row.shopId"
                :is-admin="userInfo?.isAdmin" :auto-fetch="false" />
              <div class="progress-actions">
                <el-button v-if="userInfo?.isAdmin" type="primary" size="mini"
                  @click="handleManageProgress(scope.row.subPurchaseOrderSn)">
                  管理
                </el-button>
                <el-button type="default" size="mini" @click="handleRefreshProgress(scope.row.subPurchaseOrderSn)">
                  刷新
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- 在表格的最后添加操作列 -->
        <!-- <el-table-column label="操作" fixed="right" width="160">
          <template #default="scope">
            <div v-if="!scope.row._isSummaryRow" class="action-buttons">
              <el-button
                type="primary"
                size="mini"
                @click="handleViewDetail(scope.row)"
              >查看详情</el-button>
              <el-button
                size="mini"
                @click="handlePrintBarcode(scope.row, scope.row._orderGroupIndex)"
              >打印条码</el-button>
              <el-button
                size="mini"
                @click="handlePrintPicking(scope.row, scope.row._orderGroupIndex)"
              >打印拣货单</el-button>
            </div>
          </template>
        </el-table-column> -->
      </el-table>
    </div>
  </TableCard>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, reactive, onMounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { TableCard, EmptyTips, DeliveryTimeline } from '@/components/temu'
import QrCodeViewer from '@/components/temu/QrCodeViewer.vue'
import ProductionProgressViewer from '@/components/temu/ProductionProgressViewer.vue'
import { formatTime } from '@/utils/format'
import type { PurchaseOrderRequestDTO, PurchaseOrder } from '@/types/purchaseOrder'
import { useUserStore } from '@/store'
import { batchGetProductionProgress } from '@/api/temu/productionProgress'

const props = defineProps({
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 订单数据
  purchaseOrders: {
    type: Array as () => any[],
    default: () => []
  },
  // 是否已经执行过搜索
  hasSearched: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'selection-change',
  'preview-image',
  'view-detail',
  'print-picking'
])

// 获取用户信息（临时实现，根据实际情况修改）
const userStore = useUserStore()
const userInfo = computed(() => {
  return { isAdmin: true } // 临时返回，实际应根据用户权限判断
})

// 选中的行
const selectedRows = ref<any[]>([])

// 生产进度组件引用映射
const progressRefs = reactive<Record<string, any>>({})
// 生产进度数据缓存
const progressDataCache = reactive<Record<string, any>>({})

// 设置生产进度组件引用
const setProgressRef = (el: any, sn: string) => {
  if (el) {
    progressRefs[sn] = el
  }
}

// 批量获取生产进度数据
const fetchAllProgressData = async () => {
  const orderSns = props.purchaseOrders.map(order => order.subPurchaseOrderSn).filter(Boolean);
  if (orderSns.length === 0) return;

  try {
    // 使用第一个订单的shopId，如果不存在则默认为0
    const shopId = props.purchaseOrders[0]?.shopId || 0;
    console.log('开始批量获取生产进度，订单数量:', orderSns.length, '店铺ID:', shopId);

    const res = await batchGetProductionProgress({
      shopId: shopId,
      subPurchaseOrderSnList: orderSns
    });

    // 更详细地记录API响应
    console.log('批量获取生产进度响应状态:', res.status);
    console.log('批量获取生产进度响应数据类型:', typeof res.data);

    // 提取响应数据，考虑不同的响应格式
    let responseData;

    // 检查响应格式，处理不同的情况
    if (res && res.data) {
      // 情况1: 标准格式 {code: 200, message: "操作成功", data: {...}}
      if (res.data.code === 200 && res.data.data) {
        console.log('API返回标准成功响应格式');
        responseData = res.data.data;
      }
      // 情况2: 直接返回数据对象，没有包装在code/message/data中
      else if (typeof res.data === 'object' && !res.data.code) {
        console.log('API直接返回数据对象');
        responseData = res.data;
      }
      // 情况3: 返回了错误信息
      else if (res.data.code && res.data.code !== 200) {
        console.error('API返回错误状态码:', res.data.code);
        console.error('错误消息:', res.data.message || '未知错误');
        return; // 终止处理
      }
      // 情况4: 未知格式
      else {
        console.warn('未识别的API响应格式');
        console.warn('完整响应:', res.data);
        return; // 终止处理
      }
    } else {
      console.error('响应不包含data字段');
      return; // 终止处理
    }

    // 详细记录提取的响应数据
    if (responseData) {
      console.log('提取的响应数据类型:', typeof responseData);
      const resKeys = Object.keys(responseData);
      console.log('响应数据包含的备货单数量:', resKeys.length);
      if (resKeys.length > 0) {
        console.log('响应数据的第一个键值示例:', resKeys[0], responseData[resKeys[0]]);
      }
    } else {
      console.error('无法提取响应数据');
      return; // 终止处理
    }

    // 清空当前缓存
    Object.keys(progressDataCache).forEach(key => {
      delete progressDataCache[key];
    });

    // 更新缓存
    Object.assign(progressDataCache, responseData);

    const cacheKeys = Object.keys(progressDataCache);
    console.log('成功获取生产进度数据，备货单数量:', cacheKeys.length);

    if (cacheKeys.length === 0) {
      console.warn('生产进度数据为空对象');
      return;
    }

    // 更新组件数据
    nextTick(() => {
      const refKeys = Object.keys(progressRefs);
      console.log('组件引用数量:', refKeys.length);

      let updatedCount = 0;
      let emptyCount = 0;

      refKeys.forEach(sn => {
        if (progressRefs[sn]) {
          if (progressDataCache[sn]) {
            console.log(`更新备货单 ${sn} 的生产进度数据`);
            progressRefs[sn].updateProgressData(progressDataCache[sn]);
            updatedCount++;
          } else {
            // 如果没有找到数据，创建空数据结构
            console.log(`备货单 ${sn} 没有生产进度数据，使用默认空数据`);
            progressRefs[sn].updateProgressData({
              burningStatus: 0,
              sewingStatus: 0,
              tailStatus: 0,
              shippingStatus: 0,
              deliveryStatus: 0
            });
            emptyCount++;
          }
        } else {
          console.warn(`备货单 ${sn} 没有对应的组件引用`);
        }
      });

      console.log(`生产进度数据更新完成，更新 ${updatedCount} 个，缺少数据 ${emptyCount} 个`);
    });
  } catch (error) {
    console.error('批量获取生产进度异常:', error);
    if (error instanceof Error) {
      console.error('错误详情:', error.message, error.stack);
    }
  }
}

// 刷新生产进度
const handleRefreshProgress = (sn: string) => {
  try {
    if (progressRefs[sn]) {
      console.log(`刷新备货单 ${sn} 的生产进度`);
      progressRefs[sn].refresh();
      ElMessage.success('已刷新生产进度');
    } else {
      console.warn(`未找到备货单 ${sn} 的生产进度组件引用`);
      ElMessage.warning('刷新失败，请稍后重试');
    }
  } catch (error) {
    console.error('刷新生产进度错误:', error);
    ElMessage.error('刷新生产进度失败');
  }
}

// 管理生产进度
const handleManageProgress = (sn: string) => {
  if (progressRefs[sn]) {
    progressRefs[sn].openManagementDrawer();
  } else {
    ElMessage.warning('无法找到对应的生产进度组件，请先刷新页面');
  }
}

// 监听订单数据变化，自动获取生产进度
watch(() => props.purchaseOrders, (newValue) => {
  if (newValue && newValue.length > 0) {
    // 数据加载完成后批量获取生产进度
    nextTick(() => {
      fetchAllProgressData();
    });
  }
}, { deep: true });

// 组件挂载后执行
onMounted(() => {
  if (props.purchaseOrders && props.purchaseOrders.length > 0) {
    // 确保组件引用已注册后再获取数据
    nextTick(() => {
      fetchAllProgressData();
    });
  }
});

// 表格行合并处理
const spanMethod = ({ row, column, rowIndex, columnIndex }: { row: any, column: any, rowIndex: number, columnIndex: number }) => {
  // 需要合并的列的 prop 或 label (适用于首个SKU行)
  const commonColumnsForFirstSku = [
    '店铺信息',
    '备货单号',
    '商品信息',
    '商品分类', // 如果有这个列
    '状态',
    '备货单创建时间',
    '发货信息',
    '操作',
    '二维码', // 新增的二维码列
    '生产进度' // 新增的生产进度列
  ]

  // 需要在合计行特殊处理的列
  const summaryColumns = ['SKU信息', '申报价(CNY)', '备货件数', '送货/入库数'];

  // 第一列是选择框，始终为 1x1
  if (columnIndex === 0) {
    // 只在分组的第一行（非合计行）显示选择框并合并
    if (row._rowspan > 0 && !row._isSummaryRow) {
      return { rowspan: row._rowspan, colspan: 1 };
    } else {
      // 隐藏其他行的选择框单元格
      return { rowspan: 0, colspan: 0 };
    }
  }

  if (row._isSummaryRow) {
    // 处理合计行
    if (column.label === 'SKU信息') {
      // SKU信息列在合计行显示 "合计"
      return { rowspan: 1, colspan: 1 };
    } else if (column.label === '申报价(CNY)' || column.label === '备货件数' || column.label === '送货/入库数') {
      // 显示合计值
      return { rowspan: 1, colspan: 1 };
    } else if (commonColumnsForFirstSku.includes(column.label)) {
      // 合计行隐藏 店铺信息、备货单号、商品信息、状态、备货单创建时间、发货信息、操作 列
      return { rowspan: 0, colspan: 0 };
    } else {
      // 其他列在合计行正常显示，不合并
      return { rowspan: 1, colspan: 1 };
    }
  } else if (row._isSkuRow) {
    // 处理普通SKU行
    if (commonColumnsForFirstSku.includes(column.label)) {
      // 处理需要根据 _rowspan 合并的公共列
      if (row._rowspan > 0) {
        return {
          rowspan: row._rowspan,
          colspan: 1
        }
      } else {
        return {
          rowspan: 0,
          colspan: 0
        }
      }
    } else {
      // SKU相关列及非公共列正常显示，不合并
      return { rowspan: 1, colspan: 1 };
    }
  } else {
    // 处理没有SKU的订单行（如果存在这种情况）
    if (commonColumnsForFirstSku.includes(column.label)) {
      return { rowspan: 1, colspan: 1 }; // 占满一行
    } else {
      // 其他列不显示
      return { rowspan: 0, colspan: 0 };
    }
  }
};

// 计算属性，扁平化处理订单数据
const flattenedPurchaseOrders = computed(() => {
  const flattened: any[] = []
  props.purchaseOrders.forEach((order, orderIndex) => {
    let totalDeclarePrice = 0
    let totalPurchaseQuantity = 0
    let totalDeliverQuantity = 0
    let totalRealReceiveQuantity = 0

    if (order.skuQuantityDetailList && order.skuQuantityDetailList.length > 0) {
      order.skuQuantityDetailList.forEach((sku: any, skuIndex: number) => {
        const declarePrice = sku.declarePrice || 0
        const purchaseQuantity = sku.purchaseQuantity || 0
        const deliverQuantity = sku.deliverQuantity || 0
        const realReceiveQuantity = sku.realReceiveAuthenticQuantity || 0

        totalDeclarePrice += declarePrice * purchaseQuantity
        totalPurchaseQuantity += purchaseQuantity
        totalDeliverQuantity += deliverQuantity
        totalRealReceiveQuantity += realReceiveQuantity

        flattened.push({
          ...order,
          skuDetail: sku,
          _rowspan: skuIndex === 0 ? order.skuQuantityDetailList.length + 1 : 0,
          _isFirstSku: skuIndex === 0,
          _isSkuRow: true,
          _orderIndex: flattened.length,
          _orderGroupIndex: orderIndex
        })
      })
      // 添加合计行
      flattened.push({
        ...order,
        _isSummaryRow: true,
        _rowspan: 0,
        _orderIndex: flattened.length,
        _totalDeclarePrice: totalDeclarePrice.toFixed(2),
        _totalPurchaseQuantity: totalPurchaseQuantity,
        _totalDeliverQuantity: totalDeliverQuantity,
        _totalRealReceiveQuantity: totalRealReceiveQuantity,
        _orderGroupIndex: orderIndex
      })
    } else {
      flattened.push({
        ...order,
        skuDetail: {},
        _rowspan: 1,
        _isFirstSku: true,
        _isSkuRow: false,
        _orderIndex: flattened.length,
        _orderGroupIndex: orderIndex
      })
    }
  })
  return flattened
})

// 处理多选框选择变化
const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
  emit('selection-change', rows)
}

// 处理图片预览
const handlePreviewImage = (url: string) => {
  emit('preview-image', url)
}

// 处理查看详情
const handleViewDetail = (row: any) => {
  emit('view-detail', row)
}

// 处理打印拣货单
const handlePrintPicking = (row: any, orderIndex: number) => {
  emit('print-picking', row, orderIndex)
}

// 判断行是否可选择
const isRowSelectable = (row: any) => {
  // 合计行不可选择
  return !row._isSummaryRow && row._isFirstSku
}

// 复制到剪贴板
const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text).then(() => {
    ElMessage.success('已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

// 获取状态标签类型
const getStatusType = (status: number) => {
  switch (status) {
    case 0: return 'info'     // 待创建
    case 1: return 'warning'  // 已接单，待发货
    case 2: return 'primary'  // 已送货
    case 3: return 'success'  // 已收货
    case 4: return 'danger'   // 已拒收
    case 5: return 'info'     // 已验收，全部退回
    case 6: return 'success'  // 已验收
    case 7: return 'success'  // 已入库
    case 8: return 'info'     // 作废
    case 9: return 'danger'   // 已超时
    default: return 'info'
  }
}

// 格式化状态
const formatStatus = (status: number) => {
  switch (status) {
    case 0: return '待创建'
    case 1: return '待发货'
    case 2: return '已送货'
    case 3: return '已收货'
    case 4: return '已拒收'
    case 5: return '已验收，全部退回'
    case 6: return '已验收'
    case 7: return '已入库'
    case 8: return '作废'
    case 9: return '已超时'
    default: return '未知'
  }
}

// 获取缺货/售罄标签文本
const getLackSoldOutTagText = (tags: any[]) => {
  if (!tags || tags.length === 0) return ''

  const hasLack = tags.some(tag => tag.isLack)
  const hasSoldOut = tags.some(tag => tag.soldOut)

  if (hasLack && hasSoldOut) {
    return '缺货/售罄'
  } else if (hasLack) {
    return '缺货'
  } else if (hasSoldOut) {
    return '售罄'
  }

  return ''
}
</script>

<style scoped>
.table-card {
  margin-top: 10px;
}

.table-wrapper:deep(.el-table--border) {
  width: 100%;
  overflow-x: auto;
}

/* 店铺信息样式 */
.shop-info {
  display: flex;
  flex-direction: column;
}

.shop-name {
  font-weight: 500;
  margin-bottom: 4px;
}

/* 备货单号容器 */
.purchase-order-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.purchase-order-sn {
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 500;
}

.copy-icon {
  cursor: pointer;
  color: #909399;
  font-size: 14px;
}

.copy-icon:hover {
  color: #409EFF;
}

.order-tags {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

/* 商品信息样式 */
.product-info {
  display: flex;
  gap: 10px;
}

.product-image {
  width: 60px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
}

.product-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.product-parent-sn,
.product-name,
.product-skc,
.product-sn {
  font-size: 12px;
}

.product-parent-sn {
  color: #909399;
}

.product-name {
  font-weight: 500;
}

/* SKU信息样式 */
.sku-cell-content {
  display: flex;
  gap: 10px;
}

.sku-image,
.sku-image-placeholder {
  width: 60px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.sku-image {
  cursor: pointer;
}

.sku-image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 12px;
}

.sku-text-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
}

/* 发货信息样式 */
.deliver-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
}

.info-label {
  color: #909399;
}

.copy-icon-small {
  cursor: pointer;
  color: #909399;
  font-size: 12px;
}

.copy-icon-small:hover {
  color: #409EFF;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

/* 缺货提示框样式 */
:deep(.sku-lack-tooltip) {
  max-width: 480px;
}

.sku-lack-tooltip-content {
  padding: 5px;
}

.tooltip-title {
  margin-bottom: 10px;
  font-weight: 500;
}

.tooltip-table {
  width: 100%;
  border-collapse: collapse;
}

.tooltip-table th,
.tooltip-table td {
  border: 1px solid #dcdfe6;
  padding: 8px;
  text-align: center;
  font-size: 12px;
}

.tooltip-table th {
  background-color: #f5f7fa;
}

.is-lack {
  color: #f56c6c;
}

/* 新增二维码和生产进度样式 */
.progress-cell {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.progress-actions {
  display: flex;
  justify-content: flex-start;
  gap: 5px;
  margin-top: 0;
}

.progress-actions .el-button--mini {
  padding: 4px 8px;
  font-size: 11px;
}
</style>