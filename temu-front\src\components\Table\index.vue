<template>
  <div class="pro-table">
    <!-- 表格工具栏 -->
    <div class="table-header" v-if="$slots.toolbar || showToolbar">
      <slot name="toolbar">
        <div class="left">
          <slot name="tableTitle">
            <div class="title" v-if="title">{{ title }}</div>
          </slot>
        </div>
        <div class="right">
          <slot name="toolbarRight"></slot>
        </div>
      </slot>
    </div>
    
    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      :border="border"
      :stripe="stripe"
      :row-key="rowKey"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      ref="tableRef"
    >
      <!-- 复选框列 -->
      <el-table-column
        v-if="selection"
        type="selection"
        width="55"
        align="center"
        fixed="left"
      />
      
      <!-- 序号列 -->
      <el-table-column
        v-if="index"
        type="index"
        width="60"
        label="序号"
        align="center"
        fixed="left"
      />
      
      <!-- 表格列 -->
      <slot></slot>
      
      <!-- 操作列 -->
      <el-table-column v-if="$slots.action" label="操作" align="center" fixed="right" :width="actionWidth">
        <template #default="scope">
          <slot name="action" :row="scope.row" :index="scope.$index"></slot>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container" v-if="pagination && total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        :layout="paginationLayout"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 无数据 -->
    <el-empty v-if="tableData.length === 0 && !loading" :description="emptyText" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import type { ElTable, TableColumnCtx } from 'element-plus'
import type { PropType } from 'vue'

const props = defineProps({
  // 数据
  data: {
    type: Array as PropType<any[]>,
    required: true
  },
  // 总条数(分页用)
  total: {
    type: Number,
    default: 0
  },
  // 表格标题
  title: {
    type: String,
    default: ''
  },
  // 是否显示工具栏
  showToolbar: {
    type: Boolean,
    default: true
  },
  // 是否显示选择列
  selection: {
    type: Boolean,
    default: false
  },
  // 是否显示序号列
  index: {
    type: Boolean,
    default: false
  },
  // 操作列宽度
  actionWidth: {
    type: [Number, String],
    default: 200
  },
  // 是否显示边框
  border: {
    type: Boolean,
    default: true
  },
  // 是否显示斑马纹
  stripe: {
    type: Boolean,
    default: true
  },
  // 是否显示分页
  pagination: {
    type: Boolean,
    default: true
  },
  // 分页布局
  paginationLayout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  },
  // 每页条数选项
  pageSizes: {
    type: Array as PropType<number[]>,
    default: () => [10, 20, 50, 100]
  },
  // 默认每页条数
  defaultPageSize: {
    type: Number,
    default: 10
  },
  // 当前页
  page: {
    type: Number,
    default: 1
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 唯一键字段名
  rowKey: {
    type: String,
    default: 'id'
  },
  // 空数据提示文本
  emptyText: {
    type: String,
    default: '暂无数据'
  }
})

const emit = defineEmits(['selectionChange', 'sortChange', 'pageChange', 'sizeChange'])

// 表格实例
const tableRef = ref<InstanceType<typeof ElTable>>()

// 当前页码
const currentPage = ref(props.page)
// 每页条数
const pageSize = ref(props.defaultPageSize)

// 表格数据
const tableData = computed(() => props.data || [])

// 已选择行
const selectedRows = ref<any[]>([])

// 监听页码变化
watch(() => props.page, (val) => {
  currentPage.value = val
})

// 处理选择变化
function handleSelectionChange(selection: any[]) {
  selectedRows.value = selection
  emit('selectionChange', selection)
}

// 处理排序变化
function handleSortChange(column: TableColumnCtx<any>) {
  emit('sortChange', {
    prop: column.prop,
    order: column.order
  })
}

// 处理每页条数变化
function handleSizeChange(val: number) {
  pageSize.value = val
  emit('sizeChange', val)
  emit('pageChange', { page: currentPage.value, size: val })
  
  // 重新设置滚动条位置
  nextTick(() => {
    document.documentElement.scrollTop = 0
  })
}

// 处理页码变化
function handleCurrentChange(val: number) {
  currentPage.value = val
  emit('pageChange', { page: val, size: pageSize.value })
  
  // 重新设置滚动条位置
  nextTick(() => {
    document.documentElement.scrollTop = 0
  })
}

// 暴露方法给父组件
defineExpose({
  // 获取表格实例
  getTableRef: () => tableRef.value,
  // 获取选中行数据
  getSelectionRows: () => selectedRows.value,
  // 清空选中行
  clearSelection: () => tableRef.value?.clearSelection()
})
</script>

<style lang="scss" scoped>
.pro-table {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .left {
      .title {
        font-size: 16px;
        font-weight: bold;
      }
    }
    
    .right {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  
  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
  }
}
</style> 