---
description: 
globs: 
alwaysApply: true
---


** 入参要求
   - header中必须设置 authorization, 值为当前登录后保存的token值
   - 请求的参数使用json 格式， 就算是参数为空，也需要使用 {} 来代替
** http请求
   请求方式默认是post，除非有明确要求
** 返参
  - 后端统一返回的参数为json对象，格式如下
   {
    "code": 200,
    "message": "操作成功",
    "data": {}
    }
 error =200, 表示请求成功
 error = 500, 表示系统异常，需要弹出系统异常的错误
 error 其它值，表示业务异常，直接弹出 message内容
 data 是一个对象


 ** 设计一个通用函数来处理后端API返回值，所有的API文件都是用这个通用函数