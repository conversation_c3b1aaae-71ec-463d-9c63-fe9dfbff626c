<template>
  <div class="app-container">
    <el-descriptions
      title="运营组详情"
      :column="2"
      border
      class="detail-section"
    >
      <template #extra>
        <el-button 
          type="primary" 
          icon="Edit" 
          @click="handleEdit"
          v-hasPermi="['operation:group:edit']"
        >
          编辑
        </el-button>
        <el-button icon="Back" @click="goBack">返回</el-button>
      </template>
      <el-descriptions-item label="运营组名称">{{ groupDetail.groupName }}</el-descriptions-item>
      <el-descriptions-item label="运营组ID">{{ groupDetail.groupId }}</el-descriptions-item>
      <el-descriptions-item label="负责人">{{ groupDetail.leaderName }}</el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag :type="groupDetail.status === '0' ? 'success' : 'danger'">
          {{ groupDetail.status === '0' ? '正常' : '禁用' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">{{ groupDetail.createTime }}</el-descriptions-item>
      <el-descriptions-item label="更新时间">{{ groupDetail.updateTime }}</el-descriptions-item>
      <el-descriptions-item label="备注" :span="2">{{ groupDetail.remark || '无' }}</el-descriptions-item>
    </el-descriptions>

    <!-- 统计信息 -->
    <div class="statistics-section">
      <h3>统计信息</h3>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="statistic-card">
            <template #header>
              <div class="card-header">
                <span>成员数量</span>
              </div>
            </template>
            <div class="card-value">{{ statistics.memberCount }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="statistic-card">
            <template #header>
              <div class="card-header">
                <span>关联店铺</span>
              </div>
            </template>
            <div class="card-value">{{ statistics.shopCount }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="statistic-card">
            <template #header>
              <div class="card-header">
                <span>进行中任务</span>
              </div>
            </template>
            <div class="card-value">{{ statistics.ongoingTaskCount }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="statistic-card">
            <template #header>
              <div class="card-header">
                <span>已完成任务</span>
              </div>
            </template>
            <div class="card-value">{{ statistics.completedTaskCount }}</div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 运营组成员列表预览 -->
    <div class="members-section">
      <div class="section-header">
        <h3>组成员</h3>
        <el-button 
          type="primary" 
          size="small" 
          icon="More" 
          @click="goToMembers"
        >
          查看更多
        </el-button>
      </div>
      <el-table :data="memberList" style="width: 100%" border>
        <el-table-column label="用户名" prop="username" width="180" />
        <el-table-column label="昵称" prop="nickName" width="180" />
        <el-table-column label="加入时间" prop="joinTime" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.joinTime) }}
          </template>
        </el-table-column>
        <el-table-column label="角色" width="120">
          <template #default="scope">
            <el-tag v-if="scope.row.isLeader" type="danger">负责人</el-tag>
            <el-tag v-else>成员</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === '0' ? 'success' : 'info'">
              {{ scope.row.status === '0' ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getGroupDetail, getGroupMembers, getGroupStatistics } from '@/api/group'
import type { OperationGroup, GroupMember, GroupStatistics } from '@/types/group'
import { formatTime } from '@/utils/format'

const route = useRoute()
const router = useRouter()
const groupId = Number(route.params.id)

// 运营组详情数据
const groupDetail = ref<OperationGroup>({
  groupId: undefined,
  groupName: '',
  leaderId: 0
})

// 统计数据
const statistics = ref<GroupStatistics>({
  groupId: groupId,
  groupName: '',
  memberCount: 0,
  shopCount: 0,
  ongoingTaskCount: 0,
  completedTaskCount: 0
})

// 成员列表
const memberList = ref<GroupMember[]>([])

// 加载运营组详情
const loadGroupDetail = async () => {
  try {
    const res = await getGroupDetail(groupId)
    groupDetail.value = res.data || {}
  } catch (error) {
    console.error('获取运营组详情失败', error)
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const res = await getGroupStatistics(groupId)
    statistics.value = res.data || {
      groupId: groupId,
      groupName: '',
      memberCount: 0,
      shopCount: 0,
      ongoingTaskCount: 0,
      completedTaskCount: 0
    }
  } catch (error) {
    console.error('获取统计数据失败', error)
  }
}

// 加载成员列表
const loadMembers = async () => {
  try {
    const res = await getGroupMembers(groupId, 1, 5) // 只加载前5个成员作为预览
    memberList.value = res.data.records || []
  } catch (error) {
    console.error('获取成员列表失败', error)
  }
}

// 返回列表页
const goBack = () => {
  router.push('/operation/group')
}

// 前往编辑页
const handleEdit = () => {
  // 跳转到编辑页面或打开编辑弹窗
  // 此处可根据实际需求实现
}

// 前往成员管理页
const goToMembers = () => {
  router.push(`/operation/group/members/${groupId}`)
}

onMounted(() => {
  if (!groupId) {
    router.push('/operation/group')
    return
  }
  
  loadGroupDetail()
  loadStatistics()
  loadMembers()
})
</script>

<style scoped>
.detail-section {
  margin-bottom: 30px;
}

.statistics-section {
  margin-bottom: 30px;
}

.members-section {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.statistic-card {
  text-align: center;
}

.card-header {
  font-size: 16px;
  font-weight: 500;
}

.card-value {
  font-size: 28px;
  font-weight: 600;
  color: #409EFF;
}
</style> 