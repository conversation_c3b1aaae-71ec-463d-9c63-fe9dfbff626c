{"name": "temu-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@gowin100/jspdf-font-sourcehansanstc": "^1.0.2", "@types/file-saver": "^2.0.7", "@types/nprogress": "^0.2.3", "axios": "^1.8.4", "element-plus": "^2.9.6", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jsbarcode": "^3.12.1", "jspdf": "^3.0.1", "jspdf-font": "^1.0.7", "jszip": "^3.10.1", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "pinia": "^3.0.1", "qrcode": "^1.5.4", "qrcode.vue": "^3.6.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^22.13.10", "@types/path-browserify": "^1.0.3", "@types/qrcode": "^1.5.5", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "sass-embedded": "^1.86.0", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}}