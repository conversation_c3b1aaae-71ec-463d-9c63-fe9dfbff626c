<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.infrastructure.log.mapper.ApiCallLogMapper">

    <resultMap id="ApiCallLogResult" type="com.xiao.temu.infrastructure.log.entity.ApiCallLog">
        <id property="logId" column="log_id"/>
        <result property="userId" column="user_id"/>
        <result property="shopId" column="shop_id"/>
        <result property="apiName" column="api_name"/>
        <result property="requestUrl" column="request_url"/>
        <result property="requestMethod" column="request_method"/>
        <result property="requestParam" column="request_param"/>
        <result property="responseData" column="response_data"/>
        <result property="status" column="status"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="costTime" column="cost_time"/>
        <result property="ip" column="ip"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    
    <sql id="selectLogSql">
        select log_id, user_id, shop_id, api_name, request_url, request_method, 
               request_param, response_data, status, error_msg, cost_time, ip, create_time
        from api_call_log
    </sql>
    
    <!-- 分页查询API调用日志 -->
    <select id="selectLogList" resultMap="ApiCallLogResult">
        <include refid="selectLogSql"/>
        <where>
            <if test="shopId != null">
                AND shop_id = #{shopId}
            </if>
            <if test="apiName != null and apiName != ''">
                AND api_name like concat('%', #{apiName}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="startTime != null and startTime != ''">
                AND create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
        order by create_time desc
    </select>
    
    <!-- 查询用户的API调用日志 -->
    <select id="selectLogsByUserId" resultMap="ApiCallLogResult">
        <include refid="selectLogSql"/>
        where user_id = #{userId}
        order by create_time desc
    </select>
    
    <!-- 查询店铺的API调用日志 -->
    <select id="selectLogsByShopId" resultMap="ApiCallLogResult">
        <include refid="selectLogSql"/>
        where shop_id = #{shopId}
        order by create_time desc
    </select>
    
</mapper> 