<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>备货单通知测试工具</span>
        </div>
      </template>
      
      <!-- 通知类型选择 -->
      <el-form :inline="true" class="type-select-form">
        <el-form-item label="通知类型">
          <el-radio-group v-model="selectedType" @change="handleTypeChange">
            <el-radio-button v-for="(name, type) in NOTIFICATION_TYPE_MAP" :key="type" :label="Number(type)">
              {{ name }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <!-- 配置信息 -->
      <el-alert
        v-if="notificationConfig"
        title="当前通知配置"
        type="info"
        :closable="false"
        show-icon
      >
        <div class="config-info">
          <div>
            <span class="label">通知名称:</span>
            <span>{{ notificationConfig.notificationName }}</span>
          </div>
          <div>
            <span class="label">状态:</span>
            <span>{{ notificationConfig.enabled ? '启用' : '禁用' }}</span>
          </div>
          <div v-if="selectedType <= 2">
            <span class="label">触发时间:</span>
            <span>{{ notificationConfig.jitTriggerHours }}小时</span>
          </div>
          <div v-else>
            <span class="label">触发天数:</span>
            <span>{{ notificationConfig.normalTriggerDays }}天</span>
          </div>
          <div>
            <span class="label">最大通知次数:</span>
            <span>{{ notificationConfig.maxNotifyCount }}次</span>
          </div>
          <div>
            <span class="label">通知间隔:</span>
            <span>{{ notificationConfig.notifyIntervalHours }}小时</span>
          </div>
        </div>
      </el-alert>
      
      <!-- 测试设置 -->
      <el-collapse v-model="activePanel">
        <el-collapse-item title="测试设置" name="settings">
          <el-form label-width="150px" :model="testSettings">
            <el-form-item label="模拟当前时间">
              <el-date-picker 
                v-model="testSettings.mockCurrentTime" 
                type="datetime" 
                placeholder="请选择模拟时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
              <span class="tip-text">不设置则使用系统当前时间</span>
            </el-form-item>
            
            <el-form-item label="测试选项">
              <el-checkbox v-model="testSettings.skipHoursCheck">忽略小时检查</el-checkbox>
              <el-checkbox v-model="testSettings.skipDaysCheck">忽略天数检查</el-checkbox>
              <el-checkbox v-model="testSettings.skipNotifyCountCheck">忽略通知次数限制</el-checkbox>
              <el-checkbox v-model="testSettings.batchSend">使用批量发送模式</el-checkbox>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveTestSettings" v-if="hasPermission('purchase:notification:test:setting')">保存设置</el-button>
              <el-button @click="resetTestSettings">重置设置</el-button>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
      
      <!-- 条件筛选 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="店铺ID" prop="shopId">
          <el-input v-model="queryParams.shopId" placeholder="请输入店铺ID" clearable />
        </el-form-item>
        
        <el-form-item label="备货单号" prop="orderSn">
          <el-input v-model="queryParams.orderSn" placeholder="请输入备货单号" clearable />
        </el-form-item>
        
        <el-form-item label="商品名称" prop="productName">
          <el-input v-model="queryParams.productName" placeholder="请输入商品名称" clearable />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handlePreview" v-if="hasPermission('purchase:notification:test:preview')">预览匹配订单</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 匹配结果表格 -->
      <el-table
        v-loading="loading"
        :data="matchOrderList"
        style="width: 100%"
        border
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="shopId" label="店铺ID" width="80" />
        <el-table-column prop="shopName" label="店铺名称" width="120" :show-overflow-tooltip="true" />
        <el-table-column prop="subPurchaseOrderSn" label="备货单号" width="180" :show-overflow-tooltip="true" />
        <el-table-column prop="productName" label="商品名称" width="180" :show-overflow-tooltip="true" />
        <el-table-column prop="supplierName" label="供应商" width="120" :show-overflow-tooltip="true" />
        <el-table-column label="时间信息" width="180">
          <template #default="scope">
            <p v-if="scope.row.purchaseTime">
              <span>创建时间：</span>
              <span>{{ formatDateTime(scope.row.purchaseTime) }}</span>
            </p>
            <p v-if="scope.row.deliverTime">
              <span>发货时间：</span>
              <span>{{ formatDateTime(scope.row.deliverTime) }}</span>
            </p>
            <p v-if="scope.row.expectLatestArrivalTime">
              <span>预计到货：</span>
              <span>{{ formatDateTime(scope.row.expectLatestArrivalTime) }}</span>
            </p>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.status)">
              {{ getOrderStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="匹配原因" :show-overflow-tooltip="true">
          <template #default="scope">
            <div>{{ scope.row.reason }}</div>
            <div v-if="scope.row.daysOrHours">
              <el-tag size="small" type="warning">
                {{ selectedType <= 2 ? scope.row.daysOrHours + '小时' : scope.row.daysOrHours + '天' }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <pagination
        v-if="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getMatchOrderList"
      />
      
      <!-- 操作按钮 -->
      <div class="action-bar">
        <el-button
          type="primary"
          :disabled="selectedRows.length === 0"
          @click="handleSendNotification"
          v-if="hasPermission('purchase:notification:test:trigger')"
        >
          发送通知 (已选择 {{ selectedRows.length }} 条)
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup name="NotificationTest">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getNotificationTypes, getTestNotificationConfig, previewMatchOrders, sendNotification, testNotificationMatch, saveTestSetting, getTestSetting } from '@/api/notification'
import { NotificationType, NOTIFICATION_TYPE_MAP, type MatchOrder, type NotificationConfig, type NotificationTestSetting } from '@/types/notification'
import { hasPermission } from '@/utils/permission'
import Pagination from '@/components/Pagination/index.vue'

// 当前选择的通知类型
const selectedType = ref<NotificationType>(NotificationType.JIT_SOON_EXPIRE)

// 当前通知配置
const notificationConfig = ref<NotificationConfig | null>(null)

// 查询参数
const queryParams = reactive({
  notificationType: NotificationType.JIT_SOON_EXPIRE,
  shopId: '',
  orderSn: '',
  productName: '',
  pageNum: 1,
  pageSize: 10
})

// 测试设置
const testSettings = reactive<NotificationTestSetting>({
  mockCurrentTime: undefined,
  skipHoursCheck: false,
  skipDaysCheck: false,
  skipNotifyCountCheck: false,
  batchSend: false
})

// 折叠面板
const activePanel = ref(['settings'])

// 表格数据
const matchOrderList = ref<MatchOrder[]>([])
const loading = ref(false)
const total = ref(0)
const selectedRows = ref<MatchOrder[]>([])

// 选择变化事件
const handleSelectionChange = (selection: MatchOrder[]) => {
  selectedRows.value = selection
}

// 获取订单状态名称
const getOrderStatusName = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '待发货',
    2: '待收货',
    3: '已收货',
    4: '已取消'
  }
  return statusMap[status] || '未知'
}

// 获取订单状态对应的标签类型
const getOrderStatusType = (status: number) => {
  const typeMap: Record<number, string> = {
    1: 'primary',
    2: 'warning',
    3: 'success',
    4: 'info'
  }
  return typeMap[status] || ''
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`
}

// 切换通知类型
const handleTypeChange = async (type: NotificationType) => {
  queryParams.notificationType = type
  // 获取该类型的通知配置
  await getNotificationConfig()
  // 重置结果
  matchOrderList.value = []
  total.value = 0
}

// 获取通知配置
const getNotificationConfig = async () => {
  try {
    const res = await getTestNotificationConfig(selectedType.value)
    notificationConfig.value = res.data
  } catch (error) {
    console.error('获取通知配置失败', error)
    ElMessage.error('获取通知配置失败')
  }
}

// 预览匹配订单
const handlePreview = async () => {
  queryParams.pageNum = 1
  getMatchOrderList()
}

// 获取匹配订单列表
const getMatchOrderList = async () => {
  loading.value = true
  try {
    // 构造查询参数，包含测试设置
    const params = {
      ...queryParams,
      ...testSettings
    }
    const res = await testNotificationMatch(selectedType.value, params)
    matchOrderList.value = res.data.rows || []
    total.value = res.data.total || 0
  } catch (error) {
    console.error('获取匹配订单失败', error)
    ElMessage.error('获取匹配订单失败')
  } finally {
    loading.value = false
  }
}

// 重置查询参数
const resetQuery = () => {
  queryParams.shopId = ''
  queryParams.orderSn = ''
  queryParams.productName = ''
  queryParams.pageNum = 1
}

// 保存测试设置
const saveTestSettings = async () => {
  try {
    await saveTestSetting(testSettings)
    ElMessage.success('测试设置保存成功')
  } catch (error) {
    console.error('保存测试设置失败', error)
    ElMessage.error('保存测试设置失败')
  }
}

// 重置测试设置
const resetTestSettings = () => {
  testSettings.mockCurrentTime = undefined
  testSettings.skipHoursCheck = false
  testSettings.skipDaysCheck = false
  testSettings.skipNotifyCountCheck = false
  testSettings.batchSend = false
}

// 获取测试设置
const loadTestSettings = async () => {
  try {
    const res = await getTestSetting()
    if (res.data) {
      Object.assign(testSettings, res.data)
    }
  } catch (error) {
    console.error('获取测试设置失败', error)
  }
}

// 发送通知
const handleSendNotification = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请至少选择一条数据')
    return
  }
  
  ElMessageBox.confirm(`确认向选中的${selectedRows.value.length}条备货单发送通知吗？`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    loading.value = true
    try {
      // 构造参数
      const params = {
        notificationType: selectedType.value,
        orderSnList: selectedRows.value.map(row => row.subPurchaseOrderSn),
        shopId: selectedRows.value[0]?.shopId || null,
        ...testSettings
      }
      await sendNotification(params)
      ElMessage.success('发送通知成功')
      // 重新获取列表
      getMatchOrderList()
    } catch (error) {
      console.error('发送通知失败', error)
      ElMessage.error('发送通知失败')
    } finally {
      loading.value = false
    }
  }).catch(() => {})
}

onMounted(async () => {
  // 获取通知配置
  await getNotificationConfig()
  // 加载测试设置
  await loadTestSettings()
})
</script>

<style scoped>
.type-select-form {
  margin-bottom: 20px;
}

.search-form {
  margin: 20px 0;
}

.config-info {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 10px;
}

.label {
  font-weight: bold;
  margin-right: 5px;
}

.action-bar {
  margin-top: 20px;
  text-align: right;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tip-text {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}
</style> 