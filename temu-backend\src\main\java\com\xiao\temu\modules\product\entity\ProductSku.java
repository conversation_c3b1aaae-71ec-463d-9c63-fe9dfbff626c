package com.xiao.temu.modules.product.entity;

import java.util.Date;
import lombok.Data;

/**
 * 商品SKU实体类
 */
@Data
public class ProductSku {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 商品ID
     */
    private Long productId;
    
    /**
     * 商品SKC ID
     */
    private Long productSkcId;
    
    /**
     * 商品SKU ID
     */
    private Long productSkuId;
    
    /**
     * 颜色
     */
    private String color;
    
    /**
     * 尺码
     */
    private String size;
    
    /**
     * SKU状态
     */
    private Integer status;
    
    /**
     * 同步时间
     */
    private Date syncTime;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 