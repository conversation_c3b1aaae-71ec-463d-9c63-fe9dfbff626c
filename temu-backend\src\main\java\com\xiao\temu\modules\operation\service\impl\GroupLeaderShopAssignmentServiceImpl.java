package com.xiao.temu.modules.operation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiao.temu.common.response.PageResult;
import com.xiao.temu.modules.operation.dto.GroupLeaderShopAssignmentDTO;
import com.xiao.temu.modules.operation.dto.GroupLeaderShopAssignmentQueryDTO;
import com.xiao.temu.modules.operation.entity.GroupLeaderShopAssignment;
import com.xiao.temu.modules.operation.entity.OperationGroup;
import com.xiao.temu.modules.shop.entity.ShopGroupAssignment;
import com.xiao.temu.modules.operation.mapper.GroupLeaderShopAssignmentMapper;
import com.xiao.temu.modules.operation.mapper.OperationGroupMapper;
import com.xiao.temu.modules.shop.mapper.ShopGroupAssignmentMapper;
import com.xiao.temu.modules.shop.mapper.ShopMapper;
import com.xiao.temu.modules.operation.service.GroupLeaderShopAssignmentService;
import com.xiao.temu.modules.operation.service.GroupMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 组长店铺分配服务实现类
 */
@Service
public class GroupLeaderShopAssignmentServiceImpl 
    extends ServiceImpl<GroupLeaderShopAssignmentMapper, GroupLeaderShopAssignment> 
    implements GroupLeaderShopAssignmentService {

    @Autowired
    private GroupLeaderShopAssignmentMapper assignmentMapper;

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private OperationGroupMapper groupMapper;

    @Autowired
    private ShopGroupAssignmentMapper shopGroupAssignmentMapper;

    @Autowired
    private GroupMemberService groupMemberService;

    @Override
    public PageResult<GroupLeaderShopAssignmentDTO> listAssignments(GroupLeaderShopAssignmentQueryDTO queryDTO) {
        Page<GroupLeaderShopAssignmentDTO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        IPage<GroupLeaderShopAssignmentDTO> iPage = assignmentMapper.selectAssignmentList(page, queryDTO);
        
        return new PageResult<>(iPage.getTotal(), queryDTO.getPageNum(), queryDTO.getPageSize(), iPage.getRecords());
    }

    @Override
    public List<GroupLeaderShopAssignmentDTO> listAllAssignmentsByGroupId(Long groupId) {
        GroupLeaderShopAssignmentQueryDTO queryDTO = new GroupLeaderShopAssignmentQueryDTO();
        queryDTO.setGroupId(groupId);
        
        return assignmentMapper.selectAllAssignmentsByGroupId(groupId);
    }

    @Override
    public GroupLeaderShopAssignmentDTO getAssignmentById(Long id) {
        return assignmentMapper.selectAssignmentById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addAssignment(GroupLeaderShopAssignment assignment) {
        // 设置分配时间
        if (assignment.getAssignTime() == null) {
            assignment.setAssignTime(new Date());
        }
        // 设置状态
        if (assignment.getStatus() == null) {
            assignment.setStatus("0");
        }
        
        // 检查是否已存在相同分配
        LambdaQueryWrapper<GroupLeaderShopAssignment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GroupLeaderShopAssignment::getGroupId, assignment.getGroupId())
                .eq(GroupLeaderShopAssignment::getShopId, assignment.getShopId())
                .eq(GroupLeaderShopAssignment::getUserId, assignment.getUserId())
                .eq(GroupLeaderShopAssignment::getStatus, "0");
        if (this.count(wrapper) > 0) {
            // 已存在相同分配，更新权限类型即可
            GroupLeaderShopAssignment existingAssignment = this.getOne(wrapper);
            existingAssignment.setPermissionType(assignment.getPermissionType());
            existingAssignment.setAssignTime(assignment.getAssignTime());
            existingAssignment.setAssignBy(assignment.getAssignBy());
            return this.updateById(existingAssignment);
        }
        
        return this.save(assignment);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAssignment(GroupLeaderShopAssignment assignment) {
        return this.updateById(assignment);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeStatus(Long id, String status) {
        GroupLeaderShopAssignment assignment = this.getById(id);
        if (assignment != null) {
            assignment.setStatus(status);
            return this.updateById(assignment);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAssignment(Long id) {
        // 获取分配信息
        GroupLeaderShopAssignment assignment = this.getById(id);
        if (assignment != null) {
            // 检查是否是该店铺在该组的最后一个分配
            LambdaQueryWrapper<GroupLeaderShopAssignment> countWrapper = new LambdaQueryWrapper<>();
            countWrapper.eq(GroupLeaderShopAssignment::getShopId, assignment.getShopId())
                      .eq(GroupLeaderShopAssignment::getGroupId, assignment.getGroupId())
                      .ne(GroupLeaderShopAssignment::getId, id);
            
            long count = this.count(countWrapper);
            
            // 如果是最后一个分配，更新Shop表中的belongGroupId为null
            if (count == 0) {
                // 更新Shop表中的belongGroupId
                shopMapper.updateBelongGroupId(assignment.getShopId(), null);
            }
        }
        
        // 删除分配关系
        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAssignShops(Long groupId, List<Long> shopIds, Long userId, String permissionType, Long assignBy) {
        if (shopIds == null || shopIds.isEmpty()) {
            return false;
        }
        
        Date now = new Date();
        List<GroupLeaderShopAssignment> assignments = new ArrayList<>();
        
        for (Long shopId : shopIds) {
            // 修改：使用shop_group_assignment表检查店铺是否属于该运营组
            LambdaQueryWrapper<ShopGroupAssignment> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ShopGroupAssignment::getShopId, shopId)
                   .eq(ShopGroupAssignment::getGroupId, groupId)
                   .eq(ShopGroupAssignment::getStatus, "0");
            
            // 检查店铺是否关联到该运营组
            if (shopGroupAssignmentMapper.selectCount(wrapper) == 0) {
                continue; // 店铺不属于该运营组，跳过
            }
            
            // 创建分配对象
            GroupLeaderShopAssignment assignment = new GroupLeaderShopAssignment();
            assignment.setGroupId(groupId);
            assignment.setShopId(shopId);
            assignment.setUserId(userId);
            assignment.setPermissionType(permissionType);
            assignment.setAssignTime(now);
            assignment.setAssignBy(assignBy);
            assignment.setStatus("0");
            
            assignments.add(assignment);
        }
        
        if (assignments.isEmpty()) {
            return false;
        }
        
        // 批量保存
        for (GroupLeaderShopAssignment assignment : assignments) {
            this.addAssignment(assignment);
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteAssignments(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        // 遍历每个分配ID，处理每个分配的belongGroupId
        for (Long id : ids) {
            // 获取分配信息
            GroupLeaderShopAssignment assignment = this.getById(id);
            if (assignment != null) {
                // 检查是否是该店铺在该组的最后一个分配
                LambdaQueryWrapper<GroupLeaderShopAssignment> countWrapper = new LambdaQueryWrapper<>();
                countWrapper.eq(GroupLeaderShopAssignment::getShopId, assignment.getShopId())
                          .eq(GroupLeaderShopAssignment::getGroupId, assignment.getGroupId())
                          .ne(GroupLeaderShopAssignment::getId, id);
                
                long count = this.count(countWrapper);
                
                // 如果是最后一个分配，更新Shop表中的belongGroupId为null
                if (count == 0) {
                    // 更新Shop表中的belongGroupId
                    shopMapper.updateBelongGroupId(assignment.getShopId(), null);
                }
            }
        }
        
        return this.removeByIds(ids);
    }

    @Override
    public GroupLeaderShopAssignmentDTO getUserShopAssignment(Long userId, Long shopId) {
        return assignmentMapper.selectByUserAndShop(userId, shopId);
    }

    @Override
    public List<GroupLeaderShopAssignmentDTO> getUserAssignments(Long userId) {
        return assignmentMapper.selectByUserId(userId);
    }

    @Override
    public List<GroupLeaderShopAssignmentDTO> getShopAssignments(Long shopId) {
        return assignmentMapper.selectByShopId(shopId);
    }

    @Override
    public List<GroupLeaderShopAssignmentDTO> getGroupAssignments(Long groupId) {
        return assignmentMapper.selectByGroupId(groupId);
    }

    @Override
    public boolean checkShopPermission(Long userId, Long shopId, boolean requireWrite) {
        GroupLeaderShopAssignmentDTO assignment = getUserShopAssignment(userId, shopId);
        if (assignment == null) {
            return false;
        }
        
        if (requireWrite) {
            // 需要写权限，检查权限类型
            return "1".equals(assignment.getPermissionType());
        }
        
        // 只读权限即可
        return true;
    }

    @Override
    public boolean isGroupLeader(Long userId, Long groupId) {
        if (userId == null || groupId == null) {
            return false;
        }
        
        LambdaQueryWrapper<OperationGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OperationGroup::getGroupId, groupId)
                .eq(OperationGroup::getLeaderId, userId)
                .eq(OperationGroup::getStatus, "0");
        
        return groupMapper.selectCount(wrapper) > 0;
    }
} 