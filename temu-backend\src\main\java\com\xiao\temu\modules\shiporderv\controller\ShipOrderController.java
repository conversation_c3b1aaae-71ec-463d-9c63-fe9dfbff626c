package com.xiao.temu.modules.shiporderv.controller;

import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.shiporderv.dto.ShipOrderRequestDTO;
import com.xiao.temu.modules.shiporderv.dto.PrintLabelRequestDTO;
import com.xiao.temu.modules.sync.vo.ApiResponse;
import com.xiao.temu.modules.shiporderv.vo.ShipOrderVO;
import com.xiao.temu.modules.shiporderv.service.ShipOrderService;
import com.xiao.temu.modules.shiporderv.service.PrintLabelService;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.system.service.SysDataPermissionService;
import com.xiao.temu.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

/**
 * Temu发货单控制器
 */
@Slf4j
@RestController
@RequestMapping("/temu/shipOrder")
public class ShipOrderController {

    @Autowired
    private ShipOrderService shipOrderService;
    
    @Autowired
    private PrintLabelService printLabelService;
    
    @Autowired
    private ShopService shopService;
    
    @Autowired
    private UserService userService;

    @Autowired
    private SysDataPermissionService dataPermissionService;

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        // 通过用户名查询用户信息获取用户ID
        SysUser user = userService.getUserByUsername(username);
        if (user != null) {
            return user.getUserId();
        }
        throw new RuntimeException("用户未找到: " + username);
    }

    /**
     * 获取发货单列表
     *
     * @param requestDTO 请求参数
     * @return API响应
     */
    @PostMapping("/list")
    public ApiResponse getShipOrders(@RequestBody ShipOrderRequestDTO requestDTO) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        // 判断用户是否为管理员或拥有全部数据权限
        boolean isAdmin = userService.isAdmin(userId);
        
        // 获取用户的最高数据权限
        String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
        boolean hasFullDataPermission = "2".equals(permissionType);
        
        // 如果是管理员或拥有全部数据权限，设置忽略权限检查标志
        if (isAdmin || hasFullDataPermission) {
            requestDTO.setIgnorePermissionCheck(true);
        }
        
        // 调用服务获取结果
        ShipOrderVO response = shipOrderService.getShipOrderList(requestDTO, userId);
        
        if (response.getSuccess()) {
            return ApiResponse.success(response);
        } else {
            return ApiResponse.error(response.getErrorCode(), response.getErrorMsg(), response);
        }
    }
    
    /**
     * 获取当前用户有权限的店铺列表
     */
    @GetMapping("/shops")
    public ApiResponse getUserShops() {
        Long userId = getCurrentUserId();
        
        // 判断用户是否为管理员或拥有全部数据权限
        boolean isAdmin = userService.isAdmin(userId);
        List<Shop> shops;
        
        // 获取用户的最高数据权限
        String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
        boolean hasFullDataPermission = "2".equals(permissionType);
        
        if (isAdmin || hasFullDataPermission) {
            // 管理员或拥有全部数据权限的用户可以查看所有店铺
            shops = shopService.listAllShops();
        } else {
            // 普通用户只能查看分配的店铺
            shops = shopService.listUserShops(userId);
        }
        
        // 清除敏感信息
        if (shops != null && !shops.isEmpty()) {
            shops.forEach(shop -> {
                shop.setAccessToken(null);
                shop.setApiKey(null);
                shop.setApiSecret(null);
            });
        }
        
        return ApiResponse.success(shops);
    }
    
    /**
     * 批量打印商品打包标签
     * 
     * @param printLabelRequestList 包含发货单号、备货单号和店铺ID的数据列表
     * @return API响应
     */
    @PostMapping("/printLabels")
    public ApiResponse printShipOrderLabels(@RequestBody List<PrintLabelRequestDTO> printLabelRequestList) {
        log.info("接收到打印商品标签请求: {}", printLabelRequestList);
        
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        try {
            // 记录每个请求的店铺ID和发货单号信息，便于日志跟踪
            List<String> requestInfo = printLabelRequestList.stream()
                .map(dto -> String.format("店铺ID:%s,发货单号:%s,备货单号:%s", 
                        dto.getShopId(), dto.getDeliveryOrderSn(), dto.getSubPurchaseOrderSn()))
                .collect(Collectors.toList());
                
            log.info("打印请求详情: {}", requestInfo);
            
            // 调用服务获取标签数据
            return printLabelService.getBoxMarkInfo(printLabelRequestList);
        } catch (Exception e) {
            log.error("打印商品标签出错", e);
            return ApiResponse.error("打印处理失败: " + e.getMessage());
        }
    }
} 