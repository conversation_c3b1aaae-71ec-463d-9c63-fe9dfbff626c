package com.xiao.temu.modules.shop.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 店铺导入DTO
 */
@Data
public class ShopImportDTO {

    /**
     * 店铺名称
     */
    @ExcelProperty(value = "店铺名称", index = 0)
    private String shopName;

    /**
     * temu平台店铺id
     */
    @ExcelProperty(value = "Temu店铺ID", index = 1)
    private String shopTemuId;

    /**
     * API密钥
     */
    @ExcelProperty(value = "API密钥", index = 2)
    private String apiKey;

    /**
     * API密钥Secret
     */
    @ExcelProperty(value = "API密钥Secret", index = 3)
    private String apiSecret;
    
    /**
     * access_token
     */
    @ExcelProperty(value = "Access Token", index = 4)
    private String accessToken;
    
    /**
     * 所属运营组
     */
    @ExcelProperty(value = "所属运营组", index = 5)
    private String groupNames;
    
    /**
     * 状态
     */
    @ExcelProperty(value = "状态", index = 6)
    private String status;
    
    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 7)
    private String remark;
} 