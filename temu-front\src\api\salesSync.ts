import request from '@/utils/request'

/**
 * 销售同步任务请求参数
 */
export interface SalesSyncParams {
  shopIds?: number[]
  pageNum?: number
  pageSize?: number
  sortField?: string
  sortOrder?: string
}

/**
 * 销售同步任务
 */
export interface SalesSyncTask {
  id: number
  shopId: number
  shopName?: string
  shopRemark?: string
  lastSyncTime: string
  lastUpdateTime: string
  syncStatus: number
  errorMessage: string
  totalRecords: number
  skuTotalRecords: number
  warehouseTotalRecords: number
  createTime: string
  updateTime: string
}

/**
 * 销售同步结果
 */
export interface SalesSyncResult {
  success: boolean
  errorCode?: number
  errorMsg?: string
  shopId: number
  shopName: string
  syncStatus: number
  lastSyncTime: string
  lastUpdateTime: string
  totalRecords?: number
  skuTotalRecords?: number
  warehouseTotalRecords?: number
  message?: string
}

/**
 * 获取同步任务列表
 */
export function getSyncTasks(params?: SalesSyncParams) {
  return request({
    url: '/temu/sales/sync/tasks',
    method: 'post',
    data: params
  })
}

/**
 * 手动触发同步
 */
export function triggerSync(shopId: number) {
  return request({
    url: `/temu/sales/sync/trigger/${shopId}`,
    method: 'post'
  })
}

/**
 * 初始化同步任务
 */
export function initSyncTask(shopId: number) {
  return request({
    url: `/temu/sales/sync/init/${shopId}`,
    method: 'post'
  })
}

/**
 * 获取同步任务详情
 */
export function getSyncTask(shopId: number) {
  return request({
    url: `/temu/sales/sync/task/${shopId}`,
    method: 'get'
  })
}

/**
 * 批量初始化同步任务
 */
export function batchInitSyncTasks(params: SalesSyncParams) {
  return request({
    url: '/temu/sales/sync/batch/init',
    method: 'post',
    data: params
  })
}

/**
 * 批量触发同步
 */
export function batchTriggerSync(params: SalesSyncParams) {
  return request({
    url: '/temu/sales/sync/batch/trigger',
    method: 'post',
    data: params
  })
}

/**
 * 清空单个店铺的同步数据
 */
export function clearSyncData(shopId: number) {
  return request({
    url: `/temu/sales/sync/clear/${shopId}`,
    method: 'post'
  })
}

/**
 * 批量清空店铺同步数据
 */
export function batchClearSyncData(params: SalesSyncParams) {
  return request({
    url: '/temu/sales/sync/batch/clear',
    method: 'post',
    data: params
  })
} 