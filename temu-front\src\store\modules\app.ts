import { defineStore } from 'pinia'

interface AppState {
  sidebar: {
    opened: boolean
    withoutAnimation: boolean
  }
  device: 'desktop' | 'mobile'
  size: 'large' | 'default' | 'small'
  theme: 'light' | 'dark' | 'auto'
  themeColor: string
  layout: 'sidebar' | 'top' | 'mix'
  contentWidth: 'fixed' | 'fluid'
  tagsView: boolean
  fixedHeader: boolean
  showFooter: boolean
  enableTransition: boolean
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    sidebar: {
      opened: true,
      withoutAnimation: false
    },
    device: 'desktop',
    size: 'default',
    theme: 'light',
    themeColor: '#409EFF',
    layout: 'sidebar',
    contentWidth: 'fixed',
    tagsView: true,
    fixedHeader: true,
    showFooter: true,
    enableTransition: true
  }),
  actions: {
    // 切换侧边栏
    toggleSidebar() {
      this.sidebar.opened = !this.sidebar.opened
      this.sidebar.withoutAnimation = false
    },
    // 关闭侧边栏
    closeSidebar(withoutAnimation: boolean) {
      this.sidebar.opened = false
      this.sidebar.withoutAnimation = withoutAnimation
    },
    // 设备切换
    toggleDevice(device: 'desktop' | 'mobile') {
      this.device = device
    },
    // 设置大小
    setSize(size: 'large' | 'default' | 'small') {
      this.size = size
    },
    // 设置主题
    setTheme(theme: 'light' | 'dark' | 'auto') {
      this.theme = theme
    },
    // 设置主题颜色
    setThemeColor(color: string) {
      this.themeColor = color
    },
    // 设置布局模式
    setLayout(layout: 'sidebar' | 'top' | 'mix') {
      this.layout = layout
    },
    // 设置内容区域宽度
    setContentWidth(width: 'fixed' | 'fluid') {
      this.contentWidth = width
    },
    // 设置是否开启标签页
    setTagsView(show: boolean) {
      this.tagsView = show
    },
    // 设置是否固定头部
    setFixedHeader(fixed: boolean) {
      this.fixedHeader = fixed
    },
    // 设置是否显示页脚
    setShowFooter(show: boolean) {
      this.showFooter = show
    },
    // 设置是否开启页面切换动画
    setEnableTransition(enable: boolean) {
      this.enableTransition = enable
    }
  }
}) 