<template>
  <div class="export-button">
    <el-button 
      type="primary" 
      size="small" 
      :disabled="selectedRows.length === 0 || isExporting"
      @click="handleExport"
    >
      <el-icon class="button-icon"><Download /></el-icon>
      {{ isExporting ? '导出中...' : '导出' }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ElMessage, ElLoading } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'
import type { ShipOrder } from '@/types/shipOrder'
import { ref } from 'vue'

// 定义props，接收选中行数据
const props = defineProps({
  selectedRows: {
    type: Array as () => ShipOrder[],
    default: () => []
  }
})

// 导出状态
const isExporting = ref(false)

/**
 * 时间戳转换为日期时间字符串
 */
const formatTimestamp = (timestamp: number | null | undefined): string => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', { 
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

/**
 * 下载图片并转换为Buffer
 */
const downloadImageAsBuffer = async (url: string): Promise<ArrayBuffer | null> => {
  if (!url) return null
  
  try {
    const response = await fetch(url)
    if (!response.ok) {
      console.error('下载图片失败:', url)
      return null
    }
    return await response.arrayBuffer()
  } catch (error) {
    console.error('下载图片出错:', error)
    return null
  }
}

/**
 * 处理导出功能
 */
const handleExport = async () => {
  if (props.selectedRows.length === 0) {
    ElMessage.warning('请选择要导出的数据')
    return
  }
  
  const loadingInstance = ElLoading.service({
    fullscreen: true,
    text: '正在下载图片并导出数据...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  
  isExporting.value = true
  
  try {
    // 创建工作簿和工作表
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('发货单数据')
    
    // 设置列标题和宽度
    worksheet.columns = [
      { header: '序号', key: 'index', width: 10 },
      { header: '店铺代号', key: 'shopRemark', width: 20 },
      { header: '图片', key: 'image', width: 10 }, // 图片列宽度调整为10
      { header: '店铺名', key: 'shopName', width: 20 },
      { header: '发货批次', key: 'expressBatchSn', width: 20 },
      { header: '发货单号', key: 'deliveryOrderSn', width: 20 },
      { header: '备货单号', key: 'subPurchaseOrderSn', width: 20 },
      { header: 'SKC', key: 'productSkcId', width: 15 },
      { header: 'SKC货号', key: 'skcExtCode', width: 15 },
      { header: '发货数量', key: 'deliverSkcNum', width: 15 },
      { header: '包裹号', key: 'packageSn', width: 20 },
      { header: '已发货数量', key: 'deliverPackageNum', width: 15 },
      { header: '已收包裹数量', key: 'receivePackageNum', width: 15 },
      { header: '发货时间', key: 'deliverTime', width: 20 },
      { header: '收货时间', key: 'receiveTime', width: 20 },
      { header: '发货单状态', key: 'status', width: 15 }
    ]
    
    // 设置表头样式
    worksheet.getRow(1).font = { bold: true }
    worksheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' }
    worksheet.getRow(1).height = 25
    
    // 先添加所有行的数据
    for (let i = 0; i < props.selectedRows.length; i++) {
      const item = props.selectedRows[i]
      const subOrder = item.subPurchaseOrderBasicVO || {}
      
      // 获取包裹信息（取第一个包裹的包裹号）
      const packageSn = item.packageList && item.packageList.length > 0 
        ? item.packageList[0].packageSn 
        : ''
        
      // 添加行数据
      worksheet.addRow({
        index: i + 1,
        shopRemark: item.shopRemark || '',
        image: '', // 图片占位符
        shopName: item.shopName || '',
        expressBatchSn: item.expressBatchSn || '',
        deliveryOrderSn: item.deliveryOrderSn || '',
        subPurchaseOrderSn: item.subPurchaseOrderSn || '',
        productSkcId: item.productSkcId || subOrder.productSkcId || '',
        skcExtCode: item.skcExtCode || subOrder.skcExtCode || '',
        deliverSkcNum: item.deliverSkcNum || 0,
        packageSn: packageSn || '',
        deliverPackageNum: item.deliverPackageNum || 0,
        receivePackageNum: item.receivePackageNum || 0,
        deliverTime: formatTimestamp(item.deliverTime),
        receiveTime: formatTimestamp(item.receiveTime),
        status: getStatusText(item.status)
      })
      
      // 设置行高和对齐方式
      const rowIndex = i + 2 // 第1行是表头，数据从第2行开始
      worksheet.getRow(rowIndex).height = 80
      worksheet.getRow(rowIndex).alignment = { vertical: 'middle', horizontal: 'center' }
    }
    
    // 单独处理所有图片（在所有行数据添加完成后）
    const downloadPromises: Promise<void>[] = []
    
    for (let i = 0; i < props.selectedRows.length; i++) {
      const rowIndex = i + 2 // 行索引（第一行是表头）
      const item = props.selectedRows[i]
      const subOrder = item.subPurchaseOrderBasicVO || {}
      
      const downloadPromise = (async () => {
        const imageUrl = subOrder.productSkcPicture || ''
        if (imageUrl) {
          try {
            const imageBuffer = await downloadImageAsBuffer(imageUrl)
            if (imageBuffer) {
              const imageId = workbook.addImage({
                buffer: imageBuffer,
                extension: 'png', // png格式更通用
              })
              
              // 使用单元格索引方式添加图片，确保图片正确定位
              worksheet.addImage(imageId, {
                tl: { col: 2, row: rowIndex - 1 },
                br: { col: 3, row: rowIndex },
                editAs: 'oneCell'
              } as any)
              
              // 设置单元格显示一个占位符文本，防止单元格为空
              worksheet.getCell(`C${rowIndex}`).value = '图片'
              
              console.log(`成功添加图片到第${rowIndex}行`)
            } else {
              // 图片加载失败时填入提示文字
              worksheet.getCell(`C${rowIndex}`).value = '图片加载失败'
              console.warn(`无法加载图片: ${imageUrl}`)
            }
          } catch (error) {
            worksheet.getCell(`C${rowIndex}`).value = '图片处理错误'
            console.error(`处理图片出错 ${imageUrl}:`, error)
          }
        } else {
          // 没有图片URL时填入提示文字
          worksheet.getCell(`C${rowIndex}`).value = '无图片'
          console.warn(`第${rowIndex}行没有图片URL`)
        }
      })()
      
      downloadPromises.push(downloadPromise)
    }
    
    // 等待所有图片处理完成
    await Promise.all(downloadPromises)
    
    // 导出文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    
    // 获取店铺备注信息
    let shopRemark = '未知店铺'
    if (props.selectedRows.length > 0 && props.selectedRows[0].shopRemark) {
      shopRemark = props.selectedRows[0].shopRemark
    }
    
    // 判断类型是JIT还是备货
    let orderType = '备货'
    if (props.selectedRows.some(item => item.purchaseStockType === 1 || 
        (item.subPurchaseOrderBasicVO && item.subPurchaseOrderBasicVO.purchaseStockType === 1))) {
      orderType = 'JIT'
    }
    
    // 格式化日期时间
    const now = new Date()
    const dateTimeStr = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}`
    
    // 生成文件名
    const fileName = `${shopRemark}_${orderType}_${dateTimeStr}.xlsx`
    
    saveAs(blob, fileName)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后再试')
  } finally {
    loadingInstance.close()
    isExporting.value = false
  }
}

// 根据状态码获取状态文本
const getStatusText = (status?: number): string => {
  const statusMap: Record<string, string> = {
    '0': '待装箱发货',
    '1': '待仓库发货',
    '2': '已收货',
    '5': '已取消',
    '6': '部分收货'
  }
  
  return status !== undefined ? (statusMap[status.toString()] || '未知状态') : '未知状态'
}
</script>

<style scoped>
.export-button {
  display: inline-block;
}

.button-icon {
  margin-right: 4px;
  font-size: 12px;
}
</style> 