import request from '@/utils/request'
import type { DataPermission } from '@/types/dataPermission'

/**
 * 获取角色的数据权限信息
 * @param roleId 角色ID
 * @returns 数据权限信息
 */
export function getDataPermission(roleId: number) {
  return request<DataPermission>({
    url: `/system/dataPermission/${roleId}`,
    method: 'get'
  })
}

/**
 * 设置角色的数据权限
 * @param data 数据权限信息
 * @returns 操作结果
 */
export function setDataPermission(data: DataPermission) {
  return request({
    url: '/system/dataPermission',
    method: 'post',
    data
  })
}

/**
 * 获取所有角色的数据权限
 * @returns 所有角色的数据权限列表
 */
export function listDataPermissions() {
  return request<DataPermission[]>({
    url: '/system/dataPermission/list',
    method: 'get'
  })
} 