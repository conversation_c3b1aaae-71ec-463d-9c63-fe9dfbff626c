package com.xiao.temu.modules.sales.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 销售SKU数量详情实体类
 * 对应数据库表：sales_sku_quantity
 * 存储每个SKU的销量、库存天数等详细信息
 */
@Data
@TableName("sales_sku_quantity")
public class SalesSkuQuantity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品SKC ID
     */
    private Long productSkcId;

    /**
     * 商品SKU ID
     */
    private Long productSkuId;

    /**
     * 备货仓组ID
     */
    private Long warehouseGroupId;

    /**
     * 备货仓组名称
     */
    private String warehouseGroupName;

    /**
     * 尺码名称
     */
    private String className;

    /**
     * SKU货号
     */
    private String skuExtCode;

    /**
     * 仓组下是否可以备货
     */
    private Boolean canPurchase;

    /**
     * 备货天数
     */
    private Integer stockDays;

    /**
     * 安全库存天数
     */
    private Integer safeInventoryDays;

    /**
     * 下单逻辑：安全库存天数+备货天数
     */
    private String purchaseConfig;

    /**
     * 核价状态
     */
    private Integer priceReviewStatus;

    /**
     * 是否核价通过
     */
    private Boolean isVerifyPrice;

    /**
     * 是否降低供货价通过
     */
    private Boolean isReducePricePass;

    /**
     * 今日销量
     */
    private Integer todaySaleVolume;

    /**
     * 总销量
     */
    private Integer totalSaleVolume;

    /**
     * 近7天销量
     */
    private Integer lastSevenDaysSaleVolume;

    /**
     * 近30天销量
     */
    private Integer lastThirtyDaysSaleVolume;

    /**
     * 用户加购数量
     */
    private Integer inCartNumber;

    /**
     * 近7天用户加购数量
     */
    @TableField("in_cart_number_7d")
    private Integer inCartNumber7d;

    /**
     * 缺货数量
     */
    private Integer lackQuantity;

    /**
     * 建议下单量
     */
    private Integer adviceQuantity;

    /**
     * 申报价格
     */
    private BigDecimal supplierPrice;

    /**
     * 可售天数
     */
    private String availableSaleDays;

    /**
     * 库存可售天数
     */
    private String availableSaleDaysFromInventory;

    /**
     * 仓内库存可售天数
     */
    private String warehouseAvailableSaleDays;

    /**
     * 7日销量参考
     */
    private String sevenDaysSaleReference;

    /**
     * 七日销量参考类型 1.7日最大销量 2.7日日均销量
     */
    private Integer sevenDaysReferenceSaleType;

    /**
     * 同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 