/**
 * 扫码页面
 */
const scanUtils = require('./utils/scanUtils');
const normalScanHandler = require('./utils/normalScanHandler');
const fastScanHandler = require('./utils/fastScanHandler');
const batchScanHandler = require('./utils/batchScanHandler');
const userRoleManager = require('./utils/userRoleManager');
const progressConfig = require('../../utils/progressConfig');
const pageRefreshUtils = require('../../utils/pageRefreshUtils');

// 定义页面路径常量
const PAGE_PATH = 'pages/scan/scan';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    scanResult: null,
    scanVisible: true,
    processing: false,
    // 标签页切换
    activeTab: 'fast', // 默认显示快速扫码标签页
    // 用户角色列表
    userRoles: [], 
    // 选中的工序类型
    selectedProgressTypes: [], 
    // 快速扫码开关
    fastScanEnabled: true,
    // 可用于更新生产进度的角色列表
    validProgressRoles: progressConfig.VALID_PROGRESS_ROLES,
    roleProgressMap: progressConfig.PROGRESS_TYPE_MAP,
    // 角色样式映射
    roleStyleMap: {
      'cutting': 'cutting-role',
      'workshop': 'workshop-role',
      'trimming': 'trimming-role',
      'inspection': 'inspection-role',
      'packaging': 'packaging-role',
      'shipping': 'shipping-role'
    },
    showRoleSelection: false, // 是否显示角色选择
    availableProgressTypes: [],
    forceSewingUpdate: 0,
    // 为每种工序添加单独的强制更新变量
    forceBurningUpdate: 0,
    // forceSewingUpdate已存在，不需要重复
    forceTailUpdate: 0,
    forceShippingUpdate: 0,
    forceDeliveryUpdate: 0,
    forceInspectionUpdate: 0, // 添加查货的强制更新变量
    // 添加页面版本号，用于强制刷新
    pageVersion: Date.now(),
    // 批量备货单模式
    batchMode: false,
    // 批量备货单列表
    batchOrders: [],
    // 当前选中的批量备货单索引
    currentBatchIndex: 0,
    // 批量处理进度列表 (记录每个备货单的工序完成情况)
    batchProgressList: [],
    // 工序项目列表（用于在wxml中循环生成）
    progressItems: [
      { type: 'cutting', status: 'cuttingStatus', name: '烧花/剪图' },
      { type: 'workshop', status: 'workshopStatus', name: '车间/拣货' },
      { type: 'trimming', status: 'trimmingStatus', name: '剪线/压图' },
      { type: 'inspection', status: 'inspectionStatus', name: '查货' },
      { type: 'packaging', status: 'packagingStatus', name: '包装' },
      { type: 'shipping', status: 'shippingStatus', name: '发货' }
    ],
    // 选中的用户角色 (用于新的快速扫码功能)
    selectedUserRoles: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function () {
    console.log('扫码页面加载');
    try {
      // 加载用户信息
      userRoleManager.loadUserInfo(this);
      
      // 首次启动时设置默认值：快速扫码模式
      let fastScanEnabled = wx.getStorageSync('fastScanEnabled');
      if (fastScanEnabled === '') {
        fastScanEnabled = true;
        wx.setStorageSync('fastScanEnabled', true);
      }
      
      // 首次启动时设置默认标签页为快速扫码
      let activeTab = wx.getStorageSync('activeTab');
      if (!activeTab) {
        activeTab = 'fast';
        wx.setStorageSync('activeTab', 'fast');
      }
      
      this.setData({ 
        fastScanEnabled,
        activeTab
      });
      
      // 从本地存储中读取选中角色
      let selectedUserRoles = wx.getStorageSync('selectedUserRoles') || [];
      
      // 筛选出用户当前拥有的角色，移除那些不再拥有的角色
      if (this.data.userRoles && this.data.userRoles.length > 0) {
        const validRoleKeys = this.data.userRoles.map(role => role.roleKey);
        selectedUserRoles = selectedUserRoles.filter(role => validRoleKeys.includes(role));
      }
      
      this.setData({ 
        selectedUserRoles,
        pageVersion: Date.now() // 强制刷新视图
      });
      
      // 更新progressItems
      this.updateProgressItems();
      
      console.log('页面加载完成，progressItems已初始化');
      
      // 延迟初始化复选框状态
      setTimeout(() => {
        this.updateCheckboxStatus();
      }, 300);
      
      // 记录页面访问
      pageRefreshUtils.recordPageVisit(PAGE_PATH);
    } catch (error) {
      console.error('页面加载过程中发生错误:', error);
    }
  },

  /**
   * 更新工序项目列表
   */
  updateProgressItems: function() {
    try {
      console.log('开始更新工序项目列表');
      if (!progressConfig || !progressConfig.PROGRESS_TYPE_MAP) {
        console.error('progressConfig未正确加载');
        return;
      }
      
      const progressItems = [];
      progressConfig.VALID_PROGRESS_ROLES.forEach(type => {
        progressItems.push({
          type: type,
          status: `${type}Status`,
          name: progressConfig.PROGRESS_TYPE_MAP[type],
          forceUpdateVar: `force${type.charAt(0).toUpperCase() + type.slice(1)}Update`
        });
      });
      
      console.log('工序项目列表已生成:', progressItems);
      this.setData({ progressItems });
    } catch (error) {
      console.error('更新工序项目列表时发生错误:', error);
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.log('扫码页面显示 - 完全重置状态');
    
    // 记录页面访问
    pageRefreshUtils.recordPageVisit(PAGE_PATH);
    
    // 检查页面是否需要刷新
    const needRefresh = pageRefreshUtils.needRefresh(PAGE_PATH) || 
                        pageRefreshUtils.isMarkedForRefresh(PAGE_PATH);
                        
    if (needRefresh) {
      console.log('页面需要刷新');
      this.resetPageState();
      pageRefreshUtils.clearRefreshMark(PAGE_PATH);
    } else {
      // 如果不需要全局刷新，仍然确保标签页设置正确
      // 首次显示时设置默认值：快速扫码模式
      let fastScanEnabled = wx.getStorageSync('fastScanEnabled');
      if (fastScanEnabled === '') {
        fastScanEnabled = true;
        wx.setStorageSync('fastScanEnabled', true);
      } else {
        this.setData({ fastScanEnabled });
      }
      
      // 首次显示时设置默认标签页为快速扫码
      let activeTab = wx.getStorageSync('activeTab');
      if (!activeTab) {
        activeTab = 'fast';
        wx.setStorageSync('activeTab', 'fast');
      } else {
        this.setData({ activeTab });
      }
    }
    
    // 每次页面显示时都重新加载用户信息，确保获取最新角色
    userRoleManager.loadUserInfo(this);
    
    // 完全重置扫码相关状态
    this.setData({
      scanResult: null,
      scanVisible: true,
      processing: false,
      showRoleSelection: false,
      selectedProgressTypes: [],
      availableProgressTypes: [],
      
      // 重置所有工序的强制更新变量
      forceBurningUpdate: this.data.forceBurningUpdate + 1,
      forceSewingUpdate: this.data.forceSewingUpdate + 1,
      forceTailUpdate: this.data.forceTailUpdate + 1,
      forceShippingUpdate: this.data.forceShippingUpdate + 1,
      forceDeliveryUpdate: this.data.forceDeliveryUpdate + 1,
      forceInspectionUpdate: this.data.forceInspectionUpdate + 1,
      
      // 重置批量模式相关状态
      batchMode: false,
      batchOrders: [],
      batchProgressList: [],
      currentBatchIndex: 0,
      
      // 更新页面版本，强制刷新视图
      pageVersion: Date.now()
    });
    
    // 更新工序项目列表
    this.updateProgressItems();
    
    // 从本地存储中重新读取选中角色
    let selectedUserRoles = wx.getStorageSync('selectedUserRoles') || [];
    
    // 筛选出用户当前拥有的角色，移除那些不再拥有的角色
    if (this.data.userRoles && this.data.userRoles.length > 0) {
      const validRoleKeys = this.data.userRoles.map(role => role.roleKey);
      selectedUserRoles = selectedUserRoles.filter(role => validRoleKeys.includes(role));
    }
    
    this.setData({ selectedUserRoles });
    
    // 延迟初始化复选框状态
    setTimeout(() => {
      this.updateCheckboxStatus();
    }, 300);
    
    // 重置组件内部状态
    this.resetComponentStates();
  },

  /**
   * 重置页面状态
   */
  resetPageState: function() {
    // 完全重置扫码相关状态
    this.setData({
      scanResult: null,
      scanVisible: true,
      processing: false,
      showRoleSelection: false,
      selectedProgressTypes: [],
      availableProgressTypes: [],
      
      // 重置所有工序的强制更新变量
      forceBurningUpdate: this.data.forceBurningUpdate + 1,
      forceSewingUpdate: this.data.forceSewingUpdate + 1,
      forceTailUpdate: this.data.forceTailUpdate + 1,
      forceShippingUpdate: this.data.forceShippingUpdate + 1,
      forceDeliveryUpdate: this.data.forceDeliveryUpdate + 1,
      forceInspectionUpdate: this.data.forceInspectionUpdate + 1,
      
      // 重置批量模式相关状态
      batchMode: false,
      batchOrders: [],
      batchProgressList: [],
      currentBatchIndex: 0,
      
      // 更新页面版本，强制刷新视图
      pageVersion: Date.now()
    });
    
    // 更新工序项目列表
    this.updateProgressItems();
    
    // 首次重置时设置默认值：快速扫码模式
    let fastScanEnabled = wx.getStorageSync('fastScanEnabled');
    if (fastScanEnabled === '') {
      fastScanEnabled = true;
      wx.setStorageSync('fastScanEnabled', true);
    } else {
      this.setData({ fastScanEnabled });
    }
    
    // 首次重置时设置默认标签页为快速扫码
    let activeTab = wx.getStorageSync('activeTab');
    if (!activeTab) {
      activeTab = 'fast';
      wx.setStorageSync('activeTab', 'fast');
    } else {
      this.setData({ activeTab });
    }
    
    // 从本地存储中重新读取选中角色
    let selectedUserRoles = wx.getStorageSync('selectedUserRoles') || [];
    
    // 筛选出用户当前拥有的角色，移除那些不再拥有的角色
    if (this.data.userRoles && this.data.userRoles.length > 0) {
      const validRoleKeys = this.data.userRoles.map(role => role.roleKey);
      selectedUserRoles = selectedUserRoles.filter(role => validRoleKeys.includes(role));
    }
    
    this.setData({ selectedUserRoles });
    
    // 延迟初始化复选框状态
    setTimeout(() => {
      this.updateCheckboxStatus();
    }, 300);
    
    // 重置组件内部状态
    this.resetComponentStates();
  },

  /**
   * 重置组件内部状态
   */
  resetComponentStates: function() {
    // 获取页面内所有组件实例（如果有）
    const components = this.selectAllComponents('.scan-component');
    if (components && components.length > 0) {
      components.forEach(component => {
        // 如果组件有reset方法，则调用
        if (typeof component.reset === 'function') {
          component.reset();
        }
      });
    }
    
    // 重置扫码界面状态
    this.resetScannerState();
  },

  /**
   * 生命周期函数--监听页面隐藏
   * 当页面隐藏时，标记其他页面需要刷新
   */
  onHide: function() {
    console.log('扫码页面隐藏');
    // 如果有扫描结果，则标记其他页面需要刷新
    if (this.data.scanResult) {
      pageRefreshUtils.markPageForRefresh('pages/home/<USER>');
      pageRefreshUtils.markPageForRefresh('pages/worklist/worklist');
    }
  },

  /**
   * 切换标签页
   */
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ 
      activeTab: tab,
      // 如果切换到快速扫码标签页，自动启用快速扫码模式
      fastScanEnabled: tab === 'fast' ? true : false
    });
    // 保存到本地存储
    wx.setStorageSync('activeTab', tab);
    wx.setStorageSync('fastScanEnabled', tab === 'fast');
  },

  /**
   * 扫描二维码
   */
  scanCode: function () {
    console.log('触发扫码功能');
    scanUtils.performScan(this);
  },

  /**
   * 从相册选择图片
   */
  chooseImage: function () {
    console.log('从相册选择图片');
    scanUtils.chooseImageForScan(this);
  },

  /**
   * 处理扫码结果
   */
  handleScanResult: function (qrData) {
    // 快速扫码模式下且选择了角色，直接处理结果
    if (this.data.activeTab === 'fast' && this.data.selectedUserRoles.length > 0) {
      console.log('快速扫码模式，选中的角色:', this.data.selectedUserRoles);
      fastScanHandler.handleFastScanResult(this, qrData);
    } else {
      console.log('普通扫码模式，显示工序选择界面');
      normalScanHandler.handleScanResult(this, qrData);
    }
  },

  /**
   * 判断用户是否有权限编辑某个工序
   * 注意：该方法会在WXML中通过wx:if调用
   */
  canEditProgress: function(progressType) {
    return normalScanHandler.canEditProgress(this, progressType);
  },

  /**
   * 切换工序选择状态
   */
  toggleSelectProgress: function(e) {
    normalScanHandler.toggleSelectProgress(this, e);
  },
  
  /**
   * 检查用户是否有权限操作某个工序
   */
  checkUserProgressPermission: function(progressType) {
    return userRoleManager.checkUserProgressPermission(this, progressType);
  },

  /**
   * 提交选中的工序
   */
  submitSelectedProgress: function() {
    if (this.data.batchMode) {
      batchScanHandler.submitCurrentBatchProgress(this);
    } else {
      normalScanHandler.submitSelectedProgress(this);
    }
  },

  /**
   * 更新进度数据
   */
  updateProgressData: function(progressTypes) {
    normalScanHandler.updateProgressData(this, progressTypes);
  },
  
  /**
   * 刷新进度详情
   */
  refreshProgressDetail: function(shopId, subPurchaseOrderSn) {
    normalScanHandler.refreshProgressDetail(this, shopId, subPurchaseOrderSn);
  },

  /**
   * 进入进度详情页面
   */
  goToProgress: function () {
    const { scanResult } = this.data;
    if (scanResult) {
      console.log('跳转到处理页面，携带信息:', scanResult);
      wx.navigateTo({
        url: `/pages/progress/progress?subPurchaseOrderSn=${scanResult.subPurchaseOrderSn}&shopId=${scanResult.shopId}`,
      });
    }
  },

  /**
   * 返回首页
   */
  goToHome: function () {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  /**
   * 跳转到工作列表
   */
  goToWorklist: function () {
    wx.switchTab({
      url: '/pages/worklist/worklist'
    });
  },

  /**
   * 自动选择用户角色进行快速扫码
   */
  autoSelectUserRoleProgress: function(progressDetail, qrData) {
    fastScanHandler.autoSelectUserRoleProgress(this, progressDetail, qrData);
  },

  /**
   * 重置扫码状态
   */
  backToScan: function() {
    this.setData({
      scanResult: null,
      scanVisible: true,
      processing: false,
      selectedProgressTypes: [],
      pageVersion: Date.now()
    });
  },

  /**
   * 重置扫码界面状态
   */
  resetScannerState: function() {
    console.log('重置扫码界面状态');
    scanUtils.resetScannerState(this);
  },

  /**
   * 处理批量扫码结果
   */
  handleBatchScanResult: function (batchData) {
    batchScanHandler.handleBatchScanResult(this, batchData);
  },
  
  /**
   * 加载批量备货单数据
   */
  loadBatchOrdersData: function(orders) {
    batchScanHandler.loadBatchOrdersData(this, orders);
  },
  
  /**
   * 加载指定备货单的进度详情
   */
  loadOrderProgressDetail: function(order, index, allOrders, progressList) {
    batchScanHandler.loadOrderProgressDetail(this, order, index, allOrders, progressList);
  },
  
  /**
   * 显示批量备货单界面
   */
  displayBatchOrdersUI: function(orders, progressList) {
    batchScanHandler.displayBatchOrdersUI(this, orders, progressList);
  },
  
  /**
   * 切换显示下一个批量备货单
   */
  showNextBatchOrder: function() {
    batchScanHandler.showNextBatchOrder(this);
  },
  
  /**
   * 切换显示上一个批量备货单
   */
  showPrevBatchOrder: function() {
    batchScanHandler.showPrevBatchOrder(this);
  },

  /**
   * 提交当前批量备货单的选中工序
   */
  submitCurrentBatchProgress: function() {
    batchScanHandler.submitCurrentBatchProgress(this);
  },
  
  /**
   * 更新批量备货单的进度数据
   */
  updateBatchOrderProgress: function(order, progressTypes) {
    batchScanHandler.updateBatchOrderProgress(this, order, progressTypes);
  },
  
  /**
   * 刷新当前批量备货单的进度信息
   */
  refreshCurrentBatchProgress: function(order) {
    batchScanHandler.refreshCurrentBatchProgress(this, order);
  },

  /**
   * 完成所有批量备货单的选中工序
   */
  completeAllBatchOrders: function() {
    batchScanHandler.completeAllBatchOrders(this);
  },

  /**
   * 处理批量订单
   */
  processBatchOrders: function(orders) {
    batchScanHandler.processBatchOrders(this, orders);
  },

  /**
   * 执行批量进度更新
   */
  executeBatchProgress: function(orders, progressTypes) {
    batchScanHandler.executeBatchProgress(this, orders, progressTypes);
  },

  /**
   * 切换用户角色选择状态
   */
  toggleUserRole: function(e) {
    const role = e.currentTarget.dataset.role;
    console.log('切换角色选择状态:', role);
    
    let { selectedUserRoles } = this.data;
    const index = selectedUserRoles.indexOf(role);
    
    if (index > -1) {
      // 如果已选中，则取消选中
      selectedUserRoles.splice(index, 1);
    } else {
      // 如果未选中，则添加到选中列表
      selectedUserRoles.push(role);
    }
    
    this.setData({ 
      selectedUserRoles,
      pageVersion: Date.now() // 强制刷新视图
    });
    
    // 保存到本地存储
    wx.setStorageSync('selectedUserRoles', selectedUserRoles);
    
    // 显示提示
    wx.showToast({
      title: selectedUserRoles.length > 0 ? `已选择${selectedUserRoles.length}个工序` : '未选择工序',
      icon: 'none',
      duration: 1500
    });
    
    // 手动更新复选框状态
    this.updateCheckboxStatus();
  },

  /**
   * 处理角色复选框变更事件
   */
  handleRoleCheckboxChange: function(e) {
    console.log('复选框状态变更:', e);
    const { value, checked } = e.detail;
    let { selectedUserRoles } = this.data;
    
    if (checked && !selectedUserRoles.includes(value)) {
      // 选中状态且未在列表中，添加
      selectedUserRoles.push(value);
    } else if (!checked && selectedUserRoles.includes(value)) {
      // 未选中状态且在列表中，移除
      selectedUserRoles = selectedUserRoles.filter(role => role !== value);
    }
    
    this.setData({
      selectedUserRoles,
      pageVersion: Date.now()
    });
    
    // 保存到本地存储
    wx.setStorageSync('selectedUserRoles', selectedUserRoles);
    
    // 显示提示
    wx.showToast({
      title: selectedUserRoles.length > 0 ? `已选择${selectedUserRoles.length}个工序` : '未选择工序',
      icon: 'none',
      duration: 1500
    });
  },

  /**
   * 处理工序复选框变更事件
   */
  handleProgressCheckboxChange: function(e) {
    console.log('工序复选框状态变更:', e);
    const { value, checked } = e.detail;
    let { selectedProgressTypes } = this.data;
    
    if (checked && !selectedProgressTypes.includes(value)) {
      // 选中状态且未在列表中，添加
      selectedProgressTypes.push(value);
    } else if (!checked && selectedProgressTypes.includes(value)) {
      // 未选中状态且在列表中，移除
      selectedProgressTypes = selectedProgressTypes.filter(type => type !== value);
    }
    
    this.setData({
      selectedProgressTypes,
      pageVersion: Date.now()
    });
  },

  /**
   * 更新复选框状态
   * 确保复选框组件状态与数据同步
   */
  updateCheckboxStatus: function() {
    // 使用nextTick确保DOM已更新
    setTimeout(() => {
      // 更新角色复选框
      if (this.data.userRoles) {
        this.data.userRoles.forEach(role => {
          const checkboxId = `checkbox-${role.roleKey}`;
          const checkbox = this.selectComponent(`#${checkboxId}`);
          if (checkbox) {
            const isChecked = this.data.selectedUserRoles.includes(role.roleKey);
            checkbox.setData({ isChecked });
          }
        });
      }
      
      // 如果在结果页面，更新工序复选框
      if (this.data.scanResult) {
        this.data.progressItems.forEach(item => {
          const checkboxId = `progress-checkbox-${item.type}`;
          const checkbox = this.selectComponent(`#${checkboxId}`);
          if (checkbox) {
            const isChecked = this.data.selectedProgressTypes.includes(item.type);
            checkbox.setData({ isChecked });
          }
        });
      }
    }, 50);
  }
})