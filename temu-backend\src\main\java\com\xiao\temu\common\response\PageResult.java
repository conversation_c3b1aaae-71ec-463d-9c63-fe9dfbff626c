package com.xiao.temu.common.response;

import com.xiao.temu.modules.violation.dto.ShopViolationInfoDTO;

import java.util.List;

/**
 * 分页结果
 * @param <T> 数据类型
 */
public class PageResult<T> {
    
    /** 总记录数 */
    private long total;
    
    /** 当前页码 */
    private int pageNum;
    
    /** 每页记录数 */
    private int pageSize;
    
    /** 数据列表 */
    private List<T> list;

    public PageResult(List<ShopViolationInfoDTO> records, long total, Integer pageNum, Integer pageSize) {
    }

    public PageResult(long total, List<T> list) {
        this.total = total;
        this.list = list;
    }

    public PageResult(long total, int pageNum, int pageSize, List<T> list) {
        this.total = total;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.list = list;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }
} 