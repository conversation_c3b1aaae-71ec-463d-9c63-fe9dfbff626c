/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : temu_api

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 03/06/2025 15:42:09
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for purchase_order_notification_config
-- ----------------------------
DROP TABLE IF EXISTS `purchase_order_notification_config`;
CREATE TABLE `purchase_order_notification_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `notification_type` int NOT NULL COMMENT '通知类型(1:JIT即将逾期 2:JIT已逾期 3:普通备货未发货 4:普通备货未到货)',
  `notification_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知名称',
  `enabled` int NOT NULL DEFAULT 1 COMMENT '是否启用(0:禁用 1:启用)',
  `check_interval` int NULL DEFAULT 24 COMMENT '检查间隔(小时)',
  `normal_trigger_days` int NULL DEFAULT NULL COMMENT '普通备货触发天数',
  `jit_trigger_hours` int NULL DEFAULT NULL COMMENT 'JIT备货触发小时数',
  `max_notify_count` int NOT NULL DEFAULT 3 COMMENT '最大通知次数',
  `notify_interval_hours` int NULL DEFAULT 24 COMMENT '通知间隔(小时)',
  `template_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息模板代码',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_notification_type`(`notification_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '备货单通知配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of purchase_order_notification_config
-- ----------------------------
INSERT INTO `purchase_order_notification_config` VALUES (1, 1, 'JIT即将逾期通知', 1, 24, NULL, 24, 1, 24, 'JIT_SOON_OVERDUE', '2025-05-31 13:44:08', '2025-06-01 18:05:50');
INSERT INTO `purchase_order_notification_config` VALUES (2, 2, 'JIT已逾期通知', 1, 24, NULL, 24, 3, 24, 'JIT_OVERDUE', '2025-05-31 21:44:08', '2025-05-31 23:44:52');
INSERT INTO `purchase_order_notification_config` VALUES (3, 3, '普通备货未发货通知', 1, 24, 5, NULL, 3, 24, 'NORMAL_NOT_DELIVERED', '2025-05-31 21:44:08', '2025-05-31 23:44:53');
INSERT INTO `purchase_order_notification_config` VALUES (4, 4, '普通备货未到货通知', 1, 24, 5, NULL, 3, 24, 'NORMAL_NOT_RECEIVED', '2025-05-31 21:44:08', '2025-05-31 23:44:54');

SET FOREIGN_KEY_CHECKS = 1;
