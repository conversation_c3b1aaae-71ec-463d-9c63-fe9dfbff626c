/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : temu_api

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 04/06/2025 02:02:56
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码',
  `nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE,
  UNIQUE INDEX `idx_username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 42 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 'admin', 'jZae727K08KaOmKSgOaGzww/XVqGr/PKEgIMkjrcbJI=', '系统管理员', '<EMAIL>', '13800000000', '0', '2025-03-20 19:07:49', '2025-04-01 22:28:07', '系统管理员');
INSERT INTO `sys_user` VALUES (5, '组长1', 'jZae727K08KaOmKSgOaGzww/XVqGr/PKEgIMkjrcbJI=', '组长1', '', '13800000001', '0', '2025-03-22 13:02:33', '2025-04-03 14:07:59', '组长1');
INSERT INTO `sys_user` VALUES (6, '运营2', 'jZae727K08KaOmKSgOaGzww/XVqGr/PKEgIMkjrcbJI=', '运营2', '', '13800000002', '0', '2025-03-22 13:50:34', '2025-03-24 18:52:05', '运营2');
INSERT INTO `sys_user` VALUES (7, '组长2', 'jZae727K08KaOmKSgOaGzww/XVqGr/PKEgIMkjrcbJI=', '组长2', '', '13800000003', '0', '2025-03-22 23:54:04', '2025-03-24 18:52:09', '组长2');
INSERT INTO `sys_user` VALUES (8, '运营1', 'jZae727K08KaOmKSgOaGzww/XVqGr/PKEgIMkjrcbJI=', '运营1', '', '13800000004', '0', '2025-03-23 15:52:32', '2025-03-24 18:52:13', '运营1');
INSERT INTO `sys_user` VALUES (22, '运营管理员', 'jZae727K08KaOmKSgOaGzww/XVqGr/PKEgIMkjrcbJI=', '运营管理员', '', '', '0', '2025-04-03 09:52:56', '2025-04-04 19:14:59', '');
INSERT INTO `sys_user` VALUES (23, 'sh1', 'jZae727K08KaOmKSgOaGzww/XVqGr/PKEgIMkjrcbJI=', 'sh1', '', '', '0', '2025-05-08 17:15:40', '2025-05-28 09:36:01', '');
INSERT INTO `sys_user` VALUES (34, 'cf1', '5RYhQ0zm5LBBBiaQF94sUCFT3mURvKyX+16PPn4oWAs=', 'cf1', NULL, '18877666441', '0', '2025-05-12 08:37:21', '2025-05-28 09:35:58', NULL);
INSERT INTO `sys_user` VALUES (39, '生产组长1', 'jZae727K08KaOmKSgOaGzww/XVqGr/PKEgIMkjrcbJI=', '生产组长1', '', '', '0', '2025-05-28 09:39:15', NULL, '');
INSERT INTO `sys_user` VALUES (40, 'xiao', 'jZae727K08KaOmKSgOaGzww/XVqGr/PKEgIMkjrcbJI=', 'xiao', '', '', '0', '2025-05-28 10:24:46', NULL, '');
INSERT INTO `sys_user` VALUES (41, '222', 'jZae727K08KaOmKSgOaGzww/XVqGr/PKEgIMkjrcbJI=', '222', '', '', '0', '2025-05-28 12:29:06', NULL, '');

SET FOREIGN_KEY_CHECKS = 1;
