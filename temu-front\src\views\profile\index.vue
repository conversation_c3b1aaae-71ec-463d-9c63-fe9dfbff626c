<template>
  <div class="profile-container">
    <el-card class="profile-card" shadow="hover">
      <template #header>
        <div class="profile-header">
          <div class="user-info">
            <div class="avatar-container">
              <el-avatar :size="80" :src="userAvatar">
                {{ userStore.name?.charAt(0).toUpperCase() }}
              </el-avatar>
            </div>
            <div class="user-details">
              <h2>个人中心</h2>
              <p>
                {{ userStore.name }} 
                <span v-if="roleNames.length" class="user-roles">
                  <el-tag 
                    v-for="(role, index) in roleNames" 
                    :key="role" 
                    size="small" 
                    effect="plain" 
                    :type="index % 2 === 0 ? 'primary' : 'success'"
                    class="role-tag"
                  >
                    {{ role }}
                  </el-tag>
                </span>
              </p>
            </div>
          </div>
        </div>
      </template>
      
      <el-tabs v-model="activeTab" class="profile-tabs" type="border-card">
        <el-tab-pane label="个人信息" name="info">
          <user-info @rolesLoaded="updateRoles" />
        </el-tab-pane>
        <el-tab-pane label="修改密码" name="password">
          <reset-password />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'
import UserInfo from './components/UserInfo.vue'
import ResetPassword from './components/ResetPassword.vue'
import { getRouters } from '@/api/menu'
import { ElMessage } from 'element-plus'

// 添加stores
const userStore = useUserStore()
const permissionStore = usePermissionStore()

// 获取用户头像
const userAvatar = computed(() => {
  return userStore.avatar || ''
})

// 当前激活的选项卡
const activeTab = ref('info')

// 用户角色名称
const roleNames = ref<string[]>([])

// 更新角色信息
const updateRoles = (roles: any[]) => {
  roleNames.value = roles.map(role => role.roleName)
}

// 在组件挂载时，确保加载菜单数据
onMounted(async () => {
  try {
    // 获取用户信息
    if (!userStore.name) {
        await userStore.getInfo()
    }
    
    // 获取用户角色信息，用于显示在页面上
    try {
      const { data } = await getRouters()
      if (data && userStore.roles && userStore.roles.length > 0) {
        // 已有角色信息，更新显示
        updateRoles(userStore.roles.map(role => ({ roleName: role })))
      }
    } catch (error) {
      console.warn('获取角色信息失败，但不影响页面显示:', error)
    }
  } catch (error) {
    console.error('个人中心加载用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  }
})
</script>

<style lang="scss" scoped>
.profile-container {
  padding: 30px;
  min-height: calc(100vh - 60px);
  background-color: var(--theme-bg-color, #f5f7fa);
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.profile-card {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  border-radius: 12px;
  background-color: var(--theme-bg-color, #fff);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  
  :deep(.el-card__header) {
    padding: 24px 30px;
    border-bottom: 1px solid var(--theme-border-color, #ebeef5);
  }
  
  :deep(.el-card__body) {
    padding: 0;
  }
}

.profile-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 25px;
  width: 100%;
  max-width: 700px;
}

.avatar-container {
  display: flex;
  justify-content: center;
  align-items: center;
  
  :deep(.el-avatar) {
    border: 2px solid #22a699;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      transform: scale(1.05);
    }
  }
}

.user-details {
  display: flex;
  flex-direction: column;
  
  h2 {
    margin: 0 0 10px 0;
    font-size: 22px;
    color: var(--theme-text-color, #303133);
  }
  
  p {
    margin: 0;
    color: var(--theme-text-color-secondary, #909399);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
  }
}

.user-roles {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.role-tag {
  margin-left: 5px;
  font-weight: normal;
  font-size: 12px;
  border-radius: 12px;
  padding: 0 10px;
  height: 24px;
  line-height: 22px;
  
  &:hover {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
  }
  
  :deep(.el-tag__content) {
    display: inline-block;
  }
}

.profile-tabs {
  :deep(.el-tabs__content) {
    padding: 25px 30px;
  }
  
  :deep(.el-tabs__nav) {
    background-color: #f8f9fb;
  }
  
  :deep(.el-tabs__item) {
    height: 50px;
    line-height: 50px;
    transition: all 0.3s;
    
    &.is-active {
      color: #22a699;
      font-weight: 500;
    }
    
    &:hover {
      color: #22a699;
    }
  }
}

// 响应式样式
@media screen and (max-width: 768px) {
  .profile-container {
    padding: 15px;
  }
  
  .profile-card {
    border-radius: 8px;
    
    :deep(.el-card__header) {
      padding: 15px;
    }
  }
  
  .profile-tabs {
    :deep(.el-tabs__content) {
      padding: 20px 15px;
    }
  }
  
  .user-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 12px;
  }
  
  .user-details {
    align-items: center;
  }
  
  .user-roles {
    justify-content: center;
    margin-top: 5px;
  }
}
</style> 