# 生产环境配置

# 服务器端口配置
server:
  port: 8080  # 修改为普通HTTP端口
  # 增加服务器连接配置
  tomcat:
    threads:
      max: 200            # 最大工作线程数
      min-spare: 10       # 最小工作线程数
    max-connections: 8192 # 最大连接数
    accept-count: 100     # 等待队列长度
    connection-timeout: 120000 # 连接超时，毫秒
    keep-alive-timeout: 60000 # keep-alive超时时间
    max-keep-alive-requests: 10000 # 每个连接最大请求数
    max-http-form-post-size: 20MB  # 增加表单提交大小限制
    max-swallow-size: 20MB  # 增加请求体大小限制

spring:
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************
    username: root
    password: Lh2663305206.
    # 配置连接池
    hikari:
      pool-name: HikariCP
      minimum-idle: 5
      maximum-pool-size: 30
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 60000
      connection-test-query: SELECT 1
  # Redis配置
  data:
    redis:
      host: *************
      port: 6379
      password:
      database: 0
      timeout: 5000
      lettuce:
        pool:
          max-active: 8
          max-wait: -1
          max-idle: 8
          min-idle: 0
  # Quartz定时任务配置
  quartz:
    job-store-type: memory
    scheduler-name: TemuApiScheduler
    startup-delay: 1s
    auto-startup: true
    wait-for-jobs-to-complete-on-shutdown: true
    overwrite-existing-jobs: true
  session:
    timeout: 24h  # 会话超时时间延长为24小时
    cookie:
      http-only: true
      secure: false  # 不再需要HTTPS
      same-site: lax  # 使用lax模式，允许从外部链接导航到本站时发送cookie
      max-age: 86400  # 1天的秒数

# 日志配置
logging:
  level:
    root: info
    com.xiao.temu: info  # 生产环境日志级别调整为info
    # 减少销售数据同步相关的SQL日志输出
    com.xiao.temu.mapper.com.xiao.temu.modules.sales.mapper.SalesSubOrderMapper: warn
    com.xiao.temu.mapper.com.xiao.temu.modules.sales.mapper.SalesSkuQuantityMapper: warn
    com.xiao.temu.mapper.com.xiao.temu.modules.sales.mapper.SalesWarehouseInfoMapper: warn
    com.xiao.temu.mapper.com.xiao.temu.modules.sales.mapper.SalesInventoryInfoMapper: warn
    com.xiao.temu.mapper.com.xiao.temu.modules.sales.mapper.SalesCustomInfoMapper: warn
    com.xiao.temu.mapper.com.xiao.temu.modules.sales.mapper.SalesWarehouseInventoryMapper: warn
    com.xiao.temu.mapper.com.xiao.temu.modules.sync.mapper.SalesSyncTaskMapper: warn
  file:
    name: logs/temu-backend.log

# 腾讯云对象存储配置
tencent:
  cos:
    secret-id: AKIDxpY4MRIBCtNyNfnkX7UdZLxQgpnbGk14
    secret-key: tZ6RZTCj3A7tSnwyqU2ThldHk3B5QXR8
    region: ap-guangzhou
    bucket-name: temu-api-1302872665

# JWT配置
jwt:
  # 增加JWT有效期
  expiration: 2880  # 将有效期增加到48小时
  # 增加刷新Token设置
  refresh-token:
    expiration: 10080  # 刷新Token有效期7天(分钟) 