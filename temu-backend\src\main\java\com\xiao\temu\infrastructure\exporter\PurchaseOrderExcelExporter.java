package com.xiao.temu.infrastructure.exporter;

import com.alibaba.fastjson2.JSONObject;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.xiao.temu.modules.purchaseorderv.dto.PurchaseOrderRequestDTO;
import com.xiao.temu.modules.purchaseorderv.service.PurchaseOrderService;
import com.xiao.temu.modules.purchaseorderv.vo.PurchaseOrderVO;
import com.xiao.temu.modules.system.service.SysDataPermissionService;
import com.xiao.temu.modules.system.service.UserService;
import com.xiao.temu.infrastructure.excel.ExcelExportService;
import com.xiao.temu.infrastructure.excel.ExcelExportUtils;
import com.xiao.temu.modules.purchaseorderv.service.ProductionProgressService;
import com.xiao.temu.modules.purchaseorderv.vo.ProductionProgressVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.Collections;
import java.util.stream.Collectors;
import java.util.Random;

/**
 * 采购单Excel导出实现类
 */
@Slf4j
@Service
public class PurchaseOrderExcelExporter implements ExcelExportService.ExcelExporter {

    @Autowired
    private ExcelExportService excelExportService;
    
    @Autowired
    private PurchaseOrderService purchaseOrderService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private SysDataPermissionService dataPermissionService;
    
    @Autowired
    private ProductionProgressService productionProgressService;
    
    @Override
    public void export(Map<String, Object> exportParams, String taskId) {
        // 获取文件名
        String fileName = (String) exportParams.get("fileName");
        if (fileName == null || fileName.trim().isEmpty()) {
            fileName = "采购单数据";
        }
        
        // 创建数据处理器
        PurchaseOrderDataProcessor processor = new PurchaseOrderDataProcessor(exportParams);
        
        // 先分析数据，确定需要显示哪些列
        // 注意: 这里需要使用预处理来提前获取尺码类型信息
        processor.preProcessSizeTypeInfo(exportParams);
        
        // 根据预处理的结果创建表头和列宽
        String[] headersArray = processor.createHeaders();
        int[] columnWidthsArray = processor.createColumnWidths();
        
        // 执行导出
        excelExportService.exportExcel(
            taskId,
            fileName,
            "采购单数据",
            headersArray,
            columnWidthsArray,
            processor
        );
    }
    
    /**
     * 采购单数据处理器
     */
    private class PurchaseOrderDataProcessor implements ExcelExportService.ExcelDataProcessor {
        
        private final Map<String, Object> exportParams;
        private static final int BATCH_SIZE = 10; // 每批处理的订单数量
        
        // 尺码类型标志
        private boolean hasSizeTypeS_XXL = false;
        private boolean hasSizeType4Y_16Y = false;
        
        // 基础列和尾部列的数量，用于计算尺码列的索引
        private static final int BASE_COLUMN_COUNT = 8; // 序号, 店铺备注, 图片, 店铺名, skc货号, 颜色, 首/返单, 码段
        
        // 预定义的尺码信息
        private final List<String> letterSizes = Arrays.asList("S", "M", "L", "XL", "XXL");
        private final List<String> yearSizes = new ArrayList<>();
        
        // 处理复合尺码的正则表达式
        private final Pattern yearPattern = Pattern.compile("(\\d+)(-\\d+)?Y"); // 用于提取年份数字，如 "13Y" 或 "13-14Y"
        private final Pattern sizeRangePattern = Pattern.compile("([A-Za-z]+|\\d+(-\\d+)?Y)$"); // 提取尺码部分
        
        // 用于存储动态生成的尺码列映射
        private Map<String, Integer> sizeColumnIndexMap = new HashMap<>();
        private List<String> actualSizeColumns = new ArrayList<>();
        
        public PurchaseOrderDataProcessor(Map<String, Object> exportParams) {
            this.exportParams = exportParams;
            
            // 初始化年龄尺码列表
            for (int y = 4; y <= 16; y++) {
                yearSizes.add(y + "Y");
            }
        }
        
        /**
         * 预处理数据，确定需要显示的尺码类型
         */
        public void preProcessSizeTypeInfo(Map<String, Object> exportParams) {
            // 这里需要从导出参数中获取用户ID
            Object userIdObj = exportParams.get("userId");
            if (userIdObj == null) {
                throw new RuntimeException("导出参数中缺少用户ID");
            }
            Long userId = Long.valueOf(userIdObj.toString());
            
            // 获取查询参数
            Map<String, Object> queryParams = (Map<String, Object>) exportParams.get("queryParams");
            if (queryParams == null) {
                throw new RuntimeException("导出参数中缺少查询条件");
            }
            
            // 构建简单的查询请求，只获取少量数据用于分析
            PurchaseOrderRequestDTO requestDTO = new PurchaseOrderRequestDTO();
            if (userService.isAdmin(userId) || "2".equals(dataPermissionService.getUserMaxDataPermission(userId))) {
                requestDTO.setIgnorePermissionCheck(true);
            }
            
            // 设置分页参数，获取数据进行分析
            requestDTO.setPageNo(1);
            
            // 如果导出类型为"current"，则获取当前页大小
            String exportType = (String) exportParams.get("exportType");
            // 获取导出配置
            Map<String, Object> exportConfig = (Map<String, Object>) exportParams.get("exportConfig");
            
            // 对于小数据集(如10条)，直接分析所有数据
            if ("current".equals(exportType) && exportConfig != null && exportConfig.get("pageSize") != null) {
                int pageSize = Integer.parseInt(exportConfig.get("pageSize").toString());
                if (pageSize <= 50) {
                    requestDTO.setPageSize(pageSize); // 直接使用实际数据量
                    log.info("导出数据量较小({}条)，直接分析所有数据", pageSize);
                } else {
                    requestDTO.setPageSize(500); // 对于大数据集，限制分析样本数
                }
            } else {
                requestDTO.setPageSize(500); // 默认分析样本数
            }
            
            // 复制其他查询参数，确保查询结果与实际导出数据一致
            if (queryParams.get("shopIds") != null && queryParams.get("shopIds") instanceof List) {
                List<?> rawShopIds = (List<?>) queryParams.get("shopIds");
                List<Long> shopIdList = new ArrayList<>();
                for (Object id : rawShopIds) {
                    shopIdList.add(Long.valueOf(id.toString()));
                }
                requestDTO.setShopIds(shopIdList);
            }
            
            // 复制其他可能需要的查询参数
            if (queryParams.get("urgencyType") != null) {
                requestDTO.setUrgencyType(Integer.valueOf(queryParams.get("urgencyType").toString()));
            }
            
            // 如果有子订单号列表，也复制过来
            if (queryParams.get("subPurchaseOrderSnList") != null && queryParams.get("subPurchaseOrderSnList") instanceof List) {
                List<?> snList = (List<?>) queryParams.get("subPurchaseOrderSnList");
                List<String> subPurchaseOrderSnList = new ArrayList<>();
                for (Object sn : snList) {
                    subPurchaseOrderSnList.add(sn.toString());
                }
                requestDTO.setSubPurchaseOrderSnList(subPurchaseOrderSnList);
            }
            
            // 复制页码信息，确保分析与导出页码一致
            if (queryParams.get("pageNo") != null) {
                requestDTO.setPageNo(Integer.valueOf(queryParams.get("pageNo").toString()));
            }
            
            // 为了更准确，复制更多可能的查询条件
            copyQueryParams(queryParams, requestDTO);
            
            // 重置尺码类型标志，默认为false
            hasSizeTypeS_XXL = false;
            hasSizeType4Y_16Y = false;
            
            try {
                // 查询采购单数据
                PurchaseOrderVO result = purchaseOrderService.getPurchaseOrderList(requestDTO, userId);
                
                if (result != null && result.getSuccess() && result.getResult() != null && 
                    result.getResult().containsKey("subOrderForSupplierList")) {
                    List<Object> orderList = result.getResult().getJSONArray("subOrderForSupplierList")
                                                .stream().collect(java.util.stream.Collectors.toList());
                    
                    log.info("预处理尺码分析: 获取到 {} 条记录用于分析", orderList.size());
                    
                    // 分析每个订单的尺码类型
                    analyzeSizeTypeFromOrders(orderList);
                    
                    // 如果没有发现任何尺码类型，默认使用全部列（只有在真的一种尺码都没找到时才使用全部）
                    if (!hasSizeTypeS_XXL && !hasSizeType4Y_16Y) {
                        log.warn("未检测到任何尺码类型，默认使用全部尺码列");
                        hasSizeTypeS_XXL = true;
                        hasSizeType4Y_16Y = true;
                    }
                    
                } else {
                    // 请求失败或无数据时的处理
                    log.warn("预处理尺码分析请求失败或无数据返回");
                    
                    // 检查是否有备货单号列表，如果有可以通过其他方式获取尺码信息
                    List<String> subPurchaseOrderSnList = requestDTO.getSubPurchaseOrderSnList();
                    if (subPurchaseOrderSnList != null && !subPurchaseOrderSnList.isEmpty()) {
                        log.info("尝试通过备货单号进行尺码分析");
                        analyzeSizeTypeByOrderSn(subPurchaseOrderSnList, userId);
                    } else {
                        // 无法获取尺码信息时，默认使用全部列
                        log.warn("无法获取尺码信息，默认使用全部尺码列");
                        hasSizeTypeS_XXL = true;
                        hasSizeType4Y_16Y = true;
                    }
                }
            } catch (Exception e) {
                log.error("预处理尺码类型分析失败: {}", e.getMessage(), e);
                
                // 出错时，通过备货单号尝试重新分析
                List<String> subPurchaseOrderSnList = requestDTO.getSubPurchaseOrderSnList();
                if (subPurchaseOrderSnList != null && !subPurchaseOrderSnList.isEmpty()) {
                    log.info("尝试通过备货单号进行尺码分析");
                    analyzeSizeTypeByOrderSn(subPurchaseOrderSnList, userId);
                } else {
                    // 无法获取尺码信息时，默认使用全部列
                    log.warn("无法获取尺码信息，默认使用全部尺码列");
                    hasSizeTypeS_XXL = true;
                    hasSizeType4Y_16Y = true;
                }
            }
            
            log.info("最终尺码类型分析结果 - S-XXL类型: {}, 4Y-16Y类型: {}", hasSizeTypeS_XXL, hasSizeType4Y_16Y);
            
            // 根据分析结果构建实际的尺码列
            this.actualSizeColumns = new ArrayList<>();
            if (hasSizeTypeS_XXL) {
                this.actualSizeColumns.addAll(letterSizes);
            }
            if (hasSizeType4Y_16Y) {
                this.actualSizeColumns.addAll(yearSizes);
            }
            
            // 构建尺码列索引映射
            sizeColumnIndexMap.clear(); // 清除可能的旧映射
            for (int i = 0; i < actualSizeColumns.size(); i++) {
                sizeColumnIndexMap.put(actualSizeColumns.get(i), BASE_COLUMN_COUNT + i);
            }
            
            // 记录尺码列数量
            log.info("实际显示的尺码列数量: {}", actualSizeColumns.size());
        }
        
        /**
         * 从订单列表中分析尺码类型
         */
        private void analyzeSizeTypeFromOrders(List<Object> orderList) {
            // 记录所有找到的className，用于日志记录和调试
            Set<String> foundClassNames = new HashSet<>();
            
            // 分析每个订单的尺码类型
            for (Object orderObj : orderList) {
                Map<String, Object> order = (Map<String, Object>) orderObj;
                List<Map<String, Object>> skuList = (List<Map<String, Object>>) order.get("skuQuantityDetailList");
                
                if (skuList != null && !skuList.isEmpty()) {
                    for (Map<String, Object> sku : skuList) {
                        String className = getStringValue(sku, "className", "");
                        Integer quantity = getIntegerValue(sku, "purchaseQuantity", 0);
                        
                        // 只有当数量大于0时才考虑这个尺码
                        if (quantity <= 0) {
                            continue;
                        }
                        
                        // 记录找到的className
                        foundClassNames.add(className);
                        
                        // 精确匹配字母尺码
                        boolean isSizeTypeS_XXL = false;
                        for (String size : letterSizes) {
                            if (className.endsWith("-" + size)) {
                                hasSizeTypeS_XXL = true;
                                isSizeTypeS_XXL = true;
                                break;
                            }
                        }
                        
                        // 检查是否包含年龄尺码
                        boolean isSizeType4Y_16Y = false;
                        if (className.matches(".*-\\d+Y$") || 
                            className.matches(".*-\\d+-\\d+Y$")) {
                            hasSizeType4Y_16Y = true;
                            isSizeType4Y_16Y = true;
                        }
                        
                        if (!isSizeTypeS_XXL && !isSizeType4Y_16Y) {
                            // 使用正则表达式进一步分析
                            Matcher matcher = sizeRangePattern.matcher(className);
                            if (matcher.find()) {
                                String sizePart = matcher.group(1); // 如 "M", "13-14Y", "16Y"
                                
                                if (letterSizes.contains(sizePart)) {
                                    hasSizeTypeS_XXL = true;
                                    isSizeTypeS_XXL = true;
                                } else if (sizePart.endsWith("Y")) {
                                    hasSizeType4Y_16Y = true;
                                    isSizeType4Y_16Y = true;
                                }
                            }
                        }
                        
                        // 记录分析结果
                        if (isSizeTypeS_XXL || isSizeType4Y_16Y) {
                            log.debug("尺码类型分析 - className: {}, S-XXL: {}, 4Y-16Y: {}", 
                                     className, isSizeTypeS_XXL, isSizeType4Y_16Y);
                        }
                        
                        // 如果两种类型都找到了，可以提前退出内层循环
                        if (hasSizeTypeS_XXL && hasSizeType4Y_16Y) {
                            break;
                        }
                    }
                }
                
                // 如果两种类型都找到了，可以提前退出外层循环
                if (hasSizeTypeS_XXL && hasSizeType4Y_16Y) {
                    break;
                }
            }
            
            // 输出所有找到的className，帮助定位问题
            if (!foundClassNames.isEmpty()) {
                log.info("找到的所有className: {}", String.join(", ", foundClassNames));
            }
            
            log.info("订单数据尺码类型分析结果 - S-XXL类型: {}, 4Y-16Y类型: {}", hasSizeTypeS_XXL, hasSizeType4Y_16Y);
        }
        
        /**
         * 通过备货单号分析尺码类型
         */
        private void analyzeSizeTypeByOrderSn(List<String> orderSnList, Long userId) {
            // 重置尺码类型标志
            hasSizeTypeS_XXL = false;
            hasSizeType4Y_16Y = false;
            
            // 记录所有找到的className，用于日志记录和调试
            Set<String> foundClassNames = new HashSet<>();
            
            // 如果订单号太多，只取部分进行分析
            int maxOrdersToAnalyze = Math.min(orderSnList.size(), 50);
            List<String> sampleOrderList = orderSnList.subList(0, maxOrdersToAnalyze);
            
            for (String orderSn : sampleOrderList) {
                try {
                    // 构建单个订单查询请求
                    PurchaseOrderRequestDTO singleOrderRequest = new PurchaseOrderRequestDTO();
                    singleOrderRequest.setPageNo(1);
                    singleOrderRequest.setPageSize(50);
                    singleOrderRequest.setSubPurchaseOrderSnList(Collections.singletonList(orderSn));
                    if (userService.isAdmin(userId) || "2".equals(dataPermissionService.getUserMaxDataPermission(userId))) {
                        singleOrderRequest.setIgnorePermissionCheck(true);
                    }
                    
                    // 查询单个订单
                    PurchaseOrderVO result = purchaseOrderService.getPurchaseOrderList(singleOrderRequest, userId);
                    
                    if (result != null && result.getSuccess() && result.getResult() != null && 
                        result.getResult().containsKey("subOrderForSupplierList")) {
                        List<Object> orderList = result.getResult().getJSONArray("subOrderForSupplierList")
                                                .stream().collect(java.util.stream.Collectors.toList());
                        
                        // 分析订单的尺码类型
                        for (Object orderObj : orderList) {
                            Map<String, Object> order = (Map<String, Object>) orderObj;
                            List<Map<String, Object>> skuList = (List<Map<String, Object>>) order.get("skuQuantityDetailList");
                            
                            if (skuList != null && !skuList.isEmpty()) {
                                for (Map<String, Object> sku : skuList) {
                                    String className = getStringValue(sku, "className", "");
                                    Integer quantity = getIntegerValue(sku, "purchaseQuantity", 0);
                                    
                                    // 只有当数量大于0时才考虑这个尺码
                                    if (quantity <= 0) {
                                        continue;
                                    }
                                    
                                    // 记录找到的className
                                    foundClassNames.add(className);
                                    
                                    // 精确匹配字母尺码
                                    boolean isSizeTypeS_XXL = false;
                                    for (String size : letterSizes) {
                                        if (className.endsWith("-" + size)) {
                                            hasSizeTypeS_XXL = true;
                                            isSizeTypeS_XXL = true;
                                            break;
                                        }
                                    }
                                    
                                    // 检查是否包含年龄尺码
                                    boolean isSizeType4Y_16Y = false;
                                    if (className.matches(".*-\\d+Y$") || 
                                        className.matches(".*-\\d+-\\d+Y$")) {
                                        hasSizeType4Y_16Y = true;
                                        isSizeType4Y_16Y = true;
                                    }
                                    
                                    if (!isSizeTypeS_XXL && !isSizeType4Y_16Y) {
                                        // 使用正则表达式进一步分析
                                        Matcher matcher = sizeRangePattern.matcher(className);
                                        if (matcher.find()) {
                                            String sizePart = matcher.group(1); // 如 "M", "13-14Y", "16Y"
                                            
                                            if (letterSizes.contains(sizePart)) {
                                                hasSizeTypeS_XXL = true;
                                                isSizeTypeS_XXL = true;
                                            } else if (sizePart.endsWith("Y")) {
                                                hasSizeType4Y_16Y = true;
                                                isSizeType4Y_16Y = true;
                                            }
                                        }
                                    }
                                    
                                    // 记录分析结果
                                    if (isSizeTypeS_XXL || isSizeType4Y_16Y) {
                                        log.debug("尺码类型分析 - className: {}, S-XXL: {}, 4Y-16Y: {}", 
                                                 className, isSizeTypeS_XXL, isSizeType4Y_16Y);
                                    }
                                    
                                    // 如果两种类型都找到了，可以提前退出内层循环
                                    if (hasSizeTypeS_XXL && hasSizeType4Y_16Y) {
                                        break;
                                    }
                                }
                            }
                            
                            // 如果两种类型都找到了，可以提前退出外层循环
                            if (hasSizeTypeS_XXL && hasSizeType4Y_16Y) {
                                break;
                            }
                        }
                    }
                    
                    // 如果两种类型都找到了，可以提前退出整个循环
                    if (hasSizeTypeS_XXL && hasSizeType4Y_16Y) {
                        break;
                    }
                } catch (Exception e) {
                    log.warn("分析订单 {} 尺码类型失败: {}", orderSn, e.getMessage());
                    // 单个订单分析失败继续处理下一个
                    continue;
                }
            }
            
            // 输出所有找到的className，帮助定位问题
            if (!foundClassNames.isEmpty()) {
                log.info("通过备货单号找到的所有className: {}", String.join(", ", foundClassNames));
            }
            
            log.info("通过备货单号分析尺码类型结果 - S-XXL类型: {}, 4Y-16Y类型: {}", hasSizeTypeS_XXL, hasSizeType4Y_16Y);
        }
        
        /**
         * 复制查询参数
         */
        private void copyQueryParams(Map<String, Object> queryParams, PurchaseOrderRequestDTO requestDTO) {
            // 记录输入的查询参数
            log.info("开始复制查询参数, 原始参数: inFulfilmentPunish={}, deliverOrArrivalDelayStatusList={}, isTodayPlatformPurchase={}", 
                     queryParams.get("inFulfilmentPunish"),
                     queryParams.get("deliverOrArrivalDelayStatusList"),
                     queryParams.get("isTodayPlatformPurchase"));
                     
            // 处理时间范围参数
            if (queryParams.get("purchaseTimeFrom") != null) {
                requestDTO.setPurchaseTimeFrom(Long.valueOf(queryParams.get("purchaseTimeFrom").toString()));
            }
            
            if (queryParams.get("purchaseTimeTo") != null) {
                requestDTO.setPurchaseTimeTo(Long.valueOf(queryParams.get("purchaseTimeTo").toString()));
            }
            
            // 设置备货类型
            if (queryParams.get("purchaseStockType") != null) {
                requestDTO.setPurchaseStockType(Integer.valueOf(queryParams.get("purchaseStockType").toString()));
            }
            
            // 设置备货区域
            if (queryParams.get("inventoryRegionList") != null) {
                Object regionListObj = queryParams.get("inventoryRegionList");
                if (regionListObj instanceof List) {
                    List<Integer> regionList = new ArrayList<>();
                    for (Object region : (List<?>) regionListObj) {
                        regionList.add(Integer.valueOf(region.toString()));
                    }
                    requestDTO.setInventoryRegionList(regionList);
                }
            }
            
            // 设置结算类型
            if (queryParams.get("settlementType") != null) {
                requestDTO.setSettlementType(Integer.valueOf(queryParams.get("settlementType").toString()));
            }
            
            // 设置下单来源
            if (queryParams.get("sourceList") != null) {
                Object sourceListObj = queryParams.get("sourceList");
                if (sourceListObj instanceof List) {
                    List<Integer> sourceList = new ArrayList<>();
                    for (Object source : (List<?>) sourceListObj) {
                        sourceList.add(Integer.valueOf(source.toString()));
                    }
                    requestDTO.setSourceList(sourceList);
                }
            }
            
            // 设置是否系统下单
            if (queryParams.get("isSystemAutoPurchaseSource") != null) {
                requestDTO.setIsSystemAutoPurchaseSource(Boolean.valueOf(queryParams.get("isSystemAutoPurchaseSource").toString()));
            }
            
            // 设置状态列表
            if (queryParams.get("statusList") != null) {
                Object statusListObj = queryParams.get("statusList");
                if (statusListObj instanceof List) {
                    List<Integer> statusList = new ArrayList<>();
                    for (Object status : (List<?>) statusListObj) {
                        statusList.add(Integer.valueOf(status.toString()));
                    }
                    requestDTO.setStatusList(statusList);
                }
            }
            
            // 添加快速筛选条件处理
            // 添加履约考核筛选条件
            if (queryParams.get("inFulfilmentPunish") != null) {
                boolean inFulfilmentPunish = Boolean.valueOf(queryParams.get("inFulfilmentPunish").toString());
                requestDTO.setInFulfilmentPunish(inFulfilmentPunish);
                log.info("设置履约考核筛选条件: {}", inFulfilmentPunish);
            }
            
            // 添加发货/到货逾期状态列表
            if (queryParams.get("deliverOrArrivalDelayStatusList") != null) {
                Object delayStatusListObj = queryParams.get("deliverOrArrivalDelayStatusList");
                if (delayStatusListObj instanceof List) {
                    List<Integer> delayStatusList = new ArrayList<>();
                    for (Object status : (List<?>) delayStatusListObj) {
                        delayStatusList.add(Integer.valueOf(status.toString()));
                    }
                    requestDTO.setDeliverOrArrivalDelayStatusList(delayStatusList);
                    log.info("设置发货/到货逾期状态列表: {}", delayStatusList);
                }
            }
            
            // 添加今日系统创建筛选条件
            if (queryParams.get("isTodayPlatformPurchase") != null) {
                boolean isTodayPlatformPurchase = Boolean.valueOf(queryParams.get("isTodayPlatformPurchase").toString());
                requestDTO.setIsTodayPlatformPurchase(isTodayPlatformPurchase);
                log.info("设置今日系统创建筛选条件: {}", isTodayPlatformPurchase);
            }
            
            // 复制原订单号列表
            if (queryParams.get("originalPurchaseOrderSnList") != null) {
                Object snListObj = queryParams.get("originalPurchaseOrderSnList");
                if (snListObj instanceof List) {
                    List<String> originalPurchaseOrderSnList = new ArrayList<>();
                    for (Object sn : (List<?>) snListObj) {
                        originalPurchaseOrderSnList.add(sn.toString());
                    }
                    requestDTO.setOriginalPurchaseOrderSnList(originalPurchaseOrderSnList);
                }
            }
            
            // 复制子单号列表，备货单号
            if (queryParams.get("subPurchaseOrderSnList") != null) {
                Object snListObj = queryParams.get("subPurchaseOrderSnList");
                if (snListObj instanceof List) {
                    List<String> subPurchaseOrderSnList = new ArrayList<>();
                    for (Object sn : (List<?>) snListObj) {
                        subPurchaseOrderSnList.add(sn.toString());
                    }
                    requestDTO.setSubPurchaseOrderSnList(subPurchaseOrderSnList);
                }
            }
            
            // 复制商品货号列表 productSnList
            if (queryParams.get("productSnList") != null) {
                Object snListObj = queryParams.get("productSnList");
                if (snListObj instanceof List) {
                    List<String> productSnList = new ArrayList<>();
                    for (Object sn : (List<?>) snListObj) {
                        productSnList.add(sn.toString());
                    }
                    requestDTO.setProductSnList(productSnList);
                }
            }
            
            // 复制SKC列表 productSkcIdList
            if (queryParams.get("productSkcIdList") != null) {
                Object skcListObj = queryParams.get("productSkcIdList");
                if (skcListObj instanceof List) {
                    List<String> productSkcIdList = new ArrayList<>();
                    for (Object skc : (List<?>) skcListObj) {
                        productSkcIdList.add(skc.toString());
                    }
                    requestDTO.setProductSkcIdList(productSkcIdList);
                }
            }
            
            // 复制发货单号列表 deliverOrderSnList
            if (queryParams.get("deliverOrderSnList") != null) {
                Object snListObj = queryParams.get("deliverOrderSnList");
                if (snListObj instanceof List) {
                    List<String> deliverOrderSnList = new ArrayList<>();
                    for (Object sn : (List<?>) snListObj) {
                        deliverOrderSnList.add(sn.toString());
                    }
                    requestDTO.setDeliverOrderSnList(deliverOrderSnList);
                }
            }
            
            // 设置是否发货逾期 isDelayDeliver
            if (queryParams.get("isDelayDeliver") != null) {
                requestDTO.setIsDelayDeliver(Boolean.valueOf(queryParams.get("isDelayDeliver").toString()));
            }
            
            // 设置是否到货逾期 isDelayArrival
            if (queryParams.get("isDelayArrival") != null) {
                requestDTO.setIsDelayArrival(Boolean.valueOf(queryParams.get("isDelayArrival").toString()));
            }
            
            // 设置最晚发货时间范围
            if (queryParams.get("expectLatestDeliverTimeFrom") != null) {
                requestDTO.setExpectLatestDeliverTimeFrom(Long.valueOf(queryParams.get("expectLatestDeliverTimeFrom").toString()));
            }
            
            if (queryParams.get("expectLatestDeliverTimeTo") != null) {
                requestDTO.setExpectLatestDeliverTimeTo(Long.valueOf(queryParams.get("expectLatestDeliverTimeTo").toString()));
            }
            
            // 设置最晚到货时间范围
            if (queryParams.get("expectLatestArrivalTimeFrom") != null) {
                requestDTO.setExpectLatestArrivalTimeFrom(Long.valueOf(queryParams.get("expectLatestArrivalTimeFrom").toString()));
            }
            
            if (queryParams.get("expectLatestArrivalTimeTo") != null) {
                requestDTO.setExpectLatestArrivalTimeTo(Long.valueOf(queryParams.get("expectLatestArrivalTimeTo").toString()));
            }
            
            // 设置是否首单 isFirst
            if (queryParams.get("isFirst") != null) {
                requestDTO.setIsFirst(Boolean.valueOf(queryParams.get("isFirst").toString()));
            }
            
            // 设置是否缺货 skuLackSnapshot
            if (queryParams.get("skuLackSnapshot") != null) {
                requestDTO.setSkuLackSnapshot(Integer.valueOf(queryParams.get("skuLackSnapshot").toString()));
            }
            
            // 设置质量隐患 qcReject
            if (queryParams.get("qcReject") != null) {
                requestDTO.setQcReject(Integer.valueOf(queryParams.get("qcReject").toString()));
            }
            
            // 设置抽检不合格 qcOption
            if (queryParams.get("qcOption") != null) {
                requestDTO.setQcOption(Integer.valueOf(queryParams.get("qcOption").toString()));
            }
            
            // 设置因抽检不合格创建 qcNotPassCreate
            if (queryParams.get("qcNotPassCreate") != null) {
                requestDTO.setQcNotPassCreate(Boolean.valueOf(queryParams.get("qcNotPassCreate").toString()));
            }
            
            // 设置是否含缺货/售罄SKU lackOrSoldOutTagList
            if (queryParams.get("lackOrSoldOutTagList") != null) {
                Object tagListObj = queryParams.get("lackOrSoldOutTagList");
                if (tagListObj instanceof List) {
                    List<Integer> tagList = new ArrayList<>();
                    for (Object tag : (List<?>) tagListObj) {
                        tagList.add(Integer.valueOf(tag.toString()));
                    }
                    requestDTO.setLackOrSoldOutTagList(tagList);
                }
            }
            
            // 设置是否热销款 hotTag
            if (queryParams.get("hotTag") != null) {
                requestDTO.setHotTag(Boolean.valueOf(queryParams.get("hotTag").toString()));
            }
            
            // 设置可发货时间范围
            if (queryParams.get("canDeliverStartTime") != null) {
                requestDTO.setCanDeliverStartTime(Long.valueOf(queryParams.get("canDeliverStartTime").toString()));
            }
            
            if (queryParams.get("canDeliverEndTime") != null) {
                requestDTO.setCanDeliverEndTime(Long.valueOf(queryParams.get("canDeliverEndTime").toString()));
            }
            
            // 设置商品条码尺寸 productLabelCodeStyle
            if (queryParams.get("productLabelCodeStyle") != null) {
                requestDTO.setProductLabelCodeStyle(Integer.valueOf(queryParams.get("productLabelCodeStyle").toString()));
            }
            
            // 设置是否入库退供 inboundReturn
            if (queryParams.get("inboundReturn") != null) {
                requestDTO.setInboundReturn(Boolean.valueOf(queryParams.get("inboundReturn").toString()));
            }
            
            // 设置是否因入库退供创建 inboundReturnCreate
            if (queryParams.get("inboundReturnCreate") != null) {
                requestDTO.setInboundReturnCreate(Boolean.valueOf(queryParams.get("inboundReturnCreate").toString()));
            }
            
            // 新增：处理排序参数 oneDimensionSort
            if (queryParams.get("oneDimensionSort") != null) {
                Object sortObj = queryParams.get("oneDimensionSort");
                if (sortObj instanceof Map) {
                    Map<String, Object> sortMap = (Map<String, Object>) sortObj;
                    // 创建正确类型的OneDimensionSort对象
                    PurchaseOrderRequestDTO.OneDimensionSort sortParam = new PurchaseOrderRequestDTO.OneDimensionSort();
                    if (sortMap.get("firstOrderByParam") != null) {
                        sortParam.setFirstOrderByParam(sortMap.get("firstOrderByParam").toString());
                    }
                    if (sortMap.get("firstOrderByDesc") != null) {
                        sortParam.setFirstOrderByDesc(Integer.valueOf(sortMap.get("firstOrderByDesc").toString()));
                    }
                    requestDTO.setOneDimensionSort(sortParam);
                    log.info("设置排序参数: field={}, desc={}", sortParam.getFirstOrderByParam(), sortParam.getFirstOrderByDesc());
                }
            }
            
            // 记录最终设置的快速筛选条件
            log.info("查询参数复制完成，最终快速筛选条件: inFulfilmentPunish={}, deliverOrArrivalDelayStatusList={}, isTodayPlatformPurchase={}", 
                     requestDTO.getInFulfilmentPunish(),
                     requestDTO.getDeliverOrArrivalDelayStatusList(),
                     requestDTO.getIsTodayPlatformPurchase());
        }
        
        /**
         * 填充尺码相关数据
         */
        private void processSizeData(String className, Integer quantity, Map<String, Integer> sizeQuantityMap, 
                                    int[] minMaxSizeOrder, String[] minMaxSizeStr) {
            Matcher matcher = sizeRangePattern.matcher(className);
            if (matcher.find()) {
                String sizePart = matcher.group(1); // 如 "M", "13-14Y", "16Y"
                
                if (letterSizes.contains(sizePart)) { // S-XXL 类型
                    processSizeS_XXL(sizePart, quantity, sizeQuantityMap, minMaxSizeOrder, minMaxSizeStr);
                } else if (sizePart.endsWith("Y")) { // 年份类型
                    processSize4Y_16Y(sizePart, quantity, sizeQuantityMap, minMaxSizeOrder, minMaxSizeStr);
                }
            }
        }
        
        /**
         * 处理S-XXL类型的尺码数据
         */
        private void processSizeS_XXL(String sizePart, Integer quantity, Map<String, Integer> sizeQuantityMap,
                                     int[] minMaxSizeOrder, String[] minMaxSizeStr) {
            Map<String, Integer> letterSizeOrder = Map.of("S", 1, "M", 2, "L", 3, "XL", 4, "XXL", 5);
            
            sizeQuantityMap.put(sizePart, sizeQuantityMap.getOrDefault(sizePart, 0) + quantity);
            if (letterSizeOrder.containsKey(sizePart)) {
                int currentOrder = letterSizeOrder.get(sizePart);
                if (currentOrder < minMaxSizeOrder[0]) {
                    minMaxSizeOrder[0] = currentOrder;
                    minMaxSizeStr[0] = sizePart;
                }
                if (currentOrder > minMaxSizeOrder[1]) {
                    minMaxSizeOrder[1] = currentOrder;
                    minMaxSizeStr[1] = sizePart;
                }
            }
        }
        
        /**
         * 处理4Y-16Y类型的尺码数据
         */
        private void processSize4Y_16Y(String sizePart, Integer quantity, Map<String, Integer> sizeQuantityMap,
                                      int[] minMaxSizeOrder, String[] minMaxSizeStr) {
            Matcher yearMatcher = yearPattern.matcher(sizePart);
            if (yearMatcher.find()) {
                // 提取数字部分，如"13Y"或"13-14Y"中的13
                String yearStr = yearMatcher.group(1);
                int year = Integer.parseInt(yearStr);
                String sizeKey = year + "Y"; // "13Y"
                
                sizeQuantityMap.put(sizeKey, sizeQuantityMap.getOrDefault(sizeKey, 0) + quantity);
                
                // 更新最小年龄
                if (year < minMaxSizeOrder[2]) {
                    minMaxSizeOrder[2] = year;
                    minMaxSizeStr[2] = sizeKey;
                }
                
                // 更新最大年龄
                if (year > minMaxSizeOrder[3]) {
                    minMaxSizeOrder[3] = year;
                    minMaxSizeStr[3] = sizeKey;
                }
            }
        }
        
        /**
         * 创建表头
         */
        public String[] createHeaders() {
            // 定义基础表头
            List<String> baseHeaders = new ArrayList<>(Arrays.asList(
                    "序号", "店铺备注", "图片", "店铺名", "skc货号", "颜色", "首/返单", "码段"
            ));
            
            // 定义结尾表头
            List<String> tailHeaders = new ArrayList<>(Arrays.asList(
                    "数量合计", "送货", "入库", "下单日期", "发货日期", "SKC", "订单号", "二维码",
                    // 修改生产进度状态表头，每个状态两列
                    "烧花/剪图-操作人", "烧花/剪图-时间", 
                    "车间/拣货-操作人", "车间/拣货-时间", 
                    "剪线/压图-操作人", "剪线/压图-时间", 
                    "查货-操作人", "查货-时间", 
                    "包装-操作人", "包装-时间", 
                    "发货-操作人", "发货-时间"
            ));
            
            // 合并所有表头
            List<String> finalHeaders = new ArrayList<>();
            finalHeaders.addAll(baseHeaders);
            finalHeaders.addAll(actualSizeColumns);
            finalHeaders.addAll(tailHeaders);
            
            return finalHeaders.toArray(new String[0]);
        }
        
        /**
         * 创建列宽
         */
        public int[] createColumnWidths() {
            // 定义基础列宽
            List<Integer> columnWidthsList = new ArrayList<>(Arrays.asList(
                    5, 20, 10, 15, 15, 12, 8, 10 // 基础列宽
            ));
            
            // 尺码列宽
            for (int i = 0; i < actualSizeColumns.size(); i++) {
                columnWidthsList.add(5);
            }
            
            // 结尾列宽
            columnWidthsList.addAll(Arrays.asList(
                    8, 8, 8, 20, 20, 15, 20, 15, // 数量合计, 送货, 入库, 下单日期, 发货日期, SKC, 订单号, 二维码
                    // 修改生产进度列宽，每个状态两列
                    12, 16, // 烧花/剪图-操作人, 烧花/剪图-时间
                    12, 16, // 车间/拣货-操作人, 车间/拣货-时间
                    12, 16, // 剪线/压图-操作人, 剪线/压图-时间
                    12, 16, // 查货-操作人, 查货-时间
                    12, 16, // 包装-操作人, 包装-时间
                    12, 16  // 发货-操作人, 发货-时间
            ));
            
            return columnWidthsList.stream().mapToInt(i -> i).toArray();
        }
        
        @Override
        public void prepareData(ExcelExportService.ExcelExportContext context) {
            try {
                // 从导出参数中获取用户ID
                Object userIdObj = exportParams.get("userId");
                if (userIdObj == null) {
                    throw new RuntimeException("导出参数中缺少用户ID");
                }
                Long userId = Long.valueOf(userIdObj.toString());
                
                // 判断用户是否为管理员或拥有全部数据权限
                boolean isAdmin = userService.isAdmin(userId);
                
                // 获取用户的最高数据权限
                String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
                boolean hasFullDataPermission = "2".equals(permissionType);
                
                // 解析导出参数
                Map<String, Object> queryParams = (Map<String, Object>) exportParams.get("queryParams");
                
                if (queryParams == null) {
                    throw new RuntimeException("导出参数中缺少查询条件");
                }
                
                // 构建查询参数
                PurchaseOrderRequestDTO requestDTO = new PurchaseOrderRequestDTO();
                
                // 设置查询参数
                // 设置忽略权限检查标志
                if (isAdmin || hasFullDataPermission) {
                    requestDTO.setIgnorePermissionCheck(true);
                }
                
                // 处理shopIds
                if (queryParams.get("shopIds") != null) {
                    Object shopIdsObj = queryParams.get("shopIds");
                    if (shopIdsObj instanceof List) {
                        List<?> rawShopIds = (List<?>) shopIdsObj;
                        List<Long> shopIdList = new ArrayList<>();
                        for (Object id : rawShopIds) {
                            shopIdList.add(Long.valueOf(id.toString()));
                        }
                        requestDTO.setShopIds(shopIdList);
                    }
                }
                
                // 处理urgencyType
                if (queryParams.get("urgencyType") != null) {
                    requestDTO.setUrgencyType(Integer.valueOf(queryParams.get("urgencyType").toString()));
                }

                // 获取所有备货单号列表
                List<String> subPurchaseOrderSnList = null;
                if (queryParams.get("subPurchaseOrderSnList") != null) {
                    Object snListObj = queryParams.get("subPurchaseOrderSnList");
                    if (snListObj instanceof List) {
                        subPurchaseOrderSnList = new ArrayList<>();
                        for (Object sn : (List<?>) snListObj) {
                            subPurchaseOrderSnList.add(sn.toString());
                        }
                    }
                }
                
                // 如果指定了备货单号，优先使用备货单号获取数据
                if (subPurchaseOrderSnList != null && !subPurchaseOrderSnList.isEmpty()) {
                    log.info("使用指定的备货单号获取采购单数据，共{}个单号", subPurchaseOrderSnList.size());
                    List<Object> allOrderData = new ArrayList<>();
                    
                    // 分批处理备货单号
                    int totalBatches = (int) Math.ceil(subPurchaseOrderSnList.size() / (double) BATCH_SIZE);
                    for (int i = 0; i < totalBatches; i++) {
                        int fromIndex = i * BATCH_SIZE;
                        int toIndex = Math.min(fromIndex + BATCH_SIZE, subPurchaseOrderSnList.size());
                        List<String> batchSnList = subPurchaseOrderSnList.subList(fromIndex, toIndex);
                        
                        // 创建新的查询对象
                        PurchaseOrderRequestDTO batchRequestDTO = new PurchaseOrderRequestDTO();
                        batchRequestDTO.setSubPurchaseOrderSnList(batchSnList);
                        batchRequestDTO.setShopIds(requestDTO.getShopIds());
                        batchRequestDTO.setUrgencyType(requestDTO.getUrgencyType());
                        batchRequestDTO.setIgnorePermissionCheck(requestDTO.getIgnorePermissionCheck());
                        
                        // 新增：复制排序参数到批次请求对象
                        if (requestDTO.getOneDimensionSort() != null) {
                            batchRequestDTO.setOneDimensionSort(requestDTO.getOneDimensionSort());
                        }
                        
                        // 添加动态分页参数
                        batchRequestDTO.setPageNo(1); // 设置为第一页
                        // 分页大小设置为当前批次备货单号数量的倍数，确保能获取所有数据
                        int dynamicPageSize = Math.max(batchSnList.size() * 2, 50); // 至少50，或批次大小的2倍
                        batchRequestDTO.setPageSize(Math.min(dynamicPageSize, 200)); // 上限为200（平台限制）
                        
                        // 查询采购单数据
                        PurchaseOrderVO batchResult = purchaseOrderService.getPurchaseOrderList(batchRequestDTO, userId);
                        
                        // 添加到总结果
                        if (batchResult != null && batchResult.getSuccess() && batchResult.getResult() != null 
                                && batchResult.getResult().containsKey("subOrderForSupplierList")) {
                            allOrderData.addAll(batchResult.getResult().getJSONArray("subOrderForSupplierList").stream().collect(java.util.stream.Collectors.toList()));
                        }
                    }
                    
                    // 新增：如果有排序参数，对合并后的数据进行统一排序
                    if (!allOrderData.isEmpty() && requestDTO.getOneDimensionSort() != null) {
                        sortMergedData(allOrderData, requestDTO.getOneDimensionSort());
                    }
                    
                    // 设置数据
                    context.setData(allOrderData);
                    context.setDataSize(allOrderData.size());
                } else {
                    // 按条件查询，处理导出类型
                    log.info("根据条件获取采购单数据");
                    
                    // 复制查询参数
                    copyQueryParams(queryParams, requestDTO);
                    
                    // 获取分页参数和导出类型
                    Map<String, Object> exportConfig = (Map<String, Object>) exportParams.get("exportConfig");
                    String exportType = (String) exportParams.get("exportType");
                    log.info("导出类型: {}, 配置: {}", exportType, exportConfig);
                    
                    // 处理不同的导出类型
                    List<Object> allOrderData = new ArrayList<>();
                    
                    // 设置基础分页参数
                    int pageSize = exportConfig != null && exportConfig.get("pageSize") != null ? 
                                   Integer.parseInt(exportConfig.get("pageSize").toString()) : 10;
                    // 确保pageSize不超过200
                    pageSize = Math.min(pageSize, 200);
                    
                    // 根据导出类型设置分页
                    if ("current".equals(exportType)) {
                        // 当前页 - 只获取当前页数据
                        int currentPage = queryParams.get("pageNo") != null ? 
                                         Integer.parseInt(queryParams.get("pageNo").toString()) : 1;
                        
                        log.info("导出当前页数据: 第{}页, 每页{}条", currentPage, pageSize);
                        
                        requestDTO.setPageNo(currentPage);
                        requestDTO.setPageSize(pageSize);
                        
                        // 查询当前页数据
                        PurchaseOrderVO result = purchaseOrderService.getPurchaseOrderList(requestDTO, userId);
                        
                        if (result != null && result.getSuccess() && result.getResult() != null 
                                && result.getResult().containsKey("subOrderForSupplierList")) {
                            allOrderData.addAll(result.getResult().getJSONArray("subOrderForSupplierList").stream().collect(java.util.stream.Collectors.toList()));
                        }
                    } else if ("custom".equals(exportType)) {
                        // 自定义页数 - 获取从第1页到指定页数的数据
                        int pageCount = exportConfig != null && exportConfig.get("pageCount") != null ? 
                                       Integer.parseInt(exportConfig.get("pageCount").toString()) : 1;
                        
                        log.info("导出自定义页数据: 从第1页到第{}页, 每页{}条", pageCount, pageSize);
                        
                        // 逐页查询
                        for (int page = 1; page <= pageCount; page++) {
                            requestDTO.setPageNo(page);
                            requestDTO.setPageSize(pageSize);
                            
                            PurchaseOrderVO result = purchaseOrderService.getPurchaseOrderList(requestDTO, userId);
                            
                            if (result != null && result.getSuccess() && result.getResult() != null 
                                    && result.getResult().containsKey("subOrderForSupplierList")) {
                                List<Object> pageData = result.getResult().getJSONArray("subOrderForSupplierList").stream().collect(java.util.stream.Collectors.toList());
                                allOrderData.addAll(pageData);
                                
                                // 如果返回的数据少于请求的数量，说明已经到最后一页，可以提前退出
                                if (pageData.size() < pageSize) {
                                    break;
                                }
                            } else {
                                // 如果获取失败或没有数据，提前退出
                                break;
                            }
                        }
                        
                        // 新增：如果有多页数据且有排序参数，对合并后的数据进行统一排序
                        if (pageCount > 1 && !allOrderData.isEmpty() && requestDTO.getOneDimensionSort() != null) {
                            sortMergedData(allOrderData, requestDTO.getOneDimensionSort());
                        }
                    } else {
                        // 默认导出全部数据，按页获取，注意控制总数不超过10000条
                        
                        // 对于全部导出，确保分页大小至少为100，提高导出效率
                        if (pageSize < 100) {
                            pageSize = 100;
                            log.info("导出全部数据时，自动调整分页大小为100，提高导出效率");
                        }
                        
                        log.info("导出全部数据, 每页{}条", pageSize);
                        
                        int maxRecords = 10000; // Temu平台限制
                        int currentCount = 0;
                        int page = 1;
                        boolean hasMoreData = true; // 标记是否还有更多数据
                        int emptyResultCount = 0; // 连续空结果计数
                        long totalRecords = -1; // 总记录数，初始为-1表示未知
                        
                        while (currentCount < maxRecords && hasMoreData) {
                            requestDTO.setPageNo(page);
                            requestDTO.setPageSize(pageSize);
                            
                            PurchaseOrderVO result = purchaseOrderService.getPurchaseOrderList(requestDTO, userId);
                            
                            if (result != null && result.getSuccess() && result.getResult() != null 
                                    && result.getResult().containsKey("subOrderForSupplierList")) {
                                
                                // 获取API返回的总记录数（如果有）
                                if (result.getResult().containsKey("totalCount")) {
                                    Object totalObj = result.getResult().get("totalCount");
                                    if (totalObj != null) {
                                        try {
                                            if (totalObj instanceof Number) {
                                                totalRecords = ((Number) totalObj).longValue();
                                            } else {
                                                totalRecords = Long.parseLong(totalObj.toString());
                                            }
                                            log.info("获取到API返回的总记录数: {}", totalRecords);
                                        } catch (Exception e) {
                                            log.warn("转换总记录数失败: {}", e.getMessage());
                                        }
                                    }
                                }
                                
                                List<Object> pageData = result.getResult().getJSONArray("subOrderForSupplierList").stream().collect(java.util.stream.Collectors.toList());
                                
                                if (pageData.isEmpty()) {
                                    emptyResultCount++;
                                    // 如果连续两次返回空结果，说明确实没有更多数据了
                                    if (emptyResultCount >= 2) {
                                        log.info("连续{}次获取到空结果，停止获取数据", emptyResultCount);
                                        break;
                                    } else {
                                        log.warn("第{}页返回空结果，尝试获取下一页", page);
                                        page++;
                                        continue;
                                    }
                                } else {
                                    // 有数据，重置空结果计数
                                    emptyResultCount = 0;
                                }
                                
                                allOrderData.addAll(pageData);
                                currentCount += pageData.size();
                                
                                log.info("已获取第{}页数据，当前页记录数: {}，已获取总数: {}/{}", 
                                         page, pageData.size(), currentCount, totalRecords > 0 ? totalRecords : "未知");
                                
                                // 如果已知总记录数且已获取数据达到或超过总记录数，则退出
                                if (totalRecords > 0 && currentCount >= totalRecords) {
                                    log.info("已获取全部数据，总记录数: {}", totalRecords);
                                    break;
                                }
                                
                                // 如果返回的数据少于请求的数量，可能是最后一页，但不一定
                                // 如果连续两页都返回少于请求量的数据，或者已获取数据达到或接近总记录数，则认为已经获取全部数据
                                if (pageData.size() < pageSize) {
                                    if (totalRecords > 0) {
                                        // 如果已知总记录数，当获取数量已达到总数的90%以上，就认为数据基本获取完毕
                                        if (currentCount >= totalRecords * 0.9) {
                                            log.info("返回数据量少于请求量，且已获取数据接近总记录数，停止获取");
                                            break;
                                        }
                                    } else {
                                        // 如果不知道总记录数，记录警告但继续尝试下一页
                                        log.warn("第{}页返回数据量({}条)少于请求量({}条)，但由于无法确认总记录数，将继续尝试获取", 
                                                page, pageData.size(), pageSize);
                                    }
                                }
                                
                                // 下一页
                                page++;
                                
                                // 如果已经获取了大量数据(超过1000条)但返回很少数据(不到10条)，可能表示已接近末尾，防止无效查询
                                if (currentCount > 1000 && pageData.size() < 10) {
                                    log.info("已获取大量数据({}条)且当前页返回数据很少({}条)，停止继续查询", currentCount, pageData.size());
                                    break;
                                }
                                
                                // 限制最大页数查询，防止无限循环
                                if (page > 50) {
                                    log.warn("已达到最大页数限制(50页)，停止获取更多数据");
                                    break;
                                }
                            } else {
                                // API调用失败，记录错误，尝试重试1次
                                log.error("获取第{}页数据失败，将尝试重试", page);
                                
                                // 使用指数退避原则进行重试
                                boolean retrySuccess = false;
                                int maxRetryAttempts = 3; // 最大重试次数
                                Random random = new Random();
                                
                                for (int retryAttempt = 0; retryAttempt < maxRetryAttempts; retryAttempt++) {
                                    try {
                                        // 计算指数退避等待时间（毫秒）：2^尝试次数 * 1000
                                        long baseWaitTime = (long) Math.pow(2, retryAttempt) * 1000;
                                        
                                        // 添加随机抖动，避免惊群效应，在基础时间的基础上增加0-30%的随机时间
                                        long jitter = (long) (baseWaitTime * 0.3 * random.nextDouble());
                                        long waitTimeMs = baseWaitTime + jitter;
                                        
                                        log.info("第{}次重试，等待{}毫秒后重试", retryAttempt + 1, waitTimeMs);
                                        Thread.sleep(waitTimeMs);
                                        
                                        // 重试请求
                                        result = purchaseOrderService.getPurchaseOrderList(requestDTO, userId);
                                        
                                        if (result != null && result.getSuccess() && result.getResult() != null 
                                                && result.getResult().containsKey("subOrderForSupplierList")) {
                                            List<Object> pageData = result.getResult().getJSONArray("subOrderForSupplierList").stream().collect(java.util.stream.Collectors.toList());
                                            
                                            allOrderData.addAll(pageData);
                                            currentCount += pageData.size();
                                            page++;
                                            retrySuccess = true;
                                            log.info("第{}次重试成功", retryAttempt + 1);
                                            break; // 重试成功，跳出重试循环
                                        } else {
                                            log.warn("第{}次重试失败，准备下一次重试", retryAttempt + 1);
                                        }
                                    } catch (Exception retryEx) {
                                        log.error("第{}次重试出现异常: {}", retryAttempt + 1, retryEx.getMessage());
                                    }
                                }
                                
                                // 如果重试成功，继续主循环
                                if (retrySuccess) {
                                    continue;
                                }
                                
                                // 如果所有重试都失败，退出循环
                                log.error("所有重试均失败，停止获取更多数据");
                                break;
                            }
                        }
                        
                        // 新增：如果有多页数据且有排序参数，对合并后的数据进行统一排序
                        if (page > 1 && !allOrderData.isEmpty() && requestDTO.getOneDimensionSort() != null) {
                            sortMergedData(allOrderData, requestDTO.getOneDimensionSort());
                        }
                    }
                    
                    // 设置数据
                    context.setData(allOrderData);
                    context.setDataSize(allOrderData.size());
                    
                    log.info("按条件导出完成，共获取{}条记录", allOrderData.size());
                }
                
                log.info("采购单数据准备完成，共获取{}条记录", context.getDataSize());
            } catch (Exception e) {
                log.error("准备采购单导出数据失败", e);
                throw new RuntimeException("准备导出数据失败: " + e.getMessage(), e);
            }
        }
        
        @Override
        public void prepareImageTasks(ExcelExportService.ExcelExportContext context, File tempDir, List<ExcelExportUtils.ImageDownloadTask> downloadTasks) {
            List<Map<String, Object>> purchaseOrders = (List<Map<String, Object>>) context.getData();
            if (purchaseOrders == null || purchaseOrders.isEmpty()) {
                return;
            }

            int totalEstimatedImages = 0;
            for (int i = 0; i < purchaseOrders.size(); i++) {
                Map<String, Object> order = purchaseOrders.get(i);
                if (order.get("productSkcPicture") != null && !((String) order.get("productSkcPicture")).isEmpty()) {
                    totalEstimatedImages++;
                }
            }
            
            log.info("准备下载 {} 张采购单图片和生成二维码...", totalEstimatedImages);

            for (int i = 0; i < purchaseOrders.size(); i++) {
                Map<String, Object> order = purchaseOrders.get(i);
                // Excel数据从第三行开始，行号从0开始计数，所以第三行的索引是2
                // 因此图片的位置应该从rowIndex=2+i开始
                // 但由于Excel中图片锚点的特殊处理，需要调整行位置来确保图片与数据对齐
                // 根据截图显示的错位问题，需要将行索引-1以修正图片位置
                final int rowIndex = i + 2 - 1; // 修正图片位置偏移，确保与数据行对齐

                // 处理商品图片
                Object picUrlObj = order.get("productSkcPicture");
                if (picUrlObj instanceof String && !((String) picUrlObj).isEmpty()) {
                    String imageUrl = (String) picUrlObj;
                    try {
                        // 从URL提取或生成一个合理的文件名
                        String fileExtension = ".jpg"; // 默认jpg
                        int lastDot = imageUrl.lastIndexOf('.');
                        if (lastDot > 0 && imageUrl.length() - lastDot <= 5) {
                             fileExtension = imageUrl.substring(lastDot);
                        }
                        // 创建临时文件
                        File imageFile = new File(tempDir, "purchase_img_" + i + "_" + UUID.randomUUID() + fileExtension);
                        // 创建下载任务，目标列索引为 2 ("图片")
                        ExcelExportUtils.ImageDownloadTask task = new ExcelExportUtils.ImageDownloadTask(imageUrl, imageFile, 2, rowIndex);
                         // 设置行高以适应图片，但不设置行合并或列合并
                        task.setRowSpan(1);
                        task.setColSpan(1);
                        downloadTasks.add(task);
                    } catch (Exception e) {
                        log.error("为行 {} 创建图片下载任务失败, URL: {}", rowIndex, imageUrl, e);
                    }
                }
                
                // 生成二维码并添加到下载任务
                try {
                    // 获取必要参数
                    Long shopId = getLongValue(order, "shopId", 0L);
                    String subPurchaseOrderSn = getStringValue(order, "subPurchaseOrderSn", "");
                    long timestamp = System.currentTimeMillis();
                    
                    // 只有当订单号不为空时才生成二维码
                    if (!subPurchaseOrderSn.isEmpty()) {
                        // 创建二维码数据 - 使用JSON格式
                        JSONObject qrCodeJson = new JSONObject();
                        qrCodeJson.put("shopId", shopId);
                        qrCodeJson.put("subPurchaseOrderSn", subPurchaseOrderSn);
                        qrCodeJson.put("timestamp", timestamp);
                        String qrCodeData = qrCodeJson.toJSONString();
                        
                        // 生成二维码
                        File qrCodeFile = generateQRCode(qrCodeData, tempDir, "qrcode_" + i + "_" + UUID.randomUUID() + ".png");
                        
                        if (qrCodeFile != null) {
                            // 获取二维码列的索引 (订单号列后面)
                            // 基础列数 + 尺码列数 + 7 (考虑新增的两列)
                            // 原来是: 基础列数(8) + 尺码列数 + 5
                            int qrCodeColIndex = BASE_COLUMN_COUNT + actualSizeColumns.size() + 7;
                            
                            // 创建二维码图片下载任务，行位置与商品图片一致
                            ExcelExportUtils.ImageDownloadTask qrCodeTask = 
                                new ExcelExportUtils.ImageDownloadTask(qrCodeFile.getAbsolutePath(), qrCodeFile, qrCodeColIndex, rowIndex);
                            qrCodeTask.setRowSpan(1);
                            qrCodeTask.setColSpan(1);
                            downloadTasks.add(qrCodeTask);
                        }
                    }
                } catch (Exception e) {
                    log.error("为行 {} 创建二维码图片失败", rowIndex, e);
                }
            }
             log.info("采购单图片和二维码准备完成，共 {} 个任务", downloadTasks.size());
        }
        
        /**
         * 生成二维码图片
         * @param data 二维码数据
         * @param tempDir 临时目录
         * @param fileName 文件名
         * @return 二维码图片文件
         */
        private File generateQRCode(String data, File tempDir, String fileName) {
            try {
                QRCodeWriter qrCodeWriter = new QRCodeWriter();
                Map<EncodeHintType, Object> hints = new HashMap<>();
                hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
                hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
                hints.put(EncodeHintType.MARGIN, 1);
                
                BitMatrix bitMatrix = qrCodeWriter.encode(data, BarcodeFormat.QR_CODE, 200, 200, hints);
                BufferedImage qrImage = MatrixToImageWriter.toBufferedImage(bitMatrix);
                
                File qrFile = new File(tempDir, fileName);
                ImageIO.write(qrImage, "PNG", qrFile);
                
                return qrFile;
            } catch (WriterException | IOException e) {
                log.error("生成二维码失败", e);
                return null;
            }
        }
        
        @Override
        public void fillExcelData(ExcelExportService.ExcelExportContext context, Sheet sheet, CellStyle contentStyle) {
            List<Map<String, Object>> purchaseOrders = (List<Map<String, Object>>) context.getData();
            if (purchaseOrders == null || purchaseOrders.isEmpty()) {
                return;
            }
            
            // 处理表头单元格合并 - 生产进度状态表头
            // 计算基本列数量 + 尺码列数量 + 基础结尾列数(8个，包含新增的两个字段)
            int baseColCount = BASE_COLUMN_COUNT + actualSizeColumns.size() + 8;
            
            // 创建表头样式
            CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
            headerStyle.cloneStyleFrom(contentStyle);
            headerStyle.setAlignment(org.apache.poi.ss.usermodel.HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);
            headerStyle.setFillForegroundColor(org.apache.poi.ss.usermodel.IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());
            headerStyle.setFillPattern(org.apache.poi.ss.usermodel.FillPatternType.SOLID_FOREGROUND);
            
            // 创建合并的生产进度状态表头
            String[] progressTypes = new String[] {
                "烧花/剪图", "车间/拣货", "剪线/压图", "查货", "包装", "发货"
            };
            
            // 获取第一行（原有表头）
            Row headerRow = sheet.getRow(0);
            
            // 合并并设置生产进度表头
            for (int i = 0; i < 6; i++) {
                int startCol = baseColCount + i * 2;
                int endCol = startCol + 1;
                
                // 创建合并区域
                org.apache.poi.ss.util.CellRangeAddress mergedRegion = 
                    new org.apache.poi.ss.util.CellRangeAddress(0, 0, startCol, endCol);
                sheet.addMergedRegion(mergedRegion);
                
                // 设置合并单元格的值
                org.apache.poi.ss.usermodel.Cell cell = headerRow.getCell(startCol);
                if (cell == null) {
                    cell = headerRow.createCell(startCol);
                }
                cell.setCellValue(progressTypes[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // 创建第二行表头
            Row subHeaderRow = sheet.createRow(1);
            subHeaderRow.setHeight((short) (25 * 20));
            
            // 复制第一行表头的基本列和尺码列（直到二维码列）
            for (int i = 0; i < baseColCount; i++) {
                org.apache.poi.ss.usermodel.Cell srcCell = headerRow.getCell(i);
                org.apache.poi.ss.usermodel.Cell destCell = subHeaderRow.createCell(i);
                if (srcCell != null) {
                    destCell.setCellValue(srcCell.getStringCellValue());
                }
                destCell.setCellStyle(headerStyle);
                
                // 创建合并区域（垂直合并）
                org.apache.poi.ss.util.CellRangeAddress mergedRegion = 
                    new org.apache.poi.ss.util.CellRangeAddress(0, 1, i, i);
                
                // 检查该区域是否已经被合并
                boolean alreadyMerged = false;
                for (int j = 0; j < sheet.getNumMergedRegions(); j++) {
                    org.apache.poi.ss.util.CellRangeAddress existingRegion = sheet.getMergedRegion(j);
                    if (existingRegion.getFirstRow() == mergedRegion.getFirstRow() && 
                        existingRegion.getLastRow() == mergedRegion.getLastRow() && 
                        existingRegion.getFirstColumn() == mergedRegion.getFirstColumn() && 
                        existingRegion.getLastColumn() == mergedRegion.getLastColumn()) {
                        alreadyMerged = true;
                        break;
                    }
                }
                
                if (!alreadyMerged) {
                    sheet.addMergedRegion(mergedRegion);
                }
            }
            
            // 设置生产进度子表头
            for (int i = 0; i < 6; i++) {
                int operatorCol = baseColCount + i * 2;
                int timeCol = baseColCount + i * 2 + 1;
                
                org.apache.poi.ss.usermodel.Cell operatorCell = subHeaderRow.createCell(operatorCol);
                operatorCell.setCellValue("操作人");
                operatorCell.setCellStyle(headerStyle);
                
                org.apache.poi.ss.usermodel.Cell timeCell = subHeaderRow.createCell(timeCol);
                timeCell.setCellValue("时间");
                timeCell.setCellStyle(headerStyle);
            }
            
            // 从第三行开始填充数据（第一行是主表头，第二行是子表头）
            int rowIndex = 2;
            // 格式化日期
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            
            // 预先批量获取所有订单的生产进度数据
            Map<String, ProductionProgressVO> progressMap = new HashMap<>();
            try {
                // 批量查询生产进度
                progressMap = productionProgressService.batchGetProgressDetails(purchaseOrders);
                log.info("批量加载生产进度数据, 成功获取{}个记录", progressMap.size());
            } catch (Exception e) {
                log.error("批量获取生产进度数据异常: {}", e.getMessage());
            }
            
            for (int i = 0; i < purchaseOrders.size(); i++) {
                Map<String, Object> order = purchaseOrders.get(i);
                
                // 行数据计数从1开始
                int serialNumber = i + 1;
                
                Row row = sheet.createRow(rowIndex++);
                row.setHeight((short) (80 * 20)); // 设置行高以适应图片
                
                int colIndex = 0;
                
                // 0. 序号 - 修改为直接设置数值
                org.apache.poi.ss.usermodel.Cell serialCell = row.createCell(colIndex++);
                serialCell.setCellValue(serialNumber);
                serialCell.setCellStyle(contentStyle);
                
                // 1. 店铺备注
                ExcelExportUtils.setCellValue(row, colIndex++, getStringValue(order, "shopRemark", ""), contentStyle);
                
                // 2. 图片 (留空，由框架填充)
                ExcelExportUtils.setCellValue(row, colIndex++, "", contentStyle);
                
                // 3. 店铺名
                ExcelExportUtils.setCellValue(row, colIndex++, getStringValue(order, "shopName", ""), contentStyle);
                
                // 4. skc货号
                ExcelExportUtils.setCellValue(row, colIndex++, getStringValue(order, "productSn", ""), contentStyle);
                
                // 5. 颜色 - 从第一个SKU的className中提取
                String color = "";
                List<Map<String, Object>> skuList = (List<Map<String, Object>>) order.get("skuQuantityDetailList");
                if (skuList != null && !skuList.isEmpty()) {
                    String className = getStringValue(skuList.get(0), "className", "");
                    color = extractColorFromClassName(className);
                }
                ExcelExportUtils.setCellValue(row, colIndex++, color, contentStyle);
                
                // 6. 首/返单
                boolean isFirst = getBooleanValue(order, "isFirst", false);
                ExcelExportUtils.setCellValue(row, colIndex++, isFirst ? "首" : "返", contentStyle);
                
                // 7. 码段 & 8. 各尺码数量
                Map<String, Integer> sizeQuantityMap = new HashMap<>();
                
                // 0: minSizeOrderS, 1: maxSizeOrderS, 2: minSizeOrderY, 3: maxSizeOrderY
                int[] minMaxSizeOrder = {Integer.MAX_VALUE, Integer.MIN_VALUE, Integer.MAX_VALUE, Integer.MIN_VALUE};
                // 0: minSizeStrS, 1: maxSizeStrS, 2: minSizeStrY, 3: maxSizeStrY
                String[] minMaxSizeStr = {"", "", "", ""};
                
                // 记录当前订单使用的尺码类型
                boolean hasSXXL = false;
                boolean has4Y16Y = false;
                
                if (skuList != null && !skuList.isEmpty()) {
                    for (Map<String, Object> sku : skuList) {
                        String className = getStringValue(sku, "className", "");
                        Integer quantity = getIntegerValue(sku, "purchaseQuantity", 0);
                        
                        // 检测当前订单的尺码类型
                        if (className.contains("-S") || className.contains("-M") || 
                            className.contains("-L") || className.contains("-XL") || 
                            className.contains("-XXL")) {
                            hasSXXL = true;
                        }
                        
                        if (className.matches(".*\\d+Y.*") || 
                            className.matches(".*\\d+-\\d+Y.*")) {
                            has4Y16Y = true;
                        }
                        
                        // 处理尺码数据
                        processSizeData(className, quantity, sizeQuantityMap, minMaxSizeOrder, minMaxSizeStr);
                    }
                }
                
                // 7. 码段 - 写入固定的类型标识，不再显示具体的尺码范围
                String displayRange = "";
                
                // 处理S-XXL类型，使用固定字符串"S-XXL"
                if (hasSXXL) {
                    displayRange = "S-XXL";
                }
                
                // 处理4Y-16Y类型，使用固定字符串"4Y-16Y"
                if (has4Y16Y) {
                    if (!displayRange.isEmpty()) {
                        displayRange += ", ";
                    }
                    displayRange += "4Y-16Y";
                }
                
                ExcelExportUtils.setCellValue(row, colIndex++, displayRange, contentStyle);
                
                // 8. 填充各尺码列数量 - 修改为数值类型
                for (String sizeHeader : actualSizeColumns) {
                    int targetColIndex = sizeColumnIndexMap.get(sizeHeader);
                    int quantity = sizeQuantityMap.getOrDefault(sizeHeader, 0);
                    // 只有当数量大于0时才设置单元格值，避免显示0
                    if (quantity > 0) {
                        org.apache.poi.ss.usermodel.Cell sizeCell = row.createCell(targetColIndex);
                        sizeCell.setCellValue(quantity);
                        sizeCell.setCellStyle(contentStyle);
                    } else {
                        // 数量为0时创建空白单元格
                        org.apache.poi.ss.usermodel.Cell sizeCell = row.createCell(targetColIndex);
                        sizeCell.setCellStyle(contentStyle);
                    }
                }
                
                // 计算"数量合计"列的索引 = 基础列数(8) + 尺码列数
                colIndex = BASE_COLUMN_COUNT + actualSizeColumns.size();
                
                // 9. 数量合计 - 修改为数值类型
                Map<String, Object> totalInfo = (Map<String, Object>) order.get("skuQuantityTotalInfo");
                int purchaseQuantity = 0;
                if (totalInfo != null && totalInfo.get("purchaseQuantity") != null) {
                    try {
                        purchaseQuantity = Integer.parseInt(totalInfo.get("purchaseQuantity").toString());
                    } catch (NumberFormatException e) {
                        log.warn("转换数量合计为整数失败: {}", e.getMessage());
                    }
                }
                org.apache.poi.ss.usermodel.Cell totalCell = row.createCell(colIndex++);
                totalCell.setCellValue(purchaseQuantity);
                totalCell.setCellStyle(contentStyle);
                
                // 10. 送货 - 新增字段
                int deliverQuantity = 0;
                if (totalInfo != null && totalInfo.get("deliverQuantity") != null) {
                    try {
                        deliverQuantity = Integer.parseInt(totalInfo.get("deliverQuantity").toString());
                    } catch (NumberFormatException e) {
                        log.warn("转换送货数量为整数失败: {}", e.getMessage());
                    }
                }
                org.apache.poi.ss.usermodel.Cell deliverCell = row.createCell(colIndex++);
                deliverCell.setCellValue(deliverQuantity);
                deliverCell.setCellStyle(contentStyle);
                
                // 11. 入库 - 新增字段
                int realReceiveQuantity = 0;
                if (totalInfo != null && totalInfo.get("realReceiveAuthenticQuantity") != null) {
                    try {
                        realReceiveQuantity = Integer.parseInt(totalInfo.get("realReceiveAuthenticQuantity").toString());
                    } catch (NumberFormatException e) {
                        log.warn("转换入库数量为整数失败: {}", e.getMessage());
                    }
                }
                org.apache.poi.ss.usermodel.Cell receiveCell = row.createCell(colIndex++);
                receiveCell.setCellValue(realReceiveQuantity);
                receiveCell.setCellStyle(contentStyle);
                
                // 12. 下单日期
                Long purchaseTime = getLongValue(order, "purchaseTime", null);
                String purchaseTimeStr = "";
                if (purchaseTime != null) {
                    purchaseTimeStr = sdf.format(new Date(purchaseTime));
                }
                ExcelExportUtils.setCellValue(row, colIndex++, purchaseTimeStr, contentStyle);
                
                // 13. 发货日期
                String deliverTimeStr = "";
                Map<String, Object> deliverInfo = (Map<String, Object>) order.get("deliverInfo");
                if (deliverInfo != null) {
                    Long deliverTime = getLongValue(deliverInfo, "deliverTime", null);
                    if (deliverTime != null) {
                        deliverTimeStr = sdf.format(new Date(deliverTime));
                    }
                }
                ExcelExportUtils.setCellValue(row, colIndex++, deliverTimeStr, contentStyle);
                
                // 14. SKC (ID) - 尝试转换为数值类型
                String skcIdStr = getStringValue(order, "productSkcId", "");
                try {
                    // 如果SKC ID是纯数字，则以数值形式存储
                    if (skcIdStr.matches("\\d+")) {
                        long skcId = Long.parseLong(skcIdStr);
                        org.apache.poi.ss.usermodel.Cell skcCell = row.createCell(colIndex++);
                        skcCell.setCellValue(skcId);
                        skcCell.setCellStyle(contentStyle);
                    } else {
                        // 如果不是纯数字，仍以字符串形式存储
                        ExcelExportUtils.setCellValue(row, colIndex++, skcIdStr, contentStyle);
                    }
                } catch (NumberFormatException e) {
                    // 转换失败时，仍以字符串形式存储
                    ExcelExportUtils.setCellValue(row, colIndex++, skcIdStr, contentStyle);
                }
                
                // 15. 订单号
                String subPurchaseOrderSn = getStringValue(order, "subPurchaseOrderSn", "");
                ExcelExportUtils.setCellValue(row, colIndex++, subPurchaseOrderSn, contentStyle);
                
                // 注意：这里需要跳过二维码列，因为二维码图片将由prepareImageTasks方法处理
                // 跳过二维码列
                colIndex++;
                
                // 16-21. 生产进度相关信息
                // 从预加载的Map中获取生产进度数据
                Long shopId = getLongValue(order, "shopId", null);
                ProductionProgressVO progressVO = null;
                if (shopId != null && !subPurchaseOrderSn.isEmpty()) {
                    String key = shopId + ":" + subPurchaseOrderSn;
                    progressVO = progressMap.get(key);
                }
                
                // 日期格式化器，用于格式化生产进度完成时间
                SimpleDateFormat progressTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                
                // 16. 烧花/剪图状态 - 操作人
                String cuttingOperator = progressVO != null && progressVO.getCuttingStatus() != null && progressVO.getCuttingStatus() == 1 ? 
                                       progressVO.getCuttingOperatorName() : "";
                ExcelExportUtils.setCellValue(row, colIndex++, cuttingOperator, contentStyle);
                
                // 烧花/剪图状态 - 时间
                String cuttingTime = "";
                if (progressVO != null && progressVO.getCuttingTime() != null) {
                    cuttingTime = progressTimeFormat.format(progressVO.getCuttingTime());
                }
                ExcelExportUtils.setCellValue(row, colIndex++, cuttingTime, contentStyle);
                
                // 17. 车间/拣货状态 - 操作人
                String workshopOperator = progressVO != null && progressVO.getWorkshopStatus() != null && progressVO.getWorkshopStatus() == 1 ? 
                                        progressVO.getWorkshopOperatorName() : "";
                ExcelExportUtils.setCellValue(row, colIndex++, workshopOperator, contentStyle);
                
                // 车间/拣货状态 - 时间
                String workshopTime = "";
                if (progressVO != null && progressVO.getWorkshopTime() != null) {
                    workshopTime = progressTimeFormat.format(progressVO.getWorkshopTime());
                }
                ExcelExportUtils.setCellValue(row, colIndex++, workshopTime, contentStyle);
                
                // 18. 剪线/压图状态 - 操作人
                String trimmingOperator = progressVO != null && progressVO.getTrimmingStatus() != null && progressVO.getTrimmingStatus() == 1 ? 
                                        progressVO.getTrimmingOperatorName() : "";
                ExcelExportUtils.setCellValue(row, colIndex++, trimmingOperator, contentStyle);
                
                // 剪线/压图状态 - 时间
                String trimmingTime = "";
                if (progressVO != null && progressVO.getTrimmingTime() != null) {
                    trimmingTime = progressTimeFormat.format(progressVO.getTrimmingTime());
                }
                ExcelExportUtils.setCellValue(row, colIndex++, trimmingTime, contentStyle);
                
                // 19. 查货状态 - 操作人
                String inspectionOperator = progressVO != null && progressVO.getInspectionStatus() != null && progressVO.getInspectionStatus() == 1 ? 
                                          progressVO.getInspectionOperatorName() : "";
                ExcelExportUtils.setCellValue(row, colIndex++, inspectionOperator, contentStyle);
                
                // 查货状态 - 时间
                String inspectionTime = "";
                if (progressVO != null && progressVO.getInspectionTime() != null) {
                    inspectionTime = progressTimeFormat.format(progressVO.getInspectionTime());
                }
                ExcelExportUtils.setCellValue(row, colIndex++, inspectionTime, contentStyle);
                
                // 20. 包装状态 - 操作人
                String packagingOperator = progressVO != null && progressVO.getPackagingStatus() != null && progressVO.getPackagingStatus() == 1 ? 
                                         progressVO.getPackagingOperatorName() : "";
                ExcelExportUtils.setCellValue(row, colIndex++, packagingOperator, contentStyle);
                
                // 包装状态 - 时间
                String packagingTime = "";
                if (progressVO != null && progressVO.getPackagingTime() != null) {
                    packagingTime = progressTimeFormat.format(progressVO.getPackagingTime());
                }
                ExcelExportUtils.setCellValue(row, colIndex++, packagingTime, contentStyle);
                
                // 21. 发货状态 - 操作人
                String shippingOperator = progressVO != null && progressVO.getShippingStatus() != null && progressVO.getShippingStatus() == 1 ? 
                                        progressVO.getShippingOperatorName() : "";
                ExcelExportUtils.setCellValue(row, colIndex++, shippingOperator, contentStyle);
                
                // 发货状态 - 时间
                String shippingTime = "";
                if (progressVO != null && progressVO.getShippingTime() != null) {
                    shippingTime = progressTimeFormat.format(progressVO.getShippingTime());
                }
                ExcelExportUtils.setCellValue(row, colIndex++, shippingTime, contentStyle);
                
                // 二维码列已被跳过，由框架通过prepareImageTasks方法填充
            }
        }
        
        /**
         * 从Map中获取字符串值
         */
        private String getStringValue(Map<String, Object> map, String key, String defaultValue) {
            Object value = map.get(key);
            return value != null ? value.toString() : defaultValue;
        }

        /**
         * 从Map中获取布尔值
         */
         private boolean getBooleanValue(Map<String, Object> map, String key, boolean defaultValue) {
            Object value = map.get(key);
             if (value instanceof Boolean) {
                 return (Boolean) value;
             }
             if (value != null) {
                return Boolean.parseBoolean(value.toString());
             }
             return defaultValue;
         }

        /**
         * 从Map中获取长整型值
         */
         private Long getLongValue(Map<String, Object> map, String key, Long defaultValue) {
             Object value = map.get(key);
             if (value instanceof Number) {
                 return ((Number) value).longValue();
             }
             if (value != null) {
                 try {
                     return Long.parseLong(value.toString());
                 } catch (NumberFormatException e) {
                     log.warn("无法将值 '{}' 解析为 Long 类型，键: {}", value, key);
                 }
             }
             return defaultValue;
         }
         
          /**
         * 从Map中获取整型值
         */
         private Integer getIntegerValue(Map<String, Object> map, String key, Integer defaultValue) {
             Object value = map.get(key);
             if (value instanceof Number) {
                 return ((Number) value).intValue();
             }
             if (value != null) {
                 try {
                     return Integer.parseInt(value.toString());
                 } catch (NumberFormatException e) {
                    log.warn("无法将值 '{}' 解析为 Integer 类型，键: {}", value, key);
                 }
             }
             return defaultValue;
         }

        /**
         * 从className中提取颜色信息
         */
        private String extractColorFromClassName(String className) {
            if (className == null || className.isEmpty()) {
                return "";
            }
            
            // className的格式通常为"颜色-尺码"，例如"混合色-XXL"
            int separatorIndex = className.indexOf('-');
            if (separatorIndex > 0) {
                return className.substring(0, separatorIndex);
            }
            
            return className; // 如果没有分隔符，则返回整个字符串
        }

        /**
         * 对合并后的数据进行排序
         * @param dataList 合并后的数据列表
         * @param sortParamObj 排序参数
         */
        private void sortMergedData(List<Object> dataList, Object sortParamObj) {
            try {
                if (sortParamObj == null) {
                    log.warn("排序参数无效，无法进行数据排序");
                    return;
                }
                
                // 处理不同类型的排序参数
                String sortField;
                boolean isDesc;
                
                if (sortParamObj instanceof PurchaseOrderRequestDTO.OneDimensionSort) {
                    PurchaseOrderRequestDTO.OneDimensionSort sortParam = (PurchaseOrderRequestDTO.OneDimensionSort) sortParamObj;
                    sortField = sortParam.getFirstOrderByParam();
                    isDesc = sortParam.getFirstOrderByDesc() != null && sortParam.getFirstOrderByDesc() == 1;
                } else if (sortParamObj instanceof Map) {
                    // 兼容Map类型参数
                    Map<String, Object> sortParam = (Map<String, Object>) sortParamObj;
                    sortField = (String) sortParam.get("firstOrderByParam");
                    isDesc = sortParam.get("firstOrderByDesc") != null && 
                            ((Number)sortParam.get("firstOrderByDesc")).intValue() == 1;
                } else {
                    log.warn("未知的排序参数类型: {}", sortParamObj.getClass().getName());
                    return;
                }
                
                if (sortField == null || sortField.isEmpty()) {
                    log.warn("排序字段为空，无法进行数据排序");
                    return;
                }
                
                log.info("开始对合并后的数据进行排序: 字段={}, 是否降序={}", sortField, isDesc);
                
                Collections.sort(dataList, (a, b) -> {
                    try {
                        Map<String, Object> mapA = (Map<String, Object>) a;
                        Map<String, Object> mapB = (Map<String, Object>) b;
                        
                        // 根据不同的排序字段选择不同的排序策略
                        switch (sortField) {
                            case "createdAt":
                                // 按创建时间排序
                                return compareValues(getLongValue(mapA, "purchaseTime", 0L),
                                                   getLongValue(mapB, "purchaseTime", 0L),
                                                   isDesc);
                            
                            case "expectLatestDeliverTime":
                                // 按最晚发货时间排序
                                Long valueA = getNestedLongValue(mapA, "deliverInfo", "expectLatestDeliverTimeOrDefault", 0L);
                                Long valueB = getNestedLongValue(mapB, "deliverInfo", "expectLatestDeliverTimeOrDefault", 0L);
                                return compareValues(valueA, valueB, isDesc);
                            
                            case "expectLatestArrivalTime":
                                // 按最晚到货时间排序
                                Long arrivalA = getNestedLongValue(mapA, "deliverInfo", "expectLatestArrivalTimeOrDefault", 0L);
                                Long arrivalB = getNestedLongValue(mapB, "deliverInfo", "expectLatestArrivalTimeOrDefault", 0L);
                                return compareValues(arrivalA, arrivalB, isDesc);
                            
                            default:
                                // 默认按订单号排序
                                return compareValues(getStringValue(mapA, "subPurchaseOrderSn", ""),
                                                   getStringValue(mapB, "subPurchaseOrderSn", ""),
                                                   isDesc);
                        }
                    } catch (Exception e) {
                        log.error("比较数据时出错: {}", e.getMessage(), e);
                        return 0;
                    }
                });
                
                log.info("数据排序完成，共处理{}条记录", dataList.size());
            } catch (Exception e) {
                log.error("对合并数据进行排序时发生异常: {}", e.getMessage(), e);
            }
        }

        /**
         * 比较两个可比较对象的值
         * @param valueA 第一个值
         * @param valueB 第二个值
         * @param isDesc 是否降序
         * @return 比较结果
         */
        private <T extends Comparable<T>> int compareValues(T valueA, T valueB, boolean isDesc) {
            int result;
            if (valueA == null && valueB == null) {
                result = 0;
            } else if (valueA == null) {
                result = -1;
            } else if (valueB == null) {
                result = 1;
            } else {
                result = valueA.compareTo(valueB);
            }
            
            return isDesc ? -result : result;
        }

        /**
         * 获取嵌套Map中的Long值
         */
        private Long getNestedLongValue(Map<String, Object> map, String key1, String key2, Long defaultValue) {
            Object nestedObj = map.get(key1);
            if (nestedObj instanceof Map) {
                Map<String, Object> nestedMap = (Map<String, Object>) nestedObj;
                return getLongValue(nestedMap, key2, defaultValue);
            }
            return defaultValue;
        }
    }
} 