package com.xiao.temu.modules.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

/**
 * 店铺实体类
 */
@TableName("shop")
public class Shop {
    
    /** 店铺ID */
    @TableId(value = "shop_id", type = IdType.AUTO)
    private Long shopId;
    
    /** 店铺名称 */
    @TableField("shop_name")
    private String shopName;
    
    /** temu平台店铺id */
    @TableField("shop_temu_id")
    private String shopTemuId;
    
    /** API密钥 */
    @TableField("api_key")
    private String apiKey;
    
    /** API密钥Secret */
    @TableField("api_secret")
    private String apiSecret;
    
    /** access_token */
    @TableField("access_token")
    private String accessToken;
    
    /** 状态（0正常 1禁用） */
    @TableField("status")
    private String status;
    
    /** 创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /** 更新时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;
    
    /** 备注 */
    @TableField("remark")
    private String remark;
    
    /** 创建者ID */
    @TableField("create_by")
    private Long createBy;
    
    /** 所属运营组ID */
    @TableField("belong_group_id")
    private Long belongGroupId;

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShopTemuId() {
        return shopTemuId;
    }

    public void setShopTemuId(String shopTemuId) {
        this.shopTemuId = shopTemuId;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getApiSecret() {
        return apiSecret;
    }

    public void setApiSecret(String apiSecret) {
        this.apiSecret = apiSecret;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    
    public Long getCreateBy() {
        return createBy;
    }
    
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }
    
    public Long getBelongGroupId() {
        return belongGroupId;
    }
    
    public void setBelongGroupId(Long belongGroupId) {
        this.belongGroupId = belongGroupId;
    }
} 