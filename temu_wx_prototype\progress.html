<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进度更新 - TEMU备货单追踪</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f7f7f7;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }
        .wx-container {
            max-width: 414px;
            min-height: 100vh;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 0;
            box-shadow: 0 0 10px rgba(0,0,0,0.05);
            overflow: hidden;
            position: relative;
        }
        @media (min-width: 500px) {
            .wx-container {
                min-height: 90vh;
                margin: 20px auto;
                border-radius: 30px;
            }
        }
        .wx-btn-primary {
            background-color: #07c160;
            color: white;
        }
        .wx-btn-primary:hover {
            background-color: #06ad56;
        }
        .progress-line {
            position: absolute;
            top: 24px;
            left: 34px;
            width: 2px;
            height: calc(100% - 48px);
            background-color: #ddd;
            z-index: 0;
        }
        .step-circle {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="wx-container">
        <div class="flex flex-col h-screen">
            <header class="py-4 px-4 flex justify-between items-center border-b border-gray-200">
                <div class="flex items-center">
                    <a href="scan.html" class="mr-2">
                        <i class="fas fa-arrow-left text-gray-600"></i>
                    </a>
                    <h1 class="text-xl font-medium">备货单进度</h1>
                </div>
                <div class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full">烧花工</div>
            </header>
            
            <div class="flex-grow p-6">
                <div class="bg-gray-50 p-4 rounded-lg mb-6">
                    <h3 class="font-medium mb-2">备货单信息</h3>
                    <div class="text-gray-700">
                        <p>备货单号：<span class="font-medium">TM20230616001</span></p>
                        <p>产品名称：女式连衣裙</p>
                        <p>订单数量：200件</p>
                        <p>所在工序：烧花工序</p>
                    </div>
                </div>
                
                <h3 class="font-medium mb-4">生产进度</h3>
                
                <div class="relative">
                    <div class="progress-line"></div>
                    
                    <!-- 已完成步骤 -->
                    <div class="flex mb-6 relative">
                        <div class="flex-none flex items-center justify-center w-16 relative">
                            <div class="step-circle bg-green-500 border-2 border-white"></div>
                        </div>
                        <div class="flex-grow bg-green-50 p-3 rounded-lg">
                            <div class="flex justify-between">
                                <div>
                                    <p class="font-medium text-gray-800">裁剪完成</p>
                                    <p class="text-xs text-gray-500">2023/06/16 08:30</p>
                                </div>
                                <div class="text-green-600">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 当前步骤 -->
                    <div class="flex mb-6 relative">
                        <div class="flex-none flex items-center justify-center w-16 relative">
                            <div class="step-circle bg-yellow-500 border-2 border-white"></div>
                        </div>
                        <div class="flex-grow bg-yellow-50 p-3 rounded-lg">
                            <div class="flex justify-between">
                                <div>
                                    <p class="font-medium text-gray-800">烧花工序</p>
                                    <p class="text-xs text-gray-500">进行中</p>
                                </div>
                                <div class="text-yellow-600">
                                    <i class="fas fa-clock"></i>
                                </div>
                            </div>
                            <div class="mt-3">
                                <button class="w-full py-2 bg-[#07c160] text-white text-center rounded-md font-medium">
                                    确认完成烧花工序
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 未开始步骤 -->
                    <div class="flex mb-6 relative">
                        <div class="flex-none flex items-center justify-center w-16 relative">
                            <div class="step-circle bg-gray-300 border-2 border-white"></div>
                        </div>
                        <div class="flex-grow bg-gray-50 p-3 rounded-lg">
                            <div class="flex justify-between">
                                <div>
                                    <p class="font-medium text-gray-400">车缝工序</p>
                                    <p class="text-xs text-gray-400">等待中</p>
                                </div>
                                <div class="text-gray-400">
                                    <i class="fas fa-hourglass"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex mb-6 relative">
                        <div class="flex-none flex items-center justify-center w-16 relative">
                            <div class="step-circle bg-gray-300 border-2 border-white"></div>
                        </div>
                        <div class="flex-grow bg-gray-50 p-3 rounded-lg">
                            <div class="flex justify-between">
                                <div>
                                    <p class="font-medium text-gray-400">尾部处理</p>
                                    <p class="text-xs text-gray-400">等待中</p>
                                </div>
                                <div class="text-gray-400">
                                    <i class="fas fa-hourglass"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex mb-6 relative">
                        <div class="flex-none flex items-center justify-center w-16 relative">
                            <div class="step-circle bg-gray-300 border-2 border-white"></div>
                        </div>
                        <div class="flex-grow bg-gray-50 p-3 rounded-lg">
                            <div class="flex justify-between">
                                <div>
                                    <p class="font-medium text-gray-400">质检</p>
                                    <p class="text-xs text-gray-400">等待中</p>
                                </div>
                                <div class="text-gray-400">
                                    <i class="fas fa-hourglass"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex mb-6 relative">
                        <div class="flex-none flex items-center justify-center w-16 relative">
                            <div class="step-circle bg-gray-300 border-2 border-white"></div>
                        </div>
                        <div class="flex-grow bg-gray-50 p-3 rounded-lg">
                            <div class="flex justify-between">
                                <div>
                                    <p class="font-medium text-gray-400">包装</p>
                                    <p class="text-xs text-gray-400">等待中</p>
                                </div>
                                <div class="text-gray-400">
                                    <i class="fas fa-hourglass"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex mb-6 relative">
                        <div class="flex-none flex items-center justify-center w-16 relative">
                            <div class="step-circle bg-gray-300 border-2 border-white"></div>
                        </div>
                        <div class="flex-grow bg-gray-50 p-3 rounded-lg">
                            <div class="flex justify-between">
                                <div>
                                    <p class="font-medium text-gray-400">发货</p>
                                    <p class="text-xs text-gray-400">等待中</p>
                                </div>
                                <div class="text-gray-400">
                                    <i class="fas fa-hourglass"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <footer class="bg-white border-t border-gray-200">
                <div class="grid grid-cols-3 text-center">
                    <a href="home.html" class="py-3 flex flex-col items-center text-gray-500">
                        <i class="fas fa-home text-xl"></i>
                        <span class="text-xs mt-1">首页</span>
                    </a>
                    <a href="scan.html" class="py-3 flex flex-col items-center text-gray-500">
                        <i class="fas fa-qrcode text-xl"></i>
                        <span class="text-xs mt-1">扫码</span>
                    </a>
                    <a href="worklist.html" class="py-3 flex flex-col items-center text-gray-500">
                        <i class="fas fa-list-ul text-xl"></i>
                        <span class="text-xs mt-1">列表</span>
                    </a>
                </div>
            </footer>
        </div>
    </div>
    
    <script>
        // 演示用：点击完成按钮后的交互
        document.querySelector('button').addEventListener('click', function() {
            alert('已确认完成烧花工序，进度已更新！');
            setTimeout(function() {
                window.location.href = 'home.html';
            }, 1500);
        });
    </script>
</body>
</html> 