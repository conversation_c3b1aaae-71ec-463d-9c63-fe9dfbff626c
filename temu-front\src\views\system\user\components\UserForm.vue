<!-- 用户表单组件 -->
<template>
  <el-dialog 
    :title="title" 
    v-model="dialogVisible" 
    width="600px" 
    append-to-body
    destroy-on-close
  >
    <el-form 
      ref="formRef" 
      :model="form" 
      :rules="rules" 
      label-width="100px"
    >
      <el-form-item label="用户名" prop="username">
        <el-input v-model="form.username" placeholder="请输入用户名" :disabled="isEdit" />
      </el-form-item>
      
      <el-form-item label="昵称" prop="nickName">
        <el-input v-model="form.nickName" placeholder="请输入昵称" />
      </el-form-item>
      
      <el-form-item label="密码" prop="password" v-if="!isEdit">
        <el-input 
          v-model="form.password" 
          placeholder="请输入密码" 
          type="password" 
          show-password 
        />
      </el-form-item>
      
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="form.email" placeholder="请输入邮箱" />
      </el-form-item>
      
      <el-form-item label="手机号码" prop="phone">
        <el-input v-model="form.phone" placeholder="请输入手机号码" />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio label="0">正常</el-radio>
          <el-radio label="1">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input 
          v-model="form.remark" 
          type="textarea" 
          placeholder="请输入备注" 
          maxlength="200" 
          show-word-limit 
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { addUser, updateUser, getUser } from '@/api/user'

const props = defineProps({
  // 弹窗标题
  title: {
    type: String,
    default: ''
  },
  // 是否显示
  visible: {
    type: Boolean,
    default: false
  },
  // 用户ID，0表示新增
  userId: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  userId: 0,
  username: '',
  password: '',
  nickName: '',
  email: '',
  phone: '',
  status: '0',
  remark: ''
})

// 表单校验规则
const rules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  nickName: [
    { required: true, message: '请输入昵称', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: false, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { required: false, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
})

// 是否为编辑模式
const isEdit = computed(() => props.userId > 0)

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 监听用户ID变化，加载用户数据
watch(() => props.userId, async (newVal) => {
  if (newVal > 0) {
    await getUserDetail(newVal)
  } else {
    resetForm()
  }
})

// 获取用户详情
const getUserDetail = async (userId: number) => {
  try {
    const res = await getUser(userId)
    const userData = res.data.user
    
    Object.assign(form, {
      userId: userData.userId,
      username: userData.username,
      nickName: userData.nickName,
      email: userData.email,
      phone: userData.phone,
      status: userData.status,
      remark: userData.remark
    })
    
    // 编辑模式不需要填写密码
    form.password = ''
  } catch (error) {
    console.error('获取用户详情失败', error)
    ElMessage.error('获取用户详情失败')
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    userId: 0,
    username: '',
    password: '',
    nickName: '',
    email: '',
    phone: '',
    status: '0',
    remark: ''
  })
}

// 取消操作
const cancel = () => {
  dialogVisible.value = false
  resetForm()
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 新增或更新用户
        if (props.userId === 0) {
          await addUser(form)
          ElMessage.success('新增用户成功')
        } else {
          await updateUser(form)
          ElMessage.success('修改用户成功')
        }
        
        dialogVisible.value = false
        emit('success')
      } catch (error) {
        console.error('保存用户失败', error)
        ElMessage.error('保存用户失败')
      }
    }
  })
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style> 