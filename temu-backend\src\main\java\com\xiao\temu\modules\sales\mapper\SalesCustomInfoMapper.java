package com.xiao.temu.modules.sales.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.sales.entity.SalesCustomInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 销售定制信息Mapper接口
 */
@Mapper
public interface SalesCustomInfoMapper extends BaseMapper<SalesCustomInfo> {
    
    /**
     * 统计指定店铺的定制信息记录数
     * 
     * @param shopId 店铺ID
     * @return 记录数
     */
    @Select("SELECT COUNT(*) FROM sales_custom_info WHERE shop_id = #{shopId}")
    Integer countByShopId(@Param("shopId") Long shopId);
} 