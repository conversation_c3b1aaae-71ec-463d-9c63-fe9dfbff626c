import { createPinia } from 'pinia'
import { useUserStore } from './modules/user'
import { useAppStore } from './modules/app'
import { usePermissionStore } from './modules/permission'
import { useMessageStore } from './modules/message'
import { useRefundStore } from './modules/refund'
import { useQualityInspectionStore } from './modules/qcInspection'
import { useLocalQualityInspectionStore } from './modules/localQcInspection'
import { useLocalRefundStore } from './modules/localRefund'
import { useExportTaskStore } from './modules/exportTask'
import { useViolationStore } from './modules/violation'
import { usePurchaseOrderStore } from './modules/purchaseOrder'
import { useShipOrderStore } from './modules/shipOrder'

const pinia = createPinia()

export {
  useUserStore,
  useAppStore,
  usePermissionStore,
  useMessageStore,
  useRefundStore,
  useQualityInspectionStore,
  useLocalQualityInspectionStore,
  useLocalRefundStore,
  useExportTaskStore,
  useViolationStore,
  usePurchaseOrderStore,
  useShipOrderStore
}

export default pinia 