package com.xiao.temu.infrastructure.log.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * API调用日志实体类
 */
@Data
@TableName("api_call_log")
public class ApiCallLog {
    
    /**
     * 日志ID
     */
    @TableId(value = "log_id", type = IdType.AUTO)
    private Long logId;
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;
    
    /**
     * API名称
     */
    @TableField("api_name")
    private String apiName;
    
    /**
     * 请求URL
     */
    @TableField("request_url")
    private String requestUrl;
    
    /**
     * 请求方法
     */
    @TableField("request_method")
    private String requestMethod;
    
    /**
     * 请求参数
     */
    @TableField("request_param")
    private String requestParam;
    
    /**
     * 响应数据
     */
    @TableField("response_data")
    private String responseData;
    
    /**
     * 状态（0成功 1失败）
     */
    @TableField("status")
    private String status;
    
    /**
     * 错误信息
     */
    @TableField("error_msg")
    private String errorMsg;
    
    /**
     * 耗时(毫秒)
     */
    @TableField("cost_time")
    private Long costTime;
    
    /**
     * IP地址
     */
    @TableField("ip")
    private String ip;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
} 