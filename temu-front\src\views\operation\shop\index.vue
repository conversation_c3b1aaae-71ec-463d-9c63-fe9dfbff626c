<template>
  <div class="app-container">
    <!-- 顶部操作栏 -->
    <div class="toolbar">
      <el-form :model="queryParams" ref="queryRef" :inline="true" class="search-form" size="default">
        <el-form-item label="店铺名称" prop="shopName">
          <el-input
            v-model="queryParams.shopName"
            placeholder="请输入店铺名称"
            clearable
            @keyup.enter="handleQuery"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="Temu店铺ID" prop="shopTemuId">
          <el-input
            v-model="queryParams.shopTemuId"
            placeholder="请输入Temu店铺ID"
            clearable
            @keyup.enter="handleQuery"
            style="width: 220px"
          />
        </el-form-item>
        <el-form-item label="所属运营组" prop="groupId">
          <el-select v-model="queryParams.groupId" placeholder="请选择运营组" clearable style="width: 180px">
            <el-option
              v-for="item in groupOptions"
              :key="item.groupId"
              :label="item.groupName"
              :value="item.groupId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
        <div class="left">
          <el-button
            type="primary"
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['operation:shop:add']"
          >新增</el-button>
          <el-button
            type="danger"
            icon="Delete"
            :disabled="multiple"
            @click="handleBatchDelete"
            v-hasPermi="['operation:shop:remove']"
          >批量删除</el-button>
          <el-button
            type="success"
            icon="Upload"
            @click="handleImport"
            v-hasPermi="['operation:shop:import']"
          >导入</el-button>
          <el-button
            type="warning"
            icon="Download"
            @click="handleExport"
            v-hasPermi="['operation:shop:export']"
          >导出</el-button>
     
      </div>
    </div>

    <!-- 店铺表格 -->
    <el-table
      v-loading="loading"
      :data="shopList"
      style="width: 100%"
      border
      :cell-style="{padding: '8px 0'}"
      :header-cell-style="{
        background: '#f5f7fa',
        color: '#606266',
        height: '45px',
        padding: '0',
        fontWeight: 'bold'
      }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column label="店铺名称" prop="shopName" :show-overflow-tooltip="true" />
      <el-table-column label="Temu店铺ID" prop="shopTemuId" :show-overflow-tooltip="true" />
      <el-table-column label="所属运营组" width="250" :show-overflow-tooltip="true">
        <template #default="scope">
          <div v-if="scope.row.groups && scope.row.groups.length > 0" class="group-tags">
            <el-tag 
              v-for="(group, index) in scope.row.groups" 
              :key="group.groupId" 
              class="group-tag" 
              size="small" 
              :type="getTagTypeByIndex(index)"
              effect="plain"
              style="border-radius: 4px; padding: 0 8px;"
            >
              {{ group.groupName }}
            </el-tag>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" width="80" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'" size="small">
            {{ scope.row.status === '0' ? '正常' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="200" align="center">
        <template #default="scope">
          {{ formatTime(scope.row.createTime || '') }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="300" align="center" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            size="small"
            @click="handleDetail(scope.row)"
            v-hasPermi="['operation:shop:query']"
          >详情</el-button>
          <el-button
            link
            type="primary"
            icon="Edit"
            size="small"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['operation:shop:edit']"
          >编辑</el-button>
          <el-button
            link
            type="danger"
            icon="Delete"
            size="small"
            @click="handleDelete(scope.row)"
            v-hasPermi="['operation:shop:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <el-pagination
      v-if="total > 0"
      v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      background
      class="pagination-container"
    />
    
    <!-- 新增/编辑弹窗 -->
    <ShopForm
      v-model="formDialog"
      :shop-id="formShop.shopId || null"
      :shop-data="formShop"
      @success="getList"
    />
    
    <!-- 详情弹窗 -->
    <ShopDetail
      v-if="detailShop"
      v-model="detailDialog"
      :shop-info="detailShop"
      @edit="handleUpdate(detailShop)"
    />
    
    <!-- 导出选项对话框 -->
    <el-dialog
      title="导出数据"
      v-model="exportDialog.visible"
      width="500px"
      append-to-body
    >
      <el-form>
        <el-form-item label="导出类型">
          <el-radio-group v-model="exportType">
            <el-radio label="all">全部</el-radio>
            <el-radio label="page">当前页</el-radio>
            <el-radio label="selected">选中项</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="文件名">
          <el-input v-model="exportFileName" placeholder="请输入文件名" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="exportDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmExport" :loading="exportLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 导入对话框 -->
    <el-dialog
      v-model="importDialog.visible"
      title="导入店铺"
      width="500px"
    >
      <div class="import-tip">
        <p>请先下载导入模板，按照模板格式填写数据后导入</p>
        <p>导入说明：</p>
        <ul>
          <li>店铺名称、Temu店铺ID为必填项</li>
          <li>API密钥、API密钥Secret、Access Token为选填项</li>
          <li>状态默认为"正常"，可填写"正常"或"禁用"</li>
        </ul>
      </div>
      <el-form label-width="100px">
        <el-form-item label="下载模板">
          <el-button type="primary" @click="downloadTemplate">
            <el-icon><Download /></el-icon> 下载模板
          </el-button>
        </el-form-item>
        <el-form-item label="选择文件">
          <el-upload
            ref="uploadRef"
            action=""
            :auto-upload="false"
            :limit="1"
            :on-exceed="handleExceed"
            :on-change="handleFileChange"
            accept=".xlsx, .xls"
            drag
          >
            <el-icon class="el-icon--upload"><Upload /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                仅支持.xlsx, .xls格式文件
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitImport" :loading="importLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage, ElMessageBox, ElUpload } from 'element-plus'
import { getShopList, getShop, deleteShop, exportShops, getShopImportTemplate, importShops, deleteShops } from '@/api/shop'
import { getGroupList } from '@/api/group'
import type { Shop, ShopQuery } from '@/types/shop'
import ShopForm from './components/ShopForm.vue'
import ShopDetail from './components/ShopDetail.vue'
import { formatTime } from '@/utils/format'
import { exportAll, exportPage, exportSelected } from '@/utils/excel'
import { Upload, Download, Plus } from '@element-plus/icons-vue'

// 查询参数
const queryParams = reactive<ShopQuery>({
  pageNum: 1,
  pageSize: 10,
  shopName: '',
  shopTemuId: '',
  groupId: null,
  status: ''
})

// 表单引用
const queryRef = ref<FormInstance>()

// 状态选项
const statusOptions = [
  { label: '正常', value: '0' },
  { label: '禁用', value: '1' }
]

// 运营组选项
const groupOptions = ref<any[]>([])

// 数据加载状态
const loading = ref(false)

// 店铺列表数据
const shopList = ref<Shop[]>([])

// 总记录数
const total = ref(0)

// 弹窗控制
const formDialog = ref(false)
const detailDialog = ref(false)

// 当前操作的店铺数据
const formShop = reactive<Partial<Shop>>({
  shopId: null
})
const detailShop = ref<Shop | null>(null)

// 选中数据
const ids = ref<number[]>([])
// 非多个禁用
const multiple = computed(() => !ids.value.length)

// 处理表格选择变更
const handleSelectionChange = (selection: any[]) => {
  ids.value = selection.map(item => item.shopId)
}

// 导出相关
const exportDialog = reactive({
  visible: false
})
const exportType = ref('page')
const exportFileName = ref('店铺数据')
const exportLoading = ref(false)

// 导入相关
const importDialog = reactive({
  visible: false
})
const uploadRef = ref<InstanceType<typeof ElUpload>>()
const importFile = ref<File | null>(null)
const importLoading = ref(false)

// 获取店铺列表
const getList = async () => {
  loading.value = true
  try {
    const res: any = await getShopList(queryParams)
    if (res.code === 200 && res.data) {
      shopList.value = res.data.list || []
      total.value = res.data.total || 0
    } else {
      ElMessage.error(res.message || '获取店铺列表失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取店铺列表失败')
  } finally {
    loading.value = false
  }
}

// 获取运营组列表
const loadGroupOptions = async () => {
  try {
    const res: any = await getGroupList({ pageSize: 100, pageNum: 1 })
    if (res.code === 200 && res.data) {
      groupOptions.value = res.data.records || []
    }
  } catch (error) {
    console.error('加载运营组失败', error)
  }
}

// 查询按钮
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置按钮
const resetQuery = () => {
  if (queryRef.value) {
    queryRef.value.resetFields()
  }
  queryParams.pageNum = 1
  getList()
}

// 分页相关方法
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  getList()
}

const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page
  getList()
}

// 新增按钮
const handleAdd = () => {
  // 重置formShop对象
  Object.keys(formShop).forEach(key => {
    // @ts-ignore
    formShop[key] = undefined
  })
  
  // 设置默认值
  formShop.shopId = null
  formShop.status = '0'
  
  // 显示弹窗
  console.log('新增店铺数据:', formShop)
  formDialog.value = true
}

// 编辑按钮
const handleUpdate = (row: Shop) => {
  // 先将formShop重置
  Object.keys(formShop).forEach(key => {
    // @ts-ignore
    formShop[key] = undefined
  })
  
  // 查询最新数据
  if (row.shopId !== null) {
    getShop(Number(row.shopId)).then((res: any) => {
      if (res.code === 200 && res.data) {
        // 直接将整个响应数据赋值给formShop
        Object.assign(formShop, res.data)
        console.log('编辑数据:', formShop) // 添加日志确认数据
        // 确保弹窗在数据加载完成后显示
        formDialog.value = true
      } else {
        ElMessage.error(res.message || '获取店铺详情失败')
      }
    }).catch(error => {
      console.error('获取店铺详情失败:', error)
      ElMessage.error('获取店铺详情失败: ' + (error.response?.data?.message || error.message || '未知错误'))
    })
  }
}

// 查看详情
const handleDetail = (row: Shop) => {
  // 查询最新数据
  getShop(Number(row.shopId)).then((res: any) => {
    if (res.code === 200 && res.data) {
      detailShop.value = res.data
      detailDialog.value = true
    }
  }).catch(error => {
    console.error('获取店铺详情失败:', error)
    ElMessage.error('获取店铺详情失败: ' + (error.response?.data?.message || error.message || '未知错误'))
  })
}

// 删除按钮
const handleDelete = (row: Shop) => {
  ElMessageBox.confirm(`确定要删除【${row.shopName}】店铺吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res: any = await deleteShop(Number(row.shopId))
      if (res.code === 200) {
        ElMessage.success('删除成功')
        getList()
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    } catch (error: any) {
      ElMessage.error(error.message || '删除失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 根据索引获取标签类型
const getTagTypeByIndex = (index: number): string => {
  const types = ['primary', 'success', 'warning', 'danger', 'info', '']
  return types[index % types.length]
}

// 根据索引获取标签颜色
const getTagColorByIndex = (index: number): string => {
  const colors = [
    '#409eff', // 蓝色
    '#67c23a', // 绿色
    '#e6a23c', // 橙色
    '#f56c6c', // 红色
    '#909399', // 灰色
    '#9370db'  // 紫色
  ]
  return colors[index % colors.length]
}

// 导出数据
const handleExport = () => {
  // 显示导出对话框
  exportDialog.visible = true
  exportFileName.value = '店铺数据'
  exportType.value = 'page'
}

// 确认导出
const confirmExport = async () => {
  try {
    // 根据导出类型执行相应的导出操作
    let promise
    const fileName = exportFileName.value || '店铺数据'
    const sheetName = '店铺列表'
    
    if (exportType.value === 'all') {
      promise = exportAll(exportShops, queryParams, fileName, sheetName)
    } else if (exportType.value === 'page') {
      const pageParams = {
        pageNum: queryParams.pageNum || 1,
        pageSize: queryParams.pageSize || 10
      }
      promise = exportPage(exportShops, pageParams, queryParams, fileName, sheetName)
    } else if (exportType.value === 'selected') {
      // 获取选中的行
      const selectedIds = ids.value
      
      if (selectedIds.length === 0) {
        ElMessage.warning('请至少选择一条记录')
        return
      }
      promise = exportSelected(exportShops, selectedIds, fileName, sheetName)
    }
    
    // 导出中...
    exportLoading.value = true
    
    await promise
    ElMessage.success('导出成功')
    exportDialog.visible = false
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('导出失败', error)
  } finally {
    exportLoading.value = false
  }
}

// 导入数据
const handleImport = () => {
  // 重置导入相关数据
  importFile.value = null
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
  importDialog.visible = true
}

// 下载模板
const downloadTemplate = async () => {
  try {
    const res = await getShopImportTemplate()
    const blob = new Blob([res.data], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    
    // 创建临时下载链接
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = '店铺导入模板.xlsx'
    document.body.appendChild(link)
    link.click()
    
    // 清理临时对象
    setTimeout(() => {
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)
    }, 100)
  } catch (error) {
    console.error('下载模板失败', error)
    ElMessage.error('下载模板失败')
  }
}

// 处理文件上传
const handleFileChange = (file: any) => {
  if (file && file.raw) {
    importFile.value = file.raw
  } else {
    importFile.value = null
  }
}

// 处理文件上传超出限制
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 提交导入
const submitImport = async () => {
  if (!importFile.value) {
    ElMessage.warning('请选择要导入的文件')
    return
  }
  
  importLoading.value = true
  try {
    const res: any = await importShops(importFile.value)
    if (res.code === 200) {
      ElMessage.success('导入成功')
      importDialog.visible = false
      getList() // 刷新列表
    } else {
      ElMessage.error(res.message || '导入失败')
    }
  } catch (error: any) {
    console.error('导入失败', error)
    ElMessage.error(error.message || '导入失败')
  } finally {
    importLoading.value = false
  }
}

// 批量删除处理
const handleBatchDelete = () => {
  if (ids.value.length === 0) {
    ElMessage.warning('请至少选择一条记录')
    return
  }
  
  ElMessageBox.confirm(`确认删除选中的${ids.value.length}条店铺记录吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteShops(ids.value).then(() => {
      ElMessage.success('批量删除成功')
      getList()
    })
  }).catch(() => {})
}

onMounted(() => {
  getList()
  loadGroupOptions()
})
</script>

<style scoped>
.toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  background-color: #fff;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.search-form {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.search-form :deep(.el-form-item) {
  margin-right: 12px;
  margin-bottom: 10px;
}

.search-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.search-buttons {
  margin-right: 0;
}

.action-btns {
  margin-left: 12px;
  display: flex;
  align-items: flex-start;
}

.el-pagination {
  margin-top: 16px;
  justify-content: flex-end;
  padding: 12px 0;
}

.pagination-container {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

/* 表格样式优化 */
.el-table {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 0;
}

.el-table :deep(th.el-table__cell) {
  font-weight: 600;
}

.el-table :deep(.el-button--small) {
  padding: 4px 8px;
}

.app-container {
  padding: 16px;
}

/* 运营组标签样式 */
.group-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.group-tag {
  margin-right: 0;
  margin-bottom: 0;
  font-weight: 500;
  transition: all 0.3s;
}

.group-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 导入相关样式 */
.import-tip {
  margin-bottom: 20px;
  padding: 12px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.import-tip p {
  margin: 5px 0;
}

.import-tip ul {
  padding-left: 20px;
  margin: 8px 0;
}

.import-tip li {
  margin-bottom: 5px;
}

/* 拖拽上传区域样式 */
.el-upload-dragger {
  width: 100%;
  height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.el-upload-dragger:hover {
  border-color: #409eff;
}

.el-icon--upload {
  font-size: 67px;
  color: #c0c4cc;
  margin-bottom: 10px;
  line-height: 50px;
}

.el-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.el-upload__text em {
  color: #409eff;
  font-style: normal;
}
</style> 