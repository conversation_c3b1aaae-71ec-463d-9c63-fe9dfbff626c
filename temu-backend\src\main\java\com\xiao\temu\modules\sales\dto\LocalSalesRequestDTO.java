package com.xiao.temu.modules.sales.dto;

import lombok.Data;
import java.util.List;

/**
 * 本地商品销售信息查询请求DTO
 */
@Data
public class LocalSalesRequestDTO {

    /**
     * 页码
     */
    private Integer pageNo = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 商品类目
     */
    private String category;

    /**
     * SPU ID列表
     */
    private List<Long> spuIdList;

    /**
     * SPU ID列表（字符串格式，如"1,2,3"）
     */
    private String spuIdListStr;

    /**
     * SKC ID列表
     */
    private List<Long> skcIdList;

    /**
     * SKC ID列表（字符串格式，如"1,2,3"）
     */
    private String skcIdListStr;

    /**
     * 是否VMI
     */
    private Boolean isVmi;

    /**
     * 备货区域
     */
    private Integer inventoryRegion;

    /**
     * 加入站点时长（天）
     */
    private Integer onSitesDuration;

    /**
     * SKC型号/编码
     */
    private String skcExtCode;

    /**
     * SKU规格号
     */
    private String skuExtCode;

    /**
     * 商品名称关键词
     */
    private String productNameKeyword;

    /**
     * 是否定制商品
     */
    private Boolean isCustomGoods;

    /**
     * 是否自动关闭JIT
     */
    private Boolean autoCloseJit;

    /**
     * 店铺ID列表
     */
    private List<Long> shopIds;

    /**
     * 是否忽略权限检查
     * 为true时,超级管理员或拥有全部数据权限的用户可以查看所有数据
     */
    private Boolean ignorePermissionCheck = false;

    /**
     * JIT状态
     */
    private List<Integer> closeJitStatus;

    /**
     * 库存状态（1-库存充足，2-库存紧张，3-库存不足，4-缺货）
     */
    private Integer stockStatus;

    /**
     * 是否热销款
     */
    private Boolean hasHotSku;

    /**
     * 库存天数最小值
     */
    private Integer stockDaysMin;

    /**
     * 库存天数最大值
     */
    private Integer stockDaysMax;

    /**
     * 备货仓组ID
     */
    private Long warehouseGroupId;

    /**
     * 商品评分
     */
    private Integer productScore;

    /**
     * 今日销量最小值
     */
    private Integer todaySaleVolumMin;

    /**
     * 今日销量最大值
     */
    private Integer todaySaleVolumMax;

    /**
     * 近7天销量最小值
     */
    private Integer lastSevenDaysSaleVolumeMin;

    /**
     * 近7天销量最大值
     */
    private Integer lastSevenDaysSaleVolumeMax;

    /**
     * 近30天销量最小值
     */
    private Integer lastThirtyDaysSaleVolumeMin;

    /**
     * 近30天销量最大值
     */
    private Integer lastThirtyDaysSaleVolumeMax;

    /**
     * 销量查询的时间范围类型: 1-今日, 2-近7天, 3-近30天
     */
    private Integer saleVolumeTimeRange;

    /**
     * 快速筛选类型
     */
    private String quickFilter;
    
    /**
     * 查询开始时间
     */
    private Long startTime;
    
    /**
     * 查询结束时间
     */
    private Long endTime;
    
    /**
     * 商品状态列表
     */
    private List<Integer> selectStatusList;
    
    /**
     * 商品名称
     */
    private String productName;
    
    /**
     * SKC货号列表
     */
    private String skcExtCodeList;
    
    /**
     * SKU货号列表
     */
    private String skuExtCodeList;
    
    /**
     * 最小加入站点时长
     */
    private Integer onSalesDurationOfflineGte;
    
    /**
     * 最大加入站点时长
     */
    private Integer onSalesDurationOfflineLte;
    
    /**
     * 库存区域列表
     */
    private List<Integer> inventoryRegionList;
    
    /**
     * 最小剩余库存数量
     */
    private Integer minRemanentInventoryNum;
    
    /**
     * 最大剩余库存数量
     */
    private Integer maxRemanentInventoryNum;
    
    /**
     * 最小可售天数
     */
    private Integer minAvailableSaleDays;
    
    /**
     * 最大可售天数
     */
    private Integer maxAvailableSaleDays;
    
    /**
     * 图片审核状态列表
     */
    private List<Integer> pictureAuditStatusList;
    
    /**
     * 供货状态列表
     */
    private List<Integer> supplyStatusList;
    
    /**
     * 是否缺货
     */
    private Boolean isLack;
    
    /**
     * 热销标签
     */
    private Integer hotTag;
    
    /**
     * 备货类型
     */
    private Integer purchaseStockType;
    
    /**
     * 建议关闭JIT
     */
    private Boolean suggestCloseJit;
    
    /**
     * 库存状态列表
     */
    private List<Integer> stockStatusList;
    
    /**
     * 可生产数量大于零
     */
    private Boolean availableProduceNumGreaterThanzero;
    
    /**
     * 结算类型
     */
    private Integer settlementType;
    
    /**
     * SKU ID列表
     */
    private List<String> skuIdList;
    
    /**
     * 销量排序字段: 1-今日销量, 2-近7天销量, 3-近30天销量
     */
    private Integer sortField;
    
    /**
     * 排序方向: asc-升序, desc-降序
     */
    private String sortDirection;
}