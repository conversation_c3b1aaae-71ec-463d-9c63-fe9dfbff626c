package com.xiao.temu.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiao.temu.modules.system.entity.SysDataPermission;
import com.xiao.temu.modules.system.dto.SysDataPermissionDTO;
import com.xiao.temu.modules.system.mapper.SysDataPermissionMapper;
import com.xiao.temu.modules.system.service.SysDataPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Date;

/**
 * 数据权限服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysDataPermissionServiceImpl extends ServiceImpl<SysDataPermissionMapper, SysDataPermission> implements SysDataPermissionService {

    private final SysDataPermissionMapper dataPermissionMapper;

    @Override
    public SysDataPermissionDTO getByRoleId(Long roleId) {
        return dataPermissionMapper.selectByRoleId(roleId);
    }

    @Override
    public List<SysDataPermissionDTO> listAll() {
        // 查询所有角色数据权限
        LambdaQueryWrapper<SysDataPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(SysDataPermission::getRoleId);
        List<SysDataPermission> permissions = this.list(wrapper);
        
        // 转换为DTO并返回
        return permissions.stream()
                .map(this::convertToDTO)
                .toList();
    }

    @Override
    public List<SysDataPermissionDTO> getByUserId(Long userId) {
        return dataPermissionMapper.selectByUserId(userId);
    }

    @Override
    @Transactional
    public int setDataPermission(SysDataPermissionDTO dataPermission) {
        // 查询是否已存在
        SysDataPermissionDTO existPermission = getByRoleId(dataPermission.getRoleId());
        
        if (existPermission != null) {
            // 更新现有权限
            return dataPermissionMapper.updateByRoleId(
                dataPermission.getRoleId(), 
                dataPermission.getPermissionType()
            );
        } else {
            // 新增权限
            SysDataPermission permission = new SysDataPermission();
            permission.setRoleId(dataPermission.getRoleId());
            permission.setPermissionType(dataPermission.getPermissionType());
            permission.setCreateTime(new Date());
            permission.setUpdateTime(new Date());
            permission.setRemark(dataPermission.getRemark());
            
            return this.save(permission) ? 1 : 0;
        }
    }

    @Override
    public String getUserMaxDataPermission(Long userId) {
        // 获取用户所有角色的数据权限
        List<SysDataPermissionDTO> permissions = getByUserId(userId);
        
        if (permissions == null || permissions.isEmpty()) {
            // 默认为本组数据权限
            return "1";
        }
        
        // 获取最高级别的数据权限（数字越大权限越高：0-本人, 1-本组, 2-全部）
        return permissions.stream()
                .map(SysDataPermissionDTO::getPermissionType)
                .max(Comparator.comparing(Integer::valueOf))
                .orElse("1");
    }
    
    /**
     * 将实体转换为DTO
     */
    private SysDataPermissionDTO convertToDTO(SysDataPermission permission) {
        if (permission == null) {
            return null;
        }
        
        SysDataPermissionDTO dto = new SysDataPermissionDTO();
        dto.setId(permission.getId());
        dto.setRoleId(permission.getRoleId());
        dto.setPermissionType(permission.getPermissionType());
        dto.setRemark(permission.getRemark());
        
        return dto;
    }
} 