package com.xiao.temu.modules.sales.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 销售统计数据实体类
 */
@Data
@TableName("t_sales_statistics")
public class SalesStatistics implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺名称
     */
    private String shopName;
    
    /**
     * 统计日期
     */
    private LocalDate statisticsDate;
    
    /**
     * 今日销量
     */
    private Integer todaySales;
    
    /**
     * 近7天销量
     */
    private Integer lastWeekSales;
    
    /**
     * 近30天销量
     */
    private Integer lastMonthSales;
    
    /**
     * 商品总数
     */
    private Integer totalProducts;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 