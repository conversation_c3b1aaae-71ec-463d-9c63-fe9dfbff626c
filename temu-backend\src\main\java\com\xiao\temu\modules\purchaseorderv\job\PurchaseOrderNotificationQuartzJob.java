package com.xiao.temu.modules.purchaseorderv.job;

import com.xiao.temu.modules.purchaseorderv.service.PurchaseOrderNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

/**
 * 备货单通知Quartz定时任务
 */
@Slf4j
@Component
public class PurchaseOrderNotificationQuartzJob extends QuartzJobBean {

    @Autowired
    private PurchaseOrderNotificationService purchaseOrderNotificationService;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        String jobName = context.getJobDetail().getKey().getName();
        log.info("开始执行备货单通知定时任务: {}", jobName);
        try {
            // 根据任务名称执行对应的通知处理
            switch (jobName) {
                case "jitSoonOverdueJob":
                    purchaseOrderNotificationService.processJitSoonOverdueNotification();
                    break;
                case "jitOverdueJob":
                    purchaseOrderNotificationService.processJitOverdueNotification();
                    break;
                case "normalNotDeliveredJob":
                    purchaseOrderNotificationService.processNormalNotDeliveredNotification();
                    break;
                case "normalNotReceivedJob":
                    purchaseOrderNotificationService.processNormalNotReceivedNotification();
                    break;
                default:
                    log.warn("未知的备货单通知任务: {}", jobName);
                    break;
            }
        } catch (Exception e) {
            log.error("执行备货单通知任务出错，任务名: {}，错误: {}", jobName, e.getMessage(), e);
            throw new JobExecutionException(e);
        }
    }
} 