package com.xiao.temu.common.constant;

/**
 * 消息常量
 */
public class MessageConstants {

    /**
     * 消息类型：系统消息
     */
    public static final String MESSAGE_TYPE_SYSTEM = "1";

    /**
     * 消息类型：任务提醒
     */
    public static final String MESSAGE_TYPE_TASK = "2";

    /**
     * 消息类型：店铺消息
     */
    public static final String MESSAGE_TYPE_SHOP = "3";

    /**
     * 接收对象类型：全部用户
     */
    public static final String TARGET_TYPE_ALL = "0";

    /**
     * 接收对象类型：指定用户
     */
    public static final String TARGET_TYPE_USER = "1";

    /**
     * 接收对象类型：指定角色
     */
    public static final String TARGET_TYPE_ROLE = "2";

    /**
     * 接收对象类型：运营组
     */
    public static final String TARGET_TYPE_GROUP = "3";

    /**
     * 重要程度：普通
     */
    public static final String IMPORTANCE_NORMAL = "1";

    /**
     * 重要程度：重要
     */
    public static final String IMPORTANCE_IMPORTANT = "2";

    /**
     * 重要程度：紧急
     */
    public static final String IMPORTANCE_URGENT = "3";

    /**
     * 已读状态：未读
     */
    public static final String READ_STATUS_UNREAD = "0";

    /**
     * 已读状态：已读
     */
    public static final String READ_STATUS_READ = "1";

    /**
     * 删除状态：未删除
     */
    public static final String DELETED_NO = "0";

    /**
     * 删除状态：已删除
     */
    public static final String DELETED_YES = "1";
    
    /**
     * WebSocket主题：接收系统消息
     */
    public static final String WS_TOPIC_MESSAGE = "/topic/message";
    
    /**
     * WebSocket私人目的地：接收个人消息
     */
    public static final String WS_USER_DESTINATION = "/user/%s/queue/message";
} 