/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : temu_api

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 06/06/2025 16:19:29
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for production_progress
-- ----------------------------
DROP TABLE IF EXISTS `production_progress`;
CREATE TABLE `production_progress`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `sub_purchase_order_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '备货单号',
  `cutting_status` tinyint(1) NULL DEFAULT 0 COMMENT '烧花/剪图状态(0未完成 1已完成)',
  `cutting_time` datetime NULL DEFAULT NULL COMMENT '烧花/剪图完成时间',
  `cutting_operator_id` bigint NULL DEFAULT NULL COMMENT '烧花/剪图操作人ID',
  `workshop_status` tinyint(1) NULL DEFAULT 0 COMMENT '车间/拣货状态(0未完成 1已完成)',
  `workshop_time` datetime NULL DEFAULT NULL COMMENT '车间/拣货完成时间',
  `workshop_operator_id` bigint NULL DEFAULT NULL COMMENT '车间/拣货操作人ID',
  `trimming_status` tinyint(1) NULL DEFAULT 0 COMMENT '剪线/压图状态(0未完成 1已完成)',
  `trimming_time` datetime NULL DEFAULT NULL COMMENT '剪线/压图完成时间',
  `trimming_operator_id` bigint NULL DEFAULT NULL COMMENT '剪线/压图操作人ID',
  `inspection_status` tinyint(1) NULL DEFAULT 0 COMMENT '查货状态(0未完成 1已完成)',
  `inspection_time` datetime NULL DEFAULT NULL COMMENT '查货完成时间',
  `inspection_operator_id` bigint NULL DEFAULT NULL COMMENT '查货操作人ID',
  `shipping_status` tinyint(1) NULL DEFAULT 0 COMMENT '发货状态(0未完成 1已完成)',
  `shipping_time` datetime NULL DEFAULT NULL COMMENT '发货完成时间',
  `shipping_operator_id` bigint NULL DEFAULT NULL COMMENT '发货操作人ID',
  `packaging_status` tinyint(1) NULL DEFAULT 0 COMMENT '包装状态(0未完成 1已完成)',
  `packaging_time` datetime NULL DEFAULT NULL COMMENT '包装完成时间',
  `packaging_operator_id` bigint NULL DEFAULT NULL COMMENT '包装操作人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_order_sn`(`shop_id` ASC, `sub_purchase_order_sn` ASC) USING BTREE,
  INDEX `idx_sub_purchase_order_sn`(`sub_purchase_order_sn` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10119 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '生产进度表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
