package com.xiao.temu.modules.operation.dto;

import lombok.Data;

/**
 * 运营组查询条件数据传输对象
 */
@Data
public class QueryGroupDTO {

    /**
     * 运营组名称（模糊查询）
     */
    private String groupName;

    /**
     * 负责人ID
     */
    private Long leaderId;

    /**
     * 负责人姓名（模糊查询）
     */
    private String leaderName;

    /**
     * 状态（0正常 1禁用）
     */
    private String status;

    /**
     * 成员ID（查询该成员所在的所有运营组）
     */
    private Long memberId;

    /**
     * 当前页码
     */
    private Integer pageNum = 1;

    /**
     * 每页记录数
     */
    private Integer pageSize = 10;
} 