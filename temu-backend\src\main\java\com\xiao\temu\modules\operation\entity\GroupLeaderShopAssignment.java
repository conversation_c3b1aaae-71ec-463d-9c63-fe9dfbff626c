package com.xiao.temu.modules.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 组长店铺分配实体类
 */
@Data
@TableName("group_leader_shop_assignment")
public class GroupLeaderShopAssignment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 运营组ID
     */
    @TableField("group_id")
    private Long groupId;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 被分配用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 权限类型(0只读1读写)
     */
    @TableField("permission_type")
    private String permissionType;

    /**
     * 分配时间
     */
    @TableField("assign_time")
    private Date assignTime;

    /**
     * 分配人ID(组长)
     */
    @TableField("assign_by")
    private Long assignBy;

    /**
     * 状态(0正常1禁用)
     */
    @TableField("status")
    private String status;
} 