package com.xiao.temu.modules.purchaseorderv.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.modules.purchaseorderv.dto.ProductionProgressDTO;
import com.xiao.temu.modules.purchaseorderv.dto.ScanUpdateProgressDTO;
import com.xiao.temu.modules.purchaseorderv.service.ProductionProgressService;
import com.xiao.temu.modules.purchaseorderv.vo.ProductionProgressLogVO;
import com.xiao.temu.modules.purchaseorderv.vo.ProductionProgressVO;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.security.model.UserDetailsImpl;
import com.xiao.temu.security.utils.SecurityUtils;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 生产进度控制器
 */
@Slf4j
@RestController
@RequestMapping("/v1/production/progress")
public class ProductionProgressController {

    @Autowired
    private ProductionProgressService productionProgressService;

    /**
     * 获取当前登录用户信息
     */
    private Map<String, Object> getCurrentUser() {
        Long userId = SecurityUtils.getCurrentUserId();
        String username = SecurityUtils.getCurrentUsername();
        
        UserDetails userDetails = SecurityUtils.getCurrentUserDetails();
        String nickName = null;
        
        if (userDetails instanceof UserDetailsImpl) {
            nickName = ((UserDetailsImpl) userDetails).getNickName();
        } else {
            nickName = username; // 降级处理，使用用户名作为昵称
        }
        
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("userId", userId);
        userInfo.put("username", username);
        userInfo.put("nickName", nickName);
        
        return userInfo;
    }

    /**
     * 获取当前用户的操作日志记录
     *
     * @param limit 限制返回的记录数，默认20条
     * @return 用户操作日志列表
     */
    @GetMapping("/user-logs")
    public ApiResponse<List<ProductionProgressLogVO>> getUserOperationLogs(
            @RequestParam(required = false, defaultValue = "20") Integer limit) {
        Long userId = SecurityUtils.getCurrentUserId();
        log.info("获取用户[{}]的操作日志，限制{}条", userId, limit);
        
        List<ProductionProgressLogVO> logs = productionProgressService.getUserOperationLogs(userId, limit);
        return ApiResponse.success(logs);
    }

    /**
     * 获取生产进度详情
     *
     * @param shopId 店铺ID
     * @param subPurchaseOrderSn 备货单号
     * @return 生产进度详情
     */
    @GetMapping("/detail")
    public ApiResponse<ProductionProgressVO> getProgressDetail(
            @RequestParam Long shopId,
            @RequestParam String subPurchaseOrderSn) {
        ProductionProgressVO progressDetail = productionProgressService.getProgressDetail(shopId, subPurchaseOrderSn);
        return ApiResponse.success(progressDetail);
    }

    /**
     * 批量获取生产进度详情
     *
     * @param shopId 店铺ID
     * @param subPurchaseOrderSnList 备货单号列表
     * @return 生产进度详情列表，键为备货单号，值为对应的生产进度详情
     */
    @PostMapping("/batch-detail")
    public ApiResponse<Map<String, ProductionProgressVO>> batchGetProgressDetail(
            @RequestParam Long shopId,
            @RequestBody List<String> subPurchaseOrderSnList) {
        if (subPurchaseOrderSnList == null || subPurchaseOrderSnList.isEmpty()) {
            return ApiResponse.error("备货单号列表不能为空");
        }
        
        Map<String, ProductionProgressVO> result = new HashMap<>();
        for (String sn : subPurchaseOrderSnList) {
            try {
                ProductionProgressVO progressDetail = productionProgressService.getProgressDetail(shopId, sn);
                result.put(sn, progressDetail);
            } catch (Exception e) {
                log.error("获取备货单{}的生产进度失败", sn, e);
                // 继续处理下一个，不中断整个批处理
            }
        }
        
        return ApiResponse.success(result);
    }

    /**
     * 获取生产进度操作日志
     *
     * @param shopId 店铺ID
     * @param subPurchaseOrderSn 备货单号
     * @return 生产进度操作日志列表
     */
    @GetMapping("/logs")
    public ApiResponse<List<ProductionProgressLogVO>> getProgressLogs(
            @RequestParam Long shopId,
            @RequestParam String subPurchaseOrderSn) {
        List<ProductionProgressLogVO> logs = productionProgressService.getProgressLogs(shopId, subPurchaseOrderSn);
        return ApiResponse.success(logs);
    }

    /**
     * 分页查询生产进度
     *
     * @param dto 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    public ApiResponse<IPage<ProductionProgressVO>> getProgressPage(@RequestBody ProductionProgressDTO dto) {
        IPage<ProductionProgressVO> page = productionProgressService.getProgressPage(dto);
        return ApiResponse.success(page);
    }

    /**
     * 扫码更新生产进度
     *
     * @param dto 更新参数
     * @return 更新结果
     */
    @PostMapping("/update")
    public ApiResponse<Boolean> updateProgress(@RequestBody @Valid ScanUpdateProgressDTO dto) {
        Map<String, Object> currentUser = getCurrentUser();
        Long userId = (Long) currentUser.get("userId");
        String nickName = (String) currentUser.get("nickName");
        
        boolean result = productionProgressService.updateProgress(
                dto, userId, nickName);
        
        if (result) {
            return ApiResponse.success(true);
        } else {
            return ApiResponse.error("更新生产进度失败");
        }
    }

    /**
     * 撤销生产进度（管理员功能）
     *
     * @param dto 撤销参数
     * @return 撤销结果
     */
    @PostMapping("/cancel")
    public ApiResponse<Boolean> cancelProgress(@RequestBody @Valid ScanUpdateProgressDTO dto) {
        Map<String, Object> currentUser = getCurrentUser();
        Long userId = (Long) currentUser.get("userId");
        String nickName = (String) currentUser.get("nickName");
        
        boolean result = productionProgressService.cancelProgress(
                dto, userId, nickName);
        
        if (result) {
            return ApiResponse.success(true);
        } else {
            return ApiResponse.error("撤销生产进度失败");
        }
    }
} 