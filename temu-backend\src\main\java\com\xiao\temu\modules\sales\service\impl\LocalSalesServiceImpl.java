package com.xiao.temu.modules.sales.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.sales.entity.SalesInventoryInfo;
import com.xiao.temu.modules.sales.entity.SalesSkuQuantity;
import com.xiao.temu.modules.sales.entity.SalesSubOrder;
import com.xiao.temu.modules.sales.entity.SalesWarehouseInfo;
import com.xiao.temu.modules.sales.mapper.SalesInventoryInfoMapper;
import com.xiao.temu.modules.sales.mapper.SalesSkuQuantityMapper;
import com.xiao.temu.modules.sales.mapper.SalesSubOrderMapper;
import com.xiao.temu.modules.sales.mapper.SalesWarehouseInfoMapper;
import com.xiao.temu.modules.sales.vo.*;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.sales.dto.LocalSalesRequestDTO;
import com.xiao.temu.modules.shop.mapper.ShopMapper;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.sales.service.LocalSalesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 本地商品销售信息服务实现
 */
@Service
@Slf4j
public class LocalSalesServiceImpl implements LocalSalesService {

    @Autowired
    private SalesSubOrderMapper salesSubOrderMapper;
    
    @Autowired
    private SalesSkuQuantityMapper salesSkuQuantityMapper;
    
    @Autowired
    private SalesInventoryInfoMapper salesInventoryInfoMapper;
    
    @Autowired
    private ShopMapper shopMapper;
    
    @Autowired
    private ShopService shopService;

    @Autowired
    private SalesWarehouseInfoMapper salesWarehouseInfoMapper;

    /**
     * 获取指定店铺下的备货仓组列表
     * 
     * @param shopId 店铺ID
     * @return 备货仓组列表
     */
    @Override
    public List<WarehouseGroupVO> getWarehouseGroups(Long shopId) {
        log.info("获取店铺 {} 的备货仓组列表", shopId);
        
        if (shopId == null) {
            return Collections.emptyList();
        }
        
        try {
            // 查询该店铺下所有有效的仓库组
            LambdaQueryWrapper<SalesWarehouseInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SalesWarehouseInfo::getShopId, shopId);
            queryWrapper.select(
                SalesWarehouseInfo::getWarehouseGroupId, 
                SalesWarehouseInfo::getWarehouseGroupName,
                SalesWarehouseInfo::getShopId
            );
            queryWrapper.groupBy(
                SalesWarehouseInfo::getWarehouseGroupId, 
                SalesWarehouseInfo::getWarehouseGroupName,
                SalesWarehouseInfo::getShopId
            );
            
            // 执行查询
            List<SalesWarehouseInfo> warehouseInfos = salesWarehouseInfoMapper.selectList(queryWrapper);
            
            // 转换为VO对象
            List<WarehouseGroupVO> allGroups = warehouseInfos.stream().map(warehouse -> {
                WarehouseGroupVO vo = new WarehouseGroupVO();
                vo.setWarehouseGroupId(warehouse.getWarehouseGroupId());
                vo.setWarehouseGroupName(warehouse.getWarehouseGroupName());
                vo.setShopId(warehouse.getShopId());
                return vo;
            }).collect(Collectors.toList());
            
            // 按仓组ID去重，保留同ID的第一个仓组记录
            Map<Long, WarehouseGroupVO> uniqueGroups = new LinkedHashMap<>();
            for (WarehouseGroupVO group : allGroups) {
                // 如果该仓组ID还未添加到map中，则添加
                uniqueGroups.putIfAbsent(group.getWarehouseGroupId(), group);
            }
            
            log.info("去重前仓库组数量: {}, 去重后数量: {}", allGroups.size(), uniqueGroups.size());
            
            // 返回去重后的仓组列表
            return new ArrayList<>(uniqueGroups.values());
        } catch (Exception e) {
            log.error("获取店铺 {} 的备货仓组列表失败", shopId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取多个店铺下的备货仓组列表
     * 
     * @param shopIds 店铺ID列表
     * @return 备货仓组列表
     */
    @Override
    public List<WarehouseGroupVO> getWarehouseGroupsByShopIds(List<Long> shopIds) {
        log.info("获取多个店铺的备货仓组列表: {}", shopIds);
        
        if (shopIds == null || shopIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        try {
            // 查询多个店铺下所有有效的仓库组
            LambdaQueryWrapper<SalesWarehouseInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(SalesWarehouseInfo::getShopId, shopIds);
            queryWrapper.select(
                SalesWarehouseInfo::getWarehouseGroupId, 
                SalesWarehouseInfo::getWarehouseGroupName,
                SalesWarehouseInfo::getShopId
            );
            queryWrapper.groupBy(
                SalesWarehouseInfo::getWarehouseGroupId, 
                SalesWarehouseInfo::getWarehouseGroupName,
                SalesWarehouseInfo::getShopId
            );
            
            // 执行查询
            List<SalesWarehouseInfo> warehouseInfos = salesWarehouseInfoMapper.selectList(queryWrapper);
            
            // 转换为VO对象
            List<WarehouseGroupVO> allGroups = warehouseInfos.stream().map(warehouse -> {
                WarehouseGroupVO vo = new WarehouseGroupVO();
                vo.setWarehouseGroupId(warehouse.getWarehouseGroupId());
                vo.setWarehouseGroupName(warehouse.getWarehouseGroupName());
                vo.setShopId(warehouse.getShopId());
                return vo;
            }).collect(Collectors.toList());
            
            // 按仓组ID去重，保留同ID的第一个仓组记录
            Map<Long, WarehouseGroupVO> uniqueGroups = new LinkedHashMap<>();
            for (WarehouseGroupVO group : allGroups) {
                // 如果该仓组ID还未添加到map中，则添加
                uniqueGroups.putIfAbsent(group.getWarehouseGroupId(), group);
            }
            
            log.info("去重前仓库组数量: {}, 去重后数量: {}", allGroups.size(), uniqueGroups.size());
            
            // 返回去重后的仓组列表
            return new ArrayList<>(uniqueGroups.values());
        } catch (Exception e) {
            log.error("获取多个店铺的备货仓组列表失败: {}", shopIds, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取本地商品销售信息列表
     *
     * @param requestDTO 请求参数
     * @param userId 当前用户ID
     * @return 商品销售信息VO
     */
    @Override
    public LocalSalesVO getLocalSalesList(LocalSalesRequestDTO requestDTO, Long userId) {
        // 处理分页参数
        Integer pageNo = requestDTO.getPageNo() != null ? requestDTO.getPageNo() : 1;
        Integer pageSize = requestDTO.getPageSize() != null ? requestDTO.getPageSize() : 10;
        
        // 如果不忽略权限检查，获取用户可访问的店铺列表
        List<Long> userShopIds = new ArrayList<>();
        if (!Boolean.TRUE.equals(requestDTO.getIgnorePermissionCheck())) {
            List<Shop> userShops = shopService.listUserShops(userId);
            userShopIds = userShops.stream().map(Shop::getShopId).collect(Collectors.toList());
            
            // 如果用户没有店铺权限，直接返回空结果
            if (userShopIds.isEmpty()) {
                return new LocalSalesVO(Collections.emptyList(), 0L, pageNo, pageSize);
            }
        }
        
        // 判断是否有排序参数
        Integer sortField = requestDTO.getSortField();
        String sortDirection = requestDTO.getSortDirection();
        boolean hasSortRequest = sortField != null && StringUtils.hasText(sortDirection);
        
        // 如果有排序参数，使用基于SKU排序的查询逻辑
        if (hasSortRequest) {
            log.info("使用SKU排序查询逻辑，字段: {}, 方向: {}", sortField, sortDirection);
            return getLocalSalesListWithSkuSorting(requestDTO, userId, userShopIds, pageNo, pageSize);
        } else {
            // 无排序参数，使用原有查询逻辑
            log.info("使用原有查询逻辑（无排序）");
            return getLocalSalesListOriginalLogic(requestDTO, userId, userShopIds, pageNo, pageSize);
        }
    }
    
    /**
     * 使用基于SKU排序的查询逻辑获取销售信息
     */
    private LocalSalesVO getLocalSalesListWithSkuSorting(LocalSalesRequestDTO requestDTO, Long userId, 
                                                        List<Long> userShopIds, Integer pageNo, Integer pageSize) {
        // 1. 首先查询符合条件的SKU并按销量排序
        LambdaQueryWrapper<SalesSkuQuantity> skuWrapper = buildSkuQueryWrapper(requestDTO, userShopIds);
        
        // 2. 添加销量排序条件
        Integer sortField = requestDTO.getSortField();
        String sortDirection = requestDTO.getSortDirection();
        boolean isAsc = "asc".equalsIgnoreCase(sortDirection);
        
        // 根据sortField确定要排序的字段
        switch (sortField) {
            case 1: // 今日销量
                if (isAsc) {
                    skuWrapper.orderByAsc(SalesSkuQuantity::getTodaySaleVolume);
                } else {
                    skuWrapper.orderByDesc(SalesSkuQuantity::getTodaySaleVolume);
                }
                break;
            case 2: // 近7天销量
                if (isAsc) {
                    skuWrapper.orderByAsc(SalesSkuQuantity::getLastSevenDaysSaleVolume);
                } else {
                    skuWrapper.orderByDesc(SalesSkuQuantity::getLastSevenDaysSaleVolume);
                }
                break;
            case 3: // 近30天销量
                if (isAsc) {
                    skuWrapper.orderByAsc(SalesSkuQuantity::getLastThirtyDaysSaleVolume);
                } else {
                    skuWrapper.orderByDesc(SalesSkuQuantity::getLastThirtyDaysSaleVolume);
                }
                break;
            default:
                log.warn("未知的排序字段: {}", sortField);
        }
        
        // 3. 查询符合条件的SKU（不分页）
        List<SalesSkuQuantity> allSkuList = salesSkuQuantityMapper.selectList(skuWrapper);
        log.info("按销量排序查询到的SKU数量: {}", allSkuList.size());
        
        if (allSkuList.isEmpty()) {
            return new LocalSalesVO(Collections.emptyList(), 0L, pageNo, pageSize);
        }
        
        // 4. 获取这些SKU对应的唯一SKC ID列表
        Set<Long> allSkcIds = allSkuList.stream()
            .map(SalesSkuQuantity::getProductSkcId)
            .collect(Collectors.toSet());
        
        log.info("排序后SKU对应的唯一SKC数量: {}", allSkcIds.size());
        
        // 5. 根据排序后的SKU顺序对SKC进行排序
        // 创建映射：SKC ID -> 该SKC下的最高排序SKU的索引
        Map<Long, Integer> skcToRankMap = new HashMap<>();
        for (int i = 0; i < allSkuList.size(); i++) {
            SalesSkuQuantity sku = allSkuList.get(i);
            Long skcId = sku.getProductSkcId();
            // 只保存第一次出现（即最高排序）的索引
            skcToRankMap.putIfAbsent(skcId, i);
        }
        
        // 6. 查询这些SKC的基本信息
        LambdaQueryWrapper<SalesSubOrder> skcWrapper = new LambdaQueryWrapper<>();
        skcWrapper.in(SalesSubOrder::getProductSkcId, allSkcIds);
        // 添加其他SKC层级的过滤条件
        applySKCFilters(skcWrapper, requestDTO, userShopIds);
        
        List<SalesSubOrder> skcOrders = salesSubOrderMapper.selectList(skcWrapper);
        log.info("找到的SKC记录数量: {}", skcOrders.size());
        
        // 如果没有找到SKC记录，返回空结果
        if (skcOrders.isEmpty()) {
            return new LocalSalesVO(Collections.emptyList(), 0L, pageNo, pageSize);
        }
        
        // 7. 根据SKU的排序对SKC进行排序
        skcOrders.sort(Comparator.comparing(order -> 
            skcToRankMap.getOrDefault(order.getProductSkcId(), Integer.MAX_VALUE)));
        
        // 8. 计算分页
        int totalItems = skcOrders.size();
        int fromIndex = (pageNo - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, totalItems);
        
        // 防止索引越界
        if (fromIndex >= totalItems) {
            return new LocalSalesVO(Collections.emptyList(), (long) totalItems, pageNo, pageSize);
        }
        
        // 分页获取SKC
        List<SalesSubOrder> pagedSkcOrders = skcOrders.subList(fromIndex, toIndex);
        
        // 9. 获取这些分页后的SKC记录对应的SKU、库存和仓库信息
        return buildLocalSalesVO(pagedSkcOrders, requestDTO, (long) totalItems, pageNo, pageSize);
    }
    
    /**
     * 原始逻辑查询销售信息（当没有排序参数时使用）
     */
    private LocalSalesVO getLocalSalesListOriginalLogic(LocalSalesRequestDTO requestDTO, Long userId, 
                                                       List<Long> userShopIds, Integer pageNo, Integer pageSize) {
        // 判断是否有可售天数过滤条件
        boolean hasAvailableSaleDaysFilter = requestDTO.getMinAvailableSaleDays() != null || requestDTO.getMaxAvailableSaleDays() != null;
        
        // 用于存储符合条件的SKC ID集合
        Set<Long> filteredSkcIds = new HashSet<>();
        
        // 如果有可售天数过滤条件，先查询符合条件的SKC ID
        if (hasAvailableSaleDaysFilter) {
            log.info("检测到可售天数过滤条件，先查询符合条件的SKC ID");
            
            // 构建查询条件：先查找符合可售天数条件的仓库信息
            LambdaQueryWrapper<SalesWarehouseInfo> warehouseFilter = new LambdaQueryWrapper<>();
            
            // 店铺过滤
            if (!Boolean.TRUE.equals(requestDTO.getIgnorePermissionCheck()) && !userShopIds.isEmpty()) {
                warehouseFilter.in(SalesWarehouseInfo::getShopId, userShopIds);
            }
            
            // 请求中的店铺过滤
            if (!CollectionUtils.isEmpty(requestDTO.getShopIds())) {
                warehouseFilter.in(SalesWarehouseInfo::getShopId, requestDTO.getShopIds());
            }
            
            // 可售天数过滤条件
            if (requestDTO.getMinAvailableSaleDays() != null) {
                warehouseFilter.ge(SalesWarehouseInfo::getAvailableSaleDays, requestDTO.getMinAvailableSaleDays());
                log.info("应用最小可售天数过滤: >= {}", requestDTO.getMinAvailableSaleDays());
            }
            
            if (requestDTO.getMaxAvailableSaleDays() != null) {
                warehouseFilter.le(SalesWarehouseInfo::getAvailableSaleDays, requestDTO.getMaxAvailableSaleDays());
                log.info("应用最大可售天数过滤: <= {}", requestDTO.getMaxAvailableSaleDays());
            }
            
            // 执行仓库信息查询
            List<SalesWarehouseInfo> filteredWarehouseInfos = salesWarehouseInfoMapper.selectList(warehouseFilter);
            log.info("符合可售天数条件的仓库信息数量: {}", filteredWarehouseInfos.size());
            
            // 如果没有符合条件的仓库信息，直接返回空结果
            if (filteredWarehouseInfos.isEmpty()) {
                log.info("没有符合可售天数条件的数据，返回空结果");
                return new LocalSalesVO(Collections.emptyList(), 0L, pageNo, pageSize);
            }
            
            // 获取符合条件的SKU ID列表
            Set<Long> filteredSkuIds = filteredWarehouseInfos.stream()
                .map(SalesWarehouseInfo::getProductSkuId)
                .collect(Collectors.toSet());
            
            // 通过SKU ID查询对应的SKU信息，获取SKC ID
            if (!filteredSkuIds.isEmpty()) {
                LambdaQueryWrapper<SalesSkuQuantity> skuQuery = new LambdaQueryWrapper<>();
                skuQuery.in(SalesSkuQuantity::getProductSkuId, filteredSkuIds);
                
                List<SalesSkuQuantity> skuQuantities = salesSkuQuantityMapper.selectList(skuQuery);
                
                // 提取这些SKU对应的SKC ID
                filteredSkcIds = skuQuantities.stream()
                    .map(SalesSkuQuantity::getProductSkcId)
                    .collect(Collectors.toSet());
                
                log.info("符合可售天数条件的SKU所关联的SKC数量: {}", filteredSkcIds.size());
                
                // 如果没有找到相关的SKC，直接返回空结果
                if (filteredSkcIds.isEmpty()) {
                    log.info("没有符合可售天数条件关联的SKC数据，返回空结果");
                    return new LocalSalesVO(Collections.emptyList(), 0L, pageNo, pageSize);
                }
            }
        }
        
        // 构建SKC查询条件
        LambdaQueryWrapper<SalesSubOrder> queryWrapper = new LambdaQueryWrapper<>();
        
        // 应用SKC层级的过滤条件
        applySKCFilters(queryWrapper, requestDTO, userShopIds);
        
        // 执行分页查询
        Page<SalesSubOrder> page = new Page<>(pageNo, pageSize);
        Page<SalesSubOrder> pageResult = salesSubOrderMapper.selectPage(page, queryWrapper);
        
        // 如果没有查询到SKC数据，直接返回空结果
        if (pageResult.getRecords().isEmpty()) {
            return new LocalSalesVO(Collections.emptyList(), pageResult.getTotal(), pageNo, pageSize);
        }
        
        // 使用提取的方法构建结果
        return buildLocalSalesVO(pageResult.getRecords(), requestDTO, pageResult.getTotal(), pageNo, pageSize);
    }
    
    /**
     * 构建SKU查询条件
     */
    private LambdaQueryWrapper<SalesSkuQuantity> buildSkuQueryWrapper(LocalSalesRequestDTO requestDTO, List<Long> userShopIds) {
        LambdaQueryWrapper<SalesSkuQuantity> skuWrapper = new LambdaQueryWrapper<>();
        
        // 添加店铺权限过滤
        if (!Boolean.TRUE.equals(requestDTO.getIgnorePermissionCheck()) && !userShopIds.isEmpty()) {
            skuWrapper.in(SalesSkuQuantity::getShopId, userShopIds);
        }
        
        // 请求中的店铺过滤
        if (!CollectionUtils.isEmpty(requestDTO.getShopIds())) {
            skuWrapper.in(SalesSkuQuantity::getShopId, requestDTO.getShopIds());
        }
        
        // 应用SKU级别的过滤条件
        if (StringUtils.hasText(requestDTO.getSkuExtCode())) {
            skuWrapper.like(SalesSkuQuantity::getSkuExtCode, requestDTO.getSkuExtCode());
        }
        
        if (StringUtils.hasText(requestDTO.getSkuExtCodeList())) {
            String[] skuExtCodes = requestDTO.getSkuExtCodeList().split(",");
            skuWrapper.in(SalesSkuQuantity::getSkuExtCode, Arrays.asList(skuExtCodes));
        }
        
        if (!CollectionUtils.isEmpty(requestDTO.getSkuIdList())) {
            List<Long> skuIds = requestDTO.getSkuIdList().stream()
                .filter(StringUtils::hasText)
                .map(Long::parseLong)
                .collect(Collectors.toList());
            
            if (!skuIds.isEmpty()) {
                skuWrapper.in(SalesSkuQuantity::getProductSkuId, skuIds);
            }
        }
        
        // 添加销量范围过滤条件
        if (requestDTO.getTodaySaleVolumMin() != null) {
            skuWrapper.ge(SalesSkuQuantity::getTodaySaleVolume, requestDTO.getTodaySaleVolumMin());
        }
        
        if (requestDTO.getTodaySaleVolumMax() != null) {
            skuWrapper.le(SalesSkuQuantity::getTodaySaleVolume, requestDTO.getTodaySaleVolumMax());
        }
        
        if (requestDTO.getLastSevenDaysSaleVolumeMin() != null) {
            skuWrapper.ge(SalesSkuQuantity::getLastSevenDaysSaleVolume, requestDTO.getLastSevenDaysSaleVolumeMin());
        }
        
        if (requestDTO.getLastSevenDaysSaleVolumeMax() != null) {
            skuWrapper.le(SalesSkuQuantity::getLastSevenDaysSaleVolume, requestDTO.getLastSevenDaysSaleVolumeMax());
        }
        
        if (requestDTO.getLastThirtyDaysSaleVolumeMin() != null) {
            skuWrapper.ge(SalesSkuQuantity::getLastThirtyDaysSaleVolume, requestDTO.getLastThirtyDaysSaleVolumeMin());
        }
        
        if (requestDTO.getLastThirtyDaysSaleVolumeMax() != null) {
            skuWrapper.le(SalesSkuQuantity::getLastThirtyDaysSaleVolume, requestDTO.getLastThirtyDaysSaleVolumeMax());
        }
        
        // 处理快速筛选
        if ("isLack".equals(requestDTO.getQuickFilter())) {
            skuWrapper.gt(SalesSkuQuantity::getLackQuantity, 0);
        }
        
        return skuWrapper;
    }
    
    /**
     * 应用SKC层级的过滤条件
     */
    private void applySKCFilters(LambdaQueryWrapper<SalesSubOrder> queryWrapper, LocalSalesRequestDTO requestDTO, List<Long> userShopIds) {
        // 用户店铺权限过滤
        if (!Boolean.TRUE.equals(requestDTO.getIgnorePermissionCheck()) && !userShopIds.isEmpty()) {
            queryWrapper.in(SalesSubOrder::getShopId, userShopIds);
        }
        
        // 请求中的店铺过滤
        if (!CollectionUtils.isEmpty(requestDTO.getShopIds())) {
            queryWrapper.in(SalesSubOrder::getShopId, requestDTO.getShopIds());
        }
        
        // 如果有可售天数过滤条件中得到的SKC ID集合，应用过滤
        // 现有代码中可能会设置 filteredSkcIds
        // if (hasAvailableSaleDaysFilter && !filteredSkcIds.isEmpty()) {
        //     queryWrapper.in(SalesSubOrder::getProductSkcId, filteredSkcIds);
        // }
        
        // 处理快速筛选
        if ("isLack".equals(requestDTO.getQuickFilter())) {
            // 这里可能需要特殊处理，因为是在SKU层级上的
            // 在SKU查询中已处理
        }
        
        // 添加其他查询条件
        // 商品类目过滤
        if (StringUtils.hasText(requestDTO.getCategory())) {
            queryWrapper.like(SalesSubOrder::getCategory, requestDTO.getCategory());
        }
        
        // SPU ID过滤
        if (!CollectionUtils.isEmpty(requestDTO.getSpuIdList())) {
            queryWrapper.in(SalesSubOrder::getProductId, requestDTO.getSpuIdList());
        }
        
        // SKC ID过滤
        if (!CollectionUtils.isEmpty(requestDTO.getSkcIdList())) {
            queryWrapper.in(SalesSubOrder::getProductSkcId, requestDTO.getSkcIdList());
        }
        
        // 图片审核状态过滤
        if (!CollectionUtils.isEmpty(requestDTO.getPictureAuditStatusList())) {
            if (requestDTO.getPictureAuditStatusList().size() == 1) {
                queryWrapper.eq(SalesSubOrder::getPictureAuditStatus, requestDTO.getPictureAuditStatusList().get(0));
            } else if (!requestDTO.getPictureAuditStatusList().isEmpty()) {
                queryWrapper.in(SalesSubOrder::getPictureAuditStatus, requestDTO.getPictureAuditStatusList());
            }
        }
        
        // 供应状态过滤
        if (!CollectionUtils.isEmpty(requestDTO.getSupplyStatusList())) {
            if (requestDTO.getSupplyStatusList().size() == 1) {
                queryWrapper.eq(SalesSubOrder::getSupplyStatus, requestDTO.getSupplyStatusList().get(0));
            } else if (!requestDTO.getSupplyStatusList().isEmpty()) {
                queryWrapper.in(SalesSubOrder::getSupplyStatus, requestDTO.getSupplyStatusList());
            }
        }
        
        // SKC编码过滤
        if (StringUtils.hasText(requestDTO.getSkcExtCode())) {
            queryWrapper.like(SalesSubOrder::getSkcExtCode, requestDTO.getSkcExtCode());
        }
        
        // 商品名称关键词过滤
        if (StringUtils.hasText(requestDTO.getProductNameKeyword())) {
            queryWrapper.like(SalesSubOrder::getProductName, requestDTO.getProductNameKeyword());
        }
        
        // 是否定制商品过滤
        if (requestDTO.getIsCustomGoods() != null) {
            queryWrapper.eq(SalesSubOrder::getIsCustomGoods, requestDTO.getIsCustomGoods());
        }
        
        // 库存区域过滤
        if (!CollectionUtils.isEmpty(requestDTO.getInventoryRegionList())) {
            if (requestDTO.getInventoryRegionList().size() == 1) {
                queryWrapper.eq(SalesSubOrder::getInventoryRegion, requestDTO.getInventoryRegionList().get(0));
            } else {
                queryWrapper.in(SalesSubOrder::getInventoryRegion, requestDTO.getInventoryRegionList());
            }
        }
        
        // 是否自动关闭JIT过滤
        if (requestDTO.getAutoCloseJit() != null) {
            queryWrapper.eq(SalesSubOrder::getAutoCloseJit, requestDTO.getAutoCloseJit());
        }
        
        // JIT状态过滤
        if (!CollectionUtils.isEmpty(requestDTO.getCloseJitStatus())) {
            if (requestDTO.getCloseJitStatus().size() == 1) {
                queryWrapper.eq(SalesSubOrder::getCloseJitStatus, requestDTO.getCloseJitStatus().get(0));
            } else if (!requestDTO.getCloseJitStatus().isEmpty()) {
                queryWrapper.in(SalesSubOrder::getCloseJitStatus, requestDTO.getCloseJitStatus());
            }
        }
        
        // 是否热销款过滤
        if (requestDTO.getHotTag() != null) {
            queryWrapper.eq(SalesSubOrder::getHotTag, requestDTO.getHotTag());
        }
        
        // 添加VMI条件过滤
        if (requestDTO.getSettlementType() != null) {
            queryWrapper.eq(SalesSubOrder::getSettlementType, requestDTO.getSettlementType());
        }
        
        // 添加JIT备货条件过滤
        if (requestDTO.getPurchaseStockType() != null) {
            queryWrapper.eq(SalesSubOrder::getPurchaseStockType, requestDTO.getPurchaseStockType());
        }
    }
    
    /**
     * 构建销售数据视图对象
     */
    private LocalSalesVO buildLocalSalesVO(List<SalesSubOrder> skcOrders, LocalSalesRequestDTO requestDTO, 
                                          Long total, Integer pageNo, Integer pageSize) {
        // 如果没有SKC数据，直接返回空结果
        if (skcOrders.isEmpty()) {
            return new LocalSalesVO(Collections.emptyList(), total, pageNo, pageSize);
        }
        
        // 提取SKC数据，准备查询关联信息
        List<SalesSubOrder> orders = skcOrders;
        
        // 提取店铺ID和SKC ID
        Set<Long> shopIds = orders.stream()
                .map(SalesSubOrder::getShopId)
                .collect(Collectors.toSet());
        
        List<Long> skcIds = orders.stream()
                .map(SalesSubOrder::getProductSkcId)
                .collect(Collectors.toList());
        
        // 获取店铺信息
        Map<Long, Shop> shopMap = new HashMap<>();
        if (!shopIds.isEmpty()) {
            List<Shop> shops = shopMapper.selectBatchIds(shopIds);
            shopMap = shops.stream().collect(Collectors.toMap(Shop::getShopId, shop -> shop));
        }
        
        // 查询这些SKC对应的所有SKU信息
        List<SalesSkuQuantity> allSkuQuantities = new ArrayList<>();
        if (!skcIds.isEmpty()) {
            LambdaQueryWrapper<SalesSkuQuantity> skuWrapper = new LambdaQueryWrapper<>();
            skuWrapper.in(SalesSkuQuantity::getProductSkcId, skcIds);
            
            // 应用SKU层级的过滤条件（如果有的话）
            if (StringUtils.hasText(requestDTO.getSkuExtCode())) {
                skuWrapper.like(SalesSkuQuantity::getSkuExtCode, requestDTO.getSkuExtCode());
            }
            
            if (StringUtils.hasText(requestDTO.getSkuExtCodeList())) {
                String[] skuExtCodes = requestDTO.getSkuExtCodeList().split(",");
                skuWrapper.in(SalesSkuQuantity::getSkuExtCode, Arrays.asList(skuExtCodes));
            }
            
            if (!CollectionUtils.isEmpty(requestDTO.getSkuIdList())) {
                List<Long> skuIds = requestDTO.getSkuIdList().stream()
                    .filter(StringUtils::hasText)
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
                
                if (!skuIds.isEmpty()) {
                    skuWrapper.in(SalesSkuQuantity::getProductSkuId, skuIds);
                }
            }
            
            allSkuQuantities = salesSkuQuantityMapper.selectList(skuWrapper);
            log.info("查询到SKC关联的SKU总数: {}", allSkuQuantities.size());
        }
        
        // 按SKC ID分组SKU信息
        Map<Long, List<SalesSkuQuantity>> skcToSkuMap = allSkuQuantities.stream()
                .collect(Collectors.groupingBy(SalesSkuQuantity::getProductSkcId));
        
        // 获取所有SKU ID
        List<Long> allSkuIds = allSkuQuantities.stream()
                .map(SalesSkuQuantity::getProductSkuId)
                .collect(Collectors.toList());
        
        // 查询库存信息
        List<SalesInventoryInfo> allInventories = new ArrayList<>();
        if (!allSkuIds.isEmpty()) {
            LambdaQueryWrapper<SalesInventoryInfo> inventoryWrapper = new LambdaQueryWrapper<>();
            inventoryWrapper.in(SalesInventoryInfo::getProductSkuId, allSkuIds);
            allInventories = salesInventoryInfoMapper.selectList(inventoryWrapper);
            log.info("查询到的库存信息数量: {}", allInventories.size());
        }
        
        // 按SKU ID分组库存信息
        Map<Long, List<SalesInventoryInfo>> skuToInventoryMap = allInventories.stream()
                .collect(Collectors.groupingBy(SalesInventoryInfo::getProductSkuId));
        
        // 查询仓库信息
        List<SalesWarehouseInfo> allWarehouseInfos = new ArrayList<>();
        if (!allSkuIds.isEmpty()) {
            // 查询所有SKU的仓库信息
            LambdaQueryWrapper<SalesWarehouseInfo> warehouseWrapper = new LambdaQueryWrapper<>();
            warehouseWrapper.in(SalesWarehouseInfo::getProductSkuId, allSkuIds);
            
            // 可能的仓库组过滤
            if (requestDTO.getWarehouseGroupId() != null) {
                warehouseWrapper.eq(SalesWarehouseInfo::getWarehouseGroupId, requestDTO.getWarehouseGroupId());
            }
            
            allWarehouseInfos = salesWarehouseInfoMapper.selectList(warehouseWrapper);
            log.info("查询到的仓库信息数量: {}", allWarehouseInfos.size());
        }
        
        // 按SKU ID分组仓库信息
        Map<Long, List<SalesWarehouseInfo>> skuToWarehouseMap = allWarehouseInfos.stream()
                .collect(Collectors.groupingBy(SalesWarehouseInfo::getProductSkuId));
        
        // 构建最终结果
        List<LocalSalesItemVO> resultList = new ArrayList<>();
        
        for (SalesSubOrder order : orders) {
            // 创建基本信息
            LocalSalesItemVO item = createBasicSalesItem(order, shopMap.get(order.getShopId()));
            
            // 获取当前SKC下的所有SKU
            List<SalesSkuQuantity> skuQuantities = skcToSkuMap.getOrDefault(order.getProductSkcId(), Collections.emptyList());
            
            // 准备SKU、库存和仓库信息列表
            List<SkuInfo> skuInfoList = new ArrayList<>();
            List<InventoryInfo> inventoryInfoList = new ArrayList<>();
            List<WarehouseInfo> warehouseInfoList = new ArrayList<>();
            
            // 处理SKU级别数据
            for (SalesSkuQuantity skuQuantity : skuQuantities) {
                Long skuId = skuQuantity.getProductSkuId();
                
                // 添加SKU信息
                SkuInfo skuInfo = new SkuInfo();
                skuInfo.setProductSkuId(skuId);
                skuInfo.setSkuExtCode(skuQuantity.getSkuExtCode());
                skuInfo.setClassName(skuQuantity.getClassName());
                skuInfo.setStockDays(skuQuantity.getStockDays());
                skuInfo.setTodaySaleVolume(skuQuantity.getTodaySaleVolume());
                skuInfo.setTotalSaleVolume(skuQuantity.getTotalSaleVolume());
                skuInfo.setLastSevenDaysSaleVolume(skuQuantity.getLastSevenDaysSaleVolume());
                skuInfo.setLastThirtyDaysSaleVolume(skuQuantity.getLastThirtyDaysSaleVolume());
                skuInfo.setInCartNumber(skuQuantity.getInCartNumber());
                skuInfo.setInCartNumber7d(skuQuantity.getInCartNumber7d());
                skuInfo.setLackQuantity(skuQuantity.getLackQuantity());
                skuInfoList.add(skuInfo);
                
                // 添加库存信息
                List<SalesInventoryInfo> inventories = skuToInventoryMap.getOrDefault(skuId, Collections.emptyList());
                for (SalesInventoryInfo inventory : inventories) {
                    InventoryInfo inventoryInfo = new InventoryInfo();
                    inventoryInfo.setProductSkuId(inventory.getProductSkuId());
                    inventoryInfo.setWarehouseGroupId(inventory.getWarehouseGroupId());
                    inventoryInfo.setWarehouseInventoryNum(inventory.getWarehouseInventoryNum());
                    inventoryInfo.setWaitOnShelfNum(inventory.getWaitOnShelfNum());
                    inventoryInfo.setWaitDeliveryInventoryNum(inventory.getWaitDeliveryInventoryNum());
                    inventoryInfo.setExpectedOccupiedInventoryNum(inventory.getExpectedOccupiedInventoryNum());
                    inventoryInfo.setWaitApproveInventoryNum(inventory.getWaitApproveInventoryNum());
                    inventoryInfo.setWaitQcNum(inventory.getWaitQcNum());
                    inventoryInfo.setUnavailableWarehouseInventoryNum(inventory.getUnavailableWarehouseInventoryNum());
                    inventoryInfo.setWaitInStock(inventory.getWaitInStock());
                    inventoryInfo.setWaitReceiveNum(inventory.getWaitReceiveNum());
                    inventoryInfoList.add(inventoryInfo);
                }
                
                // 添加仓库信息
                List<SalesWarehouseInfo> warehouseInfos = skuToWarehouseMap.getOrDefault(skuId, Collections.emptyList());
                for (SalesWarehouseInfo warehouse : warehouseInfos) {
                    WarehouseInfo warehouseInfo = new WarehouseInfo();
                    warehouseInfo.setProductSkuId(warehouse.getProductSkuId());
                    warehouseInfo.setWarehouseGroupId(warehouse.getWarehouseGroupId());
                    warehouseInfo.setWarehouseGroupName(warehouse.getWarehouseGroupName());
                    warehouseInfo.setStockDays(warehouse.getStockDays());
                    warehouseInfo.setSafeInventoryDays(warehouse.getSafeInventoryDays());
                    warehouseInfo.setPurchaseConfig(warehouse.getPurchaseConfig());
                    warehouseInfo.setTodaySaleVolume(warehouse.getTodaySaleVolume());
                    warehouseInfo.setTotalSaleVolume(warehouse.getTotalSaleVolume());
                    warehouseInfo.setLastSevenDaysSaleVolume(warehouse.getLastSevenDaysSaleVolume());
                    warehouseInfo.setLastThirtyDaysSaleVolume(warehouse.getLastThirtyDaysSaleVolume());
                    warehouseInfo.setLackQuantity(warehouse.getLackQuantity());
                    warehouseInfo.setAdviceQuantity(warehouse.getAdviceQuantity());
                    warehouseInfo.setAvailableSaleDays(warehouse.getAvailableSaleDays());
                    warehouseInfo.setAvailableSaleDaysFromInventory(warehouse.getAvailableSaleDaysFromInventory());
                    warehouseInfo.setWarehouseAvailableSaleDays(warehouse.getWarehouseAvailableSaleDays());
                    warehouseInfo.setSevenDaysSaleReference(warehouse.getSevenDaysSaleReference());
                    warehouseInfo.setSevenDaysReferenceSaleType(warehouse.getSevenDaysReferenceSaleType());
                    warehouseInfoList.add(warehouseInfo);
                }
            }
            
            // 设置SKU、库存和仓库信息
            item.setSkuList(skuInfoList);
            item.setInventoryList(inventoryInfoList);
            item.setWarehouseList(warehouseInfoList);
            
            // 如果符合所有条件，添加到结果列表
            resultList.add(item);
        }
        
        return new LocalSalesVO(resultList, total, pageNo, pageSize);
    }
    
    /**
     * 创建基本的销售信息项
     */
    private LocalSalesItemVO createBasicSalesItem(SalesSubOrder order, Shop shop) {
        LocalSalesItemVO item = new LocalSalesItemVO();
        
        item.setId(order.getId());
        item.setShopId(order.getShopId());
        item.setShopName(shop != null ? shop.getShopName() : null);
        item.setShopRemark(shop != null ? shop.getRemark() : null);
        item.setProductId(order.getProductId());
        item.setProductSkcId(order.getProductSkcId());
        item.setProductName(order.getProductName());
        item.setSkcExtCode(order.getSkcExtCode());
        item.setCategory(order.getCategory());
        item.setProductSkcPicture(order.getProductSkcPicture());
        item.setIsCustomGoods(order.getIsCustomGoods());
        item.setInventoryRegion(order.getInventoryRegion());
        item.setOnSalesDurationOffline(order.getOnSalesDurationOffline());
        item.setAutoCloseJit(order.getAutoCloseJit());
        item.setCloseJitStatus(order.getCloseJitStatus());
        item.setHasHotSku(order.getHasHotSku());
        item.setIsEnoughStock(order.getIsEnoughStock());
        item.setSyncTime(order.getSyncTime());
        item.setPictureAuditStatus(order.getPictureAuditStatus());
        
        // *** 添加缺失的字段映射 ***
        item.setPurchaseStockType(order.getPurchaseStockType());
        item.setSettlementType(order.getSettlementType()); // 同时添加结算类型，可能也需要
        item.setSupplyStatus(order.getSupplyStatus()); // 添加供应状态

        // 设置备货区域名称
        if (order.getInventoryRegion() != null) {
            switch (order.getInventoryRegion()) {
                case 1:
                    item.setInventoryRegionName("国内备货");
                    break;
                case 2:
                    item.setInventoryRegionName("海外备货");
                    break;
                case 3:
                    item.setInventoryRegionName("保税仓备货");
                    break;
                default:
                    item.setInventoryRegionName("其他");
            }
        }
        
        // 设置JIT关闭状态名称
        if (order.getCloseJitStatus() != null) {
            switch (order.getCloseJitStatus()) {
                case 0:
                    item.setCloseJitStatusName("未关闭");
                    break;
                case 1:
                    item.setCloseJitStatusName("已关闭");
                    break;
                case 2:
                    item.setCloseJitStatusName("关闭处理中");
                    break;
                default:
                    item.setCloseJitStatusName("未知");
            }
        }
        
        return item;
    }
    
    /**
     * 创建带SKU信息的销售信息项
     */
    private LocalSalesItemVO createSalesItemWithSkuInfo(SalesSubOrder order, SalesSkuQuantity skuQuantity, Shop shop) {
        LocalSalesItemVO item = createBasicSalesItem(order, shop);
        
        // 移除将SKU信息设置到主对象的代码
        // 注意：如果这个方法在其他地方被调用，需要相应修改代码
        return item;
    }
} 