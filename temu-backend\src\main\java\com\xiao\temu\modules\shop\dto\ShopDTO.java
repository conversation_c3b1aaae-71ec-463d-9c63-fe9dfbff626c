package com.xiao.temu.modules.shop.dto;

import com.xiao.temu.modules.operation.dto.OperationGroupDTO;
import com.xiao.temu.modules.shop.entity.Shop;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 店铺DTO
 */
@Data
public class ShopDTO {
    
    /** 店铺ID */
    private Long shopId;
    
    /** 店铺名称 */
    private String shopName;
    
    /** temu平台店铺id */
    private String shopTemuId;
    
    /** 关联的运营组列表 */
    private List<OperationGroupDTO> groups;
    
    /** API密钥 */
    private String apiKey;
    
    /** API密钥Secret */
    private String apiSecret;
    
    /** access_token */
    private String accessToken;
    
    /** 状态（0正常 1禁用） */
    private String status;
    
    /** 创建时间 */
    private LocalDateTime createTime;
    
    /** 更新时间 */
    private LocalDateTime updateTime;
    
    /** 备注 */
    private String remark;
    
    /** 创建者ID */
    private Long createBy;
    
    /** 所属运营组ID */
    private Long belongGroupId;

    /**
     * 将DTO转换为实体
     * 
     * @return Shop实体
     */
    public Shop convertToShop() {
        Shop shop = new Shop();
        shop.setShopId(this.shopId);
        shop.setShopName(this.shopName);
        shop.setShopTemuId(this.shopTemuId);
        shop.setApiKey(this.apiKey);
        shop.setApiSecret(this.apiSecret);
        shop.setAccessToken(this.accessToken);
        shop.setStatus(this.status);
        shop.setCreateTime(this.createTime);
        shop.setUpdateTime(this.updateTime);
        shop.setRemark(this.remark);
        shop.setCreateBy(this.createBy);
        shop.setBelongGroupId(this.belongGroupId);
        return shop;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShopTemuId() {
        return shopTemuId;
    }

    public void setShopTemuId(String shopTemuId) {
        this.shopTemuId = shopTemuId;
    }

    public List<OperationGroupDTO> getGroups() {
        return groups;
    }

    public void setGroups(List<OperationGroupDTO> groups) {
        this.groups = groups;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getApiSecret() {
        return apiSecret;
    }

    public void setApiSecret(String apiSecret) {
        this.apiSecret = apiSecret;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    
    public Long getCreateBy() {
        return createBy;
    }
    
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }
    
    public Long getBelongGroupId() {
        return belongGroupId;
    }
    
    public void setBelongGroupId(Long belongGroupId) {
        this.belongGroupId = belongGroupId;
    }
} 