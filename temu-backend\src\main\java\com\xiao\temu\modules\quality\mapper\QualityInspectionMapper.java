package com.xiao.temu.modules.quality.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.quality.entity.QualityInspection;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 质检结果数据Mapper接口
 */
@Mapper
public interface QualityInspectionMapper extends BaseMapper<QualityInspection> {

    /**
     * 批量插入或更新质检数据
     *
     * @param list 质检数据列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("list") List<QualityInspection> list);

    /**
     * 根据店铺ID获取最新的质检数据更新时间
     *
     * @param shopId 店铺ID
     * @return 最新更新时间
     */
    LocalDateTime getLatestUpdateTimeByShopId(@Param("shopId") Long shopId);
    
    /**
     * 根据店铺ID查询不合格的质检数据
     *
     * @param shopId 店铺ID
     * @return 不合格的质检数据列表
     */
    List<QualityInspection> selectDefectiveByShopId(@Param("shopId") Long shopId);
    
    /**
     * 根据店铺ID查询不合格的质检单ID和SKU ID
     *
     * @param shopId 店铺ID
     * @return 包含qcBillId和productSkuId的列表
     */
    List<Map<String, Object>> selectDefectiveQcBillAndSkuIdsByShopId(@Param("shopId") Long shopId);
    
    /**
     * 获取指定店铺的质检记录总数
     *
     * @param shopId 店铺ID
     * @return 质检记录总数
     */
    @Select("SELECT COUNT(*) FROM quality_inspection WHERE shop_id = #{shopId}")
    Integer getQualityInspectionCountByShopId(@Param("shopId") Long shopId);
} 