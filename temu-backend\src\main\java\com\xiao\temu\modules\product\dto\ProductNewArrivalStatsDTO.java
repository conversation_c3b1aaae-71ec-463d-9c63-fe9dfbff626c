package com.xiao.temu.modules.product.dto;

/**
 * 商品上新统计数据DTO
 */
public class ProductNewArrivalStatsDTO {
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺名称（如果需要）
     */
    private String shopName;
    
    /**
     * 店铺备注
     */
    private String shopRemark;
    
    /**
     * 本月上新数量
     */
    private Integer currentMonthNewCount;
    
    /**
     * 本周上新数量
     */
    private Integer currentWeekNewCount;
    
    /**
     * 上周上新数量
     */
    private Integer lastWeekNewCount;
    
    /**
     * 昨日上新数量
     */
    private Integer yesterdayNewCount;
    
    /**
     * 本周上架数量
     */
    private Integer currentWeekOnlineCount;
    
    /**
     * 今日销量
     */
    private Integer todaySales;
    
    /**
     * 近7天销量
     */
    private Integer lastWeekSales;
    
    /**
     * 近30天销量
     */
    private Integer lastMonthSales;
    
    /**
     * 商品总数
     */
    private Integer totalProducts;

    /**
     * 店铺所属运营组名称，多个用逗号分隔
     */
    private String groupNames;

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }
    
    public String getShopRemark() {
        return shopRemark;
    }

    public void setShopRemark(String shopRemark) {
        this.shopRemark = shopRemark;
    }

    public Integer getCurrentMonthNewCount() {
        return currentMonthNewCount;
    }

    public void setCurrentMonthNewCount(Integer currentMonthNewCount) {
        this.currentMonthNewCount = currentMonthNewCount;
    }

    public Integer getCurrentWeekNewCount() {
        return currentWeekNewCount;
    }

    public void setCurrentWeekNewCount(Integer currentWeekNewCount) {
        this.currentWeekNewCount = currentWeekNewCount;
    }

    public Integer getLastWeekNewCount() {
        return lastWeekNewCount;
    }

    public void setLastWeekNewCount(Integer lastWeekNewCount) {
        this.lastWeekNewCount = lastWeekNewCount;
    }

    public Integer getYesterdayNewCount() {
        return yesterdayNewCount;
    }

    public void setYesterdayNewCount(Integer yesterdayNewCount) {
        this.yesterdayNewCount = yesterdayNewCount;
    }

    public Integer getCurrentWeekOnlineCount() {
        return currentWeekOnlineCount;
    }

    public void setCurrentWeekOnlineCount(Integer currentWeekOnlineCount) {
        this.currentWeekOnlineCount = currentWeekOnlineCount;
    }
    
    public Integer getTodaySales() {
        return todaySales;
    }

    public void setTodaySales(Integer todaySales) {
        this.todaySales = todaySales;
    }

    public Integer getLastWeekSales() {
        return lastWeekSales;
    }

    public void setLastWeekSales(Integer lastWeekSales) {
        this.lastWeekSales = lastWeekSales;
    }

    public Integer getLastMonthSales() {
        return lastMonthSales;
    }

    public void setLastMonthSales(Integer lastMonthSales) {
        this.lastMonthSales = lastMonthSales;
    }

    public Integer getTotalProducts() {
        return totalProducts;
    }

    public void setTotalProducts(Integer totalProducts) {
        this.totalProducts = totalProducts;
    }

    public String getGroupNames() {
        return groupNames;
    }

    public void setGroupNames(String groupNames) {
        this.groupNames = groupNames;
    }
} 