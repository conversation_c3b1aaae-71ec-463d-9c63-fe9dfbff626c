package com.xiao.temu.security.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.system.mapper.SysUserMapper;
import com.xiao.temu.security.model.UserDetailsImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户详情服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

    private final SysUserMapper userMapper;
    private final PermissionService permissionService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 查询用户信息
        SysUser user = userMapper.selectOne(
                new LambdaQueryWrapper<SysUser>()
                        .eq(SysUser::getUsername, username)
        );

        if (user == null) {
            log.info("登录用户：{} 不存在", username);
            throw new UsernameNotFoundException("登录用户：" + username + " 不存在");
        } else if ("1".equals(user.getStatus())) {
            log.info("登录用户：{} 已被禁用", username);
            throw new UsernameNotFoundException("对不起，您的账号：" + username + " 已被禁用");
        }

        // 查询用户权限信息
        List<String> permissions = new ArrayList<>();
        
        // 从PermissionService获取用户权限列表
        try {
            permissions = permissionService.getPermissionsByUserId(user.getUserId());
        } catch (Exception e) {
            log.error("获取用户权限异常", e);
        }

        return UserDetailsImpl.build(user, permissions);
    }
} 