package com.xiao.temu.security.annotation;

import java.lang.annotation.*;

/**
 * 数据权限注解
 * 用于标记需要进行数据权限过滤的方法
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataScope {
    
    /**
     * 表的别名
     */
    String tableAlias() default "";
    
    /**
     * 用户ID字段名
     */
    String userIdColumn() default "user_id";
    
    /**
     * 权限类型字段名
     */
    String permissionColumn() default "permission_type";
    
    /**
     * 是否包含组所有权限（组长能查看组内所有数据）
     */
    boolean includeGroupData() default true;
    
    /**
     * 组ID字段名
     */
    String groupIdColumn() default "group_id";
} 