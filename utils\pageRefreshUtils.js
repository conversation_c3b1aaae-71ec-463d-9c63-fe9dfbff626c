/**
 * 页面刷新工具函数
 * 用于处理页面切换时的数据刷新
 */

/**
 * 记录页面访问状态
 * @param {string} pagePath - 页面路径
 */
const recordPageVisit = (pagePath) => {
  try {
    // 获取当前页面访问状态记录
    const pageVisitRecord = wx.getStorageSync('pageVisitRecord') || {};
    
    // 更新当前页面的访问时间
    pageVisitRecord[pagePath] = {
      lastVisitTime: Date.now(),
      visitCount: (pageVisitRecord[pagePath]?.visitCount || 0) + 1
    };
    
    // 保存更新后的记录
    wx.setStorageSync('pageVisitRecord', pageVisitRecord);
    
    console.log(`页面访问记录已更新: ${pagePath}`);
  } catch (error) {
    console.error('记录页面访问状态失败:', error);
  }
};

/**
 * 检查页面是否需要刷新
 * @param {string} pagePath - 页面路径
 * @returns {boolean} 是否需要刷新
 */
const needRefresh = (pagePath) => {
  try {
    // 获取页面访问记录
    const pageVisitRecord = wx.getStorageSync('pageVisitRecord') || {};
    const currentPageRecord = pageVisitRecord[pagePath];
    
    // 如果没有该页面的记录，表示首次访问，需要刷新
    if (!currentPageRecord) {
      return true;
    }
    
    // 检查上次访问时间是否超过一定时间（例如30秒）
    const now = Date.now();
    const timeDiff = now - currentPageRecord.lastVisitTime;
    
    // 如果超过30秒，需要刷新
    if (timeDiff > 30000) {
      return true;
    }
    
    // 检查访问计数，每访问3次强制刷新一次
    return currentPageRecord.visitCount % 3 === 0;
  } catch (error) {
    console.error('检查页面刷新状态失败:', error);
    // 出错时默认刷新
    return true;
  }
};

/**
 * 设置页面需要刷新的标志
 * @param {string} pagePath - 页面路径
 */
const markPageForRefresh = (pagePath) => {
  try {
    // 获取需要刷新的页面列表
    const pagesNeedRefresh = wx.getStorageSync('pagesNeedRefresh') || [];
    
    // 如果页面不在列表中，则添加
    if (!pagesNeedRefresh.includes(pagePath)) {
      pagesNeedRefresh.push(pagePath);
      wx.setStorageSync('pagesNeedRefresh', pagesNeedRefresh);
    }
  } catch (error) {
    console.error('标记页面刷新失败:', error);
  }
};

/**
 * 检查页面是否被标记为需要刷新
 * @param {string} pagePath - 页面路径
 * @returns {boolean} 是否需要刷新
 */
const isMarkedForRefresh = (pagePath) => {
  try {
    const pagesNeedRefresh = wx.getStorageSync('pagesNeedRefresh') || [];
    return pagesNeedRefresh.includes(pagePath);
  } catch (error) {
    console.error('检查页面刷新标记失败:', error);
    return false;
  }
};

/**
 * 清除页面的刷新标记
 * @param {string} pagePath - 页面路径
 */
const clearRefreshMark = (pagePath) => {
  try {
    const pagesNeedRefresh = wx.getStorageSync('pagesNeedRefresh') || [];
    const index = pagesNeedRefresh.indexOf(pagePath);
    
    if (index !== -1) {
      pagesNeedRefresh.splice(index, 1);
      wx.setStorageSync('pagesNeedRefresh', pagesNeedRefresh);
    }
  } catch (error) {
    console.error('清除页面刷新标记失败:', error);
  }
};

module.exports = {
  recordPageVisit,
  needRefresh,
  markPageForRefresh,
  isMarkedForRefresh,
  clearRefreshMark
}; 