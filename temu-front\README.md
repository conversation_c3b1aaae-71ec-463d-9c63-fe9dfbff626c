# Temu API 前端项目

Temu API平台的前端项目，基于Vue 3、TypeScript、Vite、Element Plus开发。

## 项目介绍

本项目是Temu API平台的前端部分，实现了用户认证与权限控制、角色管理、菜单管理等功能，对接后端API实现完整的RBAC权限控制系统。

## 功能模块

- 用户认证（登录/登出）
- RBAC权限控制
- 用户管理
- 角色管理
- 菜单管理
- 公共组件（权限按钮、数据表格等）

## 技术栈

- Vue 3
- TypeScript
- Vite
- Element Plus
- Vue Router
- Pinia
- Axios

## 项目结构

```
temu-front/
├── public/             # 静态资源
├── src/
│   ├── api/            # API接口封装
│   ├── assets/         # 资源文件
│   ├── components/     # 全局组件
│   ├── composables/    # 组合式API
│   ├── config/         # 配置文件
│   ├── directives/     # 自定义指令
│   ├── layout/         # 布局组件
│   ├── router/         # 路由配置
│   ├── store/          # 状态管理
│   ├── utils/          # 工具函数
│   ├── views/          # 页面视图
│   ├── App.vue         # 根组件
│   ├── main.ts         # 入口文件
│   ├── permission.ts   # 权限控制
│   └── style.css       # 全局样式
├── .vscode/            # VSCode配置
├── index.html          # HTML入口
├── package.json        # 项目依赖
├── tsconfig.json       # TypeScript配置
├── vite.config.ts      # Vite配置
└── README.md           # 项目说明
```

## 开发指南

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 权限控制

项目实现了基于RBAC的权限控制系统：

1. 路由级权限控制：根据用户角色动态加载路由
2. 按钮级权限控制：使用`v-permission`指令控制按钮显示
3. 权限组件：封装了`PermissionButton`组件方便权限控制

# 时间格式处理统一方案

为解决前后端日期时间格式不匹配问题，我们采用了以下处理方案：

## 1. 前端处理

在前端使用 `formatDateTime` 工具函数（位于 `src/utils/format.ts`）将日期转换为ISO 8601格式字符串：

```typescript
// 示例：将日期字符串转换为LocalDateTime兼容的格式
import { formatDateTime } from '@/utils/format'

// 日期选择器中的日期转换
const handleTimeRangeChange = (val: string[]) => {
  if (val && val.length > 0) {
    queryParams.startTime = formatDateTime(val[0])  // 输出如：2025-03-23T00:00:00
  } else {
    queryParams.startTime = undefined
  }
  
  if (val && val.length > 1) {
    queryParams.endTime = formatDateTime(val[1])    // 输出如：2025-03-24T00:00:00
  } else {
    queryParams.endTime = undefined
  }
}
```

## 2. 后端处理

后端添加了 `WebMvcConfig` 配置类，用于处理日期时间格式转换：

```java
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Override
    public void addFormatters(FormatterRegistry registry) {
        DateTimeFormatterRegistrar registrar = new DateTimeFormatterRegistrar();
        // 设置日期格式
        registrar.setDateFormatter(DateTimeFormatter.ISO_DATE);
        // 设置日期时间格式，同时支持带T和不带T的格式
        registrar.setDateTimeFormatter(DateTimeFormatter.ISO_DATE_TIME);
        // 注册到转换服务中
        registrar.registerFormatters(registry);
    }
}
```

## 3. 日期时间格式约定

统一使用ISO 8601格式的日期时间字符串作为前后端交互的标准：

- 日期格式：`YYYY-MM-DD`（如：2025-03-23）
- 日期时间格式：`YYYY-MM-DDThh:mm:ss`（如：2025-03-23T00:00:00）

## 4. DTO中的日期时间字段

后端DTO中的日期时间字段统一使用 `LocalDateTime` 类型：

```java
public class MessageQueryDTO extends PageRequest {
    // ...
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    // ...
}
```

前端接口定义中使用字符串类型：

```typescript
export interface MessageQueryDTO {
  // ...
  startTime?: string;
  endTime?: string;
  // ...
}
```

## 特色功能

### 后台导出功能

本系统实现了前端后台导出功能，允许用户在导出大量数据时不阻塞界面操作，主要特点：

1. **后台执行**：导出任务在后台执行，不会阻塞用户界面
2. **实时进度**：通过任务面板实时显示导出进度
3. **任务管理**：支持查看、取消和清理导出任务
4. **通知机制**：任务完成或失败时通过通知提醒用户

#### 使用方法

1. 点击列表页面的"导出Excel"按钮
2. 选择导出类型和配置导出选项
3. 点击"确认导出"，任务将在后台执行
4. 通过右下角的导出任务面板查看导出进度
5. 导出完成后会自动下载文件并显示成功通知

#### 技术实现

- 使用Pinia状态管理导出任务队列
- 基于Web Worker技术在后台处理大量数据
- 使用ExcelJS库在前端生成Excel文件
- 通过Element Plus的通知组件提供用户反馈

## 项目组件

### 通用组件

项目中包含多个可复用的组件，这些组件位于 `src/components/temu` 目录下：

- `AppLayout`: 应用布局组件，提供标准页面布局结构
- `SearchCard`: 搜索区域卡片组件
- `TableCard`: 表格区域卡片组件
- `PaginationBar`: 分页器组件
- `ImagePreview`: 图片预览组件
- `EmptyTips`: 空数据提示组件
- `TagInput`: 多值输入组件，支持标签式输入
- `ExportTaskPanel`: 导出任务管理面板
- `ExportDialog`: Excel导出对话框组件，支持当前页/自定义数量/全部数据导出

### ExportDialog组件使用说明

这是一个通用的Excel导出对话框组件，提供统一的导出功能，支持多种导出方式。

#### 基本用法

```vue
<ExportDialog
  v-model:visible="exportDialogVisible"
  data-type="qc"
  :default-file-name="fileName"
  :current-page-data="currentData"
  :total="totalCount"
  :fetch-data-fn="fetchDataFunction"
  :query-params="queryParameters"
/>
```

#### 属性说明

| 属性名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| v-model:visible | Boolean | 是 | 控制对话框显示/隐藏 |
| data-type | String | 是 | 数据类型，目前支持 'qc' (抽检) 和 'refund' (退货) |
| default-file-name | String | 否 | 默认导出文件名，默认为"导出数据" |
| current-page-data | Array | 是 | 当前页数据 |
| total | Number | 是 | 数据总条数 |
| fetch-data-fn | Function | 是 | 获取数据的函数，接收查询参数并返回Promise |
| query-params | Object | 是 | 查询参数 |

#### 导出功能

- **当前页数据**: 直接导出当前页面显示的数据
- **自定义数量**: 根据指定的数量批量获取并导出数据
- **全部数据**: 批量获取并导出所有符合条件的数据

组件内部使用Web Worker进行后台导出处理，避免阻塞主线程，并提供导出进度反馈。

## 路由配置优化

系统采用了动态路由配置方式，从后台获取菜单结构并自动生成前端路由。优化后的主要特点：

1. 不再需要手动编写路由文件对应后台菜单
2. 使用基于glob模式的组件预加载+硬编码备选的方式解决动态导入问题
3. 只要后台菜单表中的组件路径与前端视图目录结构一致，即可自动加载对应组件
4. 更灵活的适应后台菜单配置变化

### 路由加载实现方案

由于Vite的动态导入限制（不支持多层级动态路径变量），我们采用了两种策略结合的方式：

1. **预加载所有视图组件**：使用`import.meta.glob`导入所有视图目录下的组件
2. **路径匹配**：根据组件路径在预加载的组件中查找匹配
3. **硬编码备选**：当找不到匹配时，通过硬编码映射表提供兜底

关键代码示例：
```ts
// 导入所有视图组件
const modules = import.meta.glob('../views/**/*.vue')

// 获取组件导入函数
export function getViewComponent(componentPath: string) {
  // 特殊处理Layout组件
  if (componentPath === 'Layout') {
    return () => import('@/layout/index.vue')
  }

  // 构建完整的视图路径
  const viewPath = `/src/views/${componentPath}.vue` 
  
  // 检查路径是否存在于预加载的模块中
  if (modules[viewPath]) {
    return modules[viewPath]
  }
  
  // 硬编码备选方案
  const componentMap = {
    'system/user/index': () => import('@/views/system/user/index.vue'),
    // ... 其他组件
  }
  
  // 尝试从映射表获取
  if (componentMap[componentPath]) {
    return componentMap[componentPath]
  }
  
  // 找不到匹配的组件，返回404页面
  return () => import('@/views/error/404.vue')
}
```

### 组件路径约定

- 组件路径格式应为：`模块/功能/index`，例如：`system/user/index`
- 对应的组件文件应位于：`src/views/模块/功能/index.vue`

### 示例

后台菜单配置：
```sql
INSERT INTO `sys_menu` VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', 1, 'C', '0', 'system:user:list', 'user', '2025-03-20 19:07:49', NULL, '用户管理菜单');
```

前端视图目录结构：
```
src/views/system/user/index.vue
```

动态路由生成：
```js
// 自动生成路由
{
  path: '/user',
  name: '用户管理',
  component: getViewComponent('system/user/index'),
  meta: {
    title: '用户管理',
    icon: 'user'
  }
}
```
