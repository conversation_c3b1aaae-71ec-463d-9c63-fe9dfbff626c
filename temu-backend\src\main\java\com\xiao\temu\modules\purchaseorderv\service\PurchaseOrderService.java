package com.xiao.temu.modules.purchaseorderv.service;

import com.xiao.temu.modules.purchaseorderv.dto.PurchaseOrderRequestDTO;
import com.xiao.temu.modules.purchaseorderv.vo.PurchaseOrderVO;

/**
 * Temu采购单服务接口
 */
public interface PurchaseOrderService {
    /**
     * 获取采购单列表
     *
     * @param requestDTO 请求参数
     * @param userId 当前用户ID
     * @return 采购单列表
     */
    PurchaseOrderVO getPurchaseOrderList(PurchaseOrderRequestDTO requestDTO, Long userId);
} 