package com.xiao.temu.modules.quality.service;

import com.xiao.temu.modules.quality.dto.LocalQualityInspectionRequestDTO;
import com.xiao.temu.modules.quality.vo.LocalQualityInspectionVO;

/**
 * 本地抽检结果明细服务接口
 */
public interface LocalQualityInspectionService {
    
    /**
     * 获取本地抽检结果明细列表
     * 
     * @param requestDTO 请求参数
     * @param userId 用户ID
     * @return 抽检结果明细VO
     */
    LocalQualityInspectionVO getLocalQualityInspectionList(LocalQualityInspectionRequestDTO requestDTO, Long userId);
} 