<template>
  <!-- 普通备货建议标签页 -->
  <div class="normal-purchase-tab">
    <!-- 普通备货建议搜索区域 -->
    <SearchArea 
      :shops="shops" 
      :query-params="queryParams" 
      :tag-counts="tagCounts"
      @search="handleSearch"
      @reset="handleReset"
      @update:query-params="updateQueryParams"
    />

    <!-- 状态筛选选项卡 -->
    <StatusTabs 
      :status-tabs="statusTabs"
      :active-status-tab="activeStatusTab"
      :query-params="queryParams"
      :sort-options="sortOptions"
      :has-selected-rows="selectedRows && selectedRows.length > 0"
      :total="total"
      v-model:modelValue="sortDisplayValue"
      @status-tab-click="handleStatusTabClick"
      @sort-change="handleSortChange"
      @batch-print-picking="handleBatchPrintPicking"
      @export-selected="handleExportSelected"
      @export-by-condition="handleExportByCondition"
    />

    <!-- 替换原有表格，使用新组件 -->
    <PurchaseOrderTable
      :loading="loading"
      :purchase-orders="purchaseOrders"
      :has-searched="hasSearched"
      @selection-change="handleSelectionChange"
      @preview-image="handlePreviewImage"
      @view-detail="handleViewDetail"
      @print-picking="handlePrintPicking"
    />

    <!-- 分页控件 -->
    <ActionBar
      :query-params="queryParams"
      :total="total"
      :shop-ranges="shopRanges"
      @update:query-params="updateQueryParams"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 添加拣货单预览弹窗 -->
    <PickingOrderPreview
      v-model:visible="pickingDialogVisible"
      :picking-data="pickingData"
      @print-success="handlePrintSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineEmits, onMounted, defineProps, watch } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { usePurchaseOrderStore } from '@/store'
import type { PurchaseOrderRequestDTO, Shop, PurchaseOrder, ShopDataRange } from '@/types/purchaseOrder'
import { formatTime } from '@/utils/format'
// 导入组件
import SearchArea from './NormalPurchaseTab/SearchArea.vue'
import StatusTabs from './NormalPurchaseTab/StatusTabs.vue'
import ActionBar from './NormalPurchaseTab/ActionBar.vue'
import PurchaseOrderTable from './NormalPurchaseTab/PurchaseOrderTable.vue'
import PickingOrderPreview from './UrgentPurchaseTab/PickingOrderPreview.vue'
// 导入任务管理相关
import { useExportTaskStore } from '@/store/modules/exportTask'
import type { ExportTask } from '@/store/modules/exportTask'
import axios from 'axios'
import { getToken } from '@/utils/auth'

const emit = defineEmits(['previewImage', 'viewDetail'])

const props = defineProps({
  shops: {
    type: Array as () => Shop[],
    required: true
  }
})

// 备货单store
const purchaseOrderStore = usePurchaseOrderStore()

// 加载状态
const loading = ref(false)

// 备货单列表
const purchaseOrders = ref<any[]>([])

// 总记录数
const total = ref(0)

// 是否已经执行过搜索
const hasSearched = ref(false)

// 新增：店铺数据范围
const shopRanges = ref<ShopDataRange[]>([])

// 新增：用于存储选中的行
const selectedRows = ref<any[]>([])

// 新增：拣货单相关变量
const pickingDialogVisible = ref(false);
const pickingData = ref<any[]>([]);

// 用于存储标签对应的结果数量
const tagCounts = ref<Record<string, number>>({
  'hotInventoryLack': 0,
  'todayCanDeliver': 0,
  'deliverSoonDelay': 0,
  'deliverDelay': 0,
  'arrivalSoonDelay': 0,
  'arrivalDelay': 0,
  'isTodayPlatformPurchase': 0,
  'isCloseJit': 0
})

// 状态筛选选项卡定义
const statusTabs = ref([
  { label: '全部', value: 'all', count: 0 },
  { label: '待创建', value: 0, count: 0 },
  { label: '待发货', value: 1, count: 0 },
  { label: '已送货', value: 2, count: 0 },
  { label: '已收货', value: 3, count: 0 },
  { label: '抽检全部退回', value: 5, count: 0 },
  { label: '已验收', value: 6, count: 0 },
  { label: '已入库', value: 7, count: 0 },
  { label: '已作废', value: 8, count: 0 },
  { label: '已取消', value: 10, count: 0 },
  { label: '已超时', value: 9, count: 0 }
])

// 当前激活的状态选项卡
const activeStatusTab = ref<string | number>('all')

// 排序选项定义
const sortOptions = ref([
  { 
    label: '备货单创建时间最晚在上', 
    value: 'createdAt_desc'
  },
  { 
    label: '需发货时间最早在上', 
    value: 'expectLatestDeliverTime_asc'
  },
  { 
    label: '需到货时间最早在上', 
    value: 'expectLatestArrivalTime_asc'
  }
])

// 新增：用于UI展示的排序值，保存为字符串形式
const sortDisplayValue = ref(sortOptions.value[0].value)

// 根据选择的排序选项生成实际的排序参数
const getSortObject = (sortValue: string) => {
  const [param, order] = sortValue.split('_');
  return {
    firstOrderByParam: param,
    firstOrderByDesc: order === 'desc' ? 1 : 0
  }
}

// 查询参数
const queryParams = reactive<PurchaseOrderRequestDTO>({
  shopIds: [],
  pageNo: 1,
  pageSize: 10,
  urgencyType: 0, // 普通备货
  // 新增查询参数
  originalPurchaseOrderSnList: [],
  subPurchaseOrderSnList: [],
  productSnList: [],
  purchaseStockType: undefined,
  inventoryRegionList: [],
  productSkcIdList: [],
  settlementType: undefined,
  sourceList: [],
  isSystemAutoPurchaseSource: undefined,
  purchaseTimeFrom: undefined,
  purchaseTimeTo: undefined,
  deliverOrderSnList: [],
  isDelayDeliver: undefined,
  isDelayArrival: undefined,
  expectLatestDeliverTimeFrom: undefined,
  expectLatestDeliverTimeTo: undefined,
  expectLatestArrivalTimeFrom: undefined,
  expectLatestArrivalTimeTo: undefined,
  isFirst: undefined,
  skuLackSnapshot: undefined,
  qcReject: undefined,
  qcOption: undefined,
  lackOrSoldOutTagList: [],
  hotTag: undefined,
  canDeliverStartTime: undefined,
  canDeliverEndTime: undefined,
  productLabelCodeStyle: undefined,
  inboundReturn: undefined,
  inboundReturnCreate: undefined,
  // 快速筛选新增参数
  hotInventoryLack: undefined,
  todayCanDeliver: undefined,
  deliverOrArrivalDelayStatusList: [],
  isTodayPlatformPurchase: undefined,
  isCloseJit: undefined,
  // 新增状态筛选参数
  statusList: [],
  // 排序参数
  oneDimensionSort: undefined
})

// 状态格式化
const formatStatus = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '待创建',
    1: '待发货',
    2: '已送货',
    3: '已收货',
    4: '已拒收',
    5: '已验收，全部退回',
    6: '已验收',
    7: '已入库',
    8: '作废',
    9: '已超时'
  }
  return statusMap[status] || '未知状态'
}

// 状态类型
const getStatusType = (status: number): string => {
  const typeMap: Record<number, string> = {
    0: 'info',
    1: 'warning',
    2: 'primary',
    3: 'success',
    4: 'danger',
    5: 'info',
    6: 'success',
    7: 'success',
    8: 'info',
    9: 'danger'
  }
  return typeMap[status] || 'info'
}

// 查询按钮点击
const handleSearch = () => {
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请选择至少一个店铺')
    return
  }

  // 重置所有标签计数
  Object.keys(tagCounts.value).forEach(key => {
    tagCounts.value[key] = 0;
  });

  queryParams.pageNo = 1
  fetchData()
  hasSearched.value = true
}

// 重置按钮点击
const handleReset = () => {
  // 保存当前选中的店铺信息
  const currentShopIds = [...(queryParams.shopIds || [])]

  // 重置所有查询参数
  queryParams.pageNo = 1
  queryParams.pageSize = 10
  queryParams.originalPurchaseOrderSnList = []
  queryParams.subPurchaseOrderSnList = []
  queryParams.productSnList = []
  queryParams.purchaseStockType = undefined
  queryParams.inventoryRegionList = []
  queryParams.productSkcIdList = []
  queryParams.settlementType = undefined
  queryParams.sourceList = []
  queryParams.isSystemAutoPurchaseSource = undefined
  queryParams.purchaseTimeFrom = undefined
  queryParams.purchaseTimeTo = undefined
  queryParams.deliverOrderSnList = []
  queryParams.isDelayDeliver = undefined
  queryParams.isDelayArrival = undefined
  queryParams.expectLatestDeliverTimeFrom = undefined
  queryParams.expectLatestDeliverTimeTo = undefined
  queryParams.expectLatestArrivalTimeFrom = undefined
  queryParams.expectLatestArrivalTimeTo = undefined
  queryParams.isFirst = undefined
  queryParams.skuLackSnapshot = undefined
  queryParams.qcReject = undefined
  queryParams.qcOption = undefined
  queryParams.lackOrSoldOutTagList = []
  queryParams.hotTag = undefined
  queryParams.canDeliverStartTime = undefined
  queryParams.canDeliverEndTime = undefined
  queryParams.productLabelCodeStyle = undefined
  queryParams.inboundReturn = undefined
  queryParams.inboundReturnCreate = undefined
  // 重置快速筛选参数
  queryParams.hotInventoryLack = undefined
  queryParams.todayCanDeliver = undefined
  queryParams.deliverOrArrivalDelayStatusList = []
  queryParams.isTodayPlatformPurchase = undefined
  queryParams.isCloseJit = undefined
  // 重置状态筛选
  resetStatusFilter()
  // 设置默认排序参数（不重置为undefined）
  // 更新UI显示值
  sortDisplayValue.value = sortOptions.value[0].value
  // 转换为对象格式
  queryParams.oneDimensionSort = getSortObject(sortOptions.value[0].value)

  // 恢复之前选中的店铺信息
  queryParams.shopIds = currentShopIds

  // 重置所有标签计数
  Object.keys(tagCounts.value).forEach(key => {
    tagCounts.value[key] = 0;
  });
}

// 处理每页显示条数变化
const handleSizeChange = (val: number) => {
  queryParams.pageSize = val
  fetchData()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  queryParams.pageNo = val
  fetchData()
}

// 图片预览
const handlePreviewImage = (url: string) => {
  emit('previewImage', url)
}

// 查看详情
const handleViewDetail = (row: PurchaseOrder) => {
  emit('viewDetail', row)
}

// 新增：处理表格选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection;
}

// 新增：处理打印成功的回调
const handlePrintSuccess = (printedItems: any[]) => {
  console.log('打印任务已完成', printedItems)
  // 可以在这里添加其他打印成功后的处理逻辑
}

// 新增：处理批量打印拣货单
const handleBatchPrintPicking = () => {
  if (!selectedRows.value || selectedRows.value.length === 0) {
    ElMessage.warning('请至少选择一项进行打印')
    return
  }

  // 直接设置数据并显示预览组件，数据处理交给组件进行
  pickingData.value = selectedRows.value
  pickingDialogVisible.value = true
}

// 新增：处理单个订单打印拣货单
const handlePrintPicking = (row: any) => {
  // 选中单个订单，然后调用批量打印函数
  selectedRows.value = [row]
  handleBatchPrintPicking()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    // 保存当前的查询参数,用于后续单独查询每个筛选条件的数量
    const currentQueryParams = { ...queryParams }

    // API层已经处理了参数过滤，这里直接传递queryParams
    await purchaseOrderStore.getPurchaseOrderList(queryParams);
    
    // 从store中获取数据
    const storeData = purchaseOrderStore.purchaseOrders
    const storeTotal = purchaseOrderStore.total
    const storeShopRanges = purchaseOrderStore.shopDataRanges // 新增存储店铺范围信息
    purchaseOrders.value = storeData || []
    total.value = storeTotal || 0
    shopRanges.value = storeShopRanges || [] // 存储店铺范围信息

    // 如果有活跃的筛选条件,更新对应的数量
    updateActiveFilterCounts(currentQueryParams);
    
    // 更新状态选项卡数量
    updateStatusTabCounts(total.value);
    
    if (purchaseOrders.value.length === 0 && hasSearched.value) {
      ElMessage.warning('未查询到相关数据')
    }
  } catch (error) {
    console.error('获取普通备货建议失败', error)
    ElMessage.error('获取普通备货建议失败')
    purchaseOrders.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 监听与快速筛选相关的查询参数变化，自动触发搜索
watch(
  () => [
    queryParams.hotInventoryLack,
    queryParams.todayCanDeliver,
    // 使用 JSON.stringify 来深度监听数组变化
    JSON.stringify(queryParams.deliverOrArrivalDelayStatusList),
    queryParams.isTodayPlatformPurchase,
    queryParams.isCloseJit
  ],
  (newValues, oldValues) => {
    // 避免在组件初始化或重置时触发不必要的搜索
    // 只有在值确实发生变化时才搜索
    // 使用 stringify 比较确保数组内容的实际变化被捕捉
    if (JSON.stringify(newValues) !== JSON.stringify(oldValues)) {
       // 确保店铺已选择
       if (queryParams.shopIds && queryParams.shopIds.length > 0) {
           console.log('Normal Purchase Quick filter related queryParams changed, triggering search:', newValues);
           // 直接调用 handleSearch 来确保所有检查和重置逻辑被执行
           handleSearch();
       } else {
           // 如果没有选择店铺，快速筛选变化时不触发搜索，避免错误提示
           console.log('Normal Purchase Quick filter changed but no shop selected, skipping search.');
       }
    }
  },
  { deep: false } // deep: false 因为我们监听的是具体字段和字符串化的数组
);

// 更新活跃筛选条件的数量
const updateActiveFilterCounts = (currentParams: PurchaseOrderRequestDTO) => {
  // 只有在有活跃筛选条件且查询结果不为0时才更新
  if (total.value > 0) {
    // 更新活跃的筛选标签计数
    if (isTagActive('hotInventoryLack')) {
      tagCounts.value['hotInventoryLack'] = total.value;
    }

    if (isTagActive('todayCanDeliver')) {
      tagCounts.value['todayCanDeliver'] = total.value;
    }

    if (isTagActive('deliverSoonDelay')) {
      tagCounts.value['deliverSoonDelay'] = total.value;
    }

    if (isTagActive('deliverDelay')) {
      tagCounts.value['deliverDelay'] = total.value;
    }

    if (isTagActive('arrivalSoonDelay')) {
      tagCounts.value['arrivalSoonDelay'] = total.value;
    }

    if (isTagActive('arrivalDelay')) {
      tagCounts.value['arrivalDelay'] = total.value;
    }

    if (isTagActive('isTodayPlatformPurchase')) {
      tagCounts.value['isTodayPlatformPurchase'] = total.value;
    }

    if (isTagActive('isCloseJit')) {
      tagCounts.value['isCloseJit'] = total.value;
    }
  }
}

// 检查标签是否激活
const isTagActive = (tagValue: string): boolean => {
  switch (tagValue) {
    case 'hotInventoryLack':
      return queryParams.hotInventoryLack === true
    case 'todayCanDeliver':
      return queryParams.todayCanDeliver === true
    case 'deliverSoonDelay':
      return queryParams.deliverOrArrivalDelayStatusList?.includes(101) || false
    case 'deliverDelay':
      return queryParams.deliverOrArrivalDelayStatusList?.includes(102) || false
    case 'arrivalSoonDelay':
      return queryParams.deliverOrArrivalDelayStatusList?.includes(201) || false
    case 'arrivalDelay':
      return queryParams.deliverOrArrivalDelayStatusList?.includes(202) || false
    case 'isTodayPlatformPurchase':
      return queryParams.isTodayPlatformPurchase === true
    case 'isCloseJit':
      return queryParams.isCloseJit === true
    default:
      return false
  }
}

// 处理状态选项卡点击
const handleStatusTabClick = (value: string | number) => {
  // 如果当前已经是激活状态，则不做任何操作
  if (activeStatusTab.value === value) return

  // 更新激活的选项卡
  activeStatusTab.value = value

  // 更新查询参数
  if (value === 'all') {
    queryParams.statusList = []
  } else {
    queryParams.statusList = [value as number]
  }

  // 执行搜索
  handleSearch()
}

// 处理排序选项变化
const handleSortChange = (val: string) => {
  // 更新UI展示值
  sortDisplayValue.value = val
  
  // 将字符串类型的排序选项转换为后端需要的对象格式
  queryParams.oneDimensionSort = getSortObject(val)
  
  // 执行搜索
  handleSearch()
}

// 更新状态选项卡数量
const updateStatusTabCounts = (total: number) => {
  // 如果当前激活的选项卡不是"全部"且有数据，则更新其计数
  if (activeStatusTab.value !== 'all' && total > 0) {
    const activeTab = statusTabs.value.find(tab => tab.value === activeStatusTab.value)
    if (activeTab) {
      activeTab.count = total
    }
  }
}

// 重置状态筛选
const resetStatusFilter = () => {
  activeStatusTab.value = 'all'
  queryParams.statusList = []

  // 重置所有状态选项卡的计数
  statusTabs.value.forEach(tab => {
    tab.count = 0
  })
}

// 更新查询参数
const updateQueryParams = (newParams: PurchaseOrderRequestDTO) => {
  // 检查是否有排序参数，如果是字符串类型，需要转换
  if (newParams.oneDimensionSort && typeof newParams.oneDimensionSort === 'string') {
    // 更新UI显示值
    sortDisplayValue.value = newParams.oneDimensionSort;
    // 转换为对象格式
    newParams.oneDimensionSort = getSortObject(newParams.oneDimensionSort);
  }
  
  // 更新查询参数
  Object.assign(queryParams, newParams)
}

// 新增：处理导出选中数据
const handleExportSelected = async () => {
  try {
    // 检查是否有选中的行
    if (!selectedRows.value || selectedRows.value.length === 0) {
      ElMessage.warning('请至少选择一条记录进行导出')
      return
    }
    
    // 创建导出任务
    const exportTaskStore = useExportTaskStore();
    
    // 获取选中行的店铺备注列表
    const shopRemarks = selectedRows.value
      .map(row => row.shopRemark || '')
      .filter(remark => remark !== '')
      .filter((value, index, self) => self.indexOf(value) === index); // 去重
    
    // 构建文件名: 店铺代号+类型+日期时间
    const shopRemarksStr = shopRemarks.length > 0 
      ? shopRemarks.join('-').slice(0, 20) // 截取前20个字符，避免文件名过长
      : '未知店铺';
      
    const type = queryParams.urgencyType === 1 ? 'JIT' : '备货';
    const dateTimeStr = new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/[\/:]/g, '').replace(/\s+/g, '_'); // 将日期格式化并移除特殊字符
    
    const fileName = `${shopRemarksStr}-${type}-${dateTimeStr}`;
    const task = exportTaskStore.createTask(fileName, fileName);
    
    // 提取选中行的备货单号
    const subPurchaseOrderSnList = selectedRows.value.map(row => row.subPurchaseOrderSn).filter(Boolean)
    
    // 获取当前排序条件，确保是对象格式
    let sortParam = queryParams.oneDimensionSort;
    if (typeof sortParam === 'string') {
      sortParam = getSortObject(sortParam);
    }
    
    // 修改：保留所有原始查询参数，只覆盖备货单号列表
    // 这确保了导出选中行时，仍然保持其他筛选条件，同时限定为选中的备货单
    const exportQueryParams = { ...queryParams };
    exportQueryParams.subPurchaseOrderSnList = subPurchaseOrderSnList;
    exportQueryParams.oneDimensionSort = sortParam;
    
    // 准备导出参数
    const exportParams = {
      queryParams: exportQueryParams,
      taskId: task.id,
      fileName: fileName
    }
    
    // 调用导出API
    await createExportTask(exportParams, task.id);
  } catch (error) {
    console.error('导出选中数据失败', error)
    ElMessage.error('导出选中数据失败')
  }
}

// 新增：处理按条件导出数据
const handleExportByCondition = async (params: { exportType: string, pages: number }) => {
  try {
    // 创建导出任务
    const exportTaskStore = useExportTaskStore();
    
    // 获取查询的店铺备注列表
    let shopRemarks: string[] = [];
    
    // 如果有选择店铺，获取这些店铺的备注
    if (queryParams.shopIds && Array.isArray(queryParams.shopIds) && queryParams.shopIds.length > 0) {
      // 从props.shops中获取店铺备注
      shopRemarks = props.shops
        .filter(shop => {
          // 确保类型一致进行比较，将shop.shopId转换为字符串进行比较
          const shopId = shop.shopId.toString();
          return queryParams.shopIds && queryParams.shopIds.some(id => id.toString() === shopId);
        })
        .map(shop => shop.shopRemark || '')
        .filter(remark => remark !== '');
        
      // 如果从props.shops中没有获取到备注，尝试从已加载的数据中获取
      if (shopRemarks.length === 0 && purchaseOrders.value.length > 0) {
        const uniqueShopRemarks = new Set<string>();
        purchaseOrders.value.forEach(order => {
          if (order.shopRemark && order.shopRemark.trim() !== '') {
            uniqueShopRemarks.add(order.shopRemark);
          }
        });
        shopRemarks = Array.from(uniqueShopRemarks);
      }
    }
    
    // 如果仍然没有获取到店铺备注，尝试从当前页数据中获取
    if (shopRemarks.length === 0 && purchaseOrders.value.length > 0) {
      const uniqueShopRemarks = new Set<string>();
      purchaseOrders.value.forEach(order => {
        if (order.shopRemark && order.shopRemark.trim() !== '') {
          uniqueShopRemarks.add(order.shopRemark);
        }
      });
      shopRemarks = Array.from(uniqueShopRemarks);
    }
    
    // 构建文件名: 店铺代号+类型+日期时间
    const shopRemarksStr = shopRemarks.length > 0 
      ? shopRemarks.join('-').slice(0, 20) // 截取前20个字符，避免文件名过长
      : '未知店铺';
      
    const type = queryParams.urgencyType === 1 ? 'JIT' : '备货';
    const dateTimeStr = new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/[\/:]/g, '').replace(/\s+/g, '_'); // 将日期格式化并移除特殊字符
    
    // 文件名加上导出范围说明
    const exportRangeStr = params.exportType === 'current' ? '当前页' : 
                         (params.exportType === 'custom' ? `前${params.pages}页` : '全部');
    
    const fileName = `${shopRemarksStr}-${type}-${dateTimeStr}(${exportRangeStr})`;
    const task = exportTaskStore.createTask(fileName, fileName);
    
    // 计算导出的实际数据量
    let exportPageSize = queryParams.pageSize
    let exportPageCount = params.pages
    
    // 如果是导出当前页
    if (params.exportType === 'current') {
      exportPageCount = 1
    }
    
    // 获取当前排序条件，确保是对象格式
    let sortParam = queryParams.oneDimensionSort;
    if (typeof sortParam === 'string') {
      sortParam = getSortObject(sortParam);
    }
    
    // 修改：使用完整的查询参数 - 直接使用整个queryParams对象
    // 这样可以确保所有查询条件都被传递到后端
    const exportQueryParams = { ...queryParams };
    
    // 确保oneDimensionSort是对象格式
    exportQueryParams.oneDimensionSort = sortParam;
    
    // 确保pageNo和pageSize正确设置
    exportQueryParams.pageNo = queryParams.pageNo;
    exportQueryParams.pageSize = queryParams.pageSize;
    
    // 准备导出参数
    const exportParams = {
      queryParams: exportQueryParams,
      exportConfig: {
        pageSize: exportPageSize,
        pageCount: exportPageCount
      },
      taskId: task.id,
      fileName: fileName,
      exportType: params.exportType
    }
    
    // 调用导出API
    await createExportTask(exportParams, task.id);
  } catch (error) {
    console.error('按条件导出数据失败', error)
    ElMessage.error('按条件导出数据失败')
  }
}

// 新增：创建导出任务并开始轮询进度
const createExportTask = async (exportParams: any, taskId: string) => {
  try {
    const token = getToken();
    
    // 调用后端创建导出任务API
    const response = await axios({
      method: 'post',
      url: '/api/temu/purchaseOrder/export',
      data: exportParams,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
      }
    });
    
    // 检查响应状态
    if (response.data.code !== 200) {
      const exportTaskStore = useExportTaskStore();
      exportTaskStore.failTask(taskId, response.data.message || '创建导出任务失败');
      ElMessage.error(response.data.message || '导出失败');
      return;
    }
    
    // 获取服务端任务ID
    const serverTaskId = response.data.data.taskId;
    if (!serverTaskId) {
      const exportTaskStore = useExportTaskStore();
      exportTaskStore.failTask(taskId, '获取导出任务ID失败');
      ElMessage.error('获取导出任务ID失败');
      return;
    }
    
    ElMessage.success('导出任务已创建，处理中...');
    
    // 开始轮询任务进度
    pollExportProgress(serverTaskId, taskId);
    
  } catch (error: any) {
    console.error('创建导出任务失败:', error);
    const exportTaskStore = useExportTaskStore();
    exportTaskStore.failTask(taskId, error.message || '导出请求失败');
    ElMessage.error('创建导出任务失败: ' + (error.message || '未知错误'));
  }
}

// 新增：轮询导出进度
const pollExportProgress = async (serverTaskId: string, taskId: string) => {
  try {
    const token = getToken();
    const exportTaskStore = useExportTaskStore();
    let completed = false;
    let failedAttempts = 0; // 连续失败计数
    const MAX_FAILED_ATTEMPTS = 3; // 最大连续失败次数
    
    // 轮询函数
    while (!completed && failedAttempts < MAX_FAILED_ATTEMPTS) {
      try {
        // 请求进度
        const progressResponse = await axios({
          method: 'get',
          url: `/api/temu/purchaseOrder/exportProgress/${serverTaskId}`,
          headers: {
            'Authorization': 'Bearer ' + token
          }
        });
        
        // 检查响应状态
        if (progressResponse.data.code !== 200) {
          failedAttempts++;
          await new Promise(resolve => setTimeout(resolve, 1000)); // 失败后等待1秒重试
          continue;
        }
        
        // 重置失败计数
        failedAttempts = 0;
        
        // 获取进度数据
        const progressData = progressResponse.data.data;
        
        // 更新任务列表中的进度
        exportTaskStore.updateTaskProgress(taskId, progressData.progress);
        
        // 检查任务状态
        if (progressData.status === 'completed') {
          // 任务完成，触发下载
          ElMessage.success('导出完成，正在下载文件...');
          
          // 下载文件
          await downloadExcelFile(serverTaskId);
          
          // 标记完成
          completed = true;
          exportTaskStore.completeTask(taskId);
          break;
        } else if (progressData.status === 'failed') {
          // 任务失败
          ElMessage.error('导出失败: ' + (progressData.message || '未知错误'));
          exportTaskStore.failTask(taskId, progressData.message || '导出失败');
          break;
        }
        
        // 等待一段时间后继续轮询
        // 根据进度设置轮询间隔，进度越大，轮询间隔越长
        const pollInterval = Math.min(2000, 500 + Math.floor(progressData.progress / 10) * 300);
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      } catch (error: any) {
        console.error('轮询进度失败:', error);
        failedAttempts++;
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    // 如果连续失败超过最大次数
    if (failedAttempts >= MAX_FAILED_ATTEMPTS) {
      ElMessage.error('无法获取导出进度，请稍后在系统中查看导出结果');
      exportTaskStore.failTask(taskId, '无法获取导出进度');
    }
    
  } catch (error: any) {
    const exportTaskStore = useExportTaskStore();
    ElMessage.error('轮询导出进度失败: ' + (error.message || '未知错误'));
    exportTaskStore.failTask(taskId, error.message || '轮询进度失败');
  }
}

// 新增：下载Excel文件
const downloadExcelFile = async (serverTaskId: string) => {
  try {
    const token = getToken();
    
    ElMessage.success('正在准备下载，请稍候...');
    
    // 创建下载链接
    const downloadUrl = `/api/temu/purchaseOrder/downloadExcel/${serverTaskId}`;
    
    // 使用fetch API发起带认证头的请求
    const response = await fetch(downloadUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    // 检查响应状态
    if (!response.ok) {
      // 尝试解析错误响应
      try {
        const errorData = await response.json();
        ElMessage.error(errorData.message || `下载失败: ${response.status} ${response.statusText}`);
      } catch (parseError) {
        ElMessage.error(`下载失败: ${response.status} ${response.statusText}`);
      }
      return;
    }
    
    // 获取文件名
    let filename = '备货单数据.xlsx'; // 默认文件名
    
    // 尝试从Content-Disposition获取文件名
    const contentDisposition = response.headers.get('Content-Disposition');
    if (contentDisposition) {
      // 匹配filename或filename*=utf-8''格式
      const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
      const filenameUtf8Regex = /filename\*=UTF-8''([^;]*)/i;
      
      // 先尝试获取UTF-8编码的文件名
      const utf8Match = filenameUtf8Regex.exec(contentDisposition);
      if (utf8Match && utf8Match[1]) {
        try {
          filename = decodeURIComponent(utf8Match[1]);
        } catch (e) {
          console.error('解码文件名失败:', e);
        }
      } else {
        // 尝试获取普通文件名
        const match = filenameRegex.exec(contentDisposition);
        if (match && match[1]) {
          let extractedName = match[1].replace(/['"]/g, '');
          
          // 尝试处理可能的URL编码
          try {
            if (extractedName.includes('%')) {
              extractedName = decodeURIComponent(extractedName);
            }
            filename = extractedName;
          } catch (e) {
            console.error('处理文件名失败:', e);
          }
        }
      }
    }
    
    // 确保文件名以.xlsx结尾
    if (!filename.toLowerCase().endsWith('.xlsx')) {
      filename += '.xlsx';
    }
    
    // 将响应转换为blob
    const blob = await response.blob();
    
    // 创建下载链接并触发下载
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    
    // 清理资源
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    
    ElMessage.success('文件下载已开始，请查看浏览器下载管理器');
  } catch (error: any) {
    console.error('下载文件失败:', error);
    ElMessage.error('下载文件失败: ' + (error.message || '未知错误'));
  }
};

// 组件挂载时初始化数据
onMounted(() => {
  // 设置默认排序为"备货单创建时间最晚在上"
  sortDisplayValue.value = sortOptions.value[0].value
  queryParams.oneDimensionSort = getSortObject(sortOptions.value[0].value)
  
  // 如果有店铺数据，自动执行一次查询
  if (props.shops && props.shops.length > 0) {
    queryParams.shopIds = [props.shops[0].shopId]
    fetchData()
    hasSearched.value = true
  }
})
</script>

<style scoped>
.normal-purchase-tab {
  width: 100%;
}
</style> 