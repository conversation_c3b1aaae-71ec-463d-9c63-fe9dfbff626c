package com.xiao.temu.modules.message.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.message.dto.UnreadCountVO;
import com.xiao.temu.modules.message.entity.SysMessageUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 消息用户关系Mapper接口
 */
@Mapper
public interface SysMessageUserMapper extends BaseMapper<SysMessageUser> {

    /**
     * 批量插入消息用户关系
     *
     * @param messageId 消息ID
     * @param userIds   用户ID数组
     * @return 影响行数
     */
    int batchInsert(@Param("messageId") Long messageId, @Param("userIds") Long[] userIds);

    /**
     * 获取用户未读消息数量
     *
     * @param userId  用户ID
     * @return 未读消息数量统计
     */
    UnreadCountVO getUnreadCount(@Param("userId") Long userId);
} 