import request from '@/utils/request'

/**
 * 通知配置模块API
 */

// 获取通知配置列表
export function getNotificationConfigList() {
  return request({
    url: '/purchase-order/notification/config/list',
    method: 'get'
  })
}

// 获取通知配置详情
export function getNotificationConfig(id: number) {
  return request({
    url: `/purchase-order/notification/config/${id}`,
    method: 'get'
  })
}

// 更新通知配置
export function updateNotificationConfig(data: any) {
  return request({
    url: '/purchase-order/notification/config/update',
    method: 'put',
    data
  })
}

// 切换通知配置启用状态
export function toggleNotificationConfig(id: number, enabled: boolean) {
  return request({
    url: `/purchase-order/notification/config/toggle/${id}/${enabled}`,
    method: 'put'
  })
}

/**
 * 通知记录模块API
 */

// 查询通知记录列表
export function getNotificationRecordList(query: any) {
  return request({
    url: '/purchase-order/notification/record/list',
    method: 'post',
    data: query
  })
}

// 导出通知记录
export function exportNotificationRecord(query: any) {
  return request({
    url: '/purchase-order/notification/record/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

/**
 * 通知测试工具模块API
 */

// 获取通知类型列表
export function getNotificationTypes() {
  return request({
    url: '/purchase-order/notification-manage/notification-types',
    method: 'get'
  })
}

// 获取通知配置
export function getTestNotificationConfig(notificationType: number) {
  return request({
    url: `/purchase-order/notification-manage/notification-config/${notificationType}`,
    method: 'get'
  })
}

// 预览匹配订单
export function previewMatchOrders(data: any) {
  return request({
    url: '/purchase-order/notification-manage/match-orders',
    method: 'post',
    data
  })
}

// 手动发送通知
export function sendNotification(data: any) {
  return request({
    url: '/purchase-order/notification-manage/send-notification',
    method: 'post',
    data
  })
}

// 测试通知匹配
export function testNotificationMatch(notificationType: number, data: any) {
  return request({
    url: `/purchase-order/notification-manage/test-match/${notificationType}`,
    method: 'post',
    data
  })
}

// 通知测试设置
export function saveTestSetting(data: any) {
  return request({
    url: '/purchase-order/notification-manage/test-setting',
    method: 'post',
    data
  })
}

// 获取测试设置
export function getTestSetting() {
  return request({
    url: '/purchase-order/notification-manage/test-setting',
    method: 'get'
  })
} 