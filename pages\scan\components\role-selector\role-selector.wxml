<view class="role-selector">
  <view class="role-grid">
    <view wx:for="{{roles}}" wx:key="roleKey" class="role-item {{currentSelectedRoles.includes(item.roleKey) ? 'selected' : ''}}">
      <custom-checkbox 
        value="{{item.roleKey}}" 
        label="{{item.roleName}}" 
        checked="{{currentSelectedRoles.includes(item.roleKey)}}"
        bindchange="handleSingleCheckboxChange"
      />
    </view>
  </view>
  
  <!-- 选中状态提示 -->
  <view class="selected-roles-tip" wx:if="{{currentSelectedRoles.length > 0}}">
    <text class="tip-text">已选择 {{currentSelectedRoles.length}} 个工序角色</text>
  </view>
</view> 