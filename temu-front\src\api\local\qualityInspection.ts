import request from '@/utils/request'
import type { LocalQualityInspectionRequestParams, LocalQualityInspectionResponse } from '@/types/local/qcInspection'

/**
 * 获取本地抽检结果明细列表
 */
export function getLocalQualityInspectionList(params: LocalQualityInspectionRequestParams) {
  return request({
    url: '/local/qualityInspection/details',
    method: 'post',
    data: params
  })
}

/**
 * 导出本地抽检结果明细数据
 * @param data 导出参数
 * @returns 导出的Excel文件
 */
export function exportLocalQualityInspection(data: any) {
  return request({
    url: '/local/qualityInspection/export',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

/**
 * 获取用户店铺列表
 */
export function getUserShops() {
  return request({
    url: '/local/qualityInspection/shops',
    method: 'get'
  })
} 