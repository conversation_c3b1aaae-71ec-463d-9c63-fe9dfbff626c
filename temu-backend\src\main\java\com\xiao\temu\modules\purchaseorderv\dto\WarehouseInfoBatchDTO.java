package com.xiao.temu.modules.purchaseorderv.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 批量保存仓库信息DTO
 */
@Data
public class WarehouseInfoBatchDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private Boolean success;
    
    private Integer errorCode;
    
    private String errorMsg;
    
    private List<WarehouseInfoDTO> result;
} 