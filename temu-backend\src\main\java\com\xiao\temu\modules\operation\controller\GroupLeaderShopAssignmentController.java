package com.xiao.temu.modules.operation.controller;

import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.common.response.PageResult;
import com.xiao.temu.modules.operation.dto.GroupLeaderShopAssignmentDTO;
import com.xiao.temu.modules.operation.dto.GroupLeaderShopAssignmentQueryDTO;
import com.xiao.temu.modules.operation.entity.GroupLeaderShopAssignment;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.security.annotation.RequiresPermission;
import com.xiao.temu.modules.operation.service.GroupLeaderShopAssignmentService;
import com.xiao.temu.modules.operation.service.GroupMemberService;
import com.xiao.temu.modules.operation.service.OperationGroupService;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.system.service.SysDataPermissionService;
import com.xiao.temu.modules.system.service.UserService;
import com.xiao.temu.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;

/**
 * 组长店铺分配控制器
 */
@RestController
@RequestMapping("/leader/assign")
public class GroupLeaderShopAssignmentController {

    @Autowired
    private GroupLeaderShopAssignmentService assignmentService;

    @Autowired
    private ShopService shopService;

    @Autowired
    private OperationGroupService groupService;

    @Autowired
    private GroupMemberService groupMemberService;

    @Autowired
    private SysDataPermissionService dataPermissionService;
    
    @Autowired
    private UserService userService;

    /**
     * 分页查询分配列表
     */
    @GetMapping("/list")
    @RequiresPermission("operation:leader:list")
    public ApiResponse<PageResult<GroupLeaderShopAssignmentDTO>> list(GroupLeaderShopAssignmentQueryDTO queryDTO) {
        PageResult<GroupLeaderShopAssignmentDTO> pageResult = assignmentService.listAssignments(queryDTO);
        return ApiResponse.success(pageResult);
    }

    /**
     * 获取分配详情
     */
    @GetMapping("/{id}")
    @RequiresPermission("operation:leader:list")
    public ApiResponse<GroupLeaderShopAssignmentDTO> getDetail(@PathVariable("id") Long id) {
        GroupLeaderShopAssignmentDTO dto = assignmentService.getAssignmentById(id);
        return ApiResponse.success(dto);
    }

    /**
     * 新增店铺分配
     */
    @PostMapping
    @RequiresPermission("operation:leader:add")
    public ApiResponse<Boolean> add(@RequestBody GroupLeaderShopAssignment assignment) {
        // 验证参数
        if (assignment.getGroupId() == null || assignment.getShopId() == null || 
            assignment.getUserId() == null || assignment.getAssignBy() == null) {
            return ApiResponse.error("参数不完整");
        }

        // 获取当前用户ID
        Long currentUserId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        
        // 检查是否管理员
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        if (!isAdmin) {
            // 获取用户的最高数据权限
            String permissionType = dataPermissionService.getUserMaxDataPermission(currentUserId);
            
            // 如果不是全部数据权限，则需要检查是否为该组的组长
            if (!"2".equals(permissionType)) {
                // 验证组长权限
                if (!assignmentService.isGroupLeader(assignment.getAssignBy(), assignment.getGroupId())) {
                    return ApiResponse.error("没有组长权限");
                }
            }
        }

        // 验证用户是否为组成员
        if (!groupMemberService.checkUserInGroup(assignment.getGroupId(), assignment.getUserId())) {
            return ApiResponse.error("用户不是该组成员");
        }

        // 验证店铺是否属于该组
        if (!shopService.checkShopInGroup(assignment.getShopId(), assignment.getGroupId())) {
            return ApiResponse.error("店铺不属于该运营组");
        }

        // 设置分配时间
        assignment.setAssignTime(new Date());
        // 设置状态
        assignment.setStatus("0");

        boolean result = assignmentService.addAssignment(assignment);
        return result ? ApiResponse.success(true) : ApiResponse.error("分配失败");
    }

    /**
     * 批量分配店铺
     */
    @PostMapping("/batch")
    @RequiresPermission("operation:leader:add")
    public ApiResponse<Boolean> batchAssign(@RequestParam("groupId") Long groupId,
                                          @RequestParam("shopIds") List<Long> shopIds,
                                          @RequestParam("userId") Long userId,
                                          @RequestParam("permissionType") String permissionType,
                                          @RequestParam("assignBy") Long assignBy) {
        // 获取当前用户ID
        Long currentUserId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        
        // 检查是否管理员
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        if (!isAdmin) {
            // 获取用户的最高数据权限
            String userPermissionType = dataPermissionService.getUserMaxDataPermission(currentUserId);
            
            // 如果不是全部数据权限，则需要检查是否为该组的组长
            if (!"2".equals(userPermissionType)) {
                // 验证组长权限
                if (!assignmentService.isGroupLeader(assignBy, groupId)) {
                    return ApiResponse.error("没有组长权限");
                }
            }
        }

        // 验证用户是否为组成员
        if (!groupMemberService.checkUserInGroup(groupId, userId)) {
            return ApiResponse.error("用户不是该组成员");
        }

        boolean result = assignmentService.batchAssignShops(groupId, shopIds, userId, permissionType, assignBy);
        return result ? ApiResponse.success(true) : ApiResponse.error("批量分配失败");
    }

    /**
     * 修改分配权限
     */
    @PutMapping("/{id}")
    @RequiresPermission("operation:leader:update")
    public ApiResponse<Boolean> update(@PathVariable("id") Long id,
                                     @RequestParam("permissionType") String permissionType,
                                     @RequestParam("assignBy") Long assignBy) {
        // 获取原分配信息
        GroupLeaderShopAssignmentDTO dto = assignmentService.getAssignmentById(id);
        if (dto == null) {
            return ApiResponse.error("分配记录不存在");
        }

        // 获取当前用户ID
        Long currentUserId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        
        // 检查是否管理员
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        if (!isAdmin) {
            // 获取用户的最高数据权限
            String userPermissionType = dataPermissionService.getUserMaxDataPermission(currentUserId);
            
            // 如果不是全部数据权限，则需要检查是否为该组的组长
            if (!"2".equals(userPermissionType)) {
                // 验证组长权限
                if (!assignmentService.isGroupLeader(assignBy, dto.getGroupId())) {
                    return ApiResponse.error("没有组长权限");
                }
            }
        }

        // 更新权限
        GroupLeaderShopAssignment assignment = new GroupLeaderShopAssignment();
        assignment.setId(id);
        assignment.setPermissionType(permissionType);
        assignment.setAssignBy(assignBy);
        assignment.setAssignTime(new Date());

        boolean result = assignmentService.updateAssignment(assignment);
        return result ? ApiResponse.success(true) : ApiResponse.error("更新失败");
    }

    /**
     * 取消分配
     */
    @DeleteMapping("/{id}")
    @RequiresPermission("operation:leader:remove")
    public ApiResponse<Boolean> delete(@PathVariable("id") Long id, @RequestParam("operatorId") Long operatorId) {
        // 获取原分配信息
        GroupLeaderShopAssignmentDTO dto = assignmentService.getAssignmentById(id);
        if (dto == null) {
            return ApiResponse.error("分配记录不存在");
        }

        // 获取当前用户ID
        Long currentUserId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        
        // 检查是否管理员
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        if (!isAdmin) {
            // 获取用户的最高数据权限
            String userPermissionType = dataPermissionService.getUserMaxDataPermission(currentUserId);
            
            // 如果不是全部数据权限，则需要检查是否为该组的组长
            if (!"2".equals(userPermissionType)) {
                // 验证组长权限
                if (!assignmentService.isGroupLeader(operatorId, dto.getGroupId())) {
                    return ApiResponse.error("没有组长权限");
                }
            }
        }

        boolean result = assignmentService.deleteAssignment(id);
        return result ? ApiResponse.success(true) : ApiResponse.error("取消分配失败");
    }

    /**
     * 批量取消分配
     */
    @DeleteMapping("/batch")
    @RequiresPermission("operation:leader:remove")
    public ApiResponse<Boolean> batchDelete(@RequestBody List<Long> ids, @RequestParam("operatorId") Long operatorId) {
        if (ids == null || ids.isEmpty()) {
            return ApiResponse.error("参数不能为空");
        }

        // 获取当前用户ID
        Long currentUserId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        
        // 检查是否管理员
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        // 获取用户的最高数据权限
        String userPermissionType = null;
        if (!isAdmin) {
            userPermissionType = dataPermissionService.getUserMaxDataPermission(currentUserId);
        }
        
        // 如果不是管理员且不是全部数据权限，需要验证每个分配的组长权限
        if (!isAdmin && !"2".equals(userPermissionType)) {
            for (Long id : ids) {
                GroupLeaderShopAssignmentDTO dto = assignmentService.getAssignmentById(id);
                if (dto == null) {
                    continue;
                }

                // 验证组长权限
                if (!assignmentService.isGroupLeader(operatorId, dto.getGroupId())) {
                    return ApiResponse.error("没有组长权限");
                }
            }
        }

        boolean result = assignmentService.batchDeleteAssignments(ids);
        return result ? ApiResponse.success(true) : ApiResponse.error("批量取消分配失败");
    }

    /**
     * 获取用户的所有分配
     */
    @GetMapping("/user/{userId}")
    @RequiresPermission("operation:leader:list")
    public ApiResponse<List<GroupLeaderShopAssignmentDTO>> getUserAssignments(@PathVariable("userId") Long userId) {
        List<GroupLeaderShopAssignmentDTO> list = assignmentService.getUserAssignments(userId);
        return ApiResponse.success(list);
    }

    /**
     * 获取店铺的所有分配
     */
    @GetMapping("/shop/{shopId}")
    @RequiresPermission("operation:leader:list")
    public ApiResponse<List<GroupLeaderShopAssignmentDTO>> getShopAssignments(@PathVariable("shopId") Long shopId) {
        List<GroupLeaderShopAssignmentDTO> list = assignmentService.getShopAssignments(shopId);
        return ApiResponse.success(list);
    }

    /**
     * 获取运营组的所有分配
     */
    @GetMapping("/group/{groupId}")
    @RequiresPermission("operation:leader:list")
    public ApiResponse<List<GroupLeaderShopAssignmentDTO>> getGroupAssignments(@PathVariable("groupId") Long groupId) {
        List<GroupLeaderShopAssignmentDTO> assignments = assignmentService.getGroupAssignments(groupId);
        return ApiResponse.success(assignments);
    }

    /**
     * 获取指定运营组的所有店铺分配数据（不分页）
     */
    @GetMapping("/list/all/{groupId}")
    @RequiresPermission("operation:leader:list")
    public ApiResponse<List<GroupLeaderShopAssignmentDTO>> listAllByGroupId(@PathVariable("groupId") Long groupId) {
        List<GroupLeaderShopAssignmentDTO> assignments = assignmentService.listAllAssignmentsByGroupId(groupId);
        return ApiResponse.success(assignments);
    }

    /**
     * 检查用户是否有店铺权限
     */
    @GetMapping("/check")
    public ApiResponse<Boolean> checkPermission(@RequestParam("userId") Long userId,
                                              @RequestParam("shopId") Long shopId,
                                              @RequestParam(value = "requireWrite", defaultValue = "false") boolean requireWrite) {
        boolean hasPermission = assignmentService.checkShopPermission(userId, shopId, requireWrite);
        return ApiResponse.success(hasPermission);
    }

    /**
     * 获取运营组可分配的店铺列表
     */
    @GetMapping("/assignableShops/{groupId}")
    @RequiresPermission("operation:leader:list")
    public ApiResponse<List<Shop>> getAssignableShops(@PathVariable("groupId") Long groupId) {
        // 验证组是否存在
        if (!groupService.existsById(groupId)) {
            return ApiResponse.error("运营组不存在");
        }
        
        // 获取当前用户ID
        Long userId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        
        // 检查是否管理员
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        // 如果不是管理员，验证用户是否有权限
        if (!isAdmin) {
            // 获取用户的最高数据权限
            String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
            
            // 如果不是全部数据权限，则需要检查是否为该组的组长
            if (!"2".equals(permissionType)) {
                // 验证是否有权限查看该组
                boolean isGroupLeader = assignmentService.isGroupLeader(userId, groupId);
                if (!isGroupLeader) {
                    return ApiResponse.error("无权查看该运营组的店铺");
                }
            }
        }
        
        // 获取运营组下的所有关联店铺，包括那些被移除了belongGroupId的店铺
        List<Shop> shops = shopService.listAllGroupShops(groupId);
        return ApiResponse.success(shops);
    }

    /**
     * 获取运营组的成员列表
     */
    @GetMapping("/groupMembers/{groupId}")
    @RequiresPermission("operation:leader:list")
    public ApiResponse getGroupMembers(@PathVariable("groupId") Long groupId) {
        // 返回组成员列表
        return ApiResponse.success(groupMemberService.getAllMembers(groupId));
    }

    /**
     * 组长创建组员
     */
    @PostMapping("/createMember")
    @RequiresPermission("operation:leader:add")
    public ApiResponse<?> createMember(@RequestBody Map<String, Object> params) {
        // 提取参数
        String username = params.get("username").toString();
        String password = params.get("password").toString();
        String nickName = params.get("nickName") != null ? params.get("nickName").toString() : username;
        Long groupId = Long.valueOf(params.get("groupId").toString());
        
        // 可选参数
        String email = params.get("email") != null ? params.get("email").toString() : null;
        String phone = params.get("phone") != null ? params.get("phone").toString() : null;
        
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getCurrentUserId();
        
        // 验证组长权限
        boolean isAdmin = SecurityUtils.isAdmin();
        
        if (!isAdmin) {
            // 获取用户的最高数据权限
            String permissionType = dataPermissionService.getUserMaxDataPermission(currentUserId);
            
            // 如果不是全部数据权限，则需要检查是否为该组的组长
            if (!"2".equals(permissionType)) {
                // 验证组长权限
                if (!assignmentService.isGroupLeader(currentUserId, groupId)) {
                    return ApiResponse.error("没有组长权限");
                }
            }
        }
        
        try {
            // 校验用户名是否唯一
            if (!userService.checkUsernameUnique(username)) {
                return ApiResponse.error("用户名'" + username + "'已存在");
            }
            
            // 创建用户
            SysUser user = new SysUser();
            user.setUsername(username);
            user.setPassword(password);
            user.setNickName(nickName);
            user.setEmail(email);
            user.setPhone(phone);
            user.setStatus("0"); // 正常状态
            user.setCreateTime(new Date());
            
            // 插入用户
            int rows = userService.insertUser(user);
            if (rows <= 0) {
                return ApiResponse.error("创建用户失败");
            }
            
            Long userId = user.getUserId();
            
            // 分配运营人员角色(角色ID=3)
            Long[] roleIds = new Long[] { 3L }; // 运营人员角色ID
            userService.assignRoles(userId, roleIds);
            
            // 添加到组
            int result = groupMemberService.addMember(groupId, userId);
            if (result <= 0) {
                return ApiResponse.error("添加到运营组失败");
            }
            
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("userId", userId);
            resultMap.put("groupId", groupId);
            
            return ApiResponse.success(resultMap);
        } catch (Exception e) {
            return ApiResponse.error("创建组员失败: " + e.getMessage());
        }
    }
} 