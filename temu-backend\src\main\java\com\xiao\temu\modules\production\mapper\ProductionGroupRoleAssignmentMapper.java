package com.xiao.temu.modules.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.production.dto.ProductionGroupRoleAssignmentDTO;
import com.xiao.temu.modules.production.entity.ProductionGroupRoleAssignment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 生产组角色分配Mapper接口
 */
@Mapper
public interface ProductionGroupRoleAssignmentMapper extends BaseMapper<ProductionGroupRoleAssignment> {

    /**
     * 分页查询角色分配列表
     *
     * @param page 分页参数
     * @param groupId 生产组ID
     * @param userId 用户ID，可选
     * @param roleId 角色ID，可选
     * @return 分页数据
     */
    IPage<ProductionGroupRoleAssignmentDTO> selectAssignmentList(
            Page<ProductionGroupRoleAssignmentDTO> page,
            @Param("groupId") Long groupId,
            @Param("userId") Long userId,
            @Param("roleId") Long roleId);

    /**
     * 查询用户在生产组中的角色ID列表
     *
     * @param groupId 生产组ID
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> selectUserRoleIds(@Param("groupId") Long groupId, @Param("userId") Long userId);
    
    /**
     * 批量查询多个用户的角色ID列表
     *
     * @param groupId 生产组ID
     * @param userIds 用户ID列表
     * @return 角色ID数据列表，包含userId和roleId字段
     */
    List<Map<String, Object>> batchSelectUserRoleIds(@Param("groupId") Long groupId, @Param("userIds") List<Long> userIds);

    /**
     * 批量查询多个用户的角色分配信息
     *
     * @param groupId 生产组ID
     * @param userIds 用户ID列表
     * @return 角色分配详情列表
     */
    List<ProductionGroupRoleAssignmentDTO> batchSelectUserRoleAssignments(@Param("groupId") Long groupId, @Param("userIds") List<Long> userIds);

    /**
     * 查询角色分配详情
     *
     * @param id 主键ID
     * @return 角色分配详情
     */
    ProductionGroupRoleAssignmentDTO selectAssignmentById(@Param("id") Long id);

    /**
     * 删除用户在生产组中的所有角色
     *
     * @param groupId 生产组ID
     * @param userId 用户ID
     * @return 结果
     */
    int deleteUserRoles(@Param("groupId") Long groupId, @Param("userId") Long userId);
    
    /**
     * 删除生产组的所有角色分配
     *
     * @param groupId 生产组ID
     * @return 结果
     */
    int deleteAllGroupAssignments(@Param("groupId") Long groupId);
    
    /**
     * 检查用户是否拥有指定角色
     *
     * @param groupId 生产组ID
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 结果
     */
    int checkUserHasRole(@Param("groupId") Long groupId, @Param("userId") Long userId, @Param("roleId") Long roleId);
} 