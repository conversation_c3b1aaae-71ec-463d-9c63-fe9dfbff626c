package com.xiao.temu.modules.purchaseorderv.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.common.constant.PurchaseOrderNotificationConstants;
import com.xiao.temu.modules.purchaseorderv.dto.NotificationMatchDTO;
import com.xiao.temu.modules.purchaseorderv.dto.NotificationQueryDTO;
import com.xiao.temu.modules.purchaseorderv.dto.NotificationTriggerDTO;
import com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderNotification;
import com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderNotificationConfig;
import com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderV;
import com.xiao.temu.modules.purchaseorderv.mapper.PurchaseOrderNotificationConfigMapper;
import com.xiao.temu.modules.purchaseorderv.mapper.PurchaseOrderNotificationMapper;
import com.xiao.temu.modules.purchaseorderv.mapper.PurchaseOrderVMapper;
import com.xiao.temu.modules.purchaseorderv.service.PurchaseOrderNotificationConfigService;
import com.xiao.temu.modules.purchaseorderv.service.PurchaseOrderNotificationService;
import com.xiao.temu.modules.purchaseorderv.vo.NotificationConfigVO;
import com.xiao.temu.modules.purchaseorderv.vo.NotificationMatchOrderVO;
import com.xiao.temu.modules.purchaseorderv.vo.NotificationRecordVO;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.shop.mapper.ShopMapper;
import com.xiao.temu.modules.operation.mapper.GroupMemberMapper;
import com.xiao.temu.modules.operation.entity.GroupMember;
import com.xiao.temu.modules.operation.mapper.OperationGroupMapper;
import com.xiao.temu.modules.operation.entity.OperationGroup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 备货单通知配置服务实现类
 */
@Slf4j
@Service
public class PurchaseOrderNotificationConfigServiceImpl implements PurchaseOrderNotificationConfigService {

    @Autowired
    private PurchaseOrderNotificationConfigMapper configMapper;
    
    @Autowired
    private PurchaseOrderNotificationMapper notificationMapper;
    
    @Autowired
    private PurchaseOrderVMapper purchaseOrderVMapper;
    
    @Autowired
    private PurchaseOrderNotificationService notificationService;
    
    @Autowired
    private ShopMapper shopMapper;
    
    @Autowired
    private GroupMemberMapper groupMemberMapper;
    
    @Autowired
    private OperationGroupMapper operationGroupMapper;
    
    @Override
    public List<NotificationConfigVO> getAllNotificationConfigs() {
        // 查询所有通知配置
        LambdaQueryWrapper<PurchaseOrderNotificationConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(PurchaseOrderNotificationConfig::getNotificationType);
        List<PurchaseOrderNotificationConfig> configs = configMapper.selectList(wrapper);
        
        // 转换为VO对象
        return configs.stream().map(config -> {
            NotificationConfigVO vo = new NotificationConfigVO();
            BeanUtils.copyProperties(config, vo);
            return vo;
        }).collect(Collectors.toList());
    }
    
    @Override
    public NotificationConfigVO getNotificationConfigById(Long id) {
        PurchaseOrderNotificationConfig config = configMapper.selectById(id);
        if (config == null) {
            return null;
        }
        
        NotificationConfigVO vo = new NotificationConfigVO();
        BeanUtils.copyProperties(config, vo);
        return vo;
    }
    
    @Override
    public PurchaseOrderNotificationConfig getConfigByType(Integer notificationType) {
        return configMapper.getByNotificationType(notificationType);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNotificationConfig(PurchaseOrderNotificationConfig config) {
        // 查询原配置
        PurchaseOrderNotificationConfig existConfig = configMapper.selectById(config.getId());
        if (existConfig == null) {
            return false;
        }
        
        // 更新配置
        config.setUpdateTime(new Date());
        return configMapper.updateById(config) > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleNotificationConfig(Long id, Boolean enabled) {
        PurchaseOrderNotificationConfig config = configMapper.selectById(id);
        if (config == null) {
            return false;
        }
        
        // 直接设置布尔值，无需转换
        config.setEnabled(enabled);
        config.setUpdateTime(new Date());
        return configMapper.updateById(config) > 0;
    }
    
    @Override
    public IPage<NotificationRecordVO> getNotificationRecords(NotificationQueryDTO queryDTO) {
        // 构建分页对象
        Page<PurchaseOrderNotification> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        
        // 构建查询条件
        LambdaQueryWrapper<PurchaseOrderNotification> wrapper = new LambdaQueryWrapper<>();
        
        if (queryDTO.getShopId() != null) {
            wrapper.eq(PurchaseOrderNotification::getShopId, queryDTO.getShopId());
        }
        
        if (queryDTO.getSubPurchaseOrderSn() != null && !queryDTO.getSubPurchaseOrderSn().isEmpty()) {
            wrapper.eq(PurchaseOrderNotification::getSubPurchaseOrderSn, queryDTO.getSubPurchaseOrderSn());
        }
        
        if (queryDTO.getNotificationTypes() != null && !queryDTO.getNotificationTypes().isEmpty()) {
            wrapper.in(PurchaseOrderNotification::getNotificationType, queryDTO.getNotificationTypes());
        }
        
        if (queryDTO.getNotifyStatus() != null) {
            wrapper.eq(PurchaseOrderNotification::getNotifyStatus, queryDTO.getNotifyStatus());
        }
        
        // 按创建时间倒序排序
        wrapper.orderByDesc(PurchaseOrderNotification::getCreateTime);
        
        // 查询数据
        IPage<PurchaseOrderNotification> result = notificationMapper.selectPage(page, wrapper);
        
        // 转换为VO
        IPage<NotificationRecordVO> voPage = new Page<>(result.getCurrent(), result.getSize(), result.getTotal());
        List<NotificationRecordVO> records = new ArrayList<>();
        
        for (PurchaseOrderNotification notification : result.getRecords()) {
            NotificationRecordVO vo = new NotificationRecordVO();
            BeanUtils.copyProperties(notification, vo);
            
            // 设置最大通知次数
            PurchaseOrderNotificationConfig config = configMapper.getByNotificationType(notification.getNotificationType());
            if (config != null) {
                vo.setMaxNotifyCount(config.getMaxNotifyCount());
            } else {
                vo.setMaxNotifyCount(PurchaseOrderNotificationConstants.MAX_NOTIFY_COUNT);
            }
            
            // 设置通知类型名称
            switch (notification.getNotificationType()) {
                case PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_SOON_OVERDUE:
                    vo.setNotificationTypeName("JIT即将逾期通知");
                    break;
                case PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_OVERDUE:
                    vo.setNotificationTypeName("JIT已逾期通知");
                    break;
                case PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_DELIVERED:
                    vo.setNotificationTypeName("普通备货未发货通知");
                    break;
                case PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_RECEIVED:
                    vo.setNotificationTypeName("普通备货未到货通知");
                    break;
                default:
                    vo.setNotificationTypeName("未知通知类型");
                    break;
            }
            
            // 查询订单信息
            PurchaseOrderV order = purchaseOrderVMapper.getByShopIdAndOrderSn(notification.getShopId(), notification.getSubPurchaseOrderSn());
            if (order != null) {
                vo.setProductName(order.getProductName());
            }
            
            // 查询店铺名称
            Shop shop = shopMapper.selectById(notification.getShopId());
            if (shop != null) {
                vo.setShopName(shop.getShopName());
            } else {
                vo.setShopName("未知店铺");
            }
            
            // 查询运营组名称
            if (notification.getGroupId() != null) {
                // 使用OperationGroupMapper获取运营组名称
                OperationGroup group = operationGroupMapper.selectById(notification.getGroupId());
                if (group != null) {
                    vo.setGroupName(group.getGroupName());
                } else {
                    vo.setGroupName("未知运营组");
                }
            } else {
                vo.setGroupName("未分配运营组");
            }
            
            records.add(vo);
        }
        
        voPage.setRecords(records);
        return voPage;
    }
    
    @Override
    public IPage<NotificationMatchOrderVO> previewMatchOrders(NotificationMatchDTO matchDTO) {
        // 构建分页对象
        Page<Object> page = new Page<>(matchDTO.getPageNum(), matchDTO.getPageSize());
        
        // 获取配置
        PurchaseOrderNotificationConfig config = getConfigByType(matchDTO.getNotificationType());
        if (config == null || !config.getEnabled()) {
            // 如果配置不存在或被禁用，则返回空结果
            IPage<NotificationMatchOrderVO> emptyPage = new Page<>(matchDTO.getPageNum(), matchDTO.getPageSize());
            emptyPage.setRecords(new ArrayList<>());
            emptyPage.setTotal(0);
            return emptyPage;
        }
        
        // 根据通知类型查询符合条件的订单
        List<PurchaseOrderV> matchedOrders = new ArrayList<>();
        Date now = new Date();
        
        switch (matchDTO.getNotificationType()) {
            case PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_SOON_OVERDUE:
                // JIT即将逾期：到货时间在未来24小时内
                Date endTime = DateUtils.addHours(now, config.getJitTriggerHours());
                matchedOrders = purchaseOrderVMapper.findJitOrdersAboutToExpire(now, endTime);
                break;
                
            case PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_OVERDUE:
                // JIT已逾期：昨天开始往前三天的逾期订单
                Date yesterday = DateUtils.addDays(now, -1);
                Date startTime = DateUtils.addDays(yesterday, -3);
                matchedOrders = purchaseOrderVMapper.findExpiredJitOrders(startTime, yesterday);
                break;
                
            case PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_DELIVERED:
                // 普通备货未发货：创建时间超过5天仍未发货
                Date createdBefore = DateUtils.addDays(now, -config.getNormalTriggerDays());
                matchedOrders = purchaseOrderVMapper.findLongTimeNotShippedOrders(createdBefore);
                break;
                
            case PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_RECEIVED:
                // 普通备货未到货：发货时间超过5天仍未收货
                Date shippedBefore = DateUtils.addDays(now, -config.getNormalTriggerDays());
                matchedOrders = purchaseOrderVMapper.findShippedButNotReceivedOrders(shippedBefore);
                break;
                
            default:
                break;
        }
        
        // 应用筛选条件
        if (matchDTO.getShopId() != null) {
            matchedOrders = matchedOrders.stream()
                    .filter(order -> order.getShopId().equals(matchDTO.getShopId()))
                    .collect(Collectors.toList());
        }
        
        if (matchDTO.getOrderSnLike() != null && !matchDTO.getOrderSnLike().isEmpty()) {
            matchedOrders = matchedOrders.stream()
                    .filter(order -> order.getSubPurchaseOrderSn().contains(matchDTO.getOrderSnLike()))
                    .collect(Collectors.toList());
        }
        
        if (matchDTO.getProductNameLike() != null && !matchDTO.getProductNameLike().isEmpty()) {
            matchedOrders = matchedOrders.stream()
                    .filter(order -> order.getProductName() != null && order.getProductName().contains(matchDTO.getProductNameLike()))
                    .collect(Collectors.toList());
        }
        
        if (matchDTO.getSupplierId() != null) {
            matchedOrders = matchedOrders.stream()
                    .filter(order -> order.getSupplierId().equals(matchDTO.getSupplierId()))
                    .collect(Collectors.toList());
        }
        
        // 计算总数
        int total = matchedOrders.size();
        
        // 分页
        int start = (matchDTO.getPageNum() - 1) * matchDTO.getPageSize();
        int end = Math.min(start + matchDTO.getPageSize(), total);
        
        if (start < total) {
            matchedOrders = matchedOrders.subList(start, end);
        } else {
            matchedOrders = new ArrayList<>();
        }
        
        // 转换为VO
        List<NotificationMatchOrderVO> voList = matchedOrders.stream().map(order -> {
            NotificationMatchOrderVO vo = new NotificationMatchOrderVO();
            BeanUtils.copyProperties(order, vo);
            
            // 设置匹配原因
            vo.setMatchReason(getMatchReason(order, matchDTO.getNotificationType(), config));
            
            return vo;
        }).collect(Collectors.toList());
        
        // 返回结果
        IPage<NotificationMatchOrderVO> resultPage = new Page<>(matchDTO.getPageNum(), matchDTO.getPageSize());
        resultPage.setRecords(voList);
        resultPage.setTotal(total);
        
        return resultPage;
    }
    
    /**
     * 获取订单匹配原因
     */
    private String getMatchReason(PurchaseOrderV order, Integer notificationType, PurchaseOrderNotificationConfig config) {
        Date now = new Date();
        
        switch (notificationType) {
            case PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_SOON_OVERDUE:
                // JIT即将逾期
                long hoursDiff = (order.getExpectLatestArrivalTime().getTime() - now.getTime()) / (60 * 60 * 1000);
                return "JIT备货单预计将在" + hoursDiff + "小时后到达最晚到货时间";
                
            case PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_OVERDUE:
                // JIT已逾期
                long daysDiff1 = (now.getTime() - order.getExpectLatestArrivalTime().getTime()) / (24 * 60 * 60 * 1000);
                return "JIT备货单已逾期" + daysDiff1 + "天未收货";
                
            case PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_DELIVERED:
                // 普通备货未发货
                long daysDiff2 = (now.getTime() - order.getPurchaseTime().getTime()) / (24 * 60 * 60 * 1000);
                return "普通备货单创建" + daysDiff2 + "天仍未发货";
                
            case PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_RECEIVED:
                // 普通备货未到货
                long daysDiff3 = (now.getTime() - order.getDeliverTime().getTime()) / (24 * 60 * 60 * 1000);
                return "普通备货单发货" + daysDiff3 + "天仍未收货";
                
            default:
                return "未知原因";
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int manualTriggerNotification(NotificationTriggerDTO triggerDTO) {
        // 查询对应的通知配置
        PurchaseOrderNotificationConfig config = getConfigByType(triggerDTO.getNotificationType());
        if (config == null || !config.getEnabled()) {
            return 0;
        }
        
        // 首先获取全部符合通知类型条件的备货单
        List<PurchaseOrderV> allMatchedOrders = new ArrayList<>();
        Date now = new Date();
        
        // 根据通知类型执行相应的查询，与previewMatchOrders方法保持一致
        switch (triggerDTO.getNotificationType()) {
            case PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_SOON_OVERDUE:
                // JIT即将逾期：到货时间在未来24小时内
                Date endTime = DateUtils.addHours(now, config.getJitTriggerHours() != null ? 
                        config.getJitTriggerHours() : PurchaseOrderNotificationConstants.JIT_SOON_OVERDUE_HOURS);
                allMatchedOrders = purchaseOrderVMapper.findJitOrdersAboutToExpire(now, endTime);
                break;
                
            case PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_OVERDUE:
                // JIT已逾期：昨天开始往前三天的逾期订单
                Date yesterday = DateUtils.addDays(now, -1);
                Date startTime = DateUtils.addDays(yesterday, -3);  // 三天前
                allMatchedOrders = purchaseOrderVMapper.findExpiredJitOrders(startTime, yesterday);
                break;
                
            case PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_DELIVERED:
                // 普通备货未发货：创建时间超过5天仍未发货
                Date createdBefore = DateUtils.addDays(now, -config.getNormalTriggerDays());
                allMatchedOrders = purchaseOrderVMapper.findLongTimeNotShippedOrders(createdBefore);
                break;
                
            case PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_RECEIVED:
                // 普通备货未到货：发货时间超过5天仍未收货
                Date shippedBefore = DateUtils.addDays(now, -config.getNormalTriggerDays());
                allMatchedOrders = purchaseOrderVMapper.findShippedButNotReceivedOrders(shippedBefore);
                break;
                
            default:
                log.warn("不支持的通知类型: {}", triggerDTO.getNotificationType());
                return 0;
        }
        
        // 从全部匹配订单中筛选出用户指定的订单号
        Set<String> targetOrderSns = new HashSet<>(triggerDTO.getOrderSnList());
        List<PurchaseOrderV> orders = allMatchedOrders.stream()
                .filter(order -> targetOrderSns.contains(order.getSubPurchaseOrderSn()))
                .collect(Collectors.toList());
        
        log.info("通知触发: 找到{}个符合条件的备货单，其中{}个为用户选择的订单", 
                allMatchedOrders.size(), orders.size());
        
        if (orders.isEmpty()) {
            return 0;
        }
        
        // 设置测试标志
        if (triggerDTO.getSkipHoursCheck() != null && triggerDTO.getSkipHoursCheck()) {
            notificationService.setSkipHoursCheck(true);
        }
        
        if (triggerDTO.getSkipDaysCheck() != null && triggerDTO.getSkipDaysCheck()) {
            notificationService.setSkipDaysCheck(true);
        }
        
        if (triggerDTO.getSkipNotifyCountCheck() != null && triggerDTO.getSkipNotifyCountCheck()) {
            notificationService.setSkipNotifyCountCheck(true);
        }
        
        if (triggerDTO.getMockCurrentTime() != null && !triggerDTO.getMockCurrentTime().isEmpty()) {
            notificationService.setMockCurrentTime(triggerDTO.getMockCurrentTime());
        }
        
        try {
            // 使用批量模式（单个处理模式已不再支持）
                    switch (triggerDTO.getNotificationType()) {
                        case PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_SOON_OVERDUE:
                    notificationService.processJitSoonOverdueNotification();
                            break;
                            
                        case PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_OVERDUE:
                    notificationService.processJitOverdueNotification();
                            break;
                            
                        case PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_DELIVERED:
                    notificationService.processNormalNotDeliveredNotification();
                            break;
                            
                        case PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_RECEIVED:
                    notificationService.processNormalNotReceivedNotification();
                            break;
                            
                        default:
                            log.warn("不支持的通知类型: {}", triggerDTO.getNotificationType());
                            break;
                    }
                    
            // 返回订单数量作为成功数量
            return orders.size();
        } finally {
            // 重置测试标志
            notificationService.resetTestFlags();
        }
    }
} 