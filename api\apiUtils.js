/**
 * API请求通用处理工具
 */

/**
 * 获取存储的token
 * @returns {string} 用户token
 */
const getToken = () => {
  return wx.getStorageSync('token') || '';
};

/**
 * 处理API响应结果
 * @param {Object} res - API响应数据
 * @returns {Promise} 处理后的Promise
 */
const handleApiResponse = (res) => {
  // 如果请求失败
  if (res.statusCode !== 200) {
    // 尝试解析响应数据
    if (res.data && typeof res.data === 'object') {
      return Promise.reject(res.data);
    }
    // 如果没有可用的响应数据，创建一个错误对象
    return Promise.reject({
      code: res.statusCode,
      message: '网络请求失败',
      data: null
    });
  }

  const { code, message, data } = res.data;

  // 成功状态
  if (code === 200) {
    return Promise.resolve(data);
  }
  // 账号密码错误
  else if (code === 401) {
    // 直接返回完整错误对象，包含code和message
    return Promise.reject(res.data);
  }
  // 系统异常
  else if (code === 500) {
    wx.showToast({
      title: '系统异常，请稍后再试',
      icon: 'none',
      duration: 2000
    });
    return Promise.reject(res.data);
  }
  // 业务异常
  else {
    wx.showToast({
      title: message || '操作失败',
      icon: 'none',
      duration: 2000
    });
    return Promise.reject(res.data);
  }
};

/**
 * 发送API请求
 * @param {string} url - 请求地址
 * @param {Object} data - 请求参数(POST请求的body)
 * @param {string} method - 请求方法，默认POST
 * @param {Object} queryParams - GET请求的查询参数
 * @returns {Promise} 请求结果Promise
 */
const request = (url, data = {}, method = 'POST', queryParams = {}) => {
  return new Promise((resolve, reject) => {
    // 构建请求头
    const header = {
      'Content-Type': 'application/json'
    };
    
    // 只有非登录接口才需要携带token
    if (!url.includes('/auth/login')) {
      header.Authorization = getToken();
    }
    
    // 处理GET请求的查询参数
    let finalUrl = url;
    if (method.toUpperCase() === 'GET' && Object.keys(queryParams).length > 0) {
      const queryString = Object.keys(queryParams)
        .map(key => `${key}=${encodeURIComponent(queryParams[key])}`)
        .join('&');
      finalUrl = `${url}?${queryString}`;
    }
    
    wx.request({
      url: finalUrl,
      data: method.toUpperCase() === 'GET' ? {} : data,
      method,
      header,
      success: (res) => {
        handleApiResponse(res)
          .then(resolve)
          .catch(reject);
      },
      fail: (err) => {
        wx.showToast({
          title: '网络请求失败',
          icon: 'none',
          duration: 2000
        });
        // 返回一个标准格式的错误对象
        reject({
          code: -1, // 自定义错误码
          message: '网络请求失败',
          data: null,
          originalError: err
        });
      }
    });
  });
};

module.exports = {
  request,
  getToken
}; 