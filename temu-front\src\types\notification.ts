// 通知类型枚举
export enum NotificationType {
  JIT_SOON_EXPIRE = 1, // JIT备货单到货即将逾期
  JIT_EXPIRED = 2, // JIT备货单到货已逾期
  NORMAL_NOT_DELIVERED = 3, // 普通备货未发货
  NORMAL_NOT_RECEIVED = 4 // 普通备货未到货
}

// 通知状态枚举
export enum NotifyStatus {
  PENDING = 0, // 待通知
  NOTIFYING = 1, // 通知中
  COMPLETED = 2 // 已通知完成
}

// 通知配置接口
export interface NotificationConfig {
  id: number; // 配置ID
  notificationType: NotificationType; // 通知类型
  notificationName: string; // 通知名称
  enabled: boolean; // 是否启用
  checkInterval: number; // 检查间隔(小时)
  normalTriggerDays: number; // 普通备货触发天数
  jitTriggerHours: number; // JIT备货触发小时数
  maxNotifyCount: number; // 最大通知次数
  notifyIntervalHours: number; // 通知间隔(小时)
  templateCode: string; // 消息模板代码
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
}

// 通知记录接口
export interface NotificationRecord {
  id: number; // 记录ID
  shopId: number; // 店铺ID
  shopName?: string; // 店铺名称
  subPurchaseOrderSn: string; // 备货单号
  notificationType: NotificationType; // 通知类型
  notifyCount: number; // 通知次数
  lastNotifyTime: string; // 最后通知时间
  notifyStatus: NotifyStatus; // 通知状态
  groupId: number; // 通知的运营组ID
  groupName?: string; // 运营组名称
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
}

// 通知记录查询条件
export interface NotificationRecordQuery {
  shopId?: number; // 店铺ID
  subPurchaseOrderSn?: string; // 备货单号
  notificationType?: NotificationType; // 通知类型
  notifyStatus?: NotifyStatus; // 通知状态
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
  pageNum: number; // 当前页码
  pageSize: number; // 每页条数
}

// 匹配订单接口
export interface MatchOrder {
  id: number; // 记录ID
  shopId: number; // 店铺ID
  shopName: string; // 店铺名称
  subPurchaseOrderSn: string; // 备货单号
  productName: string; // 商品名称
  supplierName: string; // 供应商名称
  purchaseTime: string; // 备货单创建时间
  deliverTime: string | null; // 发货时间
  expectLatestDeliverTime: string | null; // 预计最晚发货时间
  expectLatestArrivalTime: string | null; // 预计最晚到货时间
  status: number; // 备货单状态
  purchaseStockType: number; // 备货类型
  receiveTime: string | null; // 实际收货时间
  daysOrHours: number; // 天数或小时数
  reason: string; // 匹配原因
}

// 通知测试设置接口
export interface NotificationTestSetting {
  mockCurrentTime?: string; // 模拟当前时间
  skipHoursCheck?: boolean; // 跳过小时检查
  skipDaysCheck?: boolean; // 跳过天数检查
  skipNotifyCountCheck?: boolean; // 跳过通知次数检查
  batchSend?: boolean; // 使用批量发送模式
}

// 通知类型名称映射
export const NOTIFICATION_TYPE_MAP = {
  [NotificationType.JIT_SOON_EXPIRE]: 'JIT备货单到货即将逾期通知',
  [NotificationType.JIT_EXPIRED]: 'JIT备货单到货已逾期通知',
  [NotificationType.NORMAL_NOT_DELIVERED]: '普通备货未发货通知',
  [NotificationType.NORMAL_NOT_RECEIVED]: '普通备货未到货通知'
}

// 通知状态名称映射
export const NOTIFY_STATUS_MAP = {
  [NotifyStatus.PENDING]: '待通知',
  [NotifyStatus.NOTIFYING]: '通知中',
  [NotifyStatus.COMPLETED]: '已完成'
} 