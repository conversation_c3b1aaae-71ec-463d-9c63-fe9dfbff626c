package com.xiao.temu.modules.system.controller;

import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.modules.system.dto.SysDataPermissionDTO;
import com.xiao.temu.security.annotation.RequiresPermission;
import com.xiao.temu.modules.system.service.SysDataPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据权限控制器
 */
@Slf4j
@RestController
@RequestMapping("/system/dataPermission")
@RequiredArgsConstructor
public class DataPermissionController {

    private final SysDataPermissionService dataPermissionService;

    /**
     * 获取数据权限列表
     */
    @GetMapping("/list")
    @RequiresPermission("system:dataPermission:list")
    public ApiResponse<List<SysDataPermissionDTO>> list() {
        List<SysDataPermissionDTO> list = dataPermissionService.listAll();
        return ApiResponse.success(list);
    }

    /**
     * 获取角色数据权限
     */
    @GetMapping("/{roleId}")
    @RequiresPermission("system:dataPermission:query")
    public ApiResponse<SysDataPermissionDTO> getInfo(@PathVariable Long roleId) {
        SysDataPermissionDTO permission = dataPermissionService.getByRoleId(roleId);
        return ApiResponse.success(permission);
    }

    /**
     * 设置角色数据权限
     */
    @PostMapping
    @RequiresPermission("system:dataPermission:edit")
    public ApiResponse<?> setPermission(@Validated @RequestBody SysDataPermissionDTO dataPermission) {
        int rows = dataPermissionService.setDataPermission(dataPermission);
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("设置数据权限失败");
    }

    /**
     * 获取用户数据权限
     */
    @GetMapping("/user/{userId}")
    @RequiresPermission("system:dataPermission:query")
    public ApiResponse<List<SysDataPermissionDTO>> getUserPermissions(@PathVariable Long userId) {
        List<SysDataPermissionDTO> permissions = dataPermissionService.getByUserId(userId);
        return ApiResponse.success(permissions);
    }
    
    /**
     * 获取当前用户最高数据权限类型
     */
    @GetMapping("/user/current/maxType")
    public ApiResponse<String> getCurrentUserMaxPermissionType() {
        Long userId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        String type = dataPermissionService.getUserMaxDataPermission(userId);
        return ApiResponse.success(type);
    }
} 