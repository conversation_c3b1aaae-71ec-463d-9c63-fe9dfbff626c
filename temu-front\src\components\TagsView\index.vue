<template>
  <div class="tags-view-container">
    <el-scrollbar class="tags-scrollbar">
      <el-tabs
        v-model="activeTab"
        type="card"
        @tab-click="handleTabClick"
        @tab-remove="removeTab"
        closable
        class="demo-tabs"
        @edit="handleTabEdit"
      >
        <el-tab-pane
          v-for="item in visitedViews"
          :key="item.path"
          :label="item.title"
          :name="item.path"
          :closable="!item.affix"
        >
        </el-tab-pane>
      </el-tabs>
    </el-scrollbar>
    <div class="tags-view-actions">
      <el-dropdown @command="handleCommand" trigger="click">
        <span class="action-icon">
          <el-icon><arrow-down /></el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="closeOthers">关闭其他</el-dropdown-item>
            <el-dropdown-item command="closeAll">关闭所有</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAppStore } from '@/store/modules/app'
import { ArrowDown } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'

interface TagView {
  path: string
  title: string
  name: string
  affix: boolean
}

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()

const visitedViews = ref<TagView[]>([])
const activeTab = ref('')

// 添加标签
const addVisitedView = (view: any) => {
  // 只处理有标题的路由
  if (!view.meta?.title) return
  
  // 检查是否已存在
  if (visitedViews.value.some(v => v.path === view.path)) return
  
  // 添加新标签
  visitedViews.value.push({
    path: view.path,
    title: view.meta?.title || 'unknown',
    name: view.name,
    affix: view.meta?.affix || false
  })
}

// 移除标签
const removeVisitedView = (view: any) => {
  const index = visitedViews.value.findIndex(v => v.path === view.path)
  if (index !== -1) {
    visitedViews.value.splice(index, 1)
  }
}

// 处理标签点击
const handleTabClick = (tab: any) => {
  router.push(tab.props.name)
}

// 移除标签
const removeTab = (targetPath: string) => {
  const view = visitedViews.value.find(v => v.path === targetPath)
  if (!view) return
  
  // 不允许关闭固定标签
  if (view.affix) return
  
  removeVisitedView(view)
  
  // 如果关闭的是当前激活的标签，则需要跳转到其他标签
  if (activeTab.value === targetPath) {
    toLastView()
  }
}

// 跳转到最后一个标签
const toLastView = () => {
  if (visitedViews.value.length > 0) {
    const latestView = visitedViews.value[visitedViews.value.length - 1]
    router.push(latestView.path)
  } else {
    router.push('/')
  }
}

// 处理标签编辑（添加和删除）
const handleTabEdit = (targetPath: string, action: string) => {
  if (action === 'remove') {
    removeTab(targetPath)
  }
}

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  switch (command) {
    case 'closeOthers':
      closeOthersTabs()
      break
    case 'closeAll':
      closeAllTabs()
      break
  }
}

// 关闭其他标签
const closeOthersTabs = () => {
  visitedViews.value = visitedViews.value.filter(
    v => v.affix || v.path === activeTab.value
  )
}

// 关闭所有标签
const closeAllTabs = () => {
  // 保留固定的标签
  const affixTabs = visitedViews.value.filter(v => v.affix)
  visitedViews.value = affixTabs

  if (affixTabs.length > 0) {
    router.push(affixTabs[0].path)
  } else {
    router.push('/')
  }
}

// 监听路由变化，添加标签
watch(
  () => route.path,
  () => {
    addVisitedView(route)
    activeTab.value = route.path
  }
)

// 初始化：添加固定标签和当前路由标签
onMounted(() => {
  // 获取所有具有affix:true的路由
  const affixRoutes = router.getRoutes().filter(route => route.meta?.affix)
  affixRoutes.forEach(route => {
    addVisitedView({
      path: route.path,
      meta: route.meta,
      name: route.name
    })
  })
  
  // 添加当前路由
  addVisitedView(route)
  activeTab.value = route.path
})
</script>

<style lang="scss" scoped>
.tags-view-container {
  height: 100%;
  width: 100%;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  box-sizing: border-box;
  
  .tags-scrollbar {
    flex: 1;
    height: 100%;
    width: calc(100% - 40px);
    display: flex;
    align-items: center;
  }
  
  .demo-tabs {
    flex: 1;
    white-space: nowrap;
    width: 100%;
    
    :deep(.el-tabs__header) {
      margin: 0;
      height: 60px;
      display: flex;
      align-items: center;
    }
    
    :deep(.el-tabs__nav) {
      border: none;
    }
    
    :deep(.el-tabs__item) {
      height: 40px;
      line-height: 40px;
      border: 1px solid #d8dce5;
      margin: 10px 2px 10px 0;
      color: #495060;
      background: #fff;
      padding: 0 10px;
      
      &.is-active {
        background-color: #22a699;
        color: #fff;
        border-color: #22a699;
        
        &::before {
          content: '';
          background: #fff;
          display: inline-block;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          position: relative;
          margin-right: 4px;
        }
      }
    }
  }
  
  .tags-view-actions {
    margin-left: 5px;
    margin-right: 5px;
    height: 100%;
    display: flex;
    align-items: center;
    
    .action-icon {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      height: 40px;
      width: 30px;
      border-radius: 2px;
      color: #606266;
      cursor: pointer;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: #f0f0f0;
        color: #22a699;
      }
      
      .el-icon {
        font-size: 14px;
      }
    }
  }
}
</style> 