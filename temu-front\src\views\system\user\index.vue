<!-- 用户管理页面 -->
<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="search-container">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="queryParams.username" placeholder="请输入用户名" clearable />
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="queryParams.phone" placeholder="请输入手机号码" clearable />
        </el-form-item>
        <el-form-item label="状态" prop="status" style="width: 150px">
          <el-select v-model="queryParams.status" placeholder="用户状态" clearable>
            <el-option label="正常" value="0" />
            <el-option label="禁用" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon> 搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区 -->
    <el-card class="table-container">
      <template #header>
        <div class="card-header">
          <span>用户列表</span>
          <div class="right-buttons">
            <el-button type="primary" @click="handleAdd" v-permission="'system:user:add'">
              <el-icon><Plus /></el-icon> 新增
            </el-button>
            <el-button type="primary" @click="handleImport" v-permission="'system:user:import'">
              <el-icon><Upload /></el-icon> 导入
            </el-button>
            <el-button type="success" @click="handleExport" v-permission="'system:user:export'">
              <el-icon><Download /></el-icon> 导出
            </el-button>
            <el-button 
              type="danger" 
              @click="handleBatchDelete" 
              :disabled="multipleSelection.length === 0"
              v-permission="'system:user:remove'"
            >
              <el-icon><Delete /></el-icon> 批量删除
            </el-button>
          </div>
        </div>
      </template>

      <!-- 表格工具栏 -->
      <div v-if="multipleSelection.length > 0" class="table-toolbar">
        <div class="selection-info">
          已选择 <span class="selected-count">{{ multipleSelection.length }}</span> 条记录
        </div>
        <el-button size="small" @click="clearSelection">
          清空选择
        </el-button>
      </div>

      <!-- 表格 -->
      <el-table 
        ref="tableRef"
        v-loading="loading" 
        :data="userList"
        border
        class="menu-tree-table"
        @sort-change="handleSortChange"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" width="80" align="center">
          <template #default="scope">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="用户名" prop="username" :show-overflow-tooltip="true" sortable="custom" />
        <el-table-column label="昵称" prop="nickName" :show-overflow-tooltip="true" />
        <el-table-column label="邮箱" prop="email" :show-overflow-tooltip="true" />
        <el-table-column label="手机号码" prop="phone" width="120" />
        <el-table-column label="状态" prop="status" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="'0'"
              :inactive-value="'1'"
              @change="handleStatusChange(scope.row)"
              v-permission="'system:user:edit'"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="350" align="center">
          <template #default="scope">
            <el-button 
              type="primary" 
              link 
              @click="handleUpdate(scope.row)"
              v-permission="'system:user:edit'"
            >
              <el-icon><Edit /></el-icon> 编辑
            </el-button>
            <el-button 
              type="primary" 
              link 
              @click="handleRoleAssign(scope.row)"
              v-permission="'system:user:edit'"
            >
              <el-icon><UserFilled /></el-icon> 分配角色
            </el-button>
            <el-button 
              type="primary" 
              link 
              @click="handleResetPassword(scope.row)"
              v-permission="'system:user:resetPassword'"
            >
              <el-icon><Key /></el-icon> 重置密码
            </el-button>
            <el-button 
              type="danger" 
              link 
              @click="handleDelete(scope.row)"
              v-permission="'system:user:remove'"
            >
              <el-icon><Delete /></el-icon> 删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 用户添加/编辑弹窗 -->
    <UserForm 
      v-model:visible="userFormVisible" 
      :title="formTitle" 
      :user-id="selectedUserId"
      @success="getList" 
    />

    <!-- 角色分配弹窗 -->
    <RoleDialog 
      v-model:visible="roleDialogVisible" 
      :user-id="selectedUserId"
      :user-name="selectedUserName"
      @success="getList" 
    />

    <!-- 重置密码弹窗 -->
    <ResetPwdDialog 
      v-model:visible="resetPwdVisible" 
      :user-id="selectedUserId"
      :user-name="selectedUserName"
      @success="getList" 
    />

    <!-- 导出对话框 -->
    <el-dialog
      v-model="exportDialog.visible"
      title="导出用户数据"
      width="500px"
    >
      <el-form label-width="100px">
        <el-form-item label="导出方式">
          <el-radio-group v-model="exportType">
            <el-radio label="all">全部数据</el-radio>
            <el-radio label="page">当前页数据</el-radio>
            <el-radio label="selected">选中数据</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="文件名">
          <el-input v-model="exportFileName" placeholder="请输入文件名" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="exportDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="confirmExport" :loading="exportLoading">确定</el-button>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog
      v-model="importDialog.visible"
      title="导入用户"
      width="500px"
    >
      <div class="import-tip">
        <p>请先下载导入模板，按照模板格式填写数据后导入</p>
        <p>导入说明：</p>
        <ul>
          <li>用户名、昵称为必填项</li>
          <li>角色名称对应系统已有角色，多个角色请用逗号分隔</li>
          <li>导入的用户默认密码为：123456</li>
        </ul>
      </div>
      <el-form label-width="100px">
        <el-form-item label="下载模板">
          <el-button type="primary" @click="downloadTemplate">
            <el-icon><Download /></el-icon> 下载模板
          </el-button>
        </el-form-item>
        <el-form-item label="选择文件">
          <el-upload
            ref="uploadRef"
            action=""
            :auto-upload="false"
            :limit="1"
            :on-exceed="handleExceed"
            :on-change="handleFileChange"
            accept=".xlsx, .xls"
            drag
          >
            <el-icon class="el-icon--upload"><Upload /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                仅支持.xlsx, .xls格式文件
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="importDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="submitImport" :loading="importLoading">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Plus, Edit, Delete, Search, Refresh, UserFilled, Key, Download, Upload, More, ArrowDown } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, ElUpload } from 'element-plus'
import { getUserList, deleteUser, changeUserStatus, exportUsers, importUsers, getImportTemplate, batchDeleteUsers } from '@/api/user'
import UserForm from './components/UserForm.vue'
import RoleDialog from './components/RoleDialog.vue'
import ResetPwdDialog from './components/ResetPwdDialog.vue'
import { formatTime } from '@/utils/format'
import { exportAll, exportPage, exportSelected } from '@/utils/excel'
import { getToken } from '@/utils/auth'
import type { TableInstance } from 'element-plus'

// 加载状态
const loading = ref(false)
// 用户列表数据
const userList = ref([])
// 表格引用
const tableRef = ref<TableInstance>()
// 总记录数
const total = ref(0)
// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  username: '',
  phone: '',
  status: '',
  orderBy: 'username',
  orderDir: 'asc'
})

// 弹窗控制
const userFormVisible = ref(false)
const roleDialogVisible = ref(false)
const resetPwdVisible = ref(false)
const formTitle = ref('')
const selectedUserId = ref<number>(0)
const selectedUserName = ref('')

// 表格多选
const multipleSelection = ref<any[]>([])
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}

// 导出相关
const exportType = ref('page')
const exportFileName = ref('用户数据')
const exportLoading = ref(false)
const exportDialog = reactive({
  visible: false
})

// 导入相关
const uploadRef = ref<InstanceType<typeof ElUpload>>()
const importFile = ref<File | null>(null)
const importLoading = ref(false)
const importDialog = reactive({
  visible: false
})

// 获取用户列表
const getList = async () => {
  loading.value = true
  try {
    const res = await getUserList(queryParams)
    userList.value = res.data.records
    total.value = res.data.total
  } catch (error) {
    console.error('获取用户列表失败', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 查询操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  queryParams.username = ''
  queryParams.phone = ''
  queryParams.status = ''
  queryParams.orderBy = 'username'
  queryParams.orderDir = 'asc'
  handleQuery()
}

// 每页数量变更
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  getList()
}

// 页码变更
const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page
  getList()
}

// 新增用户
const handleAdd = () => {
  selectedUserId.value = 0
  formTitle.value = '新增用户'
  userFormVisible.value = true
}

// 编辑用户
const handleUpdate = (row: any) => {
  selectedUserId.value = row.userId
  formTitle.value = '编辑用户'
  userFormVisible.value = true
}

// 分配角色
const handleRoleAssign = (row: any) => {
  selectedUserId.value = row.userId
  selectedUserName.value = row.username
  roleDialogVisible.value = true
}

// 重置密码
const handleResetPassword = (row: any) => {
  selectedUserId.value = row.userId
  selectedUserName.value = row.username
  resetPwdVisible.value = true
}

// 删除用户
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确认删除用户 ${row.username} 吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        await deleteUser(row.userId)
        ElMessage.success('删除成功')
        getList()
      } catch (error) {
        console.error('删除用户失败', error)
        ElMessage.error('删除用户失败')
      }
    })
    .catch(() => {
      ElMessage.info('已取消删除')
    })
}

// 修改用户状态
const handleStatusChange = async (row: any) => {
  try {
    await changeUserStatus(row.userId, row.status)
    ElMessage.success(`${row.status === '0' ? '启用' : '禁用'}成功`)
  } catch (error) {
    console.error('修改用户状态失败', error)
    ElMessage.error('修改用户状态失败')
    row.status = row.status === '0' ? '1' : '0' // 失败时恢复原状态
  }
}

// 处理表格排序变更
const handleSortChange = (column: any) => {
  if (column.prop && column.order) {
    queryParams.orderBy = column.prop
    queryParams.orderDir = column.order === 'ascending' ? 'asc' : 'desc'
  } else {
    // 如果取消排序，恢复默认排序
    queryParams.orderBy = 'username'
    queryParams.orderDir = 'asc'
  }
  getList()
}

// 格式化时间
const formatDateTime = (time: string | number | Date) => {
  return formatTime(time, 'YYYY-MM-DD HH:mm:ss')
}

// 处理导出
const handleExport = () => {
  // 检查选中的数据
  if (exportType.value === 'selected' && multipleSelection.value.length === 0) {
    ElMessage.warning('请至少选择一条记录')
    exportType.value = 'page'
  }
  exportDialog.visible = true
}

// 确认导出
const confirmExport = async () => {
  // 准备参数
  const fileName = exportFileName.value || '用户数据'
  const sheetName = '用户列表'
  let promise
  
  if (exportType.value === 'all') {
    promise = exportAll(exportUsers, queryParams, fileName, sheetName)
  } else if (exportType.value === 'page') {
    const pageParams = {
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize
    }
    promise = exportPage(exportUsers, pageParams, queryParams, fileName, sheetName)
  } else if (exportType.value === 'selected') {
    if (multipleSelection.value.length === 0) {
      ElMessage.warning('请至少选择一条记录')
      return
    }
    const selectedIds = multipleSelection.value.map(item => item.userId)
    promise = exportSelected(exportUsers, selectedIds, fileName, sheetName)
  }
  
  exportLoading.value = true
  try {
    await promise
    exportDialog.visible = false
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 处理导入
const handleImport = () => {
  importFile.value = null
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
  importDialog.visible = true
}

// 下载导入模板
const downloadTemplate = async () => {
  try {
    const res = await getImportTemplate()
    // 正确处理Excel文件的blob
    const blob = new Blob([res.data], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    
    // 创建临时下载链接
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = '用户导入模板.xlsx'
    document.body.appendChild(link)
    link.click()
    
    // 清理临时对象
    setTimeout(() => {
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)
    }, 100)
  } catch (error) {
    console.error('下载模板失败', error)
    ElMessage.error('下载模板失败')
  }
}

// 文件超出限制处理
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 文件选择变更处理
const handleFileChange = (file: any) => {
  if (file && file.raw) {
    importFile.value = file.raw
  } else {
    importFile.value = null
  }
}

// 提交导入
const submitImport = async () => {
  if (!importFile.value) {
    ElMessage.warning('请选择要导入的文件')
    return
  }
  
  importLoading.value = true
  try {
    const res = await importUsers(importFile.value)
    // 安全地访问响应数据
    ElMessage.success('导入成功')
    importDialog.visible = false
    getList() // 刷新列表
  } catch (error) {
    console.error('导入失败', error)
    ElMessage.error('导入失败')
  } finally {
    importLoading.value = false
  }
}

// 批量删除用户
const handleBatchDelete = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请至少选择一条记录')
    return
  }
  
  const usernames = multipleSelection.value.map(item => item.username).join('、')
  const userIds = multipleSelection.value.map(item => item.userId)
  
  ElMessageBox.confirm(`确认批量删除以下用户吗？<br/>${usernames}`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    dangerouslyUseHTMLString: true
  })
    .then(async () => {
      try {
        await batchDeleteUsers(userIds)
        ElMessage.success('批量删除成功')
        getList()
      } catch (error) {
        console.error('批量删除用户失败', error)
        ElMessage.error('批量删除用户失败')
      }
    })
    .catch(() => {
      ElMessage.info('已取消删除')
    })
}

// 清空表格选择
const clearSelection = () => {
  if (tableRef.value) {
    tableRef.value.clearSelection()
    multipleSelection.value = []
  }
}

// 页面加载时获取列表
onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.search-container,
.table-container {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.right-buttons {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

/* 表格样式 */
.menu-tree-table :deep(.el-table__row) {
  transition: background-color 0.2s;
}

/* 添加悬停效果 */
.menu-tree-table :deep(.el-table__row:hover) {
  background-color: #f0f9ff !important;
}

/* 为不同类型的项添加不同的左侧边框样式 */
.menu-tree-table :deep(.el-table__row) td:first-child {
  border-left: 4px solid #409eff;
}

.import-tip {
  margin-bottom: 20px;
  padding: 12px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.import-tip p {
  margin: 5px 0;
}

.import-tip ul {
  padding-left: 20px;
  margin: 8px 0;
}

.import-tip li {
  margin-bottom: 5px;
}

/* 拖拽上传区域样式 */
.el-upload-dragger {
  width: 100%;
  height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.el-upload-dragger:hover {
  border-color: #409eff;
}

.el-icon--upload {
  font-size: 67px;
  color: #c0c4cc;
  margin-bottom: 10px;
  line-height: 50px;
}

.el-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.el-upload__text em {
  color: #409eff;
  font-style: normal;
}

/* 表格工具栏样式 */
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.selection-info {
  display: flex;
  align-items: center;
}

.selected-count {
  font-weight: bold;
  margin-left: 5px;
}
</style> 