package com.xiao.temu.modules.shop.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.shop.entity.ShopGroupAssignment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 店铺运营组关联Mapper接口
 */
@Mapper
public interface ShopGroupAssignmentMapper extends BaseMapper<ShopGroupAssignment> {
    
    /**
     * 根据店铺ID查询所属的所有运营组ID
     *
     * @param shopId 店铺ID
     * @return 运营组ID列表
     */
    List<Long> selectGroupIdsByShopId(@Param("shopId") Long shopId);
    
    /**
     * 根据运营组ID查询所属的所有店铺ID
     *
     * @param groupId 运营组ID
     * @return 店铺ID列表
     */
    List<Long> selectShopIdsByGroupId(@Param("groupId") Long groupId);
    
    /**
     * 根据店铺ID查询一个运营组关联
     *
     * @param shopId 店铺ID
     * @return 运营组关联
     */
    ShopGroupAssignment selectOneByShopId(@Param("shopId") Long shopId);
} 