package com.xiao.temu.modules.sync.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.sync.entity.QualityInspectionSyncTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 质检数据同步任务Mapper接口
 */
@Mapper
public interface QualityInspectionSyncTaskMapper extends BaseMapper<QualityInspectionSyncTask> {

    /**
     * 查询需要同步的任务列表
     * 
     * @return 需要同步的任务列表
     */
    List<QualityInspectionSyncTask> getTasksToSync();

    /**
     * 根据店铺ID查询同步任务
     * 
     * @param shopId 店铺ID
     * @return 同步任务
     */
    QualityInspectionSyncTask getByShopId(@Param("shopId") Long shopId);
    
    /**
     * 初始化店铺的同步任务
     * 
     * @param shopId 店铺ID
     * @return 影响行数
     */
    int initSyncTask(@Param("shopId") Long shopId);

    /**
     * 直接清空时间字段
     * 
     * @param params 参数，包含shopId
     * @return 更新行数
     */
    int clearTimeFields(Map<String, Object> params);
} 