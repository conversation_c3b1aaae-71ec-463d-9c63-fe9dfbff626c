package com.xiao.temu.modules.violation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 店铺违规详情实体类
 */
@Data
@TableName("shop_violation_detail")
public class ShopViolationDetail {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 违规编号
     */
    private String punishSn;

    /**
     * 备货单号
     */
    private String subPurchaseOrderSn;

    /**
     * 商品SKU ID
     */
    private Long productSkuId;

    /**
     * 库存数量
     */
    private Integer stockQuantity;

    /**
     * 缺货数量
     */
    private Integer lackQuantity;

    /**
     * 不合格数量
     */
    private Integer unqualifiedQuantity;

    /**
     * 同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date syncTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
} 