package com.xiao.temu.infrastructure.task;

import org.springframework.stereotype.Component;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.io.File;
import lombok.extern.slf4j.Slf4j;

/**
 * 导出任务管理器
 * 用于跟踪导出任务的进度
 */
@Slf4j
@Component
public class ExportTaskManager {

    /**
     * 任务信息类，记录导出任务的状态和进度
     */
    public static class TaskInfo {
        private String taskId;
        private int progress; // 进度，0-100
        private String status; // 状态：waiting, processing, completed, failed
        private String message; // 进度消息
        private long startTime; // 开始时间戳
        private long updateTime; // 最后更新时间戳
        private String fileName; // 导出的文件名
        private String filePath; // 导出文件的路径

        public TaskInfo(String taskId, String fileName) {
            this.taskId = taskId;
            this.progress = 0;
            this.status = "waiting";
            this.message = "等待处理...";
            this.startTime = System.currentTimeMillis();
            this.updateTime = this.startTime;
            this.fileName = fileName;
            this.filePath = null;
        }

        public String getTaskId() {
            return taskId;
        }

        public int getProgress() {
            return progress;
        }

        public void setProgress(int progress) {
            this.progress = progress;
            this.updateTime = System.currentTimeMillis();
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
            this.updateTime = System.currentTimeMillis();
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
            this.updateTime = System.currentTimeMillis();
        }

        public long getStartTime() {
            return startTime;
        }

        public long getUpdateTime() {
            return updateTime;
        }

        public String getFileName() {
            return fileName;
        }
        
        public String getFilePath() {
            return filePath;
        }
        
        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }
    }

    // 使用ConcurrentHashMap存储任务信息，保证线程安全
    private final Map<String, TaskInfo> tasks = new ConcurrentHashMap<>();

    /**
     * 创建新的导出任务
     * @param fileName 导出的文件名
     * @return 任务ID
     */
    public String createTask(String fileName) {
        String taskId = generateTaskId();
        tasks.put(taskId, new TaskInfo(taskId, fileName));
        return taskId;
    }

    /**
     * 获取任务信息
     * @param taskId 任务ID
     * @return 任务信息
     */
    public TaskInfo getTaskInfo(String taskId) {
        return tasks.get(taskId);
    }

    /**
     * 更新任务进度
     * @param taskId 任务ID
     * @param progress 进度，0-100
     * @param message 进度消息
     */
    public void updateTaskProgress(String taskId, int progress, String message) {
        TaskInfo task = tasks.get(taskId);
        if (task != null) {
            task.setProgress(progress);
            task.setMessage(message);
            task.setStatus("processing");
        }
    }

    /**
     * 标记任务完成
     * @param taskId 任务ID
     * @param message 完成消息
     */
    public void completeTask(String taskId, String message) {
        TaskInfo task = tasks.get(taskId);
        if (task != null) {
            task.setProgress(100);
            task.setMessage(message);
            task.setStatus("completed");
        }
    }
    
    /**
     * 标记任务完成并记录导出的文件路径
     * @param taskId 任务ID
     * @param message 完成消息
     * @param filePath 导出文件的路径
     */
    public void completeTaskWithFilePath(String taskId, String message, String filePath) {
        TaskInfo task = tasks.get(taskId);
        if (task != null) {
            task.setProgress(100);
            task.setMessage(message);
            task.setStatus("completed");
            task.setFilePath(filePath);
        }
    }

    /**
     * 标记任务失败
     * @param taskId 任务ID
     * @param message 失败消息
     */
    public void failTask(String taskId, String message) {
        TaskInfo task = tasks.get(taskId);
        if (task != null) {
            task.setMessage(message);
            task.setStatus("failed");
        }
    }

    /**
     * 清理指定任务的导出文件
     * @param taskId 任务ID
     * @return 是否成功删除文件
     */
    public boolean cleanupTaskFile(String taskId) {
        TaskInfo task = tasks.get(taskId);
        if (task != null && task.getFilePath() != null) {
            File file = new File(task.getFilePath());
            if (file.exists()) {
                boolean deleted = file.delete();
                if (deleted) {
                    log.info("成功删除导出文件: {}, 任务ID: {}", file.getAbsolutePath(), taskId);
                    return true;
                } else {
                    log.warn("删除导出文件失败: {}, 任务ID: {}", file.getAbsolutePath(), taskId);
                }
            }
        }
        return false;
    }

    /**
     * 清理已下载的导出文件和过期任务记录
     * 用户下载后立即调用，不等待定时任务
     * @param taskId 任务ID
     */
    public void cleanupAfterDownload(String taskId) {
        TaskInfo task = tasks.get(taskId);
        if (task != null) {
            // 尝试删除文件
            cleanupTaskFile(taskId);
            // 移除任务记录
            tasks.remove(taskId);
            log.info("下载后清理完成，已删除任务: {}", taskId);
        }
    }

    /**
     * 清理旧任务（可以设置定时任务执行此方法）
     * 清理超过24小时的已完成任务和超过2小时的失败任务，同时删除对应的文件
     */
    public void cleanupOldTasks() {
        long currentTime = System.currentTimeMillis();
        long oneDayMs = 24 * 60 * 60 * 1000; // 24小时
        long twoHoursMs = 2 * 60 * 60 * 1000; // 2小时

        tasks.entrySet().removeIf(entry -> {
            TaskInfo task = entry.getValue();
            boolean shouldRemove = false;
            
            if ("completed".equals(task.getStatus()) && (currentTime - task.getUpdateTime() > oneDayMs)) {
                log.info("清理超过24小时的已完成任务: {}, 文件: {}", task.getTaskId(), task.getFilePath());
                shouldRemove = true;
            } else if ("failed".equals(task.getStatus()) && (currentTime - task.getUpdateTime() > twoHoursMs)) {
                log.info("清理超过2小时的失败任务: {}", task.getTaskId());
                shouldRemove = true;
            }
            
            if (shouldRemove) {
                // 如果需要移除任务，尝试删除对应的文件
                cleanupTaskFile(task.getTaskId());
            }
            
            return shouldRemove;
        });
        
        // 清理导出目录中的孤儿文件（没有对应任务记录的文件）
        cleanupOrphanFiles();
    }
    
    /**
     * 清理导出目录中的孤儿文件（没有对应任务记录的文件）
     * 删除超过48小时的文件
     */
    private void cleanupOrphanFiles() {
        try {
            File outputDir = new File(System.getProperty("java.io.tmpdir"), "temu_export_output");
            if (!outputDir.exists() || !outputDir.isDirectory()) {
                return;
            }
            
            File[] files = outputDir.listFiles();
            if (files == null || files.length == 0) {
                return;
            }
            
            long currentTime = System.currentTimeMillis();
            long twoDaysMs = 48 * 60 * 60 * 1000; // 48小时
            
            int totalFiles = 0;
            int deletedFiles = 0;
            
            for (File file : files) {
                totalFiles++;
                // 如果文件超过48小时
                if (currentTime - file.lastModified() > twoDaysMs) {
                    boolean deleted = file.delete();
                    if (deleted) {
                        deletedFiles++;
                        log.info("删除过期孤儿文件: {}", file.getAbsolutePath());
                    } else {
                        log.warn("删除过期孤儿文件失败: {}", file.getAbsolutePath());
                    }
                }
            }
            
            log.info("孤儿文件清理完成: 共扫描 {} 个文件，删除 {} 个过期文件", totalFiles, deletedFiles);
        } catch (Exception e) {
            log.error("清理孤儿文件失败", e);
        }
    }

    /**
     * 生成唯一的任务ID
     */
    private String generateTaskId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}