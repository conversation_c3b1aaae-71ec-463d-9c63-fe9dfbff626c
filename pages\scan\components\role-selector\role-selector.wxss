.role-selector {
  width: 100%;
  padding: 10rpx 0;
}

.role-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  width: 100%;
}

.role-item {
  width: calc(50% - 10rpx);
  transition: all 0.3s ease;
}

.role-item.selected {
  transform: translateY(-2rpx);
}

/* 选中提示样式 */
.selected-roles-tip {
  margin-top: 20rpx;
  background-color: #f0f9ff;
  padding: 16rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #1890ff;
}

.tip-text {
  font-size: 24rpx;
  color: #1890ff;
} 