<template>
  <!-- 分页和操作区域 -->
  <div class="action-bar">
    <div class="left-actions"></div>
    <div class="right-actions">
      <PaginationBar
        :current-page="Number(queryParams.pageNo)" 
        :page-size="Number(queryParams.pageSize)"
        :total="total"
        @update:current-page="(val) => updatePageNo(val)"
        @update:page-size="(val) => updatePageSize(val)"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import { PaginationBar } from '@/components/temu'
import type { PurchaseOrderRequestDTO, ShopDataRange } from '@/types/purchaseOrder'

const props = defineProps({
  // 查询参数
  queryParams: {
    type: Object as () => PurchaseOrderRequestDTO,
    required: true
  },
  // 总记录数
  total: {
    type: Number,
    default: 0
  },
  // 店铺数据范围
  shopRanges: {
    type: Array as () => ShopDataRange[],
    default: () => []
  }
})

const emit = defineEmits(['update:query-params', 'size-change', 'current-change'])

// 更新页码
const updatePageNo = (val: number) => {
  emit('update:query-params', { ...props.queryParams, pageNo: Number(val) })
}

// 更新每页条数
const updatePageSize = (val: number) => {
  emit('update:query-params', { ...props.queryParams, pageSize: Number(val) })
}

// 处理每页显示条数变化
const handleSizeChange = (val: number) => {
  emit('size-change', val)
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  emit('current-change', val)
}
</script>

<style scoped>
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  padding: 0 10px;
}

.left-actions {
  display: flex;
  gap: 10px;
}

.right-actions {
  display: flex;
  justify-content: flex-end;
}
</style> 