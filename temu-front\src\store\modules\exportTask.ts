import { defineStore } from 'pinia'
import { ElMessage, ElNotification } from 'element-plus'
import type { ShallowRef } from 'vue'
import { shallowRef } from 'vue'

export interface ExportTask {
  id: string
  name: string
  fileName: string
  progress: number
  status: 'pending' | 'processing' | 'completed' | 'failed'
  createdAt: Date
  completedAt?: Date
  error?: string
  cancelFn?: () => void
  serverTaskId?: string
}

interface ExportTaskState {
  tasks: ExportTask[]
  lastId: number
}

export const useExportTaskStore = defineStore('exportTask', {
  state: (): ExportTaskState => ({
    tasks: [],
    lastId: 0
  }),
  
  getters: {
    activeTasks: (state) => state.tasks.filter(task => task.status === 'pending' || task.status === 'processing'),
    recentTasks: (state) => {
      // 获取最近24小时内的任务
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
      return state.tasks
        .filter(task => task.createdAt > oneDayAgo)
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
    }
  },
  
  actions: {
    /**
     * 创建新的导出任务
     */
    createTask(name: string, fileName: string): ExportTask {
      const id = `export-${Date.now()}-${++this.lastId}`
      
      const task: ExportTask = {
        id,
        name,
        fileName,
        progress: 0,
        status: 'pending',
        createdAt: new Date()
      }
      
      this.tasks.unshift(task)
      
      // 如果任务数量超过20个，移除最旧的任务
      if (this.tasks.length > 20) {
        const oldTasks = this.tasks
          .filter(t => t.status === 'completed' || t.status === 'failed')
          .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
        
        if (oldTasks.length > 0) {
          this.tasks = this.tasks.filter(t => t.id !== oldTasks[0].id)
        }
      }
      
      return task
    },
    
    /**
     * 更新任务进度
     */
    updateTaskProgress(taskId: string, progress: number) {
      const task = this.tasks.find(t => t.id === taskId)
      if (task) {
        task.progress = Math.min(Math.max(0, progress), 100)
        task.status = 'processing'
      }
    },
    
    /**
     * 完成任务
     */
    completeTask(taskId: string) {
      const task = this.tasks.find(t => t.id === taskId)
      if (task) {
        task.progress = 100
        task.status = 'completed'
        task.completedAt = new Date()
        
        // 显示通知
        ElNotification({
          title: '导出完成',
          message: `${task.name} 已完成导出`,
          type: 'success',
          duration: 3000
        })
      }
    },
    
    /**
     * 设置任务失败
     */
    failTask(taskId: string, error: string) {
      const task = this.tasks.find(t => t.id === taskId)
      if (task) {
        task.status = 'failed'
        task.error = error
        task.completedAt = new Date()
        
        // 显示通知
        ElNotification({
          title: '导出失败',
          message: `${task.name} 导出失败：${error}`,
          type: 'error',
          duration: 5000
        })
      }
    },
    
    /**
     * 设置任务取消函数
     */
    setTaskCancelFn(taskId: string, cancelFn: () => void) {
      const task = this.tasks.find(t => t.id === taskId)
      if (task) {
        task.cancelFn = cancelFn
      }
    },
    
    /**
     * 取消任务
     */
    cancelTask(taskId: string) {
      const task = this.tasks.find(t => t.id === taskId)
      if (task) {
        if (task.cancelFn) {
          task.cancelFn()
        }
        task.status = 'failed'
        task.error = '任务已取消'
        task.completedAt = new Date()
      }
    },
    
    /**
     * 清除所有已完成和失败的任务
     */
    clearCompletedTasks() {
      this.tasks = this.tasks.filter(t => t.status === 'pending' || t.status === 'processing')
    }
  }
}) 