/**
 * Excel导出工具类
 */

/**
 * 处理导出文件下载
 * @param res axios响应数据，类型为blob
 * @param defaultFileName 默认文件名
 */
export function handleExportFile(res: any, defaultFileName: string = '导出数据') {
  // 获取文件名
  let fileName = defaultFileName;
  const contentDisposition = res.headers['content-disposition'];
  if (contentDisposition) {
    // 尝试从响应头获取文件名
    const filenameRegex = /filename\*=utf-8''([\w%.-]+)/;
    const matches = filenameRegex.exec(contentDisposition);
    if (matches && matches.length > 1) {
      fileName = decodeURIComponent(matches[1]);
    }
  }
  
  // 创建Blob对象
  const blob = new Blob([res.data], { 
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
  });
  
  // 创建临时下载链接
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  
  // 清理临时对象
  setTimeout(() => {
    URL.revokeObjectURL(link.href);
    document.body.removeChild(link);
  }, 100);
}

/**
 * 导出数据 - 全部
 * @param exportFn 导出API函数
 * @param queryParams 查询参数
 * @param fileName 文件名
 * @param sheetName 工作表名
 */
export async function exportAll(exportFn: Function, queryParams: any = {}, fileName?: string, sheetName?: string) {
  try {
    const data = {
      exportType: 'all',
      queryParams,
      fileName,
      sheetName
    };
    const res = await exportFn(data);
    handleExportFile(res, fileName);
    return true;
  } catch (error) {
    console.error('导出失败', error);
    return false;
  }
}

/**
 * 导出数据 - 当前页
 * @param exportFn 导出API函数
 * @param pageParams 分页参数
 * @param queryParams 查询参数
 * @param fileName 文件名
 * @param sheetName 工作表名
 */
export async function exportPage(
  exportFn: Function, 
  pageParams: { pageNum: number, pageSize: number }, 
  queryParams: any = {},
  fileName?: string, 
  sheetName?: string
) {
  try {
    const data = {
      exportType: 'page',
      pageNum: pageParams.pageNum,
      pageSize: pageParams.pageSize,
      queryParams,
      fileName,
      sheetName
    };
    const res = await exportFn(data);
    handleExportFile(res, fileName);
    return true;
  } catch (error) {
    console.error('导出失败', error);
    return false;
  }
}

/**
 * 导出数据 - 选中项
 * @param exportFn 导出API函数
 * @param selectedIds 选中记录ID列表
 * @param fileName 文件名
 * @param sheetName 工作表名
 */
export async function exportSelected(
  exportFn: Function, 
  selectedIds: number[] | string[], 
  fileName?: string, 
  sheetName?: string
) {
  console.log('导出选中项 - 传入的ID列表:', selectedIds);
  
  if (!selectedIds || selectedIds.length === 0) {
    console.error('未选择任何记录');
    return false;
  }
  
  try {
    const data = {
      exportType: 'selected',
      selectedIds,
      fileName,
      sheetName
    };
    console.log('导出选中项 - 发送的数据:', data);
    const res = await exportFn(data);
    handleExportFile(res, fileName);
    return true;
  } catch (error) {
    console.error('导出失败', error);
    return false;
  }
} 