package com.xiao.temu.config.scheduling;

import com.xiao.temu.modules.sync.job.ExportFileCleanupJob;
import com.xiao.temu.modules.sync.job.ProductSyncQuartzJob;
import com.xiao.temu.modules.sync.job.PurchaseOrderSyncQuartzJob;
import com.xiao.temu.modules.sync.job.QualityInspectionSyncJob;
import com.xiao.temu.modules.sync.job.RefundPackageSyncQuartzJob;
import com.xiao.temu.modules.sync.job.SalesSyncQuartzJob;
import com.xiao.temu.modules.sync.job.SalesStatisticsQuartzJob;
import com.xiao.temu.modules.purchaseorderv.job.PurchaseOrderNotificationQuartzJob;
import org.quartz.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
* Quartz定时任务配置
*/
@Configuration
public class QuartzConfig {
   /**
    * 退货包裹数据同步任务触发器，每30分钟执行一次
    */
   @Bean
   public Trigger refundPackageSyncTrigger() {
       // 每30分钟执行一次
       SimpleScheduleBuilder scheduleBuilder = SimpleScheduleBuilder.simpleSchedule()
               .withIntervalInMinutes(30)
               .repeatForever();

       return TriggerBuilder.newTrigger()
               .forJob(refundPackageSyncJobDetail())
               .withIdentity("refundPackageSyncTrigger")
               .startNow()
               .withSchedule(scheduleBuilder)
               .build();
   }
   /**
    * 质检数据同步任务触发器，每30分钟执行一次
    */
   @Bean
   public Trigger qualityInspectionSyncTrigger() {
       // 每30分钟执行一次
       SimpleScheduleBuilder scheduleBuilder = SimpleScheduleBuilder.simpleSchedule()
               .withIntervalInMinutes(30)
               .repeatForever();

       return TriggerBuilder.newTrigger()
               .forJob(qualityInspectionSyncJobDetail())
               .withIdentity("qualityInspectionSyncTrigger")
               .startNow()
               .withSchedule(scheduleBuilder)
               .build();
   }
   /**
    * 商品数据同步任务触发器，每30分钟执行一次
    */
   @Bean
   public Trigger productSyncTrigger() {
       // 每30分钟执行一次
       SimpleScheduleBuilder scheduleBuilder = SimpleScheduleBuilder.simpleSchedule()
               .withIntervalInMinutes(30)
               .repeatForever();

       return TriggerBuilder.newTrigger()
               .forJob(productSyncJobDetail())
               .withIdentity("productSyncTrigger")
               .startNow()
               .withSchedule(scheduleBuilder)
               .build();
   }

   /**
    * 质检数据同步任务
    */
   @Bean
   public JobDetail qualityInspectionSyncJobDetail() {
       return JobBuilder.newJob(QualityInspectionSyncJob.class)
               .withIdentity("qualityInspectionSyncJob")
               .storeDurably()
               .build();
   }


   /**
    * 商品数据同步任务
    */
   @Bean
   public JobDetail productSyncJobDetail() {
       return JobBuilder.newJob(ProductSyncQuartzJob.class)
               .withIdentity("productSyncJob")
               .storeDurably()
               .build();
   }


   /**
    * 退货包裹数据同步任务
    */
   @Bean
   public JobDetail refundPackageSyncJobDetail() {
       return JobBuilder.newJob(RefundPackageSyncQuartzJob.class)
               .withIdentity("refundPackageSyncJob")
               .storeDurably()
               .build();
   }

   /**
    * 导出文件清理任务触发器，每小时执行一次
    */
   @Bean
   public Trigger exportFileCleanupTrigger() {
       // 每小时执行一次
       SimpleScheduleBuilder scheduleBuilder = SimpleScheduleBuilder.simpleSchedule()
               .withIntervalInHours(1)
               .repeatForever();

       return TriggerBuilder.newTrigger()
               .forJob(exportFileCleanupJobDetail())
               .withIdentity("exportFileCleanupTrigger")
               .startNow()
               .withSchedule(scheduleBuilder)
               .build();
   }

   /**
    * 导出文件清理任务
    */
   @Bean
   public JobDetail exportFileCleanupJobDetail() {
       return JobBuilder.newJob(ExportFileCleanupJob.class)
               .withIdentity("exportFileCleanupJob")
               .storeDurably()
               .build();
   }

   /**
    * 销售数据全量同步任务触发器，每天晚上12点执行一次
    */
   @Bean
   public Trigger salesFullSyncTrigger() {
       // 每天晚上12点执行一次
       CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule("0 0 0 * * ?");

       return TriggerBuilder.newTrigger()
               .forJob(salesSyncJobDetail())
               .withIdentity("salesFullSyncTrigger")
               .startNow()
               .withSchedule(scheduleBuilder)
               .build();
   }

   /**
    * 销售数据增量同步任务触发器，每15分钟执行一次，仅在白天6:00-21:00期间
    */
   @Bean
   public Trigger salesIncrementalSyncTrigger() {
       // 每15分钟执行一次，在每天的6点到21点之间
       CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule("0 0/15 6-23 * * ?");

       return TriggerBuilder.newTrigger()
               .forJob(salesSyncJobDetail())
               .withIdentity("salesIncrementalSyncTrigger")
               .startNow()
               .withSchedule(scheduleBuilder)
               .build();
   }

   /**
    * 销售数据同步任务
    */
   @Bean
   public JobDetail salesSyncJobDetail() {
       return JobBuilder.newJob(SalesSyncQuartzJob.class)
               .withIdentity("salesSyncJob")
               .storeDurably()
               .build();
   }

   /**
    * 销售统计数据同步任务触发器，每天凌晨0点执行一次
    */
   @Bean
   public Trigger salesStatisticsSyncTrigger() {
       // 每天凌晨0点执行一次
       CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule("0 0 0 * * ?");

       return TriggerBuilder.newTrigger()
               .forJob(salesStatisticsSyncJobDetail())
               .withIdentity("salesStatisticsSyncTrigger")
               .startNow()
               .withSchedule(scheduleBuilder)
               .build();
   }

   /**
    * 销售统计数据同步任务立即执行触发器，仅执行一次，用于启动时立即执行测试
    */
   @Bean
   public Trigger salesStatisticsImmediateTrigger() {
       // 创建一个立即执行但不重复的触发器
       SimpleScheduleBuilder scheduleBuilder = SimpleScheduleBuilder.simpleSchedule()
               .withRepeatCount(0); // 只执行一次，不重复

       return TriggerBuilder.newTrigger()
               .forJob(salesStatisticsSyncJobDetail())
               .withIdentity("salesStatisticsImmediateTrigger")
               .startNow() // 立即开始执行
               .withSchedule(scheduleBuilder)
               .build();
   }

   /**
    * 销售统计数据同步任务
    */
   @Bean
   public JobDetail salesStatisticsSyncJobDetail() {
       return JobBuilder.newJob(SalesStatisticsQuartzJob.class)
               .withIdentity("salesStatisticsSyncJob")
               .storeDurably()
               .build();
   }

   /**
    * 备货单数据同步任务触发器，每小时执行一次
    */
   @Bean
   public Trigger purchaseOrderSyncTrigger() {
       // 每小时执行一次
       SimpleScheduleBuilder scheduleBuilder = SimpleScheduleBuilder.simpleSchedule()
               .withIntervalInHours(1)
               .repeatForever();

       return TriggerBuilder.newTrigger()
               .forJob(purchaseOrderSyncJobDetail())
               .withIdentity("purchaseOrderSyncTrigger")
               .startNow()
               .withSchedule(scheduleBuilder)
               .build();
   }

   /**
    * 备货单数据同步任务
    */
   @Bean
   public JobDetail purchaseOrderSyncJobDetail() {
       return JobBuilder.newJob(PurchaseOrderSyncQuartzJob.class)
               .withIdentity("purchaseOrderSyncJob")
               .storeDurably()
               .build();
   }

   /**
    * JIT即将逾期通知任务（每小时执行一次）
    */
   @Bean
   public JobDetail jitSoonOverdueJobDetail() {
       return JobBuilder.newJob(PurchaseOrderNotificationQuartzJob.class)
               .withIdentity("jitSoonOverdueJob")
               .storeDurably()
               .build();
   }

   /**
    * JIT即将逾期通知任务触发器（每天10:00执行）
    */
   @Bean
   public Trigger jitSoonOverdueTrigger() {
       CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule("0 0 10 * * ?");

       return TriggerBuilder.newTrigger()
               .forJob(jitSoonOverdueJobDetail())
               .withIdentity("jitSoonOverdueTrigger")
               .startNow()
               .withSchedule(scheduleBuilder)
               .build();
   }

   /**
    * JIT已逾期通知任务（每天一次）
    */
   @Bean
   public JobDetail jitOverdueJobDetail() {
       return JobBuilder.newJob(PurchaseOrderNotificationQuartzJob.class)
               .withIdentity("jitOverdueJob")
               .storeDurably()
               .build();
   }

   /**
    * JIT已逾期通知任务触发器（每天10:01执行）
    */
   @Bean
   public Trigger jitOverdueTrigger() {
       CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule("0 1 10 * * ?");

       return TriggerBuilder.newTrigger()
               .forJob(jitOverdueJobDetail())
               .withIdentity("jitOverdueTrigger")
               .startNow()
               .withSchedule(scheduleBuilder)
               .build();
   }

   /**
    * 普通备货未发货通知任务
    */
   @Bean
   public JobDetail normalNotDeliveredJobDetail() {
       return JobBuilder.newJob(PurchaseOrderNotificationQuartzJob.class)
               .withIdentity("normalNotDeliveredJob")
               .storeDurably()
               .build();
   }

   /**
    * 普通备货未发货通知任务触发器（每天10:2执行）
    */
   @Bean
   public Trigger normalNotDeliveredTrigger() {
       CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule("0 2 10 * * ?");

       return TriggerBuilder.newTrigger()
               .forJob(normalNotDeliveredJobDetail())
               .withIdentity("normalNotDeliveredTrigger")
               .startNow()
               .withSchedule(scheduleBuilder)
               .build();
   }

   /**
    * 普通备货未到货通知任务
    */
   @Bean
   public JobDetail normalNotReceivedJobDetail() {
       return JobBuilder.newJob(PurchaseOrderNotificationQuartzJob.class)
               .withIdentity("normalNotReceivedJob")
               .storeDurably()
               .build();
   }

   /**
    * 普通备货未到货通知任务触发器（每天10:3执行）
    */
   @Bean
   public Trigger normalNotReceivedTrigger() {
       CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule("0 3 10 * * ?");

       return TriggerBuilder.newTrigger()
               .forJob(normalNotReceivedJobDetail())
               .withIdentity("normalNotReceivedTrigger")
               .startNow()
               .withSchedule(scheduleBuilder)
               .build();
   }
}