package com.xiao.temu.modules.sync.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.sync.entity.ProductSyncTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 商品同步任务Mapper
 */
@Mapper
public interface ProductSyncTaskMapper extends BaseMapper<ProductSyncTask> {
    
    /**
     * 根据店铺ID获取同步任务
     * 
     * @param shopId 店铺ID
     * @return 同步任务
     */
    @Select("SELECT * FROM product_sync_task WHERE shop_id = #{shopId}")
    ProductSyncTask getByShopId(@Param("shopId") Long shopId);
    
    /**
     * 根据店铺ID列表获取同步任务
     * 
     * @param shopIds 店铺ID列表
     * @return 同步任务列表
     */
    List<ProductSyncTask> getByShopIds(@Param("shopIds") List<Long> shopIds);
    
    /**
     * 获取所有同步任务
     * 
     * @return 所有同步任务
     */
    @Select("SELECT * FROM product_sync_task ORDER BY last_sync_time DESC")
    List<ProductSyncTask> getAllTasks();
    
    /**
     * 直接清空时间字段
     * 
     * @param params 参数，包含shopId
     * @return 更新行数
     */
    int clearTimeFields(Map<String, Object> params);
} 