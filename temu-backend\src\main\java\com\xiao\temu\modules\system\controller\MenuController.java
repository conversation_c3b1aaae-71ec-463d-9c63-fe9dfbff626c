package com.xiao.temu.modules.system.controller;

import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.modules.system.entity.SysMenu;
import com.xiao.temu.security.annotation.RequiresPermission;
import com.xiao.temu.security.model.UserDetailsImpl;
import com.xiao.temu.modules.system.service.MenuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 菜单管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/system/menu")
@RequiredArgsConstructor
public class MenuController {

    private final MenuService menuService;

    /**
     * 获取菜单列表
     */
    @GetMapping("/list")
    @RequiresPermission("system:menu:list")
    public ApiResponse getMenuList(SysMenu menu) {
        List<SysMenu> menus = menuService.getMenuList(menu);
        // 构建树形结构
        List<SysMenu> menuTree = menuService.buildMenuTree(menus);
        return ApiResponse.success(menuTree);
    }

    /**
     * 获取菜单树
     */
    @GetMapping("/tree")
    @RequiresPermission("system:menu:list")
    public ApiResponse getMenuTree() {
        List<SysMenu> menus = menuService.getAllMenus();
        List<SysMenu> menuTree = menuService.buildMenuTree(menus);
        return ApiResponse.success(menuTree);
    }

    /**
     * 获取当前用户菜单树
     */
    @GetMapping("/userMenuTree")
    public ApiResponse getUserMenuTree() {
        // 从SecurityContext中获取当前登录用户ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        log.info("获取菜单树 - 认证信息: {}", authentication);
        
        if (authentication == null || !(authentication.getPrincipal() instanceof UserDetailsImpl)) {
            log.error("获取菜单树失败 - 用户未登录或认证信息不正确");
            return ApiResponse.error("用户未登录");
        }
        
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        Long userId = userDetails.getUserId();
        log.info("获取菜单树 - 用户ID: {}", userId);
        
        List<SysMenu> menus = menuService.getMenusByUserId(userId);
        log.info("获取菜单树 - 菜单数量: {}", menus != null ? menus.size() : 0);
        
        List<SysMenu> menuTree = menuService.buildMenuTree(menus);
        log.info("获取菜单树 - 菜单树节点数量: {}", menuTree != null ? menuTree.size() : 0);
        
        return ApiResponse.success(menuTree);
    }

    /**
     * 根据角色ID获取菜单树
     */
    @GetMapping("/roleMenuTree/{roleId}")
    @RequiresPermission("system:menu:list")
    public ApiResponse getRoleMenuTree(@PathVariable Long roleId) {
        List<SysMenu> menus = menuService.getMenusByRoleId(roleId);
        List<SysMenu> menuTree = menuService.buildMenuTree(menus);
        return ApiResponse.success(menuTree);
    }

    /**
     * 获取菜单详细信息
     */
    @GetMapping("/{menuId}")
    @RequiresPermission("system:menu:query")
    public ApiResponse getMenu(@PathVariable Long menuId) {
        SysMenu menu = menuService.getMenuById(menuId);
        if (menu == null) {
            return ApiResponse.error("菜单不存在");
        }
        return ApiResponse.success(menu);
    }

    /**
     * 新增菜单
     */
    @PostMapping
    @RequiresPermission("system:menu:add")
    public ApiResponse addMenu(@Validated @RequestBody SysMenu menu) {
        // 插入菜单信息
        int rows = menuService.insertMenu(menu);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("新增菜单失败");
    }

    /**
     * 修改菜单
     */
    @PutMapping
    @RequiresPermission("system:menu:edit")
    public ApiResponse updateMenu(@Validated @RequestBody SysMenu menu) {
        // 更新菜单信息
        try {
            int rows = menuService.updateMenu(menu);
            return rows > 0 ? ApiResponse.success() : ApiResponse.error("修改菜单失败");
        } catch (RuntimeException e) {
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 删除菜单
     */
    @DeleteMapping("/{menuId}")
    @RequiresPermission("system:menu:remove")
    public ApiResponse deleteMenu(@PathVariable Long menuId) {
        // 检查是否有子菜单
        if (menuService.hasChildMenu(menuId)) {
            return ApiResponse.error("存在子菜单，不允许删除");
        }
        
        // 检查菜单是否已分配给角色
        if (menuService.isMenuAssigned(menuId)) {
            return ApiResponse.error("菜单已分配给角色，不允许删除");
        }
        
        // 删除菜单
        try {
            int rows = menuService.deleteMenu(menuId);
            return rows > 0 ? ApiResponse.success() : ApiResponse.error("删除菜单失败");
        } catch (RuntimeException e) {
            return ApiResponse.error(e.getMessage());
        }
    }
} 