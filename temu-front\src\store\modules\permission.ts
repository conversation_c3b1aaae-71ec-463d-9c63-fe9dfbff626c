import { defineStore } from 'pinia'
import { constantRoutes } from '@/router'
import router from '@/router'
import { getRouters } from '@/api/menu'
import type { RouteRecordRaw } from 'vue-router'
import { getViewComponent } from '@/utils/importRouter'

/**
 * 使用meta.role确定当前用户是否有权限访问
 * @param roles
 * @param route
 */
function hasPermission(roles: string[], route: RouteRecordRaw) {
  // 对于带有perms权限标识的路由进行检查
  if (route.meta && route.meta.perms) {
    // 如果用户是admin，直接返回true
    if (roles.includes('admin')) {
      return true
    }
    
    // common用户需要根据perms检查
    // 对于目录类型，始终返回true
    if (route.meta.menuType === 'M') {
      return true
    }
    
    // 这里不检查roles而是检查路由的perms属性
    // 如果路由有明确的perms权限要求，那么我们认为这是进行权限控制的关键
    return true // 先假设所有菜单都可见，主要通过后端控制
  }
  
  // 如果是目录类型，直接允许访问
  if (route.meta && route.meta.menuType === 'M') {
    return true
  }
  
  // 如果路由没有设置权限要求，则允许访问
  return true
}

/**
 * 通过递归过滤异步路由表
 * @param routes 异步路由
 * @param roles 角色
 */
function filterAsyncRoutes(routes: RouteRecordRaw[], roles: string[]) {
  const res: RouteRecordRaw[] = []

  routes.forEach(route => {
    const tmp = { ...route }
    
    // 简化判断逻辑: 
    // 1. 目录类型菜单始终保留
    const isDirectoryMenu = tmp.meta && tmp.meta.menuType === 'M'
    
    // 2. 检查权限: 目录类型或有权限的其他类型都保留
    if (isDirectoryMenu || hasPermission(roles, tmp)) {
      // 递归处理子路由
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      } else {
        tmp.children = []
      }
      
      // 重要修改: 即使子路由为空，也保留目录型菜单和页面菜单 (C类型)
      // 只有当非目录且非页面类型菜单的子路由为空时，才不添加
      if (tmp.children.length === 0 && !isDirectoryMenu && tmp.meta?.menuType !== 'C') {
        return
      }
      
      res.push(tmp)
    }
  })

  return res
}

interface PermissionState {
  routes: RouteRecordRaw[]
  addRoutes: RouteRecordRaw[]
  isGeneratingRoutes: boolean
}

export const usePermissionStore = defineStore('permission', {
  state: (): PermissionState => ({
    routes: [],
    addRoutes: [],
    isGeneratingRoutes: false
  }),
  actions: {
    // 设置路由
    setRoutes(routes: RouteRecordRaw[]) {
      this.addRoutes = routes
      
      // 合并静态路由和动态路由，并确保不会有重复的Dashboard路由
      const mergedRoutes: RouteRecordRaw[] = []
      
      // 查找并添加单独的首页路由
      const dashboardRoute = constantRoutes.find(route => route.path === '/dashboard' || route.path === '/')
      if (dashboardRoute) {
        // 确保首页路由是一个独立的菜单项，不是可折叠目录
        const homepageRoute = {...dashboardRoute}
        
        // 确保meta存在
        if (!homepageRoute.meta) {
          homepageRoute.meta = {}
        }
        
        // 设置menuType为'C'(菜单)而不是'M'(目录)
        homepageRoute.meta.menuType = 'C'
        
        // 强制设置alwaysShow为false，确保不会作为可折叠菜单
        homepageRoute.meta.alwaysShow = false as const
        
        // 明确设置标题为"首页"
        homepageRoute.meta.title = '首页'
        
        // 去掉子路由
        if (homepageRoute.children && homepageRoute.children.length > 0) {
          const indexRoute = homepageRoute.children.find(child => 
            child.path === 'index' || child.path === '' || child.path === '/')
          
          if (indexRoute) {
            // 将首页子路由的属性合并到首页路由，但保留标题为"首页"
            homepageRoute.meta.icon = indexRoute.meta?.icon || 'House'
            homepageRoute.component = indexRoute.component
          }
          
          // 清空子路由，确保不作为目录展示
          homepageRoute.children = []
        }
        
        mergedRoutes.push(homepageRoute)
      }
      
      // 检查是否是首页相关路由
      const isDashboardRoute = (route: RouteRecordRaw): boolean => {
        if (!route) return false;
        
        // 确保路径非空
        const routePath = route.path || '';
        const routeName = route.name ? String(route.name) : '';
        
        return routePath === '/' || 
                routePath === '/dashboard' || 
                routePath.endsWith('/dashboard') ||
                routeName === 'Dashboard' || 
                routeName === '首页' || 
                routeName.toLowerCase().includes('dashboard');
      }
      
      // 检查是否是"首页"目录 - 即只含首页路由的目录
      const isDashboardDirectory = (route: RouteRecordRaw): boolean => {
        if (!route || !route.meta) return false;
        
        // 检查是否是目录
        const isDirectory = route.meta.menuType === 'M';
        
        // 没有子路由或者不是目录，则不是首页目录
        if (!isDirectory || !route.children || route.children.length === 0) {
          return false;
        }
        
        // 检查是否只有一个子路由，且为首页
        const hasDashboardChild = route.children.length === 1 && isDashboardRoute(route.children[0]);
        
        if (isDirectory && hasDashboardChild) {
          return true;
        }
        
        return false;
      }
      
      // 添加静态路由中非首页相关路由
      constantRoutes.forEach(route => {
        if (!isDashboardRoute(route) && !isDashboardDirectory(route) && 
            route.path !== '/login' && route.path !== '/404') {
          mergedRoutes.push(route)
        }
      })
      
      // 添加动态路由(非首页相关)
      routes.forEach(route => {
        // 跳过首页相关路由
        if (isDashboardRoute(route) || isDashboardDirectory(route)) {
          return
        }
        
        // 跳过登录和404路由
        if (route.path === '/login' || route.path === '/404') {
          return
        }
        
        // 递归处理路由树，移除首页路由
        const processRoute = (r: RouteRecordRaw): RouteRecordRaw => {
          if (r.children && r.children.length > 0) {
            // 筛选掉子路由中的首页路由
            r.children = r.children
              .filter(child => !isDashboardRoute(child))
              .map(child => processRoute(child))
          }
          return r
        }
        
        const processedRoute = processRoute(route)
        
        // 如果处理后仍有内容，则添加路由
        mergedRoutes.push(processedRoute)
      })
      
      this.routes = mergedRoutes
    },
    // 生成路由
    async generateRoutes(roles: string[]) {
      if (this.isGeneratingRoutes) {
        return this.routes
      }
      
      try {
        this.isGeneratingRoutes = true
        
        // 从服务器获取路由
        const res = await getRouters()
        
        // 转换为前端路由结构
        let accessedRoutes: RouteRecordRaw[] = []
        
        if (res.data && res.data.length > 0) {
          // 处理菜单数据，确保路径都以斜杠开头
          accessedRoutes = this.formatMenuToRoutes(res.data)
        }
        
        // 添加静态路由中的profile路由确保其在路由表中
        // 这是个特殊处理，确保/profile路由始终可访问
        const profileRoute = constantRoutes.find(route => route.path === '/profile')
        if (profileRoute) {
          const profileRouteClone = JSON.parse(JSON.stringify(profileRoute)) as RouteRecordRaw
          
          // 确保元数据正确
          if (!profileRouteClone.meta) {
            profileRouteClone.meta = {}
          }
          profileRouteClone.meta.menuType = 'C'
          // 确保hidden属性为true, 这样在侧边栏不会显示
          profileRouteClone.meta.hidden = true
          
          // 将其添加到路由列表
          accessedRoutes.push(profileRouteClone)
        }
        
        // 生成路由并设置perms到meta
        accessedRoutes.forEach(route => {
          this.setPermsToMeta(route)
        })
        
        let filteredRoutes = accessedRoutes
        
        // 过滤路由 - 对所有用户都应用过滤，但admin会自动通过权限检查
        filteredRoutes = filterAsyncRoutes(accessedRoutes, roles)
        
        // 清除现有动态路由
        try {
          const existingRoutes = router.getRoutes()
          existingRoutes.forEach(route => {
            if (route.name && !constantRoutes.some(cr => cr.name === route.name) && route.name !== 'Dashboard' && route.name !== 'DashboardIndex' && route.name !== 'Profile') {
              router.removeRoute(route.name)
            }
          })
        } catch (e) {
          console.error('清除现有路由时发生错误:', e)
        }
        
        // 添加路由到router
        filteredRoutes.forEach(route => {
          router.addRoute(route)
        })
        
        this.setRoutes(filteredRoutes)
        return filteredRoutes
      } catch (error) {
        console.error('生成路由时发生错误:', error)
        return []
      } finally {
        this.isGeneratingRoutes = false
      }
    },
    
    // 新增：将perms设置到meta中，便于权限检查
    setPermsToMeta(route: RouteRecordRaw) {
      if (route.meta && route.meta.perms) {
        // 已经存在perms，不需要处理
      } else if (route.meta && !route.meta.perms) {
        // 如果meta存在但perms不存在，初始化perms
        route.meta.perms = ''
      }
      
      // 递归处理子路由
      if (route.children && route.children.length > 0) {
        route.children.forEach(child => {
          this.setPermsToMeta(child)
        })
      }
    },
    
    // 格式化菜单为路由
    formatMenuToRoutes(menus: any[]): RouteRecordRaw[] {
      const routes: RouteRecordRaw[] = []
      
      menus.forEach(menu => {
        // 跳过类型为按钮的菜单
        if (menu.menuType === 'F') {
          return
        }
        
        // 确保path以斜杠开头
        let path = menu.path
        if (path && !path.startsWith('/')) {
          path = `/${path}`
        }
        
        // 创建路由对象
        const route: any = {
          path: path || '',
          name: menu.menuName || menu.name || '',
          meta: {
            title: menu.menuName || menu.name,
            icon: menu.icon !== '#' ? menu.icon : 'Menu',
            menuType: menu.menuType,     // 添加菜单类型到meta
            perms: menu.perms || '',     // 添加权限标识到meta
            keepAlive: menu.keepAlive === '1'
          }
        }
        
        // 处理组件路径
        if (menu.component) {
          // 对于具体组件路径的处理
          if (menu.component === 'Layout') {
            route.component = () => import('@/layout/index.vue')
          } else {
            // 移除开头的斜杠，确保路径格式正确
            const componentPath = menu.component.startsWith('/') 
              ? menu.component.substring(1) 
              : menu.component
                
            console.log('处理组件路径:', componentPath)
                
            // 使用importRouter工具加载组件
            route.component = getViewComponent(componentPath)
          }
        } else if (menu.menuType === 'M' || (menu.children && menu.children.length > 0)) {
          route.component = () => import('@/layout/index.vue')
        } else {
          // 没有组件也没有子菜单的情况，这种情况可能有问题
          route.component = () => import('@/views/error/404.vue')
        }
        
        // 处理子菜单
        if (menu.children && menu.children.length > 0) {
          // 过滤掉按钮类型的菜单项
          const childMenus = menu.children.filter(child => child.menuType !== 'F')
          
          // 递归处理子菜单
          const childRoutes = this.formatMenuToRoutes(childMenus)
          
          if (childRoutes.length > 0) {
            // 确保每个子路由都有正确的path
            childRoutes.forEach(childRoute => {
              // 修复子路由的path
              // 如果子路由path不是以/开头，并且不是相对于当前路由的路径，则需要处理
              if (childRoute.path && !childRoute.path.startsWith('/')) {
                // 如果父路由path是"/"，则直接添加子路由path
                if (route.path === '/') {
                  childRoute.path = '/' + childRoute.path
                } else {
                  // 否则拼接父路由path和子路由path
                  childRoute.path = route.path + '/' + childRoute.path
                }
              }
            });
            
            route.children = childRoutes
          } else {
            route.children = [] // 确保children不是undefined
          }
        } else {
          route.children = [] // 确保children不是undefined
        }
        
        routes.push(route as RouteRecordRaw)
      })
      
      return routes
    }
  }
}) 