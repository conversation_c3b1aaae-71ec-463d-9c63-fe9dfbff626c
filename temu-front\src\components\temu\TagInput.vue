<template>
  <div class="tag-input-wrapper">
    <div class="input-container" :class="{ 'has-border': border }">
      <div class="id-type-select-wrapper" v-if="showTypeSelect">
        <el-select 
          v-model="selectedType" 
          :placeholder="typeSelectPlaceholder" 
          size="small"
          style="width: 80px"
        >
          <el-option 
            v-for="option in typeOptions" 
            :key="option.value" 
            :label="option.label" 
            :value="option.value" 
          />
        </el-select>
      </div>
      <div class="tags-input-container">
        <div class="tags-container" v-if="modelValue && modelValue.length > 0">
          <el-tag
            v-for="(tag, index) in modelValue"
            :key="`tag-${index}`"
            closable
            size="small"
            @close="handleRemoveTag(index)"
          >
            {{ tag }}
          </el-tag>
        </div>
        <el-input
          v-model="inputValue"
          :placeholder="modelValue && modelValue.length > 0 ? '' : placeholder"
          @keyup.enter="handleAddTag"
          @keyup.space="handleAddTag"
          @keyup.comma="handleAddTag"
          size="small"
          class="tag-main-input"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: Array,
    required: true
  },
  isNumeric: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: '多个以空格，逗号等分隔'
  },
  border: {
    type: Boolean,
    default: true
  },
  showTypeSelect: {
    type: Boolean,
    default: false
  },
  typeValue: {
    type: String,
    default: null
  },
  typeOptions: {
    type: Array,
    default: () => []
  },
  typeSelectPlaceholder: {
    type: String,
    default: '类型'
  }
});

const emit = defineEmits(['update:modelValue', 'update:typeValue']);

const inputValue = ref('');
const selectedType = ref(props.typeValue);

// 监听类型选择变化
watch(selectedType, (newValue) => {
  emit('update:typeValue', newValue);
});

// 添加标签
const handleAddTag = () => {
  if (!inputValue.value.trim()) return;
  
  // 处理可能的多值输入（逗号、空格分隔）
  const values = inputValue.value.trim().split(/[,\s]+/).filter(Boolean);
  
  // 创建新数组以保持响应性
  const newValues = [...props.modelValue];
  
  // 将新值添加到数组（去重）
  values.forEach(val => {
    const trimmedVal = val.trim();
    
    if (!trimmedVal) return;
    
    if (props.isNumeric) {
      // 尝试转换为数字
      const numVal = Number(trimmedVal);
      if (!isNaN(numVal) && numVal > 0) {
        // 检查是否已存在于数组中
        if (!newValues.includes(numVal)) {
          newValues.push(numVal);
        }
      } else {
        // 可以在这里添加错误提示
        console.warn(`"${trimmedVal}" 不是有效的数字，已忽略`);
      }
    } else {
      // 字符串类型
      if (!newValues.includes(trimmedVal)) {
        newValues.push(trimmedVal);
      }
    }
  });
  
  // 更新绑定值
  emit('update:modelValue', newValues);
  
  // 清空输入框
  inputValue.value = '';
};

// 移除标签
const handleRemoveTag = (index) => {
  if (index < 0 || index >= props.modelValue.length) return;
  
  // 创建新数组以保持响应性
  const newValues = [...props.modelValue];
  newValues.splice(index, 1);
  
  // 更新绑定值
  emit('update:modelValue', newValues);
};
</script>

<style scoped>
.tag-input-wrapper {
  width: 100%;
}

.input-container {
  display: flex;
  align-items: flex-start;
  width: 100%;
  padding: 4px 8px;
  background-color: #FFF;
  min-height: 32px; /* 确保最小高度 */
  box-sizing: border-box;
}

.input-container.has-border {
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  overflow: visible; /* 允许内容溢出但保留边框 */
}

.input-container:hover {
  border-color: #C0C4CC;
}

.input-container:focus-within {
  border-color: #409EFF;
}

.id-type-select-wrapper {
  margin-right: 8px;
  flex-shrink: 0; /* 防止类型选择器被压缩 */
}

.id-type-select-wrapper :deep(.el-input__wrapper) {
  box-shadow: none !important;
  background-color: #f5f7fa;
  padding: 0 8px;
  border-radius: 4px;
}

.tags-input-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: visible; /* 允许内容溢出 */
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 5px;
  width: 100%;
}

.tag-main-input {
  width: 100%;
  min-height: 22px; /* 确保输入框高度 */
}

.tag-main-input :deep(.el-input__wrapper) {
  box-shadow: none !important;
  padding: 0;
  width: 100%;
  min-height: 22px;
  border-bottom: none; /* 移除下框线 */
}
</style> 