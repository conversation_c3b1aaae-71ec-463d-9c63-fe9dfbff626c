package com.xiao.temu.modules.quality.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xiao.temu.modules.quality.service.DefectImageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 疵点图片工具类
 */
@Slf4j
public class DefectImageUtils {

    // 转存后的图片KEY字段名
    private static final String IMAGE_KEY_FIELD = "imageKey";
    // 原始URL字段名
    private static final String URL_FIELD = "url";

    /**
     * 从附件JSON中提取图片URL列表
     * 
     * @param attachmentsJson 附件JSON字符串
     * @param defectImageService 疵点图片服务（可为null，如果提供则使用转存后的URL）
     * @return 图片URL列表
     */
    public static List<String> extractImageUrls(String attachmentsJson, DefectImageService defectImageService) {
        List<String> imageUrls = new ArrayList<>();
        if (!StringUtils.hasText(attachmentsJson)) {
            return imageUrls;
        }

        try {
            JSONArray attachments = JSON.parseArray(attachmentsJson);
            if (attachments == null || attachments.isEmpty()) {
                return imageUrls;
            }

            for (int i = 0; i < attachments.size(); i++) {
                JSONObject attachment = attachments.getJSONObject(i);
                if (attachment == null) {
                    continue;
                }

                // 检查是否有转存后的imageKey
                if (defectImageService != null && attachment.containsKey(IMAGE_KEY_FIELD)) {
                    String imageKey = attachment.getString(IMAGE_KEY_FIELD);
                    if (StringUtils.hasText(imageKey)) {
                        // 获取转存后的访问URL
                        String imageUrl = defectImageService.getDefectImageUrl(imageKey);
                        if (StringUtils.hasText(imageUrl)) {
                            imageUrls.add(imageUrl);
                            continue;
                        }
                    }
                }

                // 如果没有转存的图片或获取失败，则使用原始URL
                String originalUrl = attachment.getString(URL_FIELD);
                if (StringUtils.hasText(originalUrl)) {
                    imageUrls.add(originalUrl);
                }
            }
        } catch (Exception e) {
            log.error("解析附件JSON异常: {}, error: {}", attachmentsJson, e.getMessage(), e);
        }

        return imageUrls;
    }

    /**
     * 从附件JSON中提取原始图片URL列表
     * 
     * @param attachmentsJson 附件JSON字符串
     * @return 原始图片URL列表
     */
    public static List<String> extractOriginalImageUrls(String attachmentsJson) {
        return extractImageUrls(attachmentsJson, null);
    }
} 