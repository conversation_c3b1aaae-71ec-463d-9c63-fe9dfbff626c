package com.xiao.temu.modules.product.mapper;

import com.xiao.temu.modules.product.entity.ProductSku;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品SKU数据访问接口
 */
@Mapper
public interface ProductSkuMapper {
    
    /**
     * 批量插入SKU数据
     *
     * @param skuList SKU数据列表
     * @return 影响行数
     */
    int batchInsert(List<ProductSku> skuList);
    
    /**
     * 根据店铺ID和SKC ID查询SKU列表
     *
     * @param shopId 店铺ID
     * @param productSkcId 商品SKC ID
     * @return SKU列表
     */
    List<ProductSku> selectByShopIdAndSkcId(@Param("shopId") Long shopId, @Param("productSkcId") Long productSkcId);
    
    /**
     * 根据店铺ID查询SKU列表
     *
     * @param shopId 店铺ID
     * @return SKU列表
     */
    List<ProductSku> selectByShopId(@Param("shopId") Long shopId);
    
    /**
     * 根据店铺ID清空SKU数据
     *
     * @param shopId 店铺ID
     * @return 影响行数
     */
    int deleteByShopId(@Param("shopId") Long shopId);
    
    /**
     * 根据店铺ID统计SKU数量
     *
     * @param shopId 店铺ID
     * @return SKU数量
     */
    int countByShopId(@Param("shopId") Long shopId);
    
    /**
     * 查询指定店铺的商品SKU总数
     *
     * @param shopId 店铺ID
     * @return 商品SKU总数
     */
    Integer getProductSkuCountByShopId(@Param("shopId") Long shopId);
} 