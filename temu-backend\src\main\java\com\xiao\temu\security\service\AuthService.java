package com.xiao.temu.security.service;

import com.xiao.temu.security.model.AuthResult;
import com.xiao.temu.security.model.LoginRequest;

/**
 * 认证服务接口
 */
public interface AuthService {

    /**
     * 用户登录
     *
     * @param loginRequest 登录请求
     * @return 认证结果
     */
    AuthResult login(LoginRequest loginRequest);

    /**
     * 用户注册
     *
     * @param username 用户名
     * @param password 密码
     * @param nickName 昵称
     * @return 认证结果
     */
    AuthResult register(String username, String password, String nickName);

    /**
     * 刷新Token
     *
     * @param oldToken 旧Token
     * @return 新的认证结果
     */
    AuthResult refreshToken(String oldToken);

    /**
     * 获取当前用户认证信息
     *
     * @return 当前用户认证信息
     */
    AuthResult getCurrentUser();

    /**
     * 退出登录
     */
    void logout();
} 