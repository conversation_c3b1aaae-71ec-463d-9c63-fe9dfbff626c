/**
 * 应用程序入口
 */
const pageRefreshUtils = require('./utils/pageRefreshUtils');

App({
  globalData: {
    userInfo: null,
    token: ""
  },
  
  /**
   * 小程序启动时执行
   */
  onLaunch() {
    try {
      // 获取存储的token
      const token = wx.getStorageSync("token");
      if (token) {
        this.globalData.token = token;
      }
      
      // 获取存储的用户信息
      const userInfo = wx.getStorageSync("userInfo");
      if (userInfo) {
        this.globalData.userInfo = userInfo;
      }
      
      // 初始化页面刷新机制
      this.initPageRefresh();
      
      console.log('应用启动完成，登录状态:', !!token);
    } catch (error) {
      console.error('启动时读取存储数据失败:', error);
    }
  },
  
  /**
   * 初始化页面刷新机制
   */
  initPageRefresh: function() {
    // 标记所有tabBar页面为需要刷新
    pageRefreshUtils.markPageForRefresh('pages/home/<USER>');
    pageRefreshUtils.markPageForRefresh('pages/scan/scan');
    pageRefreshUtils.markPageForRefresh('pages/worklist/worklist');
    
    // 清理可能过期的页面访问记录
    wx.removeStorageSync('pageVisitRecord');
    
    console.log('页面刷新机制初始化完成');
  }
});