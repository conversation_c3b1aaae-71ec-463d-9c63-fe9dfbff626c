package com.xiao.temu.modules.purchaseorderv.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产进度实体类
 */
@Data
@TableName("production_progress")
public class ProductionProgress implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 备货单号
     */
    private String subPurchaseOrderSn;

    /**
     * 烧花/剪图状态(0未完成 1已完成)
     */
    private Integer cuttingStatus;

    /**
     * 烧花/剪图完成时间
     */
    private Date cuttingTime;

    /**
     * 烧花/剪图操作人ID
     */
    private Long cuttingOperatorId;

    /**
     * 车间/拣货状态(0未完成 1已完成)
     */
    private Integer workshopStatus;

    /**
     * 车间/拣货完成时间
     */
    private Date workshopTime;

    /**
     * 车间/拣货操作人ID
     */
    private Long workshopOperatorId;

    /**
     * 剪线/压图状态(0未完成 1已完成)
     */
    private Integer trimmingStatus;

    /**
     * 剪线/压图完成时间
     */
    private Date trimmingTime;

    /**
     * 剪线/压图操作人ID
     */
    private Long trimmingOperatorId;
    
    /**
     * 查货状态(0未完成 1已完成)
     */
    private Integer inspectionStatus;

    /**
     * 查货完成时间
     */
    private Date inspectionTime;

    /**
     * 查货操作人ID
     */
    private Long inspectionOperatorId;

    /**
     * 包装状态(0未完成 1已完成)
     */
    private Integer packagingStatus;

    /**
     * 包装完成时间
     */
    private Date packagingTime;

    /**
     * 包装操作人ID
     */
    private Long packagingOperatorId;

    /**
     * 发货状态(0未完成 1已完成)
     */
    private Integer shippingStatus;

    /**
     * 发货完成时间
     */
    private Date shippingTime;

    /**
     * 发货操作人ID
     */
    private Long shippingOperatorId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 