import request from '@/utils/request'
import type { LocalSalesRequestParams, LocalSalesResponse } from '@/types/local/sales'

/**
 * 获取销售数据列表
 * @param data 请求参数
 * @returns 接口响应
 */
export function getLocalSalesList(data: LocalSalesRequestParams) {
  return request<any, { code: number, message: string, data: LocalSalesResponse }>({
    url: '/local/sales/list',
    method: 'post',
    data
  })
}

/**
 * 获取当前用户有权限的店铺列表
 * @returns 接口响应
 */
export function getUserShops() {
  return request<any, { code: number, message: string, data: any[] }>({
    url: '/local/sales/shops',
    method: 'get'
  })
}

/**
 * 获取指定店铺下的仓库组列表
 * @param shopId 店铺ID
 * @returns 接口响应
 */
export function getWarehouseGroups(shopId: number) {
  return request<any, { code: number, message: string, data: any[] }>({
    url: `/local/sales/warehouse-groups`,
    method: 'get',
    params: { shopId }
  })
}

/**
 * 获取多个店铺下的仓库组列表
 * @param shopIds 店铺ID列表
 * @returns 接口响应
 */
export function getWarehouseGroupsByShopIds(shopIds: number[]) {
  return request<any, { code: number, message: string, data: any[] }>({
    url: `/local/sales/warehouse-groups/multi`,
    method: 'get',
    params: { shopIds: shopIds.join(',') }
  })
}

/**
 * 创建销售数据导出任务
 * @param data 请求参数
 * @returns 接口响应
 */
export function createSalesExportTask(data: any) {
  return request<any, { code: number, message: string, data: any }>({
    url: '/local/sales/createExportTask',
    method: 'post',
    data
  })
}

/**
 * 获取销售数据导出进度
 * @param taskId 任务ID
 * @returns 接口响应
 */
export function getSalesExportProgress(taskId: string) {
  return request<any, { code: number, message: string, data: any }>({
    url: `/local/sales/exportProgress/${taskId}`,
    method: 'get'
  })
}

/**
 * 下载销售数据Excel文件
 * @param taskId 任务ID
 * @returns 接口响应
 */
export function downloadSalesExcelFile(taskId: string) {
  return request({
    url: `/local/sales/downloadExcel/${taskId}`,
    method: 'get',
    responseType: 'blob'
  })
} 