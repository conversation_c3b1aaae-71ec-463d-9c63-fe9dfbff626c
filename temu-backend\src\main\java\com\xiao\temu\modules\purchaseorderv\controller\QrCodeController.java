package com.xiao.temu.modules.purchaseorderv.controller;

import com.alibaba.fastjson.JSON;
import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.common.utils.QrCodeUtils;
import com.xiao.temu.modules.purchaseorderv.dto.QrCodeDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 二维码控制器
 */
@Slf4j
@RestController
@RequestMapping("/v1/qrcode")
public class QrCodeController {

    /**
     * 生成备货单二维码(Base64格式)
     *
     * @param shopId 店铺ID
     * @param subPurchaseOrderSn 备货单号
     * @return Base64编码的二维码图片
     */
    @GetMapping("/generate/base64")
    public ApiResponse<String> generateQrCodeBase64(
            @RequestParam Long shopId,
            @RequestParam String subPurchaseOrderSn) {
        try {
            // 构造二维码数据
            QrCodeDataDTO qrCodeData = QrCodeDataDTO.builder()
                    .shopId(shopId)
                    .subPurchaseOrderSn(subPurchaseOrderSn)
                    .timestamp(System.currentTimeMillis())
                    .build();
            
            // 将数据转为JSON字符串
            String content = JSON.toJSONString(qrCodeData);
            
            // 生成二维码
            String base64QrCode = QrCodeUtils.generateQrCodeBase64(content, 300, 300);
            
            return ApiResponse.success(base64QrCode);
        } catch (Exception e) {
            log.error("生成二维码失败", e);
            return ApiResponse.error("生成二维码失败");
        }
    }

    /**
     * 生成备货单二维码(图片格式)
     *
     * @param shopId 店铺ID
     * @param subPurchaseOrderSn 备货单号
     * @return 二维码图片
     */
    @GetMapping("/generate/image")
    public ResponseEntity<byte[]> generateQrCodeImage(
            @RequestParam Long shopId,
            @RequestParam String subPurchaseOrderSn) {
        try {
            // 构造二维码数据
            QrCodeDataDTO qrCodeData = QrCodeDataDTO.builder()
                    .shopId(shopId)
                    .subPurchaseOrderSn(subPurchaseOrderSn)
                    .timestamp(System.currentTimeMillis())
                    .build();
            
            // 将数据转为JSON字符串
            String content = JSON.toJSONString(qrCodeData);
            
            // 生成二维码
            byte[] qrCodeBytes = QrCodeUtils.generateQrCodeBytes(content, 300, 300);
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_PNG);
            headers.set("Content-Disposition", "attachment; filename=" + subPurchaseOrderSn + "_qrcode.png");
            
            return new ResponseEntity<>(qrCodeBytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("生成二维码失败", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 解析二维码数据
     *
     * @param qrCodeData Base64编码的二维码数据
     * @return 解析后的数据
     */
    @PostMapping("/decode")
    public ApiResponse<QrCodeDataDTO> decodeQrCode(@RequestBody String qrCodeData) {
        try {
            // 这里简化处理，实际应用中需要使用ZXing解码图片
            // 这里假设传入的是已经从图片中提取的JSON字符串
            QrCodeDataDTO data = JSON.parseObject(qrCodeData, QrCodeDataDTO.class);
            return ApiResponse.success(data);
        } catch (Exception e) {
            log.error("解析二维码失败", e);
            return ApiResponse.error("解析二维码失败");
        }
    }
} 