<template>
  <el-dialog
    title="修改权限"
    v-model="dialogVisible"
    width="400px"
    append-to-body
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="店铺">
        <div>{{ assignment.shopName }}</div>
      </el-form-item>
      
      <el-form-item label="组员">
        <div>{{ assignment.nickName || assignment.userName }}</div>
      </el-form-item>
      
      <el-form-item label="权限类型" prop="permissionType">
        <el-radio-group v-model="form.permissionType">
          <el-radio label="0">只读</el-radio>
          <el-radio label="1">读写</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useLeaderAssignStore } from '@/store/modules/leaderAssign'
import type { FormInstance, FormRules } from 'element-plus'
import type { ShopAssignment } from '@/types/leaderAssign'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  assignment: {
    type: Object as () => ShopAssignment,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

const leaderAssignStore = useLeaderAssignStore()
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  id: null as number | null,
  permissionType: '1' // 默认读写权限
})

// 表单校验规则
const rules = reactive<FormRules>({
  permissionType: [
    { required: true, message: '请选择权限类型', trigger: 'change' }
  ]
})

// 添加计算属性处理对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 监听assignment变化
watch(() => props.assignment, (val) => {
  if (val && val.id) {
    form.id = val.id
    form.permissionType = val.permissionType
  }
}, { immediate: true, deep: true })

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await leaderAssignStore.updatePermission({
          id: form.id as number,
          permissionType: form.permissionType
        })
        
        ElMessage.success('修改成功')
        emit('success')
        handleClose()
      } catch (error: any) {
        ElMessage.error(error.message || '修改失败')
      }
    }
  })
}

// 关闭对话框
function handleClose() {
  emit('update:visible', false)
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style> 