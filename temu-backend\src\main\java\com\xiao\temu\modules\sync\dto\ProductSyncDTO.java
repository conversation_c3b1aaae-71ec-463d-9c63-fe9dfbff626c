package com.xiao.temu.modules.sync.dto;

import lombok.Data;

import java.util.List;

/**
 * 商品同步DTO
 */
@Data
public class ProductSyncDTO {
    
    /**
     * 店铺ID列表
     */
    private List<Long> shopIds;
    
    /**
     * 页码
     */
    private Integer pageNum;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
    
    /**
     * 同步状态
     */
    private Integer syncStatus;
    
    /**
     * 排序字段
     */
    private String sortField;
    
    /**
     * 排序方向：asc-升序，desc-降序
     */
    private String sortOrder;
} 