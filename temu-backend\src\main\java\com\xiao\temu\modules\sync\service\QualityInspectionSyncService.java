package com.xiao.temu.modules.sync.service;

import com.xiao.temu.modules.sync.entity.QualityInspectionSyncTask;
import com.xiao.temu.modules.sync.vo.ApiResponse;

import java.util.List;
import java.util.Set;

/**
 * 质检数据同步服务接口
 */
public interface QualityInspectionSyncService {

    /**
     * 同步指定店铺的质检数据
     *
     * @param shopId 店铺ID
     * @return 同步结果
     */
    ApiResponse.QualityInspectionSyncVO syncQualityInspectionData(Long shopId);
    
    /**
     * 根据SKU ID列表同步指定店铺的质检数据
     *
     * @param shopId 店铺ID
     * @param skuIds SKU ID列表
     * @return 同步结果
     */
    ApiResponse.QualityInspectionSyncVO syncQualityInspectionDataBySkuIds(Long shopId, Set<Long> skuIds);

    /**
     * 初始化店铺的同步任务
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    boolean initSyncTask(Long shopId);

    /**
     * 获取同步任务列表
     *
     * @param shopIds 店铺ID列表，为空则查询所有
     * @return 同步任务列表
     */
    List<QualityInspectionSyncTask> getSyncTasks(List<Long> shopIds);

    /**
     * 根据店铺ID获取同步任务
     *
     * @param shopId 店铺ID
     * @return 同步任务
     */
    QualityInspectionSyncTask getSyncTaskByShopId(Long shopId);

    /**
     * 执行定时同步
     * 
     * @return 同步结果
     */
    String executeScheduledSync();

    /**
     * 获取指定店铺的质检记录总数
     *
     * @param shopId 店铺ID
     * @return 质检记录总数
     */
    Integer getQualityInspectionCountByShopId(Long shopId);

    /**
     * 清空店铺的同步数据
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    boolean clearSyncData(Long shopId);
} 