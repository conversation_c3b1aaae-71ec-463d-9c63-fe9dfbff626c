-- 生产组组长成员同步脚本
-- 该脚本的目的是确保每个生产组的组长同时也是该组的成员
-- 这样即使通过leader_id查询失败，也能通过成员表获取到组长

-- 将所有现有的活跃生产组组长添加为成员（如果他们还不是成员）
INSERT INTO production_group_member (group_id, user_id, join_time, status)
SELECT 
    pg.group_id, 
    pg.leader_id, 
    NOW(), 
    '0'
FROM 
    production_group pg
WHERE 
    pg.status = '0' -- 仅处理活跃的生产组
    AND NOT EXISTS (
        -- 检查组长是否已经是该组成员
        SELECT 1 FROM production_group_member pgm 
        WHERE pgm.group_id = pg.group_id AND pgm.user_id = pg.leader_id
    )
ON DUPLICATE KEY UPDATE 
    status = '0',  -- 如果已存在但被禁用，则重新启用
    join_time = NOW(); -- 更新加入时间

-- 更新日志记录
SELECT CONCAT('同步了 ', ROW_COUNT(), ' 个生产组组长作为成员') AS sync_result; 