package com.xiao.temu.modules.purchaseorderv.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 批量备货单通知数据
 */
@Data
public class BatchPurchaseOrderNotificationData {
    
    /**
     * 通知类型名称
     */
    private String notifyTypeName;
    
    /**
     * 备货单总数量
     */
    private int totalOrderCount;
    
    /**
     * 店铺组项目列表
     */
    private List<ShopGroup> shopGroups = new ArrayList<>();
    
    /**
     * 店铺组
     */
    @Data
    public static class ShopGroup {
        /**
         * 店铺ID
         */
        private Long shopId;
        
        /**
         * 店铺名称
         */
        private String shopName;
        
        /**
         * 店铺备注
         */
        private String shopRemark;
        
        /**
         * 该店铺的备货单项目列表
         */
        private List<PurchaseOrderNotificationItem> items = new ArrayList<>();
    }
    
    /**
     * 备货单通知项目
     */
    @Data
    public static class PurchaseOrderNotificationItem {
        /**
         * 店铺ID
         */
        private Long shopId;
        
        /**
         * 店铺名称
         */
        private String shopName;
        
        /**
         * 备货单号
         */
        private String subPurchaseOrderSn;
        
        /**
         * 商品名称
         */
        private String productName;
        
        /**
         * 预计最晚到货时间 (适用于JIT类型)
         */
        private Date expectLatestArrivalTime;
        
        /**
         * 备货单创建时间 (适用于普通备货未发货类型)
         */
        private Date purchaseTime;
        
        /**
         * 发货时间 (适用于普通备货未到货类型)
         */
        private Date shippingTime;
    }
} 