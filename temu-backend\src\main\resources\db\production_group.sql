/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : temu_api

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 04/06/2025 02:02:18
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for production_group
-- ----------------------------
DROP TABLE IF EXISTS `production_group`;
CREATE TABLE `production_group`  (
  `group_id` bigint NOT NULL AUTO_INCREMENT COMMENT '生产组ID',
  `group_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '生产组名称',
  `leader_id` bigint NOT NULL COMMENT '负责人ID',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`group_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '生产组表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of production_group
-- ----------------------------
INSERT INTO `production_group` VALUES (1, '生产组1', 40, '0', '2025-05-09 16:04:04', '2025-06-04 01:34:59', '1');
INSERT INTO `production_group` VALUES (2, '生产组2', 23, '0', '2025-05-12 08:51:52', '2025-05-28 09:39:40', '123');
INSERT INTO `production_group` VALUES (3, '生产组3', 39, '0', '2025-05-12 10:28:14', '2025-06-04 01:05:25', '');

SET FOREIGN_KEY_CHECKS = 1;
