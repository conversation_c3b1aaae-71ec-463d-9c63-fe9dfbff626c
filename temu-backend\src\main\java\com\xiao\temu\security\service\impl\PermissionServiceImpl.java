package com.xiao.temu.security.service.impl;

import com.xiao.temu.modules.system.entity.SysRoleMenu;
import com.xiao.temu.modules.system.mapper.SysMenuMapper;
import com.xiao.temu.modules.system.mapper.SysRoleMapper;
import com.xiao.temu.modules.system.mapper.SysRoleMenuMapper;
import com.xiao.temu.modules.system.mapper.SysUserRoleMapper;
import com.xiao.temu.security.model.UserDetailsImpl;
import com.xiao.temu.security.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 权限服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionServiceImpl implements PermissionService {

    private final SysMenuMapper menuMapper;
    private final SysRoleMapper roleMapper;
    private final SysUserRoleMapper userRoleMapper;
    private final SysRoleMenuMapper roleMenuMapper;
    private final RedisTemplate<String, Object> redisTemplate;

    // 缓存key前缀
    private static final String PERMISSION_CACHE_PREFIX = "temu:permission:";
    private static final String ROLE_CACHE_PREFIX = "temu:role:";
    private static final String ROLE_PERMISSION_CACHE_PREFIX = "temu:role:permission:";
    private static final String ROLE_NAME_CACHE_PREFIX = "temu:role:name:";
    
    // 超级管理员角色标识
    private static final String ROLE_ADMIN = "admin";
    
    // 缓存过期时间（60分钟）
    private static final long CACHE_EXPIRATION = 60;

    @Override
    public List<String> getPermissionsByUserId(Long userId) {
        // 先从缓存中获取
        String cacheKey = PERMISSION_CACHE_PREFIX + userId;
        @SuppressWarnings("unchecked")
        List<String> permissionList = (List<String>) redisTemplate.opsForValue().get(cacheKey);

        // 缓存不存在则从数据库中查询
        if (permissionList == null) {
            permissionList = new ArrayList<>();
            
            // 获取用户角色列表
            List<String> roleList = getRolesByUserId(userId);
            
            // 用户拥有超级管理员角色，获取所有权限
            if (roleList.contains(ROLE_ADMIN)) {
                permissionList = menuMapper.selectAllPermissions();
            } else {
                // 根据用户角色集合获取权限列表
                permissionList = menuMapper.selectPermissionsByUserId(userId);
            }
            
            // 将权限列表放入缓存
            if (!CollectionUtils.isEmpty(permissionList)) {
                redisTemplate.opsForValue().set(cacheKey, permissionList, CACHE_EXPIRATION, TimeUnit.MINUTES);
            }
        }
        
        return permissionList;
    }

    @Override
    public List<String> getRolesByUserId(Long userId) {
        // 先从缓存中获取
        String cacheKey = ROLE_CACHE_PREFIX + userId;
        @SuppressWarnings("unchecked")
        List<String> roleList = (List<String>) redisTemplate.opsForValue().get(cacheKey);

        // 缓存不存在则从数据库中查询
        if (roleList == null) {
            roleList = roleMapper.selectRoleKeysByUserId(userId);
            
            // 将角色列表放入缓存
            if (!CollectionUtils.isEmpty(roleList)) {
                redisTemplate.opsForValue().set(cacheKey, roleList, CACHE_EXPIRATION, TimeUnit.MINUTES);
            }
        }
        
        return roleList;
    }

    @Override
    public boolean hasPermission(String permission) {
        // 获取当前登录用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return false;
        }
        
        Object principal = authentication.getPrincipal();
        if (!(principal instanceof UserDetailsImpl)) {
            return false;
        }
        
        UserDetailsImpl userDetails = (UserDetailsImpl) principal;
        Long userId = userDetails.getUserId();
        
        // 获取用户角色列表
        List<String> roleList = getRolesByUserId(userId);
        
        // 用户拥有超级管理员角色，拥有所有权限
        if (roleList.contains(ROLE_ADMIN)) {
            return true;
        }
        
        // 获取用户权限列表
        List<String> permissionList = getPermissionsByUserId(userId);
        
        // 判断是否包含权限标识
        return permissionList.contains(permission);
    }

    @Override
    public boolean hasRole(String role) {
        // 获取当前登录用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return false;
        }
        
        Object principal = authentication.getPrincipal();
        if (!(principal instanceof UserDetailsImpl)) {
            return false;
        }
        
        UserDetailsImpl userDetails = (UserDetailsImpl) principal;
        Long userId = userDetails.getUserId();
        
        // 获取用户角色列表
        List<String> roleList = getRolesByUserId(userId);
        
        // 判断是否包含角色标识
        return roleList.contains(role);
    }

    @Override
    public void reloadPermission(Long userId) {
        // 清除用户权限缓存
        String permissionKey = PERMISSION_CACHE_PREFIX + userId;
        String roleKey = ROLE_CACHE_PREFIX + userId;
        String rolePermissionKey = ROLE_PERMISSION_CACHE_PREFIX + userId;
        String roleNameKey = ROLE_NAME_CACHE_PREFIX + userId;
        
        redisTemplate.delete(permissionKey);
        redisTemplate.delete(roleKey);
        redisTemplate.delete(rolePermissionKey);
        redisTemplate.delete(roleNameKey);
    }

    @Override
    public void clearAllPermissionCache() {
        // 删除所有权限缓存
        Set<String> permissionKeys = redisTemplate.keys(PERMISSION_CACHE_PREFIX + "*");
        Set<String> roleKeys = redisTemplate.keys(ROLE_CACHE_PREFIX + "*");
        Set<String> rolePermissionKeys = redisTemplate.keys(ROLE_PERMISSION_CACHE_PREFIX + "*");
        Set<String> roleNameKeys = redisTemplate.keys(ROLE_NAME_CACHE_PREFIX + "*");
        
        if (!CollectionUtils.isEmpty(permissionKeys)) {
            redisTemplate.delete(permissionKeys);
        }
        
        if (!CollectionUtils.isEmpty(roleKeys)) {
            redisTemplate.delete(roleKeys);
        }
        
        if (!CollectionUtils.isEmpty(rolePermissionKeys)) {
            redisTemplate.delete(rolePermissionKeys);
        }
        
        if (!CollectionUtils.isEmpty(roleNameKeys)) {
            redisTemplate.delete(roleNameKeys);
        }
    }

    /**
     * 更新角色菜单权限
     *
     * @param roleId 角色ID
     * @param permissions 角色菜单权限
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRolePermissions(Long roleId, Set<Long> permissions) {
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(roleId);
        
        if (permissions != null && permissions.size() > 0) {
            // 构建角色菜单关联对象列表
            List<SysRoleMenu> roleMenuList = permissions.stream()
                .map(menuId -> {
                    SysRoleMenu rm = new SysRoleMenu();
                    rm.setRoleId(roleId);
                    rm.setMenuId(menuId);
                    return rm;
                })
                .collect(Collectors.toList());
            
            return roleMenuMapper.batchInsertRoleMenu(roleMenuList) > 0;
        }
        return true;
    }

    @Override
    public List<Long> getUserIdsByPermission(String permission) {
        // 获取具有该权限的角色ID
        List<Long> roleIds = roleMapper.selectRoleIdsByPermission(permission);
        if (CollectionUtils.isEmpty(roleIds)) {
            return new ArrayList<>();
        }
        
        // 根据角色ID获取用户ID
        return userRoleMapper.selectUserIdsByRoleIds(roleIds);
    }

    @Override
    public Map<String, List<String>> getRolePermissionsByUserId(Long userId) {
        // 先从缓存中获取
        String cacheKey = ROLE_PERMISSION_CACHE_PREFIX + userId;
        @SuppressWarnings("unchecked")
        Map<String, List<String>> rolePermissionsMap = (Map<String, List<String>>) redisTemplate.opsForValue().get(cacheKey);

        // 缓存不存在则从数据库中查询
        if (rolePermissionsMap == null) {
            rolePermissionsMap = new HashMap<>();
            
            // 获取用户角色列表
            List<String> roleList = getRolesByUserId(userId);
            
            // 遍历每个角色，获取对应的权限
            for (String roleKey : roleList) {
                List<String> permissionList;
                
                // 如果是超级管理员角色，获取所有权限
                if (ROLE_ADMIN.equals(roleKey)) {
                    permissionList = menuMapper.selectAllPermissions();
                } else {
                    // 查询角色ID
                    Long roleId = roleMapper.selectRoleIdByRoleKey(roleKey);
                    if (roleId != null) {
                        // 查询角色对应的权限
                        permissionList = menuMapper.selectPermissionsByRoleId(roleId);
                    } else {
                        permissionList = new ArrayList<>();
                    }
                }
                
                // 将角色和权限关系放入映射表
                rolePermissionsMap.put(roleKey, permissionList);
            }
            
            // 将角色权限映射表放入缓存
            if (!rolePermissionsMap.isEmpty()) {
                redisTemplate.opsForValue().set(cacheKey, rolePermissionsMap, CACHE_EXPIRATION, TimeUnit.MINUTES);
            }
        }
        
        return rolePermissionsMap;
    }

    @Override
    public Map<String, String> getRoleNamesByUserId(Long userId) {
        // 先从缓存中获取
        String cacheKey = ROLE_NAME_CACHE_PREFIX + userId;
        @SuppressWarnings("unchecked")
        Map<String, String> roleNamesMap = (Map<String, String>) redisTemplate.opsForValue().get(cacheKey);

        // 缓存不存在则从数据库中查询
        if (roleNamesMap == null) {
            roleNamesMap = new HashMap<>();
            
            // 查询用户角色标识和角色名称
            List<Map<String, String>> roleKeyAndNames = roleMapper.selectRoleKeysAndNamesByUserId(userId);
            
            // 将结果转换为Map
            if (!CollectionUtils.isEmpty(roleKeyAndNames)) {
                for (Map<String, String> map : roleKeyAndNames) {
                    String roleKey = map.get("roleKey");
                    String roleName = map.get("roleName");
                    if (roleKey != null && roleName != null) {
                        roleNamesMap.put(roleKey, roleName);
                    }
                }
            }
            
            // 将角色名称映射表放入缓存
            if (!roleNamesMap.isEmpty()) {
                redisTemplate.opsForValue().set(cacheKey, roleNamesMap, CACHE_EXPIRATION, TimeUnit.MINUTES);
            }
        }
        
        return roleNamesMap;
    }
} 