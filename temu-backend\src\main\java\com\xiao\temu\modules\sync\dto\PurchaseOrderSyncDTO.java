package com.xiao.temu.modules.sync.dto;

import lombok.Data;

import java.util.List;

/**
 * 备货单同步请求参数DTO
 */
@Data
public class PurchaseOrderSyncDTO {
    
    /**
     * 店铺ID列表
     */
    private List<Long> shopIds;
    
    /**
     * 备货单类型（0:普通备货 1:JIT备货 null:全部）
     */
    private Integer purchaseStockType;
    
    /**
     * 页码
     */
    private Integer pageNum;
    
    /**
     * 每页数量
     */
    private Integer pageSize;
    
    /**
     * 最大页数
     */
    private Integer maxPages;
    
    /**
     * 排序字段
     */
    private String sortField;
    
    /**
     * 排序方式（asc/desc）
     */
    private String sortOrder;
} 