package com.xiao.temu.infrastructure.excel;

import com.xiao.temu.infrastructure.task.ExportTaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Excel导出服务，处理带图片的Excel导出通用流程
 */
@Slf4j
@Service
public class ExcelExportService {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private ExportTaskManager exportTaskManager;

    /**
     * 通用Excel导出方法，封装整个导出流程
     * 
     * @param taskId       导出任务ID
     * @param fileName     导出文件名
     * @param sheetName    工作表名称
     * @param headers      表头数组
     * @param columnWidths 列宽数组
     * @param dataProcessor 数据处理器，负责填充Excel数据并生成图片下载任务
     */
    public void exportExcel(
            String taskId, 
            String fileName, 
            String sheetName, 
            String[] headers, 
            int[] columnWidths, 
            ExcelDataProcessor dataProcessor) {
        
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("导出流程");
        
        File tempDir = null;
        File exportFile = null;
        
        // 设置堆内存使用监控
        Runtime runtime = Runtime.getRuntime();
        double maxMemory = runtime.maxMemory() / (1024.0 * 1024.0); // 最大可用内存(MB)

        // 添加整体导出过程的超时控制
        final long EXPORT_TIMEOUT_MS = 30 * 60 * 1000; // 导出总时间不超过30分钟
        final long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始导出Excel，任务ID: {}, 最大可用内存: {}MB", taskId, String.format("%.2f", maxMemory));
            
            // 更新任务状态
            exportTaskManager.updateTaskProgress(taskId, 5, "正在准备导出数据...");
            
            // 1. 获取要导出的数据
            stopWatch.stop();
            stopWatch.start("数据准备");
            
            // 调用处理器加载数据
            ExcelExportContext context = new ExcelExportContext();
            dataProcessor.prepareData(context);
            
            if (context.isDataEmpty()) {
                exportTaskManager.failTask(taskId, "没有符合条件的数据可导出");
                return;
            }
            
            // 记录内存使用情况
            long usedMemoryBefore = runtime.totalMemory() - runtime.freeMemory();
            double usedMemoryMB = usedMemoryBefore / (1024.0 * 1024.0);
            log.info("数据准备完成，记录数: {}, 当前内存使用: {}MB (可用堆内存的 {}%)", 
                    context.getDataSize(), 
                    String.format("%.2f", usedMemoryMB), 
                    String.format("%.2f", (usedMemoryMB / maxMemory) * 100));
            
            // 检查内存使用超过90%，发出警告
            if (usedMemoryMB / maxMemory > 0.9) {
                log.warn("内存使用超过可用堆内存的90%，可能导致后续处理中内存不足");
            }
            
            // 检查是否已超时
            if (System.currentTimeMillis() - startTime > EXPORT_TIMEOUT_MS) {
                log.error("导出Excel处理已超时 ({}分钟)，强制中止处理", EXPORT_TIMEOUT_MS / 60000);
                exportTaskManager.failTask(taskId, "导出处理超时，请尝试减少导出数据量或分批导出");
                return;
            }
            
            stopWatch.stop();
            stopWatch.start("图片下载");
            
            // 2. 准备图片下载
            exportTaskManager.updateTaskProgress(taskId, 30, "正在下载图片，共" + context.getDataSize() + "条记录...");
            
            // 创建临时文件夹存储下载的图片
            tempDir = ExcelExportUtils.createTempDirectory();
            
            // 3. 准备图片下载任务
            List<ExcelExportUtils.ImageDownloadTask> downloadTasks = new ArrayList<>();
            dataProcessor.prepareImageTasks(context, tempDir, downloadTasks);
            
            // 记录图片任务准备后的内存使用
            long usedMemoryAfterTasks = runtime.totalMemory() - runtime.freeMemory();
            double usedMemoryMBAfterTasks = usedMemoryAfterTasks / (1024.0 * 1024.0);
            log.info("图片任务准备完成，图片数: {}, 当前内存使用: {}MB (增加了 {}MB)", 
                    downloadTasks.size(), 
                    String.format("%.2f", usedMemoryMBAfterTasks), 
                    String.format("%.2f", usedMemoryMBAfterTasks - usedMemoryMB));
            
            // 手动触发GC，释放图片任务准备阶段的临时对象
            System.gc();
            
            // 检查是否已超时
            if (System.currentTimeMillis() - startTime > EXPORT_TIMEOUT_MS) {
                log.error("导出Excel处理已超时 ({}分钟)，强制中止处理", EXPORT_TIMEOUT_MS / 60000);
                exportTaskManager.failTask(taskId, "导出处理超时，请尝试减少导出数据量或分批导出");
                return;
            }
            
            // 4. 下载图片
            ExcelExportUtils.downloadImages(downloadTasks, applicationContext, taskId, exportTaskManager);
            
            // 如果图片数量过多，限制最大图片数
            final int MAX_IMAGES = 5000;
            if (downloadTasks.size() > MAX_IMAGES) {
                log.warn("图片数量过多 ({}张)，将只保留前{}张图片以提高处理效率", downloadTasks.size(), MAX_IMAGES);
                exportTaskManager.updateTaskProgress(taskId, 70, 
                    String.format("图片数量过多，将只处理前%d张图片以确保导出稳定性", MAX_IMAGES));
                
                if (downloadTasks.size() > MAX_IMAGES) {
                    // 创建新的列表存储前MAX_IMAGES个任务
                    List<ExcelExportUtils.ImageDownloadTask> limitedTasks = new ArrayList<>(downloadTasks.subList(0, MAX_IMAGES));
                    // 删除多余的图片文件
                    for (int i = MAX_IMAGES; i < downloadTasks.size(); i++) {
                        ExcelExportUtils.ImageDownloadTask task = downloadTasks.get(i);
                        if (task.getTargetFile().exists()) {
                            task.getTargetFile().delete();
                        }
                    }
                    // 替换原任务列表
                    downloadTasks = limitedTasks;
                }
            }
            
            // 检查是否已超时
            if (System.currentTimeMillis() - startTime > EXPORT_TIMEOUT_MS) {
                log.error("导出Excel处理已超时 ({}分钟)，强制中止处理", EXPORT_TIMEOUT_MS / 60000);
                exportTaskManager.failTask(taskId, "导出处理超时，请尝试减少导出数据量或分批导出");
                return;
            }
            
            stopWatch.stop();
            stopWatch.start("生成Excel");
            
            // 5. 创建Excel文件
            exportTaskManager.updateTaskProgress(taskId, 70, "正在生成Excel文件...");
            
            // 创建导出目录
            File outputDir = ExcelExportUtils.createExportDirectory();
            
            // 创建导出文件
            exportFile = new File(outputDir, fileName + "_" + System.currentTimeMillis() + ".xlsx");
            
            // 记录Excel生成前的内存
            long memoryBeforeExcel = runtime.totalMemory() - runtime.freeMemory();
            double memoryMBBeforeExcel = memoryBeforeExcel / (1024.0 * 1024.0);
            
            // 6. 生成Excel内容 - 使用SXSSFWorkbook处理大文件
            exportTaskManager.updateTaskProgress(taskId, 75, "正在创建Excel内容...");
            
            // 使用SXSSF处理大数据量
            try (XSSFWorkbook workbook = new XSSFWorkbook()) {
                Sheet sheet = workbook.createSheet(sheetName);
                
                // 创建样式
                CellStyle[] styles = ExcelExportUtils.createStyles(workbook);
                CellStyle headerStyle = styles[0];
                CellStyle contentStyle = styles[1];
                
                // 创建表头
                exportTaskManager.updateTaskProgress(taskId, 75, "正在创建Excel表头...");
                ExcelExportUtils.createHeader(sheet, headers, headerStyle, columnWidths);
                
                // 允许数据处理器创建自定义表头（如合并表头）
                // 调用数据处理器填充数据前，先检查是否有子表头（sheet中是否有大于0的行数）
                boolean hasSubHeaders = sheet.getLastRowNum() > 0;
                
                // 填充数据
                exportTaskManager.updateTaskProgress(taskId, 80, "正在填充数据...");
                dataProcessor.fillExcelData(context, sheet, contentStyle);
                
                // 创建绘图对象
                XSSFDrawing drawing = (XSSFDrawing) sheet.createDrawingPatriarch();
                
                // 记录填充数据后的内存
                long memoryAfterData = runtime.totalMemory() - runtime.freeMemory();
                double memoryMBAfterData = memoryAfterData / (1024.0 * 1024.0);
                log.info("Excel数据填充完成，当前内存使用: {}MB (增加了 {}MB)", 
                        String.format("%.2f", memoryMBAfterData), 
                        String.format("%.2f", memoryMBAfterData - memoryMBBeforeExcel));
                
                // 手动触发GC清理临时对象
                System.gc();
                
                // 插入图片
                exportTaskManager.updateTaskProgress(taskId, 90, "正在插入图片...");
                ExcelExportUtils.insertImages(downloadTasks, workbook, sheet, drawing, taskId, exportTaskManager);
                
                // 记录插入图片后的内存使用
                long memoryAfterImages = runtime.totalMemory() - runtime.freeMemory();
                double memoryMBAfterImages = memoryAfterImages / (1024.0 * 1024.0);
                log.info("图片插入完成，当前内存使用: {}MB (增加了 {}MB)", 
                        String.format("%.2f", memoryMBAfterImages), 
                        String.format("%.2f", memoryMBAfterImages - memoryMBAfterData));
                
                // 保存Excel文件
                ExcelExportUtils.saveExcelFile(workbook, exportFile, taskId, exportTaskManager);
                
                // 最终内存使用情况
                long finalMemory = runtime.totalMemory() - runtime.freeMemory();
                double finalMemoryMB = finalMemory / (1024.0 * 1024.0);
                log.info("Excel文件保存完成，最终内存使用: {}MB, 文件大小: {}MB", 
                        String.format("%.2f", finalMemoryMB), 
                        String.format("%.2f", exportFile.length() / (1024.0 * 1024.0)));
            }
            
            stopWatch.stop();
            log.info("导出Excel完成，耗时: {}ms，导出数据量: {} 条", stopWatch.getTotalTimeMillis(), context.getDataSize());
            
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            exportTaskManager.failTask(taskId, "导出失败: " + e.getMessage());
            
            // 删除已创建的文件
            if (exportFile != null && exportFile.exists()) {
                exportFile.delete();
            }
        } finally {
            // 清理临时文件
            ExcelExportUtils.cleanupTempDirectory(tempDir);
            
            // 最后再次触发GC，释放所有内存
            System.gc();
        }
    }
    
    /**
     * 异步执行Excel导出
     * 
     * @param exportParams 导出参数
     * @param exporter 导出实现
     */
    public void asyncExportExcel(final Map<String, Object> exportParams, ExcelExporter exporter) {
        // 使用CompletableFuture异步执行
        CompletableFuture.runAsync(() -> {
            String taskId = (String) exportParams.get("taskId");
            try {
                // 执行实际的导出操作
                exporter.export(exportParams, taskId);
            } catch (Exception e) {
                log.error("导出Excel处理失败", e);
                // 更新任务状态为失败
                exportTaskManager.failTask(taskId, "导出失败: " + e.getMessage());
            }
        });
    }
    
    /**
     * Excel数据处理器接口，负责处理数据和填充Excel
     */
    public interface ExcelDataProcessor {
        /**
         * 准备导出数据
         * @param context 导出上下文
         */
        void prepareData(ExcelExportContext context);
        
        /**
         * 准备图片下载任务
         * @param context 导出上下文
         * @param tempDir 临时目录
         * @param downloadTasks 下载任务列表
         */
        void prepareImageTasks(ExcelExportContext context, File tempDir, List<ExcelExportUtils.ImageDownloadTask> downloadTasks);
        
        /**
         * 填充Excel数据
         * @param context 导出上下文
         * @param sheet 工作表
         * @param contentStyle 内容样式
         */
        void fillExcelData(ExcelExportContext context, Sheet sheet, CellStyle contentStyle);
    }
    
    /**
     * Excel导出器接口，由具体业务模块实现
     */
    public interface ExcelExporter {
        /**
         * 执行导出操作
         * @param exportParams 导出参数
         * @param taskId 任务ID
         */
        void export(Map<String, Object> exportParams, String taskId);

    }
    
    /**
     * Excel导出上下文，用于在不同处理阶段传递数据
     */
    public static class ExcelExportContext {
        private Object data;
        private int dataSize;
        
        public void setData(Object data) {
            this.data = data;
        }
        
        public Object getData() {
            return data;
        }
        
        public void setDataSize(int dataSize) {
            this.dataSize = dataSize;
        }
        
        public int getDataSize() {
            return dataSize;
        }
        
        public boolean isDataEmpty() {
            return dataSize <= 0;
        }
    }
} 