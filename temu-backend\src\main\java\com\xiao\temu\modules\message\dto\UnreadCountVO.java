package com.xiao.temu.modules.message.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 未读消息计数VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UnreadCountVO {

    /**
     * 未读消息总数
     */
    private Integer totalCount = 0;

    /**
     * 系统消息未读数
     */
    private Integer systemCount = 0;

    /**
     * 任务提醒未读数
     */
    private Integer taskCount = 0;

    /**
     * 店铺消息未读数
     */
    private Integer shopCount = 0;

    /**
     * 紧急消息未读数
     */
    private Integer urgentCount = 0;
} 