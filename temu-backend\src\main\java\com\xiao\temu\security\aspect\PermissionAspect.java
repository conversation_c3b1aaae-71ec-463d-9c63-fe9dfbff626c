package com.xiao.temu.security.aspect;

import com.xiao.temu.security.annotation.RequiresPermission;
import com.xiao.temu.security.annotation.RequiresRoles;
import com.xiao.temu.security.exception.NotPermittedException;
import com.xiao.temu.security.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 权限注解切面
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class PermissionAspect {

    private final PermissionService permissionService;

    /**
     * 权限注解切点
     */
    @Pointcut("@annotation(com.xiao.temu.security.annotation.RequiresPermission)")
    public void permissionPointCut() {
    }

    /**
     * 角色注解切点
     */
    @Pointcut("@annotation(com.xiao.temu.security.annotation.RequiresRoles)")
    public void rolePointCut() {
    }

    /**
     * 前置处理 - 权限校验
     */
    @Before("permissionPointCut()")
    public void checkPermission(JoinPoint point) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();

        RequiresPermission requiresPermission = method.getAnnotation(RequiresPermission.class);
        if (requiresPermission == null) {
            return;
        }

        // 获取注解中的权限值
        String permission = requiresPermission.value();
        String message = requiresPermission.message();

        // 验证权限
        if (!permissionService.hasPermission(permission)) {
            log.warn("用户没有权限[{}]，访问[{}]接口", permission, method.getName());
            throw new NotPermittedException(message);
        }
    }

    /**
     * 前置处理 - 角色校验
     */
    @Before("rolePointCut()")
    public void checkRole(JoinPoint point) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();

        RequiresRoles requiresRoles = method.getAnnotation(RequiresRoles.class);
        if (requiresRoles == null) {
            return;
        }

        // 获取注解中的角色值
        String[] roles = requiresRoles.value();
        RequiresPermission.Logical logical = requiresRoles.logical();
        String message = requiresRoles.message();

        // 根据逻辑运算符验证角色
        if (RequiresPermission.Logical.AND.equals(logical)) {
            // AND: 需要满足所有角色
            for (String role : roles) {
                if (!permissionService.hasRole(role)) {
                    log.warn("用户没有角色[{}]，访问[{}]接口", role, method.getName());
                    throw new NotPermittedException(message);
                }
            }
        } else {
            // OR: 满足任一角色即可
            boolean hasAnyRole = false;
            for (String role : roles) {
                if (permissionService.hasRole(role)) {
                    hasAnyRole = true;
                    break;
                }
            }

            if (!hasAnyRole) {
                log.warn("用户没有任何所需角色，访问[{}]接口", method.getName());
                throw new NotPermittedException(message);
            }
        }
    }
} 