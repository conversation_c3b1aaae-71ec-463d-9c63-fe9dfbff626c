package com.xiao.temu.modules.violation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.violation.entity.ShopViolationInfo;
import com.xiao.temu.modules.violation.dto.ShopViolationInfoDTO;
import com.xiao.temu.modules.violation.dto.ShopViolationInfoQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 店铺违规信息Mapper接口
 */
@Mapper
public interface ShopViolationInfoMapper extends BaseMapper<ShopViolationInfo> {
    
    /**
     * 分页查询店铺违规信息列表
     * 
     * @param page 分页参数
     * @param query 查询条件
     * @return 店铺违规信息列表（包含店铺名称）
     */
    IPage<ShopViolationInfoDTO> selectViolationInfoList(Page<ShopViolationInfoDTO> page, @Param("query") ShopViolationInfoQueryDTO query);
    
    /**
     * 分页查询店铺违规信息列表(包含详情信息)
     * 
     * @param page 分页参数
     * @param query 查询条件
     * @return 店铺违规信息列表（包含店铺名称和违规详情）
     */
    IPage<ShopViolationInfoDTO> selectViolationInfoListWithDetail(Page<ShopViolationInfoDTO> page, @Param("query") ShopViolationInfoQueryDTO query);
    
    /**
     * 根据ID查询店铺违规信息详情
     * 
     * @param id 违规信息ID
     * @return 店铺违规信息详情（包含店铺名称）
     */
    ShopViolationInfoDTO selectViolationInfoById(@Param("id") Long id);
    
    /**
     * 根据店铺ID和违规编号查询违规信息
     * 
     * @param shopId 店铺ID
     * @param punishSn 违规编号
     * @return 店铺违规信息
     */
    ShopViolationInfo selectByShopIdAndPunishSn(@Param("shopId") Long shopId, @Param("punishSn") String punishSn);
    
    /**
     * 根据店铺MallId和违规编号查询违规信息
     * 
     * @param mallId 店铺MallId
     * @param punishSn 违规编号
     * @return 店铺违规信息
     */
    ShopViolationInfo selectByMallIdAndPunishSn(@Param("mallId") String mallId, @Param("punishSn") String punishSn);
} 