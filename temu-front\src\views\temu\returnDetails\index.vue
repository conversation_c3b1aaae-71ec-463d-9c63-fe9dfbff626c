<template>
  <AppLayout>
    <!-- 搜索区域 -->
    <template #search>
      <SearchCard>
        <!-- 搜索表单 - 改为行布局，每行4个条件 -->
        <div class="search-container">
          <!-- 第一行 -->
          <div class="search-row">
            <!-- 店铺 -->
            <div class="search-item">
              <div class="search-label">店铺</div>
              <el-select 
                v-model="queryParams.shopId" 
                placeholder="请选择店铺" 
                clearable 
                @change="handleShopChange" 

                class="search-input"
              >
                <el-option 
                  v-for="shop in shops" 
                  :key="shop.shopId" 
                  :label="shop.shopName" 
                  :value="shop.shopId"
                />
              </el-select>
            </div>
            
            <!-- SKU -->
            <div class="search-item">
              <div class="search-label">SKU</div>
              <TagInput
                v-model="queryParams.productSkuIdList"
                placeholder="输入SKU (空格,逗号或回车分隔)"
                :is-numeric="true"
                size="small"
                class="search-input"
              />
            </div>
            
            <!-- 出库时间 -->
            <div class="search-item">
              <div class="search-label">出库时间</div>
              <el-date-picker
                v-model="timeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                @change="handleTimeRangeChange"
                size="small"
                class="search-input date-picker-input"
              />
            </div>
            
            <!-- 退货包裹号 -->
            <div class="search-item">
              <div class="search-label">退货包裹号</div>
              <TagInput
                v-model="queryParams.returnSupplierPackageNos"
                placeholder="输入退货包裹号 (空格,逗号或回车分隔)"
                :is-numeric="false"
                size="small"
                class="search-input"
              />
            </div>
          </div>
          
          <!-- 第二行 -->
          <div class="search-row">
            <!-- 备货单号 -->
            <div class="search-item">
              <div class="search-label">备货单号</div>
              <TagInput
                v-model="queryParams.purchaseSubOrderSns"
                placeholder="输入备货单号 (空格,逗号或回车分隔)"
                :is-numeric="false"
                size="small"
                class="search-input"
              />
            </div>
            
            <!-- 空白项 -->
            <div class="search-item"></div>
            
            <!-- 空白项 -->
            <div class="search-item"></div>
            
            <!-- 按钮 -->
            <div class="search-item buttons-container">
              <div class="search-buttons">
                <el-button type="primary" @click="handleQuery" size="small" class="action-button">
                  <el-icon><Search /></el-icon> 搜索
                </el-button>
                <el-button @click="resetQuery" size="small" class="action-button">
                  <el-icon><Refresh /></el-icon> 重置
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </SearchCard>
    </template>

    <!-- 表格内容区域 -->
    <template #table>
      <TableCard>
        <!-- 表格容器 -->
        <el-table
          v-loading="loading"
          :data="refundPackages"
          border
          style="width: 100%"
          size="small"
          class="compact-table"
        >
          <template #empty>
            <EmptyTips message="请选择店铺后点击搜索按钮查询数据（已默认设置最近7天）" />
          </template>
          <el-table-column type="index" label="序号" width="50" align="center" />
          <el-table-column prop="productSpuId" label="SPU" width="100" />
          <el-table-column prop="productSkuId" label="SKU" width="100" />
          <el-table-column label="商品信息" min-width="200">
            <template #default="scope">
              <div class="product-info">
                <img 
                  :src="scope.row.thumbUrl" 
                  class="product-image" 
                  alt="商品图片"
                  @click="handlePreviewImage(scope.row.thumbUrl)"
                >
                <div class="product-details">
                  <div class="product-skc">SKC: {{ scope.row.productSkcId }}</div>
                  <div class="product-attr">属性集: {{ scope.row.mainSaleSpec }}-{{ scope.row.secondarySaleSpec }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="purchaseSubOrderSn" label="备货单号" width="140" />
          <el-table-column label="退货原因" width="120">
            <template #default="scope">
              {{ Array.isArray(scope.row.reasonDesc) ? scope.row.reasonDesc.join(', ') : scope.row.reasonDesc }}
            </template>
          </el-table-column>
          <el-table-column prop="packageSn" label="退货包裹号" width="170" />
          <el-table-column prop="quantity" label="SKU件数" width="70" align="center" />
          <el-table-column prop="outboundTime" label="出库时间" width="160">
            <template #default="scope">
              {{ formatTime(scope.row.outboundTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="handleViewDetail(scope.row)">
                查看抽检记录
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </TableCard>
    </template>
    
    <!-- 固定在底部的分页区域 -->
    <template #pagination>
      <PaginationBar
        :current-page="Number(queryParams.pageNo)" 
        :page-size="Number(queryParams.pageSize)"
        :total="total"
        @update:current-page="(val) => queryParams.pageNo = Number(val)"
        @update:page-size="(val) => queryParams.pageSize = Number(val)"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        size="small"
      />
    </template>
  
    <!-- 弹窗区域 -->
    <template #dialogs>
      <ImagePreview
        v-model:visible="imagePreviewVisible"
        :image-url="currentPreviewImage"
      />
    </template>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, onUnmounted } from 'vue'
import type { Ref } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useRefundStore } from '@/store'
import type { RefundRequestDTO, Shop, RefundPackage } from '@/types/refund'
import { formatTime } from '@/utils/format'
import { fetchUserAccessibleShops } from '@/utils/shop-helper'
import { 
  AppLayout, 
  SearchCard, 
  TableCard, 
  PaginationBar, 
  ImagePreview, 
  EmptyTips,
  TagInput
} from '@/components/temu'

// 退货明细store
const refundStore = useRefundStore()

// 加载状态
const loading = computed(() => refundStore.loading)

// 店铺列表
const shops = ref<Shop[]>([])

// 图片预览相关
const imagePreviewVisible = ref(false)
const currentPreviewImage = ref('')

// 退货包裹列表
const refundPackages = ref<RefundPackage[]>([])

// 总记录数
const total = ref(0)

// 监听窗口大小变化
const handleResize = () => {
  // 不再需要动态计算表格高度
}

// 获取默认时间范围（最近30天）
const getDefaultTimeRange = (): [string, string] => {
  const end = new Date()
  const start = new Date()
  start.setDate(start.getDate() - 7)
  
  // 转换为YYYY-MM-DD格式（UI显示格式，发送请求前会在store中转换为时间戳）
  return [
    formatDateToYYYYMMDD(start),
    formatDateToYYYYMMDD(end)
  ]
}

// 日期格式化为YYYY-MM-DD
const formatDateToYYYYMMDD = (date: Date): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 时间范围，默认为最近30天
const timeRange = ref<[string, string] | null>(getDefaultTimeRange())

// 查询参数
const queryParams = reactive<RefundRequestDTO>({
  shopId: undefined,
  outboundTimeStart: timeRange.value ? timeRange.value[0] : undefined,
  outboundTimeEnd: timeRange.value ? timeRange.value[1] : undefined,
  pageNo: 1,
  pageSize: 10,
  productSkuIdList: [],
  returnSupplierPackageNos: [],
  purchaseSubOrderSns: []
})

// 初始化
onMounted(async () => {
  // 只加载店铺列表，不自动加载退货包裹列表
  await loadShops()
  
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
})

// 加载店铺列表
const loadShops = async () => {
  shops.value = await fetchUserAccessibleShops()
}

// 加载退货包裹列表
const loadRefundPackages = async () => {
  const result = await refundStore.getRefundPackages(queryParams)
  
  if (result && result.success) {
    // 解析后端返回的数据结构
    if (result.result) {
      if (result.result.packageDetailDTOList) {
        // 使用后端返回的packageDetailDTOList作为列表数据
        refundPackages.value = result.result.packageDetailDTOList
        total.value = result.result.total || 0
      } else if (Array.isArray(result.result.list)) {
        // 兼容老结构
        refundPackages.value = result.result.list
        total.value = result.result.total || 0
      } else if (Array.isArray(result.result)) {
        // 兼容直接返回数组的情况
        refundPackages.value = result.result
        total.value = result.result.length || 0
      } else {
        refundPackages.value = []
        total.value = 0
      }
    } else {
      refundPackages.value = []
      total.value = 0
    }
  } else {
    refundPackages.value = []
    total.value = 0
    
    // 显示错误消息
    if (result && result.errorMsg) {
      ElMessage.error(result.errorMsg)
    } else {
      ElMessage.error('获取退货明细失败')
    }
  }
}

// 处理查询
const handleQuery = () => {
  // 验证必须选择店铺
  if (!queryParams.shopId) {
    ElMessage.warning('请选择店铺')
    return
  }

  // 确保时间范围存在（使用默认值）
  if (!timeRange.value || !queryParams.outboundTimeStart || !queryParams.outboundTimeEnd) {
    const defaultRange = getDefaultTimeRange()
    timeRange.value = defaultRange
    queryParams.outboundTimeStart = defaultRange[0]
    queryParams.outboundTimeEnd = defaultRange[1]
  }
  
  // 检查日期范围是否超过30天
  if (queryParams.outboundTimeStart && queryParams.outboundTimeEnd) {
    const startDate = new Date(queryParams.outboundTimeStart)
    const endDate = new Date(queryParams.outboundTimeEnd)
    const diffDays = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
    
    if (diffDays > 30) {
      // 如果超过30天，显示友好提示
      ElMessage.warning('查询时间范围不能超过30天，已自动调整为最近30天')
      
      // 重置为最近30天
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 30)
      
      const newRange: [string, string] = [
        formatDateToYYYYMMDD(start),
        formatDateToYYYYMMDD(end)
      ]
      
      // 更新时间范围
      timeRange.value = newRange
      queryParams.outboundTimeStart = newRange[0]
      queryParams.outboundTimeEnd = newRange[1]
      refundStore.setTimeRange(newRange)
    }
  }

  queryParams.pageNo = 1
  refundStore.setShopId(queryParams.shopId)
  loadRefundPackages()
}

// 重置查询
const resetQuery = () => {
  queryParams.shopId = undefined
  
  // 恢复默认时间范围（最近30天）
  timeRange.value = getDefaultTimeRange()
  queryParams.outboundTimeStart = timeRange.value[0]
  queryParams.outboundTimeEnd = timeRange.value[1]
  
  // 重置多值输入
  queryParams.productSkuIdList = []
  queryParams.returnSupplierPackageNos = []
  queryParams.purchaseSubOrderSns = []
  
  queryParams.pageNo = 1
  
  // 重置store中的查询条件
  refundStore.resetQuery()
  
  // 清空当前数据，不重新加载
  refundPackages.value = []
  total.value = 0
}

// 处理时间范围变化
const handleTimeRangeChange = (val: [string, string] | null) => {
  if (val) {
    // 检查日期范围是否超过30天
    const startDate = new Date(val[0])
    const endDate = new Date(val[1])
    const diffDays = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
    
    if (diffDays > 30) {
      // 如果超过30天，显示友好提示
      ElMessage.warning('查询时间范围不能超过30天，已自动调整为最近30天')
      
      // 重置为最近30天
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 30)
      
      const newRange: [string, string] = [
        formatDateToYYYYMMDD(start),
        formatDateToYYYYMMDD(end)
      ]
      
      // 更新时间范围
      timeRange.value = newRange
      queryParams.outboundTimeStart = newRange[0]
      queryParams.outboundTimeEnd = newRange[1]
      refundStore.setTimeRange(newRange)
      
      return
    }
    
    // 时间范围合法，正常设置
    queryParams.outboundTimeStart = val[0]
    queryParams.outboundTimeEnd = val[1]
    refundStore.setTimeRange(val)
  } else {
    // 如果手动清除，恢复为默认的30天
    const defaultRange = getDefaultTimeRange()
    timeRange.value = defaultRange
    queryParams.outboundTimeStart = defaultRange[0]
    queryParams.outboundTimeEnd = defaultRange[1]
    refundStore.setTimeRange(defaultRange)
  }
}

// 处理每页显示数量变化
const handleSizeChange = (size: number) => {
  queryParams.pageSize = Number(size)
  loadRefundPackages()
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  queryParams.pageNo = Number(page)
  loadRefundPackages()
}

// 处理店铺选择
const handleShopChange = (shopId: number | undefined) => {
  refundStore.setShopId(shopId || null)
  console.log('选择店铺:', shopId)
}

// 格式化金额
const formatCurrency = (val: number | string | undefined) => {
  if (val === undefined || val === null) return '¥0.00'
  const num = typeof val === 'string' ? parseFloat(val) : val
  return `¥${num.toFixed(2)}`
}

// 获取状态类型
const getStatusType = (status: string | number | undefined) => {
  if (status === undefined) return 'info'
  
  // 根据不同状态返回不同的标签类型
  switch (String(status)) {
    case '1': return 'success'
    case '2': return 'warning'
    case '3': return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string | number | undefined) => {
  if (status === undefined) return '未知'
  
  // 根据不同状态返回不同的文本
  switch (String(status)) {
    case '1': return '已退款'
    case '2': return '处理中'
    case '3': return '已拒绝'
    default: return '未知'
  }
}

// 处理查看详情
const handleViewDetail = (row: RefundPackage) => {
  // 实现查看详情逻辑
  console.log('查看详情:', row)
}

// 处理图片预览
const handlePreviewImage = (imageUrl: string) => {
  currentPreviewImage.value = imageUrl
  imagePreviewVisible.value = true
}

// 组件销毁时移除监听器
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 改为行布局样式 */
.search-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 0 5px;
}

.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.search-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 250px;
  max-width: calc(25% - 12px);
  margin-bottom: 5px;
}

.search-label {
  width: 90px;
  text-align: left;
  margin-bottom: 8px;
  color: #606266;
  font-size: 14px;
}

.search-input {
  width: 100%;
}

/* 按钮容器样式 */
.buttons-container {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

.search-buttons {
  display: flex;
  gap: 10px;
}

.action-button {
  min-width: 80px; 
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 确保日期选择器宽度保持一致 */
.date-picker-input {
  width: 100% !important;
}

.date-picker-input :deep(.el-input__wrapper) {
  width: 100%;
}

/* 优化TagInput样式，确保它在SearchCard中正确显示 */
:deep(.tag-input-wrapper) {
  width: 100%;
  height: auto; /* 允许自动高度 */
}

:deep(.tag-input-wrapper .input-container) {
  min-height: 32px; /* 最小高度与其他输入框一致 */
  height: auto; /* 允许高度自动增长 */
  box-sizing: border-box;
  overflow: visible; /* 允许内容溢出 */
}

:deep(.tag-input-wrapper .tags-container) {
  margin-bottom: 5px;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

:deep(.tag-input-wrapper .tag-main-input) {
  width: 100%;
  min-height: 22px; /* 确保输入框有足够的高度 */
}

/* 确保SearchCard有足够空间容纳内容 */
:deep(.search-section .box-card) {
  height: auto; /* 允许卡片高度自适应 */
  overflow: visible; /* 允许内容溢出 */
}

:deep(.search-section .el-card__body) {
  padding: 15px; /* 增加内部边距 */
  overflow: visible; /* 确保内容可见 */
  height: auto;
}

/* 表格相关样式 */
.product-info {
  display: flex;
  align-items: center;
}

.product-image {
  width: 60px;
  height: 60px;
  margin-right: 8px;
  object-fit: contain;
  cursor: pointer;
  transition: opacity 0.3s;
}

.product-image:hover {
  opacity: 0.8;
}

.product-details {
  flex: 1;
  font-size: 12px;
}

.product-skc {
  margin-bottom: 3px;
}

.product-attr {
  font-size: 11px;
  color: #909399;
}

/* 添加紧凑表格样式 */
.compact-table {
  font-size: 12px;
}

.compact-table :deep(.el-table__cell) {
  padding: 6px 0;
}

.compact-table :deep(.cell) {
  line-height: 1.3;
}
</style> 