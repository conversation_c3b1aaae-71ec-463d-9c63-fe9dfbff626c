import request from '@/utils/request'

// 获取角色列表
export function getRoleList(query: any) {
  return request({
    url: '/system/role/list',
    method: 'get',
    params: query
  })
}

// 查询角色详细
export function getRole(roleId: number) {
  return request({
    url: `/system/role/${roleId}`,
    method: 'get'
  })
}

// 新增角色
export function addRole(data: any) {
  return request({
    url: '/system/role',
    method: 'post',
    data
  })
}

// 修改角色
export function updateRole(data: any) {
  return request({
    url: '/system/role',
    method: 'put',
    data
  })
}

// 删除角色
export function deleteRole(roleId: number) {
  return request({
    url: `/system/role/${roleId}`,
    method: 'delete'
  })
}

// 获取所有角色列表
export function getAllRoles() {
  return request({
    url: '/system/role/all',
    method: 'get'
  })
}

// 修改角色状态
export function changeRoleStatus(roleId: number, status: string) {
  return request({
    url: '/system/role/changeStatus',
    method: 'put',
    params: {
      roleId,
      status
    }
  })
}

// 分配权限
export function assignPermissions(roleId: number, menuIds: number[]) {
  return request({
    url: '/system/role/assignPermissions',
    method: 'put',
    params: {
      roleId
    },
    data: menuIds
  })
} 