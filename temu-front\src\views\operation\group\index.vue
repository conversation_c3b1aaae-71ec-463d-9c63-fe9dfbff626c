<template>
  <div class="app-container">
    <!-- 顶部操作栏 -->
    <div class="toolbar">
      <el-form :model="queryParams" ref="queryRef" :inline="true" class="search-form">
        <el-form-item label="运营组名称" prop="groupName">
          <el-input
            v-model="queryParams.groupName"
            placeholder="请输入运营组名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="负责人" prop="leaderName">
          <el-input
            v-model="queryParams.leaderName"
            placeholder="请输入负责人姓名"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status" style="width: 180px">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="action-btns">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['operation:group:add']"
        >新增</el-button>
        <el-button
          type="success"
          icon="Download"
          @click="handleExport"
          v-hasPermi="['operation:group:export']"
        >导出</el-button>
      </div>
    </div>

    <!-- 运营组表格 -->
    <el-table
      v-loading="loading"
      :data="groupList"
      style="width: 100%"
      border
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="45" />
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column label="运营组名称" prop="groupName" :show-overflow-tooltip="true" min-width="120"/>
      <el-table-column label="负责人" prop="leaderName" width="100" />
      <el-table-column label="成员数量" prop="memberCount" width="80" align="center" />
      <el-table-column label="状态" prop="status" width="80" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
            {{ scope.row.status === '0' ? '正常' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="160">
        <template #default="scope">
          {{ formatTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="350" align="center" fixed="right">
        <template #default="scope">
          <el-button 
            type="primary"   
            link 
            icon="View" 
            @click="handleView(scope.row)"
            v-hasPermi="['operation:group:query']"
          >查看</el-button>
          <el-button 
            type="primary" 
            link 
            icon="Edit" 
            @click="handleUpdate(scope.row)"
            v-hasPermi="['operation:group:edit']"
          >编辑</el-button>
          <!-- <el-button
            type="primary"
            link
            icon="User"
            @click="handleMembers(scope.row)"
            v-hasPermi="['operation:group:query']"
          >成员</el-button> -->
          <el-button
            type="primary"
            link
            icon="Plus"
            @click="handleAssignMembers(scope.row)"
            v-hasPermi="['operation:group:edit']"
          >分配成员</el-button>
          <el-button
            type="primary"
            link
            icon="Shop"
            @click="handleAssignShops(scope.row)"
            v-hasPermi="['operation:group:edit']"
          >分配店铺</el-button>
          <el-button
            type="danger"
            link
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['operation:group:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-if="total > 0"
      class="pagination"
      :total="total"
      v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 添加/修改运营组对话框 -->
    <el-dialog
      :title="dialog.title"
      v-model="dialog.visible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="groupFormRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="组名称" prop="groupName">
          <el-input v-model="form.groupName" placeholder="请输入运营组名称" />
        </el-form-item>
        <el-form-item label="负责人" prop="leaderId">
          <el-select
            v-model="form.leaderId"
            filterable
            placeholder="请选择负责人"
          >
            <el-option
              v-for="item in leaderOptions"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 分配成员对话框 -->
    <el-dialog
      title="分配成员"
      v-model="memberDialog.visible"
      width="600px"
      append-to-body
    >
      <el-form>
        <el-form-item label="运营组" prop="groupName">
          <span>{{ memberDialog.groupName }}</span>
        </el-form-item>
        <el-form-item label="已分配成员" v-if="existingMemberList.length > 0">
          <div class="existing-members">
            <el-tag
              v-for="member in existingMemberList"
              :key="member.userId"
              class="member-tag"
              type="success"
              closable
              @close="handleRemoveMember(member)"
            >
              {{ member.nickName }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="filter-options">
            <el-switch
              v-model="memberDialog.showAllOperationUsers"
              active-text="显示所有运营人员"
              inactive-text="仅显示未分配到运营组的人员"
              @change="handleMemberFilterChange"
            />
          </div>
        </el-form-item>
        <el-form-item label="选择成员" prop="selectedUsers">
          <el-select
            v-model="memberDialog.selectedUsers"
            filterable
            multiple
            placeholder="请选择运营人员"
            style="width: 100%"
          >
            <el-option
              v-for="item in operationUserOptions"
              :key="item.userId"
              :label="item.groupNames ? `${item.nickName} (${item.groupNames})` : item.nickName"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="memberDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitAssignMembers">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 分配店铺对话框 -->
    <el-dialog
      title="分配店铺"
      v-model="shopDialog.visible"
      width="600px"
      append-to-body
    >
      <el-form>
        <el-form-item label="运营组" prop="groupName">
          <span>{{ shopDialog.groupName }}</span>
        </el-form-item>
        <el-form-item label="已分配店铺" v-if="assignedShops.length > 0">
          <div class="assigned-shops">
            <el-tag
              v-for="shop in assignedShops"
              :key="shop.shopId"
              class="shop-tag"
              type="success"
              size="small"
              closable
              @close="handleRemoveShop(shop)"
            >
              {{ shop.shopName }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="filter-options">
            <el-switch
              v-model="shopDialog.showOtherGroupShops"
              active-text="显示所有未分配给本组的店铺"
              inactive-text="仅显示未分配的店铺"
              @change="handleFilterChange"
            />
          </div>
        </el-form-item>
        <el-form-item label="选择店铺" prop="selectedShops">
          <el-select
            v-model="shopDialog.selectedShops"
            filterable
            multiple
            placeholder="请选择店铺"
            style="width: 100%"
            v-loading="shopLoading"
          >
            <el-option-group label="未分配给任何运营组的店铺">
              <el-option
                v-for="item in unassignedGroupShops"
                :key="item.shopId"
                :label="item.shopName"
                :value="item.shopId"
              />
            </el-option-group>
            <el-option-group v-if="shopDialog.showOtherGroupShops && otherGroupShops.length > 0" label="已分配给其他运营组的店铺">
              <el-option
                v-for="item in otherGroupShops"
                :key="item.shopId"
                :label="`${item.shopName} (${item.groupNames})`"
                :value="item.shopId"
              />
            </el-option-group>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="shopDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitAssignShops">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看运营组详情弹窗 -->
    <GroupDetail
      v-model:visible="detailDialog.visible"
      :group-id="detailDialog.groupId"
    />

    <!-- 导出选项对话框 -->
    <el-dialog
      title="导出数据"
      v-model="exportDialog.visible"
      width="500px"
      append-to-body
    >
      <el-form>
        <el-form-item label="导出类型">
          <el-radio-group v-model="exportType">
            <el-radio label="all">全部</el-radio>
            <el-radio label="page">当前页</el-radio>
            <el-radio label="selected">选中项</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="文件名">
          <el-input v-model="exportFileName" placeholder="请输入文件名" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="exportDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmExport" :loading="exportLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, h } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { 
  getGroupList, 
  addGroup, 
  updateGroup, 
  deleteGroup, 
  changeGroupStatus, 
  getGroupMembers, 
  addGroupMembers, 
  removeGroupMember,
  getUnassignedShops,
  assignShopsToGroup,
  removeShopFromGroup,
  exportGroups,
  getUserGroupsByUserId
} from '@/api/group'
import { getUserList, getUsersByPermission, getUsersByRoleKey, getUnassignedUsers } from '@/api/user'
import { getGroupShops } from '@/api/shop'
import type { OperationGroup, QueryGroupParams } from '@/types/group'
import { useRouter } from 'vue-router'
import { formatTime } from '@/utils/format'
import GroupDetail from './components/GroupDetail.vue'
import { exportAll, exportPage, exportSelected } from '@/utils/excel'

const router = useRouter()

// 运营组列表数据
const groupList = ref<OperationGroup[]>([])
const total = ref(0)
const loading = ref(false)

// 查询参数
const queryParams = reactive<QueryGroupParams>({
  groupName: '',
  leaderName: '',
  status: '',
  pageNum: 1,
  pageSize: 10
})

// 状态选项
const statusOptions = [
  { label: '正常', value: '0' },
  { label: '禁用', value: '1' }
]

// 表单参数
const groupFormRef = ref<FormInstance>()
const form = reactive<OperationGroup>({
  groupName: '',
  leaderId: undefined as unknown as number,
  status: '0',
  remark: ''
})

// 表单校验规则
const rules = {
  groupName: [
    { required: true, message: '运营组名称不能为空', trigger: 'blur' }
  ],
  leaderId: [
    { required: true, message: '请选择负责人', trigger: 'change' }
  ]
}

// 弹窗配置
const dialog = reactive({
  visible: false,
  title: '',
  type: ''
})

// 用户选择相关
const userOptions = ref<any[]>([])
const userSearchLoading = ref(false)
const leaderOptions = ref<any[]>([])
const operationUserOptions = ref<any[]>([])
const existingMemberList = ref<any[]>([])

// 分配成员对话框
const memberDialog = reactive({
  visible: false,
  groupId: 0,
  groupName: '',
  selectedUsers: [] as number[],
  showAllOperationUsers: false
})

// 分配店铺对话框
const shopDialog = reactive({
  visible: false,
  groupId: 0,
  groupName: '',
  selectedShops: [] as number[],
  showOtherGroupShops: false
})

// 未分配的店铺列表
const unassignedShops = ref<any[]>([])
const unassignedGroupShops = ref<any[]>([]) // 未分配给任何运营组的店铺
const otherGroupShops = ref<any[]>([]) // 已分配给其他运营组的店铺
const assignedShops = ref<any[]>([]) // 已分配给当前运营组的店铺
const shopLoading = ref(false)

// 查看详情弹窗
const detailDialog = reactive({
  visible: false,
  groupId: 0
})

// 导出类型和文件名
const exportType = ref('page')
const exportFileName = ref('运营组数据')
const exportLoading = ref(false)

// 多选选中数据
const multipleSelection = ref<any[]>([])

// 选择行
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
  console.log('选中的行数据:', selection)
}

// 查询列表
const getList = async () => {
  loading.value = true
  try {
    const res = await getGroupList(queryParams)
    groupList.value = res.data.records || []
    total.value = res.data.total || 0
  } catch (error) {
    console.error('获取运营组列表失败', error)
  } finally {
    loading.value = false
  }
}

// 搜索用户（远程）
const remoteUserSearch = async (query: string) => {
  if (query) {
    userSearchLoading.value = true
    try {
      const res = await getUserList({ nickName: query, pageSize: 10 })
      userOptions.value = res.data.records || []
    } catch (error) {
      console.error('搜索用户失败', error)
    } finally {
      userSearchLoading.value = false
    }
  } else {
    userOptions.value = []
  }
}

// 获取运营组长用户列表
const getLeaderUsers = async () => {
  try {
    const res = await getUsersByRoleKey('operationLeader', true)
    leaderOptions.value = res.data || []
  } catch (error) {
    console.error('获取运营组长用户列表失败', error)
    leaderOptions.value = []
  }
}

// 获取运营人员用户列表
const getOperationUsers = async (groupId: number) => {
  try {
    // 根据开关状态筛选用户
    if (memberDialog.showAllOperationUsers) {
      // 获取所有运营角色的用户
      const res = await getUsersByRoleKey('operation')
      const allOperationUsers = res.data || []
      
      // 过滤掉已经分配给此运营组的成员
      const existingUserIds = existingMemberList.value.map(member => member.userId)
      const filteredUsers = allOperationUsers.filter(user => !existingUserIds.includes(user.userId))
      
      // 获取每个用户所在的运营组信息
      const usersWithGroups = await Promise.all(
        filteredUsers.map(async (user) => {
          try {
            const groupsRes = await getUserGroupsByUserId(user.userId)
            const userGroups = groupsRes.data || []
            
            // 添加所属运营组信息
            return {
              ...user,
              groups: userGroups,
              groupNames: userGroups.map((g: any) => g.groupName).join(', ')
            }
          } catch (error) {
            console.error(`获取用户${user.userId}的运营组失败:`, error)
            return {
              ...user,
              groups: [],
              groupNames: ''
            }
          }
        })
      )
      
      operationUserOptions.value = usersWithGroups
    } else {
      // 仅显示未分配到任何运营组的运营人员
      const res = await getUnassignedUsers('operation')
      operationUserOptions.value = res.data || []
    }
  } catch (error) {
    console.error('获取运营人员列表失败', error)
    operationUserOptions.value = []
  }
}

// 获取运营组成员列表
const getExistingMembers = async (groupId: number) => {
  try {
    const res = await getGroupMembers(groupId, 1, 100)
    existingMemberList.value = res.data.records || []
  } catch (error) {
    console.error('获取运营组成员列表失败', error)
    existingMemberList.value = []
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置搜索
const resetQuery = () => {
  queryParams.groupName = ''
  queryParams.leaderName = ''
  queryParams.status = ''
  handleQuery()
}

// 改变每页数量
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  getList()
}

// 改变页码
const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page
  getList()
}

// 打开添加弹窗
const handleAdd = () => {
  reset()
  getLeaderUsers() // 获取运营组长用户列表
  dialog.visible = true
  dialog.title = '添加运营组'
  dialog.type = 'add'
}

// 打开修改弹窗
const handleUpdate = (row: OperationGroup) => {
  reset()
  getLeaderUsers() // 获取运营组长用户列表
  dialog.visible = true
  dialog.title = '修改运营组'
  dialog.type = 'edit'
  
  // 设置表单数据
  Object.assign(form, row)
}

// 查看详情
const handleView = (row: OperationGroup) => {
  // 打开详情弹窗
  detailDialog.groupId = row.groupId as number
  detailDialog.visible = true
}

// 管理成员
const handleMembers = (row: OperationGroup) => {
  // 跳转到成员管理页面
  const groupId = row.groupId
  router.push({ path: `/operation/group/members/${groupId}` })
}

// 打开分配成员对话框
const handleAssignMembers = async (row: OperationGroup) => {
  memberDialog.groupId = row.groupId as number
  memberDialog.groupName = row.groupName
  memberDialog.selectedUsers = []
  
  // 先获取已有成员
  await getExistingMembers(memberDialog.groupId)
  
  // 再获取可选的运营人员（过滤掉已分配的）
  await getOperationUsers(memberDialog.groupId)
  
  memberDialog.visible = true
}

// 提交分配成员
const submitAssignMembers = async () => {
  if (memberDialog.selectedUsers.length === 0) {
    ElMessage.warning('请选择至少一个运营人员')
    return
  }
  
  try {
    await addGroupMembers(memberDialog.groupId, memberDialog.selectedUsers)
    ElMessage.success('分配成员成功')
    memberDialog.visible = false
    // 刷新列表
    getList()
  } catch (error) {
    console.error('分配成员失败', error)
  }
}

// 打开分配店铺对话框
const handleAssignShops = async (row: OperationGroup) => {
  shopDialog.groupId = row.groupId as number
  shopDialog.groupName = row.groupName
  shopDialog.selectedShops = []
  shopDialog.showOtherGroupShops = false // 默认只显示未分配的店铺
  
  // 获取已分配给该运营组的店铺
  await getAssignedShopsList(shopDialog.groupId)
  
  // 获取未分配的店铺列表
  await getUnassignedShopsList(shopDialog.groupId)
  
  shopDialog.visible = true
}

// 获取已分配给运营组的店铺列表
const getAssignedShopsList = async (groupId: number) => {
  try {
    const res = await getGroupShops(groupId)
    assignedShops.value = res.data || []
  } catch (error) {
    console.error('获取已分配店铺列表失败', error)
    assignedShops.value = []
  }
}

// 获取未分配的店铺列表
const getUnassignedShopsList = async (groupId: number) => {
  shopLoading.value = true
  try {
    const res = await getUnassignedShops(groupId)
    unassignedShops.value = res.data || []
    
    // 分组处理店铺列表
    unassignedGroupShops.value = unassignedShops.value.filter(shop => !shop.groups || shop.groups.length === 0)
    otherGroupShops.value = unassignedShops.value.filter(shop => shop.groups && shop.groups.length > 0)
  } catch (error) {
    console.error('获取未分配店铺列表失败', error)
    unassignedShops.value = []
    unassignedGroupShops.value = []
    otherGroupShops.value = []
  } finally {
    shopLoading.value = false
  }
}

// 提交分配店铺
const submitAssignShops = async () => {
  if (shopDialog.selectedShops.length === 0) {
    ElMessage.warning('请选择至少一个店铺')
    return
  }
  
  try {
    await assignShopsToGroup(shopDialog.groupId, shopDialog.selectedShops)
    ElMessage.success('分配店铺成功')
    
    // 刷新已分配店铺列表
    await getAssignedShopsList(shopDialog.groupId)
    
    shopDialog.visible = false
    // 刷新列表
    getList()
  } catch (error) {
    console.error('分配店铺失败', error)
    ElMessage.error('分配店铺失败')
  }
}

// 删除运营组
const handleDelete = (row: OperationGroup) => {
  const groupId = row.groupId
  if (!groupId) return
  
  ElMessageBox.confirm(
    `确认删除运营组 "${row.groupName}" 吗？删除前请确保该运营组没有普通运营人员（组长可以保留）。`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteGroup([groupId])
      ElMessage.success('删除成功')
      getList()
    } catch (error: any) {
      console.error('删除失败', error)
      // 处理特定错误消息
      if (error.response && error.response.data && error.response.data.message) {
        ElMessage.error(error.response.data.message)
      } else {
        ElMessage.error('删除失败，请检查该运营组是否还有普通运营人员')
      }
    }
  }).catch(() => {
    // 取消删除
  })
}

// 重置表单
const reset = () => {
  form.groupId = undefined
  form.groupName = ''
  form.leaderId = undefined as unknown as number
  form.status = '0'
  form.remark = ''
  
  if (groupFormRef.value) {
    groupFormRef.value.resetFields()
  }
}

// 提交表单
const submitForm = async () => {
  if (!groupFormRef.value) return
  
  await groupFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialog.type === 'add') {
          await addGroup(form)
          ElMessage.success('添加成功')
        } else {
          await updateGroup(form)
          ElMessage.success('修改成功')
        }
        dialog.visible = false
        getList()
      } catch (error) {
        console.error('提交失败', error)
      }
    }
  })
}

// 处理移除成员
const handleRemoveMember = async (member: any) => {
  try {
    // 调用移除成员API
    await removeGroupMember(memberDialog.groupId, member.userId)
    
    // 从已有成员列表中移除
    existingMemberList.value = existingMemberList.value.filter(item => item.userId !== member.userId)
    
    // 将该用户添加回可选列表
    const removedUser = {
      userId: member.userId,
      nickName: member.nickName
    }
    operationUserOptions.value.push(removedUser)
    
    // 按昵称排序
    operationUserOptions.value.sort((a, b) => a.nickName.localeCompare(b.nickName))
    
    ElMessage.success(`成员 ${member.nickName} 已移除`)
    
    // 刷新列表（可选，因为弹窗关闭时会刷新）
    getList()
  } catch (error) {
    console.error('移除成员失败', error)
    ElMessage.error('移除成员失败')
  }
}

// 处理成员过滤选项变化
const handleMemberFilterChange = () => {
  // 重新获取运营人员列表
  getOperationUsers(memberDialog.groupId)
}

// 处理过滤选项变化
const handleFilterChange = () => {
  // 重新获取未分配的店铺列表
  getUnassignedShopsList(shopDialog.groupId)
}

// 从remark中获取运营组名称 (不再需要，直接使用groupNames)
const getGroupNameFromRemark = (remark: string | null): string => {
  // 这个方法可以保留以兼容旧数据，但现在我们直接使用groupNames属性
  if (!remark) return '';
  return remark;
}

// 处理移除店铺
const handleRemoveShop = async (shop: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要将店铺 "${shop.shopName}" 从运营组 "${shopDialog.groupName}" 中移除吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用移除店铺API
    await removeShopFromGroup(shopDialog.groupId, shop.shopId)
    
    // 从已分配店铺列表中移除
    assignedShops.value = assignedShops.value.filter(item => item.shopId !== shop.shopId)
    
    // 将该店铺添加到未分配店铺列表中
    const removedShop = {
      shopId: shop.shopId,
      shopName: shop.shopName,
      groups: []
    }
    unassignedGroupShops.value.push(removedShop)
    
    // 按店铺名称排序
    unassignedGroupShops.value.sort((a, b) => a.shopName.localeCompare(b.shopName))
    
    ElMessage.success(`店铺 ${shop.shopName} 已从该运营组移除`)
    
    // 刷新列表
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除店铺失败', error)
      ElMessage.error('移除店铺失败')
    }
  }
}

// 导出数据
const handleExport = () => {
  // 记录当前选中的行
  console.log('导出前选中的行数据:', multipleSelection.value)
  
  ElMessageBox.confirm('确认导出运营组数据?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    center: true
  }).then(() => {
    // 显示导出选项对话框
    exportDialog.visible = true
  }).catch(() => {})
}

// 导出选项对话框
const exportDialog = reactive({
  visible: false
})

// 确认导出
const confirmExport = async () => {
  try {
    // 根据导出类型执行相应的导出操作
    let promise
    const fileName = exportFileName.value || '运营组数据'
    const sheetName = '运营组列表'
    
    if (exportType.value === 'all') {
      promise = exportAll(exportGroups, queryParams, fileName, sheetName)
    } else if (exportType.value === 'page') {
      const pageParams = {
        pageNum: queryParams.pageNum || 1,
        pageSize: queryParams.pageSize || 10
      }
      promise = exportPage(exportGroups, pageParams, queryParams, fileName, sheetName)
    } else if (exportType.value === 'selected') {
      // 获取选中的行
      const selectedIds = multipleSelection.value.map(row => row.groupId)
      console.log('确认导出时选中的ID:', selectedIds, '原始数据:', multipleSelection.value)
      if (selectedIds.length === 0) {
        ElMessage.warning('请至少选择一条记录')
        return
      }
      promise = exportSelected(exportGroups, selectedIds, fileName, sheetName)
    }
    
    // 操作中...
    exportLoading.value = true
    
    await promise
    ElMessage.success('导出成功')
    exportDialog.visible = false
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('导出失败', error)
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.search-form {
  flex: 1;
}

.action-btns {
  display: flex;
  align-items: flex-start;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.existing-members {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.member-tag {
  margin-bottom: 5px;
  margin-right: 8px;
  cursor: pointer;
}

.filter-options {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.el-option-group {
  margin-bottom: 10px;
}

.el-option-group__title {
  font-weight: bold;
  color: #606266;
  font-size: 14px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.assigned-shops {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 150px;
  overflow-y: auto;
  padding: 5px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.shop-tag {
  margin-bottom: 5px;
  margin-right: 5px;
}
</style> 