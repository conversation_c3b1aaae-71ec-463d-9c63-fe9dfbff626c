-- 批量通知消息模板SQL
-- 执行此SQL前请确保sys_message_template表存在

-- JIT备货单即将逾期批量通知模板
INSERT INTO `sys_message_template` VALUES (
    NULL, 
    'JIT_SOON_OVERDUE_BATCH', 
    'JIT备货单即将逾期批量通知', 
    '【${notifyTypeName}】系统共有${totalOrderCount}个备货单需要处理', 
    '<p><strong>${notifyTypeName}通知</strong>：系统中共有<strong>${totalOrderCount}</strong>个备货单需要处理</p>
#foreach($shopGroup in $shopGroups)
<div style="margin-top: 10px; border-top: 1px solid #eee; padding-top: 5px;">
  <h3>店铺【${shopGroup.shopName}】${shopGroup.shopRemark} - 共${shopGroup.items.size()}个备货单</h3>
  <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <tr style="background-color: #f2f2f2;">
      <th>备货单号</th>
      <th>商品名称</th>
      <th>预计最晚到货时间</th>
    </tr>
    #foreach($item in $shopGroup.items)
    <tr>
      <td>${item.subPurchaseOrderSn}</td>
      <td>${item.productName}</td>
      <td>#if($item.expectLatestArrivalTime)${formatDate.format("yyyy-MM-dd HH:mm:ss", $item.expectLatestArrivalTime)}#end</td>
    </tr>
    #end
  </table>
</div>
#end
<p style="margin-top: 20px; color: #d14836; font-weight: bold;">请各相关运营人员及时处理！</p>',
    '2', 
    '0', 
    now(), 
    now(), 
    'JIT备货单即将逾期批量通知模板');

-- JIT备货单已逾期批量通知模板
INSERT INTO `sys_message_template` VALUES (
    NULL, 
    'JIT_OVERDUE_BATCH', 
    'JIT备货单已逾期批量通知', 
    '【${notifyTypeName}】系统共有${totalOrderCount}个备货单需要处理', 
    '<p><strong>${notifyTypeName}通知</strong>：系统中共有<strong>${totalOrderCount}</strong>个备货单需要处理</p>
#foreach($shopGroup in $shopGroups)
<div style="margin-top: 10px; border-top: 1px solid #eee; padding-top: 5px;">
  <h3>店铺【${shopGroup.shopName}】${shopGroup.shopRemark} - 共${shopGroup.items.size()}个备货单</h3>
  <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <tr style="background-color: #f2f2f2;">
      <th>备货单号</th>
      <th>商品名称</th>
      <th>预计最晚到货时间</th>
    </tr>
    #foreach($item in $shopGroup.items)
    <tr>
      <td>${item.subPurchaseOrderSn}</td>
      <td>${item.productName}</td>
      <td>#if($item.expectLatestArrivalTime)${formatDate.format("yyyy-MM-dd HH:mm:ss", $item.expectLatestArrivalTime)}#end</td>
    </tr>
    #end
  </table>
</div>
#end
<p style="margin-top: 20px; color: #d14836; font-weight: bold;">请各相关运营人员立即处理！</p>',
    '2', 
    '0', 
    now(), 
    now(), 
    'JIT备货单已逾期批量通知模板');

-- 普通备货单未发货批量通知模板
INSERT INTO `sys_message_template` VALUES (
    NULL, 
    'NORMAL_NOT_DELIVERED_BATCH', 
    '普通备货单未发货批量通知', 
    '【${notifyTypeName}】系统共有${totalOrderCount}个备货单需要处理', 
    '<p><strong>${notifyTypeName}通知</strong>：系统中共有<strong>${totalOrderCount}</strong>个备货单需要处理</p>
#foreach($shopGroup in $shopGroups)
<div style="margin-top: 10px; border-top: 1px solid #eee; padding-top: 5px;">
  <h3>店铺【${shopGroup.shopName}】${shopGroup.shopRemark} - 共${shopGroup.items.size()}个备货单</h3>
  <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <tr style="background-color: #f2f2f2;">
      <th>备货单号</th>
      <th>商品名称</th>
      <th>创建时间</th>
    </tr>
    #foreach($item in $shopGroup.items)
    <tr>
      <td>${item.subPurchaseOrderSn}</td>
      <td>${item.productName}</td>
      <td>#if($item.purchaseTime)${formatDate.format("yyyy-MM-dd HH:mm:ss", $item.purchaseTime)}#end</td>
    </tr>
    #end
  </table>
</div>
#end
<p style="margin-top: 20px; color: #d14836; font-weight: bold;">请各相关运营人员及时联系供应商发货！</p>',
    '2', 
    '0', 
    now(), 
    now(), 
    '普通备货单未发货批量通知模板');

-- 普通备货单未到货批量通知模板
INSERT INTO `sys_message_template` VALUES (
    NULL, 
    'NORMAL_NOT_RECEIVED_BATCH', 
    '普通备货单未到货批量通知', 
    '【${notifyTypeName}】系统共有${totalOrderCount}个备货单需要处理', 
    '<p><strong>${notifyTypeName}通知</strong>：系统中共有<strong>${totalOrderCount}</strong>个备货单需要处理</p>
#foreach($shopGroup in $shopGroups)
<div style="margin-top: 10px; border-top: 1px solid #eee; padding-top: 5px;">
  <h3>店铺【${shopGroup.shopName}】${shopGroup.shopRemark} - 共${shopGroup.items.size()}个备货单</h3>
  <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <tr style="background-color: #f2f2f2;">
      <th>备货单号</th>
      <th>商品名称</th>
      <th>发货时间</th>
    </tr>
    #foreach($item in $shopGroup.items)
    <tr>
      <td>${item.subPurchaseOrderSn}</td>
      <td>${item.productName}</td>
      <td>#if($item.shippingTime)${formatDate.format("yyyy-MM-dd HH:mm:ss", $item.shippingTime)}#end</td>
    </tr>
    #end
  </table>
</div>
#end
<p style="margin-top: 20px; color: #d14836; font-weight: bold;">请各相关运营人员及时跟进物流状态！</p>',
    '2', 
    '0', 
    now(), 
    now(), 
    '普通备货单未到货批量通知模板'); 