package com.xiao.temu.modules.quality.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.modules.quality.service.QualityInspectionService;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.quality.dto.QualityInspectionRequestDTO;
import com.xiao.temu.modules.quality.vo.QualityInspectionVO;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.system.service.UserService;
import com.xiao.temu.infrastructure.api.TemuApiClient;
import lombok.extern.slf4j.Slf4j;
import org.ehcache.shadow.org.terracotta.statistics.Time;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static com.xiao.temu.infrastructure.api.TemuApiClient.addParameterIfPresent;

/**
 * 抽检结果明细服务实现
 */
@Service
@Slf4j
public class QualityInspectionServiceImpl implements QualityInspectionService {

    @Autowired
    private ShopService shopService;

    @Autowired
    private UserService userService;

    @Override
    public QualityInspectionVO getQualityInspectionList(QualityInspectionRequestDTO requestDTO, Long userId) {
        log.debug("查询Temu抽检结果明细, 请求参数: {}, 用户ID: {}", requestDTO, userId);
        
        // 验证用户是否有权限访问该店铺
        Long shopId = requestDTO.getShopId();
        if (shopId == null) {
            QualityInspectionVO errorResponse = new QualityInspectionVO();
            errorResponse.setSuccess(false);
            errorResponse.setErrorCode(400);
            errorResponse.setErrorMsg("店铺ID不能为空");
            return errorResponse;
        }
        
        // 检查用户是否是管理员
        boolean isAdmin = userService.isAdmin(userId);
        // 检查是否忽略权限检查
        boolean ignorePermissionCheck = Boolean.TRUE.equals(requestDTO.getIgnorePermissionCheck());
        
        // 如果不是管理员且不忽略权限检查，需要验证店铺权限
        if (!isAdmin && !ignorePermissionCheck && !shopService.checkShopPermission(userId, shopId, false)) {
            QualityInspectionVO errorResponse = new QualityInspectionVO();
            errorResponse.setSuccess(false);
            errorResponse.setErrorCode(403);
            errorResponse.setErrorMsg("您没有访问该店铺的权限");
            return errorResponse;
        }

        // 获取店铺信息
        Shop shop = shopService.getShopById(shopId).convertToShop();
        if (shop == null) {
            QualityInspectionVO errorResponse = new QualityInspectionVO();
            errorResponse.setSuccess(false);
            errorResponse.setErrorCode(404);
            errorResponse.setErrorMsg("店铺不存在或已被删除");
            return errorResponse;
        }

        try {
            // 设置API调用所需的公共参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.goods.qualityinspection.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());

            // 设置业务参数
            Map<String, Object> businessParams = new HashMap<>();
            
            // 添加分页信息
            HashMap<String, Integer> pageInfo = new HashMap<>();
            pageInfo.put("pageNo",requestDTO.getPageNo());
            pageInfo.put("pageSize",requestDTO.getPageSize());
            businessParams.put("pageInfo",pageInfo);
            businessParams.put("timestamp", String.valueOf(Time.time()));

            // 优化后的主逻辑
            addParameterIfPresent(businessParams, "purchaseNo", requestDTO.getPurchaseNo());
            addParameterIfPresent(businessParams, "skuQcResult", requestDTO.getSkuQcResult());
            addParameterIfPresent(businessParams, "skuIdList", requestDTO.getSkuIdList());
            addParameterIfPresent(businessParams, "skcIdList", requestDTO.getSkcIdList());
            addParameterIfPresent(businessParams, "qcResultUpdateTimeBegin", requestDTO.getQcResultUpdateTimeBegin());
            addParameterIfPresent(businessParams, "qcResultUpdateTimeEnd", requestDTO.getQcResultUpdateTimeEnd());
            // 调用API获取结果
            JSONObject result = TemuApiClient.sendRequest(commonParams, businessParams);

            // 构建返回对象
            QualityInspectionVO response = new QualityInspectionVO();
            response.setSuccess(result.getBoolean("success"));
            response.setErrorCode(result.getInteger("errorCode"));
            response.setErrorMsg(result.getString("errorMsg"));
            response.setResult(result.getJSONObject("result"));
            response.setShopId(shopId);
            response.setShopName(shop.getShopName());

            return response;
        } catch (Exception e) {
            log.error("获取抽检结果明细异常", e);
            QualityInspectionVO errorResponse = new QualityInspectionVO();
            errorResponse.setSuccess(false);
            errorResponse.setErrorCode(500);
            errorResponse.setErrorMsg("调用API失败: " + e.getMessage());
            return errorResponse;
        }
    }
} 