package com.xiao.temu.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xiao.temu.modules.system.entity.SysMenu;
import com.xiao.temu.modules.system.mapper.SysMenuMapper;
import com.xiao.temu.modules.system.mapper.SysRoleMenuMapper;
import com.xiao.temu.security.service.PermissionService;
import com.xiao.temu.modules.system.service.MenuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MenuServiceImpl implements MenuService {

    private final SysMenuMapper menuMapper;
    private final SysRoleMenuMapper roleMenuMapper;
    private final PermissionService permissionService;

    @Override
    public SysMenu getMenuById(Long menuId) {
        return menuMapper.selectById(menuId);
    }

    @Override
    public List<SysMenu> getMenuList(SysMenu menu) {
        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (menu != null) {
            // 按菜单名称模糊查询
            if (StringUtils.isNotBlank(menu.getMenuName())) {
                queryWrapper.like(SysMenu::getMenuName, menu.getMenuName());
            }
            
            // 按菜单可见性查询
            if (StringUtils.isNotBlank(menu.getVisible())) {
                queryWrapper.eq(SysMenu::getVisible, menu.getVisible());
            }
        }
        
        // 排序方式：父菜单ID、显示顺序
        queryWrapper.orderByAsc(SysMenu::getParentId).orderByAsc(SysMenu::getOrderNum);
        
        return menuMapper.selectList(queryWrapper);
    }

    @Override
    public List<SysMenu> getAllMenus() {
        // 查询所有可见的菜单
        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysMenu::getVisible, "0"); // 状态为显示
        queryWrapper.orderByAsc(SysMenu::getParentId).orderByAsc(SysMenu::getOrderNum);
        return menuMapper.selectList(queryWrapper);
    }

    @Override
    public List<SysMenu> getMenusByUserId(Long userId) {
        log.info("获取用户菜单 - 用户ID: {}", userId);
        
        // 如果用户ID为空，返回空列表
        if (userId == null) {
            log.warn("获取用户菜单 - 用户ID为空");
            return new ArrayList<>();
        }
        
        // 判断是否是超级管理员(用户ID为1的用户)
        if (userId == 1L) {
            log.info("获取用户菜单 - 超级管理员，返回所有菜单");
            return menuMapper.selectMenuAll();
        }
        
        // 普通用户，根据关联角色获取菜单
        List<SysMenu> menus = menuMapper.selectMenusByUserId(userId);
        log.info("获取用户菜单 - 菜单数量: {}", menus != null ? menus.size() : 0);
        return menus;
    }

    @Override
    public List<SysMenu> getMenusByRoleId(Long roleId) {
        return menuMapper.selectMenusByRoleId(roleId);
    }

    @Override
    public List<SysMenu> buildMenuTree(List<SysMenu> menus) {
        if (menus == null || menus.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 构建菜单ID和菜单对象的映射
        Map<Long, SysMenu> menuMap = menus.stream()
                .collect(Collectors.toMap(SysMenu::getMenuId, menu -> {
                    // 确保每个菜单都有children列表
                    if (menu.getChildren() == null) {
                        menu.setChildren(new ArrayList<>());
                    }
                    return menu;
                }));
        
        // 构建结果列表
        List<SysMenu> result = new ArrayList<>();
        
        // 循环处理每个菜单项
        for (SysMenu menu : menus) {
            // 如果是根菜单（父菜单ID为0）
            if (menu.getParentId() == 0) {
                result.add(menu);
            } else {
                // 非根菜单，查找其父菜单
                SysMenu parent = menuMap.get(menu.getParentId());
                if (parent != null) {
                    // 将当前菜单添加到父菜单的子菜单列表中
                    parent.getChildren().add(menu);
                } else {
                    // 如果找不到父菜单，作为顶级菜单处理
                    result.add(menu);
                }
            }
        }
        
        // 排序
        sortMenuTree(result);
        
        return result;
    }
    
    /**
     * 递归排序菜单树
     * 
     * @param menus 菜单列表
     */
    private void sortMenuTree(List<SysMenu> menus) {
        if (menus != null && !menus.isEmpty()) {
            // 按orderNum排序
            menus.sort(Comparator.comparing(SysMenu::getOrderNum));
            // 递归排序子菜单
            for (SysMenu menu : menus) {
                if (menu.getChildren() != null && !menu.getChildren().isEmpty()) {
                    sortMenuTree(menu.getChildren());
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertMenu(SysMenu menu) {
        // 设置默认值
        menu.setCreateTime(new Date());
        
        // 插入菜单
        int result = menuMapper.insert(menu);
        
        // 清除权限缓存
        if (result > 0) {
            permissionService.clearAllPermissionCache();
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateMenu(SysMenu menu) {
        // 检查菜单是否存在
        SysMenu existingMenu = menuMapper.selectById(menu.getMenuId());
        if (existingMenu == null) {
            throw new RuntimeException("修改菜单失败，菜单不存在");
        }
        
        // 设置更新时间
        menu.setUpdateTime(new Date());
        
        // 更新菜单
        int result = menuMapper.updateById(menu);
        
        // 清除权限缓存
        if (result > 0) {
            permissionService.clearAllPermissionCache();
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteMenu(Long menuId) {
        // 检查是否有子菜单
        if (hasChildMenu(menuId)) {
            throw new RuntimeException("存在子菜单，不允许删除");
        }
        
        // 检查菜单是否已分配给角色
        if (isMenuAssigned(menuId)) {
            throw new RuntimeException("菜单已分配给角色，不允许删除");
        }
        
        // 删除菜单
        int result = menuMapper.deleteById(menuId);
        
        // 清除权限缓存
        if (result > 0) {
            permissionService.clearAllPermissionCache();
        }
        
        return result;
    }

    @Override
    public boolean hasChildMenu(Long menuId) {
        return menuMapper.selectCountMenuByParentId(menuId) > 0;
    }

    @Override
    public boolean isMenuAssigned(Long menuId) {
        return roleMenuMapper.countRoleMenuByMenuId(menuId) > 0;
    }
} 