/**
 * 生产进度相关API
 */

const { request } = require('./apiUtils');
const { getBaseUrl } = require('./config');

// 获取API基础URL
const BASE_URL = getBaseUrl();

/**
 * 获取生产进度详情
 * @param {number} shopId - 店铺ID
 * @param {string} subPurchaseOrderSn - 备货单号
 * @returns {Promise} 生产进度详情
 */
const getProgressDetail = (shopId, subPurchaseOrderSn) => {
  return request(`${BASE_URL}/v1/production/progress/detail`, {}, 'GET', {
    shopId,
    subPurchaseOrderSn
  });
};

/**
 * 获取生产进度操作日志
 * @param {number} shopId - 店铺ID
 * @param {string} subPurchaseOrderSn - 备货单号
 * @returns {Promise} 生产进度操作日志
 */
const getProgressLogs = (shopId, subPurchaseOrderSn) => {
  return request(`${BASE_URL}/v1/production/progress/logs`, {}, 'GET', {
    shopId,
    subPurchaseOrderSn
  });
};

/**
 * 获取当前用户的操作日志记录(最近20条)
 * @returns {Promise} 用户操作日志
 */
const getUserOperationLogs = () => {
  return request(`${BASE_URL}/v1/production/progress/user-logs`, {}, 'GET', {
    limit: 20
  });
};

/**
 * 扫码更新生产进度
 * @param {Object} data - 更新数据
 * @param {number} data.shopId - 店铺ID
 * @param {string} data.subPurchaseOrderSn - 备货单号
 * @param {string} data.progressType - 进度类型(cutting/workshop/trimming/inspection/packaging/shipping)
 * @returns {Promise} 更新结果
 */
const updateProgress = (data) => {
  return request(`${BASE_URL}/v1/production/progress/update`, data);
};

/**
 * 撤销生产进度（管理员功能）
 * @param {Object} data - 撤销数据
 * @param {number} data.shopId - 店铺ID
 * @param {string} data.subPurchaseOrderSn - 备货单号
 * @param {string} data.progressType - 进度类型(cutting/workshop/trimming/inspection/packaging/shipping)
 * @returns {Promise} 撤销结果
 */
const cancelProgress = (data) => {
  return request(`${BASE_URL}/v1/production/progress/cancel`, data);
};

module.exports = {
  getProgressDetail,
  getProgressLogs,
  getUserOperationLogs,
  updateProgress,
  cancelProgress
}; 