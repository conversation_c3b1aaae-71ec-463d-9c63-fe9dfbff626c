import request from '@/utils/request'
import type { SendMessageDTO, MessageQueryDTO } from '@/types/message'
import type { ApiResponse } from '@/types/api'

// 发送消息
export function sendMessage(data: SendMessageDTO): Promise<ApiResponse> {
  return request({
    url: '/messages',
    method: 'post',
    data
  })
}

// 获取消息列表
export function getMessageList(query: MessageQueryDTO): Promise<ApiResponse> {
  return request({
    url: '/messages',
    method: 'get',
    params: query
  })
}

// 获取消息详情
export function getMessageDetail(messageId: number): Promise<ApiResponse> {
  return request({
    url: `/messages/${messageId}`,
    method: 'get'
  })
}

// 标记消息已读
export function markMessageRead(messageId: number): Promise<ApiResponse> {
  return request({
    url: `/messages/${messageId}/read`,
    method: 'put'
  })
}

// 删除消息
export function deleteMessage(messageId: number): Promise<ApiResponse> {
  return request({
    url: `/messages/${messageId}`,
    method: 'delete'
  })
}

// 批量标记消息已读
export function batchMarkRead(messageIds: number[]): Promise<ApiResponse> {
  return request({
    url: '/messages/batch-read',
    method: 'put',
    data: messageIds
  })
}

// 批量删除消息
export function batchDeleteMessage(messageIds: number[]): Promise<ApiResponse> {
  return request({
    url: '/messages/batch',
    method: 'delete',
    data: messageIds
  })
}

// 获取未读消息数量
export function getUnreadCount(): Promise<ApiResponse> {
  return request({
    url: '/messages/unread-count',
    method: 'get'
  })
}

// 获取消息模板列表
export function getTemplateList(query?: any): Promise<ApiResponse> {
  return request({
    url: '/message-templates',
    method: 'get',
    params: query
  })
}

// 获取消息模板详情
export function getTemplateDetail(templateId: number): Promise<ApiResponse> {
  return request({
    url: `/message-templates/${templateId}`,
    method: 'get'
  })
}

// 根据模板编码获取模板
export function getTemplateByCode(templateCode: string): Promise<ApiResponse> {
  return request({
    url: `/messages/templates/${templateCode}`,
    method: 'get'
  })
}

// 添加模板
export function addTemplate(data: any): Promise<ApiResponse> {
  return request({
    url: '/message-templates',
    method: 'post',
    data
  })
}

// 更新模板
export function updateTemplate(templateId: number, data: any): Promise<ApiResponse> {
  return request({
    url: `/message-templates/${templateId}`,
    method: 'put',
    data
  })
}

// 删除模板
export function deleteTemplate(templateId: number): Promise<ApiResponse> {
  return request({
    url: `/message-templates/${templateId}`,
    method: 'delete'
  })
}

// 更新模板状态
export function updateTemplateStatus(templateId: number, status: string): Promise<ApiResponse> {
  return request({
    url: `/message-templates/${templateId}/status/${status}`,
    method: 'put'
  })
} 