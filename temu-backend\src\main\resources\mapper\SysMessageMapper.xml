<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xiao.temu.modules.message.mapper.SysMessageMapper">

    <!-- 查询用户消息列表 -->
    <select id="selectMessageList" resultType="com.xiao.temu.modules.message.entity.SysMessage">
        SELECT
            m.*,
            mu.read_status AS readStatus,
            mu.read_time AS readTime
        FROM
            sys_message m
        INNER JOIN sys_message_user mu ON m.message_id = mu.message_id
        WHERE
            mu.user_id = #{userId}
            AND mu.deleted = '0'
            <if test="query.messageType != null and query.messageType != ''">
                AND m.message_type = #{query.messageType}
            </if>
            <if test="query.readStatus != null and query.readStatus != ''">
                AND mu.read_status = #{query.readStatus}
            </if>
            <if test="query.importance != null and query.importance != ''">
                AND m.importance = #{query.importance}
            </if>
            <if test="query.startTime != null">
                AND m.create_time &gt;= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                AND m.create_time &lt;= #{query.endTime}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                AND (m.title LIKE CONCAT('%', #{query.keyword}, '%') OR m.content LIKE CONCAT('%', #{query.keyword}, '%'))
            </if>
            <if test="query.messageId != null">
                AND m.message_id = #{query.messageId}
            </if>
            <if test="query.deleted != null and query.deleted != ''">
                AND m.deleted = #{query.deleted}
            </if>
        ORDER BY
            m.importance DESC,
            m.create_time DESC
    </select>

</mapper> 