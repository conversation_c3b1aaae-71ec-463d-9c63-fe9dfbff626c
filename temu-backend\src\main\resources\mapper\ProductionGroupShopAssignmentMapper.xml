<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.production.mapper.ProductionGroupShopAssignmentMapper">
    
    <resultMap type="com.xiao.temu.modules.production.entity.ProductionGroupShopAssignment" id="ProductionGroupShopAssignmentResult">
        <id property="id" column="id"/>
        <result property="shopId" column="shop_id"/>
        <result property="groupId" column="group_id"/>
        <result property="assignTime" column="assign_time"/>
        <result property="assignBy" column="assign_by"/>
        <result property="status" column="status"/>
    </resultMap>
    
    <sql id="selectProductionGroupShopAssignmentVo">
        select id, shop_id, group_id, assign_time, assign_by, status from production_group_shop_assignment
    </sql>
    
    <select id="selectGroupIdsByShopId" parameterType="Long" resultType="Long">
        select group_id
        from production_group_shop_assignment
        where shop_id = #{shopId} and status = '0'
    </select>
    
    <select id="selectShopIdsByGroupId" parameterType="Long" resultType="Long">
        select shop_id
        from production_group_shop_assignment
        where group_id = #{groupId} and status = '0'
    </select>
    
    <select id="selectOneByShopId" parameterType="Long" resultMap="ProductionGroupShopAssignmentResult">
        <include refid="selectProductionGroupShopAssignmentVo"/>
        where shop_id = #{shopId} and status = '0'
        limit 1
    </select>
    
</mapper> 