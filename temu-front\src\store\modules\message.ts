import { defineStore } from 'pinia'
import { getMessageList, getUnreadCount, markMessageRead, deleteMessage, batchMarkRead } from '@/api/message'
import type { Message, MessageQueryDTO, UnreadCountVO, ReadStatus } from '@/types/message'
import type { PageResult } from '@/types/message'
import type { ApiResponse } from '@/types/api'

interface MessageState {
  messageList: Message[]
  total: number
  loading: boolean
  unreadCount: UnreadCountVO
  countLoading: boolean
}

export const useMessageStore = defineStore('message', {
  state: (): MessageState => ({
    messageList: [],
    total: 0,
    loading: false,
    unreadCount: {
      totalCount: 0,
      systemCount: 0,
      taskCount: 0,
      shopCount: 0
    },
    countLoading: false
  }),

  actions: {
    // 获取消息列表
    async getMessageList(query: MessageQueryDTO): Promise<ApiResponse> {
      this.loading = true
      try {
        return await getMessageList(query)
      } finally {
        this.loading = false
      }
    },

    // 获取未读消息数量
    async getUnreadCount(): Promise<UnreadCountVO> {
      this.countLoading = true
      try {
        const response = await getUnreadCount()
        if (response && response.code === 200) {
          this.unreadCount = response.data
          return response.data
        }
        return this.unreadCount
      } finally {
        this.countLoading = false
      }
    },

    // 标记消息已读
    async markMessageRead(messageId: number): Promise<ApiResponse> {
      const response = await markMessageRead(messageId)
      if (response && response.code === 200) {
        // 更新本地消息状态
        const index = this.messageList.findIndex(msg => Number(msg.messageId) === messageId)
        if (index !== -1) {
          this.messageList[index].readStatus = '1' as ReadStatus
          this.messageList[index].readTime = new Date().toISOString()
        }
        // 更新未读计数
        this.updateUnreadCount(-1, this.messageList[index]?.messageType)
      }
      return response
    },

    // 删除消息
    async deleteMessage(messageId: number): Promise<ApiResponse> {
      const response = await deleteMessage(messageId)
      if (response && response.code === 200) {
        // 从列表中移除消息
        const index = this.messageList.findIndex(msg => Number(msg.messageId) === messageId)
        const isUnread = index !== -1 && this.messageList[index].readStatus === '0'
        const msgType = index !== -1 ? this.messageList[index].messageType : null
        
        if (index !== -1) {
          this.messageList.splice(index, 1)
          this.total--
        }
        
        // 如果是未读消息，更新未读计数
        if (isUnread && msgType) {
          this.updateUnreadCount(-1, msgType)
        }
      }
      return response
    },

    // 批量标记已读
    async batchMarkRead(messageIds: number[]): Promise<ApiResponse> {
      const response = await batchMarkRead(messageIds)
      if (response && response.code === 200) {
        // 更新本地消息状态
        let unreadSystemCount = 0
        let unreadTaskCount = 0
        let unreadShopCount = 0
        
        messageIds.forEach(id => {
          const index = this.messageList.findIndex(msg => Number(msg.messageId) === id)
          if (index !== -1 && this.messageList[index].readStatus === '0') {
            this.messageList[index].readStatus = '1' as ReadStatus
            this.messageList[index].readTime = new Date().toISOString()
            
            // 统计不同类型的消息数量
            switch (this.messageList[index].messageType) {
              case '1':
                unreadSystemCount++
                break
              case '2':
                unreadTaskCount++
                break
              case '3':
                unreadShopCount++
                break
            }
          }
        })
        
        // 更新未读计数
        this.unreadCount.totalCount -= (unreadSystemCount + unreadTaskCount + unreadShopCount)
        this.unreadCount.systemCount -= unreadSystemCount
        this.unreadCount.taskCount -= unreadTaskCount
        this.unreadCount.shopCount -= unreadShopCount
      }
      return response
    },

    // 更新未读计数
    updateUnreadCount(count: number, messageType?: string) {
      if (this.unreadCount.totalCount + count >= 0) {
        this.unreadCount.totalCount += count
      } else {
        this.unreadCount.totalCount = 0
      }
      
      if (messageType) {
        switch (messageType) {
          case '1': // 系统消息
            if (this.unreadCount.systemCount + count >= 0) {
              this.unreadCount.systemCount += count
            } else {
              this.unreadCount.systemCount = 0
            }
            break
          case '2': // 任务消息
            if (this.unreadCount.taskCount + count >= 0) {
              this.unreadCount.taskCount += count
            } else {
              this.unreadCount.taskCount = 0
            }
            break
          case '3': // 店铺消息
            if (this.unreadCount.shopCount + count >= 0) {
              this.unreadCount.shopCount += count
            } else {
              this.unreadCount.shopCount = 0
            }
            break
        }
      }
    },

    // 重置状态
    resetState() {
      this.messageList = []
      this.total = 0
      this.unreadCount = {
        totalCount: 0,
        systemCount: 0,
        taskCount: 0,
        shopCount: 0
      }
    }
  }
}) 