<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xiao.temu.modules.product.mapper.ProductMapper">
    
    <!-- 批量保存或更新商品数据 -->
    <insert id="batchSaveOrUpdate">
        INSERT INTO product (
            shop_id, product_id, product_name, product_skc_id, created_at, 
            is_support_personalization, ext_code, skc_site_status, match_skc_jit_mode, 
            main_image_url, sync_time, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.shopId}, #{item.productId}, #{item.productName}, 
                #{item.productSkcId}, #{item.createdAt}, #{item.isSupportPersonalization}, 
                #{item.extCode}, #{item.skcSiteStatus}, #{item.matchSkcJitMode}, 
                #{item.mainImageUrl}, #{item.syncTime}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            product_name = VALUES(product_name),
            created_at = VALUES(created_at),
            is_support_personalization = VALUES(is_support_personalization),
            ext_code = VALUES(ext_code),
            skc_site_status = VALUES(skc_site_status),
            match_skc_jit_mode = VALUES(match_skc_jit_mode),
            main_image_url = VALUES(main_image_url),
            sync_time = VALUES(sync_time),
            update_time = VALUES(update_time)
    </insert>
    
    <!-- 根据店铺ID和商品SKC ID列表查询商品 -->
    <select id="findBySkcIds" resultType="com.xiao.temu.modules.product.entity.Product">
        SELECT * FROM product 
        WHERE shop_id = #{shopId} 
        AND product_skc_id IN 
        <foreach collection="skcIds" item="skcId" open="(" separator="," close=")">
            #{skcId}
        </foreach>
    </select>
    
</mapper> 