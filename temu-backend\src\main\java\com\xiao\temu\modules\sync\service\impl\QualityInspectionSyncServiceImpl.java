package com.xiao.temu.modules.sync.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.modules.quality.entity.QualityInspection;
import com.xiao.temu.modules.sync.entity.QualityInspectionSyncTask;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.sync.vo.ApiResponse;
import com.xiao.temu.modules.quality.mapper.QualityInspectionMapper;
import com.xiao.temu.modules.sync.mapper.QualityInspectionSyncTaskMapper;
import com.xiao.temu.modules.sync.service.ProductSyncService;
import com.xiao.temu.modules.quality.service.QualityInspectionDefectDetailService;
import com.xiao.temu.modules.sync.service.QualityInspectionSyncService;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.infrastructure.api.TemuApiClient;
import com.xiao.temu.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 质检数据同步服务实现
 */
@Service
@Slf4j
public class QualityInspectionSyncServiceImpl implements QualityInspectionSyncService {

    @Autowired
    private QualityInspectionMapper qualityInspectionMapper;

    @Autowired
    private QualityInspectionSyncTaskMapper syncTaskMapper;

    @Autowired
    private ShopService shopService;
    
    @Autowired
    private QualityInspectionDefectDetailService defectDetailService;
    
    @Autowired
    private ProductSyncService productSyncService;
    
    @Value("${temu.sync.auto-sync-product:true}")
    private boolean autoSyncProduct;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse.QualityInspectionSyncVO syncQualityInspectionData(Long shopId) {
        // 获取同步任务
        QualityInspectionSyncTask task = syncTaskMapper.getByShopId(shopId);
        if (task == null) {
            // 如果不存在任务，则初始化
            boolean init = initSyncTask(shopId);
            if (!init) {
                ApiResponse.QualityInspectionSyncVO errorResponse = new ApiResponse.QualityInspectionSyncVO();
                errorResponse.setSuccess(false);
                errorResponse.setErrorCode(500);
                errorResponse.setErrorMsg("初始化同步任务失败");
                errorResponse.setShopId(shopId);
                return errorResponse;
            }
            task = syncTaskMapper.getByShopId(shopId);
        }

        // 获取店铺信息
        Shop shop = shopService.getShopById(shopId).convertToShop();
        if (shop == null) {
            ApiResponse.QualityInspectionSyncVO errorResponse = new ApiResponse.QualityInspectionSyncVO();
            errorResponse.setSuccess(false);
            errorResponse.setErrorCode(404);
            errorResponse.setErrorMsg("店铺不存在或已被删除");
            errorResponse.setShopId(shopId);
            return errorResponse;
        }

        // 更新任务状态为同步中
        task.setSyncStatus(1);
        task.setErrorMessage(null);
        syncTaskMapper.updateById(task);

        try {
            // 设置API调用所需的公共参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.goods.qualityinspection.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());

            // 获取最近的质检数据更新时间作为查询开始时间
            LocalDateTime latestUpdateTime = task.getLastUpdateTime();
            if (latestUpdateTime == null) {
                // 如果没有最近更新时间，则使用数据库中最新的记录时间
                latestUpdateTime = qualityInspectionMapper.getLatestUpdateTimeByShopId(shopId);
            }

            // 分别同步合格和不合格的数据
            int totalCount = 0;
            LocalDateTime maxUpdateTime = latestUpdateTime;
            
            // 同步合格数据 (skuQcResult = 1)
            Map<String, Object> qualifiedResult = syncQualityInspectionByResult(commonParams, shopId, latestUpdateTime, 1);
            int qualifiedCount = (int) qualifiedResult.get("count");
            LocalDateTime qualifiedMaxTime = (LocalDateTime) qualifiedResult.get("maxTime");
            totalCount += qualifiedCount;
            
            // 更新最大时间(合格数据)
            if (qualifiedMaxTime != null && (maxUpdateTime == null || qualifiedMaxTime.isAfter(maxUpdateTime))) {
                maxUpdateTime = qualifiedMaxTime;
            }
            
            // 同步不合格数据 (skuQcResult = 2)
            Map<String, Object> unqualifiedResult = syncQualityInspectionByResult(commonParams, shopId, latestUpdateTime, 2);
            int unqualifiedCount = (int) unqualifiedResult.get("count");
            LocalDateTime unqualifiedMaxTime = (LocalDateTime) unqualifiedResult.get("maxTime");
            totalCount += unqualifiedCount;
            
            // 更新最大时间(不合格数据)
            if (unqualifiedMaxTime != null && (maxUpdateTime == null || unqualifiedMaxTime.isAfter(maxUpdateTime))) {
                maxUpdateTime = unqualifiedMaxTime;
            }
            
            // 更新同步任务状态
            task.setSyncStatus(2); // 同步成功
            task.setLastSyncTime(LocalDateTime.now());
            task.setLastUpdateTime(maxUpdateTime);
            // 获取店铺质检实际总数，而不是累加记录
            Integer inspectionCount = getQualityInspectionCountByShopId(shopId);
            task.setTotalRecords(inspectionCount != null ? inspectionCount : 0);
            syncTaskMapper.updateById(task);
            
            // 如果有不合格数据，则触发不合格详情数据同步
            if (unqualifiedCount > 0) {
                try {
                    // 异步触发不合格详情数据同步
                    log.info("开始同步质检不合格详情数据: shopId={}", shopId);
                    // 使用默认配置进行同步
                    defectDetailService.syncDefectDetailData(shopId);
                } catch (Exception e) {
                    log.error("同步质检不合格详情数据异常: shopId={}, error={}", shopId, e.getMessage(), e);
                }
            }
            
            // 返回同步结果
            return ApiResponse.QualityInspectionSyncVO.fromTask(task, shop.getShopName());
            
        } catch (Exception e) {
            // 更新任务状态为同步失败
            log.error("同步质检数据异常，店铺ID: {}", shopId, e);
            task.setSyncStatus(3); // 同步失败
            
            // 限制错误消息长度，避免数据库字段溢出
            String errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.length() > 950) {
                errorMessage = errorMessage.substring(0, 950) + "...";
            }
            task.setErrorMessage(errorMessage);
            
            syncTaskMapper.updateById(task);
            
            // 返回错误信息
            ApiResponse.QualityInspectionSyncVO errorResponse = new ApiResponse.QualityInspectionSyncVO();
            errorResponse.setSuccess(false);
            errorResponse.setErrorCode(500);
            errorResponse.setErrorMsg("同步失败：" + e.getMessage());
            errorResponse.setShopId(shopId);
            errorResponse.setSyncStatus(3);
            return errorResponse;
        }
    }
    
    /**
     * 根据SKU ID列表同步指定店铺的质检数据
     *
     * @param shopId 店铺ID
     * @param skuIds SKU ID列表
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse.QualityInspectionSyncVO syncQualityInspectionDataBySkuIds(Long shopId, Set<Long> skuIds) {
        // 获取同步任务
        QualityInspectionSyncTask task = syncTaskMapper.getByShopId(shopId);
        if (task == null) {
            // 如果不存在任务，则初始化
            boolean init = initSyncTask(shopId);
            if (!init) {
                ApiResponse.QualityInspectionSyncVO errorResponse = new ApiResponse.QualityInspectionSyncVO();
                errorResponse.setSuccess(false);
                errorResponse.setErrorCode(500);
                errorResponse.setErrorMsg("初始化同步任务失败");
                errorResponse.setShopId(shopId);
                return errorResponse;
            }
            task = syncTaskMapper.getByShopId(shopId);
        }

        // 获取店铺信息
        Shop shop = shopService.getShopById(shopId).convertToShop();
        if (shop == null) {
            ApiResponse.QualityInspectionSyncVO errorResponse = new ApiResponse.QualityInspectionSyncVO();
            errorResponse.setSuccess(false);
            errorResponse.setErrorCode(404);
            errorResponse.setErrorMsg("店铺不存在或已被删除");
            errorResponse.setShopId(shopId);
            return errorResponse;
        }

        // 更新任务状态为同步中
        task.setSyncStatus(1);
        task.setErrorMessage(null);
        syncTaskMapper.updateById(task);

        try {
            // 设置API调用所需的公共参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.goods.qualityinspection.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());

            // 分别同步合格和不合格的数据
            int totalCount = 0;
            LocalDateTime maxUpdateTime = LocalDateTime.now();
            Set<Long> allSkcIds = new HashSet<>();
            
            // 将skuIds分批处理，每批最多10个
            List<List<Long>> skuIdBatches = partitionList(new ArrayList<>(skuIds), 10);
            
            for (List<Long> skuIdBatch : skuIdBatches) {
                // 同步合格数据 (skuQcResult = 1)
                Map<String, Object> qualifiedResult = syncQualityInspectionBySkuIdsAndResult(
                    commonParams, shopId, skuIdBatch, 1, allSkcIds);
                int qualifiedCount = (int) qualifiedResult.get("count");
                totalCount += qualifiedCount;
                
                // 同步不合格数据 (skuQcResult = 2)
                Map<String, Object> unqualifiedResult = syncQualityInspectionBySkuIdsAndResult(
                    commonParams, shopId, skuIdBatch, 2, allSkcIds);
                int unqualifiedCount = (int) unqualifiedResult.get("count");
                totalCount += unqualifiedCount;
            }
            
            // 更新同步任务状态
            task.setSyncStatus(2); // 同步成功
            task.setLastSyncTime(LocalDateTime.now());
            task.setLastUpdateTime(maxUpdateTime);
            // 获取店铺质检实际总数，而不是累加记录
            Integer inspectionCount = getQualityInspectionCountByShopId(shopId);
            task.setTotalRecords(inspectionCount != null ? inspectionCount : 0);
            syncTaskMapper.updateById(task);
            
            // 如果有不合格数据，则触发不合格详情数据同步
            if (totalCount > 0) {
                try {
                    // 异步触发不合格详情数据同步
                    log.info("开始同步质检不合格详情数据: shopId={}", shopId);
                    // 使用默认配置进行同步
                    defectDetailService.syncDefectDetailData(shopId);
                } catch (Exception e) {
                    log.error("同步质检不合格详情数据异常: shopId={}, error={}", shopId, e.getMessage(), e);
                }
            }
            
            // 返回同步结果
            return ApiResponse.QualityInspectionSyncVO.fromTask(task, shop.getShopName());
            
        } catch (Exception e) {
            // 更新任务状态为同步失败
            log.error("同步质检数据异常，店铺ID: {}", shopId, e);
            task.setSyncStatus(3); // 同步失败
            
            // 限制错误消息长度，避免数据库字段溢出
            String errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.length() > 950) {
                errorMessage = errorMessage.substring(0, 950) + "...";
            }
            task.setErrorMessage(errorMessage);
            
            syncTaskMapper.updateById(task);
            
            // 返回错误信息
            ApiResponse.QualityInspectionSyncVO errorResponse = new ApiResponse.QualityInspectionSyncVO();
            errorResponse.setSuccess(false);
            errorResponse.setErrorCode(500);
            errorResponse.setErrorMsg("同步失败：" + e.getMessage());
            errorResponse.setShopId(shopId);
            errorResponse.setSyncStatus(3);
            return errorResponse;
        }
    }
    
    /**
     * 根据SKU ID列表和质检结果类型同步数据
     * 
     * @param commonParams API调用公共参数
     * @param shopId 店铺ID
     * @param skuIds SKU ID列表
     * @param skuQcResult 质检结果类型：1-合格，2-不合格
     * @param allSkcIds 所有SKC ID集合，用于收集
     * @return 包含同步记录数和最大更新时间的Map
     */
    private Map<String, Object> syncQualityInspectionBySkuIdsAndResult(CommonParams commonParams, Long shopId, 
                                             List<Long> skuIds, Integer skuQcResult, Set<Long> allSkcIds) {
        int totalCount = 0;
        
        try {
            // 设置业务参数
            Map<String, Object> businessParams = new HashMap<>();
            
            // 添加分页信息
            HashMap<String, Integer> pageInfo = new HashMap<>();
            pageInfo.put("pageNo", 1);
            pageInfo.put("pageSize", 100); // 每页100条记录
            businessParams.put("pageInfo", pageInfo);
            
            // 添加质检结果参数
            businessParams.put("skuQcResult", skuQcResult);
            
            // 添加SKU ID列表参数
            businessParams.put("skuIdList", skuIds.toArray(new Long[0]));

            // 调用API获取结果
            JSONObject result = TemuApiClient.sendRequest(commonParams, businessParams);
            
            if (!result.getBoolean("success")) {
                String errorMsg = result.getString("errorMsg");
                if (errorMsg != null && errorMsg.length() > 950) {
                    errorMsg = errorMsg.substring(0, 950) + "...";
                }
                throw new RuntimeException("API调用失败: " + errorMsg);
            }

            // 解析结果
            JSONObject resultObj = result.getJSONObject("result");
            JSONArray skuList = resultObj.getJSONArray("skuList");
            
            if (skuList != null && !skuList.isEmpty()) {
                // 解析并保存数据
                List<QualityInspection> dataList = parseAndSaveData(skuList, shopId, null, skuQcResult);
                totalCount += dataList.size();
                
                // 收集所有商品的SKC ID
                Set<Long> skcIds = dataList.stream()
                        .map(QualityInspection::getProductSkcId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                allSkcIds.addAll(skcIds);
                
                // 如果开启了自动同步商品，且有SKC ID，则同步商品数据
                if (autoSyncProduct && !skcIds.isEmpty()) {
                    log.info("开始同步相关商品数据 - 店铺ID: {}, SKC ID数量: {}", shopId, skcIds.size());
                    try {
                        productSyncService.syncProductsBySkcIds(shopId, skcIds);
                    } catch (Exception e) {
                        log.error("同步相关商品数据异常 - 店铺ID: {}", shopId, e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("根据SKU ID同步质检数据异常 - 店铺ID: {}, SKU IDs: {}", shopId, skuIds, e);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("count", totalCount);
        return result;
    }
    
    /**
     * 将List分割成多个子List，每个子List最大大小为指定值
     * 
     * @param list 原始List
     * @param size 每个子List的最大大小
     * @return 分割后的子List集合
     */
    private <T> List<List<T>> partitionList(List<T> list, int size) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            partitions.add(list.subList(i, Math.min(i + size, list.size())));
        }
        return partitions;
    }
    
    /**
     * 根据质检结果类型同步数据
     * 
     * @param commonParams API调用公共参数
     * @param shopId 店铺ID
     * @param latestUpdateTime 最近更新时间
     * @param skuQcResult 质检结果类型：1-合格，2-不合格
     * @return 包含同步记录数和最大更新时间的Map
     */
    private Map<String, Object> syncQualityInspectionByResult(CommonParams commonParams, Long shopId, 
                                             LocalDateTime latestUpdateTime, Integer skuQcResult) {
        boolean hasMoreData = true;
        int pageNo = 1;
        int pageSize = 100; // 每页100条记录
        int totalCount = 0;
        LocalDateTime maxUpdateTime = latestUpdateTime;
        // 用于收集所有质检记录的productSkcId
        Set<Long> allSkcIds = new HashSet<>();

        while (hasMoreData) {
            // 设置业务参数
            Map<String, Object> businessParams = new HashMap<>();
            
            // 添加分页信息
            HashMap<String, Integer> pageInfo = new HashMap<>();
            pageInfo.put("pageNo", pageNo);
            pageInfo.put("pageSize", pageSize);
            businessParams.put("pageInfo", pageInfo);
            
            // 添加质检结果参数
            businessParams.put("skuQcResult", skuQcResult);
            
            // 添加时间范围
            if (latestUpdateTime != null) {
                // 转换为毫秒时间戳
                long timestamp = latestUpdateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                businessParams.put("qcResultUpdateTimeBegin", timestamp);
            }

            // 调用API获取结果
            JSONObject result = TemuApiClient.sendRequest(commonParams, businessParams);
            
            if (!result.getBoolean("success")) {
                String errorMsg = result.getString("errorMsg");
                if (errorMsg != null && errorMsg.length() > 950) {
                    errorMsg = errorMsg.substring(0, 950) + "...";
                }
                throw new RuntimeException("API调用失败: " + errorMsg);
            }

            // 解析结果
            JSONObject resultObj = result.getJSONObject("result");
            JSONArray skuList = resultObj.getJSONArray("skuList");
            
            if (skuList == null || skuList.isEmpty()) {
                hasMoreData = false;
                continue;
            }
            
            // 解析并保存数据
            List<QualityInspection> dataList = parseAndSaveData(skuList, shopId, maxUpdateTime, skuQcResult);
            totalCount += dataList.size();
            
            // 收集所有商品的SKC ID
            Set<Long> skcIds = dataList.stream()
                    .map(QualityInspection::getProductSkcId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            allSkcIds.addAll(skcIds);
            
            // 更新最大更新时间
            if (!dataList.isEmpty()) {
                LocalDateTime currentMaxTime = dataList.stream()
                        .map(QualityInspection::getQcResultUpdateTime)
                        .max(LocalDateTime::compareTo)
                        .orElse(maxUpdateTime);
                
                if (maxUpdateTime == null || (currentMaxTime != null && currentMaxTime.isAfter(maxUpdateTime))) {
                    maxUpdateTime = currentMaxTime;
                }
            }
            
            // 如果返回的数据量小于页大小，说明没有更多数据了
            if (skuList.size() < pageSize) {
                hasMoreData = false;
            } else {
                pageNo++;
            }
            
            // 防止无限循环，最多查询100页数据
            if (pageNo > 100) {
                hasMoreData = false;
            }
        }
        
        // 如果开启了自动同步商品，且有SKC ID，则同步商品数据
        if (autoSyncProduct && !allSkcIds.isEmpty()) {
            log.info("开始同步相关商品数据 - 店铺ID: {}, SKC ID数量: {}", shopId, allSkcIds.size());
            try {
                productSyncService.syncProductsBySkcIds(shopId, allSkcIds);
            } catch (Exception e) {
                log.error("同步相关商品数据异常 - 店铺ID: {}", shopId, e);
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("count", totalCount);
        result.put("maxTime", maxUpdateTime);
        return result;
    }

    /**
     * 解析API返回的数据并保存到数据库
     * 
     * @param skuList 质检SKU列表
     * @param shopId 店铺ID
     * @param maxUpdateTime 最大更新时间
     * @param skuQcResult 质检结果类型：1-合格，2-不合格
     * @return 保存的数据列表
     */
    private List<QualityInspection> parseAndSaveData(JSONArray skuList, Long shopId, 
                                                    LocalDateTime maxUpdateTime, Integer skuQcResult) {
        List<QualityInspection> dataList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
        
        for (int i = 0; i < skuList.size(); i++) {
            JSONObject skuItem = skuList.getJSONObject(i);
            
            QualityInspection data = new QualityInspection();
            data.setShopId(shopId);
            data.setQcBillId(skuItem.getLong("qcBillId"));
            data.setProductSkuId(skuItem.getLong("productSkuId"));
            data.setProductSkcId(skuItem.getLong("productSkcId"));
            data.setSpuId(skuItem.getLong("spuId"));
            data.setSkuName(skuItem.getString("skuName"));
            data.setCatName(skuItem.getString("catName"));
            data.setPurchaseNo(skuItem.getString("purchaseNo"));
            data.setSpec(skuItem.getString("spec"));
            data.setThumbUrl(skuItem.getString("thumbUrl"));
            
            // 设置质检结果
            data.setQcResult(skuQcResult == 1 ? "1" : "2");
            
            // 处理日期时间格式
            // 先尝试获取字符串类型的时间
            String qcResultUpdateTimeStr = skuItem.getString("qcResultUpdateTime");
            if (qcResultUpdateTimeStr != null && !qcResultUpdateTimeStr.isEmpty()) {
                try {
                    // 解析ISO-8601格式的日期时间字符串
                    data.setQcResultUpdateTime(DateUtils.parseLocalDateTime(qcResultUpdateTimeStr));
                } catch (Exception e) {
                    // 如果解析失败，尝试作为时间戳处理
                    try {
                        Long qcResultUpdateTimeLong = skuItem.getLong("qcResultUpdateTime");
                        if (qcResultUpdateTimeLong != null) {
                            data.setQcResultUpdateTime(
                                DateUtils.ofEpochMilli(qcResultUpdateTimeLong)
                            );
                        }
                    } catch (Exception ex) {
                        log.error("解析时间失败：{}", qcResultUpdateTimeStr, ex);
                    }
                }
            }
            
            // 确保qcResultUpdateTime不为null
            if (data.getQcResultUpdateTime() == null) {
                // 使用当前时间作为默认值
                data.setQcResultUpdateTime(now);
                log.warn("质检结果更新时间为空，使用当前时间作为默认值：SKU ID={}", data.getProductSkuId());
            }
            
            data.setSyncTime(now);
            data.setCreateTime(now);
            data.setUpdateTime(now);
            
            dataList.add(data);
        }
        
        // 批量保存或更新数据
        if (!dataList.isEmpty()) {
            qualityInspectionMapper.batchInsertOrUpdate(dataList);
        }
        
        return dataList;
    }

    @Override
    public boolean initSyncTask(Long shopId) {
        try {
            // 检查是否已存在任务
            QualityInspectionSyncTask existTask = syncTaskMapper.getByShopId(shopId);
            if (existTask != null) {
                // 如果任务已存在，则更新质检记录总数
                Integer inspectionCount = getQualityInspectionCountByShopId(shopId);
                existTask.setTotalRecords(inspectionCount != null ? inspectionCount : 0);
                syncTaskMapper.updateById(existTask);
                return true;
            }
            
            // 创建新的同步任务
            QualityInspectionSyncTask task = new QualityInspectionSyncTask();
            task.setShopId(shopId);
            task.setSyncStatus(0); // 未同步
            // 获取当前店铺的质检记录总数
            Integer inspectionCount = getQualityInspectionCountByShopId(shopId);
            task.setTotalRecords(inspectionCount != null ? inspectionCount : 0);
            task.setCreateTime(LocalDateTime.now());
            task.setUpdateTime(LocalDateTime.now());
            
            return syncTaskMapper.insert(task) > 0;
        } catch (Exception e) {
            log.error("初始化质检同步任务异常，店铺ID: {}", shopId, e);
            return false;
        }
    }

    @Override
    public List<QualityInspectionSyncTask> getSyncTasks(List<Long> shopIds) {
        if (shopIds == null || shopIds.isEmpty()) {
            // 查询所有任务
            return syncTaskMapper.selectList(null);
        } else {
            // 查询指定店铺的任务
            LambdaQueryWrapper<QualityInspectionSyncTask> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(QualityInspectionSyncTask::getShopId, shopIds);
            return syncTaskMapper.selectList(wrapper);
        }
    }

    @Override
    public QualityInspectionSyncTask getSyncTaskByShopId(Long shopId) {
        return syncTaskMapper.getByShopId(shopId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String executeScheduledSync() {
        // 获取需要同步的任务列表
        List<QualityInspectionSyncTask> tasks = syncTaskMapper.getTasksToSync();
        if (tasks.isEmpty()) {
            return "没有需要同步的任务";
        }
        
        StringBuilder resultBuilder = new StringBuilder();
        resultBuilder.append("执行定时同步任务，任务数量: ").append(tasks.size()).append("\n");
        
        for (QualityInspectionSyncTask task : tasks) {
            try {
                ApiResponse.QualityInspectionSyncVO result = syncQualityInspectionData(task.getShopId());
                resultBuilder.append("店铺ID: ")
                        .append(task.getShopId())
                        .append(", 结果: ")
                        .append(result.getSuccess() ? "成功" : "失败")
                        .append(", 记录数: ")
                        .append(result.getTotalRecords())
                        .append("\n");
            } catch (Exception e) {
                log.error("执行定时同步任务异常，店铺ID: {}", task.getShopId(), e);
                resultBuilder.append("店铺ID: ")
                        .append(task.getShopId())
                        .append(", 结果: 异常, 错误: ")
                        .append(e.getMessage())
                        .append("\n");
            }
        }
        
        return resultBuilder.toString();
    }

    /**
     * 获取指定店铺的质检记录总数
     *
     * @param shopId 店铺ID
     * @return 质检记录总数
     */
    @Override
    public Integer getQualityInspectionCountByShopId(Long shopId) {
        return qualityInspectionMapper.getQualityInspectionCountByShopId(shopId);
    }

    /**
     * 清空店铺的同步数据
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearSyncData(Long shopId) {
        if (shopId == null) {
            log.error("清空同步数据失败: shopId不能为空");
            return false;
        }
        
        try {
            log.info("开始清空店铺[{}]的质检同步数据", shopId);
            
            // 1. 删除质检不合格详情数据
            defectDetailService.deleteDefectDetailByShopId(shopId);
            
            // 2. 删除质检数据
            LambdaQueryWrapper<QualityInspection> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(QualityInspection::getShopId, shopId);
            int count = qualityInspectionMapper.delete(wrapper);
            log.info("已删除店铺[{}]的质检数据记录{}条", shopId, count);
            
            // 3. 重置同步任务状态
            QualityInspectionSyncTask task = syncTaskMapper.getByShopId(shopId);
            if (task != null) {
                // 更新为初始状态
                task.setSyncStatus(0); // 未同步
                
                // 明确设置时间字段为null
                task.setLastSyncTime(null);
                task.setLastUpdateTime(null);
                
                task.setErrorMessage(null);
                task.setTotalRecords(0);
                
                // 更新数据库
                int updated = syncTaskMapper.updateById(task);
                log.info("已重置店铺[{}]的同步任务状态，更新结果: {}", shopId, updated > 0 ? "成功" : "失败");
                
                // 确保时间字段被清空 - 使用直接SQL更新
                Map<String, Object> params = new HashMap<>();
                params.put("shopId", shopId);
                int timeFieldsCleared = syncTaskMapper.clearTimeFields(params);
                log.info("强制清空店铺[{}]的时间字段，更新结果: {}", shopId, timeFieldsCleared > 0 ? "成功" : "失败");
            } else {
                // 如果任务不存在，则创建一个初始状态的任务
                boolean init = initSyncTask(shopId);
                if (!init) {
                    log.error("店铺[{}]的同步任务初始化失败", shopId);
                    return false;
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("清空店铺[{}]的同步数据失败", shopId, e);
            throw e; // 抛出异常触发事务回滚
        }
    }
} 