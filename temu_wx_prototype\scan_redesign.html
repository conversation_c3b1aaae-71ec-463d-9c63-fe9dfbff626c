<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Temu扫码界面原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            color: #333;
        }
        
        .prototype-container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .header {
            background-color: #fff;
            height: 56px;
            display: flex;
            align-items: center;
            padding: 0 16px;
            border-bottom: 1px solid #eee;
            position: relative;
            z-index: 10;
        }
        
        .back-btn {
            margin-right: 10px;
        }
        
        .title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .tabs {
            display: flex;
            background: #fff;
            border-bottom: 1px solid #eee;
        }
        
        .tab {
            flex: 1;
            height: 44px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            position: relative;
            color: #666;
        }
        
        .tab.active {
            color: #1890ff;
            font-weight: 500;
        }
        
        .tab.active:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 25%;
            width: 50%;
            height: 3px;
            background: #1890ff;
            border-radius: 3px 3px 0 0;
        }
        
        .content {
            padding: 16px;
            height: calc(100vh - 100px);
        }
        
        .scan-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
        }
        
        .scanner-frame {
            width: 240px;
            height: 240px;
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 32px;
            border: 2px solid transparent;
        }
        
        .scan-border {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 12px;
        }
        
        .scan-corner {
            position: absolute;
            width: 30px;
            height: 30px;
            border-color: #1890ff;
            border-style: solid;
            border-width: 3px;
        }
        
        .corner-tl {
            top: 0;
            left: 0;
            border-right: none;
            border-bottom: none;
            border-radius: 8px 0 0 0;
        }
        
        .corner-tr {
            top: 0;
            right: 0;
            border-left: none;
            border-bottom: none;
            border-radius: 0 8px 0 0;
        }
        
        .corner-bl {
            bottom: 0;
            left: 0;
            border-right: none;
            border-top: none;
            border-radius: 0 0 0 8px;
        }
        
        .corner-br {
            bottom: 0;
            right: 0;
            border-left: none;
            border-top: none;
            border-radius: 0 0 8px 0;
        }
        
        .scan-line {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(to right, transparent, #1890ff, transparent);
            animation: scan 2s linear infinite;
        }
        
        @keyframes scan {
            from { top: 0; }
            to { top: 240px; }
        }
        
        .scan-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 24px;
            font-size: 16px;
            font-weight: 500;
            box-shadow: 0 4px 8px rgba(24, 144, 255, 0.2);
            cursor: pointer;
        }
        
        .album-btn {
            position: absolute;
            bottom: 20px;
            background: transparent;
            border: none;
            color: #666;
            font-size: 14px;
            display: flex;
            align-items: center;
            padding: 8px 16px;
            cursor: pointer;
        }
        
        .album-icon {
            margin-right: 6px;
        }
        
        /* 快速扫码配置区域 */
        .fast-scan-config {
            background: #fff;
            padding: 16px;
            margin-bottom: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .config-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .config-title {
            font-size: 16px;
            font-weight: 500;
        }
        
        .switch-container {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }
        
        .switch-input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .switch-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            border-radius: 34px;
            transition: .4s;
        }
        
        .switch-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 3px;
            bottom: 2px;
            background-color: white;
            border-radius: 50%;
            transition: .4s;
        }
        
        .switch-input:checked + .switch-slider {
            background-color: #1890ff;
        }
        
        .switch-input:checked + .switch-slider:before {
            transform: translateX(18px);
        }
        
        .role-section {
            margin-top: 16px;
        }
        
        .role-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 12px;
        }
        
        .role-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #eee;
            border-radius: 8px;
            cursor: pointer;
        }
        
        .role-item.selected {
            border-color: #1890ff;
            background-color: #e6f7ff;
        }
        
        .role-checkbox {
            width: 18px;
            height: 18px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            margin-right: 8px;
            position: relative;
        }
        
        .role-item.selected .role-checkbox {
            background-color: #1890ff;
            border-color: #1890ff;
        }
        
        .role-item.selected .role-checkbox:after {
            content: '';
            position: absolute;
            width: 5px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            top: 2px;
            left: 6px;
            transform: rotate(45deg);
        }
        
        .role-name {
            font-size: 14px;
        }
        
        .config-note {
            font-size: 12px;
            color: #999;
            margin-top: 12px;
        }
        
        /* 扫码结果区域 */
        .scan-result {
            background: #fff;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .result-header {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #eee;
        }
        
        .result-info {
            margin-bottom: 16px;
        }
        
        .info-item {
            display: flex;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .info-label {
            color: #666;
            width: 80px;
        }
        
        .info-value {
            font-weight: 500;
        }
        
        .progress-section {
            margin-top: 20px;
        }
        
        .progress-title {
            font-size: 15px;
            font-weight: 500;
            margin-bottom: 12px;
        }
        
        .progress-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .progress-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border: 1px solid #eee;
            border-radius: 8px;
            cursor: pointer;
        }
        
        .progress-item.selected {
            border-color: #1890ff;
            background-color: #e6f7ff;
        }
        
        .progress-item.completed {
            border-color: #52c41a;
        }
        
        .progress-name {
            font-size: 14px;
            display: flex;
            align-items: center;
        }
        
        .progress-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 10px;
            background: #f5f5f5;
            color: #666;
        }
        
        .completed .progress-status {
            background: #f6ffed;
            color: #52c41a;
        }
        
        .progress-checkbox {
            width: 18px;
            height: 18px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            margin-right: 10px;
            position: relative;
        }
        
        .progress-item.selected .progress-checkbox {
            background-color: #1890ff;
            border-color: #1890ff;
        }
        
        .progress-item.selected .progress-checkbox:after {
            content: '';
            position: absolute;
            width: 5px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            top: 2px;
            left: 6px;
            transform: rotate(45deg);
        }
        
        .action-buttons {
            margin-top: 24px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .btn {
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
        }
        
        .btn-primary {
            background: #1890ff;
            color: white;
        }
        
        .btn-outline {
            background: transparent;
            border: 1px solid #1890ff;
            color: #1890ff;
        }
        
        .btn-disabled {
            background: #f5f5f5;
            color: #999;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <!-- 原型图1：普通扫码界面 -->
    <div class="prototype-container">
        <div class="header">
            <div class="title">扫描二维码</div>
        </div>
        
        <div class="tabs">
            <div class="tab active">普通扫码</div>
            <div class="tab">快速扫码</div>
        </div>
        
        <div class="content">
            <div class="scan-container">
                <div class="scanner-frame">
                    <div class="scan-line"></div>
                    <div class="scan-border">
                        <div class="scan-corner corner-tl"></div>
                        <div class="scan-corner corner-tr"></div>
                        <div class="scan-corner corner-bl"></div>
                        <div class="scan-corner corner-br"></div>
                    </div>
                </div>
                
                <button class="scan-button">点击扫码</button>
                
                <button class="album-btn">
                    <span class="album-icon">🖼️</span>
                    从相册选择
                </button>
            </div>
        </div>
    </div>

    <!-- 原型图2：快速扫码配置界面 -->
    <div class="prototype-container" style="margin-top: 30px;">
        <div class="header">
            <div class="title">扫描二维码</div>
        </div>
        
        <div class="tabs">
            <div class="tab">普通扫码</div>
            <div class="tab active">快速扫码</div>
        </div>
        
        <div class="content">
            <div class="fast-scan-config">
                <div class="config-header">
                    <div class="config-title">快速扫码模式</div>
                    <label class="switch-container">
                        <input type="checkbox" class="switch-input" checked>
                        <span class="switch-slider"></span>
                    </label>
                </div>
                
                <div class="role-section">
                    <div class="role-title">选择要完成的工序</div>
                    <div class="role-list">
                        <div class="role-item selected">
                            <div class="role-checkbox"></div>
                            <div class="role-name">烧花/剪图</div>
                        </div>
                        <div class="role-item">
                            <div class="role-checkbox"></div>
                            <div class="role-name">车间/拣货</div>
                        </div>
                        <div class="role-item selected">
                            <div class="role-checkbox"></div>
                            <div class="role-name">剪线/压图</div>
                        </div>
                        <div class="role-item">
                            <div class="role-checkbox"></div>
                            <div class="role-name">查货</div>
                        </div>
                        <div class="role-item">
                            <div class="role-checkbox"></div>
                            <div class="role-name">包装</div>
                        </div>
                        <div class="role-item">
                            <div class="role-checkbox"></div>
                            <div class="role-name">发货</div>
                        </div>
                    </div>
                </div>
                
                <p class="config-note">开启快速扫码后，扫描二维码将直接完成所选工序，不会显示详情页面</p>
            </div>
            
            <div class="scan-container" style="margin-top: 20px;">
                <div class="scanner-frame">
                    <div class="scan-line"></div>
                    <div class="scan-border">
                        <div class="scan-corner corner-tl"></div>
                        <div class="scan-corner corner-tr"></div>
                        <div class="scan-corner corner-bl"></div>
                        <div class="scan-corner corner-br"></div>
                    </div>
                </div>
                
                <button class="scan-button">点击扫码</button>
                
                <button class="album-btn">
                    <span class="album-icon">🖼️</span>
                    从相册选择
                </button>
            </div>
        </div>
    </div>
    
    <!-- 原型图3：普通扫码结果界面 -->
    <div class="prototype-container" style="margin-top: 30px;">
        <div class="header">
            <div class="back-btn">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12.5 15L7.5 10L12.5 5" stroke="#333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <div class="title">扫描结果</div>
        </div>
        
        <div class="content" style="padding-top: 20px;">
            <div class="scan-result">
                <div class="result-header">备货单信息</div>
                
                <div class="result-info">
                    <div class="info-item">
                        <div class="info-label">备货单号:</div>
                        <div class="info-value">WB250606384325</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">店铺ID:</div>
                        <div class="info-value">10</div>
                    </div>
                </div>
                
                <div class="progress-section">
                    <div class="progress-title">选择要完成的工序</div>
                    
                    <div class="progress-list">
                        <div class="progress-item completed">
                            <div class="progress-name">
                                <div class="progress-checkbox"></div>
                                烧花/剪图
                            </div>
                            <div class="progress-status">已完成</div>
                        </div>
                        
                        <div class="progress-item selected">
                            <div class="progress-name">
                                <div class="progress-checkbox"></div>
                                车间/拣货
                            </div>
                            <div class="progress-status">未完成</div>
                        </div>
                        
                        <div class="progress-item">
                            <div class="progress-name">
                                <div class="progress-checkbox"></div>
                                剪线/压图
                            </div>
                            <div class="progress-status">未完成</div>
                        </div>
                        
                        <div class="progress-item">
                            <div class="progress-name">
                                <div class="progress-checkbox"></div>
                                查货
                            </div>
                            <div class="progress-status">未完成</div>
                        </div>
                        
                        <div class="progress-item selected">
                            <div class="progress-name">
                                <div class="progress-checkbox"></div>
                                包装
                            </div>
                            <div class="progress-status">未完成</div>
                        </div>
                        
                        <div class="progress-item">
                            <div class="progress-name">
                                <div class="progress-checkbox"></div>
                                发货
                            </div>
                            <div class="progress-status">未完成</div>
                        </div>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-primary">完成选中工序</button>
                    <button class="btn btn-outline">查看详情</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 