package com.xiao.temu.modules.sales.service;

import com.xiao.temu.modules.sales.dto.LocalSalesRequestDTO;
import com.xiao.temu.modules.sales.vo.LocalSalesVO;
import com.xiao.temu.modules.sales.vo.WarehouseGroupVO;

import java.util.List;

/**
 * 本地商品销售信息服务接口
 */
public interface LocalSalesService {

    /**
     * 获取本地商品销售信息列表
     *
     * @param requestDTO 请求参数
     * @param userId 当前用户ID
     * @return 商品销售信息VO
     */
    LocalSalesVO getLocalSalesList(LocalSalesRequestDTO requestDTO, Long userId);
    
    /**
     * 获取指定店铺下的备货仓组列表
     * 
     * @param shopId 店铺ID
     * @return 备货仓组列表
     */
    List<WarehouseGroupVO> getWarehouseGroups(Long shopId);
    
    /**
     * 获取多个店铺下的备货仓组列表
     * 
     * @param shopIds 店铺ID列表
     * @return 备货仓组列表
     */
    List<WarehouseGroupVO> getWarehouseGroupsByShopIds(List<Long> shopIds);
} 