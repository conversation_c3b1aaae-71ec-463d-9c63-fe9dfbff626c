package com.xiao.temu.modules.production.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产组角色分配数据传输对象
 */
@Data
public class ProductionGroupRoleAssignmentDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 生产组ID
     */
    private Long groupId;

    /**
     * 生产组名称
     */
    private String groupName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色标识
     */
    private String roleKey;

    /**
     * 分配时间
     */
    private Date assignTime;

    /**
     * 分配人ID(组长)
     */
    private Long assignBy;

    /**
     * 分配人姓名
     */
    private String assignByName;

    /**
     * 状态(0正常1禁用)
     */
    private String status;
} 