package com.xiao.temu.security.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * JWT配置属性类
 */
@Data
@Component
@ConfigurationProperties(prefix = "jwt")
public class JwtProperties {
    
    /**
     * JWT加密密钥
     */
    private String secret;
    
    /**
     * Token有效期（单位：分钟）
     */
    private Long expiration;
    
    /**
     * Token请求头名称
     */
    private String header;
    
    /**
     * Token前缀
     */
    private String tokenPrefix;
} 