package com.xiao.temu.modules.message.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xiao.temu.modules.message.dto.MessageQueryDTO;
import com.xiao.temu.modules.message.entity.SysMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 消息Mapper接口
 */
@Mapper
public interface SysMessageMapper extends BaseMapper<SysMessage> {

    /**
     * 查询用户消息列表
     *
     * @param page    分页参数
     * @param query   查询条件
     * @param userId  用户ID
     * @return 消息列表
     */
    IPage<SysMessage> selectMessageList(IPage<SysMessage> page, @Param("query") MessageQueryDTO query, @Param("userId") Long userId);
} 