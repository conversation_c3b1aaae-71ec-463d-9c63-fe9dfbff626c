<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.refund.mapper.RefundPackageDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xiao.temu.modules.refund.entity.RefundPackageDetail">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="package_sn" property="packageSn" />
        <result column="product_sku_id" property="productSkuId" />
        <result column="product_skc_id" property="productSkcId" />
        <result column="product_spu_id" property="productSpuId" />
        <result column="purchase_sub_order_sn" property="purchaseSubOrderSn" />
        <result column="quantity" property="quantity" />
        <result column="main_sale_spec" property="mainSaleSpec" />
        <result column="secondary_sale_spec" property="secondarySaleSpec" />
        <result column="thumb_url" property="thumbUrl" />
        <result column="order_type_desc" property="orderTypeDesc" />
        <result column="reason_desc" property="reasonDesc" />
        <result column="remark" property="remark" />
        <result column="outbound_time" property="outboundTime" />
        <result column="sync_time" property="syncTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    
    <!-- 批量插入退货包裹明细记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO refund_package_detail (
            shop_id, package_sn, product_sku_id, product_skc_id, product_spu_id, 
            purchase_sub_order_sn, quantity, main_sale_spec, secondary_sale_spec, 
            thumb_url, order_type_desc, reason_desc, remark, outbound_time, sync_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.shopId}, #{item.packageSn}, #{item.productSkuId}, #{item.productSkcId}, #{item.productSpuId}, 
                #{item.purchaseSubOrderSn}, #{item.quantity}, #{item.mainSaleSpec}, #{item.secondarySaleSpec}, 
                #{item.thumbUrl}, #{item.orderTypeDesc}, #{item.reasonDesc}, #{item.remark}, #{item.outboundTime}, #{item.syncTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE 
            purchase_sub_order_sn = VALUES(purchase_sub_order_sn),
            quantity = VALUES(quantity),
            main_sale_spec = VALUES(main_sale_spec),
            secondary_sale_spec = VALUES(secondary_sale_spec),
            thumb_url = VALUES(thumb_url),
            order_type_desc = VALUES(order_type_desc),
            reason_desc = VALUES(reason_desc),
            remark = VALUES(remark),
            sync_time = VALUES(sync_time),
            update_time = NOW()
    </insert>
    
    <!-- 获取最近的出库时间 -->
    <select id="getLatestOutboundTime" resultType="java.lang.Long">
        SELECT MAX(outbound_time) FROM refund_package_detail WHERE shop_id = #{shopId}
    </select>
    
</mapper>