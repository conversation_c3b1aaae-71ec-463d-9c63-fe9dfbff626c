package com.xiao.temu.modules.quality.controller;

import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.quality.dto.QualityInspectionRequestDTO;
import com.xiao.temu.modules.sync.vo.ApiResponse;
import com.xiao.temu.modules.quality.vo.QualityInspectionVO;
import com.xiao.temu.modules.quality.service.QualityInspectionService;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.system.service.SysDataPermissionService;
import com.xiao.temu.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Temu抽检结果明细控制器
 */
@RestController
@RequestMapping("/temu/qualityInspection")
public class QualityInspectionController {

    @Autowired
    private QualityInspectionService qualityInspectionService;
    

    @Autowired
    private UserService userService;

    @Autowired
    private SysDataPermissionService dataPermissionService;

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        // 通过用户名查询用户信息获取用户ID
        SysUser user = userService.getUserByUsername(username);
        if (user != null) {
            return user.getUserId();
        }
        throw new RuntimeException("用户未找到: " + username);
    }

    /**
     * 获取抽检结果明细列表
     *
     * @param requestDTO 请求参数
     * @return API响应
     */
    @PostMapping("/details")
    public ApiResponse getQualityInspectionList(@RequestBody QualityInspectionRequestDTO requestDTO) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        // 判断用户是否为管理员或拥有全部数据权限
        boolean isAdmin = userService.isAdmin(userId);
        
        // 获取用户的最高数据权限
        String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
        boolean hasFullDataPermission = "2".equals(permissionType);
        
        // 如果是管理员或拥有全部数据权限，设置忽略权限检查标志
        if (isAdmin || hasFullDataPermission) {
            requestDTO.setIgnorePermissionCheck(true);
        }
        
        // 调用服务获取结果
        QualityInspectionVO response = qualityInspectionService.getQualityInspectionList(requestDTO, userId);
        
        if (response.getSuccess()) {
            return ApiResponse.success(response);
        } else {
            return ApiResponse.error(response.getErrorCode(), response.getErrorMsg(), response);
        }
    }
    


    @PostMapping("/export")
    public ApiResponse exportQualityInspectionData(@RequestBody Map<String, Object> exportParams) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        try {
            // 判断用户是否为管理员或拥有全部数据权限
            boolean isAdmin = userService.isAdmin(userId);
            
            // 获取用户的最高数据权限
            String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
            boolean hasFullDataPermission = "2".equals(permissionType);
            
            // 解析导出参数
            Map<String, Object> queryParams = (Map<String, Object>) exportParams.get("queryParams");
            
            // 构建查询参数
            QualityInspectionRequestDTO requestDTO = new QualityInspectionRequestDTO();
            
            // 设置查询参数
            if (queryParams != null) {
                // 设置各种参数
                // ...
            }
            
            // 如果是管理员或拥有全部数据权限，设置忽略权限检查标志
            if (isAdmin || hasFullDataPermission) {
                requestDTO.setIgnorePermissionCheck(true);
            }
            
            // 调用服务获取结果
            QualityInspectionVO result = qualityInspectionService.getQualityInspectionList(requestDTO, userId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("导出数据失败: " + e.getMessage());
        }
    }
} 