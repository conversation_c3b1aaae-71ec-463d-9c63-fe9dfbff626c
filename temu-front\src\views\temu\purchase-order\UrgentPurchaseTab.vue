<template>
  <!-- 紧急备货建议标签页 -->
  <div class="urgent-purchase-tab">
    <!-- 紧急备货建议搜索区域 -->
    <div class="content-wrapper">
      <SearchArea 
        :shops="shops" 
        :query-params="queryParams" 
        :tag-counts="tagCounts"
        @search="handleSearch"
        @reset="handleReset"
        @update:query-params="updateQueryParams"
      />
      
        <!-- 状态筛选选项卡 -->
      <StatusTabs 
        :status-tabs="statusTabs"
        :active-status-tab="activeStatusTab"
        :query-params="queryParams"
        :sort-options="sortOptions"
        :has-selected-rows="selectedRows && selectedRows.length > 0"
        :total="total"
        v-model:modelValue="sortDisplayValue"
        @status-tab-click="handleStatusTabClick"
        @sort-change="handleSortChange"
        @batch-print-picking="handleBatchPrintPicking"
        @export-selected="handleExportSelected"
        @export-by-condition="handleExportByCondition"
        @batch-generate-qrcode="handleBatchGenerateQrCode"
        @batch-print-product-labels="handleBatchPrintProductLabels"
      />

      <!-- 紧急备货建议表格 -->
      <PurchaseOrderTable
        :loading="loading"
        :purchase-orders="purchaseOrders"
        :has-searched="hasSearched"
            @selection-change="handleSelectionChange"  
        @preview-image="handlePreviewImage"
        @view-detail="handleViewDetail"
        @print-picking="handlePrintPicking"
      />

      <!-- 分页控件 -->
      <ActionBar
        :query-params="queryParams"
          :total="total"
          :shop-ranges="shopRanges"
        @update:query-params="updateQueryParams"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 添加拣货单预览弹窗 -->
    <PickingOrderPreview
      v-model:visible="pickingDialogVisible"
      :picking-data="pickingData"
      @print-success="handlePrintSuccess"
    />

    <!-- 新增：多选二维码对话框 -->
    <el-dialog
      v-model="batchQrCodeDialogVisible"
      title="批量备货单二维码"
      width="600px"
      align-center
      destroy-on-close
      :append-to-body="true"
      :close-on-click-modal="true"
    >
      <div class="batch-qrcode-container">
        <!-- 新增：添加提示信息 -->
        <div v-if="batchQrCodeDataList.length > 1" class="qrcode-tip">
          <el-alert
            type="info"
            :closable="false"
            show-icon
          >
            <p>选中数据较多，已自动分成 {{ batchQrCodeDataList.length }} 张二维码，每张最多包含10个备货单</p>
          </el-alert>
        </div>

        <!-- 分页查看多个二维码 -->
        <div v-if="batchQrCodeDataList.length > 0" class="qrcode-preview">
          <!-- 添加分页 -->
          <div v-if="batchQrCodeDataList.length > 1" class="qrcode-pagination">
            <el-pagination
              background
              layout="prev, pager, next"
              :total="batchQrCodeDataList.length * 10"
              :page-size="10"
              :current-page="currentQrCodePage"
              @current-change="handleQrCodePageChange"
            />
            <span class="qrcode-pagination-text">第 {{ currentQrCodePage }} / {{ batchQrCodeDataList.length }} 张二维码</span>
          </div>

          <!-- 当前查看的二维码 -->
          <img :src="batchQrCodeUrlList[currentQrCodePage - 1]" class="qrcode-large" alt="批量备货单二维码" />
          
          <div class="qrcode-info">
            <p>当前二维码包含 {{ getCurrentBatchOrderCount() }} 个备货单</p>
            <p v-if="getSelectedShopCount(currentQrCodePage - 1) > 1" class="shop-count warning-text">涉及 {{ getSelectedShopCount(currentQrCodePage - 1) }} 个不同店铺</p>
            <div class="order-sns">
              <el-tooltip
                v-for="(order, index) in getCurrentBatchOrders()"
                :key="order.subPurchaseOrderSn"
                :content="`店铺ID: ${order.shopId}, 店铺名称: ${order.shopName || '未知'}`"
                placement="top"
              >
                <span class="order-sn-item">
                  {{ order.subPurchaseOrderSn }}{{ index < getCurrentBatchOrders().length - 1 ? ',' : '' }}
                </span>
              </el-tooltip>
            </div>
            <p>扫描此二维码可批量更新对应备货单的生产进度</p>
          </div>
          <div class="qrcode-actions">
            <el-button type="primary" @click="handlePrintCurrentQrCode">打印当前二维码</el-button>
            <el-button @click="handleDownloadCurrentQrCode">下载当前二维码</el-button>
            <el-button type="success" @click="handleDownloadAllQrCodes">下载全部二维码</el-button>
          </div>
        </div>
        <div v-else class="qrcode-empty">
          <el-empty description="生成二维码失败，请重试"></el-empty>
        </div>
      </div>
    </el-dialog>

    <!-- 在template部分的最后，添加ProductLabelPreview组件 -->
    <!-- 在</div>之前添加 -->
    <ProductLabelPreview
      v-model:visible="productLabelDialogVisible"
      :label-data="productLabelData"
      :loading="productLabelLoading"
      :error="productLabelError"
      @print-all="handlePrintAllLabels"
      @download-label="handleDownloadLabel"
      @retry="handleRetryGetLabels"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineEmits, defineProps, computed, onMounted, onBeforeUnmount, watch } from 'vue'
import { Search, Refresh, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { ElMessage, ElTooltip } from 'element-plus'
import { usePurchaseOrderStore } from '@/store'
import type { PurchaseOrderRequestDTO, Shop, PurchaseOrder, ShopDataRange } from '@/types/purchaseOrder'
import { formatTime } from '@/utils/format'
import { getPurchaseOrders, exportPurchaseOrders } from '@/api/temu/purchaseOrder'
import {
  SearchCard,
  TableCard,
  PaginationBar,
  EmptyTips,
} from '@/components/temu'
// 导入组件
import SearchArea from './UrgentPurchaseTab/SearchArea.vue'
import StatusTabs from './UrgentPurchaseTab/StatusTabs.vue'
import ActionBar from './UrgentPurchaseTab/ActionBar.vue'
import PurchaseOrderTable from './UrgentPurchaseTab/PurchaseOrderTable.vue'
import PickingOrderPreview from './UrgentPurchaseTab/PickingOrderPreview.vue'
// 导入生产进度相关组件
import QrCodeViewer from '@/components/temu/QrCodeViewer.vue'
import ProductionProgressViewer from '@/components/temu/ProductionProgressViewer.vue'
// 导入任务管理相关
import { useExportTaskStore } from '@/store/modules/exportTask'
import type { ExportTask } from '@/store/modules/exportTask'
import axios from 'axios'
import { getToken } from '@/utils/auth'
// 导入QRCode生成库
import QRCode from 'qrcode'
// 在导入部分添加
import ProductLabelPreview from './components/ProductLabelPreview.vue'
import { batchGetProductLabels } from '@/api/temu/productLabel'

const emit = defineEmits(['previewImage', 'viewDetail'])

const props = defineProps({
  shops: {
    type: Array as () => Shop[],
    required: true
  }
})

// 备货单store
const purchaseOrderStore = usePurchaseOrderStore()

// 加载状态
const loading = ref(false)

// 备货单列表
const purchaseOrders = ref<any[]>([])

// 总记录数
const total = ref(0)

// 新增：店铺数据范围
const shopRanges = ref<ShopDataRange[]>([])

// 新增：用于存储选中的行
const selectedRows = ref<any[]>([])

// 拣货单相关变量
const pickingDialogVisible = ref(false);
const pickingData = ref<any[]>([]);

// 是否已经执行过搜索
const hasSearched = ref(false)

// 新增：计算属性，用于将订单数据扁平化为SKU列表，并添加合计行
const flattenedPurchaseOrders = computed(() => {
  const flattened: any[] = []
  purchaseOrders.value.forEach((order, orderIndex) => { // 添加 orderIndex
    let totalDeclarePrice = 0
    let totalPurchaseQuantity = 0
    let totalDeliverQuantity = 0
    let totalRealReceiveQuantity = 0

    if (order.skuQuantityDetailList && order.skuQuantityDetailList.length > 0) {
      order.skuQuantityDetailList.forEach((sku: any, skuIndex: number) => {
        // 假设 declarePrice 存在于 sku 对象中，如果不存在需要确认字段来源
        const declarePrice = sku.declarePrice || 0; // 使用默认值0
        const purchaseQuantity = sku.purchaseQuantity || 0;
        const deliverQuantity = sku.deliverQuantity || 0;
        const realReceiveQuantity = sku.realReceiveAuthenticQuantity || 0;

        totalDeclarePrice += declarePrice * purchaseQuantity; // 申报价通常是单价*数量
        totalPurchaseQuantity += purchaseQuantity;
        totalDeliverQuantity += deliverQuantity;
        totalRealReceiveQuantity += realReceiveQuantity;

        flattened.push({
          ...order, // 复制订单的公共信息
          skuDetail: sku, // 当前SKU的详细信息
          _rowspan: skuIndex === 0 ? order.skuQuantityDetailList.length + 1 : 0, // 标记首个SKU行需要合并的行数（加上合计行）
          _isFirstSku: skuIndex === 0, // 标记是否是第一个SKU
          _isSkuRow: true, // 标记为SKU数据行
          _orderIndex: flattened.length, // 用于 spanMethod 判断
          _orderGroupIndex: orderIndex // 新增：原始订单索引
        })
      })
      // 添加合计行
      flattened.push({
        ...order, // 复制订单的公共信息以便访问订单ID等
        _isSummaryRow: true, // 标记为合计行
        _rowspan: 0, // 合计行不参与向上合并
        _orderIndex: flattened.length, // 用于 spanMethod 判断
        // 存储计算好的总计值
        _totalDeclarePrice: totalDeclarePrice.toFixed(2), // 保留两位小数
        _totalPurchaseQuantity: totalPurchaseQuantity,
        _totalDeliverQuantity: totalDeliverQuantity,
        _totalRealReceiveQuantity: totalRealReceiveQuantity,
        _orderGroupIndex: orderIndex // 新增：原始订单索引
      })
    } else {
      // 如果没有SKU详情，只保留订单行，不添加合计行
      flattened.push({
        ...order,
        skuDetail: {},
        _rowspan: 1,
        _isFirstSku: true,
        _isSkuRow: false, // 非SKU数据行
        _orderIndex: flattened.length,
        _orderGroupIndex: orderIndex // 新增：原始订单索引
      })
    }
  })
  return flattened
})

// 新增：表格行合并方法
const spanMethod = ({ row, column, rowIndex, columnIndex }: { row: any, column: any, rowIndex: number, columnIndex: number }) => {
  // 需要合并的列的 prop 或 label (适用于首个SKU行)
  // 修改：调整列名和索引判断
  const commonColumnsForFirstSku = [
    '店铺信息',
    '备货单号',
    '商品信息',
    '商品分类', // 如果有这个列
    '状态',
    '备货单创建时间',
    '发货信息',
    '操作'
  ]

  // 需要在合计行特殊处理的列
  const summaryColumns = ['SKU信息', '申报价(CNY)', '备货件数', '送货/入库数'];

  // 第一列是选择框，始终为 1x1
  if (columnIndex === 0) {
      // 只在分组的第一行（非合计行）显示选择框并合并
      if (row._rowspan > 0 && !row._isSummaryRow) {
        return { rowspan: row._rowspan, colspan: 1 };
      } else {
        // 隐藏其他行的选择框单元格
        return { rowspan: 0, colspan: 0 };
      }
  }

  if (row._isSummaryRow) {
    // 处理合计行
    if (column.label === 'SKU信息') {
      // SKU信息列在合计行显示 "合计"
      return { rowspan: 1, colspan: 1 };
    } else if (column.label === '申报价(CNY)' || column.label === '备货件数' || column.label === '送货/入库数') {
      // 显示合计值
      return { rowspan: 1, colspan: 1 };
    } else if (commonColumnsForFirstSku.includes(column.label)) {
      // 合计行隐藏 店铺信息、备货单号、商品信息、状态、备货单创建时间、发货信息、操作 列
      return { rowspan: 0, colspan: 0 };
    } else {
      // 其他列在合计行正常显示，不合并
      return { rowspan: 1, colspan: 1 };
    }
  } else if (row._isSkuRow) {
    // 处理普通SKU行
    if (commonColumnsForFirstSku.includes(column.label)) {
      // 处理需要根据 _rowspan 合并的公共列
      if (row._rowspan > 0) {
        return {
          rowspan: row._rowspan,
          colspan: 1
        }
      } else {
        return {
          rowspan: 0,
          colspan: 0
        }
      }
    } else {
      // SKU相关列及非公共列正常显示，不合并
      return { rowspan: 1, colspan: 1 };
    }
  } else {
     // 处理没有SKU的订单行（如果存在这种情况）
     if (commonColumnsForFirstSku.includes(column.label)) {
        return { rowspan: 1, colspan: 1 }; // 占满一行
     } else {
        // 其他列不显示
        return { rowspan: 0, colspan: 0 };
     }
  }
};

// 日期范围控件
const purchaseTimeRange = ref<[string, string] | null>(null)
const expectLatestDeliverTimeRange = ref<[string, string] | null>(null)
const expectLatestArrivalTimeRange = ref<[string, string] | null>(null)
const canDeliverTimeRange = ref<[string, string] | null>(null)

// 查询参数
const queryParams = reactive<PurchaseOrderRequestDTO>({
  shopIds: [],
  pageNo: 1,
  pageSize: 10,
  urgencyType: 1, // 紧急备货
  status: undefined,
  // 新增查询参数
  originalPurchaseOrderSnList: [],
  subPurchaseOrderSnList: [],
  productSnList: [],
  purchaseStockType: undefined,
  inventoryRegionList: [],
  productSkcIdList: [],
  settlementType: undefined,
  sourceList: [],
  isSystemAutoPurchaseSource: undefined,
  purchaseTimeFrom: undefined,
  purchaseTimeTo: undefined,
  deliverOrderSnList: [],
  isDelayDeliver: undefined,
  isDelayArrival: undefined,
  expectLatestDeliverTimeFrom: undefined,
  expectLatestDeliverTimeTo: undefined,
  expectLatestArrivalTimeFrom: undefined,
  expectLatestArrivalTimeTo: undefined,
  isFirst: undefined,
  skuLackSnapshot: undefined,
  qcReject: undefined,
  qcOption: undefined,
  qcNotPassCreate: undefined,
  lackOrSoldOutTagList: [],
  hotTag: undefined,
  canDeliverStartTime: undefined,
  canDeliverEndTime: undefined,
  productLabelCodeStyle: undefined,
  inboundReturn: undefined,
  inboundReturnCreate: undefined,
  // 快速筛选新增参数
  inFulfilmentPunish: undefined,
  deliverOrArrivalDelayStatusList: [],
  isTodayPlatformPurchase: undefined,
  // 状态筛选新增参数
  statusList: [],
  // 排序参数
  oneDimensionSort: undefined
})

// SKC输入框的值
const skcInput = ref('')
// 发货单号输入框的值
const deliverOrderSnInput = ref('')
// 新增：备货单号输入框的值
const subPurchaseOrderSnInput = ref('')

// 添加控制高级搜索展开/收起的状态变量
const isAdvancedSearchExpanded = ref(false)

// 窗口高度
const windowHeight = ref(window.innerHeight)

// 监听窗口大小变化
const handleResize = () => {
  windowHeight.value = window.innerHeight
}

// 添加用于存储每个筛选标签对应的结果数量
const tagCounts = ref<Record<string, number>>({
  'inFulfilmentPunish': 0,
  'deliverSoonDelay': 0,
  'deliverDelay': 0,
  'arrivalSoonDelay': 0,
  'arrivalDelay': 0,
  'isTodayPlatformPurchase': 0
})

// 切换高级搜索展开/收起状态
const toggleAdvancedSearch = () => {
  isAdvancedSearchExpanded.value = !isAdvancedSearchExpanded.value
}

// 日期范围变更处理函数
const handlePurchaseTimeChange = (val: [string, string] | null) => {
  if (val) {
    // 开始时间设置为当天的0:00:00
    const startDate = new Date(val[0]);
    startDate.setHours(0, 0, 0, 0);
    queryParams.purchaseTimeFrom = startDate.getTime();

    // 结束时间设置为当天的23:59:59
    const endDate = new Date(val[1]);
    endDate.setHours(23, 59, 59, 999);
    queryParams.purchaseTimeTo = endDate.getTime();
  } else {
    queryParams.purchaseTimeFrom = undefined;
    queryParams.purchaseTimeTo = undefined;
  }
}

const handleExpectLatestDeliverTimeChange = (val: [string, string] | null) => {
  if (val) {
    // 开始时间设置为当天的0:00:00
    const startDate = new Date(val[0]);
    startDate.setHours(0, 0, 0, 0);
    queryParams.expectLatestDeliverTimeFrom = startDate.getTime();

    // 结束时间设置为当天的23:59:59
    const endDate = new Date(val[1]);
    endDate.setHours(23, 59, 59, 999);
    queryParams.expectLatestDeliverTimeTo = endDate.getTime();
  } else {
    queryParams.expectLatestDeliverTimeFrom = undefined;
    queryParams.expectLatestDeliverTimeTo = undefined;
  }
}

const handleExpectLatestArrivalTimeChange = (val: [string, string] | null) => {
  if (val) {
    // 开始时间设置为当天的0:00:00
    const startDate = new Date(val[0]);
    startDate.setHours(0, 0, 0, 0);
    queryParams.expectLatestArrivalTimeFrom = startDate.getTime();

    // 结束时间设置为当天的23:59:59
    const endDate = new Date(val[1]);
    endDate.setHours(23, 59, 59, 999);
    queryParams.expectLatestArrivalTimeTo = endDate.getTime();
  } else {
    queryParams.expectLatestArrivalTimeFrom = undefined;
    queryParams.expectLatestArrivalTimeTo = undefined;
  }
}

const handleCanDeliverTimeChange = (val: [string, string] | null) => {
  if (val) {
    // 开始时间设置为当天的0:00:00
    const startDate = new Date(val[0]);
    startDate.setHours(0, 0, 0, 0);
    queryParams.canDeliverStartTime = startDate.getTime();

    // 结束时间设置为当天的23:59:59
    const endDate = new Date(val[1]);
    endDate.setHours(23, 59, 59, 999);
    queryParams.canDeliverEndTime = endDate.getTime();
  } else {
    queryParams.canDeliverStartTime = undefined;
    queryParams.canDeliverEndTime = undefined;
  }
}

// 状态格式化
const formatStatus = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '待创建',
    1: '待发货',
    2: '已送货',
    3: '已收货',
    4: '已拒收',
    5: '已验收，全部退回',
    6: '已验收',
    7: '已入库',
    8: '作废',
    9: '已超时'
  }
  return statusMap[status] || '未知状态'
}

// 状态类型
const getStatusType = (status: number): string => {
  const typeMap: Record<number, string> = {
    0: 'info',
    1: 'warning',
    2: 'primary',
    3: 'success',
    4: 'danger',
    5: 'success'
  }
  return typeMap[status] || 'info'
}

// 查询按钮点击
const handleSearch = () => {
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请选择至少一个店铺')
    return
  }

  // 重置所有标签计数
  Object.keys(tagCounts.value).forEach(key => {
    tagCounts.value[key] = 0;
  });

  queryParams.pageNo = 1
  fetchData()
  hasSearched.value = true
}

// 重置按钮点击
const handleReset = () => {
  // 保存当前选中的店铺信息
  const currentShopIds = [...(queryParams.shopIds || [])]

  // 重置所有查询参数
  queryParams.pageNo = 1
  queryParams.pageSize = 10
  queryParams.originalPurchaseOrderSnList = []
  queryParams.subPurchaseOrderSnList = []
  queryParams.productSnList = []
  queryParams.purchaseStockType = undefined
  queryParams.inventoryRegionList = []
  queryParams.productSkcIdList = []
  queryParams.settlementType = undefined
  queryParams.sourceList = []
  queryParams.isSystemAutoPurchaseSource = undefined
  queryParams.purchaseTimeFrom = undefined
  queryParams.purchaseTimeTo = undefined
  queryParams.deliverOrderSnList = []
  queryParams.isDelayDeliver = undefined
  queryParams.isDelayArrival = undefined
  queryParams.expectLatestDeliverTimeFrom = undefined
  queryParams.expectLatestDeliverTimeTo = undefined
  queryParams.expectLatestArrivalTimeFrom = undefined
  queryParams.expectLatestArrivalTimeTo = undefined
  queryParams.isFirst = undefined
  queryParams.skuLackSnapshot = undefined
  queryParams.qcReject = undefined
  queryParams.qcOption = undefined
  queryParams.qcNotPassCreate = undefined
  queryParams.lackOrSoldOutTagList = []
  queryParams.hotTag = undefined
  queryParams.canDeliverStartTime = undefined
  queryParams.canDeliverEndTime = undefined
  queryParams.productLabelCodeStyle = undefined
  queryParams.inboundReturn = undefined
  queryParams.inboundReturnCreate = undefined
  // 重置快速筛选参数
  queryParams.inFulfilmentPunish = undefined
  queryParams.deliverOrArrivalDelayStatusList = []
  queryParams.isTodayPlatformPurchase = undefined
  // 设置默认排序参数（不重置为undefined）
  // 更新UI显示值
  sortDisplayValue.value = sortOptions.value[0].value
  // 转换为对象格式
  queryParams.oneDimensionSort = getSortObject(sortOptions.value[0].value)

  // 重置状态筛选
  resetStatusFilter()

  // 恢复之前选中的店铺信息
  queryParams.shopIds = currentShopIds

  // 重置所有标签计数
  Object.keys(tagCounts.value).forEach(key => {
    tagCounts.value[key] = 0;
  });

  // 清空日期选择器的值
  purchaseTimeRange.value = null
  expectLatestDeliverTimeRange.value = null
  expectLatestArrivalTimeRange.value = null
  canDeliverTimeRange.value = null
  skcInput.value = ''
  deliverOrderSnInput.value = ''
  // 新增：重置备货单号输入框
  subPurchaseOrderSnInput.value = ''
}

// 处理每页显示条数变化
const handleSizeChange = (val: number) => {
  queryParams.pageSize = val
  fetchData()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  queryParams.pageNo = val
  fetchData()
}

// 图片预览
const handlePreviewImage = (url: string) => {
  emit('previewImage', url)
}

// 查看详情
const handleViewDetail = (row: PurchaseOrder) => {
  emit('viewDetail', row)
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    // 保存当前的查询参数,用于后续单独查询每个筛选条件的数量
    const currentQueryParams = { ...queryParams }

    // API层已经处理了参数过滤，这里直接传递queryParams
    await purchaseOrderStore.getPurchaseOrderList(queryParams);

    // 从store中获取数据
    const storeData = purchaseOrderStore.purchaseOrders
    const storeTotal = purchaseOrderStore.total
    const storeShopRanges = purchaseOrderStore.shopDataRanges // 现在store中应该有这个字段了
    purchaseOrders.value = storeData || []
    total.value = storeTotal || 0
    shopRanges.value = storeShopRanges || [] // 存储店铺范围信息

    // 如果有活跃的筛选条件,更新对应的数量
    updateActiveFilterCounts(currentQueryParams);

    // 更新状态选项卡数量
    updateStatusTabCounts(total.value)

    if (purchaseOrders.value.length === 0 && hasSearched.value) {
      ElMessage.warning('未查询到相关数据')
    }
  } catch (error) {
    console.error('获取紧急备货建议失败', error)
    ElMessage.error('获取紧急备货建议失败')
    purchaseOrders.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 监听与快速筛选相关的查询参数变化，自动触发搜索
watch(
  () => [
    queryParams.inFulfilmentPunish,
    // 使用 JSON.stringify 来深度监听数组变化
    JSON.stringify(queryParams.deliverOrArrivalDelayStatusList),
    queryParams.isTodayPlatformPurchase
  ],
  (newValues, oldValues) => {
    // 避免在组件初始化或重置时触发不必要的搜索
    // 只有在值确实发生变化时才搜索
    // 使用 stringify 比较确保数组内容的实际变化被捕捉
    if (JSON.stringify(newValues) !== JSON.stringify(oldValues)) {
       // 确保店铺已选择
       if (queryParams.shopIds && queryParams.shopIds.length > 0) {
           console.log('Quick filter related queryParams changed, triggering search:', newValues);
           // 直接调用 handleSearch 来确保所有检查和重置逻辑被执行
           handleSearch();
       } else {
           // 如果没有选择店铺，快速筛选变化时不触发搜索，避免错误提示
           console.log('Quick filter changed but no shop selected, skipping search.');
       }
    }
  },
  { deep: false } // deep: false 因为我们监听的是具体字段和字符串化的数组
);

// 更新活跃筛选条件的数量
const updateActiveFilterCounts = (currentParams: PurchaseOrderRequestDTO) => {
  // 只有在有活跃筛选条件且查询结果不为0时才更新
  if (total.value > 0) {
    // 更新活跃的筛选标签计数
    if (isTagActive('inFulfilmentPunish')) {
      tagCounts.value['inFulfilmentPunish'] = total.value;
    }

    if (isTagActive('deliverSoonDelay')) {
      tagCounts.value['deliverSoonDelay'] = total.value;
    }

    if (isTagActive('deliverDelay')) {
      tagCounts.value['deliverDelay'] = total.value;
    }

    if (isTagActive('arrivalSoonDelay')) {
      tagCounts.value['arrivalSoonDelay'] = total.value;
    }

    if (isTagActive('arrivalDelay')) {
      tagCounts.value['arrivalDelay'] = total.value;
    }

    if (isTagActive('isTodayPlatformPurchase')) {
      tagCounts.value['isTodayPlatformPurchase'] = total.value;
    }
  }
}

// SKC输入处理
const handleSkcInputChange = (val: string) => {
  // 移除此方法，改用下面的方法替代
}

// 添加SKC标签
const handleAddSkc = () => {
  if (!skcInput.value) return

  // 将输入内容按空格或逗号分割
  const skcItems = skcInput.value.split(/[\s,，]+/).filter(item => item.trim() !== '')

  // 添加标签
  skcItems.forEach(skc => {
    if (skc && !queryParams.productSkcIdList.includes(skc)) {
      queryParams.productSkcIdList.push(skc)
    }
  })

  // 清空输入框
  skcInput.value = ''
}

// 移除SKC标签
const handleRemoveSkc = (skc: string) => {
  const index = queryParams.productSkcIdList.indexOf(skc)
  if (index !== -1) {
    queryParams.productSkcIdList.splice(index, 1)
  }
}

// 添加发货单号标签
const handleAddDeliverOrderSn = () => {
  if (!deliverOrderSnInput.value) return

  // 将输入内容按空格或逗号分割
  const orderSnItems = deliverOrderSnInput.value.split(/[\s,，]+/).filter(item => item.trim() !== '')

  // 添加标签
  orderSnItems.forEach(orderSn => {
    if (orderSn && !queryParams.deliverOrderSnList.includes(orderSn)) {
      queryParams.deliverOrderSnList.push(orderSn)
    }
  })

  // 清空输入框
  deliverOrderSnInput.value = ''
}

// 移除发货单号标签
const handleRemoveDeliverOrderSn = (orderSn: string) => {
  const index = queryParams.deliverOrderSnList.indexOf(orderSn)
  if (index !== -1) {
    queryParams.deliverOrderSnList.splice(index, 1)
  }
}

// 快速筛选标签定义
const quickFilterTags = ref([
  { label: '履约考核', value: 'inFulfilmentPunish' },
  { label: '即将发货逾期', value: 'deliverSoonDelay' },
  { label: '发货已逾期', value: 'deliverDelay' },
  { label: '到货即将逾期', value: 'arrivalSoonDelay' },
  { label: '到货已逾期', value: 'arrivalDelay' },
  { label: '今日系统创建', value: 'isTodayPlatformPurchase' }
])

// 检查标签是否激活
const isTagActive = (tagValue: string): boolean => {
  switch (tagValue) {
    case 'inFulfilmentPunish':
      return queryParams.inFulfilmentPunish === true
    case 'deliverSoonDelay':
      return queryParams.deliverOrArrivalDelayStatusList?.includes(101) || false
    case 'deliverDelay':
      return queryParams.deliverOrArrivalDelayStatusList?.includes(102) || false
    case 'arrivalSoonDelay':
      return queryParams.deliverOrArrivalDelayStatusList?.includes(201) || false
    case 'arrivalDelay':
      return queryParams.deliverOrArrivalDelayStatusList?.includes(202) || false
    case 'isTodayPlatformPurchase':
      return queryParams.isTodayPlatformPurchase === true
    default:
      return false
  }
}

// 切换快速筛选
const toggleQuickFilter = (tagValue: string) => {
  // 清除此标签之前的数量
  tagCounts.value[tagValue] = 0;

  switch (tagValue) {
    case 'inFulfilmentPunish':
      queryParams.inFulfilmentPunish = queryParams.inFulfilmentPunish === true ? undefined : true
      break
    case 'deliverSoonDelay':
      toggleDeliveryStatus(101)
      break
    case 'deliverDelay':
      toggleDeliveryStatus(102)
      break
    case 'arrivalSoonDelay':
      toggleDeliveryStatus(201)
      break
    case 'arrivalDelay':
      toggleDeliveryStatus(202)
      break
    case 'isTodayPlatformPurchase':
      queryParams.isTodayPlatformPurchase = queryParams.isTodayPlatformPurchase === true ? undefined : true
      break
  }

  // 自动执行搜索
  handleSearch()
}

// 切换发货/到货状态
const toggleDeliveryStatus = (status: number) => {
  if (!queryParams.deliverOrArrivalDelayStatusList) {
    queryParams.deliverOrArrivalDelayStatusList = []
  }

  const index = queryParams.deliverOrArrivalDelayStatusList.indexOf(status)
  if (index > -1) {
    queryParams.deliverOrArrivalDelayStatusList.splice(index, 1)
  } else {
    queryParams.deliverOrArrivalDelayStatusList.push(status)
  }

  // 如果列表为空，则设为undefined
  if (queryParams.deliverOrArrivalDelayStatusList.length === 0) {
    queryParams.deliverOrArrivalDelayStatusList = []
  }
}

// 获取标签数量
const getTagCount = (tagValue: string): number => {
  // 直接返回tagCounts中存储的对应标签数量
  return tagCounts.value[tagValue] || 0;
}

// 状态筛选选项卡定义
const statusTabs = ref([
  { label: '全部', value: 'all', count: 0 },
  { label: '待创建', value: 0, count: 0 },
  { label: '待发货', value: 1, count: 0 },
  { label: '已送货', value: 2, count: 0 },
  { label: '已收货', value: 3, count: 0 },
  { label: '抽检全部退回', value: 5, count: 0 },
  { label: '已验收', value: 6, count: 0 },
  { label: '已入库', value: 7, count: 0 },
  { label: '已作废', value: 8, count: 0 },
  { label: '已取消', value: 10, count: 0 },
  { label: '已超时', value: 9, count: 0 }
])

// 当前激活的状态选项卡
const activeStatusTab = ref<string | number>('all')

// 处理状态选项卡点击
const handleStatusTabClick = (value: string | number) => {
  // 如果当前已经是激活状态，则不做任何操作
  if (activeStatusTab.value === value) return

  // 更新激活的选项卡
  activeStatusTab.value = value

  // 更新查询参数
  if (value === 'all') {
    queryParams.statusList = []
  } else {
    queryParams.statusList = [value as number]
  }

  // 执行搜索
  handleSearch()
}

// 更新状态选项卡数量
const updateStatusTabCounts = (total: number) => {
  // 如果当前激活的选项卡不是"全部"且有数据，则更新其计数
  if (activeStatusTab.value !== 'all' && total > 0) {
    const activeTab = statusTabs.value.find(tab => tab.value === activeStatusTab.value)
    if (activeTab) {
      activeTab.count = total
    }
  }
}

// 重置状态筛选
const resetStatusFilter = () => {
  activeStatusTab.value = 'all'
  queryParams.statusList = []

  // 重置所有状态选项卡的计数
  statusTabs.value.forEach(tab => {
    tab.count = 0
  })
}

// 排序选项定义
const sortOptions = ref([
  { 
    label: '子单创建时间最晚在上', 
    value: 'createdAt_desc'
  },
  { 
    label: '需发货时间最早在上', 
    value: 'expectLatestDeliverTime_asc'
  },
  { 
    label: '需到货时间最早在上', 
    value: 'expectLatestArrivalTime_asc'
  }
])

// 新增：用于UI展示的排序值，保存为字符串形式
const sortDisplayValue = ref(sortOptions.value[0].value)

// 根据选择的排序选项生成实际的排序参数
const getSortObject = (sortValue: string) => {
  const [param, order] = sortValue.split('_');
  return {
    firstOrderByParam: param,
    firstOrderByDesc: order === 'desc' ? 1 : 0
  }
}

// 处理排序选项变化
const handleSortChange = (val: string) => {
  // 更新UI显示值
  sortDisplayValue.value = val
  
  // 将字符串类型的排序选项转换为后端需要的对象格式
  queryParams.oneDimensionSort = getSortObject(val)
  
  // 执行搜索
  handleSearch()
}

// 组件挂载时初始化数据
onMounted(() => {
  // 设置默认排序为"子单创建时间最晚在上"
  sortDisplayValue.value = sortOptions.value[0].value
  queryParams.oneDimensionSort = getSortObject(sortOptions.value[0].value)

  // 如果有店铺数据，自动执行一次查询
  if (props.shops && props.shops.length > 0) {
    queryParams.shopIds = [props.shops[0].shopId]
    fetchData()
    hasSearched.value = true
  }
})

// 组件卸载前清除监听
onBeforeUnmount(() => {
  // 不再需要移除窗口大小变化监听
  // window.removeEventListener('resize', handleResize)
})

// 复制文本到剪贴板
const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text).then(() => {
    ElMessage.success('备货单号已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

// 格式化发货日期，显示为MM-DD HH:mm格式
const formatDeliveryDate = (timestamp: number): string => {
  if (!timestamp) return '-';
  const date = new Date(timestamp);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${month}-${day} ${hours}:${minutes}`;
}

// 新增：获取缺货/售罄标签文本
const getLackSoldOutTagText = (list: any[]): string => {
  if (!list || list.length === 0) return '';
  const hasLack = list.some(item => item.isLack);
  const hasSoldOut = list.some(item => item.soldOut); // 假设 soldOut 属性表示售罄
  if (hasLack && hasSoldOut) {
    return '含缺货/售罄SKU';
  } else if (hasLack) {
    return '含缺货SKU';
  } else if (hasSoldOut) {
    return '含售罄SKU';
  }
  return ''; // 如果列表不为空但没有缺货或售罄，理论上不应发生，返回空
}

// 计算剩余时间（小时和分钟）
const getRemainingTime = (timestamp: number): { hours: number; minutes: number } => {
  if (!timestamp) return { hours: 0, minutes: 0 };
  const now = new Date().getTime();
  const diff = timestamp - now;
  if (diff <= 0) return { hours: 0, minutes: 0 };

  const totalMinutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  return { hours, minutes };
}

// 计算超出时间（小时和分钟）
const getOverdueTime = (timestamp: number): { hours: number; minutes: number } => {
  if (!timestamp) return { hours: 0, minutes: 0 };
  const now = new Date().getTime();
  const diff = now - timestamp;
  if (diff <= 0) return { hours: 0, minutes: 0 };

  const totalMinutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  return { hours, minutes };
}

// 判断发货是否已完成 - 通过deliverInfo.deliverTime判断
const isDeliveryCompleted = (row: any): boolean => {
  if (!row.deliverInfo) return false;
  return !!row.deliverInfo.deliverTime;
}

// 判断是否已延时发货 - 实际发货时间比最晚要求发货时间晚
const isDeliveryDelayed = (row: any): boolean => {
  if (!row.deliverInfo || !row.deliverInfo.deliverTime) return false;
  return row.deliverInfo.deliverTime > row.deliverInfo.expectLatestDeliverTimeOrDefault;
}

// 判断是否已准时发货 - 实际发货时间比最晚要求发货时间早或相等
const isDeliveryOnTime = (row: any): boolean => {
  if (!row.deliverInfo || !row.deliverInfo.deliverTime) return false;
  return row.deliverInfo.deliverTime <= row.deliverInfo.expectLatestDeliverTimeOrDefault;
}

// 获取发货状态文本
const getDeliveryStatus = (row: any): string => {
  if (isDeliveryCompleted(row)) {
    // 已完成状态由 timeline-content 中的 el-tag 处理
    return ''; 
  } else if (isDeliveryOverdue(row)) {
    const { hours, minutes } = getOverdueTime(row.deliverInfo.expectLatestDeliverTimeOrDefault);
    let overdueText = '已逾期';
    if (hours > 0) overdueText += `${hours}小时`;
    if (minutes > 0) overdueText += `${minutes}分钟`;
    return overdueText || '已逾期'; // 兜底
  } else {
    const { hours, minutes } = getRemainingTime(row.deliverInfo.expectLatestDeliverTimeOrDefault);
    let remainingText = '';
    if (hours > 0) remainingText += `${hours}小时`;
    if (minutes > 0) remainingText += `${minutes}分钟`;
    return remainingText ? `${remainingText}后逾期` : '即将逾期'; // 兜底
  }
}

// 判断到货是否已完成
const isArrivalCompleted = (row: any): boolean => {
  return row.status >= 5; // 已完成状态
}

// 判断到货是否即将逾期（小于24小时）
const isArrivalSoon = (row: any): boolean => {
  if (!row.deliverInfo?.expectLatestArrivalTimeOrDefault) return false;
  const { hours, minutes } = getRemainingTime(row.deliverInfo.expectLatestArrivalTimeOrDefault);
  return hours > 0 && hours <= 24;
}

// 判断到货是否已逾期
const isArrivalOverdue = (row: any): boolean => {
  if (!row.deliverInfo?.expectLatestArrivalTimeOrDefault) return false;
  const { hours, minutes } = getRemainingTime(row.deliverInfo.expectLatestArrivalTimeOrDefault);
  return hours <= 0;
}

// 获取到货状态文本 (仅用于时间戳显示即将到期状态)
const getArrivalStatusTextForTimestamp = (row: any): string => {
  if (!isArrivalCompleted(row) && !isArrivalOverdue(row)) {
    // Only return remaining time if pending
    const { hours, minutes } = getRemainingTime(row.deliverInfo.expectLatestArrivalTimeOrDefault);
    let remainingText = '';
    if (hours > 0) remainingText += `${hours}小时`;
    if (minutes > 0) remainingText += `${minutes}分钟`;
    return remainingText ? `${remainingText}后逾期` : '即将逾期'; // 兜底
  }
  return ''; // Return empty otherwise
}

// 判断发货是否即将逾期（小于24小时）
const isDeliverySoon = (row: any): boolean => {
  if (!row.deliverInfo?.expectLatestDeliverTimeOrDefault) return false;
  const { hours, minutes } = getRemainingTime(row.deliverInfo.expectLatestDeliverTimeOrDefault);
  return hours > 0 && hours <= 24;
}

// 判断发货是否已逾期
const isDeliveryOverdue = (row: any): boolean => {
  if (!row.deliverInfo?.expectLatestDeliverTimeOrDefault) return false;
  const { hours, minutes } = getRemainingTime(row.deliverInfo.expectLatestDeliverTimeOrDefault);
  return hours <= 0;
}

// 获取发货时间线节点类型
const getDeliveryTimelineType = (row: any): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  if (isDeliveryOnTime(row)) {
    return 'success';
  } else if (isDeliveryDelayed(row)) {
    return 'danger';
  } else if (isDeliverySoon(row)) {
    return 'warning';
  } else if (isDeliveryOverdue(row)) {
    return 'danger';
  }
  return 'primary';
}

// 获取到货时间线节点类型
const getArrivalTimelineType = (row: any): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  if (isArrivalCompleted(row)) {
    return 'success';
  } else if (isArrivalSoon(row)) {
    return 'warning';
  } else if (isArrivalOverdue(row)) {
    return 'danger';
  }
  return 'primary';
}

// 计算发货和到货之间的时间间隔 (向上取整到小时)
const calculateTimeInterval = (row: any): string => {
  if (!row.deliverInfo?.expectLatestDeliverTimeOrDefault || !row.deliverInfo?.expectLatestArrivalTimeOrDefault) {
    return '';
  }
  const deliverTime = row.deliverInfo.expectLatestDeliverTimeOrDefault;
  const arrivalTime = row.deliverInfo.expectLatestArrivalTimeOrDefault;

  if (arrivalTime <= deliverTime) {
    // 到货时间不晚于发货时间，不显示间隔
    return '';
  }

  const diffInMillis = arrivalTime - deliverTime;
  // 计算总小时数并向上取整
  const hours = Math.ceil(diffInMillis / (1000 * 60 * 60));

  // 如果小时数大于0，则显示
  if (hours > 0) {
    return `${hours}h`;
  } else {
    // 如果差值小于1小时但大于0，可以显示 "<1小时" 或留空，这里选择留空
    return '';
  }
};

// 新增：添加备货单号标签
const handleAddSubPurchaseOrderSn = () => {
  if (!subPurchaseOrderSnInput.value) return

  // 将输入内容按空格或逗号分割
  const snItems = subPurchaseOrderSnInput.value.split(/[\s,，]+/).filter(item => item.trim() !== '')

  // 添加标签
  snItems.forEach(sn => {
    if (sn && !queryParams.subPurchaseOrderSnList.includes(sn)) {
      queryParams.subPurchaseOrderSnList.push(sn)
    }
  })

  // 清空输入框
  subPurchaseOrderSnInput.value = ''
}

// 新增：移除备货单号标签
const handleRemoveSubPurchaseOrderSn = (sn: string) => {
  const index = queryParams.subPurchaseOrderSnList.indexOf(sn)
  if (index !== -1) {
    queryParams.subPurchaseOrderSnList.splice(index, 1)
  }
}

// 新增：处理表格选择变化的函数
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection;
}

// 新增：处理打印成功的回调
const handlePrintSuccess = (printedItems: any[]) => {
  console.log('打印任务已完成', printedItems)
  // 可以在这里添加其他打印成功后的处理逻辑
}

// 新增：修改处理批量打印商品条码的函数
const handleBatchPrintBarcode = () => {
  if (!selectedRows.value || selectedRows.value.length === 0) {
    ElMessage.warning('请至少选择一项进行打印')
    return
  }
  
  
}

// 处理打印条码的函数 - 单个订单
const handlePrintBarcode = (row: any, orderIndex: number) => {
  // 选中单个订单，然后调用批量打印函数
  selectedRows.value = [row]
  handleBatchPrintBarcode()
}

// 处理打印拣货单的函数
const handlePrintPicking = (row: any, orderIndex: number) => {
  // 选中单个订单，然后调用批量打印函数
  selectedRows.value = [row]
  handleBatchPrintPicking()
}

// 处理批量打印拣货单
const handleBatchPrintPicking = () => {
  if (!selectedRows.value || selectedRows.value.length === 0) {
    ElMessage.warning('请至少选择一项进行打印')
    return
  }

  // 直接设置数据并显示预览组件，数据处理交给组件进行
  pickingData.value = selectedRows.value
  pickingDialogVisible.value = true
}

// 更新查询参数
const updateQueryParams = (newParams: PurchaseOrderRequestDTO) => {
  // 检查是否有排序参数，如果是字符串类型，需要转换
  if (newParams.oneDimensionSort && typeof newParams.oneDimensionSort === 'string') {
    // 更新UI显示值
    sortDisplayValue.value = newParams.oneDimensionSort;
    // 转换为对象格式
    newParams.oneDimensionSort = getSortObject(newParams.oneDimensionSort);
  }
  
  // 更新查询参数
  Object.assign(queryParams, newParams)
}

// 处理导出选中数据
const handleExportSelected = async () => {
  if (!selectedRows.value || selectedRows.value.length === 0) {
    ElMessage.warning('请至少选择一条记录进行导出')
    return
  }
  
  try {
    // 创建导出任务
    const exportTaskStore = useExportTaskStore();
    
    // 获取选中行的店铺备注列表
    const shopRemarks = selectedRows.value
      .map(row => row.shopRemark || '')
      .filter(remark => remark !== '')
      .filter((value, index, self) => self.indexOf(value) === index); // 去重
    
    // 构建文件名: 店铺代号+类型+日期时间
    const shopRemarksStr = shopRemarks.length > 0 
      ? shopRemarks.join('-').slice(0, 20) // 截取前20个字符，避免文件名过长
      : '未知店铺';
      
    const type = queryParams.urgencyType === 1 ? 'JIT' : '备货';
    const dateTimeStr = new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/[\/:]/g, '').replace(/\s+/g, '_'); // 将日期格式化并移除特殊字符
    
    const fileName = `${shopRemarksStr}-${type}-${dateTimeStr}`;
    const task = exportTaskStore.createTask(fileName, fileName);
    
    // 显示loading提示
    ElMessage.info('正在处理导出请求，请稍候...')
    
    // 提取选中行的备货单号
    const subPurchaseOrderSnList = selectedRows.value.map(row => row.subPurchaseOrderSn).filter(Boolean)
    
    // 获取当前排序条件，确保是对象格式
    let sortParam = queryParams.oneDimensionSort;
    if (typeof sortParam === 'string') {
      sortParam = getSortObject(sortParam);
    }
    
    // 修改：保留所有原始查询参数，只覆盖备货单号列表
    // 这确保了导出选中行时，仍然保持其他筛选条件，同时限定为选中的备货单
    const exportQueryParams = { ...queryParams };
    exportQueryParams.subPurchaseOrderSnList = subPurchaseOrderSnList;
    exportQueryParams.oneDimensionSort = sortParam;
    
    // 准备导出参数
    const exportParams = {
      queryParams: exportQueryParams,
      taskId: task.id, // 前端任务ID
      fileName: fileName
    }
    
    // 调用导出API
    await createExportTask(exportParams, task.id);
  } catch (error) {
    console.error('导出选中数据失败', error)
    ElMessage.error('导出选中数据失败')
  }
}

// 处理按条件导出数据
const handleExportByCondition = async (params: { exportType: string, pages: number }) => {
  try {
    // 创建导出任务
    const exportTaskStore = useExportTaskStore();
    
    // 获取查询的店铺备注列表
    let shopRemarks: string[] = [];
    
    // 如果有选择店铺，获取这些店铺的备注
    if (queryParams.shopIds && Array.isArray(queryParams.shopIds) && queryParams.shopIds.length > 0) {
      // 从props.shops中获取店铺备注
      shopRemarks = props.shops
        .filter(shop => {
          // 确保类型一致进行比较，将shop.shopId转换为字符串进行比较
          const shopId = shop.shopId.toString();
          return queryParams.shopIds && queryParams.shopIds.some(id => id.toString() === shopId);
        })
        .map(shop => shop.shopRemark || '')
        .filter(remark => remark !== '');
        
      // 如果从props.shops中没有获取到备注，尝试从已加载的数据中获取
      if (shopRemarks.length === 0 && purchaseOrders.value.length > 0) {
        const uniqueShopRemarks = new Set<string>();
        purchaseOrders.value.forEach(order => {
          if (order.shopRemark && order.shopRemark.trim() !== '') {
            uniqueShopRemarks.add(order.shopRemark);
          }
        });
        shopRemarks = Array.from(uniqueShopRemarks);
      }
    }
    
    // 如果仍然没有获取到店铺备注，尝试从当前页数据中获取
    if (shopRemarks.length === 0 && purchaseOrders.value.length > 0) {
      const uniqueShopRemarks = new Set<string>();
      purchaseOrders.value.forEach(order => {
        if (order.shopRemark && order.shopRemark.trim() !== '') {
          uniqueShopRemarks.add(order.shopRemark);
        }
      });
      shopRemarks = Array.from(uniqueShopRemarks);
    }
    
    // 构建文件名: 店铺代号+类型+日期时间
    const shopRemarksStr = shopRemarks.length > 0 
      ? shopRemarks.join('-').slice(0, 20) // 截取前20个字符，避免文件名过长
      : '未知店铺';
      
    const type = queryParams.urgencyType === 1 ? 'JIT' : '备货';
    const dateTimeStr = new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/[\/:]/g, '').replace(/\s+/g, '_'); // 将日期格式化并移除特殊字符
    
    // 文件名加上导出范围说明
    const exportRangeStr = params.exportType === 'current' ? '当前页' : 
                         (params.exportType === 'custom' ? `前${params.pages}页` : '全部');
    
    const fileName = `${shopRemarksStr}-${type}-${dateTimeStr}(${exportRangeStr})`;
    const task = exportTaskStore.createTask(fileName, fileName);
    
    // 计算导出的实际数据量
    let exportPageSize = queryParams.pageSize
    let exportPageCount = params.pages
    
    // 如果是导出当前页
    if (params.exportType === 'current') {
      exportPageCount = 1
    }
    
    // 获取当前排序条件，确保是对象格式
    let sortParam = queryParams.oneDimensionSort;
    if (typeof sortParam === 'string') {
      sortParam = getSortObject(sortParam);
    }
    
    // 修改：使用完整的查询参数 - 直接使用整个queryParams对象
    // 这样可以确保所有查询条件都被传递到后端
    const exportQueryParams = { ...queryParams };
    
    // 确保oneDimensionSort是对象格式
    exportQueryParams.oneDimensionSort = sortParam;
    
    // 确保pageNo和pageSize正确设置
    exportQueryParams.pageNo = queryParams.pageNo;
    exportQueryParams.pageSize = queryParams.pageSize;
    
    // 准备导出参数
    const exportParams = {
      queryParams: exportQueryParams,
      exportConfig: {
        pageSize: exportPageSize,
        pageCount: exportPageCount
      },
      taskId: task.id,
      fileName: fileName,
      exportType: params.exportType
    }
    
    // 调用导出API
    await createExportTask(exportParams, task.id);
  } catch (error) {
    console.error('按条件导出数据失败', error)
    ElMessage.error('按条件导出数据失败')
  }
}

// 创建导出任务并开始轮询进度
const createExportTask = async (exportParams: any, taskId: string) => {
  try {
    const token = getToken();
    
    // 调用后端创建导出任务API
    const response = await axios({
      method: 'post',
      url: '/api/temu/purchaseOrder/export',
      data: exportParams,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
      }
    });
    
    // 检查响应状态
    if (response.data.code !== 200) {
      const exportTaskStore = useExportTaskStore();
      exportTaskStore.failTask(taskId, response.data.message || '创建导出任务失败');
      ElMessage.error(response.data.message || '导出失败');
      return;
    }
    
    // 获取服务端任务ID
    const serverTaskId = response.data.data.taskId;
    if (!serverTaskId) {
      const exportTaskStore = useExportTaskStore();
      exportTaskStore.failTask(taskId, '获取导出任务ID失败');
      ElMessage.error('获取导出任务ID失败');
      return;
    }
    
    ElMessage.success('导出任务已创建，处理中...');
    
    // 开始轮询任务进度
    pollExportProgress(serverTaskId, taskId);
    
  } catch (error: any) {
    console.error('创建导出任务失败:', error);
    const exportTaskStore = useExportTaskStore();
    exportTaskStore.failTask(taskId, error.message || '导出请求失败');
    ElMessage.error('创建导出任务失败: ' + (error.message || '未知错误'));
  }
}

// 轮询导出进度
const pollExportProgress = async (serverTaskId: string, taskId: string) => {
  try {
    const token = getToken();
    const exportTaskStore = useExportTaskStore();
    let completed = false;
    let failedAttempts = 0; // 连续失败计数
    const MAX_FAILED_ATTEMPTS = 3; // 最大连续失败次数
    
    // 轮询函数
    while (!completed && failedAttempts < MAX_FAILED_ATTEMPTS) {
      try {
        // 请求进度
        const progressResponse = await axios({
          method: 'get',
          url: `/api/temu/purchaseOrder/exportProgress/${serverTaskId}`,
          headers: {
            'Authorization': 'Bearer ' + token
          }
        });
        
        // 检查响应状态
        if (progressResponse.data.code !== 200) {
          failedAttempts++;
          await new Promise(resolve => setTimeout(resolve, 1000)); // 失败后等待1秒重试
          continue;
        }
        
        // 重置失败计数
        failedAttempts = 0;
        
        // 获取进度数据
        const progressData = progressResponse.data.data;
        
        // 更新任务列表中的进度
        exportTaskStore.updateTaskProgress(taskId, progressData.progress);
        
        // 检查任务状态
        if (progressData.status === 'completed') {
          // 任务完成，触发下载
          ElMessage.success('导出完成，正在下载文件...');
          
          // 下载文件
          await downloadExcelFile(serverTaskId);
          
          // 标记完成
          completed = true;
          exportTaskStore.completeTask(taskId);
          break;
        } else if (progressData.status === 'failed') {
          // 任务失败
          ElMessage.error('导出失败: ' + (progressData.message || '未知错误'));
          exportTaskStore.failTask(taskId, progressData.message || '导出失败');
          break;
        }
        
        // 等待一段时间后继续轮询
        // 根据进度设置轮询间隔，进度越大，轮询间隔越长
        const pollInterval = Math.min(2000, 500 + Math.floor(progressData.progress / 10) * 300);
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      } catch (error: any) {
        console.error('轮询进度失败:', error);
        failedAttempts++;
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    // 如果连续失败超过最大次数
    if (failedAttempts >= MAX_FAILED_ATTEMPTS) {
      ElMessage.error('无法获取导出进度，请稍后在系统中查看导出结果');
      exportTaskStore.failTask(taskId, '无法获取导出进度');
    }
    
  } catch (error: any) {
    const exportTaskStore = useExportTaskStore();
    ElMessage.error('轮询导出进度失败: ' + (error.message || '未知错误'));
    exportTaskStore.failTask(taskId, error.message || '轮询进度失败');
  }
}

// 下载Excel文件
const downloadExcelFile = async (serverTaskId: string) => {
  try {
    const token = getToken();
    
    ElMessage.success('正在准备下载，请稍候...');
    
    // 创建下载链接
    const downloadUrl = `/api/temu/purchaseOrder/downloadExcel/${serverTaskId}`;
    
    // 使用fetch API发起带认证头的请求
    const response = await fetch(downloadUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    // 检查响应状态
    if (!response.ok) {
      // 尝试解析错误响应
      try {
        const errorData = await response.json();
        ElMessage.error(errorData.message || `下载失败: ${response.status} ${response.statusText}`);
      } catch (parseError) {
        ElMessage.error(`下载失败: ${response.status} ${response.statusText}`);
      }
      return;
    }
    
    // 获取文件名
    let filename = '备货单数据.xlsx'; // 默认文件名
    
    // 尝试从Content-Disposition获取文件名
    const contentDisposition = response.headers.get('Content-Disposition');
    if (contentDisposition) {
      // 匹配filename或filename*=utf-8''格式
      const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
      const filenameUtf8Regex = /filename\*=UTF-8''([^;]*)/i;
      
      // 先尝试获取UTF-8编码的文件名
      const utf8Match = filenameUtf8Regex.exec(contentDisposition);
      if (utf8Match && utf8Match[1]) {
        try {
          filename = decodeURIComponent(utf8Match[1]);
        } catch (e) {
          console.error('解码文件名失败:', e);
        }
      } else {
        // 尝试获取普通文件名
        const match = filenameRegex.exec(contentDisposition);
        if (match && match[1]) {
          let extractedName = match[1].replace(/['"]/g, '');
          
          // 尝试处理可能的URL编码
          try {
            if (extractedName.includes('%')) {
              extractedName = decodeURIComponent(extractedName);
            }
            filename = extractedName;
          } catch (e) {
            console.error('处理文件名失败:', e);
          }
        }
      }
    }
    
    // 确保文件名以.xlsx结尾
    if (!filename.toLowerCase().endsWith('.xlsx')) {
      filename += '.xlsx';
    }
    
    // 将响应转换为blob
    const blob = await response.blob();
    
    // 创建下载链接并触发下载
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    
    // 清理资源
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    
    ElMessage.success('文件下载已开始，请查看浏览器下载管理器');
  } catch (error: any) {
    console.error('下载文件失败:', error);
    ElMessage.error('下载文件失败: ' + (error.message || '未知错误'));
  }
};

// 新增：批量二维码对话框相关变量
const batchQrCodeDialogVisible = ref(false)
const batchQrCodeDataList = ref<any[]>([])
const batchQrCodeUrlList = ref<string[]>([])
const currentQrCodePage = ref(1)

// 新增：处理二维码分页变化
const handleQrCodePageChange = (page: number) => {
  currentQrCodePage.value = page
}

// 获取当前批次的订单数量
const getCurrentBatchOrderCount = () => {
  const currentBatch = batchQrCodeDataList.value[currentQrCodePage.value - 1]
  if (!currentBatch) return 0
  
  try {
    const data = JSON.parse(currentBatch)
    return data.orders?.length || 0
  } catch {
    return 0
  }
}

// 获取当前批次的订单列表
const getCurrentBatchOrders = () => {
  const currentBatch = batchQrCodeDataList.value[currentQrCodePage.value - 1]
  if (!currentBatch) return []
  
  try {
    const data = JSON.parse(currentBatch)
    // 为了显示完整信息，需要从选中行中找到对应的完整数据
    return data.orders.map((order: any) => {
      const fullOrder = selectedRows.value.find(row => row.subPurchaseOrderSn === order.subPurchaseOrderSn)
      return fullOrder || order
    }) as any[] // 显式指定返回类型为 any[]
  } catch {
    return []
  }
}

// 修改：获取所选备货单中不同店铺的数量，添加批次参数
const getSelectedShopCount = (batchIndex?: number): number => {
  if (!selectedRows.value || selectedRows.value.length === 0) return 0;
  
  // 如果指定了批次，则只计算该批次的不同店铺数量
  if (batchIndex !== undefined && batchQrCodeDataList.value[batchIndex]) {
    try {
      const batchData = JSON.parse(batchQrCodeDataList.value[batchIndex])
      const shopIdSet = new Set(batchData.orders.map((order: any) => order.shopId))
      return shopIdSet.size
    } catch {
      return 0
    }
  }
  
  // 默认计算所有选中行的不同店铺数量
  const shopIdSet = new Set(selectedRows.value.map(row => row.shopId))
  return shopIdSet.size
}

// 修改：处理批量生成二维码，实现分批处理
const handleBatchGenerateQrCode = async () => {
  if (!selectedRows.value || selectedRows.value.length === 0) {
    ElMessage.warning('请至少选择一项生成二维码')
    return
  }

  try {
    // 重置状态
    batchQrCodeDataList.value = []
    batchQrCodeUrlList.value = []
    currentQrCodePage.value = 1
    
    // 将选中的行按每批10个进行分组
    const batchSize = 10
    const totalRows = selectedRows.value.length
    const batchCount = Math.ceil(totalRows / batchSize)
    
    // 为每批生成二维码数据和图像
    for (let i = 0; i < batchCount; i++) {
      // 获取当前批次的订单
      const batchStart = i * batchSize
      const batchEnd = Math.min((i + 1) * batchSize, totalRows)
      const batchRows = selectedRows.value.slice(batchStart, batchEnd)
      
      // 创建当前批次的二维码数据
      const qrCodeData = {
        orders: batchRows.map(row => ({
          subPurchaseOrderSn: row.subPurchaseOrderSn,
          shopId: row.shopId
        })),
        timestamp: new Date().getTime(),
        type: 'batch',
        batchIndex: i + 1,
        batchCount: batchCount
      }
      
      // 将数据转换为JSON字符串
      const qrCodeDataString = JSON.stringify(qrCodeData)
      batchQrCodeDataList.value.push(qrCodeDataString)
      
      // 使用QRCode库生成二维码
      const dataUrl = await QRCode.toDataURL(qrCodeDataString, {
        width: 200,
        margin: 1,
        errorCorrectionLevel: 'M'
      })
      
      batchQrCodeUrlList.value.push(dataUrl)
    }
    
    // 显示对话框
    batchQrCodeDialogVisible.value = true
  } catch (error) {
    console.error('生成批量二维码失败:', error)
    ElMessage.error('生成批量二维码失败')
    batchQrCodeDataList.value = []
    batchQrCodeUrlList.value = []
  }
}

// 修改：打印当前查看的二维码
const handlePrintCurrentQrCode = () => {
  const currentUrl = batchQrCodeUrlList.value[currentQrCodePage.value - 1]
  if (!currentUrl) return
  
  // 获取当前批次的订单数量和店铺数量
  const orderCount = getCurrentBatchOrderCount()
  const shopCount = getSelectedShopCount(currentQrCodePage.value - 1)
  const shopCountText = shopCount > 1 ? `<p style="color: #E6A23C; font-weight: bold;">涉及 ${shopCount} 个不同店铺</p>` : ''
  
  // 获取当前批次的订单号列表
  const orders = getCurrentBatchOrders()
  const orderSnList = orders.map(order => order.subPurchaseOrderSn).join(', ')
  
  // 创建一个隐藏的iframe用于打印
  const printFrame = document.createElement('iframe')
  printFrame.style.position = 'absolute'
  printFrame.style.top = '-999px'
  printFrame.style.left = '-999px'
  printFrame.style.width = '0'
  printFrame.style.height = '0'
  document.body.appendChild(printFrame)
  
  // 设置打印内容
  const frameDoc = printFrame.contentWindow?.document
  if (frameDoc) {
    frameDoc.open()
    frameDoc.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>批量备货单二维码 (${currentQrCodePage.value}/${batchQrCodeUrlList.value.length})</title>
        <style>
          body {
            text-align: center;
            padding: 20px;
            font-family: Arial, sans-serif;
          }
          .qr-container {
            margin: 0 auto;
            max-width: 300px;
          }
          .qr-image {
            width: 200px;
            height: 200px;
            margin: 10px auto;
          }
          .qr-info {
            margin-top: 10px;
            font-size: 14px;
          }
          .selected-orders {
            margin-top: 5px;
            font-size: 12px;
            color: #666;
            max-width: 280px;
            margin: 5px auto;
            word-wrap: break-word;
          }
          .batch-info {
            margin-top: 5px;
            font-size: 12px;
            color: #409EFF;
          }
        </style>
      </head>
      <body>
        <div class="qr-container">
          <h3>批量备货单二维码</h3>
          <p class="batch-info">第 ${currentQrCodePage.value} / ${batchQrCodeUrlList.value.length} 张二维码</p>
          <img src="${currentUrl}" class="qr-image" alt="批量备货单二维码" />
          <div class="qr-info">
            <p>包含 ${orderCount} 个备货单</p>
            ${shopCountText}
            <div class="selected-orders">
              ${orderSnList}
            </div>
            <p>扫描此二维码可批量更新所选备货单的生产进度</p>
          </div>
        </div>
      </body>
      </html>
    `)
    frameDoc.close()
    
    // 等待图片加载完成后打印
    setTimeout(() => {
      printFrame.contentWindow?.print()
      // 延迟移除iframe
      setTimeout(() => {
        document.body.removeChild(printFrame)
        ElMessage.success('二维码打印成功')
      }, 500)
    }, 500)
  }
}

// 修改：下载当前查看的二维码
const handleDownloadCurrentQrCode = () => {
  const currentUrl = batchQrCodeUrlList.value[currentQrCodePage.value - 1]
  if (!currentUrl) return
  
  const link = document.createElement('a')
  link.href = currentUrl
  link.download = `批量备货单二维码_第${currentQrCodePage.value}批_${new Date().getTime()}.png`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  ElMessage.success('二维码已下载')
}

// 新增：下载所有二维码为zip文件
const handleDownloadAllQrCodes = async () => {
  try {
    // 此处需要导入JSZip库，如果项目中没有，请先安装
    const JSZip = (await import('jszip')).default
    const zip = new JSZip()
    const timestamp = new Date().getTime()
    
    // 将所有二维码添加到zip文件中
    for (let i = 0; i < batchQrCodeUrlList.value.length; i++) {
      const url = batchQrCodeUrlList.value[i]
      // 将dataURL转换为二进制数据
      const binary = atob(url.split(',')[1])
      // 使用Array.from确保正确的类型推导
      const array = Array.from(binary, char => char.charCodeAt(0))
      const blob = new Uint8Array(array)
      
      // 添加到zip文件
      zip.file(`批量备货单二维码_第${i + 1}批_${timestamp}.png`, blob)
    }
    
    // 生成并下载zip文件
    const content = await zip.generateAsync({ type: 'blob' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(content)
    link.download = `批量备货单二维码_全部_${timestamp}.zip`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(link.href)
    
    ElMessage.success('所有二维码已打包下载')
  } catch (error) {
    console.error('打包下载二维码失败:', error)
    ElMessage.error('打包下载二维码失败，请尝试单个下载')
  }
}

// 商品标签相关变量
const productLabelDialogVisible = ref(false)
const productLabelData = ref<any>(null)
const productLabelLoading = ref(false)
const productLabelError = ref('')

// 处理批量打印商品条码
const handleBatchPrintProductLabels = async () => {
  if (!selectedRows.value || selectedRows.value.length === 0) {
    ElMessage.warning('请先选择需要打印条码的备货单')
    return
  }

  // 显示加载中
  productLabelDialogVisible.value = true
  productLabelLoading.value = true
  productLabelData.value = null
  productLabelError.value = ''

  try {
    // 按店铺分组商品SKU ID
    const shopProductMap: Record<number, string[]> = {}

    // 遍历选中的行，提取商品SKU ID并按店铺分组
    selectedRows.value.forEach(row => {
      const shopId = row.shopId
      
      // 尝试从不同位置获取productSkuId
      let productSkuId = null
      if (row.productSkuId) {
        productSkuId = row.productSkuId
      } else if (row.skuDetail && row.skuDetail.productSkuId) {
        productSkuId = row.skuDetail.productSkuId
      } else if (row.skuQuantityDetailList && row.skuQuantityDetailList.length > 0) {
        // 尝试从skuQuantityDetailList中获取
        const skuDetail = row.skuQuantityDetailList[0]
        if (skuDetail && skuDetail.productSkuId) {
          productSkuId = skuDetail.productSkuId
        }
      }

      console.log('行数据:', row)
      console.log('提取的shopId:', shopId, '提取的productSkuId:', productSkuId)

      if (shopId && productSkuId) {
        if (!shopProductMap[shopId]) {
          shopProductMap[shopId] = []
        }
        
        // 避免重复添加相同的SKU ID
        if (!shopProductMap[shopId].includes(productSkuId)) {
          shopProductMap[shopId].push(String(productSkuId))
        }
      }
    })

    // 检查是否有有效的商品SKU ID
    const hasValidProducts = Object.values(shopProductMap).some(products => products.length > 0)
    
    if (!hasValidProducts) {
      productLabelError.value = '未找到有效的商品SKU ID，无法获取条码数据'
      productLabelLoading.value = false
      return
    }

    console.log('发送请求数据:', JSON.stringify(shopProductMap, null, 2))

    // 调用API获取商品标签数据
    const response = await batchGetProductLabels({
      shopProductMap
    })

    console.log('API返回状态:', response?.status, response?.statusText)
    console.log('API返回数据:', response?.data)

    // 修改判断逻辑，只要返回的数据存在且success为true就认为成功
    if (response && response.data) {
      const apiData = response.data.data || response.data;
      
      if (apiData && (apiData.success === true || response.data.code === 200)) {
        // 直接使用返回的数据
        productLabelData.value = apiData;
        console.log('设置标签数据:', productLabelData.value);
        
        // 添加详细的数据结构日志
        console.log('数据结构分析:', {
          success: productLabelData.value.success,
          shopId: productLabelData.value.shopId,
          shopName: productLabelData.value.shopName,
          hasShopResultMap: !!productLabelData.value.shopResultMap,
          hasData: !!productLabelData.value.data && Array.isArray(productLabelData.value.data),
          dataLength: productLabelData.value.data ? productLabelData.value.data.length : 0,
          numericKeys: Object.keys(productLabelData.value).filter(key => /^\d+$/.test(key))
        });
        
        // 不需要额外检查数据结构，ProductLabelPreview组件已经能处理多种数据格式
      } else {
        productLabelError.value = apiData?.errorMsg || response?.data?.msg || '获取商品标签数据失败';
        console.error('API返回错误:', response?.data);
      }
    } else {
      productLabelError.value = '获取商品标签数据失败，响应为空';
      console.error('API响应为空');
    }
  } catch (error: any) {
    console.error('获取商品标签数据失败', error)
    productLabelError.value = error.message || '获取商品标签数据失败'
  } finally {
    productLabelLoading.value = false
  }
}

// 修改处理打印所有标签的函数
const handlePrintAllLabels = (data: any) => {
  if (!data || !data.labels || data.labels.length === 0) {
    ElMessage.warning('没有可打印的标签数据')
    return
  }
  
  try {
    console.log('打印标签完成:', data)
    
    // 如果有PDF文件名，显示成功消息
    if (data.pdfFile) {
      ElMessage.success(`成功生成标签PDF文件: ${data.pdfFile}`)
    } else {
      ElMessage.success(`成功处理${data.labels.length}个商品标签`)
    }
    
    // 关闭标签预览对话框
    productLabelDialogVisible.value = false
  } catch (error) {
    console.error('处理标签打印结果失败:', error)
    ElMessage.error('处理标签打印结果失败')
  }
}

// 处理下载单个标签
const handleDownloadLabel = (label: any) => {
  if (label && label.labelUrl) {
    try {
      console.log('下载标签:', label)
      
      // 创建一个临时的a标签用于下载
      const a = document.createElement('a')
      a.href = label.labelUrl
      a.download = `商品标签_${label.productSkuId || '未知'}.png`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      
      ElMessage.success('标签下载已开始')
    } catch (error) {
      console.error('下载标签失败:', error)
      ElMessage.error('下载标签失败，请重试')
    }
  } else {
    ElMessage.error('标签图片不存在，无法下载')
  }
}

// 处理重试获取标签数据
const handleRetryGetLabels = () => {
  console.log('重试获取标签数据')
  
  // 重置错误状态
  productLabelError.value = ''
  productLabelLoading.value = true
  
  // 如果有已选择的行，重新调用获取标签数据的方法
  if (selectedRows.value && selectedRows.value.length > 0) {
    handleBatchPrintProductLabels()
  } else {
    ElMessage.warning('请先选择需要打印条码的备货单')
    productLabelLoading.value = false
  }
}
</script>

<style scoped>
.urgent-purchase-tab {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0; /* 确保组件之间没有间隙 */
  width: 100%;
  min-height: auto;
  height: auto;
  overflow: visible;
}

/* 添加表格组件样式，需要在PurchaseOrderTable组件中手动添加 */
:deep(.table-card) {
  margin-top: -1px;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 0;
  border: 1px solid #e4e7ed;
  border-top: none;
}

/* 确保表格内部边框样式统一 */
:deep(.table-card .el-table) {
  border: none !important;
}

:deep(.table-card .el-table::before) {
  display: none;
}

/* 批量二维码对话框样式 */
.batch-qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
}

.qrcode-tip {
  width: 100%;
  margin-bottom: 15px;
}

.qrcode-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 15px;
}

.qrcode-pagination-text {
  margin-left: 10px;
  color: #606266;
  font-size: 14px;
}

.qrcode-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.qrcode-large {
  width: 200px;
  height: 200px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.qrcode-info {
  margin: 15px 0;
  text-align: center;
  width: 100%;
}

.qrcode-info p {
  margin: 5px 0;
  color: #606266;
}

.order-sns {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  max-height: 80px;
  overflow-y: auto;
  margin: 8px 0;
  padding: 5px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.order-sn-item {
  margin: 2px 3px;
  font-size: 12px;
  color: #409EFF;
}

.warning-text {
  color: #E6A23C;
  font-weight: bold;
}

.qrcode-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
}

.qrcode-empty {
  padding: 30px 0;
}
</style>

<!-- 全局样式 -->
<style>
/* 新增 Tooltip 样式 */
.sku-lack-tooltip .el-tooltip__popper {
  max-width: 400px; /* 调整最大宽度 */
  padding: 10px;
}

.sku-lack-tooltip-content .tooltip-title {
  font-size: 13px;
  margin-bottom: 8px;
  color: #606266;
}

.sku-lack-tooltip-content .tooltip-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.sku-lack-tooltip-content .tooltip-table th,
.sku-lack-tooltip-content .tooltip-table td {
  border: 1px solid #ebeef5;
  padding: 6px 8px;
  text-align: center;
}

.sku-lack-tooltip-content .tooltip-table th {
  background-color: #f5f7fa;
  color: #909399;
  font-weight: 500;
}

.sku-lack-tooltip-content .tooltip-table td {
  color: #606266;
}

.sku-lack-tooltip-content .tooltip-table td.is-lack {
  color: #F56C6C; /* 缺货时显示红色 */
}
</style>