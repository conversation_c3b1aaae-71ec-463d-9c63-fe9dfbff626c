package com.xiao.temu.modules.production.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.production.dto.ProductionGroupMemberDTO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 生产组成员服务接口
 */
public interface ProductionGroupMemberService {

    /**
     * 分页查询生产组成员列表
     *
     * @param groupId 生产组ID
     * @param page 分页参数
     * @param keyword 搜索关键词
     * @return 成员分页列表
     */
    IPage<ProductionGroupMemberDTO> getMemberList(Long groupId, Page<ProductionGroupMemberDTO> page, String keyword);

    /**
     * 查询生产组成员列表（不分页）
     *
     * @param groupId 生产组ID
     * @return 成员列表
     */
    List<ProductionGroupMemberDTO> getAllMembers(Long groupId);

    /**
     * 添加生产组成员
     *
     * @param groupId 生产组ID
     * @param userId 用户ID
     * @return 结果
     */
    int addMember(Long groupId, Long userId);

    /**
     * 批量添加生产组成员
     *
     * @param groupId 生产组ID
     * @param userIds 用户ID列表
     * @return 结果
     */
    int batchAddMembers(Long groupId, List<Long> userIds);

    /**
     * 删除生产组成员
     *
     * @param groupId 生产组ID
     * @param userId 用户ID
     * @return 结果
     */
    int removeMember(Long groupId, Long userId);

    /**
     * 删除生产组的所有成员
     *
     * @param groupId 生产组ID
     * @return 结果
     */
    int removeAllMembers(Long groupId);

    /**
     * 检查用户是否为生产组成员
     *
     * @param groupId 生产组ID
     * @param userId 用户ID
     * @return 结果 true-是 false-否
     */
    boolean checkUserInGroup(Long groupId, Long userId);

    /**
     * 统计生产组成员数量
     *
     * @param groupId 生产组ID
     * @return 成员数量
     */
    int countGroupMembers(Long groupId);
    
    /**
     * 根据用户ID查询所属的生产组ID列表
     * 
     * @param userId 用户ID
     * @return 生产组ID列表
     */
    List<Long> getGroupIdsByUserId(Long userId);

    /**
     * 获取可添加到生产组的用户列表（具有生产角色的用户）
     *
     * @param groupId 生产组ID
     * @param page 分页参数
     * @param keyword 搜索关键词
     * @return 用户分页列表
     */
    IPage<com.xiao.temu.modules.system.entity.SysUser> getAvailableUsers(Long groupId, Page<com.xiao.temu.modules.system.entity.SysUser> page, String keyword);
} 