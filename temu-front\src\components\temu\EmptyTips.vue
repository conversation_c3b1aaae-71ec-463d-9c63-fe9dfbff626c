<template>
  <div class="empty-tips">
    <el-icon><component :is="icon" /></el-icon>
    <span>{{ message }}</span>
  </div>
</template>

<script setup>
import { InfoFilled } from '@element-plus/icons-vue';

const props = defineProps({
  message: {
    type: String,
    default: '请选择店铺后点击搜索按钮查询数据'
  },
  icon: {
    type: String,
    default: 'InfoFilled'
  }
});
</script>

<style scoped>
.empty-tips {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #909399;
}

.empty-tips .el-icon {
  font-size: 24px;
  margin-bottom: 12px;
  color: #e6a23c;
}

.empty-tips span {
  font-size: 14px;
}
</style> 