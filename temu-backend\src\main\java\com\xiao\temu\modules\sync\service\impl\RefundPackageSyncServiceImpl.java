package com.xiao.temu.modules.sync.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.modules.refund.entity.RefundPackageDetail;
import com.xiao.temu.modules.sync.entity.RefundPackageSyncTask;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.sync.vo.ApiResponse;
import com.xiao.temu.modules.refund.mapper.RefundPackageDetailMapper;
import com.xiao.temu.modules.sync.mapper.RefundPackageSyncTaskMapper;
import com.xiao.temu.modules.sync.service.ProductSyncService;
import com.xiao.temu.modules.sync.service.RefundPackageSyncService;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.infrastructure.api.TemuApiClient;
import lombok.extern.slf4j.Slf4j;
import org.ehcache.shadow.org.terracotta.statistics.Time;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 退货包裹数据同步服务实现
 */
@Slf4j
@Service
public class RefundPackageSyncServiceImpl extends ServiceImpl<RefundPackageSyncTaskMapper, RefundPackageSyncTask> implements RefundPackageSyncService {

    @Autowired
    private ShopService shopService;
    
    @Autowired
    private RefundPackageDetailMapper refundPackageDetailMapper;
    
    @Autowired
    private ProductSyncService productSyncService;
    
    @Value("${temu.sync.auto-sync-product:true}")
    private boolean autoSyncProduct;
    
    /**
     * 默认的页大小
     */
    private static final int DEFAULT_PAGE_SIZE = 50;
    
    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_COUNT = 3;

    /**
     * 同步指定店铺的退货包裹数据
     *
     * @param shopId 店铺ID
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse.RefundPackageSyncVO syncRefundPackageData(Long shopId) {
        ApiResponse.RefundPackageSyncVO result = new ApiResponse.RefundPackageSyncVO();
        result.setShopId(shopId);
        
        // 检查店铺是否存在
        Shop shop = shopService.getShopById(shopId).convertToShop();
        if (shop == null) {
            result.setFailed("店铺不存在", 404);
            return result;
        }
        result.setShopName(shop.getShopName());
        
        // 获取或创建同步任务
        RefundPackageSyncTask task = getSyncTaskByShopId(shopId);
        if (task == null) {
            boolean initResult = initSyncTask(shopId);
            if (!initResult) {
                result.setFailed("初始化同步任务失败", 500);
                return result;
            }
            task = getSyncTaskByShopId(shopId);
        }
        
        // 如果任务状态为"同步中"，则返回此状态
        if (task.getSyncStatus() == 1) {
            result.setProcessing();
            result.setTaskId(task.getId());
            return result;
        }
        
        // 更新任务状态为"同步中"
        task.setSyncStatus(1);
        task.setLastSyncTime(LocalDateTime.now());
        updateById(task);
        
        result.setTaskId(task.getId());
        result.setProcessing();
        
        try {
            // 设置API请求参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.refund.returnpackagelist.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());
            
            // 确定同步时间范围
            // 默认查询最近2个月的数据，但由于平台接口限制，每次查询时间间隔不能超过30天
            // 因此需要分两次查询，每次查询一个月的数据
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime;
            
            // 如果已有同步记录，则从上次同步的最新数据时间开始
            Long latestOutboundTime = refundPackageDetailMapper.getLatestOutboundTime(shopId);
            if (latestOutboundTime != null) {
                // 转换时间戳为LocalDateTime并减去1小时（为了避免漏数据）
                LocalDateTime latestTime = LocalDateTime.ofEpochSecond(latestOutboundTime / 1000, 0, ZoneOffset.UTC);
                startTime = latestTime.minus(1, ChronoUnit.HOURS);
            } else {
                // 如果没有同步记录，则默认同步最近两个月的数据，分两次进行
                // 第一次同步：最近一个月的数据
                // 注意：使用25天作为安全的时间区间，确保不会超过平台31天的限制
                startTime = endTime.minus(25, ChronoUnit.DAYS);
                
                // 记录本次同步的起止时间
                task.setLastSyncStartTime(startTime);
                task.setLastSyncEndTime(endTime);
                
                // 转换为毫秒时间戳
                String outboundTimeStart = String.valueOf(startTime.toInstant(ZoneOffset.UTC).toEpochMilli());
                String outboundTimeEnd = String.valueOf(endTime.toInstant(ZoneOffset.UTC).toEpochMilli());
                
                // 记录第一次同步的时间区间信息
                log.info("开始第一次同步退货包裹数据(无历史数据)，店铺ID={}，时间区间：{} 至 {}，间隔天数={}", 
                        shopId, startTime, endTime, ChronoUnit.DAYS.between(startTime, endTime));
                
                // 同步第一个月的数据
                AtomicInteger totalCount = new AtomicInteger(0);
                AtomicInteger successCount = new AtomicInteger(0);
                
                // 执行第一个月数据同步
                syncDateRangeData(commonParams, outboundTimeStart, outboundTimeEnd, shopId, task, totalCount, successCount);
                
                // 更新结果中已同步的数据量
                result.setSyncedCount(successCount.get());
                
                // 第二次同步：前一个月的数据
                endTime = startTime;
                // 使用25天作为安全的时间区间
                startTime = endTime.minus(25, ChronoUnit.DAYS);
            }
            
            // 记录本次同步的起止时间
            task.setLastSyncStartTime(startTime);
            task.setLastSyncEndTime(endTime);
            
            // 转换为毫秒时间戳
            String outboundTimeStart = String.valueOf(startTime.toInstant(ZoneOffset.UTC).toEpochMilli());
            String outboundTimeEnd = String.valueOf(endTime.toInstant(ZoneOffset.UTC).toEpochMilli());
            
            // 记录同步的时间区间信息
            log.info("开始同步退货包裹数据，店铺ID={}，时间区间：{} 至 {}，间隔天数={}", 
                    shopId, startTime, endTime, ChronoUnit.DAYS.between(startTime, endTime));
            
            // 同步退货包裹数据
            AtomicInteger totalCount = new AtomicInteger(0);
            AtomicInteger successCount = new AtomicInteger(0);
            
            // 执行同步
            syncDateRangeData(commonParams, outboundTimeStart, outboundTimeEnd, shopId, task, totalCount, successCount);
            
            // 如果是第二次同步（已有同步记录的情况下），直接返回结果
            if (latestOutboundTime != null) {
                task.setSyncStatus(2); // 同步成功
                task.setLastUpdateTime(LocalDateTime.now());
                // 获取实际的退货包裹记录总数
                Integer packageCount = getRefundPackageCountByShopId(shopId);
                task.setTotalRecords(packageCount != null ? packageCount : 0);
                updateById(task);
                
                result.setSuccess(true);
                result.setSyncStatus(2);
                result.setTotalRecords(packageCount);
                result.setSuccessRecords(successCount.get());
                
                log.info("同步退货包裹数据成功，店铺ID={}，总数={}，成功数={}", shopId, packageCount, successCount.get());
                return result;
            } else {
                // 如果是第一次同步（两次查询），设置实际的退货包裹记录总数
                Integer packageCount = getRefundPackageCountByShopId(shopId);
                
                task.setSyncStatus(2); // 同步成功
                task.setLastUpdateTime(LocalDateTime.now());
                task.setTotalRecords(packageCount != null ? packageCount : 0);
                updateById(task);
                
                result.setSuccess(true);
                result.setSyncStatus(2);
                result.setTotalRecords(packageCount);
                result.setSuccessRecords(successCount.get() + (result.getSyncedCount() != null ? result.getSyncedCount() : 0));
                
                log.info("同步退货包裹数据成功，店铺ID={}，总数={}，成功数={}", shopId, packageCount, result.getSuccessRecords());
                return result;
            }
            
        } catch (Exception e) {
            log.error("同步退货包裹数据异常，店铺ID=" + shopId, e);
            
            // 更新任务状态为失败
            task.setSyncStatus(3); // 同步失败
            task.setErrorMessage(e.getMessage());
            updateById(task);
            
            result.setFailed(e.getMessage(), 500);
            return result;
        }
    }

    /**
     * 同步指定时间范围内的退货包裹数据
     *
     * @param commonParams API公共参数
     * @param outboundTimeStart 开始时间（毫秒时间戳）
     * @param outboundTimeEnd 结束时间（毫秒时间戳）
     * @param shopId 店铺ID
     * @param task 同步任务
     * @param totalCount 总记录数计数器
     * @param successCount 成功记录数计数器
     * @throws Exception 同步异常
     */
    private void syncDateRangeData(CommonParams commonParams, String outboundTimeStart, String outboundTimeEnd,
                                 Long shopId, RefundPackageSyncTask task, AtomicInteger totalCount, AtomicInteger successCount) throws Exception {
        // 第一页，获取总数
        Map<String, Object> businessParams = new HashMap<>();
        businessParams.put("outboundTimeEnd", outboundTimeEnd);
        businessParams.put("outboundTimeStart", outboundTimeStart);
        businessParams.put("pageNo", "1");
        businessParams.put("pageSize", String.valueOf(DEFAULT_PAGE_SIZE));
        businessParams.put("timestamp", String.valueOf(Time.time()));
        
        JSONObject response = TemuApiClient.sendRequest(commonParams, businessParams);
        if (!response.getBooleanValue("success")) {
            String errorMsg = response.getString("errorMsg");
            Integer errorCode = response.getInteger("errorCode");
            
            // 特殊处理时间区间超过限制的错误
            if (errorMsg != null && errorMsg.contains("Participation time exceeds 31 day")) {
                log.warn("同步退货包裹数据时间区间超过平台限制，店铺ID={}，尝试缩短时间区间重试", shopId);
                
                // 计算缩短后的时间区间 - 使用20天作为安全值
                long startTimeMs = Long.parseLong(outboundTimeStart);
                long endTimeMs = Long.parseLong(outboundTimeEnd);
                LocalDateTime startTimeDt = LocalDateTime.ofInstant(java.time.Instant.ofEpochMilli(startTimeMs), ZoneOffset.UTC);
                LocalDateTime endTimeDt = LocalDateTime.ofInstant(java.time.Instant.ofEpochMilli(endTimeMs), ZoneOffset.UTC);
                
                // 如果时间区间超过20天，则缩短到20天
                if (ChronoUnit.DAYS.between(startTimeDt, endTimeDt) > 20) {
                    LocalDateTime newStartTimeDt = endTimeDt.minus(20, ChronoUnit.DAYS);
                    String newOutboundTimeStart = String.valueOf(newStartTimeDt.toInstant(ZoneOffset.UTC).toEpochMilli());
                    
                    // 更新业务参数
                    businessParams.put("outboundTimeStart", newOutboundTimeStart);
                    
                    // 重试请求
                    log.info("使用缩短后的时间区间重试同步，店铺ID={}, 新的开始时间={}", shopId, newStartTimeDt);
                    response = TemuApiClient.sendRequest(commonParams, businessParams);
                    
                    // 如果依然失败，则抛出异常
                    if (!response.getBooleanValue("success")) {
                        errorMsg = response.getString("errorMsg");
                        errorCode = response.getInteger("errorCode");
                        log.error("使用缩短时间区间重试同步仍然失败，店铺ID={}，错误码={}，错误信息={}", shopId, errorCode, errorMsg);
                        task.setSyncStatus(3); // 同步失败
                        task.setErrorMessage(errorMsg);
                        updateById(task);
                        throw new Exception(errorMsg);
                    }
                } else {
                    // 时间区间已经很短，但仍然报错，记录错误并抛出异常
                    log.error("同步退货包裹数据失败，店铺ID={}，错误码={}，错误信息={}，时间区间已经很短但仍然失败", 
                            shopId, errorCode, errorMsg);
                    task.setSyncStatus(3); // 同步失败
                    task.setErrorMessage(errorMsg);
                    updateById(task);
                    throw new Exception(errorMsg);
                }
            } else {
                // 其他类型的错误，记录并抛出异常
                log.error("同步退货包裹数据失败，店铺ID={}，错误码={}，错误信息={}", shopId, errorCode, errorMsg);
                task.setSyncStatus(3); // 同步失败
                task.setErrorMessage(errorMsg);
                updateById(task);
                throw new Exception(errorMsg);
            }
        }
        
        JSONObject resultObj = response.getJSONObject("result");
        int total = resultObj.getIntValue("total");
        totalCount.set(total);
        
        // 如果总数为0，直接返回
        if (total == 0) {
            return;
        }
        
        // 处理第一页数据
        processPageData(resultObj, shopId, successCount);
        
        // 计算总页数
        int totalPages = (total + DEFAULT_PAGE_SIZE - 1) / DEFAULT_PAGE_SIZE;
        
        // 如果有多页，处理后续页面
        for (int pageNo = 2; pageNo <= totalPages; pageNo++) {
            businessParams.put("pageNo", String.valueOf(pageNo));
            
            boolean success = false;
            int retryCount = 0;
            JSONObject pageResponse = null;
            
            // 重试逻辑
            while (!success && retryCount < MAX_RETRY_COUNT) {
                try {
                    pageResponse = TemuApiClient.sendRequest(commonParams, businessParams);
                    success = pageResponse.getBooleanValue("success");
                    if (!success) {
                        retryCount++;
                        log.warn("同步退货包裹数据第{}页失败，店铺ID={}，重试次数={}，错误信息={}", 
                                pageNo, shopId, retryCount, pageResponse.getString("errorMsg"));
                        Thread.sleep(1000); // 等待1秒后重试
                    }
                } catch (Exception e) {
                    retryCount++;
                    log.warn("同步退货包裹数据第{}页异常，店铺ID={}，重试次数={}，异常信息={}", 
                            pageNo, shopId, retryCount, e.getMessage());
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            
            if (!success) {
                log.error("同步退货包裹数据第{}页失败，店铺ID={}，重试次数达到上限", pageNo, shopId);
                continue; // 继续处理下一页
            }
            
            // 处理该页数据
            JSONObject pageResultObj = pageResponse.getJSONObject("result");
            processPageData(pageResultObj, shopId, successCount);
        }
    }

    /**
     * 处理一页退货包裹数据
     *
     * @param resultObj 结果对象
     * @param shopId 店铺ID
     * @param successCount 成功计数器
     */
    private void processPageData(JSONObject resultObj, Long shopId, AtomicInteger successCount) {
        if (resultObj == null) {
            return;
        }
        
        JSONArray packageList = resultObj.getJSONArray("packageDetailDTOList");
        if (packageList == null || packageList.isEmpty()) {
            return;
        }
        
        List<RefundPackageDetail> details = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        // 收集所有商品的SKC ID，用于后续商品同步
        Set<Long> skcIds = new HashSet<>();
        
        for (int i = 0; i < packageList.size(); i++) {
            JSONObject item = packageList.getJSONObject(i);
            
            RefundPackageDetail detail = new RefundPackageDetail();
            detail.setShopId(shopId);
            detail.setPackageSn(item.getString("packageSn"));
            detail.setProductSkuId(item.getLong("productSkuId"));
            detail.setProductSkcId(item.getLong("productSkcId"));
            detail.setProductSpuId(item.getLong("productSpuId"));
            detail.setPurchaseSubOrderSn(item.getString("purchaseSubOrderSn"));
            detail.setQuantity(item.getInteger("quantity"));
            detail.setMainSaleSpec(item.getString("mainSaleSpec"));
            detail.setSecondarySaleSpec(item.getString("secondarySaleSpec"));
            detail.setThumbUrl(item.getString("thumbUrl"));
            detail.setOrderTypeDesc(item.getString("orderTypeDesc"));
            detail.setRemark(item.getString("remark"));
            detail.setOutboundTime(item.getLong("outboundTime"));
            
            // 处理退货原因数组
            JSONArray reasonDesc = item.getJSONArray("reasonDesc");
            if (reasonDesc != null && !reasonDesc.isEmpty()) {
                detail.setReasonDesc(reasonDesc.toJSONString());
            }
            
            detail.setSyncTime(now);
            details.add(detail);
            
            // 收集商品SKC ID
            Long skcId = detail.getProductSkcId();
            if (skcId != null) {
                skcIds.add(skcId);
            }
        }
        
        if (!details.isEmpty()) {
            try {
                int insertCount = refundPackageDetailMapper.batchInsert(details);
                successCount.addAndGet(insertCount);
                
                // 如果启用了自动同步商品，且有SKC ID列表，则同步商品数据
                if (autoSyncProduct && !skcIds.isEmpty()) {
                    try {
                        log.info("开始同步退货包裹关联商品数据 - 店铺ID: {}, SKC ID数量: {}", shopId, skcIds.size());
                        productSyncService.syncProductsBySkcIds(shopId, skcIds);
                    } catch (Exception e) {
                        log.error("同步退货包裹关联商品数据异常 - 店铺ID: {}", shopId, e);
                    }
                }
            } catch (Exception e) {
                log.error("批量插入退货包裹明细失败，店铺ID=" + shopId, e);
            }
        }
    }

    /**
     * 初始化店铺的同步任务
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    @Override
    public boolean initSyncTask(Long shopId) {
        try {
            // 检查是否已存在同步任务
            RefundPackageSyncTask existTask = getSyncTaskByShopId(shopId);
            if (existTask != null) {
                // 如果任务已存在，则更新退货包裹记录总数
                Integer packageCount = getRefundPackageCountByShopId(shopId);
                existTask.setTotalRecords(packageCount != null ? packageCount : 0);
                updateById(existTask);
                return true;
            }
            
            // 创建新的同步任务
            RefundPackageSyncTask task = new RefundPackageSyncTask();
            task.setShopId(shopId);
            task.setSyncStatus(0); // 未同步
            // 获取当前店铺的退货包裹记录总数
            Integer packageCount = getRefundPackageCountByShopId(shopId);
            task.setTotalRecords(packageCount != null ? packageCount : 0);
            task.setCreateTime(LocalDateTime.now());
            task.setUpdateTime(LocalDateTime.now());
            
            return save(task);
        } catch (Exception e) {
            log.error("初始化退货包裹同步任务失败，店铺ID: {}", shopId, e);
            return false;
        }
    }

    /**
     * 获取同步任务列表
     *
     * @param shopIds 店铺ID列表，为空则查询所有
     * @return 同步任务列表
     */
    @Override
    public List<RefundPackageSyncTask> getSyncTasks(List<Long> shopIds) {
        if (shopIds == null || shopIds.isEmpty()) {
            return list();
        }
        
        LambdaQueryWrapper<RefundPackageSyncTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(RefundPackageSyncTask::getShopId, shopIds);
        
        return list(wrapper);
    }

    /**
     * 根据店铺ID获取同步任务
     *
     * @param shopId 店铺ID
     * @return 同步任务
     */
    @Override
    public RefundPackageSyncTask getSyncTaskByShopId(Long shopId) {
        LambdaQueryWrapper<RefundPackageSyncTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RefundPackageSyncTask::getShopId, shopId);
        
        return getOne(wrapper);
    }

    /**
     * 执行定时同步
     *
     * @return 同步结果
     */
    @Override
    public String executeScheduledSync() {
        log.info("开始执行退货包裹数据定时同步任务");
        
        // 获取所有店铺
        List<Shop> shops = shopService.listAllShops();
        if (shops == null || shops.isEmpty()) {
            log.info("没有可同步的店铺");
            return "没有可同步的店铺";
        }
        
        int total = shops.size();
        int success = 0;
        int failed = 0;
        
        for (Shop shop : shops) {
            try {
                ApiResponse.RefundPackageSyncVO result = syncRefundPackageData(shop.getShopId());
                if (result.getSuccess()) {
                    success++;
                } else {
                    failed++;
                    log.error("定时同步退货包裹数据失败，店铺ID={}，错误信息={}", shop.getShopId(), result.getErrorMsg());
                }
            } catch (Exception e) {
                failed++;
                log.error("定时同步退货包裹数据异常，店铺ID=" + shop.getShopId(), e);
            }
        }
        
        String resultMsg = String.format("退货包裹数据定时同步完成，总店铺数：%d，成功：%d，失败：%d", total, success, failed);
        log.info(resultMsg);
        
        return resultMsg;
    }

    /**
     * 获取指定店铺的退货包裹记录总数
     *
     * @param shopId 店铺ID
     * @return 退货包裹记录总数
     */
    @Override
    public Integer getRefundPackageCountByShopId(Long shopId) {
        return refundPackageDetailMapper.getRefundPackageCountByShopId(shopId);
    }

    /**
     * 清空店铺的同步数据
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearSyncData(Long shopId) {
        if (shopId == null) {
            log.error("清空同步数据失败: shopId不能为空");
            return false;
        }
        
        try {
            log.info("开始清空店铺[{}]的退货包裹同步数据", shopId);
            
            // 1. 删除退货包裹详情数据
            LambdaQueryWrapper<RefundPackageDetail> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RefundPackageDetail::getShopId, shopId);
            int count = refundPackageDetailMapper.delete(wrapper);
            log.info("已删除店铺[{}]的退货包裹数据记录{}条", shopId, count);
            
            // 2. 重置同步任务状态 - 使用Mybatis方式清空时间字段
            Map<String, Object> params = new HashMap<>();
            params.put("shopId", shopId);
            int updated = baseMapper.clearTimeFields(params);
            log.info("已重置店铺[{}]的同步任务状态，更新记录数：{}", shopId, updated);
            
            return true;
        } catch (Exception e) {
            log.error("清空店铺[{}]的退货包裹同步数据失败", shopId, e);
            return false;
        }
    }
}