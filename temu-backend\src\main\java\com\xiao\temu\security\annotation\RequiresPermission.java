package com.xiao.temu.security.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 权限注解，用于标识需要特定权限才能访问的方法
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequiresPermission {

    /**
     * 需要的权限标识
     */
    String value();

    /**
     * 验证模式：AND | OR
     * AND: 需要满足所有权限
     * OR: 满足任一权限即可
     */
    Logical logical() default Logical.AND;

    /**
     * 验证未通过时的错误提示信息
     */
    String message() default "权限不足";
    
    public enum Logical {
        AND,
        OR
    }
} 