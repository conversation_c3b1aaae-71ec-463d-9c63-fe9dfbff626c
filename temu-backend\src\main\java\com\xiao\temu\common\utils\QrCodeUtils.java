package com.xiao.temu.common.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 二维码工具类
 */
@Slf4j
public class QrCodeUtils {

    /**
     * 生成二维码，返回Base64字符串
     *
     * @param content 二维码内容
     * @param width   宽度
     * @param height  高度
     * @return Base64编码的图片字符串
     */
    public static String generateQrCodeBase64(String content, int width, int height) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            BitMatrix bitMatrix = generateQrCodeBitMatrix(content, width, height);
            MatrixToImageWriter.writeToStream(bitMatrix, "PNG", outputStream);
            
            // 转换为Base64
            String base64 = Base64.getEncoder().encodeToString(outputStream.toByteArray());
            return "data:image/png;base64," + base64;
        } catch (Exception e) {
            log.error("生成二维码出错", e);
            return null;
        }
    }

    /**
     * 生成二维码，返回字节数组
     *
     * @param content 二维码内容
     * @param width   宽度
     * @param height  高度
     * @return 字节数组
     */
    public static byte[] generateQrCodeBytes(String content, int width, int height) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            BitMatrix bitMatrix = generateQrCodeBitMatrix(content, width, height);
            MatrixToImageWriter.writeToStream(bitMatrix, "PNG", outputStream);
            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("生成二维码出错", e);
            return null;
        }
    }

    /**
     * 生成二维码位矩阵
     *
     * @param content 二维码内容
     * @param width   宽度
     * @param height  高度
     * @return 位矩阵
     * @throws WriterException 编码异常
     */
    private static BitMatrix generateQrCodeBitMatrix(String content, int width, int height) throws WriterException {
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        hints.put(EncodeHintType.MARGIN, 1);
        
        return new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints);
    }
} 