package com.xiao.temu.modules.operation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.operation.dto.OperationGroupDTO;
import com.xiao.temu.modules.operation.dto.QueryGroupDTO;
import com.xiao.temu.modules.operation.entity.OperationGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 运营组Mapper接口
 */
@Mapper
public interface OperationGroupMapper extends BaseMapper<OperationGroup> {

    /**
     * 分页查询运营组列表
     *
     * @param page    分页参数
     * @param queryDTO 查询条件
     * @return 运营组列表
     */
    IPage<OperationGroupDTO> selectGroupList(Page<OperationGroupDTO> page, @Param("query") QueryGroupDTO queryDTO);

    /**
     * 查询运营组详情
     *
     * @param groupId 运营组ID
     * @return 运营组详情
     */
    OperationGroupDTO selectGroupById(@Param("groupId") Long groupId);

    /**
     * 检查运营组名称是否唯一
     *
     * @param groupName 运营组名称
     * @param groupId   运营组ID（更新时排除自身）
     * @return 结果
     */
    int checkGroupNameUnique(@Param("groupName") String groupName, @Param("groupId") Long groupId);

    /**
     * 更新运营组状态
     *
     * @param groupId 运营组ID
     * @param status  状态
     * @return 结果
     */
    int updateStatus(@Param("groupId") Long groupId, @Param("status") String status);

    /**
     * 根据负责人ID查询运营组列表
     *
     * @param leaderId 负责人ID
     * @return 运营组列表
     */
    List<OperationGroup> selectGroupsByLeaderId(@Param("leaderId") Long leaderId);

    /**
     * 查询成员所在的运营组列表
     *
     * @param userId 用户ID
     * @return 运营组列表
     */
    List<OperationGroupDTO> selectGroupsByMemberId(@Param("userId") Long userId);

    /**
     * 统计用户所属的运营组数量
     *
     * @param userId 用户ID
     * @return 数量
     */
    int countUserGroups(@Param("userId") Long userId);
} 