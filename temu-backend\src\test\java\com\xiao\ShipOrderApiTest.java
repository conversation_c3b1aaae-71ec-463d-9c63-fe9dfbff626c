package com.xiao;

import com.alibaba.fastjson2.JSONObject;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.infrastructure.api.TemuApiClient;

import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;

/**
 * 用于测试bg.shiporderv2.get接口稳定性的测试类
 * 每5秒调用一次接口，连续调用10次
 */
public class ShipOrderApiTest {

    private static final String LOG_FILE = "ship_order_api_test.log";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
    
    public static void main(String[] args) {
        System.out.println("开始测试bg.shiporderv2.get接口稳定性...");
        System.out.println("间隔5秒，连续调用10次，测试结果将保存到" + LOG_FILE + "文件中");
        
        // 清空日志文件
        try (PrintWriter writer = new PrintWriter(new FileWriter(LOG_FILE))) {
            writer.println("======== TEMU API测试日志 ========");
            writer.println("测试接口: bg.shiporderv2.get");
            writer.println("测试时间: " + DATE_FORMAT.format(new Date()));
            writer.println("测试次数: 10次");
            writer.println("测试间隔: 5秒");
            writer.println("==============================");
        } catch (IOException e) {
            System.err.println("无法创建日志文件: " + e.getMessage());
            return;
        }
        
        // 连续调用10次
        int successCount = 0;
        int failCount = 0;
        
        for (int i = 1; i <= 10; i++) {
            System.out.println("第" + i + "次调用开始...");
            
            try {
                // 调用接口并记录结果
                JSONObject result = callShipOrderV2Api();
                boolean isSuccess = result != null && result.getBooleanValue("success");
                
                if (isSuccess) {
                    successCount++;
                    logResult(i, true, result);
                    System.out.println("第" + i + "次调用成功");
                } else {
                    failCount++;
                    logResult(i, false, result);
                    System.out.println("第" + i + "次调用失败: " + (result != null ? result.getString("errorMsg") : "无响应"));
                }
                
                // 如果不是最后一次调用，则等待5秒
                if (i < 10) {
                    System.out.println("等待5秒后进行下一次调用...");
                    Thread.sleep(5000);
                }
                
            } catch (Exception e) {
                failCount++;
                logError(i, e);
                System.out.println("第" + i + "次调用异常: " + e.getMessage());
                
                // 如果不是最后一次调用，则等待5秒
                if (i < 10) {
                    try {
                        System.out.println("等待5秒后进行下一次调用...");
                        Thread.sleep(5000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }
        
        // 输出统计结果
        System.out.println("\n测试完成，统计结果:");
        System.out.println("总调用次数: 10");
        System.out.println("成功次数: " + successCount);
        System.out.println("失败次数: " + failCount);
        System.out.println("成功率: " + (successCount * 10) + "%");
        System.out.println("详细日志请查看: " + LOG_FILE);
        
        // 记录统计结果到日志
        try (PrintWriter writer = new PrintWriter(new FileWriter(LOG_FILE, true))) {
            writer.println("\n======== 测试统计 ========");
            writer.println("总调用次数: 10");
            writer.println("成功次数: " + successCount);
            writer.println("失败次数: " + failCount);
            writer.println("成功率: " + (successCount * 10) + "%");
            writer.println("测试结束时间: " + DATE_FORMAT.format(new Date()));
            writer.println("=======================");
        } catch (IOException e) {
            System.err.println("无法写入日志文件: " + e.getMessage());
        }
    }
    
    /**
     * 调用bg.shiporderv2.get接口
     * @return 接口返回的JSON对象
     */
    private static JSONObject callShipOrderV2Api() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken("juekxhoepdyctc7lgex2nvvo8gbhzg9rxtklha6vgvxipe46njx7gz20");
        commonParams.setType("bg.shiporderv2.get");
        commonParams.setAppKey("9c65725c36feec08d6b7848cdff20e2a");
        commonParams.setAppSecret("f6cc806fb70ee21fb06ea176db6d3b9bb10b0f78");

        HashMap<String, Object> businessParams = new HashMap<>();
        businessParams.put("pageNo", 1);
        businessParams.put("pageSize", 10);
        businessParams.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        return TemuApiClient.sendRequest(commonParams, businessParams);
    }
    
    /**
     * 记录API调用结果
     * @param count 当前是第几次调用
     * @param isSuccess 是否调用成功
     * @param result API返回的结果
     */
    private static void logResult(int count, boolean isSuccess, JSONObject result) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(LOG_FILE, true))) {
            writer.println("\n----- 第" + count + "次调用 -----");
            writer.println("调用时间: " + DATE_FORMAT.format(new Date()));
            writer.println("调用状态: " + (isSuccess ? "成功" : "失败"));
            
            if (result != null) {
                writer.println("返回结果:");
                writer.println(result.toJSONString());
                
                if (!isSuccess) {
                    writer.println("错误码: " + result.getString("errorCode"));
                    writer.println("错误信息: " + result.getString("errorMsg"));
                    writer.println("请求ID: " + result.getString("requestId"));
                } else {
                    writer.println("请求ID: " + result.getString("requestId"));
                    writer.println("数据条数: " + (result.getJSONObject("result") != null ? result.getJSONObject("result").getIntValue("total") : 0));
                }
            } else {
                writer.println("返回结果: 空");
            }
        } catch (IOException e) {
            System.err.println("无法写入日志文件: " + e.getMessage());
        }
    }
    
    /**
     * 记录API调用异常
     * @param count 当前是第几次调用
     * @param e 异常对象
     */
    private static void logError(int count, Exception e) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(LOG_FILE, true))) {
            writer.println("\n----- 第" + count + "次调用 -----");
            writer.println("调用时间: " + DATE_FORMAT.format(new Date()));
            writer.println("调用状态: 异常");
            writer.println("异常信息: " + e.getMessage());
            writer.println("异常堆栈:");
            e.printStackTrace(writer);
        } catch (IOException ex) {
            System.err.println("无法写入日志文件: " + ex.getMessage());
        }
    }
} 