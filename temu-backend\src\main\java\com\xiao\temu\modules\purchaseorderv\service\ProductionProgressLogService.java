package com.xiao.temu.modules.purchaseorderv.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xiao.temu.modules.purchaseorderv.entity.ProductionProgressLog;
import com.xiao.temu.modules.purchaseorderv.vo.ProductionProgressLogVO;
import java.util.List;

/**
 * 生产进度日志服务接口
 */
public interface ProductionProgressLogService extends IService<ProductionProgressLog> {

    /**
     * 记录生产进度日志
     *
     * @param shopId 店铺ID
     * @param subPurchaseOrderSn 备货单号
     * @param progressType 进度类型
     * @param operationType 操作类型(1:完成 2:撤销)
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param remarks 备注
     */
    void recordLog(Long shopId, String subPurchaseOrderSn, String progressType, 
                  String operationType, Long operatorId, String operatorName, String remarks);

    /**
     * 获取备货单的生产进度日志
     *
     * @param shopId 店铺ID
     * @param subPurchaseOrderSn 备货单号
     * @return 生产进度日志列表
     */
    List<ProductionProgressLogVO> getLogsByOrderSn(Long shopId, String subPurchaseOrderSn);
    
    /**
     * 获取用户的操作日志
     *
     * @param operatorId 操作人ID
     * @param limit 限制返回记录数
     * @return 用户操作日志列表
     */
    List<ProductionProgressLogVO> getLogsByOperator(Long operatorId, Integer limit);
} 