package com.xiao.temu.modules.operation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xiao.temu.infrastructure.exporter.dto.ExportRequestDTO;
import com.xiao.temu.modules.operation.dto.GroupStatisticsDTO;
import com.xiao.temu.modules.operation.dto.OperationGroupDTO;
import com.xiao.temu.modules.operation.dto.QueryGroupDTO;
import com.xiao.temu.modules.shop.dto.ShopWithGroupsDTO;
import com.xiao.temu.modules.operation.entity.OperationGroup;

import java.util.List;

/**
 * 运营组服务接口
 */
public interface OperationGroupService {

    /**
     * 分页查询运营组列表
     *
     * @param queryDTO 查询条件
     * @return 运营组分页列表
     */
    IPage<OperationGroupDTO> getGroupList(QueryGroupDTO queryDTO);

    /**
     * 根据ID查询运营组
     *
     * @param groupId 运营组ID
     * @return 运营组详情
     */
    OperationGroupDTO getGroupById(Long groupId);

    /**
     * 新增运营组
     *
     * @param groupDTO 运营组信息
     * @return 结果
     */
    int insertGroup(OperationGroupDTO groupDTO);

    /**
     * 修改运营组
     *
     * @param groupDTO 运营组信息
     * @return 结果
     */
    int updateGroup(OperationGroupDTO groupDTO);

    /**
     * 删除运营组
     *
     * @param groupId 运营组ID
     * @return 结果
     */
    int deleteGroup(Long groupId);

    /**
     * 批量删除运营组
     *
     * @param groupIds 运营组ID数组
     * @return 结果
     */
    int deleteGroups(Long[] groupIds);

    /**
     * 修改运营组状态
     *
     * @param groupId 运营组ID
     * @param status 状态
     * @return 结果
     */
    int changeStatus(Long groupId, String status);

    /**
     * 检查运营组名称是否唯一
     *
     * @param groupName 运营组名称
     * @param groupId 运营组ID（更新时排除自身）
     * @return 结果 true-唯一 false-不唯一
     */
    boolean checkGroupNameUnique(String groupName, Long groupId);

    /**
     * 设置运营组负责人
     *
     * @param groupId 运营组ID
     * @param leaderId 负责人ID
     * @return 结果
     */
    int setGroupLeader(Long groupId, Long leaderId);

    /**
     * 获取用户负责的运营组列表
     *
     * @param userId 用户ID
     * @return 运营组列表
     */
    List<OperationGroup> getGroupsByLeaderId(Long userId);

    /**
     * 获取用户所属的运营组列表
     *
     * @param userId 用户ID
     * @return 运营组列表
     */
    List<OperationGroupDTO> getGroupsByMemberId(Long userId);

    /**
     * 获取运营组统计数据
     *
     * @param groupId 运营组ID
     * @return 统计数据
     */
    GroupStatisticsDTO getGroupStatistics(Long groupId);

    /**
     * 批量获取运营组统计数据
     *
     * @param groupIds 运营组ID列表
     * @return 统计数据列表
     */
    List<GroupStatisticsDTO> getGroupsStatistics(List<Long> groupIds);
    
    /**
     * 获取用户作为组长的运营组ID列表
     *
     * @param userId 用户ID
     * @return 运营组ID列表
     */
    List<Long> getGroupIdsByLeaderId(Long userId);
    
    /**
     * 检查用户是否是某个运营组的组长
     *
     * @param userId 用户ID
     * @return 是否为组长
     */
    boolean isUserGroupLeader(Long userId);
    
    /**
     * 检查用户是否为运营组成员
     *
     * @param userId 用户ID
     * @param groupId 运营组ID
     * @return 是否为成员
     */
    boolean checkUserInGroup(Long userId, Long groupId);
    
    /**
     * 检查运营组是否存在
     * 
     * @param groupId 运营组ID
     * @return 是否存在
     */
    boolean existsById(Long groupId);
    
    /**
     * 获取未分配给指定运营组的店铺列表
     * 包含完全未分配的店铺和已分配给其他运营组的店铺
     *
     * @param groupId 运营组ID (用于过滤当前运营组已有的店铺)
     * @return 包含所属运营组信息的店铺列表
     */
    List<ShopWithGroupsDTO> getUnassignedShops(Long groupId);
    
    /**
     * 为运营组分配店铺
     *
     * @param groupId 运营组ID
     * @param shopIds 店铺ID列表
     * @return 成功分配的数量
     */
    int assignShopsToGroup(Long groupId, List<Long> shopIds);
    
    /**
     * 获取店铺所属的所有运营组
     *
     * @param shopId 店铺ID
     * @return 运营组列表
     */
    List<OperationGroup> getGroupsByShopId(Long shopId);
    
    /**
     * 取消店铺与运营组的关联
     *
     * @param groupId 运营组ID
     * @param shopId 店铺ID
     * @return 结果
     */
    int removeShopFromGroup(Long groupId, Long shopId);
    
    /**
     * 获取所有运营组
     *
     * @return 运营组列表
     */
    List<OperationGroup> getAllGroups();
    
    /**
     * 获取导出数据
     *
     * @param exportDTO 导出请求参数
     * @return 运营组列表
     */
    List<OperationGroupDTO> getExportData(ExportRequestDTO exportDTO);

    /**
     * 获取用户所在的运营组ID列表
     *
     * @param userId 用户ID
     * @return 运营组ID列表
     */
    List<Long> getGroupIdsByMemberId(Long userId);
} 