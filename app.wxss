/**
 * 全局样式
 */
@import "./static/fonts/iconfont.wxss";

page {
  --primary-color: #07c160;
  --primary-hover-color: #06ad56;
  --bg-color: #f7f7f7;
  --text-color: #333333;
  --text-secondary-color: #666666;
  --text-light-color: #999999;
  --border-color: #e5e5e5;
  --btn-height: 88rpx;
  
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 28rpx;
  color: var(--text-color);
  background-color: var(--bg-color);
  box-sizing: border-box;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.role-tag {
  font-size: 24rpx;
  background-color: #e6f7ff;
  color: #1890ff;
  padding: 4rpx 16rpx;
  border-radius: 30rpx;
}

.content {
  flex: 1;
  padding: 30rpx;
}

.tabbar {
  display: flex;
  height: 100rpx;
  border-top: 1rpx solid var(--border-color);
  background-color: #ffffff;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: var(--text-light-color);
}

.tab-active {
  color: var(--primary-color);
}

.tab-text {
  font-size: 22rpx;
  margin-top: 6rpx;
}

.btn-primary {
  height: var(--btn-height);
  line-height: var(--btn-height);
  background-color: var(--primary-color);
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 8rpx;
}

/* 图标尺寸 */
.iconfont {
  font-size: 44rpx;
} 