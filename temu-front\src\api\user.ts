import request from '@/utils/request'

// 用于防止重复请求的变量
let userInfoPromise: Promise<any> | null = null
let userInfoPromiseTimeout: number | null = null

// 登录方法
export function login(username: string, password: string, rememberMe: boolean = false) {
  return request({
    url: '/auth/login',
    method: 'post',
    data: {
      username,
      password,
      rememberMe
    }
  })
}

// 获取用户详细信息 - 添加防抖机制
export function getUserInfo() {
  // 如果已经有一个请求在进行中，直接返回该请求的Promise
  if (userInfoPromise) {
    return userInfoPromise
  }
  
  // 创建新的请求
  userInfoPromise = request({
    url: '/auth/user-info',
    method: 'get'
  }).finally(() => {
    // 请求完成后设置一个短暂的锁定期，防止高频重复请求
    if (userInfoPromiseTimeout) {
      clearTimeout(userInfoPromiseTimeout)
    }
    
    userInfoPromiseTimeout = window.setTimeout(() => {
      userInfoPromise = null
      userInfoPromiseTimeout = null
    }, 1000) // 1秒内不允许重复请求
  })
  
  return userInfoPromise
}

// 退出登录
export function logout() {
  // 清除用户信息请求缓存
  userInfoPromise = null
  if (userInfoPromiseTimeout) {
    clearTimeout(userInfoPromiseTimeout)
    userInfoPromiseTimeout = null
  }
  
  return request({
    url: '/auth/logout',
    method: 'post'
  })
}

// 注册方法
export function register(data: any) {
  return request({
    url: '/auth/register',
    method: 'post',
    data
  })
}

// 获取用户列表
export function getUserList(query: any) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  })
}

// 查询用户详细
export function getUser(userId: number) {
  return request({
    url: `/system/user/${userId}`,
    method: 'get'
  })
}

// 新增用户
export function addUser(data: any) {
  return request({
    url: '/system/user',
    method: 'post',
    data
  })
}

// 修改用户
export function updateUser(data: any) {
  return request({
    url: '/system/user',
    method: 'put',
    data
  })
}

// 删除用户
export function deleteUser(userId: number) {
  return request({
    url: `/system/user/${userId}`,
    method: 'delete'
  })
}

// 批量删除用户
export function batchDeleteUsers(userIds: number[]) {
  return request({
    url: `/system/user/${userIds.join(',')}`,
    method: 'delete'
  })
}

// 重置密码
export function resetUserPwd(userId: number, password: string) {
  return request({
    url: '/system/user/resetPassword',
    method: 'put',
    params: {
      userId,
      password
    }
  })
}

// 修改用户状态
export function changeUserStatus(userId: number, status: string) {
  return request({
    url: '/system/user/changeStatus',
    method: 'put',
    params: {
      userId,
      status
    }
  })
}

// 分配角色
export function assignRoles(userId: number, roleIds: number[]) {
  return request({
    url: '/system/user/assignRoles',
    method: 'put',
    params: {
      userId
    },
    data: roleIds
  })
}

// 获取用户所有角色
export function getUserRoles(userId: number) {
  return request({
    url: `/system/user/roles/${userId}`,
    method: 'get'
  })
}

// 获取用户完整资料（包含手机号和邮箱等）
export function getUserProfile() {
  return request({
    url: '/system/user/profile',
    method: 'get'
  })
}

// 更新用户个人资料
export function updateUserProfile(data: any) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data
  })
}

// 获取具有特定权限的用户列表
export function getUsersByPermission(permission: string) {
  return request({
    url: '/system/user/listByPermission',
    method: 'get',
    params: { permission }
  })
}

// 获取具有特定角色的用户列表
export function getUsersByRoleKey(roleKey: string, isOperation: boolean = false) {
  if (isOperation) {
    return request({
      url: '/system/user/operation/listByRoleKey',
      method: 'get',
      params: { roleKey }
    })
  }
  return request({
    url: '/system/user/listByRoleKey',
    method: 'get',
    params: { roleKey }
  })
}

// 获取生产组管理中的生产组长用户列表
export function getProductionLeaders(roleKey: string) {
  return request({
    url: '/system/user/production/listByRoleKey',
    method: 'get',
    params: { roleKey }
  })
}

// 获取未分配到任何运营组的用户列表
export function getUnassignedUsers(roleKey?: string) {
  return request({
    url: '/system/user/unassignedUsers',
    method: 'get',
    params: { roleKey }
  })
}

// 导出用户数据
export function exportUsers(data: any) {
  return request({
    url: '/system/user/export',
    method: 'post',
    responseType: 'blob',
    headers: { 'Content-Type': 'application/json' },
    data
  })
}

// 导入用户数据
export function importUsers(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/system/user/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取导入模板
export function getImportTemplate() {
  return request({
    url: '/system/user/importTemplate',
    method: 'get',
    responseType: 'blob'
  })
} 