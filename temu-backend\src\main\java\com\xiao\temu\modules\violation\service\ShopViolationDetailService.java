package com.xiao.temu.modules.violation.service;

import com.xiao.temu.modules.violation.entity.ShopViolationDetail;
import com.xiao.temu.modules.violation.dto.ViolationInfoItem;

import java.util.List;

/**
 * 店铺违规详情Service接口
 */
public interface ShopViolationDetailService {
    
    /**
     * 批量保存违规详情信息
     *
     * @param shopId 店铺ID
     * @param violationItems 违规信息列表
     * @return 是否成功
     */
    boolean batchSaveViolationDetail(Long shopId, List<ViolationInfoItem> violationItems);
    
    /**
     * 保存单条违规详情信息
     *
     * @param detail 违规详情
     * @return 是否成功
     */
    boolean saveViolationDetail(ShopViolationDetail detail);
    
    /**
     * 检查违规详情是否存在
     *
     * @param shopId 店铺ID
     * @param punishSn 违规编号
     * @return 是否存在
     */
    boolean checkDetailExists(Long shopId, String punishSn);
    
    /**
     * 根据店铺ID和违规编号删除违规详情
     *
     * @param shopId 店铺ID
     * @param punishSn 违规编号
     * @return 是否成功
     */
    boolean deleteDetailByShopIdAndPunishSn(Long shopId, String punishSn);
} 