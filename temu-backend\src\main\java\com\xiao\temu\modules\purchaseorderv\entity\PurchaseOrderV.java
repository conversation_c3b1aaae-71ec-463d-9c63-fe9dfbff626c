package com.xiao.temu.modules.purchaseorderv.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 备货单信息实体类
 */
@Data
@TableName("purchase_order_v")
public class PurchaseOrderV {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 备货单号
     */
    private String subPurchaseOrderSn;
    
    /**
     * 原始采购单号
     */
    private String originalPurchaseOrderSn;
    
    /**
     * 商品名称
     */
    private String productName;
    
    /**
     * 商品ID
     */
    private Long productId;
    
    /**
     * 商品SKC ID
     */
    private Long productSkcId;
    
    /**
     * 供应商ID
     */
    private Long supplierId;
    
    /**
     * 供应商名称
     */
    private String supplierName;
    
    /**
     * 备货单创建时间
     */
    private Date purchaseTime;
    
    /**
     * 发货时间
     */
    private Date deliverTime;
    
    /**
     * 发货单号
     */
    private String deliveryOrderSn;
    
    /**
     * 预计最晚发货时间
     */
    private Date expectLatestDeliverTime;
    
    /**
     * 预计最晚到货时间
     */
    private Date expectLatestArrivalTime;
    
    /**
     * 收货仓库ID
     */
    private Long receiveWarehouseId;
    
    /**
     * 收货仓库名称
     */
    private String receiveWarehouseName;
    
    /**
     * 备货单状态(1:待发货 2:已发货待收货 3:已收货 4:已取消)
     */
    private Integer status;
    
    /**
     * 备货类型(0:普通备货 1:JIT备货)
     */
    private Integer purchaseStockType;
    
    /**
     * 实际收货时间
     */
    private Date receiveTime;
    
    /**
     * 采购数量
     */
    private Integer purchaseQuantity;
    
    /**
     * 实际发货数量
     */
    private Integer deliverQuantity;
    
    /**
     * 实际收货数量
     */
    private Integer receiveQuantity;
    
    /**
     * 是否删除(0:否 1:是)
     */
    private Boolean isDeleted;
    
    /**
     * 同步时间
     */
    private Date syncTime;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 