package com.xiao.temu.modules.refund.service;

import com.xiao.temu.modules.refund.dto.RefundRequestDTO;
import com.xiao.temu.modules.refund.vo.RefundPackageVO;

/**
 * Temu退货明细服务接口
 */
public interface RefundPackageService {
    
    /**
     * 获取退货包裹列表
     *
     * @param requestDTO 请求参数
     * @param userId 当前用户ID
     * @return 退货包裹列表
     */
    RefundPackageVO getRefundPackageList(RefundRequestDTO requestDTO, Long userId);
} 