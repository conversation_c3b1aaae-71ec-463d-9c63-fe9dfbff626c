package com.xiao.temu.modules.purchaseorderv.dto;

import lombok.Data;
import java.util.List;

/**
 * Temu采购单请求参数DTO
 */
@Data
public class PurchaseOrderRequestDTO {
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺ID列表
     */
    private List<Long> shopIds;
    
    /**
     * 页码，从1开始
     */
    private Integer pageNo;
    
    /**
     * 每页大小，默认10，最大1500
     */
    private Integer pageSize;
    
    /**
     * 下单时间-开始，毫秒
     */
    private Long purchaseTimeFrom;
    
    /**
     * 下单时间-结束，毫秒
     */
    private Long purchaseTimeTo;
    
    /**
     * 原始采购单号列表
     */
    private List<String> originalPurchaseOrderSnList;
    
    /**
     * 发货交付延迟状态列表
     */
    private List<Integer> deliverOrArrivalDelayStatusList;
    
    /**
     * 商品编码列表
     */
    private List<String> productSnList;
    
    /**
     * sku列表
     */
    private List<Long> productSkuIdList;
    

    /**
     * 店铺ID列表
     */
    private List<Long> supplierIdList;
    
    /**
     * 是否有缺货或标品缺货，0-不存在，1-存在
     */
    private Integer skuLackSnapshot;
    
    /**
     * 下单来源，0-非系统下单，1-系统下单，9999-非标下单
     */
    private List<Integer> sourceList;
    
    /**
     * 发货单列表
     */
    private List<String> deliverOrderSnList;
    
    /**
     * 紧急类型，0-普通，1-紧急
     */
    private Integer urgencyType;
    
    /**
     * 是否忽略权限检查
     */
    private Boolean ignorePermissionCheck;
    
    /**
     * 备货单号列表
     */
    private List<String> subPurchaseOrderSnList;
    
    /**
     * 备货类型，0-普通，1-JIT备货
     */
    private Integer purchaseStockType;
    
    /**
     * 备货区域，0-国内备货,1-海外备货,2-保税仓备货
     */
    private List<Integer> inventoryRegionList;
    
    /**
     * SKC列表
     */
    private List<String> productSkcIdList;
    
    /**
     * 结算类型，0-非vmi(采购) 1-vmi(备货)
     */
    private Integer settlementType;
    
    /**
     * 是否系统下单，true-系统自动下单，false-其他
     */
    private Boolean isSystemAutoPurchaseSource;
    
    /**
     * 发货是否逾期，true-是，false-否
     */
    private Boolean isDelayDeliver;
    
    /**
     * 到货是否逾期，true-是，false-否
     */
    private Boolean isDelayArrival;
    
    /**
     * 要求最晚发货时间-开始，毫秒
     */
    private Long expectLatestDeliverTimeFrom;
    
    /**
     * 要求最晚发货时间-结束，毫秒
     */
    private Long expectLatestDeliverTimeTo;
    
    /**
     * 要求最晚到货时间-开始，毫秒
     */
    private Long expectLatestArrivalTimeFrom;
    
    /**
     * 要求最晚到货时间-结束，毫秒
     */
    private Long expectLatestArrivalTimeTo;
    
    /**
     * 是否首单，true-是，false-否
     */
    private Boolean isFirst;
    
    /**
     * 是否有质量隐患，0-不存在 1-存在
     */
    private Integer qcReject;
    
    /**
     * 是否存在质检不合格的sku，10-是，20-否
     */
    private Integer qcOption;
    
    /**
     * 是否因抽检不合格创建，true-是，false-否
     */
    private Boolean qcNotPassCreate;
    
    /**
     * 是否含缺货/售罄SKU，1-含缺货SKU；2-含售罄SKU
     */
    private List<Integer> lackOrSoldOutTagList;
    
    /**
     * 是否热销款，true-是，false-否
     */
    private Boolean hotTag;
    
    /**
     * 预计可发货日期-开始，毫秒
     */
    private Long canDeliverStartTime;
    
    /**
     * 预计可发货日期-结束，毫秒
     */
    private Long canDeliverEndTime;
    
    /**
     * 商品条码样式，0-全选，1-旧样式，2-新样式
     */
    private Integer productLabelCodeStyle;
    
    /**
     * 是否入库退供，true-是，false-否
     */
    private Boolean inboundReturn;
    
    /**
     * 是否因入库退供创建，true-是，false-否
     */
    private Boolean inboundReturnCreate;
    
    /**
     * 履约考核，true-是，false-否
     */
    private Boolean inFulfilmentPunish;
    
    /**
     * 今日系统创建，true-是，false-否
     */
    private Boolean isTodayPlatformPurchase;
    
    /**
     * 热销款库存不足，true-是，false-否
     */
    private Boolean hotInventoryLack;
    
    /**
     * 今日可发货，true-是，false-否
     */
    private Boolean todayCanDeliver;
    
    /**
     * JIT转备货，true-是，false-否
     */
    private Boolean isCloseJit;
    
    /**
     * 收货仓库ID列表
     */
    private List<Long> subWarehouseIdList;
    
    /**
     * 状态筛选列表
     * 0-待创建, 1-待发货, 2-已送货, 3-已收货
     * 5-抽检全部退回, 6-已验收, 7-已入库
     * 8-已作废, 9-已超时, 10-已取消
     */
    private List<Integer> statusList;
    
    /**
     * 排序参数
     * firstOrderByParam: 排序字段，如 "createdAt", "expectLatestDeliverTime", "expectLatestArrivalTime"
     * firstOrderByDesc: 排序方向，1-降序，0-升序
     */
    private OneDimensionSort oneDimensionSort;
    
    /**
     * 一维排序参数
     */
    @Data
    public static class OneDimensionSort {
        /**
         * 排序字段
         */
        private String firstOrderByParam;
        
        /**
         * 排序方向: 1-降序，0-升序
         */
        private Integer firstOrderByDesc;
    }
} 