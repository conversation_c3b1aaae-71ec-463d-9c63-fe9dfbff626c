package com.xiao.temu.modules.purchaseorderv.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderNotification;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 备货单通知记录数据访问接口
 */
@Mapper
public interface PurchaseOrderNotificationMapper extends BaseMapper<PurchaseOrderNotification> {
    
    /**
     * 根据店铺ID和备货单号查询通知记录
     *
     * @param shopId 店铺ID
     * @param subPurchaseOrderSn 备货单号
     * @param notificationType 通知类型
     * @return 通知记录
     */
    PurchaseOrderNotification getByShopIdAndOrderSn(@Param("shopId") Long shopId, 
                                                    @Param("subPurchaseOrderSn") String subPurchaseOrderSn, 
                                                    @Param("notificationType") Integer notificationType);
    
    /**
     * 获取需要继续通知的记录列表（通知次数小于3次的）
     *
     * @param notificationType 通知类型
     * @param maxNotifyCount 最大通知次数
     * @return 通知记录列表
     */
    List<PurchaseOrderNotification> findNeedToContinueNotify(@Param("notificationType") Integer notificationType, 
                                                             @Param("maxNotifyCount") Integer maxNotifyCount);
    
    /**
     * 获取特定店铺和通知类型的通知记录数量
     *
     * @param shopId 店铺ID
     * @param notificationType 通知类型
     * @return 通知记录数量
     */
    int countByShopIdAndType(@Param("shopId") Long shopId, @Param("notificationType") Integer notificationType);
    
    /**
     * 根据店铺ID删除通知记录
     *
     * @param shopId 店铺ID
     * @return 影响行数
     */
    int deleteByShopId(@Param("shopId") Long shopId);
} 