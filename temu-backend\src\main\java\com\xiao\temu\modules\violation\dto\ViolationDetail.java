package com.xiao.temu.modules.violation.dto;

import com.xiao.temu.modules.violation.vo.MmsPurchaseOrderViolationInfoVO;
import lombok.Data;
import java.util.List;

/**
 * 违规详情
 */
@Data
public class ViolationDetail {
    
    /**
     * 申诉编号
     */
    private String appealSn;
    
    /**
     * 违规开始时间
     */
    private Long violationStartTime;
    
    /**
     * 版本
     */
    private String version;
    
    /**
     * 申诉状态
     */
    private Integer appealStatus;
    
    /**
     * 申诉时间
     */
    private Long appealTime;
    
    /**
     * 申诉原因
     */
    private String appealReason;
    
    /**
     * 申诉材料 - 支持数组格式
     */
    private List<String> appealMaterial;
    
    /**
     * 操作人
     */
    private String operator;
    
    /**
     * 操作结果
     */
    private String operateResult;
    
    /**
     * 操作时间
     */
    private Long operateTime;
    
    /**
     * 操作备注
     */
    private String operateRemark;
    
    /**
     * 质量事件违规信息
     */
    private Object mmsqeviolationInfoVO;
    
    /**
     * 体积重量违规信息
     */
    private Object mmsvolumeWeightViolationInfoVO;
    
    /**
     * 备货单违规信息
     */
    private MmsPurchaseOrderViolationInfoVO mmspurchaseOrderViolationInfoVO;
} 