package com.xiao.temu.infrastructure.excel;

import com.xiao.temu.infrastructure.storage.CosStorageService;
import com.xiao.temu.infrastructure.task.ExportTaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.springframework.context.ApplicationContext;
import org.springframework.util.StopWatch;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 带图片的Excel导出工具类
 * 用于处理需要在Excel中显示图片的导出场景
 */
@Slf4j
public class ExcelExportUtils {

    /**
     * 图片下载任务类
     */
    public static class ImageDownloadTask {
        private String imageUrl;
        private File targetFile;
        private int col;
        private int row;
        private int rowSpan = 1; // 新增：图片跨行数
        private int colSpan = 1; // 新增：图片跨列数

        public ImageDownloadTask(String imageUrl, File targetFile, int col, int row) {
            this.imageUrl = imageUrl;
            this.targetFile = targetFile;
            this.col = col;
            this.row = row;
        }

        // 新增getter和setter方法
        public int getRowSpan() {
            return rowSpan;
        }

        public void setRowSpan(int rowSpan) {
            this.rowSpan = rowSpan;
        }

        public int getColSpan() {
            return colSpan;
        }

        public void setColSpan(int colSpan) {
            this.colSpan = colSpan;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public File getTargetFile() {
            return targetFile;
        }

        public int getCol() {
            return col;
        }

        public int getRow() {
            return row;
        }
    }

    /**
     * 创建临时目录用于存储图片
     * @return 临时目录
     */
    public static File createTempDirectory() {
        String uuid = UUID.randomUUID().toString();
        File tempDir = new File(System.getProperty("java.io.tmpdir"), "temu_export_" + uuid);
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }
        return tempDir;
    }

    /**
     * 创建导出文件目录
     * @return 导出文件目录
     */
    public static File createExportDirectory() {
        File outputDir = new File(System.getProperty("java.io.tmpdir"), "temu_export_output");
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }
        return outputDir;
    }

    /**
     * 下载图片
     * @param downloadTasks 下载任务列表
     * @param applicationContext Spring应用上下文
     * @param taskId 任务ID
     * @param exportTaskManager 导出任务管理器
     * @throws InterruptedException 如果线程等待被中断
     */
    public static void downloadImages(List<ImageDownloadTask> downloadTasks, 
                                     ApplicationContext applicationContext,
                                     String taskId,
                                     ExportTaskManager exportTaskManager) throws InterruptedException {
        if (downloadTasks.isEmpty()) {
            return;
        }

        // 总任务数
        int totalImageCount = downloadTasks.size();
        log.info("开始下载图片，总数量: {}", totalImageCount);
        
        // 批量处理参数
        final int BATCH_SIZE = 200; // 每批处理200个图片
        final int BATCH_COUNT = (totalImageCount + BATCH_SIZE - 1) / BATCH_SIZE; // 总批次，向上取整
        
        // 已处理计数
        final AtomicInteger downloadedCount = new AtomicInteger(0);
        
        // 创建线程池，用于并行下载图片
        int processorCount = Runtime.getRuntime().availableProcessors();
        log.info("当前处理器核心数: {}, 使用线程数: {}", processorCount, processorCount * 2);
        
        // 使用有界队列，避免任务过多导致内存溢出
        LinkedBlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(BATCH_SIZE);
        ThreadPoolExecutor threadPool = new ThreadPoolExecutor(
            processorCount * 2, // 核心线程数
            processorCount * 4, // 最大线程数
            60L, TimeUnit.SECONDS, // 空闲线程存活时间
            workQueue, // 工作队列
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：调用者运行
        );

        try {
            // 配置重试策略
            final int MAX_RETRY = 3; // 最大重试次数
            final long INITIAL_RETRY_DELAY_MS = 500; // 初始重试延迟（毫秒）
            
            // 批量处理下载任务
            for (int batchIndex = 0; batchIndex < BATCH_COUNT; batchIndex++) {
                int fromIndex = batchIndex * BATCH_SIZE;
                int toIndex = Math.min(fromIndex + BATCH_SIZE, totalImageCount);
                List<ImageDownloadTask> batchTasks = downloadTasks.subList(fromIndex, toIndex);
                
                log.info("处理第 {}/{} 批图片下载，范围: {}-{}, 数量: {}", 
                        batchIndex + 1, BATCH_COUNT, fromIndex, toIndex - 1, batchTasks.size());
                
                // 为当前批次创建倒计时锁
                CountDownLatch batchLatch = new CountDownLatch(batchTasks.size());
                
                // 更新任务进度
                int progressBase = 30 + (int)((double)fromIndex / totalImageCount * 40);
                exportTaskManager.updateTaskProgress(taskId, progressBase, 
                    String.format("开始下载第 %d/%d 批图片，已处理 %d/%d 张...", batchIndex + 1, BATCH_COUNT, downloadedCount.get(), totalImageCount));
                
                // 处理当前批次的图片
                for (ImageDownloadTask task : batchTasks) {
                    int finalBatchIndex = batchIndex;
                    threadPool.execute(() -> {
                        int retryCount = 0;
                        boolean success = false;
                        Exception lastException = null;
                        
                        // 使用重试机制
                        while (retryCount <= MAX_RETRY && !success) {
                            try {
                                if (retryCount > 0) {
                                    // 使用指数退避策略计算延迟时间
                                    long delayMs = INITIAL_RETRY_DELAY_MS * (long) Math.pow(2, retryCount - 1);
                                    log.debug("重试下载图片: {}, 第{}次重试, 延迟 {}ms", task.getImageUrl(), retryCount, delayMs);
                                    Thread.sleep(delayMs);
                                }
                                
                                CosStorageService cosStorageService = applicationContext.getBean(CosStorageService.class);
                                File downloadedFile = cosStorageService.downloadImage(task.getImageUrl());
                                
                                // 如果下载成功，复制到指定文件
                                if (downloadedFile != null && downloadedFile.exists()) {
                                    org.apache.commons.io.FileUtils.copyFile(downloadedFile, task.getTargetFile());
                                    downloadedFile.delete(); // 删除原始下载的临时文件
                                    success = true;
                                    
                                    // 更新下载进度
                                    int completed = downloadedCount.incrementAndGet();
                                    if (completed % 20 == 0 || completed % BATCH_SIZE == 0 || completed == totalImageCount) {
                                        int progress = 30 + (int)((double)completed / totalImageCount * 40);
                                        exportTaskManager.updateTaskProgress(taskId, progress, 
                                            String.format("已下载 %d/%d 张图片 (第 %d/%d 批)...", 
                                                completed, totalImageCount, finalBatchIndex + 1, BATCH_COUNT));
                                    }
                                } else {
                                    throw new IOException("下载图片返回为空");
                                }
                            } catch (Exception e) {
                                lastException = e;
                                retryCount++;
                                if (retryCount > MAX_RETRY) {
                                    log.error("下载图片失败，已超过最大重试次数: {}", task.getImageUrl(), e);
                                }
                            } finally {
                                if (retryCount > MAX_RETRY || success) {
                                    batchLatch.countDown();
                                }
                            }
                        }
                        
                        if (!success) {
                            log.error("下载图片最终失败: {}, 原因: {}", task.getImageUrl(), 
                                      lastException != null ? lastException.getMessage() : "未知错误");
                            // 即使下载失败也更新进度
                            int completed = downloadedCount.incrementAndGet();
                            if (completed % 20 == 0 || completed % BATCH_SIZE == 0 || completed == totalImageCount) {
                                int progress = 30 + (int)((double)completed / totalImageCount * 40);
                                exportTaskManager.updateTaskProgress(taskId, progress, 
                                    String.format("已处理 %d/%d 张图片（部分图片可能下载失败）...", completed, totalImageCount));
                            }
                        }
                    });
                }
                
                // 等待当前批次完成，最多等待3分钟
                boolean allCompleted = batchLatch.await(3, TimeUnit.MINUTES);
                
                if (!allCompleted) {
                    log.warn("第 {}/{} 批次图片下载超时未完成", batchIndex + 1, BATCH_COUNT);
                }
                
                // 每处理3批后，主动触发GC释放内存
                if (batchIndex > 0 && (batchIndex + 1) % 3 == 0) {
                    log.info("处理完 {} 批次后，主动触发GC释放内存", batchIndex + 1);
                    System.gc();
                }
                
                log.info("完成第 {}/{} 批图片下载，当前进度: {}/{}", 
                        batchIndex + 1, BATCH_COUNT, downloadedCount.get(), totalImageCount);
            }
            
            log.info("所有图片处理完成，总数: {}, 成功数: {}", totalImageCount, downloadedCount.get());
        } finally {
            // 关闭线程池
            threadPool.shutdown();
        }
    }

    /**
     * 创建通用的表头和内容样式
     * @param workbook Excel工作簿
     * @return 样式数组，index 0为表头样式，index 1为内容样式
     */
    public static CellStyle[] createStyles(Workbook workbook) {
        CellStyle[] styles = new CellStyle[2];
        
        // 创建表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 12);
        headerStyle.setFont(headerFont);
        
        // 创建内容样式
        CellStyle contentStyle = workbook.createCellStyle();
        contentStyle.setBorderBottom(BorderStyle.THIN);
        contentStyle.setBorderLeft(BorderStyle.THIN);
        contentStyle.setBorderRight(BorderStyle.THIN);
        contentStyle.setBorderTop(BorderStyle.THIN);
        contentStyle.setAlignment(HorizontalAlignment.CENTER);
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        
        styles[0] = headerStyle;
        styles[1] = contentStyle;
        
        return styles;
    }
    
    /**
     * 设置单元格值并应用样式
     * @param row 行
     * @param colIndex 列索引
     * @param value 值
     * @param style 样式
     */
    public static void setCellValue(Row row, int colIndex, String value, CellStyle style) {
        Cell cell = row.createCell(colIndex);
        cell.setCellValue(value != null ? value : "");
        cell.setCellStyle(style);
    }
    
    /**
     * 创建表头
     * @param sheet 工作表
     * @param headers 表头内容
     * @param headerStyle 表头样式
     * @param columnWidths 列宽设置（可选）
     */
    public static void createHeader(Sheet sheet, String[] headers, CellStyle headerStyle, int[] columnWidths) {
        Row headerRow = sheet.createRow(0);
        
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
            
            // 设置列宽
            if (columnWidths != null && i < columnWidths.length) {
                sheet.setColumnWidth(i, columnWidths[i] * 256); // 256是一个字符的宽度
            } else {
                sheet.setColumnWidth(i, 15 * 256); // 默认15个字符宽度
            }
        }
    }
    
    /**
     * 插入图片到工作表
     * @param downloadTasks 图片下载任务列表
     * @param workbook 工作簿
     * @param sheet 工作表
     * @param drawingPatriarch 绘图对象
     * @param taskId 任务ID
     * @param exportTaskManager 导出任务管理器
     */
    public static void insertImages(List<ImageDownloadTask> downloadTasks, 
                                  Workbook workbook, 
                                  Sheet sheet, 
                                  Drawing<?> drawingPatriarch,
                                  String taskId,
                                  ExportTaskManager exportTaskManager) {
        int totalImageCount = downloadTasks.size();
        log.info("开始插入图片到Excel，总数量: {}", totalImageCount);
        
        if (totalImageCount == 0) {
            return;
        }
        
        // 批量处理参数
        final int BATCH_SIZE = 200; // 每批插入200张图片
        final int BATCH_COUNT = (totalImageCount + BATCH_SIZE - 1) / BATCH_SIZE; // 总批次
        int insertedImageCount = 0;
        
        // 添加处理超时设置
        final long BATCH_TIMEOUT_MS = 5 * 60 * 1000; // 每批处理最长5分钟超时
        final int MAX_RETRY_PER_IMAGE = 2; // 每张图片最多重试2次
        
        try {
            // 分批插入图片到Excel
            for (int batchIndex = 0; batchIndex < BATCH_COUNT; batchIndex++) {
                int fromIndex = batchIndex * BATCH_SIZE;
                int toIndex = Math.min(fromIndex + BATCH_SIZE, totalImageCount);
                List<ImageDownloadTask> batchTasks = downloadTasks.subList(fromIndex, toIndex);
                
                log.info("处理第 {}/{} 批图片插入，范围: {}-{}, 数量: {}", 
                        batchIndex + 1, BATCH_COUNT, fromIndex, toIndex - 1, batchTasks.size());
                
                // 更新任务进度
                int progressBase = 90 + (int)((double)fromIndex / totalImageCount * 8);
                exportTaskManager.updateTaskProgress(taskId, progressBase, 
                    String.format("正在插入第 %d/%d 批图片，已处理 %d/%d 张...", 
                        batchIndex + 1, BATCH_COUNT, insertedImageCount, totalImageCount));
                
                // 插入当前批次的图片
                int batchInsertCount = 0;
                StopWatch batchTimer = new StopWatch();
                batchTimer.start();
                
                // 记录批次开始时间，用于超时检测
                long batchStartTime = System.currentTimeMillis();
                
                for (ImageDownloadTask task : batchTasks) {
                    // 检查批次处理是否超时
                    if (System.currentTimeMillis() - batchStartTime > BATCH_TIMEOUT_MS) {
                        log.warn("第 {}/{} 批图片插入处理超时（{}分钟），跳过剩余图片继续下一批次", 
                                batchIndex + 1, BATCH_COUNT, BATCH_TIMEOUT_MS / 60000);
                        exportTaskManager.updateTaskProgress(taskId, progressBase + 1, 
                            String.format("第 %d 批图片处理超时，跳过一些图片继续处理...", batchIndex + 1));
                        break;
                    }
                    
                    if (!task.getTargetFile().exists()) {
                        continue;
                    }
                    
                    boolean imageInserted = false;
                    int retryCount = 0;
                    Exception lastException = null;
                    
                    // 添加重试机制
                    while (!imageInserted && retryCount <= MAX_RETRY_PER_IMAGE) {
                        try {
                            if (retryCount > 0) {
                                log.info("重试第 {} 次插入图片: {}", retryCount, task.getTargetFile().getName());
                                Thread.sleep(500); // 重试前等待500ms
                            }
                            
                            // 读取图片
                            byte[] imageBytes = org.apache.commons.io.FileUtils.readFileToByteArray(task.getTargetFile());
                            
                            // 处理特别大的图片，限制大小
                            if (imageBytes.length > 1024 * 1024) { // 如果图片大于1MB
                                log.warn("图片过大 ({}KB)，可能导致处理缓慢: {}", 
                                        imageBytes.length / 1024, task.getTargetFile().getName());
                            }
                            
                            int pictureIdx = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_JPEG);
                            
                            // 创建图片锚点
                            XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, 
                                                task.getCol(), task.getRow() + 1, 
                                                task.getCol() + task.getColSpan(), task.getRow() + task.getRowSpan() + 1);
                            anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);
                            
                            // 插入图片
                            drawingPatriarch.createPicture(anchor, pictureIdx);
                            
                            // 释放内存
                            imageBytes = null;
                            
                            // 标记成功
                            imageInserted = true;
                            
                            // 更新计数
                            insertedImageCount++;
                            batchInsertCount++;
                            
                            // 更新进度（每20张图片更新一次）
                            if (insertedImageCount % 20 == 0 || insertedImageCount % BATCH_SIZE == 0 || insertedImageCount == totalImageCount) {
                                int progress = 90 + (int)((double)insertedImageCount / totalImageCount * 8);
                                exportTaskManager.updateTaskProgress(taskId, progress, 
                                    String.format("已插入 %d/%d 张图片（第 %d/%d 批）...", 
                                        insertedImageCount, totalImageCount, batchIndex + 1, BATCH_COUNT));
                            }
                        } catch (OutOfMemoryError oom) {
                            // 内存溢出是严重错误，立即触发GC并继续处理
                            log.error("插入图片时发生内存溢出: {}", task.getTargetFile().getAbsolutePath(), oom);
                            lastException = new Exception("内存溢出: " + oom.getMessage());
                            System.gc(); // 立即触发GC
                            retryCount = MAX_RETRY_PER_IMAGE + 1; // 不再重试
                        } catch (Exception e) {
                            lastException = e;
                            retryCount++;
                            log.warn("插入图片失败(第{}次尝试): {}, 错误: {}", 
                                    retryCount, task.getTargetFile().getName(), e.getMessage());
                        }
                    }
                    
                    if (!imageInserted) {
                        log.error("插入图片最终失败: {}, 原因: {}", 
                                task.getTargetFile().getAbsolutePath(), 
                                lastException != null ? lastException.getMessage() : "未知错误");
                    }
                    
                    // 处理过的图片立即删除，释放磁盘空间
                    if (task.getTargetFile().exists()) {
                        boolean deleted = task.getTargetFile().delete();
                        if (!deleted) {
                            log.warn("无法删除处理完的图片文件: {}", task.getTargetFile().getAbsolutePath());
                        }
                    }
                }
                
                batchTimer.stop();
                log.info("完成第 {}/{} 批图片插入，成功插入 {}/{} 张，耗时: {}ms", 
                        batchIndex + 1, BATCH_COUNT, batchInsertCount, batchTasks.size(), batchTimer.getTotalTimeMillis());
                
                // 每处理3批后，主动触发GC释放内存
                if (batchIndex > 0 && (batchIndex + 1) % 3 == 0) {
                    log.info("插入完 {} 批图片后，主动触发GC释放内存", batchIndex + 1);
                    System.gc();
                }
                
                // 如果这批处理时间超过3分钟，提前触发GC
                if (batchTimer.getTotalTimeMillis() > 3 * 60 * 1000) {
                    log.info("第 {} 批处理时间超过3分钟，主动触发GC释放内存", batchIndex + 1);
                    System.gc();
                }
            }
            
            log.info("所有图片插入完成，总数: {}, 成功插入: {}", totalImageCount, insertedImageCount);
        } catch (Exception e) {
            log.error("图片插入过程发生异常", e);
            throw e;
        }
    }
    
    /**
     * 保存Excel文件
     * @param workbook 工作簿
     * @param exportFile 导出文件
     * @param taskId 任务ID
     * @param exportTaskManager 导出任务管理器
     * @throws IOException IO异常
     */
    public static void saveExcelFile(Workbook workbook, File exportFile, String taskId, ExportTaskManager exportTaskManager) throws IOException {
        // 更新进度
        exportTaskManager.updateTaskProgress(taskId, 98, "正在保存Excel文件...");
        
        // 保存Excel文件
        try (FileOutputStream fileOut = new FileOutputStream(exportFile)) {
            workbook.write(fileOut);
        }
        
        // 更新任务为完成状态，并记录文件路径
        exportTaskManager.completeTaskWithFilePath(taskId, "导出完成，可以下载", exportFile.getAbsolutePath());
    }
    
    /**
     * 清理临时目录
     * @param tempDir 临时目录
     */
    public static void cleanupTempDirectory(File tempDir) {
        if (tempDir != null && tempDir.exists()) {
            try {
                org.apache.commons.io.FileUtils.deleteDirectory(tempDir);
            } catch (IOException e) {
                log.error("删除临时文件夹失败: {}", tempDir.getAbsolutePath(), e);
            }
        }
    }
} 