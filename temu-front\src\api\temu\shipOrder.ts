import request from '@/utils/request'

// 获取发货单列表数据
export function getShipOrders(query) {
  // 过滤掉空值和空数组
  const filteredQuery = filterEmptyValues(query);
  
  return request({
    url: '/temu/shipOrder/list',
    method: 'post',
    data: filteredQuery
  })
}

// 获取用户可访问的店铺列表
export function getUserShops() {
  return request({
    url: '/temu/shipOrder/shops',
    method: 'get'
  }).catch(error => {
    console.error('获取店铺列表API错误')
    throw error
  })
}

// 导出发货单数据（预留）
export function exportShipOrders(query) {
  // 过滤掉空值和空数组
  const filteredQuery = filterEmptyValues(query);
  
  return request({
    url: '/temu/shipOrder/export',
    method: 'post',
    data: filteredQuery,
    responseType: 'blob'
  })
}

/**
 * 批量打印商品打包标签
 * @param {Array} data - 包含发货单号和备货单号的数据数组
 * @returns {Promise} - API响应
 */
export function printShipOrderLabels(data) {
  return request({
    url: '/temu/shipOrder/printLabels',
    method: 'post',
    data
  })
}

/**
 * 过滤掉对象中的空值、空数组和未定义的值
 * @param {Object} obj - 需要过滤的对象
 * @returns {Object} - 过滤后的对象
 */
function filterEmptyValues(obj) {
  if (!obj || typeof obj !== 'object') return obj;
  
  return Object.entries(obj).reduce((result, [key, value]) => {
    // 跳过undefined和null值
    if (value === undefined || value === null) return result;
    
    // 检查数组是否为空
    if (Array.isArray(value) && value.length === 0) return result;
    
    // 如果值是对象，递归过滤
    if (typeof value === 'object' && !Array.isArray(value)) {
      const filteredValue = filterEmptyValues(value);
      if (Object.keys(filteredValue).length > 0) {
        result[key] = filteredValue;
      }
      return result;
    }
    
    // 添加有效值
    result[key] = value;
    return result;
  }, {});
} 