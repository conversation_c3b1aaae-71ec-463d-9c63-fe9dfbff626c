package com.xiao.temu.modules.shiporderv.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.modules.shiporderv.service.ShipOrderService;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.shiporderv.dto.ShipOrderRequestDTO;
import com.xiao.temu.modules.shiporderv.vo.ShipOrderVO;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.system.service.UserService;
import com.xiao.temu.infrastructure.api.TemuApiClient;
import org.ehcache.shadow.org.terracotta.statistics.Time;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static com.xiao.temu.infrastructure.api.TemuApiClient.addParameterIfPresent;

/**
 * Temu发货单服务实现
 */
@Service
public class ShipOrderServiceImpl implements ShipOrderService {

    private static final Logger log = LoggerFactory.getLogger(ShipOrderServiceImpl.class);

    @Autowired
    private ShopService shopService;

    @Autowired
    private UserService userService;

    /**
     * 获取发货单列表
     *
     * @param requestDTO 请求参数
     * @param userId 当前用户ID
     * @return 发货单列表
     */
    @Override
    public ShipOrderVO getShipOrderList(ShipOrderRequestDTO requestDTO, Long userId) {
        log.debug("查询Temu发货单列表, 请求参数: {}, 用户ID: {}", requestDTO, userId);
        
        ShipOrderVO response = new ShipOrderVO();
        response.setSuccess(true);
        
        try {
            // 获取店铺ID列表
            List<Long> shopIds = requestDTO.getShopIds();
            Long singleShopId = requestDTO.getShopId();
            
            // 如果shopIds为空但singleShopId不为空，将singleShopId加入shopIds
            if ((shopIds == null || shopIds.isEmpty()) && singleShopId != null) {
                shopIds = new ArrayList<>();
                shopIds.add(singleShopId);
                requestDTO.setShopIds(shopIds);
            }
            
            // 验证店铺ID参数
            if (shopIds == null || shopIds.isEmpty()) {
                throw new RuntimeException("店铺ID不能为空");
            }
            
            // 检查用户是否是管理员
            boolean isAdmin = userService.isAdmin(userId);
            // 检查是否忽略权限检查
            boolean ignorePermissionCheck = Boolean.TRUE.equals(requestDTO.getIgnorePermissionCheck());
            
            // 如果不是管理员且不忽略权限检查，需要验证店铺权限
            if (!isAdmin && !ignorePermissionCheck) {
                List<Long> validShopIds = new ArrayList<>();
                for (Long id : shopIds) {
                    if (shopService.checkShopPermission(userId, id, false)) {
                        validShopIds.add(id);
                    }
                }
                
                // 更新为有权限的店铺ID列表
                requestDTO.setShopIds(validShopIds);
                shopIds = validShopIds;
                
                // 如果没有任何有效的店铺ID，返回权限错误
                if (validShopIds.isEmpty()) {
                    response.setSuccess(false);
                    response.setErrorCode(Integer.valueOf("403"));
                    response.setErrorMsg("您没有权限访问所选店铺的数据");
                    return response;
                }
            }
            
            // 如果是单店铺查询，直接使用原始逻辑
            if (shopIds.size() == 1) {
                Long shopId = shopIds.get(0);
                return getSingleShopShipOrders(shopId, requestDTO);
            }
            
            // 多店铺查询暂不实现
            response.setSuccess(false);
            response.setErrorCode(Integer.valueOf("400"));
            response.setErrorMsg("暂不支持多店铺查询");
            return response;
            
        } catch (Exception e) {
            // 异常处理
            log.error("调用发货单API失败", e);
            response.setSuccess(false);
            response.setErrorCode(Integer.valueOf("500"));
            response.setErrorMsg("调用API失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 针对单个店铺的查询逻辑
     */
    private ShipOrderVO getSingleShopShipOrders(Long shopId, ShipOrderRequestDTO requestDTO) {
        ShipOrderVO response = new ShipOrderVO();
        response.setSuccess(true);
        
        try {
            // 获取店铺信息
            Shop shop = shopService.getShopById(shopId).convertToShop();
            if (shop == null) {
                response.setSuccess(false);
                response.setErrorCode(Integer.valueOf("404"));
                response.setErrorMsg("店铺不存在");
                return response;
            }

            // 调用API获取单店铺数据
            JSONObject result = callTemuApi(shop, requestDTO);
            if (result == null) {
                response.setSuccess(false);
                response.setErrorCode(Integer.valueOf("500"));
                response.setErrorMsg("调用API失败");
                return response;
            }
            
            // 设置响应信息
            response.setSuccess(result.getBoolean("success"));
            response.setErrorCode(result.getInteger("errorCode"));
            response.setErrorMsg(result.getString("errorMsg"));
            
            if (response.getSuccess()) {
                JSONObject resultData = result.getJSONObject("result");
                if (resultData != null) {
                    // 为返回结果添加店铺信息
                    List<JSONObject> orders = resultData.getJSONArray("list").toList(JSONObject.class);
                    if (orders != null) {
                        for (JSONObject order : orders) {
                            order.put("shopId", shopId);
                            order.put("shopName", shop.getShopName());
                            order.put("shopRemark", shop.getRemark());
                        }
                    }
                }
                response.setResult(resultData);
            }
            
            response.setShopId(shopId);
            response.setShopName(shop.getShopName());
            response.setShopRemark(shop.getRemark());
            
            return response;
        } catch (Exception e) {
            log.error("调用发货单API失败", e);
            response.setSuccess(false);
            response.setErrorCode(Integer.valueOf("500"));
            response.setErrorMsg("调用API失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 调用TemuAPI
     */
    private JSONObject callTemuApi(Shop shop, ShipOrderRequestDTO requestDTO) {
        // 创建CommonParams对象
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken(shop.getAccessToken());
        commonParams.setType("bg.shiporderv2.get");
        commonParams.setAppKey(shop.getApiKey());
        commonParams.setAppSecret(shop.getApiSecret());

        // 创建业务参数Map
        HashMap<String, Object> businessParams = new HashMap<>();
        
        // 添加必要的业务参数
        businessParams.put("timestamp", String.valueOf(Time.time()));
        
        // 可选参数
        addParameterIfPresent(businessParams, "pageNo", requestDTO.getPageNo());
        addParameterIfPresent(businessParams, "pageSize", requestDTO.getPageSize());
        addParameterIfPresent(businessParams, "subPurchaseOrderSnList", requestDTO.getSubPurchaseOrderSnList());
        
        // 处理发货单号的兼容性
        if (requestDTO.getDeliveryOrderSnList() != null && !requestDTO.getDeliveryOrderSnList().isEmpty()) {
            addParameterIfPresent(businessParams, "deliveryOrderSnList", requestDTO.getDeliveryOrderSnList());
        } else if (requestDTO.getShipOrderSnList() != null && !requestDTO.getShipOrderSnList().isEmpty()) {
            addParameterIfPresent(businessParams, "shipOrderSnList", requestDTO.getShipOrderSnList());
        }
        
        addParameterIfPresent(businessParams, "expressDeliverySnList", requestDTO.getExpressDeliverySnList());
        addParameterIfPresent(businessParams, "expressWeightFeedbackStatus", requestDTO.getExpressWeightFeedbackStatus());
        addParameterIfPresent(businessParams, "productSkcIdList", requestDTO.getProductSkcIdList());
        addParameterIfPresent(businessParams, "deliverTimeFrom", requestDTO.getDeliverTimeFrom());
        addParameterIfPresent(businessParams, "deliverTimeTo", requestDTO.getDeliverTimeTo());
        addParameterIfPresent(businessParams, "urgencyType", requestDTO.getUrgencyType());
        addParameterIfPresent(businessParams, "subWarehouseIdList", requestDTO.getSubWarehouseIdList());
        addParameterIfPresent(businessParams, "targetReceiveAddress", requestDTO.getTargetReceiveAddress());
        addParameterIfPresent(businessParams, "targetDeliveryAddress", requestDTO.getTargetDeliveryAddress());
        addParameterIfPresent(businessParams, "isCustomProduct", requestDTO.getIsCustomProduct());
        addParameterIfPresent(businessParams, "latestFeedbackStatusList", requestDTO.getLatestFeedbackStatusList());
        addParameterIfPresent(businessParams, "inventoryRegion", requestDTO.getInventoryRegion());
        addParameterIfPresent(businessParams, "skcExtCodeList", requestDTO.getSkcExtCodeList());
        
        // 添加商品条码样式参数，确保使用默认值0
        if (requestDTO.getProductLabelCodeStyle() != null) {
            businessParams.put("productLabelCodeStyle", requestDTO.getProductLabelCodeStyle());
        } else {
            businessParams.put("productLabelCodeStyle", 0);
        }
        
        // 添加isJit参数，支持JIT发货单查询
        addParameterIfPresent(businessParams, "isJit", requestDTO.getIsJit());
        // 添加status参数，支持状态筛选
        addParameterIfPresent(businessParams, "status", requestDTO.getStatus());
        // 添加onlyTaxWarehouseWaitApply参数，支持待税仓申领订单筛选
        addParameterIfPresent(businessParams, "onlyTaxWarehouseWaitApply", requestDTO.getOnlyTaxWarehouseWaitApply());
        
        // 调用API
        return TemuApiClient.sendRequest(commonParams, businessParams);
    }
} 