<template>
  <div class="navbar">
    <div class="navbar-left">
      <hamburger
        :is-active="appStore.sidebar.opened"
        class="hamburger-container"
        @toggleClick="toggleSidebar"
      />
      
      <!-- 添加标签页组件在hamburger右侧 -->
      <tags-view v-if="appStore.tagsView" class="tags-container" />
    </div>
    
    <div class="right-menu">
      <message-notification class="notification-container" />
      
      <export-task-panel class="export-task-container" />
      
      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper">
          <span class="user-name">{{ userStore.name }}</span>
          <el-icon><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <router-link to="/profile">
              <el-dropdown-item>个人中心</el-dropdown-item>
            </router-link>
            <router-link to="/message/my-message">
              <el-dropdown-item>我的消息</el-dropdown-item>
            </router-link>
            <el-divider style="margin: 5px 0" />
            <el-dropdown-item @click="logout">
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import Hamburger from '@/components/Hamburger/index.vue'
// 移除面包屑组件引入
// import Breadcrumb from '@/components/Breadcrumb/index.vue'
import TagsView from '@/components/TagsView/index.vue'
import MessageNotification from '@/components/MessageNotification/index.vue'
import ExportTaskPanel from '@/components/temu/ExportTaskPanel.vue'
import { useAppStore } from '@/store/modules/app'
import { useUserStore } from '@/store/modules/user'
import { useRouter } from 'vue-router'

const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

function toggleSidebar() {
  appStore.toggleSidebar()
}

function logout() {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logout().then(() => {
      router.push('/login')
    })
  })
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 60px;
  overflow: hidden;
  position: relative;
  background: #ffffff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .navbar-left {
    display: flex;
    align-items: center;
    width: calc(100% - 250px); // 调整宽度留出右侧菜单的空间
  }
  
  .hamburger-container {
    line-height: 60px;
    height: 100%;
    cursor: pointer;
    transition: background 0.3s;
    padding: 0 15px;
    
    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }
  
  .tags-container {
    height: 100%;
    flex: 1;
  }
  
  .right-menu {
    display: flex;
    align-items: center;
    margin-right: 20px;
    
    .notification-container {
      margin-right: 15px;
    }
    
    .export-task-container {
      margin-right: 15px;
    }
    
    .avatar-container {
      
      .avatar-wrapper {
        display: flex;
        align-items: center;
        cursor: pointer;
        
        .user-name {
          font-size: 14px;
          color: #333;
          margin-right: 5px;
        }
      }
    }
  }
}
</style> 