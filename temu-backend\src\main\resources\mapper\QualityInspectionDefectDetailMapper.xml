<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.quality.mapper.QualityInspectionDefectDetailMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.xiao.temu.modules.quality.entity.QualityInspectionDefectDetail">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="qc_bill_id" property="qcBillId" />
        <result column="product_sku_id" property="productSkuId" />
        <result column="product_skc_id" property="productSkcId" />
        <result column="flaw_name_desc" property="flawNameDesc" />
        <result column="remark" property="remark" />
        <result column="attachments" property="attachments" />
        <result column="sync_time" property="syncTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 插入或更新质检不合格详情数据 -->
    <insert id="insertOrUpdate" parameterType="com.xiao.temu.modules.quality.entity.QualityInspectionDefectDetail">
        INSERT INTO quality_inspection_defect_detail (
            shop_id, qc_bill_id, product_sku_id, product_skc_id, flaw_name_desc, remark,
            attachments, sync_time, create_time, update_time
        ) VALUES (
            #{shopId}, #{qcBillId}, #{productSkuId}, #{productSkcId}, #{flawNameDesc}, #{remark},
            #{attachments}, #{syncTime}, #{createTime}, #{updateTime}
        )
        ON DUPLICATE KEY UPDATE
            product_skc_id = VALUES(product_skc_id),
            flaw_name_desc = VALUES(flaw_name_desc),
            remark = VALUES(remark),
            attachments = VALUES(attachments),
            sync_time = VALUES(sync_time),
            update_time = VALUES(update_time)
    </insert>

    <!-- 批量插入或更新质检不合格详情数据 -->
    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO quality_inspection_defect_detail (
            shop_id, qc_bill_id, product_sku_id, product_skc_id, flaw_name_desc, remark,
            attachments, sync_time, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.shopId}, #{item.qcBillId}, #{item.productSkuId}, #{item.productSkcId}, #{item.flawNameDesc}, #{item.remark},
            #{item.attachments}, #{item.syncTime}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            product_skc_id = VALUES(product_skc_id),
            flaw_name_desc = VALUES(flaw_name_desc),
            remark = VALUES(remark),
            attachments = VALUES(attachments),
            sync_time = VALUES(sync_time),
            update_time = VALUES(update_time)
    </insert>

    <!-- 根据店铺ID和质检单ID查询质检不合格详情数据 -->
    <select id="selectByShopIdAndQcBillId" resultMap="BaseResultMap">
        SELECT * FROM quality_inspection_defect_detail
        WHERE shop_id = #{shopId} AND qc_bill_id = #{qcBillId}
    </select>

    <!-- 根据店铺ID查询已存在的质检单ID列表 -->
    <select id="selectExistingQcBillIdsByShopId" resultType="java.lang.Long">
        SELECT DISTINCT qc_bill_id FROM quality_inspection_defect_detail
        WHERE shop_id = #{shopId}
    </select>

    <!-- 根据店铺ID删除质检不合格详情数据 -->
    <delete id="deleteByShopId" parameterType="java.util.Map">
        DELETE FROM quality_inspection_defect_detail
        WHERE shop_id = #{shopId}
    </delete>

</mapper> 