package com.xiao.temu.security.service.impl;

import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.system.mapper.SysUserMapper;
import com.xiao.temu.security.exception.NotPermittedException;
import com.xiao.temu.security.jwt.JwtTokenUtil;
import com.xiao.temu.security.model.AuthResult;
import com.xiao.temu.security.model.LoginRequest;
import com.xiao.temu.security.model.UserDetailsImpl;
import com.xiao.temu.security.service.AuthService;
import com.xiao.temu.security.service.PermissionService;
import com.xiao.temu.security.service.UserDetailsServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 认证服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final AuthenticationManager authenticationManager;
    private final JwtTokenUtil jwtTokenUtil;
    private final UserDetailsServiceImpl userDetailsService;
    private final PermissionService permissionService;
    private final SysUserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    private final RedisTemplate<String, Object> redisTemplate;

    @Value("${jwt.expiration}")
    private long expiration;

    @Value("${jwt.tokenPrefix}")
    private String tokenPrefix;

    // Redis Token黑名单前缀
    private static final String TOKEN_BLACKLIST_PREFIX = "temu:token:blacklist:";

    @Override
    public AuthResult login(LoginRequest loginRequest) {
        // 进行身份验证
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        loginRequest.getUsername(),
                        loginRequest.getPassword()
                )
        );

        // 设置认证信息到上下文
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 获取UserDetails
        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        
        // 确保用户ID可以获取到
        Long userId = null;
        if (userDetails instanceof UserDetailsImpl) {
            userId = ((UserDetailsImpl) userDetails).getUserId();
        } else {
            log.warn("无法识别的UserDetails实现类: {}", userDetails.getClass().getName());
            throw new NotPermittedException("无法获取用户ID");
        }

        // 生成JWT令牌
        String token = jwtTokenUtil.generateToken(userDetails);

        // 构建认证结果
        return buildAuthResult(userDetails, userId, token);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuthResult register(String username, String password, String nickName) {
        // 检查用户名是否已存在
        SysUser existUser = userMapper.selectUserByUsername(username);
        if (existUser != null) {
            throw new RuntimeException("用户名已存在");
        }

        // 创建新用户
        SysUser user = new SysUser();
        user.setUsername(username);
        user.setPassword(passwordEncoder.encode(password));
        user.setNickName(nickName);
        user.setStatus("0"); // 0正常 1禁用
        user.setCreateTime(new Date());
        userMapper.insert(user);

        // 加载用户信息并生成token
        UserDetails userDetails = userDetailsService.loadUserByUsername(username);
        
        // 获取用户ID
        Long userId = null;
        if (userDetails instanceof UserDetailsImpl) {
            userId = ((UserDetailsImpl) userDetails).getUserId();
        } else {
            log.warn("无法识别的UserDetails实现类: {}", userDetails.getClass().getName());
            throw new NotPermittedException("无法获取用户ID");
        }
        
        String token = jwtTokenUtil.generateToken(userDetails);

        // 构建认证结果
        return buildAuthResult(userDetails, userId, token);
    }

    @Override
    public AuthResult refreshToken(String oldToken) {
        // 检查token是否在黑名单中
        if (isTokenBlacklisted(oldToken)) {
            throw new NotPermittedException("无效的令牌");
        }

        String username = jwtTokenUtil.getUsernameFromToken(oldToken);
        UserDetails userDetails = userDetailsService.loadUserByUsername(username);

        // 验证旧token是否有效
        if (!jwtTokenUtil.validateToken(oldToken, userDetails)) {
            throw new NotPermittedException("无效的令牌");
        }

        // 生成新token
        String token = jwtTokenUtil.refreshToken(oldToken);

        // 获取用户ID
        Long userId = null;
        if (userDetails instanceof UserDetailsImpl) {
            userId = ((UserDetailsImpl) userDetails).getUserId();
        } else {
            log.warn("无法识别的UserDetails实现类: {}", userDetails.getClass().getName());
            throw new NotPermittedException("无法获取用户ID");
        }

        // 构建认证结果
        return buildAuthResult(userDetails, userId, token);
    }

    @Override
    public AuthResult getCurrentUser() {
        // 获取当前认证信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !(authentication.getPrincipal() instanceof UserDetails)) {
            throw new NotPermittedException("用户未登录");
        }

        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        
        // 确保用户ID可以获取到
        Long userId = null;
        if (userDetails instanceof UserDetailsImpl) {
            userId = ((UserDetailsImpl) userDetails).getUserId();
        } else {
            log.warn("无法识别的UserDetails实现类: {}", userDetails.getClass().getName());
            throw new NotPermittedException("无法获取用户ID");
        }

        // 获取昵称
        String nickName = null;
        if (userDetails instanceof UserDetailsImpl) {
            nickName = ((UserDetailsImpl) userDetails).getNickName();
        } else {
            // 如果无法直接获取昵称，可以从数据库查询
            SysUser user = userMapper.selectById(userId);
            if (user != null) {
                nickName = user.getNickName();
            } else {
                nickName = userDetails.getUsername();
            }
        }

        // 获取用户角色和权限
        List<String> roles = permissionService.getRolesByUserId(userId);
        List<String> permissions = permissionService.getPermissionsByUserId(userId);
        
        // 获取角色与权限的映射关系
        Map<String, List<String>> rolePermissionsMap = permissionService.getRolePermissionsByUserId(userId);
        // 转换为数组形式
        Map<String, String[]> rolePermissionsArray = rolePermissionsMap.entrySet().stream()
                .collect(java.util.stream.Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> entry.getValue().toArray(new String[0])
                ));
        
        // 获取角色标识与角色名称的映射关系
        Map<String, String> roleNamesMap = permissionService.getRoleNamesByUserId(userId);

        // 构建认证结果（不包含token）
        return AuthResult.builder()
                .userId(userId)
                .username(userDetails.getUsername())
                .nickName(nickName)
                .roles(roles.toArray(new String[0]))
                .permissions(permissions.toArray(new String[0]))
                .rolePermissions(rolePermissionsArray)
                .roleNames(roleNamesMap)
                .build();
    }

    @Override
    public void logout() {
        // 获取当前认证信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !(authentication.getPrincipal() instanceof UserDetails)) {
            return;
        }

        // 获取请求头中的token
        String token = (String) authentication.getCredentials();
        if (token != null) {
            // 将token加入黑名单
            addTokenToBlacklist(token);
        }

        // 清除认证信息
        SecurityContextHolder.clearContext();
    }

    /**
     * 构建认证结果
     */
    private AuthResult buildAuthResult(UserDetails userDetails, Long userId, String token) {
        // 获取用户权限
        List<String> permissions = userDetails.getAuthorities().stream()
                .map(auth -> auth.getAuthority())
                .collect(java.util.stream.Collectors.toList());
        
        // 提取角色信息（假设角色权限以ROLE_开头）
        List<String> roles = permissions.stream()
                .filter(p -> p.startsWith("ROLE_"))
                .map(p -> p.substring(5)) // 去掉ROLE_前缀
                .collect(java.util.stream.Collectors.toList());
        
        // 检查是否有admin角色，如果没有从权限中推断
        if (!roles.contains("admin")) {
            if (permissions.stream().anyMatch(p -> p.startsWith("system:") || p.contains(":all:"))) {
                roles.add("admin");
            }
        }
        
        // 获取角色与权限的映射关系
        Map<String, List<String>> rolePermissionsMap = permissionService.getRolePermissionsByUserId(userId);
        // 转换为数组形式
        Map<String, String[]> rolePermissionsArray = rolePermissionsMap.entrySet().stream()
                .collect(java.util.stream.Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> entry.getValue().toArray(new String[0])
                ));
        
        // 获取角色标识与角色名称的映射关系
        Map<String, String> roleNamesMap = permissionService.getRoleNamesByUserId(userId);
        
        // 构建认证结果
        AuthResult authResult = new AuthResult();
        authResult.setToken(token);
        authResult.setTokenType(tokenPrefix.trim());
        authResult.setExpiration(expiration);
        authResult.setUserId(userId);
        authResult.setUsername(userDetails.getUsername());
        
        // 设置昵称
        if (userDetails instanceof UserDetailsImpl) {
            authResult.setNickName(((UserDetailsImpl) userDetails).getNickName());
        } else {
            // 如果无法直接获取昵称，可以从数据库查询
            SysUser user = userMapper.selectById(userId);
            if (user != null) {
                authResult.setNickName(user.getNickName());
            } else {
                authResult.setNickName(userDetails.getUsername());
            }
        }
        
        // 设置角色和权限
        authResult.setRoles(roles.toArray(new String[0]));
        authResult.setPermissions(permissions.toArray(new String[0]));
        // 设置角色权限映射
        authResult.setRolePermissions(rolePermissionsArray);
        // 设置角色名称映射
        authResult.setRoleNames(roleNamesMap);
        
        return authResult;
    }

    /**
     * 将token加入黑名单
     */
    private void addTokenToBlacklist(String token) {
        try {
            // 获取token过期时间
            Date expiration = jwtTokenUtil.getExpirationDateFromToken(token);
            // 计算剩余过期时间
            long ttl = (expiration.getTime() - System.currentTimeMillis()) / 1000;
            if (ttl > 0) {
                // 将token加入黑名单，有效期为token的剩余过期时间
                redisTemplate.opsForValue().set(TOKEN_BLACKLIST_PREFIX + token, "1", ttl, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("将token加入黑名单失败", e);
        }
    }

    /**
     * 检查token是否在黑名单中
     */
    private boolean isTokenBlacklisted(String token) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(TOKEN_BLACKLIST_PREFIX + token));
        } catch (Exception e) {
            log.error("检查token黑名单失败", e);
            return false;
        }
    }
} 