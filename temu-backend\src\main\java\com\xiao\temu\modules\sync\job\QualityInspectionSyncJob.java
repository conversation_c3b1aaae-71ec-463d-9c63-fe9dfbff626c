package com.xiao.temu.modules.sync.job;

import com.xiao.temu.modules.sync.service.QualityInspectionSyncService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

/**
 * 质检数据同步任务
 */
@Component
@Slf4j
public class QualityInspectionSyncJob extends QuartzJobBean {

    @Autowired
    private QualityInspectionSyncService qualityInspectionSyncService;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        log.info("开始执行质检数据同步任务... 触发时间: {}", context.getFireTime());
        try {
            log.info("调用数据库查询同步任务...");
            String result = qualityInspectionSyncService.executeScheduledSync();
            log.info("质检数据同步任务执行结果: {}", result);
        } catch (Exception e) {
            log.error("质检数据同步任务执行异常", e);
            throw new JobExecutionException(e);
        }
    }
} 