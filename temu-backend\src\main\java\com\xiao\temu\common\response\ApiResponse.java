package com.xiao.temu.common.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 通用API响应类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 消息
     */
    private String message;

    /**
     * 数据
     */
    private T data;

    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success() {
        return ApiResponse.<T>builder()
                .code(200)
                .message("操作成功")
                .build();
    }

    /**
     * 成功响应（带消息）
     */
    public static <T> ApiResponse<T> success(String message) {
        return ApiResponse.<T>builder()
                .code(200)
                .message(message)
                .build();
    }

    /**
     * 成功响应（带数据）
     */
    public static <T> ApiResponse<T> success(T data) {
        // 如果是分页数据，进行特殊处理
        if (data instanceof com.baomidou.mybatisplus.core.metadata.IPage) {
            com.baomidou.mybatisplus.core.metadata.IPage<?> page = (com.baomidou.mybatisplus.core.metadata.IPage<?>) data;
            // 创建包含分页信息的Map
            Map<String, Object> pageData = new HashMap<>();
            pageData.put("records", page.getRecords());
            pageData.put("total", page.getTotal());
            pageData.put("size", page.getSize());
            pageData.put("current", page.getCurrent());
            pageData.put("pages", page.getPages());
            // 强制类型转换返回
            @SuppressWarnings("unchecked")
            T result = (T) pageData;
            return ApiResponse.<T>builder()
                    .code(200)
                    .message("操作成功")
                    .data(result)
                    .build();
        }
        
        return ApiResponse.<T>builder()
                .code(200)
                .message("操作成功")
                .data(data)
                .build();
    }

    /**
     * 成功响应（带消息和数据）
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return ApiResponse.<T>builder()
                .code(200)
                .message(message)
                .data(data)
                .build();
    }

    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> error() {
        return ApiResponse.<T>builder()
                .code(500)
                .message("操作失败")
                .build();
    }

    /**
     * 失败响应（带消息）
     */
    public static <T> ApiResponse<T> error(String message) {
        return ApiResponse.<T>builder()
                .code(500)
                .message(message)
                .build();
    }

    /**
     * 失败响应（带状态码和消息）
     */
    public static <T> ApiResponse<T> error(Integer code, String message) {
        return ApiResponse.<T>builder()
                .code(code)
                .message(message)
                .build();
    }

    /**
     * 自定义响应
     */
    public static <T> ApiResponse<T> custom(Integer code, String message, T data) {
        return ApiResponse.<T>builder()
                .code(code)
                .message(message)
                .data(data)
                .build();
    }
} 