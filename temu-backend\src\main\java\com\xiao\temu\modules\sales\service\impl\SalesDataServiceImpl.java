package com.xiao.temu.modules.sales.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.sales.dto.SalesDataQueryDTO;
import com.xiao.temu.modules.sales.vo.SalesDataVO;
import com.xiao.temu.modules.shop.mapper.ShopMapper;
import com.xiao.temu.modules.sales.service.SalesDataService;
import com.xiao.temu.infrastructure.api.TemuApiClient;
import org.ehcache.shadow.org.terracotta.statistics.Time;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 销售数据服务实现类
 */
@Service
public class SalesDataServiceImpl implements SalesDataService {
    
    private static final Logger logger = LoggerFactory.getLogger(SalesDataServiceImpl.class);
    
    @Autowired
    private ShopMapper shopMapper;

    @Override
    public List<SalesDataVO> getSalesData(SalesDataQueryDTO queryDTO) {
        List<SalesDataVO> resultList = new ArrayList<>();
        
        if (queryDTO.getShopIds() == null || queryDTO.getShopIds().isEmpty()) {
            logger.error("请选择至少一个店铺");
            return resultList;
        }

        // 查询店铺信息
        for (Long shopId : queryDTO.getShopIds()) {
            Shop shop = shopMapper.selectById(shopId);
            if (shop == null) {
                logger.error("店铺不存在: {}", shopId);
                continue;
            }
            
            // 如果店铺被禁用或密钥信息不完整，跳过
            if ("1".equals(shop.getStatus()) || 
                shop.getApiKey() == null || shop.getApiSecret() == null || shop.getAccessToken() == null) {
                SalesDataVO errorVO = new SalesDataVO();
                errorVO.setShopId(shopId);
                errorVO.setShopName(shop.getShopName());
                errorVO.setShopRemark(shop.getRemark());
                errorVO.setSuccess(false);
                errorVO.setErrorMsg("店铺被禁用或API密钥信息不完整");
                resultList.add(errorVO);
                continue;
            }
            
            // 设置API调用参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.goods.salesv2.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());
            
            // 设置业务参数
            Map<String, Object> businessParams = new HashMap<>();
            businessParams.put("timestamp", String.valueOf(Time.time()));
            
            // 添加页码和每页数量
            if (queryDTO.getPageNo() != null) {
                businessParams.put("pageNo", queryDTO.getPageNo().toString());
            } else {
                businessParams.put("pageNo", "1");
            }
            
            if (queryDTO.getPageSize() != null) {
                businessParams.put("pageSize", queryDTO.getPageSize().toString());
            } else {
                businessParams.put("pageSize", "10");
            }
            
            // 添加时间范围
            if (queryDTO.getStartTime() != null && !queryDTO.getStartTime().isEmpty()) {
                businessParams.put("startTime", queryDTO.getStartTime());
            }
            
            if (queryDTO.getEndTime() != null && !queryDTO.getEndTime().isEmpty()) {
                businessParams.put("endTime", queryDTO.getEndTime());
            }
            
            // 添加商品SKU ID列表
            if (queryDTO.getSkuIdList() != null && !queryDTO.getSkuIdList().isEmpty()) {
                businessParams.put("skuIdList", queryDTO.getSkuIdList().toArray(new Long[0]));
            }
            
            // 添加商品状态列表
            if (queryDTO.getSelectStatusList() != null && !queryDTO.getSelectStatusList().isEmpty()) {
                businessParams.put("selectStatusList", queryDTO.getSelectStatusList().toArray(new Integer[0]));
            }
            
            // 添加高级查询参数
            if (queryDTO.getProductName() != null && !queryDTO.getProductName().isEmpty()) {
                businessParams.put("productName", queryDTO.getProductName());
            }
            if (queryDTO.getProductIdList() != null && !queryDTO.getProductIdList().isEmpty()) {
                businessParams.put("productIdList", queryDTO.getProductIdList().toArray(new Long[0]));
            }
            if (queryDTO.getProductSkcIdList() != null && !queryDTO.getProductSkcIdList().isEmpty()) {
                businessParams.put("productSkcIdList", queryDTO.getProductSkcIdList().toArray(new Long[0]));
            }
            if (queryDTO.getSkcExtCodeList() != null && !queryDTO.getSkcExtCodeList().isEmpty()) {
                businessParams.put("skcExtCodeList", queryDTO.getSkcExtCodeList().toArray(new String[0]));
            }
            if (queryDTO.getSkuExtCodeList() != null && !queryDTO.getSkuExtCodeList().isEmpty()) {
                businessParams.put("skuExtCodeList", queryDTO.getSkuExtCodeList().toArray(new String[0]));
            }
            if (queryDTO.getOnSalesDurationOfflineGte() != null) {
                businessParams.put("onSalesDurationOfflineGte", queryDTO.getOnSalesDurationOfflineGte());
            }
            if (queryDTO.getOnSalesDurationOfflineLte() != null) {
                businessParams.put("onSalesDurationOfflineLte", queryDTO.getOnSalesDurationOfflineLte());
            }
            if (queryDTO.getInventoryRegionList() != null && !queryDTO.getInventoryRegionList().isEmpty()) {
                businessParams.put("inventoryRegionList", queryDTO.getInventoryRegionList().toArray(new Integer[0]));
            }
            if (queryDTO.getMinRemanentInventoryNum() != null) {
                businessParams.put("minRemanentInventoryNum", queryDTO.getMinRemanentInventoryNum());
            }
            if (queryDTO.getMaxRemanentInventoryNum() != null) {
                businessParams.put("maxRemanentInventoryNum", queryDTO.getMaxRemanentInventoryNum());
            }
            if (queryDTO.getMinAvailableSaleDays() != null) {
                businessParams.put("minAvailableSaleDays", queryDTO.getMinAvailableSaleDays());
            }
            if (queryDTO.getMaxAvailableSaleDays() != null) {
                businessParams.put("maxAvailableSaleDays", queryDTO.getMaxAvailableSaleDays());
            }
            if (queryDTO.getPictureAuditStatusList() != null && !queryDTO.getPictureAuditStatusList().isEmpty()) {
                businessParams.put("pictureAuditStatusList", queryDTO.getPictureAuditStatusList().toArray(new Integer[0]));
            }
            if (queryDTO.getSupplyStatusList() != null && !queryDTO.getSupplyStatusList().isEmpty()) {
                businessParams.put("supplyStatusList", queryDTO.getSupplyStatusList().toArray(new Integer[0]));
            }
            if (queryDTO.getCloseJitStatus() != null && !queryDTO.getCloseJitStatus().isEmpty()) {
                businessParams.put("closeJitStatus", queryDTO.getCloseJitStatus().toArray(new Integer[0]));
            }
            if (queryDTO.getIsLack() != null) {
                businessParams.put("isLack", queryDTO.getIsLack() ? 1 : 0);
            }
            if (queryDTO.getIsCustomGoods() != null) {
                businessParams.put("isCustomGoods", queryDTO.getIsCustomGoods());
            }
            if (queryDTO.getTodaySaleVolumMin() != null) {
                businessParams.put("todaySaleVolumMin", queryDTO.getTodaySaleVolumMin());
            }
            if (queryDTO.getTodaySaleVolumMax() != null) {
                businessParams.put("todaySaleVolumMax", queryDTO.getTodaySaleVolumMax());
            }
            if (queryDTO.getHotTag() != null) {
                businessParams.put("hotTag", queryDTO.getHotTag());
            }
            if (queryDTO.getPurchaseStockType() != null && !queryDTO.getPurchaseStockType().isEmpty()) {
                try {
                    businessParams.put("purchaseStockType", Integer.parseInt(queryDTO.getPurchaseStockType()));
                } catch (NumberFormatException e) {
                   logger.warn("无法将 purchaseStockType '{}' 转换为整数", queryDTO.getPurchaseStockType());
                }
            }
            if (queryDTO.getStockStatusList() != null && !queryDTO.getStockStatusList().isEmpty()) {
                businessParams.put("stockStatusList", queryDTO.getStockStatusList().toArray(new Integer[0]));
            }
            if (queryDTO.getAvailableProduceNumGreaterThanZero() != null) {
                businessParams.put("availableProduceNumGreaterThanZero", queryDTO.getAvailableProduceNumGreaterThanZero());
            }
            if (queryDTO.getWarehouseGroupIdList() != null && !queryDTO.getWarehouseGroupIdList().isEmpty()) {
                businessParams.put("warehouseGroupIdList", queryDTO.getWarehouseGroupIdList().toArray(new Integer[0]));
            }
            if (queryDTO.getSettlementType() != null) {
                businessParams.put("settlementType", queryDTO.getSettlementType());
            }
            
            // 添加排序参数
            if (queryDTO.getOrderByParam() != null && !queryDTO.getOrderByParam().isEmpty()) {
                businessParams.put("orderByParam", queryDTO.getOrderByParam());
                
                // 如果排序方向参数不为空，则添加排序方向
                if (queryDTO.getOrderByDesc() != null) {
                    businessParams.put("orderByDesc", queryDTO.getOrderByDesc());
                }
            }
            
            try {
                // 调用TEMU API
                JSONObject jsonResult = TemuApiClient.sendRequest(commonParams, businessParams);
                logger.info("店铺[{}]销售数据API调用结果: {}", shop.getShopName(), jsonResult);
                
                SalesDataVO salesDataVO = new SalesDataVO();
                salesDataVO.setShopId(shopId);
                salesDataVO.setShopName(shop.getShopName());
                salesDataVO.setShopRemark(shop.getRemark());
                salesDataVO.setApiResult(jsonResult);
                
                // 检查API调用是否成功
                if (jsonResult != null && jsonResult.getBooleanValue("success") && jsonResult.getInteger("errorCode") == 1000000) {
                    salesDataVO.setSuccess(true);
                    
                    // 解析数据
                    JSONObject data = jsonResult.getJSONObject("result");
                    if (data != null) {
                        salesDataVO.setTotal(data.getLong("total"));
                        JSONArray subOrderList = data.getJSONArray("subOrderList");
                        if (subOrderList != null) {
                            List<JSONObject> dataList = new ArrayList<>();
                            for (int i = 0; i < subOrderList.size(); i++) {
                                dataList.add(subOrderList.getJSONObject(i));
                            }
                            salesDataVO.setDataList(dataList);
                        }
                    }
                } else {
                    salesDataVO.setSuccess(false);
                    String errorMsg = jsonResult != null ? jsonResult.getString("errorMsg") : "API调用失败";
                    salesDataVO.setErrorMsg(errorMsg);
                }
                
                resultList.add(salesDataVO);
            } catch (Exception e) {
                logger.error("调用销售数据API异常: {}", e.getMessage(), e);
                
                SalesDataVO errorVO = new SalesDataVO();
                errorVO.setShopId(shopId);
                errorVO.setShopName(shop.getShopName());
                errorVO.setShopRemark(shop.getRemark());
                errorVO.setSuccess(false);
                errorVO.setErrorMsg("调用API异常: " + e.getMessage());
                resultList.add(errorVO);
            }
        }
        
        return resultList;
    }
} 