package com.xiao.temu.modules.violation.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 店铺违规信息DTO
 */
@Data
public class ShopViolationInfoDTO {
    
    /** 主键ID */
    private Long id;
    
    /** 店铺ID */
    private Long shopId;
    
    /** Temu平台店铺ID */
    private String mallId;
    
    /** 店铺名称 */
    private String shopName;
    
    /** 店铺备注 */
    private String shopRemark;
    
    /** 违规编号 */
    private String punishSn;
    
    /** 备货单号 */
    private String subPurchaseOrderSn;
    
    /** 违规类型代码 */
    private Integer punishTypeCode;
    
    /** 违规类型描述 */
    private String punishTypeDesc;
    
    /** 违规一级类型代码 */
    private Integer punishFirstTypeCode;
    
    /** 违规一级类型描述 */
    private String punishFirstTypeDesc;
    
    /** 违规二级类型描述 */
    private String punishSecondTypeDesc;
    
    /** 违规发起时间戳(毫秒) */
    private Long violationStartTime;
    
    /** 违规发起时间 */
    private LocalDateTime violationTime;
    
    /** 违规金额 */
    private BigDecimal punishAmount;
    
    /** 违规金额币种 */
    private String punishAmountCurrency;
    
    /** 违规状态码 */
    private Integer punishStatus;
    
    /** 违规状态描述 */
    private String punishStatusDesc;
    
    /** 详情查看状态码 */
    private Integer viewDetailsStatus;
    
    /** 倒计时时间戳(毫秒) */
    private Long countdownTime;
    
    /** 同步时间 */
    private LocalDateTime syncTime;
    
    /** 创建时间 */
    private LocalDateTime createTime;
    
    /** 更新时间 */
    private LocalDateTime updateTime;
    
    /** 商品SKU ID */
    private Long productSkuId;
    
    /** 库存数量 */
    private Integer stockQuantity;
    
    /** 缺货数量 */
    private Integer lackQuantity;
    
    /** 不合格数量 */
    private Integer unqualifiedQuantity;
} 