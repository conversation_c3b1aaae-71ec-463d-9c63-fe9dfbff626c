package com.xiao.temu.modules.purchaseorderv.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiao.temu.modules.purchaseorderv.entity.ProductionProgressLog;
import com.xiao.temu.modules.purchaseorderv.mapper.ProductionProgressLogMapper;
import com.xiao.temu.modules.purchaseorderv.service.ProductionProgressLogService;
import com.xiao.temu.modules.purchaseorderv.vo.ProductionProgressLogVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 生产进度日志服务实现类
 */
@Service
public class ProductionProgressLogServiceImpl extends ServiceImpl<ProductionProgressLogMapper, ProductionProgressLog> implements ProductionProgressLogService {

    /**
     * 记录生产进度日志
     *
     * @param shopId 店铺ID
     * @param subPurchaseOrderSn 备货单号
     * @param progressType 进度类型
     * @param operationType 操作类型(1:完成 2:撤销)
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param remarks 备注
     */
    @Override
    public void recordLog(Long shopId, String subPurchaseOrderSn, String progressType,
                         String operationType, Long operatorId, String operatorName, String remarks) {
        // 创建日志记录
        ProductionProgressLog log = new ProductionProgressLog();
        log.setShopId(shopId);
        log.setSubPurchaseOrderSn(subPurchaseOrderSn);
        log.setProgressType(progressType);
        log.setOperationType(operationType);
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setOperationTime(new Date());
        log.setRemarks(remarks);
        log.setCreateTime(new Date());
        
        // 保存日志
        this.save(log);
    }

    /**
     * 获取备货单的生产进度日志
     *
     * @param shopId 店铺ID
     * @param subPurchaseOrderSn 备货单号
     * @return 生产进度日志列表
     */
    @Override
    public List<ProductionProgressLogVO> getLogsByOrderSn(Long shopId, String subPurchaseOrderSn) {
        LambdaQueryWrapper<ProductionProgressLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductionProgressLog::getShopId, shopId)
                .eq(ProductionProgressLog::getSubPurchaseOrderSn, subPurchaseOrderSn)
                .orderByDesc(ProductionProgressLog::getOperationTime);
        
        List<ProductionProgressLog> logList = list(queryWrapper);
        
        // 转换为VO对象
        return logList.stream().map(log -> {
            ProductionProgressLogVO vo = new ProductionProgressLogVO();
            BeanUtils.copyProperties(log, vo);
            return vo;
        }).collect(Collectors.toList());
    }
    
    /**
     * 获取用户的操作日志
     *
     * @param operatorId 操作人ID
     * @param limit 限制返回记录数
     * @return 用户操作日志列表
     */
    @Override
    public List<ProductionProgressLogVO> getLogsByOperator(Long operatorId, Integer limit) {
        LambdaQueryWrapper<ProductionProgressLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductionProgressLog::getOperatorId, operatorId)
                .orderByDesc(ProductionProgressLog::getOperationTime)
                .last(limit != null && limit > 0, "LIMIT " + limit);
        
        List<ProductionProgressLog> logList = list(queryWrapper);
        
        // 转换为VO对象
        return logList.stream().map(log -> {
            ProductionProgressLogVO vo = new ProductionProgressLogVO();
            BeanUtils.copyProperties(log, vo);
            return vo;
        }).collect(Collectors.toList());
    }
} 