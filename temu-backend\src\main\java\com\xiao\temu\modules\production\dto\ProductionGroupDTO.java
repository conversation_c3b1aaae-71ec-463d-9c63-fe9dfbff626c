package com.xiao.temu.modules.production.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产组数据传输对象
 */
@Data
public class ProductionGroupDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 生产组ID
     */
    private Long groupId;

    /**
     * 生产组名称
     */
    private String groupName;

    /**
     * 负责人ID
     */
    private Long leaderId;

    /**
     * 负责人姓名
     */
    private String leaderName;

    /**
     * 状态（0正常 1禁用）
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;
    
    /**
     * 成员数量
     */
    private Integer memberCount;
} 