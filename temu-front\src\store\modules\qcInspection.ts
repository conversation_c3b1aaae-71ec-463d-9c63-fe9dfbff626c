import { defineStore } from 'pinia'
import request from '@/utils/request'
import { ElMessage } from 'element-plus'
import type { 
  QualityInspectionRequestParams, 
  QualityInspectionResponse 
} from '@/types/qcInspection'

export interface QualityInspectionState {
  loading: boolean
  shopId: number | null
  timeRange: [string, string] | null
}

export const useQualityInspectionStore = defineStore('qualityInspection', {
  state: (): QualityInspectionState => ({
    loading: false,
    shopId: null,
    timeRange: null
  }),
  
  actions: {
    // 设置加载状态
    setLoading(loading: boolean) {
      this.loading = loading
    },
    
    // 设置店铺ID
    setShopId(shopId: number | null) {
      this.shopId = shopId
    },
    
    // 设置时间范围
    setTimeRange(timeRange: [string, string] | null) {
      this.timeRange = timeRange
    },
    
    // 重置查询条件
    resetQuery() {
      this.shopId = null
      this.timeRange = null
    },
    
    // 获取用户店铺列表
    async getUserShops() {
      try {
        this.loading = true
        const { data } = await request({
          url: '/temu/qualityInspection/shops',
          method: 'get'
        })
        return data
      } catch (error) {
        console.error('获取店铺列表失败:', error)
        ElMessage.error('获取店铺列表失败')
        return []
      } finally {
        this.loading = false
      }
    },
    
    // 获取抽检结果明细
    async getQualityInspectionList(params: QualityInspectionRequestParams): Promise<QualityInspectionResponse> {
      try {
        this.loading = true
        const { data } = await request({
          url: '/temu/qualityInspection/details',
          method: 'post',
          data: params
        })
        return data
      } catch (error) {
        console.error('获取抽检结果明细失败:', error)
        ElMessage.error('获取抽检结果明细失败')
        return {
          success: false,
          errorMsg: '获取抽检结果明细失败'
        }
      } finally {
        this.loading = false
      }
    }
  }
}) 