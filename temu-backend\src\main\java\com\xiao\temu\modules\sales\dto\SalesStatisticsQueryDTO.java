package com.xiao.temu.modules.sales.dto;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 销售统计查询DTO
 */
@Data
public class SalesStatisticsQueryDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 店铺ID列表
     */
    private List<Long> shopIds;
    
    /**
     * 开始日期
     */
    private LocalDate startDate;
    
    /**
     * 结束日期
     */
    private LocalDate endDate;
} 