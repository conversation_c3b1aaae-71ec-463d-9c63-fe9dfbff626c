package com.xiao.temu.config.web;

import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.format.datetime.standard.DateTimeFormatterRegistrar;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;

import java.time.format.DateTimeFormatter;

/**
 * Web MVC配置类
 * 用于配置LocalDateTime等日期时间类型的格式化
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    /**
     * 添加日期时间格式化器
     * 解决前端传入日期无法转换为LocalDateTime的问题
     * 支持多种日期时间格式
     * 
     * @param registry 格式注册器
     */
    @Override
    public void addFormatters(FormatterRegistry registry) {
        DateTimeFormatterRegistrar registrar = new DateTimeFormatterRegistrar();
        
        // 设置日期格式 - 支持ISO标准格式 (yyyy-MM-dd)
        registrar.setDateFormatter(DateTimeFormatter.ISO_DATE);
        
        // 设置日期时间格式 - 支持ISO标准格式，既支持带T的格式又支持不带T的格式
        // 例如：2025-04-04T00:00:00 和 2025-04-04 00:00:00
        registrar.setDateTimeFormatter(DateTimeFormatter.ISO_DATE_TIME);
        
        // 注册到转换服务中
        registrar.registerFormatters(registry);
    }
    
    /**
     * 添加资源处理程序
     * 确保API路径不会被当做静态资源处理
     * 
     * @param registry 资源处理程序注册表
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 仅配置静态资源路径，明确排除API路径
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
                
        // 其他需要的资源映射可以在这里添加
    }
} 