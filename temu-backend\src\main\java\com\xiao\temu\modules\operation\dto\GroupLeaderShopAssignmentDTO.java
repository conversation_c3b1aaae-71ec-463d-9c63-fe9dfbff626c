package com.xiao.temu.modules.operation.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 组长店铺分配DTO
 */
@Data
public class GroupLeaderShopAssignmentDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 运营组ID
     */
    private Long groupId;

    /**
     * 运营组名称
     */
    private String groupName;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺Temu ID
     */
    private String shopTemuId;

    /**
     * 被分配用户ID
     */
    private Long userId;

    /**
     * 被分配用户名称
     */
    private String userName;

    /**
     * 被分配用户昵称
     */
    private String nickName;

    /**
     * 权限类型(0只读1读写)
     */
    private String permissionType;

    /**
     * 权限类型名称
     */
    private String permissionTypeName;

    /**
     * 分配时间
     */
    private Date assignTime;

    /**
     * 分配人ID(组长)
     */
    private Long assignBy;

    /**
     * 分配人姓名
     */
    private String assignByName;

    /**
     * 状态(0正常1禁用)
     */
    private String status;
} 