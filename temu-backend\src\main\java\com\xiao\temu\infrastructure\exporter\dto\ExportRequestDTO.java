package com.xiao.temu.infrastructure.exporter.dto;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 导出请求DTO
 */
@Data
public class ExportRequestDTO {
    
    /**
     * 导出方式：all-全部, page-分页, selected-选中
     */
    @NotEmpty(message = "导出方式不能为空")
    private String exportType;
    
    /**
     * 当前页码，导出方式为page时必填
     */
    private Integer pageNum;
    
    /**
     * 每页条数，导出方式为page时必填
     */
    private Integer pageSize;
    
    /**
     * 选中的记录ID列表，导出方式为selected时必填
     */
    private List<Long> selectedIds;
    
    /**
     * 查询参数
     */
    private Map<String, Object> queryParams;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 工作表名
     */
    private String sheetName;
} 