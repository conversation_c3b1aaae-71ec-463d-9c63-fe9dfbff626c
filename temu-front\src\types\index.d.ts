import 'vue-router'

// 扩展RouteRecordRaw类型
declare module 'vue-router' {
  interface RouteRecordRaw {
    alwaysShow?: boolean
    hidden?: boolean
    children?: RouteRecordRaw[]
    meta?: {
      title?: string
      icon?: string
      roles?: string[]
      hidden?: boolean
      activeMenu?: string
      affix?: boolean
      [key: string]: any
    }
  }
}

// 路径模块声明
declare module 'path-browserify'; 