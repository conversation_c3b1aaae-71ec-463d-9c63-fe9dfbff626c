<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>备货单通知配置管理</span>
          <div class="right-menu">
            <el-button
              type="primary"
              plain
              icon="Refresh"
              @click="fetchConfigList"
            >
              刷新列表
            </el-button>
          </div>
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="configList"
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="80"
        />
        <el-table-column 
          prop="notificationName"
          label="通知名称"
          width="180"
        />
        <el-table-column
          label="通知类型"
          width="150"
        >
          <template #default="scope">
            <notification-type-tag :type="scope.row.notificationType" />
          </template>
        </el-table-column>
        <el-table-column
          label="通知配置"
        >
          <template #default="scope">
            <p>
              <span>检查间隔：</span>
              <el-tag size="small" type="info">{{ scope.row.checkInterval }}小时</el-tag>
            </p>
            <p v-if="scope.row.notificationType <= 2">
              <span>JIT触发小时：</span>
              <el-tag size="small" type="warning">{{ scope.row.jitTriggerHours }}小时</el-tag>
            </p>
            <p v-else>
              <span>普通触发天数：</span>
              <el-tag size="small" type="warning">{{ scope.row.normalTriggerDays }}天</el-tag>
            </p>
            <p>
              <span>通知次数上限：</span>
              <el-tag size="small" type="success">{{ scope.row.maxNotifyCount }}次</el-tag>
              <span style="margin-left: 10px">通知间隔：</span>
              <el-tag size="small" type="success">{{ scope.row.notifyIntervalHours }}小时</el-tag>
            </p>
          </template>
        </el-table-column>
        <el-table-column
          prop="templateCode"
          label="模板代码"
          width="150"
        >
          <template #default="scope">
            <el-tag size="small">{{ scope.row.templateCode }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="是否启用"
          width="100"
        >
          <template #default="scope">
            <el-switch
              :model-value="scope.row.enabled"
              @update:model-value="(val) => handleSwitchChange(scope.row, val)"
              inline-prompt
              :active-value="true"
              :inactive-value="false"
              active-text="是"
              inactive-text="否"
              :disabled="!hasPermission('purchase:notification:config:toggle')"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="120"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleEdit(scope.row)"
              v-if="hasPermission('purchase:notification:config:edit')"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 编辑配置弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="编辑通知配置"
      width="600px"
    >
      <el-form
        ref="configFormRef"
        :model="configForm"
        :rules="configRules"
        label-width="140px"
      >
        <el-form-item label="通知名称" prop="notificationName">
          <el-input v-model="configForm.notificationName" placeholder="请输入通知名称" />
        </el-form-item>
        
        <el-form-item label="通知类型">
          <notification-type-tag :type="configForm.notificationType" />
        </el-form-item>

        <el-form-item label="是否启用" prop="enabled">
          <el-switch
            v-model="configForm.enabled"
            inline-prompt
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        
        <el-form-item label="检查间隔(小时)" prop="checkInterval">
          <el-input-number v-model="configForm.checkInterval" :min="1" :max="24" />
        </el-form-item>
        
        <template v-if="configForm.notificationType <= 2">
          <el-form-item label="JIT触发小时数" prop="jitTriggerHours">
            <el-input-number v-model="configForm.jitTriggerHours" :min="1" :max="72" />
            <span class="form-tips">到期前/后多少小时触发</span>
          </el-form-item>
        </template>
        
        <template v-else>
          <el-form-item label="普通触发天数" prop="normalTriggerDays">
            <el-input-number v-model="configForm.normalTriggerDays" :min="1" :max="30" />
            <span class="form-tips">创建/发货后多少天触发</span>
          </el-form-item>
        </template>
        
        <el-form-item label="最大通知次数" prop="maxNotifyCount">
          <el-input-number v-model="configForm.maxNotifyCount" :min="1" :max="10" />
        </el-form-item>
        
        <el-form-item label="通知间隔(小时)" prop="notifyIntervalHours">
          <el-input-number v-model="configForm.notifyIntervalHours" :min="1" :max="48" />
        </el-form-item>
        
        <el-form-item label="消息模板代码" prop="templateCode">
          <el-input v-model="configForm.templateCode" placeholder="请输入消息模板代码" />
        </el-form-item>
        
        <el-form-item v-if="configForm.createTime" label="创建时间">
          <span>{{ configForm.createTime }}</span>
        </el-form-item>
        
        <el-form-item v-if="configForm.updateTime" label="更新时间">
          <span>{{ configForm.updateTime }}</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup name="NotificationConfig">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { getNotificationConfigList, getNotificationConfig, updateNotificationConfig, toggleNotificationConfig } from '@/api/notification'
import NotificationTypeTag from '@/components/NotificationTypeTag/index.vue'
import type { NotificationConfig } from '@/types/notification'
import { hasPermission } from '@/utils/permission'

// 配置列表
const configList = ref<NotificationConfig[]>([])
const loading = ref(false)
// 添加加载标记，防止初始加载时触发change事件
const isLoading = ref(true)

// 编辑弹窗
const dialogVisible = ref(false)
const configFormRef = ref<FormInstance>()
const submitLoading = ref(false)

// 配置表单
const configForm = reactive<NotificationConfig>({
  id: 0,
  notificationType: 1,
  notificationName: '',
  enabled: true,
  checkInterval: 1,
  normalTriggerDays: 5,
  jitTriggerHours: 24,
  maxNotifyCount: 3,
  notifyIntervalHours: 24,
  templateCode: '',
  createTime: '',
  updateTime: ''
})

// 表单验证规则
const configRules = reactive<FormRules>({
  notificationName: [
    { required: true, message: '请输入通知名称', trigger: 'blur' }
  ],
  checkInterval: [
    { required: true, message: '请输入检查间隔', trigger: 'blur' }
  ],
  maxNotifyCount: [
    { required: true, message: '请输入最大通知次数', trigger: 'blur' }
  ],
  notifyIntervalHours: [
    { required: true, message: '请输入通知间隔', trigger: 'blur' }
  ],
  templateCode: [
    { required: true, message: '请输入消息模板代码', trigger: 'blur' }
  ]
})

// 获取通知配置列表
const fetchConfigList = async () => {
  isLoading.value = true // 设置正在加载标记
  loading.value = true
  try {
    const res = await getNotificationConfigList()
    configList.value = res.data
  } catch (error) {
    console.error('获取通知配置列表失败', error)
    ElMessage.error('获取通知配置列表失败')
  } finally {
    loading.value = false
    // 数据加载完成后，延迟设置isLoading为false，确保不触发change事件
    setTimeout(() => {
      isLoading.value = false
    }, 100)
  }
}

// 切换switch状态处理
const handleSwitchChange = async (row: NotificationConfig, value: boolean) => {
  // 如果是加载中，不处理change事件
  if (isLoading.value) return
  
  // 先乐观更新UI
  row.enabled = value
  
  try {
    await toggleNotificationConfig(row.id, value)
    ElMessage.success(`${value ? '启用' : '禁用'}成功`)
  } catch (error) {
    console.error('切换通知配置状态失败', error)
    ElMessage.error('切换通知配置状态失败')
    // 回滚状态
    row.enabled = !value
  }
}

// 保留此方法用于兼容性，可以在后续版本中移除
const handleToggleStatus = async (row: NotificationConfig) => {
  // 如果是加载中，不处理change事件
  if (isLoading.value) return
  
  // 调用新方法
  await handleSwitchChange(row, row.enabled)
}

// 编辑通知配置
const handleEdit = async (row: NotificationConfig) => {
  try {
    // 获取最新的配置详情
    const res = await getNotificationConfig(row.id)
    // 复制到表单
    Object.assign(configForm, res.data)
    dialogVisible.value = true
  } catch (error) {
    console.error('获取通知配置详情失败', error)
    ElMessage.error('获取通知配置详情失败')
  }
}

// 提交表单
const submitForm = async () => {
  if (!configFormRef.value) return
  
  await configFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    submitLoading.value = true
    try {
      await updateNotificationConfig(configForm)
      ElMessage.success('更新成功')
      dialogVisible.value = false
      // 重新获取列表
      fetchConfigList()
    } catch (error) {
      console.error('更新通知配置失败', error)
      ElMessage.error('更新通知配置失败')
    } finally {
      submitLoading.value = false
    }
  })
}

onMounted(() => {
  fetchConfigList()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.right-menu {
  display: flex;
  gap: 10px;
}

.form-tips {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}
</style> 