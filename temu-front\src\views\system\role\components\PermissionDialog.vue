<template>
  <div>
    <el-dialog
      title="分配权限"
      v-model="visible"
      width="650px"
      append-to-body
      @close="cancel"
    >
      <el-form :model="form" label-width="80px">
        <el-form-item label="角色名称">
          <el-input v-model="form.roleName" disabled />
        </el-form-item>
        <el-form-item label="权限字符">
          <el-input v-model="form.roleKey" disabled />
        </el-form-item>
        
        <!-- 树操作按钮 -->
        <div class="tree-controls">
          <el-button type="primary" link @click="handleExpandAll">
            <el-icon><Expand /></el-icon> 展开所有
          </el-button>
          <el-button type="primary" link @click="handleCollapseAll">
            <el-icon><Fold /></el-icon> 折叠所有
          </el-button>
        </div>
        
        <el-form-item label="权限分配">
          <div class="tree-container">
            <el-tree
              v-if="menuOptions.length > 0"
              ref="menuTreeRef"
              :data="menuOptions"
              show-checkbox
              node-key="menuId"
              :props="defaultProps"
              :check-strictly="false"
              :default-expand-all="expandAll"
              class="permission-tree"
            />
            <div v-else class="empty-text">加载中，请稍候</div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import type { ElTree } from 'element-plus'
import { ElMessage } from 'element-plus'
import { getMenuTree } from '@/api/menu'
import { getRole, assignPermissions } from '@/api/role'
import { Expand, Fold } from '@element-plus/icons-vue'

// 定义组件触发的事件
const emit = defineEmits(['refreshList'])

// 弹窗可见状态
const visible = ref(false)
// 菜单树引用
const menuTreeRef = ref<InstanceType<typeof ElTree> | null>(null)
// 菜单树数据
const menuOptions = ref<any[]>([])
// 存储原始的选中菜单ID
const originalCheckedMenuIds = ref<number[]>([])
// 控制是否展开所有节点
const expandAll = ref(false)
// 当前编辑的角色信息
const form = reactive({
  roleId: undefined,
  roleName: '',
  roleKey: '',
  menuIds: [] as number[]
})

// 树形控件属性配置
const defaultProps = {
  children: 'children',
  label: 'menuName'
}

// 处理展开所有节点
const handleExpandAll = () => {
  // 保存当前选中的节点
  let checkedKeys: number[] = []
  if (menuTreeRef.value) {
    checkedKeys = menuTreeRef.value.getCheckedKeys() as number[]
  }
  
  expandAll.value = true
  // 重新渲染树
  const currentTreeData = [...menuOptions.value]
  menuOptions.value = []
  
  nextTick(() => {
    menuOptions.value = currentTreeData
    // 等待树重新渲染后恢复选中状态
    nextTick(() => {
      if (menuTreeRef.value && checkedKeys.length > 0) {
        menuTreeRef.value.setCheckedKeys(checkedKeys)
      }
    })
  })
}

// 处理折叠所有节点
const handleCollapseAll = () => {
  // 保存当前选中的节点
  let checkedKeys: number[] = []
  if (menuTreeRef.value) {
    checkedKeys = menuTreeRef.value.getCheckedKeys() as number[]
  }
  
  expandAll.value = false
  // 重新渲染树
  const currentTreeData = [...menuOptions.value]
  menuOptions.value = []
  
  nextTick(() => {
    menuOptions.value = currentTreeData
    // 等待树重新渲染后恢复选中状态
    nextTick(() => {
      if (menuTreeRef.value && checkedKeys.length > 0) {
        menuTreeRef.value.setCheckedKeys(checkedKeys)
      }
    })
  })
}

// 取消按钮
const cancel = () => {
  visible.value = false
  // 清空数据，避免缓存问题
  form.roleId = undefined
  form.roleName = ''
  form.roleKey = ''
  form.menuIds = []
  originalCheckedMenuIds.value = []
  // 清空树数据
  menuOptions.value = []
  // 重置展开状态
  expandAll.value = false
}

// 获取菜单树数据
const getMenuTreeData = async () => {
  try {
    const res = await getMenuTree()
    menuOptions.value = res.data || []
  } catch (error) {
    console.error('获取菜单树失败', error)
    menuOptions.value = []
  }
}

// 获取角色菜单权限
const getRoleMenuPermissions = async (roleId: number) => {
  try {
    const res = await getRole(roleId)
    const menuIds = res.data.menuIds || []
    form.menuIds = menuIds
    // 保存原始选中的ID
    originalCheckedMenuIds.value = [...menuIds]
    
    // 等待DOM更新后设置选中状态
    await nextTick()
    // 先清空所有已选项，确保不会保留之前的选择状态
    menuTreeRef.value?.setCheckedKeys([])
    
    // 在父子联动模式下，只需要选中最底层的叶子节点，父节点会自动处理
    if (menuTreeRef.value) {
      const leafMenuIds = filterLeafMenuIds(menuOptions.value, menuIds)
      menuTreeRef.value.setCheckedKeys(leafMenuIds)
    }
  } catch (error) {
    console.error('获取角色权限失败', error)
  }
}

// 过滤出叶子节点ID（没有子节点或子节点未被选中的节点）
const filterLeafMenuIds = (menus: any[], selectedIds: number[]): number[] => {
  const result: number[] = []
  
  const traverse = (nodes: any[]) => {
    for (const node of nodes) {
      // 如果该节点在选中列表中
      if (selectedIds.includes(node.menuId)) {
        // 如果没有子节点或者没有子节点被选中，那么这是一个叶子节点
        const hasSelectedChildren = node.children && 
                                   node.children.some((child: any) => 
                                     selectedIds.includes(child.menuId))
        
        if (!node.children || !hasSelectedChildren) {
          result.push(node.menuId)
        }
      }
      
      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    }
  }
  
  traverse(menus)
  return result
}

// 打开对话框
const openDialog = async (role: any) => {
  // 重置表单状态
  form.roleId = role.roleId
  form.roleName = role.roleName
  form.roleKey = role.roleKey
  form.menuIds = []
  
  // 显示对话框
  visible.value = true
  
  // 重置菜单数据，确保每次都是新的
  menuOptions.value = []
  
  // 重置展开状态
  expandAll.value = false
  
  // 加载菜单树
  await getMenuTreeData()
  
  // 获取角色菜单权限
  await getRoleMenuPermissions(role.roleId)
}

// 提交表单
const submitForm = async () => {
  if (!menuTreeRef.value || form.roleId === undefined) return
  
  // 父子联动模式下，获取所有选中节点
  const checkedKeys = menuTreeRef.value.getCheckedKeys() as number[]
  const halfCheckedKeys = menuTreeRef.value.getHalfCheckedKeys() as number[]
  const menuIds = [...checkedKeys, ...halfCheckedKeys]
  
  try {
    await assignPermissions(form.roleId, menuIds)
    ElMessage.success('权限分配成功')
    visible.value = false
    emit('refreshList')
  } catch (error) {
    console.error('分配权限失败', error)
    ElMessage.error('分配权限失败')
  }
}

// 对外暴露方法
defineExpose({
  openDialog
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.empty-text {
  color: #909399;
  text-align: center;
  padding: 20px 0;
}

.tree-controls {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 15px;
  padding-right: 5px;
}

.tree-controls .el-button {
  margin-left: 10px;
  display: flex;
  align-items: center;
}

.tree-controls .el-icon {
  margin-right: 4px;
}

.tree-container {
  width: 400px;
  max-height: 450px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 10px;
  background-color: #f9fafc;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  margin: 0 auto;
}

.tree-container:hover {
  border-color: #c0c4cc;
  box-shadow: inset 0 1px 5px rgba(0, 0, 0, 0.1);
}

:deep(.permission-tree) {
  background-color: transparent;
  width: 100%;
}

:deep(.permission-tree .el-tree-node__content) {
  height: 32px;
  border-radius: 4px;
  margin: 2px 0;
  transition: background-color 0.2s;
}

:deep(.permission-tree .el-tree-node__content:hover) {
  background-color: #ecf5ff;
}

:deep(.permission-tree .el-checkbox__inner) {
  border-radius: 4px;
}

:deep(.permission-tree .el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff;
}
</style> 