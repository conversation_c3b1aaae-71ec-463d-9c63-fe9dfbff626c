package com.xiao.temu.modules.sync.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.sync.entity.PurchaseOrderSyncTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 备货单同步任务数据访问接口
 */
@Mapper
public interface PurchaseOrderSyncTaskMapper extends BaseMapper<PurchaseOrderSyncTask> {
    
    /**
     * 根据店铺ID获取同步任务
     *
     * @param shopId 店铺ID
     * @return 同步任务
     */
    PurchaseOrderSyncTask getByShopId(@Param("shopId") Long shopId);
    
    /**
     * 获取所有店铺的同步任务
     *
     * @return 同步任务列表
     */
    List<PurchaseOrderSyncTask> getAllTasks();
    
    /**
     * 获取指定店铺列表的同步任务
     *
     * @param shopIds 店铺ID列表
     * @return 同步任务列表
     */
    List<PurchaseOrderSyncTask> getTasksByShopIds(@Param("shopIds") List<Long> shopIds);
    
    /**
     * 获取状态为非同步中的同步任务
     *
     * @return 同步任务列表
     */
    List<PurchaseOrderSyncTask> getNotSyncingTasks();
} 