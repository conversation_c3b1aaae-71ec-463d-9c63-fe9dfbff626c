package com.xiao.temu.modules.quality.dto;

import lombok.Data;
import java.util.List;

/**
 * 抽检结果明细查询请求DTO
 */
@Data
public class QualityInspectionRequestDTO {
    
    /**
     * 页码
     */
    private Integer pageNo;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
    
    /**
     * 采购单号列表
     */
    private List<String> purchaseNo;
    
    /**
     * 质检结果 1-合格 2-不合格
     */
    private Integer skuQcResult;
    
    /**
     * SKU ID列表
     */
    private List<Long> skuIdList;
    
    /**
     * SKC ID列表
     */
    private List<Long> skcIdList;
    
    /**
     * 质检结果更新时间 ms 起始值
     */
    private Long qcResultUpdateTimeBegin;
    
    /**
     * 质检结果更新时间 ms 结束值
     */
    private Long qcResultUpdateTimeEnd;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 是否忽略权限检查
     * 为true时,超级管理员或拥有全部数据权限的用户可以查看所有数据
     */
    private Boolean ignorePermissionCheck = false;
} 