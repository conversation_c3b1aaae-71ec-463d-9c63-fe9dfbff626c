<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.system.mapper.SysUserMapper">

    <!-- 根据用户名查询用户 -->
    <select id="selectUserByUsername" parameterType="String" resultType="com.xiao.temu.modules.system.entity.SysUser">
        select *
        from sys_user
        where username = #{username}
        limit 1
    </select>
    
    <!-- 检查用户名是否唯一 -->
    <select id="checkUsernameUnique" parameterType="String" resultType="int">
        select count(1)
        from sys_user
        where username = #{username}
    </select>
    
    <!-- 更新用户密码 -->
    <update id="updatePassword">
        update sys_user
        set password = #{password},
            update_time = now()
        where user_id = #{userId}
    </update>
    
    <!-- 更新用户状态 -->
    <update id="updateStatus">
        update sys_user
        set status = #{status},
            update_time = now()
        where user_id = #{userId}
    </update>
    
    <!-- 根据角色ID查询用户ID列表 -->
    <select id="selectUserIdsByRoleId" resultType="java.lang.Long">
        SELECT
            u.user_id
        FROM
            sys_user u
            INNER JOIN sys_user_role ur ON u.user_id = ur.user_id
        WHERE
            ur.role_id = #{roleId}
            AND u.status = '0'
    </select>
    
    <!-- 查询具有生产角色的用户 -->
    <select id="selectProductionRoleUsers" resultType="com.xiao.temu.modules.system.entity.SysUser">
        SELECT DISTINCT
            u.*
        FROM
            sys_user u
            INNER JOIN sys_user_role ur ON u.user_id = ur.user_id
            INNER JOIN sys_role r ON ur.role_id = r.role_id
        WHERE
            r.role_key IN
            <foreach collection="roleKeys" item="roleKey" open="(" separator="," close=")">
                #{roleKey}
            </foreach>
            AND u.status = '0'
            <if test="excludeUserIds != null and excludeUserIds.size() > 0">
                AND u.user_id NOT IN
                <foreach collection="excludeUserIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                    u.username LIKE CONCAT('%', #{keyword}, '%')
                    OR u.nick_name LIKE CONCAT('%', #{keyword}, '%')
                    OR u.phone LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        ORDER BY
            u.user_id ASC
    </select>
    
</mapper> 