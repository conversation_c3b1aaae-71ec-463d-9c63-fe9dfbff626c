/**
 * 扫码工具函数
 */

/**
 * 执行扫码操作
 */
function performScan(page) {
  wx.scanCode({
    onlyFromCamera: true,
    scanType: ['qrCode'],
    success: (res) => {
      // 在控制台打印扫描结果
      console.log('扫码结果:', res.result);
      console.log('扫码完整信息:', res);
      
      try {
        // 尝试解析JSON格式的二维码内容
        let qrData;
        try {
          qrData = JSON.parse(res.result);
          console.log('成功解析JSON格式二维码:', qrData);
          
          // 判断是单个备货单还是批量备货单
          if (qrData.orders && Array.isArray(qrData.orders) && qrData.orders.length > 0 && qrData.type === 'batch') {
            // 批量备货单处理
            console.log('检测到批量备货单二维码:', qrData);
            page.handleBatchScanResult(qrData);
            return;
          } else if (qrData.subPurchaseOrderSn && qrData.shopId) {
            // 单个备货单处理
            console.log('检测到单个备货单二维码:', qrData);
            page.handleScanResult(qrData);
            return;
          } else {
            console.error('JSON格式二维码数据结构不符合要求:', qrData);
          }
        } catch (e) {
          console.log('非JSON格式二维码，尝试其他格式');
          // 如果不是JSON格式，尝试解析URL查询参数格式
          if (res.result.includes('subPurchaseOrderSn=') && res.result.includes('shopId=')) {
            const urlParams = new URLSearchParams(res.result.split('?')[1]);
            qrData = {
              subPurchaseOrderSn: urlParams.get('subPurchaseOrderSn'),
              shopId: parseInt(urlParams.get('shopId'), 10)
            };
            console.log('成功解析URL参数格式二维码:', qrData);
          } else if (res.result.includes('WB') && res.result.match(/\d+/)) {
            // 尝试直接从文本中提取备货单号和店铺ID
            const snMatch = res.result.match(/WB\d+/);
            qrData = {
              subPurchaseOrderSn: snMatch ? snMatch[0] : res.result,
              shopId: 6 // 默认店铺ID
            };
            console.log('尝试直接提取备货单号:', qrData);
          }
        }
        
        // 检查是否包含必要的属性
        if (qrData && qrData.subPurchaseOrderSn && qrData.shopId) {
          // 处理扫码结果
          page.handleScanResult(qrData);
          return;
        } else {
          console.error('二维码数据不完整:', qrData);
          // 尝试使用二维码内容作为备货单号
          if (res.result && res.result.trim()) {
            const directSn = res.result.trim();
            console.log('尝试直接使用扫码内容作为备货单号:', directSn);
            page.handleScanResult({
              subPurchaseOrderSn: directSn,
              shopId: 6 // 默认店铺ID
            });
            return;
          }
        }
      } catch (e) {
        console.error('解析二维码失败:', e);
      }
      
      // 如果所有解析方法都失败，弹出错误提示
      wx.showModal({
        title: '二维码无效',
        content: '无法识别的二维码格式，请扫描有效的备货单二维码',
        showCancel: false
      });
    },
    fail: (err) => {
      if (err.errMsg && err.errMsg.includes('scanCode:fail cancel')) {
        // 用户取消了扫码操作
        console.log('用户取消扫码');
        // 在这里可以根据需要选择不执行任何操作，或者给用户一个更友好的提示
        // 例如：
        // wx.showToast({
        //   title: '扫码已取消',
        //   icon: 'none',
        //   duration: 1500
        // });
        // 当前选择不弹出提示，仅在控制台记录
      }
    }
  });
}

/**
 * 从相册选择图片
 */
function chooseImageForScan(page) {
  // 显示加载中提示
  wx.showLoading({
    title: '正在处理图片',
    mask: true
  });
  
  wx.chooseMedia({
    count: 1,
    mediaType: ['image'],
    sourceType: ['album'],
    camera: 'back',
    success: (res) => {
      console.log('选择图片成功:', res);
      if (res.tempFiles && res.tempFiles.length > 0) {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        
        // 使用小程序提供的二维码识别API
        wx.scanCode({
          scanType: ['qrCode'],
          onlyFromCamera: false, // 允许从非相机来源扫码
          path: tempFilePath, // 指定图片路径
          success: (scanRes) => {
            console.log('从相册图片解析二维码成功:', scanRes);
            
            try {
              // 处理扫码结果，复用performScan中的逻辑
              let qrData;
              try {
                qrData = JSON.parse(scanRes.result);
                console.log('成功解析JSON格式二维码:', qrData);
                
                // 判断是单个备货单还是批量备货单
                if (qrData.orders && Array.isArray(qrData.orders) && qrData.orders.length > 0 && qrData.type === 'batch') {
                  // 批量备货单处理
                  console.log('检测到批量备货单二维码:', qrData);
                  wx.hideLoading();
                  page.handleBatchScanResult(qrData);
                  return;
                } else if (qrData.subPurchaseOrderSn && qrData.shopId) {
                  // 单个备货单处理
                  console.log('检测到单个备货单二维码:', qrData);
                  wx.hideLoading();
                  page.handleScanResult(qrData);
                  return;
                } else {
                  console.error('JSON格式二维码数据结构不符合要求:', qrData);
                }
              } catch (e) {
                console.log('非JSON格式二维码，尝试其他格式');
                // 如果不是JSON格式，尝试解析URL查询参数格式
                if (scanRes.result.includes('subPurchaseOrderSn=') && scanRes.result.includes('shopId=')) {
                  const urlParams = new URLSearchParams(scanRes.result.split('?')[1]);
                  qrData = {
                    subPurchaseOrderSn: urlParams.get('subPurchaseOrderSn'),
                    shopId: parseInt(urlParams.get('shopId'), 10)
                  };
                  console.log('成功解析URL参数格式二维码:', qrData);
                } else if (scanRes.result.includes('WB') && scanRes.result.match(/\d+/)) {
                  // 尝试直接从文本中提取备货单号和店铺ID
                  const snMatch = scanRes.result.match(/WB\d+/);
                  qrData = {
                    subPurchaseOrderSn: snMatch ? snMatch[0] : scanRes.result,
                    shopId: 6 // 默认店铺ID
                  };
                  console.log('尝试直接提取备货单号:', qrData);
                }
              }
              
              // 检查是否包含必要的属性
              if (qrData && qrData.subPurchaseOrderSn && qrData.shopId) {
                // 处理扫码结果
                wx.hideLoading();
                page.handleScanResult(qrData);
                return;
              } else {
                console.error('二维码数据不完整:', qrData);
                // 尝试使用二维码内容作为备货单号
                if (scanRes.result && scanRes.result.trim()) {
                  const directSn = scanRes.result.trim();
                  console.log('尝试直接使用扫码内容作为备货单号:', directSn);
                  wx.hideLoading();
                  page.handleScanResult({
                    subPurchaseOrderSn: directSn,
                    shopId: 6 // 默认店铺ID
                  });
                  return;
                }
              }
            } catch (e) {
              console.error('解析二维码失败:', e);
            }
            
            wx.hideLoading();
            // 如果所有解析方法都失败，弹出错误提示
            wx.showModal({
              title: '二维码无效',
              content: '无法识别的二维码格式，请选择包含有效备货单二维码的图片',
              showCancel: false
            });
          },
          fail: (scanErr) => {
            console.error('从相册图片解析二维码失败:', scanErr);
            wx.hideLoading();
            
            // 如果微信原生API不支持从图片解析二维码，可以考虑使用云函数或第三方服务
            // 这里提供一个备选方案，使用wx.cloud.callFunction调用云函数解析二维码
            // 注意：需要先开通云开发功能并部署相应的云函数
            
            wx.showModal({
              title: '提示',
              content: '无法从相册图片识别二维码，请尝试使用相机直接扫码',
              showCancel: false
            });
          }
        });
      }
    },
    fail: (err) => {
      console.error('选择图片失败:', err);
      wx.hideLoading();
      wx.showToast({
        title: '选择图片失败',
        icon: 'none'
      });
    }
  });
}

/**
 * 重置扫码界面状态
 */
function resetScannerState(page) {
  page.setData({
    scanResult: null,
    scanVisible: true,
    processing: false,
    showRoleSelection: false,
    selectedProgressTypes: [],
    availableProgressTypes: [],
    // 重置所有工序的强制更新变量
    forceBurningUpdate: 0,
    forceSewingUpdate: 0,
    forceTailUpdate: 0,
    forceShippingUpdate: 0,
    forceDeliveryUpdate: 0,
    forceInspectionUpdate: 0,
    // 重置批量模式相关状态
    batchMode: false,
    batchOrders: [],
    batchProgressList: [],
    currentBatchIndex: 0,
    // 更新页面版本
    pageVersion: Date.now()
  });
  
  // 更新工序项目列表
  page.updateProgressItems();
}

module.exports = {
  performScan,
  chooseImageForScan,
  resetScannerState
}; 