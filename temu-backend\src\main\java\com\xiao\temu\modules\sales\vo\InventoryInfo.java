package com.xiao.temu.modules.sales.vo;

import lombok.Data;

/**
 * 库存信息VO
 */
@Data
public class InventoryInfo {
    /**
     * 商品SKU ID
     */
    private Long productSkuId;
    
    /**
     * 仓库组ID
     */
    private Long warehouseGroupId;
    
    /**
     * 仓内库存
     */
    private Integer warehouseInventoryNum;
    
    /**
     * 待上架库存
     */
    private Integer waitOnShelfNum;
    
    /**
     * 待发货库存
     */
    private Integer waitDeliveryInventoryNum;
    
    /**
     * 预期占用库存数量
     */
    private Integer expectedOccupiedInventoryNum;
    
    /**
     * 待审核备货库存
     */
    private Integer waitApproveInventoryNum;
    
    /**
     * 已上架待质检库存
     */
    private Integer waitQcNum;
    
    /**
     * 仓内暂不可用库存
     */
    private Integer unavailableWarehouseInventoryNum;
    
    /**
     * 待入库库存
     */
    private Integer waitInStock;
    
    /**
     * 待收货库存
     */
    private Integer waitReceiveNum;
} 