package com.xiao.temu.modules.purchaseorderv.vo;

import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 商品标签数据响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductLabelVO {
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 错误代码
     */
    private Integer errorCode;
    
    /**
     * 错误信息
     */
    private String errorMsg;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺名称
     */
    private String shopName;
    
    /**
     * 店铺备注
     */
    private String shopRemark;
    
    /**
     * 返回结果
     * 按店铺分组的API返回结果
     */
    private Map<Long, JSONObject> shopResultMap;
} 