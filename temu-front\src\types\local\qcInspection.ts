/**
 * 本地抽检结果明细查询请求参数
 */
export interface LocalQualityInspectionRequestParams {
  shopIds?: number[]
  qcResultUpdateTimeBegin?: string
  qcResultUpdateTimeEnd?: string
  pageNo: number
  pageSize: number
  skuIdList: number[]
  skcIdList: number[]
  purchaseNo: string[]
  qcResult?: string
  skuNameKeyword?: string
}

/**
 * 本地抽检结果明细数据项
 */
export interface LocalQualityInspectionItem {
  id: number
  qcBillId: number
  productSkuId: number
  productSkcId: number
  spuId: number
  skuName: string
  catName: string
  purchaseNo: string
  spec: string
  thumbUrl: string
  qcResult: string
  qcResultUpdateTime: string
  flawNameDesc?: string
  remark?: string
  attachments?: string[]
  extCode?: string
  shopName?: string
  shopId?: number
  shopRemark?: string
}

/**
 * 本地抽检结果明细数据
 */
export interface LocalQualityInspectionData {
  total: number
  pageNo: number
  pageSize: number
  items: LocalQualityInspectionItem[]
  shopName?: string
  shopId?: number
}

/**
 * 本地抽检结果明细响应
 */
export interface LocalQualityInspectionResponse {
  code: number
  message: string
  data: LocalQualityInspectionData
} 