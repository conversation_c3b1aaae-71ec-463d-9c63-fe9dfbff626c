<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    :width="width"
    class="image-preview-dialog"
    :before-close="handleClose"
  >
    <div class="image-preview-container">
      <img :src="imageUrl" class="preview-image" :alt="alt" />
    </div>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  imageUrl: {
    type: String,
    required: true
  },
  title: {
    type: String,
    default: '商品图片预览'
  },
  width: {
    type: String,
    default: '50%'
  },
  alt: {
    type: String,
    default: '商品大图'
  }
});

const emit = defineEmits(['update:visible']);

// 通过计算属性与v-model连接
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const handleClose = () => {
  emit('update:visible', false);
};
</script>

<style scoped>
.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
}

.image-preview-dialog :deep(.el-dialog__body) {
  padding: 10px;
}
</style> 