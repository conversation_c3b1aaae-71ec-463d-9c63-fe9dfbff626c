/**
 * API配置文件
 */

// 判断当前环境
const ENV = {
  DEV: 'development',  // 开发环境
  PROD: 'production'   // 生产环境
};

// 当前环境，可以根据实际情况修改这里来切换环境
const CURRENT_ENV = ENV.PROD;

// API基础URL配置
const API_BASE_URL = {
  [ENV.DEV]: 'http://localhost:8080/api',      // 开发环境
  [ENV.PROD]: 'https://upwarad.com/api'   // 生产环境
};

// 获取当前环境的基础URL
const getBaseUrl = () => {
  return API_BASE_URL[CURRENT_ENV];
};

module.exports = {
  ENV,
  CURRENT_ENV,
  getBaseUrl
}; 