package com.xiao.temu.modules.sales.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 销售数据查询DTO
 */
public class SalesDataQueryDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 店铺ID列表
     */
    private List<Long> shopIds;
    
    /**
     * 开始时间（毫秒时间戳）
     */
    private String startTime;
    
    /**
     * 结束时间（毫秒时间戳）
     */
    private String endTime;
    
    /**
     * 页码
     */
    private Integer pageNo;
    
    /**
     * 每页条数
     */
    private Integer pageSize;
    
    /**
     * 商品SKU ID列表
     */
    private List<Long> skuIdList;
    
    /**
     * 商品选中状态列表 
     * 10-仓库中，20-出售中，30-在售编辑中/品控中/等待上架，40-下架，50-删除
     */
    private List<Integer> selectStatusList;
    
    /**
     * 商品名称
     */
    private String productName;
    
    /**
     * 商品SPU列表 (前端传入的是逗号分隔的字符串，转换为列表)
     */
    private List<Long> productIdList;
    
    /**
     * 商品SKC列表 (前端传入的是逗号分隔的字符串，转换为列表)
     */
    private List<Long> productSkcIdList;
    
    /**
     * SKC货号列表
     */
    private List<String> skcExtCodeList;
    
    /**
     * SKU货号列表
     */
    private List<String> skuExtCodeList;
    
    /**
     * 加入站点时长下限（天）
     */
    private Integer onSalesDurationOfflineGte;
    
    /**
     * 加入站点时长上限（天）
     */
    private Integer onSalesDurationOfflineLte;
    
    /**
     * 备货区域列表 (1:国内仓, 2:海外仓, 3:保税仓)
     */
    private List<Integer> inventoryRegionList;
    
    /**
     * SKU剩余库存最小值
     */
    private Integer minRemanentInventoryNum;
    
    /**
     * SKU剩余库存最大值
     */
    private Integer maxRemanentInventoryNum;
    
    /**
     * SKU可售天数最小值
     */
    private Integer minAvailableSaleDays;
    
    /**
     * SKU可售天数最大值
     */
    private Integer maxAvailableSaleDays;
    
    /**
     * 图片审核状态列表 (1:未完成, 2:已完成)
     */
    private List<Integer> pictureAuditStatusList;
    
    /**
     * 选品状态列表 (0:正常供货, 1:暂时无货, 2:停产)
     */
    private List<Integer> supplyStatusList;
    
    /**
     * 关停JIT状态 (1:未关闭, 2:待关闭, 3:特殊关闭, 4:特殊关闭审核失败, 5:关闭失败, 6:关闭生效)
     */
    private List<Integer> closeJitStatus;
    
    /**
     * 是否缺货 (0:不缺货, 1:缺货)
     */
    private Boolean isLack;
    
    /**
     * 是否定制商品
     */
    private Boolean isCustomGoods;
    
    /**
     * 今日销量最小值
     */
    private Integer todaySaleVolumMin;
    
    /**
     * 今日销量最大值
     */
    private Integer todaySaleVolumMax;
    
    /**
     * 是否热销款
     */
    private Boolean hotTag;
    
    /**
     * 备货类型 ("0": 普通, "1": JIT备货)
     */
    private String purchaseStockType;
    
    /**
     * 建议自动关JIT
     */
    private Boolean suggestCloseJit;
    
    /**
     * 售罄状态 (0-库存充足 1-即将断码 2-已断码 3-全部售罄(已断货))
     */
    private List<Integer> stockStatusList;
    
    /**
     * 剩余生产数是否大于0
     */
    private Boolean availableProduceNumGreaterThanZero;
    
    /**
     * 备货仓组ID列表
     */
    private List<Integer> warehouseGroupIdList;
    
    /**
     * 结算类型 (0:非VMI, 1:VMI)
     */
    private Integer settlementType;
    
    /**
     * 排序字段
     * 可选值: todaySaleVolume, lastSevenDaysSaleVolume, lastThirtyDaysSaleVolume
     */
    private String orderByParam;
    
    /**
     * 是否降序排序
     * 1: 降序, 0: 升序
     */
    private Integer orderByDesc;

    public List<Long> getShopIds() {
        return shopIds;
    }

    public void setShopIds(List<Long> shopIds) {
        this.shopIds = shopIds;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<Long> getSkuIdList() {
        return skuIdList;
    }

    public void setSkuIdList(List<Long> skuIdList) {
        this.skuIdList = skuIdList;
    }

    public List<Integer> getSelectStatusList() {
        return selectStatusList;
    }

    public void setSelectStatusList(List<Integer> selectStatusList) {
        this.selectStatusList = selectStatusList;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public List<Long> getProductIdList() {
        return productIdList;
    }

    public void setProductIdList(List<Long> productIdList) {
        this.productIdList = productIdList;
    }

    public List<Long> getProductSkcIdList() {
        return productSkcIdList;
    }

    public void setProductSkcIdList(List<Long> productSkcIdList) {
        this.productSkcIdList = productSkcIdList;
    }

    public List<String> getSkcExtCodeList() {
        return skcExtCodeList;
    }

    public void setSkcExtCodeList(List<String> skcExtCodeList) {
        this.skcExtCodeList = skcExtCodeList;
    }

    public List<String> getSkuExtCodeList() {
        return skuExtCodeList;
    }

    public void setSkuExtCodeList(List<String> skuExtCodeList) {
        this.skuExtCodeList = skuExtCodeList;
    }

    public Integer getOnSalesDurationOfflineGte() {
        return onSalesDurationOfflineGte;
    }

    public void setOnSalesDurationOfflineGte(Integer onSalesDurationOfflineGte) {
        this.onSalesDurationOfflineGte = onSalesDurationOfflineGte;
    }

    public Integer getOnSalesDurationOfflineLte() {
        return onSalesDurationOfflineLte;
    }

    public void setOnSalesDurationOfflineLte(Integer onSalesDurationOfflineLte) {
        this.onSalesDurationOfflineLte = onSalesDurationOfflineLte;
    }

    public List<Integer> getInventoryRegionList() {
        return inventoryRegionList;
    }

    public void setInventoryRegionList(List<Integer> inventoryRegionList) {
        this.inventoryRegionList = inventoryRegionList;
    }

    public Integer getMinRemanentInventoryNum() {
        return minRemanentInventoryNum;
    }

    public void setMinRemanentInventoryNum(Integer minRemanentInventoryNum) {
        this.minRemanentInventoryNum = minRemanentInventoryNum;
    }

    public Integer getMaxRemanentInventoryNum() {
        return maxRemanentInventoryNum;
    }

    public void setMaxRemanentInventoryNum(Integer maxRemanentInventoryNum) {
        this.maxRemanentInventoryNum = maxRemanentInventoryNum;
    }

    public Integer getMinAvailableSaleDays() {
        return minAvailableSaleDays;
    }

    public void setMinAvailableSaleDays(Integer minAvailableSaleDays) {
        this.minAvailableSaleDays = minAvailableSaleDays;
    }

    public Integer getMaxAvailableSaleDays() {
        return maxAvailableSaleDays;
    }

    public void setMaxAvailableSaleDays(Integer maxAvailableSaleDays) {
        this.maxAvailableSaleDays = maxAvailableSaleDays;
    }

    public List<Integer> getPictureAuditStatusList() {
        return pictureAuditStatusList;
    }

    public void setPictureAuditStatusList(List<Integer> pictureAuditStatusList) {
        this.pictureAuditStatusList = pictureAuditStatusList;
    }

    public List<Integer> getSupplyStatusList() {
        return supplyStatusList;
    }

    public void setSupplyStatusList(List<Integer> supplyStatusList) {
        this.supplyStatusList = supplyStatusList;
    }

    public List<Integer> getCloseJitStatus() {
        return closeJitStatus;
    }

    public void setCloseJitStatus(List<Integer> closeJitStatus) {
        this.closeJitStatus = closeJitStatus;
    }

    public Boolean getIsLack() {
        return isLack;
    }

    public void setIsLack(Boolean isLack) {
        this.isLack = isLack;
    }

    public Boolean getIsCustomGoods() {
        return isCustomGoods;
    }

    public void setIsCustomGoods(Boolean isCustomGoods) {
        this.isCustomGoods = isCustomGoods;
    }

    public Integer getTodaySaleVolumMin() {
        return todaySaleVolumMin;
    }

    public void setTodaySaleVolumMin(Integer todaySaleVolumMin) {
        this.todaySaleVolumMin = todaySaleVolumMin;
    }

    public Integer getTodaySaleVolumMax() {
        return todaySaleVolumMax;
    }

    public void setTodaySaleVolumMax(Integer todaySaleVolumMax) {
        this.todaySaleVolumMax = todaySaleVolumMax;
    }

    public Boolean getHotTag() {
        return hotTag;
    }

    public void setHotTag(Boolean hotTag) {
        this.hotTag = hotTag;
    }

    public String getPurchaseStockType() {
        return purchaseStockType;
    }

    public void setPurchaseStockType(String purchaseStockType) {
        this.purchaseStockType = purchaseStockType;
    }

    public Boolean getSuggestCloseJit() {
        return suggestCloseJit;
    }

    public void setSuggestCloseJit(Boolean suggestCloseJit) {
        this.suggestCloseJit = suggestCloseJit;
    }

    public List<Integer> getStockStatusList() {
        return stockStatusList;
    }

    public void setStockStatusList(List<Integer> stockStatusList) {
        this.stockStatusList = stockStatusList;
    }

    public Boolean getAvailableProduceNumGreaterThanZero() {
        return availableProduceNumGreaterThanZero;
    }

    public void setAvailableProduceNumGreaterThanZero(Boolean availableProduceNumGreaterThanZero) {
        this.availableProduceNumGreaterThanZero = availableProduceNumGreaterThanZero;
    }

    public List<Integer> getWarehouseGroupIdList() {
        return warehouseGroupIdList;
    }

    public void setWarehouseGroupIdList(List<Integer> warehouseGroupIdList) {
        this.warehouseGroupIdList = warehouseGroupIdList;
    }

    public Integer getSettlementType() {
        return settlementType;
    }

    public void setSettlementType(Integer settlementType) {
        this.settlementType = settlementType;
    }
    
    public String getOrderByParam() {
        return orderByParam;
    }
    
    public void setOrderByParam(String orderByParam) {
        this.orderByParam = orderByParam;
    }
    
    public Integer getOrderByDesc() {
        return orderByDesc;
    }
    
    public void setOrderByDesc(Integer orderByDesc) {
        this.orderByDesc = orderByDesc;
    }
} 