import { defineStore } from 'pinia'
import { getShipOrders } from '@/api/temu/shipOrder'
import { fetchUserAccessibleShops } from '@/utils/shop-helper'
import { ElMessage } from 'element-plus'
import type { ShopDataRange, ShipOrder } from '@/types/shipOrder'

export const useShipOrderStore = defineStore('shipOrder', {
  state: () => ({
    loading: false,
    shipOrders: [] as ShipOrder[],
    total: 0,
    shopDataRanges: [] as ShopDataRange[],
    shops: [] as any[],
  }),

  actions: {
    // 获取发货单列表
    async getShipOrderList(queryParams: any) {
      this.loading = true
      try {
        // API层已经处理了参数过滤，这里直接传递参数
        const response = await getShipOrders(queryParams)
        const apiResponse = response as any
        if (apiResponse.code === 200 && apiResponse.data) {
          // 根据data.json的数据结构进行正确解析
          if (apiResponse.data.success && apiResponse.data.result) {
            const result = apiResponse.data.result
            // 从result.list中获取数据
            this.shipOrders = result.list || []
            this.total = result.total || 0
            this.shopDataRanges = result.shopDataRanges || []
          } else {
            this.shipOrders = []
            this.total = 0
            this.shopDataRanges = []
            // 只做日志，不弹窗
            if (apiResponse.data.errorMsg === 'SYSTEM_EXCEPTION') {
              console.error('temu平台系统异常,请稍后重试')
            } else {
              console.error(apiResponse.data.errorMsg || '获取发货单列表失败')
            }
          }
        } else {
          console.error(apiResponse.message || '获取发货单列表失败')
          this.shipOrders = []
          this.total = 0
          this.shopDataRanges = []
        }
      } catch (error) {
        console.error('获取发货单列表出错', error)
        // 检查错误是否包含SYSTEM_EXCEPTION信息，只做日志
        const err = error as any
        if (err && 
            ((typeof err.message === 'string' && err.message.includes('SYSTEM_EXCEPTION')) || 
             (err.response?.data?.errorMsg === 'SYSTEM_EXCEPTION'))) {
          console.error('temu平台系统异常,请稍后重试')
        } else {
          console.error('系统异常，请稍后重试')
        }
        this.shipOrders = []
        this.total = 0
        this.shopDataRanges = []
      } finally {
        this.loading = false
      }
    },

    // 获取用户可访问的店铺列表
    async getShopList() {
      try {
        console.log('开始获取店铺列表')
        this.shops = await fetchUserAccessibleShops()
        console.log('返回店铺列表:', this.shops.length, '个店铺')
        return this.shops
      } catch (error) {
        console.error('获取店铺列表出错', error)
        // 检查错误是否包含SYSTEM_EXCEPTION信息
        const err = error as any
        if (err && 
            ((typeof err.message === 'string' && err.message.includes('SYSTEM_EXCEPTION')) || 
             (err.response?.data?.errorMsg === 'SYSTEM_EXCEPTION'))) {
          ElMessage.error('temu平台系统异常,请稍后重试')
        } else {
          ElMessage.error('系统异常，请稍后重试')
        }
        return []
      }
    },
  }
}) 