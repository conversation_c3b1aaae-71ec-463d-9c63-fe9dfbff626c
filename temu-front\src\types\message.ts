// 消息相关类型定义

// 消息类型
export enum MessageType {
  SYSTEM = '1',  // 系统消息
  TASK = '2',    // 任务提醒
  SHOP = '3'     // 店铺消息
}

// 接收对象类型
export enum TargetType {
  ALL = '0',     // 全部用户
  USER = '1',    // 指定用户
  ROLE = '2',    // 指定角色
  GROUP = '3'    // 运营组
}

// 重要程度
export enum ImportanceLevel {
  NORMAL = '1',  // 普通
  IMPORTANT = '2', // 重要
  URGENT = '3'   // 紧急
}

// 已读状态
export enum ReadStatus {
  UNREAD = '0',  // 未读
  READ = '1'     // 已读
}

// 消息对象
export interface Message {
  messageId: string;
  title: string;
  content: string;
  messageType: MessageType | string;
  importance: ImportanceLevel | string;
  readStatus: ReadStatus | string;
  createTime: string | number;
  readTime?: string | number;
  userId?: string;
  targetType?: string;
  shopId?: string;
  shopName?: string;
  expireTime?: string | number;
}

// 发送消息请求参数
export interface SendMessageDTO {
  title: string;            // 消息标题
  content: string;          // 消息内容
  messageType: MessageType | string; // 消息类型
  targetType: TargetType | string;   // 接收对象类型
  targetIds?: string;       // 接收对象ID列表
  shopId?: number;          // 相关店铺ID
  importance: ImportanceLevel | string; // 重要程度
  publishTime?: string;     // 定时发布时间
  expireTime?: string;      // 过期时间
}

// 消息查询参数
export interface MessageQueryDTO {
  pageNum: number;
  pageSize: number;
  messageType?: string;
  readStatus?: string;
  importance?: string;
  startTime?: string;
  endTime?: string;
  keyword?: string;
}

// 消息模板对象
export interface MessageTemplate {
  templateId: number;          // 模板ID
  templateCode: string;        // 模板编码
  templateName: string;        // 模板名称
  titleTemplate: string;       // 标题模板
  contentTemplate: string;     // 内容模板
  templateType: MessageType | string;   // 模板类型
  status: string;              // 状态
  createTime: string;          // 创建时间
  updateTime?: string;         // 更新时间
  remark?: string;             // 备注
}

// 未读消息计数
export interface UnreadCountVO {
  totalCount: number;           // 总未读数
  systemCount: number;          // 系统消息未读数
  taskCount: number;            // 任务消息未读数
  shopCount: number;            // 店铺消息未读数
}

// 分页结果
export interface PageResult<T> {
  rows: T[];                    // 数据列表
  total: number;                // 总数
}

// 消息发送对象类型
export interface MessageSendDTO {
  title: string;
  content: string;
  messageType: MessageType | string;
  importance: ImportanceLevel | string;
  targetType: 'ALL' | 'USER' | 'ROLE' | 'GROUP' | string;
  targetIds?: string[] | string;
  shopId?: string | number;
  publishTime?: string;
  expireTime?: string;
}

// 消息列表响应
export interface MessageListResult {
  rows: Message[];
  total: number;
}

// 消息统计数据
export interface MessageStats {
  total: number;
  unread: number;
  system: {
    total: number;
    unread: number;
  };
  task: {
    total: number;
    unread: number;
  };
  shop: {
    total: number;
    unread: number;
  };
} 