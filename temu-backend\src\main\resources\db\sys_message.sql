/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : temu_api

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 03/06/2025 15:42:43
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_message
-- ----------------------------
DROP TABLE IF EXISTS `sys_message`;
CREATE TABLE `sys_message`  (
  `message_id` bigint NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '消息内容',
  `message_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息类型（1系统消息 2任务提醒 3店铺消息）',
  `to_user_id` bigint NULL DEFAULT NULL COMMENT '接收用户ID',
  `from_user_id` bigint NULL DEFAULT NULL COMMENT '发送用户ID',
  `target_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '接收对象类型(0全部用户 1指定用户 2指定角色 3运营组)',
  `target_ids` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '接收对象ID列表,逗号分隔',
  `shop_id` bigint NULL DEFAULT NULL COMMENT '相关店铺ID',
  `read_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '已读状态（0未读 1已读）',
  `importance` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '重要程度(1普通 2重要 3紧急)',
  `important` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '重要程度（0普通 1重要）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `publish_time` datetime NULL DEFAULT NULL COMMENT '发布时间',
  `expire_time` datetime NULL DEFAULT NULL COMMENT '过期时间',
  `read_time` datetime NULL DEFAULT NULL COMMENT '阅读时间',
  `deleted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '是否删除(0未删除 1已删除)',
  `ext_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '扩展数据，存储额外JSON信息',
  PRIMARY KEY (`message_id`) USING BTREE,
  INDEX `idx_to_user`(`to_user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 423 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '消息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_message
-- ----------------------------
INSERT INTO `sys_message` VALUES (1, '测试消息1', '测试消息1', '1', NULL, 1, '0', '', NULL, '0', '1', '0', '2025-03-24 14:07:45', '2025-03-24 14:07:45', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (2, '组长消息推送测试', '组长消息推送测试', '1', NULL, 1, '1', '5', NULL, '0', '2', '0', '2025-03-24 14:33:08', '2025-03-24 14:33:08', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (3, '运营组消息测试', '运营组消息测试', '1', NULL, 1, '3', '1', NULL, '0', '2', '0', '2025-03-24 14:35:00', '2025-03-24 14:35:00', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (382, '【JIT备货单即将逾期提醒】- 店铺7', '店铺 店铺7 的JIT备货单 WB2505293552341 将于24小时内到达预计最晚到货时间，请及时处理。\n商品名称：【柚蔻选品】女童青少年时尚简约英文印花截百搭短款T恤，货号351\n供应商：Beautiful Yolanda\n预计最晚到货时间：1748762411000', '2', NULL, NULL, '1', '5,8,36', 7, '0', '2', '0', '2025-06-01 15:19:10', '2025-06-01 15:19:10', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (383, '【JIT备货单即将逾期提醒】- Beautiful Yolanda', '店铺 Beautiful Yolanda 的JIT备货单 WB2505293682749 将于24小时内到达预计最晚到货时间，请及时处理。\n商品名称：【旧草选品】青少年女休闲时尚运动字母印花街头T恤短裤两件套装217+250\n供应商：\n预计最晚到货时间：1748763315000', '2', NULL, NULL, '1', '5,8,36', 7, '0', '2', '0', '2025-06-01 15:34:29', '2025-06-01 15:34:29', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (384, '【JIT备货单即将逾期提醒】- Beautiful Yolanda', '店铺 Beautiful Yolanda 的JIT备货单 WB2505293693827 将于24小时内到达预计最晚到货时间，请及时处理。\n商品名称：【旧草选品】新青少年休闲时尚运动印花舒适T恤高弹打底裤两件套装217+250\n供应商：\n预计最晚到货时间：1748763631000', '2', NULL, NULL, '1', '5,8,36', 7, '0', '2', '0', '2025-06-01 15:35:14', '2025-06-01 15:35:14', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (385, '【JIT备货单即将逾期提醒】- Beautiful Yolanda B1', '店铺 Beautiful Yolanda B1 的JIT备货单 WB2505293693827 将于24小时内到达预计最晚到货时间，请及时处理。\n商品名称：【旧草选品】新青少年休闲时尚运动印花舒适T恤高弹打底裤两件套装217+250\n预计最晚到货时间：1748763631000', '2', NULL, NULL, '1', '5,8,36', 7, '0', '2', '0', '2025-06-01 15:36:06', '2025-06-01 15:36:06', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (386, '【JIT备货单即将逾期提醒】- Beautiful Yolanda - B1', '店铺 Beautiful Yolanda B1 的JIT备货单 WB2505301011724 将于24小时内到达预计最晚到货时间，请及时处理。\n商品名称：青少年女童时尚字母可爱花朵休闲卡通印花街头优雅朋克T恤\n预计最晚到货时间：1748815465000', '2', NULL, NULL, '1', '5,8,36', 7, '0', '2', '0', '2025-06-01 15:37:33', '2025-06-01 15:37:33', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (387, '【JIT备货单已逾期提醒】- Beautiful Yolanda - B1', '店铺 Beautiful Yolanda B1 的JIT备货单 WB2505283335625 已超过预计最晚到货时间，请立即处理。\n商品名称：【岑寒选品】青少年女童时尚印花舒适柔软T恤+弹力打底裤，2件套装，货号217-250\n预计最晚到货时间：1748561368000', '2', NULL, NULL, '1', '5,8,36', 7, '0', '2', '0', '2025-06-01 16:17:42', '2025-06-01 16:17:42', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (388, '【JIT备货单即将逾期提醒】- Beautiful Yolanda - B1', '店铺 Beautiful Yolanda B1 的JIT备货单 WB2505301202316 将于24小时内到达预计最晚到货时间，请及时处理。\n商品名称：【柚蔻选品】女童青少年夏季休闲舒适,百搭印花落肩短袖，货号349\n预计最晚到货时间：1748815615000', '2', NULL, NULL, '1', '5,8,36', 7, '0', '2', '0', '2025-06-01 16:18:18', '2025-06-01 16:18:18', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (389, '【JIT备货单到货已逾期提醒】- Flipped Bragi - Flipped Bragi', '店铺 Flipped Bragi Flipped Bragi 的JIT备货单 WB2506013850787 已超过预计最晚到货时间，请立即处理。\n商品名称：【定向】休闲长袖圆领连衣裙春秋冬款(114/7.12)\n预计最晚到货时间：1748676191000', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-01 19:17:01', '2025-06-01 19:17:01', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (390, '【JIT备货单到货已逾期提醒】- Flipped Bragi - Flipped Bragi', '店铺 Flipped Bragi Flipped Bragi 的JIT备货单 WB2506013850787 已超过预计最晚到货时间，请立即处理。\n商品名称：【定向】休闲长袖圆领连衣裙春秋冬款(114/7.12)\n预计最晚到货时间：1748676191000', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-01 19:18:04', '2025-06-01 19:18:04', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (391, '【JIT备货单到货即将逾期提醒】- Flipped Bragi - Flipped Bragi', '店铺 Flipped Bragi Flipped Bragi 的JIT备货单 WB2506013850787 将于24小时内到达预计最晚到货时间，请及时处理。\n商品名称：【定向】休闲长袖圆领连衣裙春秋冬款(114/7.12)\n预计最晚到货时间：1748848991000', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-01 19:22:16', '2025-06-01 19:22:16', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (392, '【普通备货单未发货提醒】- Flipped Bragi - Flipped Bragi', '店铺 Flipped Bragi Flipped Bragi 的普通备货单 WB2506014140638 已创建5天仍未发货，请检查处理。\n商品名称：图案印花细肩带连衣裙，休闲无袖吊带连衣裙，女装（062/8.27）\n创建时间：1748248968000', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-01 19:22:50', '2025-06-01 19:22:50', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (393, '【JIT备货单到货即将逾期提醒】- Flipped Bragi - Flipped Bragi', '店铺 Flipped Bragi Flipped Bragi 的JIT备货单 WB2506013850787 将于24小时内到达预计最晚到货时间，请及时处理。\n商品名称：【定向】休闲长袖圆领连衣裙春秋冬款(114/7.12)\n预计最晚到货时间：1748848991000', '2', NULL, NULL, '1', '6,7,5,8,36', 10, '0', '2', '0', '2025-06-01 19:58:58', '2025-06-01 19:58:58', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (394, '【JIT备货单到货已逾期提醒】- Flipped Bragi - Flipped Bragi', '店铺 Flipped Bragi Flipped Bragi 的JIT备货单 WB2506013850787 已超过预计最晚到货时间，请立即处理。\n商品名称：【定向】休闲长袖圆领连衣裙春秋冬款(114/7.12)\n预计最晚到货时间：1748676191000', '2', NULL, NULL, '1', '6,7,5,8,36', 10, '0', '2', '0', '2025-06-01 20:01:36', '2025-06-01 20:01:36', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (395, '【普通备货单未发货提醒】- Flipped Bragi - Flipped Bragi', '店铺 Flipped Bragi Flipped Bragi 的普通备货单 WB2506014140638 已创建5天仍未发货，请检查处理。\n商品名称：图案印花细肩带连衣裙，休闲无袖吊带连衣裙，女装（062/8.27）\n创建时间：1748248968000', '2', NULL, NULL, '1', '6,7,5,8,36', 10, '0', '2', '0', '2025-06-01 20:02:13', '2025-06-01 20:02:13', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (396, '【普通备货单未到货提醒】- Flipped Bragi - Flipped Bragi', '店铺 Flipped Bragi Flipped Bragi 的普通备货单 WB2506014140638 已发货5天仍未收货，请跟进物流状态。\n商品名称：图案印花细肩带连衣裙，休闲无袖吊带连衣裙，女装（062/8.27）\n发货时间：1748242960000', '2', NULL, NULL, '1', '6,7,5,8,36', 10, '0', '2', '0', '2025-06-01 20:04:48', '2025-06-01 20:04:48', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (397, '【JIT备货单到货已逾期提醒】- Flipped Bragi - Flipped Bragi', '店铺 Flipped Bragi Flipped Bragi 的JIT备货单 WB2506013850787 已超过预计最晚到货时间，请立即处理。\n商品名称：【定向】休闲长袖圆领连衣裙春秋冬款(114/7.12)\n预计最晚到货时间：1748676191000', '2', NULL, NULL, '1', '6,7,5,8,36', 10, '0', '2', '0', '2025-06-01 21:49:17', '2025-06-01 21:49:17', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (398, '【JIT备货单到货已逾期提醒】- Flipped Bragi - F店', '店铺 Flipped Bragi F店 的JIT备货单 WB2506013850787 已超过预计最晚到货时间，请立即处理。\n商品名称：【定向】休闲长袖圆领连衣裙春秋冬款(114/7.12)\n预计最晚到货时间：1748676191000', '2', NULL, NULL, '1', '6,7,5,8,36', 10, '0', '2', '0', '2025-06-01 21:52:44', '2025-06-01 21:52:44', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (399, '【JIT备货单到货已逾期提醒】- Flipped Bragi - F店', '店铺 Flipped Bragi F店 的JIT备货单 WB2506013850787 已超过预计最晚到货时间，请立即处理。\n商品名称：【定向】休闲长袖圆领连衣裙春秋冬款(114/7.12)\n预计最晚到货时间：1748676191000', '2', NULL, NULL, '1', '6,7,5,8,36', 10, '0', '2', '0', '2025-06-01 22:29:16', '2025-06-01 22:29:16', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (400, '【普通备货单未发货提醒】- Gudiizai - Gudiizai', '店铺 Gudiizai Gudiizai 的备货单 WB2505282261483 已创建5天仍未发货，请检查处理。\n商品名称：三件装时尚字母图形印花T恤彩棉短袖圆领百搭休闲上衣春夏女装\n创建时间：1748389610000', '2', NULL, NULL, '1', '5,8,36', 6, '0', '2', '0', '2025-06-02 10:11:26', '2025-06-02 10:11:26', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (401, '【JIT备货单到货即将逾期提醒】- Flipped Bragi', 'Flipped Bragi 有 1 个JIT备货单将在24小时内到达预计最晚到货时间，请及时处理。点击查看详情。', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-03 10:07:08', '2025-06-03 10:07:08', NULL, NULL, '0', '{\"templateParams\":{\"orderCount\":1,\"shopName\":\"Flipped Bragi\",\"orders\":[{\"supplierName\":\"Flipped Bragi\",\"orderSn\":\"WB250601583756\",\"productName\":\"【定向】图案印花细肩带连衣裙，休闲无袖吊带连衣裙，女装（062/8.20）\",\"expectLatestArrivalTime\":1748988666000}]},\"templateCode\":\"JIT_SOON_OVERDUE\",\"notificationType\":1}');
INSERT INTO `sys_message` VALUES (402, '【JIT备货单到货即将逾期提醒】- Flipped Bragi', 'Flipped Bragi 有 1 个JIT备货单将在24小时内到达预计最晚到货时间，请及时处理。点击查看详情。', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-03 10:07:08', '2025-06-03 10:07:08', NULL, NULL, '0', '{\"templateParams\":{\"orderCount\":1,\"shopName\":\"Flipped Bragi\",\"orders\":[{\"supplierName\":\"Flipped Bragi\",\"orderSn\":\"WB250601605965\",\"productName\":\"【定向】图案印花细肩带连衣裙，休闲无袖吊带连衣裙，女装（062/7.21）\",\"expectLatestArrivalTime\":1748988916000}]},\"templateCode\":\"JIT_SOON_OVERDUE\",\"notificationType\":1}');
INSERT INTO `sys_message` VALUES (403, '【JIT备货单到货即将逾期提醒】- Flipped Bragi', 'Flipped Bragi 有 1 个JIT备货单将在24小时内到达预计最晚到货时间，请及时处理。点击查看详情。', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-03 10:07:08', '2025-06-03 10:07:08', NULL, NULL, '0', '{\"templateParams\":{\"orderCount\":1,\"shopName\":\"Flipped Bragi\",\"orders\":[{\"supplierName\":\"Flipped Bragi\",\"orderSn\":\"WB250601606108\",\"productName\":\"图案印花细肩带连衣裙，休闲无袖吊带连衣裙，女装（062/7.21）\",\"expectLatestArrivalTime\":1748988940000}]},\"templateCode\":\"JIT_SOON_OVERDUE\",\"notificationType\":1}');
INSERT INTO `sys_message` VALUES (404, '【JIT备货单到货即将逾期提醒】- Flipped Bragi', 'Flipped Bragi 有 1 个JIT备货单将在24小时内到达预计最晚到货时间，请及时处理。点击查看详情。', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-03 10:07:08', '2025-06-03 10:07:08', NULL, NULL, '0', '{\"templateParams\":{\"orderCount\":1,\"shopName\":\"Flipped Bragi\",\"orders\":[{\"supplierName\":\"Flipped Bragi\",\"orderSn\":\"WB250601606107\",\"productName\":\"图案印花细肩带连衣裙，休闲无袖吊带连衣裙，女装（062/7.21）\",\"expectLatestArrivalTime\":1748988940000}]},\"templateCode\":\"JIT_SOON_OVERDUE\",\"notificationType\":1}');
INSERT INTO `sys_message` VALUES (405, '【JIT备货单到货即将逾期提醒】- Flipped Bragi', 'Flipped Bragi 有 1 个JIT备货单将在24小时内到达预计最晚到货时间，请及时处理。点击查看详情。', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-03 10:07:08', '2025-06-03 10:07:08', NULL, NULL, '0', '{\"templateParams\":{\"orderCount\":1,\"shopName\":\"Flipped Bragi\",\"orders\":[{\"supplierName\":\"Flipped Bragi\",\"orderSn\":\"WB2506011514811\",\"productName\":\"图案印花细肩带连衣裙，休闲无袖吊带连衣裙，女装（062/7.21）\",\"expectLatestArrivalTime\":1748991835000}]},\"templateCode\":\"JIT_SOON_OVERDUE\",\"notificationType\":1}');
INSERT INTO `sys_message` VALUES (406, '【JIT备货单到货即将逾期提醒】- Flipped Bragi', 'Flipped Bragi 有 1 个JIT备货单将在24小时内到达预计最晚到货时间，请及时处理。点击查看详情。', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-03 10:07:08', '2025-06-03 10:07:08', NULL, NULL, '0', '{\"templateParams\":{\"orderCount\":1,\"shopName\":\"Flipped Bragi\",\"orders\":[{\"supplierName\":\"Flipped Bragi\",\"orderSn\":\"WB2506011555517\",\"productName\":\"【定向】图案印花细肩带连衣裙，休闲无袖吊带连衣裙，女装（062/7.21）\",\"expectLatestArrivalTime\":1748991893000}]},\"templateCode\":\"JIT_SOON_OVERDUE\",\"notificationType\":1}');
INSERT INTO `sys_message` VALUES (407, '【JIT备货单到货即将逾期提醒】- Flipped Bragi', 'Flipped Bragi 有 1 个JIT备货单将在24小时内到达预计最晚到货时间，请及时处理。点击查看详情。', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-03 10:07:08', '2025-06-03 10:07:08', NULL, NULL, '0', '{\"templateParams\":{\"orderCount\":1,\"shopName\":\"Flipped Bragi\",\"orders\":[{\"supplierName\":\"Flipped Bragi\",\"orderSn\":\"WB2506011515522\",\"productName\":\"【定向】休闲章鱼长袖圆领连衣裙春秋冬款(114/7.12)\",\"expectLatestArrivalTime\":1748991940000}]},\"templateCode\":\"JIT_SOON_OVERDUE\",\"notificationType\":1}');
INSERT INTO `sys_message` VALUES (408, '【JIT备货单到货即将逾期提醒】- Flipped Bragi', 'Flipped Bragi 有 1 个JIT备货单将在24小时内到达预计最晚到货时间，请及时处理。点击查看详情。', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-03 10:07:54', '2025-06-03 10:07:54', NULL, NULL, '0', '{\"templateParams\":{\"orderCount\":1,\"shopName\":\"Flipped Bragi\",\"orders\":[{\"supplierName\":\"Flipped Bragi\",\"orderSn\":\"WB250601583756\",\"productName\":\"【定向】图案印花细肩带连衣裙，休闲无袖吊带连衣裙，女装（062/8.20）\",\"expectLatestArrivalTime\":1748988666000}]},\"templateCode\":\"JIT_SOON_OVERDUE\",\"notificationType\":1}');
INSERT INTO `sys_message` VALUES (409, '【JIT备货单到货即将逾期提醒】- Flipped Bragi', 'Flipped Bragi 有 1 个JIT备货单将在24小时内到达预计最晚到货时间，请及时处理。点击查看详情。', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-03 10:07:54', '2025-06-03 10:07:54', NULL, NULL, '0', '{\"templateParams\":{\"orderCount\":1,\"shopName\":\"Flipped Bragi\",\"orders\":[{\"supplierName\":\"Flipped Bragi\",\"orderSn\":\"WB250601605965\",\"productName\":\"【定向】图案印花细肩带连衣裙，休闲无袖吊带连衣裙，女装（062/7.21）\",\"expectLatestArrivalTime\":1748988916000}]},\"templateCode\":\"JIT_SOON_OVERDUE\",\"notificationType\":1}');
INSERT INTO `sys_message` VALUES (410, '【JIT备货单到货即将逾期提醒】- Flipped Bragi', 'Flipped Bragi 有 1 个JIT备货单将在24小时内到达预计最晚到货时间，请及时处理。点击查看详情。', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-03 10:07:54', '2025-06-03 10:07:54', NULL, NULL, '0', '{\"templateParams\":{\"orderCount\":1,\"shopName\":\"Flipped Bragi\",\"orders\":[{\"supplierName\":\"Flipped Bragi\",\"orderSn\":\"WB250601606108\",\"productName\":\"图案印花细肩带连衣裙，休闲无袖吊带连衣裙，女装（062/7.21）\",\"expectLatestArrivalTime\":1748988940000}]},\"templateCode\":\"JIT_SOON_OVERDUE\",\"notificationType\":1}');
INSERT INTO `sys_message` VALUES (411, '【JIT备货单到货即将逾期提醒】- Flipped Bragi', 'Flipped Bragi 有 1 个JIT备货单将在24小时内到达预计最晚到货时间，请及时处理。点击查看详情。', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-03 10:07:55', '2025-06-03 10:07:55', NULL, NULL, '0', '{\"templateParams\":{\"orderCount\":1,\"shopName\":\"Flipped Bragi\",\"orders\":[{\"supplierName\":\"Flipped Bragi\",\"orderSn\":\"WB250601606107\",\"productName\":\"图案印花细肩带连衣裙，休闲无袖吊带连衣裙，女装（062/7.21）\",\"expectLatestArrivalTime\":1748988940000}]},\"templateCode\":\"JIT_SOON_OVERDUE\",\"notificationType\":1}');
INSERT INTO `sys_message` VALUES (412, '【JIT备货单到货即将逾期提醒】- Flipped Bragi', 'Flipped Bragi 有 1 个JIT备货单将在24小时内到达预计最晚到货时间，请及时处理。点击查看详情。', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-03 10:07:55', '2025-06-03 10:07:55', NULL, NULL, '0', '{\"templateParams\":{\"orderCount\":1,\"shopName\":\"Flipped Bragi\",\"orders\":[{\"supplierName\":\"Flipped Bragi\",\"orderSn\":\"WB2506011514811\",\"productName\":\"图案印花细肩带连衣裙，休闲无袖吊带连衣裙，女装（062/7.21）\",\"expectLatestArrivalTime\":1748991835000}]},\"templateCode\":\"JIT_SOON_OVERDUE\",\"notificationType\":1}');
INSERT INTO `sys_message` VALUES (413, '【JIT备货单到货即将逾期提醒】- Flipped Bragi', 'Flipped Bragi 有 1 个JIT备货单将在24小时内到达预计最晚到货时间，请及时处理。点击查看详情。', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-03 10:07:55', '2025-06-03 10:07:55', NULL, NULL, '0', '{\"templateParams\":{\"orderCount\":1,\"shopName\":\"Flipped Bragi\",\"orders\":[{\"supplierName\":\"Flipped Bragi\",\"orderSn\":\"WB2506011555517\",\"productName\":\"【定向】图案印花细肩带连衣裙，休闲无袖吊带连衣裙，女装（062/7.21）\",\"expectLatestArrivalTime\":1748991893000}]},\"templateCode\":\"JIT_SOON_OVERDUE\",\"notificationType\":1}');
INSERT INTO `sys_message` VALUES (414, '【JIT备货单到货即将逾期提醒】- Flipped Bragi', 'Flipped Bragi 有 1 个JIT备货单将在24小时内到达预计最晚到货时间，请及时处理。点击查看详情。', '2', NULL, NULL, '1', '6,7', 10, '0', '2', '0', '2025-06-03 10:07:55', '2025-06-03 10:07:55', NULL, NULL, '0', '{\"templateParams\":{\"orderCount\":1,\"shopName\":\"Flipped Bragi\",\"orders\":[{\"supplierName\":\"Flipped Bragi\",\"orderSn\":\"WB2506011515522\",\"productName\":\"【定向】休闲章鱼长袖圆领连衣裙春秋冬款(114/7.12)\",\"expectLatestArrivalTime\":1748991940000}]},\"templateCode\":\"JIT_SOON_OVERDUE\",\"notificationType\":1}');
INSERT INTO `sys_message` VALUES (415, '【普通备货单未发货提醒】- ', '以下店铺的普通备货单已创建5天仍未发货，请检查处理。\n\nFlipped Bragi WB2506014140638 \n', '2', NULL, NULL, '1', '6,7', NULL, '0', '2', '0', '2025-06-03 11:57:29', '2025-06-03 11:57:29', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (416, '【普通备货单未发货提醒】- ', '以下店铺的普通备货单已创建5天仍未发货，请检查处理。\n\nGudiizai WB2505282261483 \n', '2', NULL, NULL, '1', '5,8', NULL, '0', '2', '0', '2025-06-03 11:57:29', '2025-06-03 11:57:29', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (417, '【JIT备货单到货即将逾期汇总提醒】- 2025-06-03', '以下店铺的JIT备货单将于24小时内到达预计最晚到货时间，请及时处理：\n\nFlipped Bragi: WB250601583756, WB250601605965, WB250601606108, WB250601606107, WB2506011514811, WB2506011555517, WB2506011515522\n', '2', NULL, NULL, '1', '6,7,5,8', NULL, '0', '2', '0', '2025-06-03 14:47:39', '2025-06-03 14:47:39', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (418, '【普通备货单未发货汇总提醒】- 2025-06-03', '以下店铺的普通备货单已创建5天仍未发货，请检查处理：\n\nGudiizai: WB2505282261483, WB2505292968302, WB2505292997735, WB2505283665815\nFlipped Bragi: WB2505292968302, WB2505292997735, WB2505283665815, WB2505282261483\n', '2', NULL, NULL, '1', '6,7,5,8', NULL, '0', '2', '0', '2025-06-03 14:52:25', '2025-06-03 14:52:25', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (419, '【普通备货单未发货汇总提醒】- 2025-06-03', '以下店铺的普通备货单已创建5天仍未发货，请检查处理：\n\nGudiizai: WB2505282261483, WB2505292968302, WB2505292997735, WB2505283665815\nFlipped Bragi: WB2505292968302, WB2505292997735, WB2505283665815, WB2505282261483\n', '2', NULL, NULL, '1', '6,7,5,8', NULL, '0', '2', '0', '2025-06-03 14:56:21', '2025-06-03 14:56:21', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (420, '【普通备货单未发货汇总提醒】- 2025-06-03', '以下店铺的普通备货单已创建5天仍未发货，请检查处理：\n\nGudiizai: WB2505282261483, WB2505292968302, WB2505292997735, WB2505283665815\nFlipped Bragi: WB2505292968302, WB2505292997735, WB2505283665815, WB2505282261483\n', '2', NULL, NULL, '1', '6,7,5,8', NULL, '0', '2', '0', '2025-06-03 15:00:34', '2025-06-03 15:00:34', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (421, '【普通备货单未发货汇总提醒】- 2025-06-03', '以下店铺的普通备货单已创建5天仍未发货，请检查处理：\n\nGudiizai: WB2505282261483, WB2505283665815\nFlipped Bragi: WB2505292968302, WB2505292997735\n', '2', NULL, NULL, '1', '6,7,5,8', NULL, '0', '2', '0', '2025-06-03 15:06:11', '2025-06-03 15:06:11', NULL, NULL, '0', NULL);
INSERT INTO `sys_message` VALUES (422, '【普通备货单未发货汇总提醒】- 2025-06-03', '以下店铺的普通备货单已创建5天仍未发货，请检查处理：\n\nGudiizai: WB2505282261483, WB2505283665815\nFlipped Bragi: WB2505292968302, WB2505292997735\n', '2', NULL, NULL, '1', '6,7,5,8', NULL, '0', '2', '0', '2025-06-03 15:18:35', '2025-06-03 15:18:35', NULL, NULL, '0', NULL);

SET FOREIGN_KEY_CHECKS = 1;
