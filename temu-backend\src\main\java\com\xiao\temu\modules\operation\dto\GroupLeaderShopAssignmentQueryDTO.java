package com.xiao.temu.modules.operation.dto;

import lombok.Data;

/**
 * 组长店铺分配查询条件
 */
@Data
public class GroupLeaderShopAssignmentQueryDTO {

    /**
     * 当前页码
     */
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    private Integer pageSize = 10;

    /**
     * 运营组ID
     */
    private Long groupId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 被分配用户ID
     */
    private Long userId;

    /**
     * 被分配用户名称
     */
    private String userName;

    /**
     * 权限类型(0只读1读写)
     */
    private String permissionType;

    /**
     * 状态(0正常1禁用)
     */
    private String status;
} 