/**
 * 进度页
 */
const worklistApi = require('../../api/worklistApi');
const productionProgressApi = require('../../api/productionProgressApi');
const progressConfig = require('../../utils/progressConfig');

Page({
  data: {
    userInfo: {},
    shopId: null,
    subPurchaseOrderSn: '',
    orderNumber: '',
    progressDetail: null,
    progressLogs: [],
    loading: false,
    processing: false,
    // 工序类型映射
    progressTypeMap: progressConfig.PROGRESS_TYPE_MAP,
    // 工序项目列表（用于在wxml中循环生成）
    progressItems: []
  },

  onLoad(options) {
    this.loadUserInfo();
    
    // 从URL参数获取orderNumber、subPurchaseOrderSn和shopId
    const { orderNumber, subPurchaseOrderSn, shopId } = options;
    
    // 动态生成工序项目列表
    const progressItems = [];
    Object.keys(progressConfig.PROGRESS_TYPE_MAP).forEach(type => {
      progressItems.push({
        type: type,
        status: `${type}Status`,
        time: `${type}Time`,
        role: progressConfig.PROGRESS_ROLE_MAP[type]
      });
    });
    
    if (subPurchaseOrderSn && shopId) {
      this.setData({
        orderNumber: orderNumber || '',
        subPurchaseOrderSn: subPurchaseOrderSn,
        shopId: Number(shopId),
        progressItems: progressItems
      }, () => {
        this.loadProgressDetail();
        this.loadProgressLogs();
      });
    } else {
      wx.showToast({
        title: '参数不完整',
        icon: 'none'
      });
      
      // 即使参数不完整，也设置progressItems
      this.setData({ progressItems: progressItems });
    }
  },

  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({ userInfo });
    } else {
      // 如果没有存储的用户信息，使用模拟数据
      const mockUserInfo = {
        workerId: 'GH001',
        userName: '张师傅',
        roleName: '烧花/剪图工',
        department: '生产部'
      };
      this.setData({ userInfo: mockUserInfo });
    }
  },

  // 获取工序中文名称
  getProgressTypeName(progressType) {
    return this.data.progressTypeMap[progressType] || progressType;
  },
  
  // 格式化时间为年-月-日 时:分:秒
  formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';
    
    // 处理ISO格式日期
    if (dateTimeStr.includes('T') && dateTimeStr.includes('+')) {
      const date = new Date(dateTimeStr);
      return date.getFullYear() + '-' + 
        this.padZero(date.getMonth() + 1) + '-' + 
        this.padZero(date.getDate()) + ' ' + 
        this.padZero(date.getHours()) + ':' + 
        this.padZero(date.getMinutes()) + ':' + 
        this.padZero(date.getSeconds());
    }
    
    // 如果已经是标准格式，直接返回
    return dateTimeStr;
  },
  
  // 数字补零
  padZero(num) {
    return num < 10 ? '0' + num : num;
  },

  // 加载生产进度详情
  loadProgressDetail() {
    const { shopId, subPurchaseOrderSn } = this.data;
    if (!shopId || !subPurchaseOrderSn) return;
    
    this.setData({ loading: true });
    
    productionProgressApi.getProgressDetail(shopId, subPurchaseOrderSn)
      .then(detail => {
        console.log('获取到的进度详情:', detail);
        // 设置productInfo
        const productInfo = this.data.orderNumber ? 
          `订单号: ${this.data.orderNumber}` : '暂无产品信息';
        
        // 格式化时间
        if (detail.cuttingTime) detail.cuttingTime = this.formatDateTime(detail.cuttingTime);
        if (detail.workshopTime) detail.workshopTime = this.formatDateTime(detail.workshopTime);
        if (detail.trimmingTime) detail.trimmingTime = this.formatDateTime(detail.trimmingTime);
        if (detail.inspectionTime) detail.inspectionTime = this.formatDateTime(detail.inspectionTime);
        if (detail.packagingTime) detail.packagingTime = this.formatDateTime(detail.packagingTime);
        if (detail.shippingTime) detail.shippingTime = this.formatDateTime(detail.shippingTime);
        
        this.setData({ 
          progressDetail: {
            ...detail,
            productInfo
          }
        });
      })
      .catch(err => {
        console.error('获取进度详情失败:', err);
        wx.showToast({
          title: '获取进度详情失败',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  },
  
  // 加载生产进度日志
  loadProgressLogs() {
    const { shopId, subPurchaseOrderSn } = this.data;
    if (!shopId || !subPurchaseOrderSn) return;
    
    productionProgressApi.getProgressLogs(shopId, subPurchaseOrderSn)
      .then(logs => {
        console.log('获取到的进度日志:', logs);
        
        // 处理日志数据，转换工序类型为中文，格式化时间
        const processedLogs = (logs || []).map(log => {
          return {
            ...log,
            progressTypeName: this.getProgressTypeName(log.progressType),
            operationTime: this.formatDateTime(log.operationTime)
          };
        });
        
        this.setData({ progressLogs: processedLogs });
      })
      .catch(err => {
        console.error('获取进度日志失败:', err);
      });
  },

  // 确认更新进度
  confirmProcess() {
    if (this.data.processing) return;
    
    const { userInfo, shopId, subPurchaseOrderSn } = this.data;
    if (!shopId || !subPurchaseOrderSn || !userInfo || !userInfo.roleName) {
      wx.showToast({
        title: '数据不完整，请重试',
        icon: 'none'
      });
      return;
    }
    
    // 根据角色名称确定进度类型
    const progressType = progressConfig.ROLE_PROGRESS_MAP[userInfo.roleName];
    if (!progressType) {
      wx.showModal({
        title: '权限不足',
        content: '您的角色无法处理生产进度',
        showCancel: false
      });
      return;
    }
    
    // 检查该环节是否已经完成
    const { progressDetail } = this.data;
    if (progressDetail) {
      const statusField = `${progressType}Status`;
      if (progressDetail[statusField] === 1) {
        wx.showModal({
          title: '无需处理',
          content: '该环节已经完成，无需重复处理',
          showCancel: false
        });
        return;
      }
    }
    
    this.setData({ processing: true });
    wx.showLoading({ title: '提交中...' });
    
    // 准备请求参数，增加operationType和remarks参数
    const params = {
      shopId: shopId,
      subPurchaseOrderSn: subPurchaseOrderSn,
      progressType: progressType,
      operationType: "1", // 确认完成工序，固定为"1"
      remarks: "" // 备注字段，可以为空字符串
    };
    
    // 调用更新进度接口
    productionProgressApi.updateProgress(params)
      .then(res => {
        console.log('更新进度成功:', res);
        wx.showToast({
          title: '处理成功',
          icon: 'success'
        });
        
        // 重新加载进度详情和日志
        this.loadProgressDetail();
        this.loadProgressLogs();
      })
      .catch(err => {
        console.error('更新进度失败:', err);
        wx.showModal({
          title: '处理失败',
          content: '无法更新生产进度，请重试',
          showCancel: false
        });
      })
      .finally(() => {
        this.setData({ processing: false });
        wx.hideLoading();
      });
  },

  // 获取进度状态文本
  getStatusText(status) {
    return status === 1 ? '已完成' : '未完成';
  },
  
  // 获取进度状态样式类
  getStatusClass(status) {
    return status === 1 ? 'status-done' : 'status-pending';
  },

  goToHome() {
    wx.navigateTo({ url: '/pages/home/<USER>' });
  },

  goToScan() {
    wx.navigateTo({ url: '/pages/scan/scan' });
  },

  goToWorklist() {
    wx.navigateTo({ url: '/pages/worklist/worklist' });
  }
}); 