/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : temu_api

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 04/06/2025 02:02:25
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for production_group_member
-- ----------------------------
DROP TABLE IF EXISTS `production_group_member`;
CREATE TABLE `production_group_member`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `group_id` bigint NOT NULL COMMENT '生产组ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `join_time` datetime NULL DEFAULT NULL COMMENT '加入时间',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_group_user`(`group_id` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '生产组成员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of production_group_member
-- ----------------------------
INSERT INTO `production_group_member` VALUES (2, 1, 23, '2025-05-10 15:51:48', '0');
INSERT INTO `production_group_member` VALUES (18, 1, 34, '2025-05-12 08:37:21', '0');
INSERT INTO `production_group_member` VALUES (21, 2, 41, '2025-05-28 12:29:06', '0');
INSERT INTO `production_group_member` VALUES (22, 3, 41, '2025-06-04 01:05:34', '0');

SET FOREIGN_KEY_CHECKS = 1;
