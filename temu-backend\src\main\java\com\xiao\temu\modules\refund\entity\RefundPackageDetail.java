package com.xiao.temu.modules.refund.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 退货包裹明细实体类
 */
@Data
@TableName("refund_package_detail")
public class RefundPackageDetail {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 包裹单号
     */
    private String packageSn;

    /**
     * 商品SKU ID
     */
    private Long productSkuId;

    /**
     * 商品SKC ID
     */
    private Long productSkcId;

    /**
     * 商品SPU ID
     */
    private Long productSpuId;

    /**
     * 备货单号
     */
    private String purchaseSubOrderSn;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 主规格
     */
    private String mainSaleSpec;

    /**
     * 次规格
     */
    private String secondarySaleSpec;

    /**
     * 缩略图
     */
    private String thumbUrl;

    /**
     * 订单类型描述
     */
    private String orderTypeDesc;

    /**
     * 退货原因描述，JSON格式
     */
    private String reasonDesc;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 出库时间戳
     */
    private Long outboundTime;

    /**
     * 同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 