package com.xiao.temu.modules.purchaseorderv.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 仓库信息实体类
 */
@Data
@Accessors(chain = true)
@TableName("warehouse_info")
public class WarehouseInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 子仓库ID
     */
    private Long subWid;
    
    /**
     * 仓库名称
     */
    private String name;
    
    /**
     * 收货地址
     */
    private String receiveAddress;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 