package com.xiao.temu.modules.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商品上新SKC信息实体类
 */
@Data
@TableName("product_new_arrival_skc")
public class ProductNewArrivalSkc {
    
    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /** 店铺ID */
    @TableField("shop_id")
    private Long shopId;
    
    /** 商品ID */
    @TableField("product_id")
    private Long productId;
    
    /** SKC ID */
    @TableField("skc_id")
    private Long skcId;
    
    /** 商品goods_skc_id */
    @TableField("goods_skc_id")
    private Long goodsSkcId;
    
    /** select_id */
    @TableField("select_id")
    private Long selectId;
    
    /** 颜色名称 */
    @TableField("color_name")
    private String colorName;
    
    /** 外部编码 */
    @TableField("ext_code")
    private String extCode;
    
    /** 核价时间 */
    @TableField("price_verification_time")
    private LocalDateTime priceVerificationTime;
    
    /** 加入站点时间 */
    @TableField("added_to_site_time")
    private LocalDateTime addedToSiteTime;
    
    /** 样品发布完成时间 */
    @TableField("sample_posting_finished_time")
    private LocalDateTime samplePostingFinishedTime;
    
    /** 选品时间 */
    @TableField("selected_time")
    private LocalDateTime selectedTime;
    
    /** 首次采购时间 */
    @TableField("first_purchase_time")
    private LocalDateTime firstPurchaseTime;
    
    /** 创建时间 */
    @TableField("created_time")
    private LocalDateTime createdTime;
    
    /** 质检完成时间 */
    @TableField("qc_completed_time")
    private LocalDateTime qcCompletedTime;
    
    /** 同步时间 */
    @TableField("sync_time")
    private LocalDateTime syncTime;
    
    /** 创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /** 更新时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;
} 