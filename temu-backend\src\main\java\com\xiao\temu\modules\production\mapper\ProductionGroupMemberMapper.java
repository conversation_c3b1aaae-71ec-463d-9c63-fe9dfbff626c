package com.xiao.temu.modules.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.production.dto.ProductionGroupMemberDTO;
import com.xiao.temu.modules.production.entity.ProductionGroupMember;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产组成员Mapper接口
 */
@Mapper
public interface ProductionGroupMemberMapper extends BaseMapper<ProductionGroupMember> {

    /**
     * 分页查询生产组成员列表
     *
     * @param page    分页参数
     * @param groupId 生产组ID
     * @param keyword 搜索关键词
     * @return 成员列表
     */
    IPage<ProductionGroupMemberDTO> selectMemberList(Page<ProductionGroupMemberDTO> page, @Param("groupId") Long groupId, @Param("keyword") String keyword);

    /**
     * 查询生产组成员列表（不分页）
     *
     * @param groupId 生产组ID
     * @return 成员列表
     */
    List<ProductionGroupMemberDTO> selectAllMembers(@Param("groupId") Long groupId);

    /**
     * 查询用户是否为生产组成员
     *
     * @param groupId 生产组ID
     * @param userId  用户ID
     * @return 结果
     */
    int checkUserInGroup(@Param("groupId") Long groupId, @Param("userId") Long userId);

    /**
     * 统计生产组成员数量
     *
     * @param groupId 生产组ID
     * @return 成员数量
     */
    int countGroupMembers(@Param("groupId") Long groupId);

    /**
     * 批量添加生产组成员
     *
     * @param groupId  生产组ID
     * @param userIds  用户ID列表
     * @param joinTime 加入时间
     * @return 结果
     */
    int batchAddMembers(@Param("groupId") Long groupId, @Param("userIds") List<Long> userIds, @Param("joinTime") java.util.Date joinTime);

    /**
     * 删除生产组成员
     *
     * @param groupId 生产组ID
     * @param userId  用户ID
     * @return 结果
     */
    int deleteMember(@Param("groupId") Long groupId, @Param("userId") Long userId);

    /**
     * 删除生产组的所有成员
     *
     * @param groupId 生产组ID
     * @return 结果
     */
    int deleteAllMembers(@Param("groupId") Long groupId);

    /**
     * 查询生产组中的用户ID列表
     *
     * @param groupId 生产组ID
     * @return 用户ID列表
     */
    List<Long> selectUserIdsByGroupId(@Param("groupId") Long groupId);
} 