package com.xiao.temu.modules.violation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.violation.entity.ShopViolationDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 店铺违规详情Mapper接口
 */
@Mapper
public interface ShopViolationDetailMapper extends BaseMapper<ShopViolationDetail> {
    
    /**
     * 检查违规详情是否存在
     *
     * @param shopId 店铺ID
     * @param punishSn 违规编号
     * @return 是否存在
     */
    Integer checkDetailExists(@Param("shopId") Long shopId, @Param("punishSn") String punishSn);
    
    /**
     * 根据店铺ID和违规编号删除违规详情
     *
     * @param shopId 店铺ID
     * @param punishSn 违规编号
     * @return 删除的记录数
     */
    Integer deleteByShopIdAndPunishSn(@Param("shopId") Long shopId, @Param("punishSn") String punishSn);
} 