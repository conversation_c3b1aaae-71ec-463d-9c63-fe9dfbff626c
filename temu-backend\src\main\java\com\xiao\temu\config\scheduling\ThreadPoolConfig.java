package com.xiao.temu.config.scheduling;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置类
 */
@Configuration
@EnableAsync
public class ThreadPoolConfig {

    @Value("${thread.pool.core-size}")
    private int corePoolSize;

    @Value("${thread.pool.max-size}")
    private int maxPoolSize;

    @Value("${thread.pool.queue-capacity}")
    private int queueCapacity;

    @Value("${thread.pool.keep-alive-seconds}")
    private int keepAliveSeconds;

    @Value("${thread.sales-sync-pool.core-size}")
    private int salesSyncCorePoolSize;

    @Value("${thread.sales-sync-pool.max-size}")
    private int salesSyncMaxPoolSize;

    @Value("${thread.sales-sync-pool.queue-capacity}")
    private int salesSyncQueueCapacity;

    @Value("${thread.sales-sync-pool.keep-alive-seconds}")
    private int salesSyncKeepAliveSeconds;

    /**
     * 通用异步线程池
     */
    @Bean(name = "taskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix("common-task-");
        // 线程池对拒绝任务的处理策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 初始化线程池
        executor.initialize();
        return executor;
    }

    /**
     * 销售数据同步专用线程池
     */
    @Bean(name = "salesSyncExecutor")
    public ThreadPoolTaskExecutor salesSyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(salesSyncCorePoolSize);
        executor.setMaxPoolSize(salesSyncMaxPoolSize);
        executor.setQueueCapacity(salesSyncQueueCapacity);
        executor.setKeepAliveSeconds(salesSyncKeepAliveSeconds);
        executor.setThreadNamePrefix("sales-sync-");
        // 线程池对拒绝任务的处理策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 初始化线程池
        executor.initialize();
        return executor;
    }
} 