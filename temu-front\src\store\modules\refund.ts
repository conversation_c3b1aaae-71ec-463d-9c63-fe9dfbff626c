import { defineStore } from 'pinia'
import { getRefundPackages, getUserShops } from '@/api/refund'
import { getUserAccessibleShops } from '@/api/shop'
import type { RefundRequestDTO, RefundPackageResult, Shop } from '@/types/refund'

interface RefundState {
  loading: boolean;
  shops: Shop[];
  refundResult: RefundPackageResult | null;
  shopId: number | null;
  timeRange: [string, string] | null;
}

export const useRefundStore = defineStore('refund', {
  state: (): RefundState => ({
    loading: false,
    shops: [],
    refundResult: null,
    shopId: null,
    timeRange: null
  }),
  
  actions: {
    /**
     * 获取退货包裹列表
     */
    async getRefundPackages(params: RefundRequestDTO) {
      this.loading = true
      
      // 确保请求参数完整
      if (this.shopId && !params.shopId) {
        params.shopId = this.shopId;
      }
      
      // 转换日期格式为毫秒时间戳
      const requestParams = { ...params };
      if (requestParams.outboundTimeStart) {
        requestParams.outboundTimeStart = new Date(requestParams.outboundTimeStart).getTime().toString();
      }
      if (requestParams.outboundTimeEnd) {
        // 结束日期设置为当天的最后一毫秒
        const endDate = new Date(requestParams.outboundTimeEnd);
        endDate.setHours(23, 59, 59, 999);
        requestParams.outboundTimeEnd = endDate.getTime().toString();
      }
      
      console.log('请求退货包裹列表参数:', JSON.stringify(requestParams, null, 2));
      
      try {
        const result = await getRefundPackages(requestParams);
        this.refundResult = result.data || null;
        
        return result.data;
      } catch (error) {
        console.error('获取退货明细失败:', error);
        return null;
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 获取用户可操作的店铺列表
     */
    async getUserShops() {
      try {
        const result = await getUserShops();
        // 由于request.ts 中已经处理了响应转换，这里直接使用result
        if (result && Array.isArray(result)) {
          this.shops = result;
        } else if (result && result.data && Array.isArray(result.data)) {
          this.shops = result.data;
        } else {
          this.shops = [];
        }
        return this.shops;
      } catch (error) {
        console.error('获取店铺列表失败:', error);
        this.shops = [];
        return [];
      }
    },
    
    /**
     * 获取用户可访问的店铺列表（公共API）
     */
    async getUserAccessibleShops() {
      try {
        const result = await getUserAccessibleShops();
        // 处理响应数据
        if (result && Array.isArray(result)) {
          this.shops = result;
        } else if (result && result.data && Array.isArray(result.data)) {
          this.shops = result.data;
        } else {
          this.shops = [];
        }
        return this.shops;
      } catch (error) {
        console.error('获取可访问店铺列表失败:', error);
        this.shops = [];
        return [];
      }
    },
    
    /**
     * 设置店铺ID
     */
    setShopId(shopId: number | null) {
      this.shopId = shopId;
    },
    
    /**
     * 设置时间范围
     */
    setTimeRange(range: [string, string] | null) {
      this.timeRange = range;
    },
    
    /**
     * 重置查询条件
     */
    resetQuery() {
      this.shopId = null;
      this.timeRange = null;
    }
  }
}) 