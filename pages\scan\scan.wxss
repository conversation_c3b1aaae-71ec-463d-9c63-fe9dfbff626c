.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 标签页样式 */
.tabs {
  display: flex;
  background: #fff;
  border-bottom: 1px solid #eee;
  height: 88rpx;
}

.tab {
  flex: 1;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  position: relative;
  color: #666;
}

.tab.active {
  color: #1890ff;
  font-weight: 500;
}

.tab.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 6rpx;
  background: #1890ff;
  border-radius: 6rpx 6rpx 0 0;
}

/* 标签页内容包装器 */
.tab-content-wrapper {
  position: relative;
  flex: 1;
  width: 100%;
  min-height: calc(100vh - 88rpx - 164rpx); /* 减去tabs高度和header高度(包括状态栏) */
  box-sizing: border-box;
}

/* 标签页内容统一样式 */
.tab-content {
  transition: all 0.3s ease;
  width: 100%;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 30rpx;
  box-sizing: border-box;
}

.active-content {
  opacity: 1;
  z-index: 2;
  visibility: visible;
}

.hidden-content {
  opacity: 0;
  z-index: 1;
  visibility: hidden;
}

/* 普通扫码页面添加顶部间距，使其与快速扫码页面布局一致 */
.normal-scan-spacer {
  width: 100%;
  height: 190rpx; /* 与fast-scan-config高度接近 */
  box-sizing: border-box;
}

/* 内容区域容器 */
.content {
  min-height: calc(100vh - 88rpx - 164rpx); /* 减去tabs高度和header高度(包括状态栏) */
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  box-sizing: border-box;
}

.result-content {
  padding: 20rpx;
  justify-content: flex-start;
  padding-bottom: 100rpx;
}

/* 扫码区域样式 */
.scan-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 30rpx;
  flex: 1;
  box-sizing: border-box;
}

.scanner-frame {
  width: 480rpx;
  height: 480rpx;
  position: relative;
  border-radius: 24rpx;
  overflow: hidden;
  margin-bottom: 64rpx;
  border: 2px solid transparent;
}

.scan-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 24rpx;
}

.scan-corner {
  position: absolute;
  width: 60rpx;
  height: 60rpx;
  border-color: #1890ff;
  border-style: solid;
  border-width: 6rpx;
}

.corner-tl {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
  border-radius: 16rpx 0 0 0;
}

.corner-tr {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
  border-radius: 0 16rpx 0 0;
}

.corner-bl {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
  border-radius: 0 0 0 16rpx;
}

.corner-br {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
  border-radius: 0 0 16rpx 0;
}

.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: linear-gradient(to right, transparent, #1890ff, transparent);
  animation: scan 2s linear infinite;
}

@keyframes scan {
  from {
    top: 0;
  }
  to {
    top: 480rpx;
  }
}

.scan-button {
  background: #1890ff;
  color: white;
  border: none;
  padding: 24rpx 48rpx;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 16rpx rgba(24, 144, 255, 0.2);
}

.album-btn-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: auto;
  padding-bottom: 40rpx;
  box-sizing: border-box;
}

.album-btn {
  background: transparent;
  border: none;
  color: #666;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  padding: 16rpx 32rpx;
}

.album-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

.btn-text {
  font-size: 28rpx;
}

/* 快速扫码配置区域 */
.fast-scan-config {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.config-header {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 24rpx;
}

.config-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 角色选择区域样式 */
.role-section {
  margin-top: 0;
}

.role-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  color: #333;
}

.role-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.role-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  border: 1rpx solid #eee;
  border-radius: 16rpx;
  background: #fff;
}

.role-item.selected {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

.role-name {
  font-size: 28rpx;
  color: #333;
  margin-left: 8rpx;
}

.config-note {
  font-size: 24rpx;
  color: #999;
  margin-top: 24rpx;
}

/* 角色项结构 */
.role-checkbox {
  display: flex;
  margin-right: 10rpx;
  flex-shrink: 0;
}

.role-name {
  flex: 1;
  padding: 6rpx 0;
  display: flex;
  align-items: center;
}

/* 扫码结果区域 */
.scan-result {
  background: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  width: 100%;
}

.result-header {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 30rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #eee;
  color: #333;
}

.result-info {
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
  font-size: 28rpx;
}

.info-label {
  color: #666;
  width: 160rpx;
}

.info-value {
  font-weight: 500;
  color: #333;
}

.progress-section {
  margin-top: 40rpx;
}

.progress-title {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 24rpx;
  color: #333;
}

.progress-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.progress-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  border: 1rpx solid #eee;
  border-radius: 16rpx;
  background: #fff;
}

.progress-item.selected {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

.progress-item.completed {
  border-color: #52c41a;
}

.progress-name {
  display: flex;
  align-items: center;
  flex: 1;
}

.progress-name text {
  margin-left: 12rpx;
}

.progress-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
  color: #666;
}

.completed .progress-status {
  background: #f6ffed;
  color: #52c41a;
}

.progress-checkbox {
  display: flex;
  margin-right: 10rpx;
  flex-shrink: 0;
}

.progress-item.selected .progress-checkbox {
  background-color: #1890ff;
  border-color: #1890ff;
}

.progress-item.selected .progress-checkbox:after {
  content: '';
  position: absolute;
  width: 10rpx;
  height: 20rpx;
  border: solid white;
  border-width: 0 4rpx 4rpx 0;
  top: 4rpx;
  left: 12rpx;
  transform: rotate(45deg);
}

.progress-text {
  flex: 1;
  padding: 6rpx 0;
}

.action-buttons {
  margin-top: 48rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  width: 100%;
}

.btn {
  border: none;
  padding: 24rpx 0;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: 500;
  width: 100%;
  text-align: center;
  line-height: 1.4;
  box-sizing: border-box;
}

.btn-primary {
  background: #1890ff;
  color: white;
  box-shadow: 0 8rpx 16rpx rgba(24, 144, 255, 0.2);
}

.button-hover {
  opacity: 0.8;
  transform: translateY(2rpx);
  transition: all 0.2s;
}

.btn-outline {
  background: transparent;
  border: 1rpx solid #1890ff;
  color: #1890ff;
}

.btn-disabled {
  background: #f5f5f5;
  color: #999;
  box-shadow: none;
}

/* 批量模式导航 */
.batch-nav-container {
  background: #fff;
  padding: 12rpx 20rpx;
  border-top: 1rpx solid #eee;
  width: 100%;
  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.05);
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 10;
}

.batch-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.compact-batch-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.nav-buttons {
  display: flex;
  align-items: center;
  flex: 3;
  max-width: 70%;
}

.batch-counter {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
  margin: 0 4rpx;
  min-width: 40rpx;
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #1890ff;
  padding: 8rpx 12rpx;
  border: 1rpx solid #1890ff;
  border-radius: 40rpx;
  background-color: #fff;
  min-width: 80rpx;
  line-height: 1.4;
  height: 60rpx;
}

.nav-btn.prev {
  margin-right: 4rpx;
  justify-content: center;
}

.nav-btn.next {
  margin-left: 4rpx;
  justify-content: center;
}

.nav-icon {
  width: 24rpx;
  height: 24rpx;
  margin: 0 2rpx;
}

.complete-all-btn {
  flex: 2;
  padding: 0;
  border-radius: 40rpx;
  font-size: 24rpx;
  font-weight: 500;
  text-align: center;
  box-sizing: border-box;
  margin-left: 10rpx;
  min-width: 100rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.complete-all-btn.btn-primary {
  background: #1890ff;
  color: white;
  box-shadow: 0 4rpx 8rpx rgba(24, 144, 255, 0.2);
}

.complete-all-btn.btn-disabled {
  background: #f5f5f5;
  color: #999;
  box-shadow: none;
}

.status-bar {
  width: 100%;
  background-color: #ffffff;
} 