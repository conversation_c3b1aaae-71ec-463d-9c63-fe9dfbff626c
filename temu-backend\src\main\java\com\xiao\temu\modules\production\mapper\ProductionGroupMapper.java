package com.xiao.temu.modules.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.production.dto.ProductionGroupDTO;
import com.xiao.temu.modules.production.dto.QueryGroupDTO;
import com.xiao.temu.modules.production.entity.ProductionGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 生产组Mapper接口
 */
@Mapper
public interface ProductionGroupMapper extends BaseMapper<ProductionGroup> {

    /**
     * 分页查询生产组列表
     *
     * @param page    分页参数
     * @param queryDTO 查询条件
     * @return 生产组列表
     */
    IPage<ProductionGroupDTO> selectGroupList(Page<ProductionGroupDTO> page, @Param("query") QueryGroupDTO queryDTO);

    /**
     * 查询生产组详情
     *
     * @param groupId 生产组ID
     * @return 生产组详情
     */
    ProductionGroupDTO selectGroupById(@Param("groupId") Long groupId);

    /**
     * 检查生产组名称是否唯一
     *
     * @param groupName 生产组名称
     * @param groupId   生产组ID（更新时排除自身）
     * @return 结果
     */
    int checkGroupNameUnique(@Param("groupName") String groupName, @Param("groupId") Long groupId);

    /**
     * 更新生产组状态
     *
     * @param groupId 生产组ID
     * @param status  状态
     * @return 结果
     */
    int updateStatus(@Param("groupId") Long groupId, @Param("status") String status);

    /**
     * 根据负责人ID查询生产组列表
     *
     * @param leaderId 负责人ID
     * @return 生产组列表
     */
    List<ProductionGroup> selectGroupsByLeaderId(@Param("leaderId") Long leaderId);

    /**
     * 根据组ID查询负责人ID
     *
     * @param groupId 生产组ID
     * @return 负责人ID
     */
    @Select("SELECT leader_id FROM production_group WHERE group_id = #{groupId} AND status = '0'")
    Long selectLeaderIdByGroupId(@Param("groupId") Long groupId);
} 