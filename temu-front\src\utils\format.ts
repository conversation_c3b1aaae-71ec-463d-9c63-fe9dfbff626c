/**
 * 格式化时间
 * @param time 时间
 * @param pattern 格式化模式 默认：YYYY-MM-DD HH:mm:ss
 * @returns 格式化后的时间字符串
 */
export function formatTime(time: string | number | Date, pattern = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!time) {
    return '-'
  }
  
  let date: Date
  if (typeof time === 'string') {
    // 处理只有日期部分的字符串
    if (time.indexOf('T') === -1 && time.indexOf(' ') === -1) {
      time = time + ' 00:00:00'
    }
    date = new Date(time)
  } else if (typeof time === 'number') {
    date = new Date(time)
  } else {
    date = time
  }
  
  // 如果日期无效，返回原始字符串或'-'
  if (isNaN(date.getTime())) {
    return typeof time === 'string' ? time : '-'
  }
  
  const o: Record<string, number | string> = {
    'M+': date.getMonth() + 1, // 月份
    'D+': date.getDate(), // 日
    'H+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    'S': date.getMilliseconds() // 毫秒
  }
  
  // 替换年份
  if (/(Y+)/.test(pattern)) {
    pattern = pattern.replace(RegExp.$1, 
      (date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  
  // 替换其他时间部分
  for (const k in o) {
    if (new RegExp('(' + k + ')').test(pattern)) {
      pattern = pattern.replace(RegExp.$1, 
        RegExp.$1.length === 1 ? o[k] as string : 
        ('00' + o[k]).substr(('' + o[k]).length))
    }
  }
  
  return pattern
} 

/**
 * 将日期字符串转换为LocalDateTime兼容的格式
 * 解决前后端日期格式不匹配问题
 * 
 * @param date 日期字符串，Date对象或时间戳
 * @param withTime 是否添加时间部分（默认true）
 * @returns ISO8601格式的日期时间字符串 (例如: 2025-03-23T00:00:00)
 */
export function formatDateTime(date: string | Date | number | undefined, withTime = true): string | undefined {
  if (!date) {
    return undefined
  }
  
  let dateObj: Date
  
  if (typeof date === 'string') {
    // 处理只有日期部分的字符串，如 "2025-03-23"
    if (date.indexOf('T') === -1 && date.indexOf(' ') === -1) {
      if (withTime) {
        // 添加时间部分
        dateObj = new Date(`${date}T00:00:00`)
      } else {
        dateObj = new Date(date)
      }
    } else {
      // 已经有时间部分的字符串
      dateObj = new Date(date)
    }
  } else if (typeof date === 'number') {
    dateObj = new Date(date)
  } else {
    dateObj = date
  }
  
  // 日期无效则返回undefined
  if (isNaN(dateObj.getTime())) {
    return undefined
  }
  
  // 返回ISO8601格式的字符串，后端LocalDateTime可以直接解析
  return dateObj.toISOString().replace('Z', '')
} 