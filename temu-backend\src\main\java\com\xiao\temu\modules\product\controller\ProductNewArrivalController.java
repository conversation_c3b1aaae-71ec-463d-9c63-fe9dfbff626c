package com.xiao.temu.modules.product.controller;

import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.modules.product.dto.ProductNewArrivalBatchDTO;
import com.xiao.temu.modules.product.dto.ProductNewArrivalStatsDTO;
import com.xiao.temu.modules.product.dto.ProductNewArrivalStatsQueryDTO;
import com.xiao.temu.modules.product.service.ProductNewArrivalService;
import com.xiao.temu.security.annotation.DataScope;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 商品上新控制器
 */
@RestController
@RequestMapping("/product/new-arrival")
public class ProductNewArrivalController {
    
    private static final Logger logger = LoggerFactory.getLogger(ProductNewArrivalController.class);
    
    @Autowired
    private ProductNewArrivalService productNewArrivalService;
    
    /**
     * 批量保存商品上新数据
     */
    @PostMapping("/batch-save")
    public ApiResponse<Integer> batchSave(@RequestBody ProductNewArrivalBatchDTO batchDTO) {
        try {
            logger.info("接收到批量保存商品上新数据请求, mallId: {}, 商品数量: {}", 
                    batchDTO.getMallId(), 
                    batchDTO.getProducts() != null ? batchDTO.getProducts().size() : 0);
            
            int savedCount = productNewArrivalService.batchSave(batchDTO);
            
            logger.info("批量保存商品上新数据完成, 保存记录数: {}", savedCount);
            return ApiResponse.success("批量保存成功", savedCount);
        } catch (Exception e) {
            logger.error("批量保存商品上新数据失败", e);
            return ApiResponse.error("批量保存失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询商品上新统计数据
     * 包括：本月上新数量、本周上新数量、上周上新数量、昨日上新数量、本周上架数量
     * 同时返回店铺销售统计数据：今日销量、近7天销量、近30天销量、商品总数
     * 
     * 数据权限控制：
     * 1. 管理员可以查看全部店铺数据
     * 2. 普通用户只能查看被分配到其所在运营组的店铺数据
     * 3. 运营组组长可以查看组内全部店铺数据
     */
    @DataScope(tableAlias = "s", userIdColumn = "shop_id", groupIdColumn = "group_id")
    @PostMapping("/stats")
    public ApiResponse<List<ProductNewArrivalStatsDTO>> getProductNewArrivalStats(
            @RequestBody ProductNewArrivalStatsQueryDTO queryDTO) {
        try {
            logger.info("接收到查询商品上新统计数据请求, mallId: {}, 指定店铺数: {}", 
                    queryDTO.getMallId(), 
                    queryDTO.getShopIds() != null ? queryDTO.getShopIds().size() : 0);
            
            List<ProductNewArrivalStatsDTO> statsList = productNewArrivalService.getProductNewArrivalStats(queryDTO);
            
            logger.info("查询商品上新统计数据及销售数据完成, 返回店铺数: {}", statsList.size());
            return ApiResponse.success("查询成功", statsList);
        } catch (Exception e) {
            logger.error("查询商品上新统计数据失败", e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }
} 