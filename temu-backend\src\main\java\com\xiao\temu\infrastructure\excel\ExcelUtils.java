package com.xiao.temu.infrastructure.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * Excel导出工具类
 */
public class ExcelUtils {

    /**
     * 导出Excel
     *
     * @param response  响应对象
     * @param data      导出数据
     * @param fileName  文件名
     * @param sheetName 工作表名称
     * @param clazz     导出对象类型
     * @param <T>       泛型
     */
    public static <T> void export(HttpServletResponse response, List<T> data, String fileName,
                                 String sheetName, Class<T> clazz) throws IOException {
        // 设置响应头
        setExcelResponseHeader(response, fileName);
        // 获取默认样式
        HorizontalCellStyleStrategy styleStrategy = getDefaultStyle();
        // 导出数据
        EasyExcel.write(response.getOutputStream(), clazz)
                .registerWriteHandler(styleStrategy)
                .sheet(sheetName)
                .doWrite(data);
    }

    /**
     * 导出多个Sheet的Excel
     *
     * @param response  响应对象
     * @param fileName  文件名
     * @param sheetDataList Sheet数据列表，包含sheetName, data和对应的class
     * @param <T>       泛型
     */
    public static <T> void exportMultiSheet(HttpServletResponse response, String fileName,
                                          List<SheetData<?>> sheetDataList) throws IOException {
        // 设置响应头
        setExcelResponseHeader(response, fileName);
        
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build()) {
            // 获取默认样式
            HorizontalCellStyleStrategy styleStrategy = getDefaultStyle();
            
            for (int i = 0; i < sheetDataList.size(); i++) {
                SheetData<?> sheetData = sheetDataList.get(i);
                WriteSheet writeSheet = EasyExcel.writerSheet(i, sheetData.getSheetName())
                        .head(sheetData.getClazz())
                        .registerWriteHandler(styleStrategy)
                        .build();
                excelWriter.write(sheetData.getData(), writeSheet);
            }
        }
    }

    /**
     * 设置Excel响应头
     *
     * @param response 响应对象
     * @param fileName 文件名
     */
    private static void setExcelResponseHeader(HttpServletResponse response, String fileName) throws IOException {
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
    }

    /**
     * 获取默认样式
     *
     * @return 样式策略
     */
    private static HorizontalCellStyleStrategy getDefaultStyle() {
        // 表头样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置为灰色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 居中
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 边框
        headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        headWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        headWriteCellStyle.setBorderRight(BorderStyle.THIN);
        headWriteCellStyle.setBorderTop(BorderStyle.THIN);

        // 内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 边框
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    /**
     * Sheet数据封装类
     */
    public static class SheetData<T> {
        private String sheetName;
        private List<T> data;
        private Class<T> clazz;

        public SheetData(String sheetName, List<T> data, Class<T> clazz) {
            this.sheetName = sheetName;
            this.data = data;
            this.clazz = clazz;
        }

        public String getSheetName() {
            return sheetName;
        }

        public List<T> getData() {
            return data;
        }

        public Class<T> getClazz() {
            return clazz;
        }
    }
} 