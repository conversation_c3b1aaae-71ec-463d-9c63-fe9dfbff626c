<template>
  <div class="qr-code-test">
    <h3>二维码测试组件</h3>
    <div class="test-container">
      <QrCodeViewer 
        :data-string="testData" 
        purchase-order-sn="TEST123456789"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import QrCodeViewer from './QrCodeViewer.vue';

// 测试数据
const testData = ref('{"shopId":6,"subPurchaseOrderSn":"TEST123456789","timestamp":1746516591596}');
</script>

<style scoped>
.qr-code-test {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 5px;
  margin: 20px;
}

.test-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}
</style> 