package com.xiao.temu.modules.sales.controller;

import com.alibaba.fastjson2.JSONObject;
import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.modules.sales.dto.SalesDataQueryDTO;
import com.xiao.temu.modules.sales.vo.SalesDataVO;
import com.xiao.temu.modules.sales.service.SalesDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售数据控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/sales")
public class SalesDataController {

    private static final Logger logger = LoggerFactory.getLogger(SalesDataController.class);

    @Autowired
    private SalesDataService salesDataService;

    /**
     * 获取销售数据
     *
     * @param queryDTO 查询条件
     * @return 销售数据
     */
    @PostMapping("/data")
    public ApiResponse<List<SalesDataVO>> getSalesData(@RequestBody SalesDataQueryDTO queryDTO) {
        logger.info("获取销售数据请求: {}", queryDTO);
        
        if (queryDTO.getShopIds() == null || queryDTO.getShopIds().isEmpty()) {
            return ApiResponse.error("请选择至少一个店铺");
        }
        
        try {
            List<SalesDataVO> salesDataList = salesDataService.getSalesData(queryDTO);
            return ApiResponse.success(salesDataList);
        } catch (Exception e) {
            logger.error("获取销售数据异常", e);
            return ApiResponse.error("获取销售数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取综合销售数据
     * 将多个店铺的数据合并为一个列表返回
     *
     * @param queryDTO 查询条件
     * @return 合并后的销售数据
     */
    @PostMapping("/combined-data")
    public ApiResponse<JSONObject> getCombinedSalesData(@RequestBody SalesDataQueryDTO queryDTO) {
        logger.info("获取综合销售数据请求: {}", queryDTO);

        if (queryDTO.getShopIds() == null || queryDTO.getShopIds().isEmpty()) {
            return ApiResponse.error("请选择至少一个店铺");
        }

        try {
            // 获取各店铺的销售数据
            List<SalesDataVO> salesDataList = salesDataService.getSalesData(queryDTO);

            // 提取成功的数据和失败的消息
            List<SalesDataVO> successList = salesDataList.stream()
                .filter(SalesDataVO::getSuccess)
                .collect(Collectors.toList());

            List<String> errorMessages = salesDataList.stream()
                .filter(vo -> !vo.getSuccess())
                .map(vo -> vo.getShopName() + ": " + vo.getErrorMsg())
                .collect(Collectors.toList());

            // 合并所有店铺的数据
            Long totalCount = 0L;
            List<JSONObject> allItems = new ArrayList<>();

            for (SalesDataVO vo : successList) {
                if (vo.getTotal() != null) {
                    totalCount += vo.getTotal();
                }

                if (vo.getDataList() != null) {
                    // 为每个数据项添加店铺信息
                    for (JSONObject item : vo.getDataList()) {
                        item.put("shopId", vo.getShopId());
                        item.put("shopName", vo.getShopName());
                        item.put("shopRemark", vo.getShopRemark());
                        allItems.add(item);
                    }
                }
            }

            // 构建最终返回数据
            JSONObject result = new JSONObject();
            result.put("total", totalCount);
            result.put("items", allItems);

            // 如果有错误，添加错误信息
            if (!errorMessages.isEmpty()) {
                result.put("errors", errorMessages);
            }

            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取综合销售数据异常", e);
            return ApiResponse.error("获取综合销售数据失败: " + e.getMessage());
        }
    }
} 