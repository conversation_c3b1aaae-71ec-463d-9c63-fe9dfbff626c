package com.xiao.temu.modules.violation.service;

import com.xiao.temu.common.response.PageResult;
import com.xiao.temu.modules.violation.entity.ShopViolationInfo;
import com.xiao.temu.modules.violation.dto.ShopViolationInfoDTO;
import com.xiao.temu.modules.violation.dto.ShopViolationInfoQueryDTO;
import com.xiao.temu.modules.violation.vo.ShopViolationInfoVO;

import java.util.List;

/**
 * 店铺违规信息Service接口
 */
public interface ShopViolationInfoService {
    
    /**
     * 分页查询店铺违规信息列表
     *
     * @param queryDTO 查询条件
     * @return 店铺违规信息分页数据
     */
    PageResult<ShopViolationInfoDTO> listViolationInfos(ShopViolationInfoQueryDTO queryDTO);
    
    /**
     * 分页查询店铺违规信息列表(返回VO对象)
     *
     * @param queryDTO 查询条件
     * @return 店铺违规信息VO对象
     */
    ShopViolationInfoVO getViolationInfoList(ShopViolationInfoQueryDTO queryDTO);
    
    /**
     * 分页查询店铺违规信息列表(包含违规详情，返回VO对象)
     *
     * @param queryDTO 查询条件
     * @return 店铺违规信息VO对象
     */
    ShopViolationInfoVO getViolationInfoListWithDetail(ShopViolationInfoQueryDTO queryDTO);
    
    /**
     * 根据ID查询违规信息详情
     *
     * @param id 违规信息ID
     * @return 违规信息详情
     */
    ShopViolationInfoDTO getViolationInfoById(Long id);
    
    /**
     * 新增违规信息
     *
     * @param violationInfo 违规信息
     * @return 是否成功
     */
    boolean addViolationInfo(ShopViolationInfo violationInfo);
    
    /**
     * 批量添加违规信息
     *
     * @param violationInfoList 违规信息列表
     * @return 是否成功
     */
    boolean batchAddViolationInfo(List<ShopViolationInfo> violationInfoList);
    
    /**
     * 修改违规信息
     *
     * @param violationInfo 违规信息
     * @return 是否成功
     */
    boolean updateViolationInfo(ShopViolationInfo violationInfo);
    
    /**
     * 删除违规信息
     *
     * @param id 违规信息ID
     * @return 是否成功
     */
    boolean deleteViolationInfo(Long id);
    
    /**
     * 批量删除违规信息
     *
     * @param ids 违规信息ID数组
     * @return 是否成功
     */
    boolean batchDeleteViolationInfo(Long[] ids);
    
    /**
     * 检查违规信息是否已存在
     *
     * @param shopId 店铺ID
     * @param punishSn 违规编号
     * @return 是否存在
     */
    boolean checkViolationInfoExists(Long shopId, String punishSn);
    
    /**
     * 根据MallId检查违规信息是否已存在
     *
     * @param mallId 店铺MallId
     * @param punishSn 违规编号
     * @return 是否存在
     */
    boolean checkViolationInfoExistsByMallId(String mallId, String punishSn);
    
    /**
     * 根据店铺ID和违规编号获取违规信息
     *
     * @param shopId 店铺ID
     * @param punishSn 违规编号
     * @return 违规信息
     */
    ShopViolationInfo getViolationInfoByShopIdAndPunishSn(Long shopId, String punishSn);
} 