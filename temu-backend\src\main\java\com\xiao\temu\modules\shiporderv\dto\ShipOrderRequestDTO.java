package com.xiao.temu.modules.shiporderv.dto;

import lombok.Data;
import java.util.List;

/**
 * Temu发货单请求参数DTO
 */
@Data
public class ShipOrderRequestDTO {
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺ID列表
     */
    private List<Long> shopIds;
    
    /**
     * 页码，从1开始
     */
    private Integer pageNo;
    
    /**
     * 每页大小，默认10，最大1500
     */
    private Integer pageSize;
    
    /**
     * 子采购单号列表
     */
    private List<String> subPurchaseOrderSnList;
    
    /**
     * 发货单号列表 (原字段，保留兼容性)
     */
    private List<String> shipOrderSnList;
    
    /**
     * 发货单号列表
     */
    private List<Long> deliveryOrderSnList;
    
    /**
     * 物流单号列表
     */
    private List<String> expressDeliverySnList;
    
    /**
     * 运单重量异常状态列表
     * 0-未定义（数据库默认值）或无异常
     * 1-异常待确认
     * 2-已提交异常反馈，待物流商处理
     * 3-物流商处理完成
     * 4-平台介入处理中
     * 5-平台处理完成
     * 6-卖家已确认
     * 7-卖家超期自动确认
     * 8-物流商介入处理，卖家确认或超时自动确认
     * 9-结算消息驱动卖家确认
     * 10-无需公示
     * 11-结算物流单计算重量查询失败
     * 12-结算理论计费重拦截
     * 13-SKU重量体积拦截
     */
    private List<Integer> expressWeightFeedbackStatus;
    
    /**
     * SKC ID列表
     */
    private List<Long> productSkcIdList;
    
    /**
     * 发货开始时间(ms)
     */
    private Long deliverTimeFrom;
    
    /**
     * 发货结束时间(ms)
     */
    private Long deliverTimeTo;
    
    /**
     * 是否加急：0-普通 1-急采
     */
    private Integer urgencyType;
    
    /**
     * 收货仓库ID列表
     */
    private List<Long> subWarehouseIdList;
    
    /**
     * 收货仓库地址
     */
    private String targetReceiveAddress;
    
    /**
     * 发货仓库地址
     */
    private String targetDeliveryAddress;
    
    /**
     * 是否定制品
     */
    private Boolean isCustomProduct;
    
    /**
     * 物流反馈异常状态列表
     * 0-当前无异常
     * 1-已提交
     * 2-物流商处理中
     * 3-已撤销
     * 4-已反馈
     */
    private List<Integer> latestFeedbackStatusList;
    
    /**
     * 备货区域列表
     * 1-国内备货
     * 2-海外备货
     * 3-保税仓备货
     */
    private List<Integer> inventoryRegion;
    
    /**
     * 货号列表
     */
    private List<String> skcExtCodeList;
    
    /**
     * 商品条码样式，0-全选，1-旧样式，2-新样式
     */
    private Integer productLabelCodeStyle = 0;
    
    /**
     * 是否忽略权限检查
     */
    private Boolean ignorePermissionCheck;
    
    /**
     * 是否是JIT发货单，true表示JIT发货单，false表示普通发货单
     */
    private Boolean isJit;
    
    /**
     * 发货单状态
     * 0-待装装箱发货
     * 1-待仓库发货
     * 2-已收货
     * 5-已取消
     * 6-部分收货
     * 不传表示全部
     */
    private Integer status;
    
    /**
     * 仅查询待税仓申领的订单(状态为待仓库收货时使用)
     */
    private Boolean onlyTaxWarehouseWaitApply;
} 