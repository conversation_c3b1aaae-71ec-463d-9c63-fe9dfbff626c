package com.xiao.temu.modules.sales.vo;

import com.alibaba.fastjson2.JSONObject;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 销售数据视图对象
 */
@Data
public class SalesDataVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺名称
     */
    private String shopName;
    
    /**
     * 店铺备注
     */
    private String shopRemark;
    
    /**
     * API调用结果
     */
    private JSONObject apiResult;
    
    /**
     * 错误信息
     */
    private String errorMsg;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 总数据
     */
    private Long total;
    
    /**
     * 数据列表
     */
    private List<JSONObject> dataList;
} 