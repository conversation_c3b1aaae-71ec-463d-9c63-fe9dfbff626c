package com.xiao.temu.config.ratelimit;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;

/**
 * 销售数据同步API请求速率限制配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "thread.sales-sync-pool")
public class SalesSyncRateLimitConfig {
    
    /**
     * 核心线程数
     */
    private int coreSize = 5;
    
    /**
     * 最大线程数
     */
    private int maxSize = 10;
    
    /**
     * 队列容量
     */
    private int queueCapacity = 200;
    
    /**
     * 线程空闲时间(秒)
     */
    private int keepAliveSeconds = 60;
    
    /**
     * 请求间隔(毫秒)
     */
    private int requestIntervalMs = 500;
    
    /**
     * 最大重试次数
     */
    private int maxRetryTimes = 3;
    
    /**
     * 重试间隔(毫秒)
     */
    private int retryIntervalMs = 1000;
    
    /**
     * 是否启用动态退避策略
     */
    private boolean dynamicBackoff = true;
} 