package com.xiao.temu.modules.sales.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 销售仓库信息实体类
 * 对应数据库表：sales_warehouse_info
 * 存储每个SKU在不同仓库的销量和备货信息
 */
@Data
@TableName("sales_warehouse_info")
public class SalesWarehouseInfo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 商品SKU ID
     */
    private Long productSkuId;

    /**
     * 仓库组ID
     */
    private Long warehouseGroupId;

    /**
     * 备货仓组名称
     */
    private String warehouseGroupName;

    /**
     * 备货天数
     */
    private Integer stockDays;

    /**
     * 安全库存天数
     */
    private Integer safeInventoryDays;

    /**
     * 下单逻辑：安全库存天数+备货天数
     */
    private String purchaseConfig;

    /**
     * 今日销量
     */
    private Integer todaySaleVolume;

    /**
     * 总销量
     */
    private Integer totalSaleVolume;

    /**
     * 近7天销量
     */
    private Integer lastSevenDaysSaleVolume;

    /**
     * 近30天销量
     */
    private Integer lastThirtyDaysSaleVolume;

    /**
     * 建议下单量
     */
    private Integer adviceQuantity;

    /**
     * 缺货数量
     */
    private Integer lackQuantity;

    /**
     * 可售天数
     */
    private Integer availableSaleDays;

    /**
     * 库存可售天数
     */
    private Integer availableSaleDaysFromInventory;

    /**
     * 仓内库存可售天数
     */
    private Integer warehouseAvailableSaleDays;

    /**
     * 7日销量参考
     */
    private Integer sevenDaysSaleReference;

    /**
     * 七日销量参考类型：1-7日最大销量，2-7日日均销量
     */
    private Integer sevenDaysReferenceSaleType;

    /**
     * 同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 