package com.xiao.temu.modules.quality.controller;

import com.xiao.temu.modules.quality.entity.QualityInspectionDefectDetail;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.sync.dto.QualityInspectionSyncDTO;
import com.xiao.temu.modules.sync.vo.ApiResponse;
import com.xiao.temu.modules.quality.service.QualityInspectionDefectDetailService;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 质检不合格详情数据控制器
 */
@RestController
@RequestMapping("/temu/qualityInspection/defect")
public class QualityInspectionDefectDetailController {

    @Autowired
    private QualityInspectionDefectDetailService defectDetailService;

    @Autowired
    private ShopService shopService;

    @Autowired
    private UserService userService;

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        // 通过用户名查询用户信息获取用户ID
        SysUser user = userService.getUserByUsername(username);
        if (user != null) {
            return user.getUserId();
        }
        throw new RuntimeException("用户未找到: " + username);
    }

    /**
     * 手动触发不合格详情数据同步
     *
     * @param shopId 店铺ID
     * @return API响应
     */
    @PostMapping("/sync/{shopId}")
    public ApiResponse syncDefectDetail(@PathVariable Long shopId) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        // 如果是管理员，直接允许操作；否则检查权限
        if (!isAdmin(userId) && !shopService.checkShopPermission(userId, shopId, false)) {
            return ApiResponse.error(403, "您没有访问该店铺的权限");
        }
        
        // 触发同步
        String result = defectDetailService.syncDefectDetailData(shopId);
        
        return ApiResponse.success(result);
    }

    /**
     * 批量触发不合格详情数据同步
     *
     * @param syncDTO 同步参数
     * @return API响应
     */
    @PostMapping("/batch/sync")
    public ApiResponse batchSyncDefectDetail(@RequestBody QualityInspectionSyncDTO syncDTO) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        if (syncDTO == null || syncDTO.getShopIds() == null || syncDTO.getShopIds().isEmpty()) {
            return ApiResponse.error(400, "请选择要同步的店铺");
        }
        
        // 过滤出用户有权限的店铺
        List<Long> shopIds;
        if (isAdmin(userId)) {
            // 管理员可以访问所有店铺
            shopIds = syncDTO.getShopIds();
        } else {
            // 非管理员需要过滤权限
            shopIds = syncDTO.getShopIds().stream()
                    .filter(shopId -> shopService.checkShopPermission(userId, shopId, false))
                    .toList();
        }
        
        if (shopIds.isEmpty()) {
            return ApiResponse.error(403, "您没有访问所选店铺的权限");
        }
        
        // 获取自定义参数
        Integer batchSize = syncDTO.getBatchSize() != null ? syncDTO.getBatchSize() : 10;
        Integer maxThreads = syncDTO.getMaxThreads() != null ? syncDTO.getMaxThreads() : 3;
        Long apiInterval = syncDTO.getApiInterval() != null ? syncDTO.getApiInterval() : 1000L;
        
        // 批量同步结果
        Map<String, Object> response = new HashMap<>();
        List<Map<String, Object>> results = new java.util.ArrayList<>();
        int successCount = 0;
        
        for (Long shopId : shopIds) {
            Map<String, Object> result = new HashMap<>();
            result.put("shopId", shopId);
            
            try {
                String syncResult = defectDetailService.batchSyncDefectDetailData(shopId, batchSize, maxThreads, apiInterval);
                result.put("success", true);
                result.put("message", syncResult);
                successCount++;
            } catch (Exception e) {
                result.put("success", false);
                result.put("message", "同步异常: " + e.getMessage());
            }
            
            results.add(result);
        }
        
        response.put("total", shopIds.size());
        response.put("success", successCount);
        response.put("results", results);
        
        return ApiResponse.success(response);
    }

    /**
     * 获取质检不合格详情数据
     *
     * @param shopId 店铺ID
     * @param qcBillId 质检单ID
     * @return API响应
     */
    @GetMapping("/{shopId}/{qcBillId}")
    public ApiResponse getDefectDetails(@PathVariable Long shopId, @PathVariable Long qcBillId) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        // 如果是管理员，直接允许操作；否则检查权限
        if (!isAdmin(userId) && !shopService.checkShopPermission(userId, shopId, false)) {
            return ApiResponse.error(403, "您没有访问该店铺的权限");
        }
        
        // 获取不合格详情数据
        List<QualityInspectionDefectDetail> details = defectDetailService.getDefectDetailsByShopIdAndQcBillId(shopId, qcBillId);
        
        return ApiResponse.success(details);
    }

    /**
     * 检查用户是否为管理员
     */
    private boolean isAdmin(Long userId) {
        return userService.isAdmin(userId);
    }
} 