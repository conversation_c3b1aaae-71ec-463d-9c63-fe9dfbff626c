<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作列表 - TEMU备货单追踪</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f7f7f7;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }
        .wx-container {
            max-width: 414px;
            min-height: 100vh;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 0;
            box-shadow: 0 0 10px rgba(0,0,0,0.05);
            overflow: hidden;
            position: relative;
        }
        @media (min-width: 500px) {
            .wx-container {
                min-height: 90vh;
                margin: 20px auto;
                border-radius: 30px;
            }
        }
        .wx-btn-primary {
            background-color: #07c160;
            color: white;
        }
        .wx-btn-primary:hover {
            background-color: #06ad56;
        }
        .tab-active {
            color: #07c160;
            border-bottom: 2px solid #07c160;
        }
    </style>
</head>
<body>
    <div class="wx-container">
        <div class="flex flex-col h-screen">
            <header class="py-4 px-4 flex justify-between items-center border-b border-gray-200">
                <div class="flex items-center">
                    <a href="home.html" class="mr-2">
                        <i class="fas fa-arrow-left text-gray-600"></i>
                    </a>
                    <h1 class="text-xl font-medium">工作列表</h1>
                </div>
                <div class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full">烧花工</div>
            </header>
            
            <div class="tabs border-b border-gray-200">
                <div class="flex">
                    <button id="tab-pending" class="flex-1 py-3 text-center font-medium tab-active">
                        待处理
                    </button>
                    <button id="tab-completed" class="flex-1 py-3 text-center font-medium text-gray-500">
                        已处理
                    </button>
                </div>
            </div>
            
            <div class="flex-grow p-4 overflow-auto">
                <!-- 待处理列表 -->
                <div id="pending-list" class="space-y-3">
                    <a href="progress.html" class="block bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="font-medium mb-1">备货单 #TM20230616001</h3>
                                <p class="text-sm text-gray-600">女式连衣裙 - 200件</p>
                                <p class="text-xs text-gray-500 mt-2">当前工序：烧花工序</p>
                            </div>
                            <div class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                                待处理
                            </div>
                        </div>
                    </a>
                    
                    <a href="progress.html" class="block bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="font-medium mb-1">备货单 #TM20230616002</h3>
                                <p class="text-sm text-gray-600">儿童T恤 - 350件</p>
                                <p class="text-xs text-gray-500 mt-2">当前工序：烧花工序</p>
                            </div>
                            <div class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                                待处理
                            </div>
                        </div>
                    </a>
                    
                    <a href="progress.html" class="block bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="font-medium mb-1">备货单 #TM20230616003</h3>
                                <p class="text-sm text-gray-600">男士休闲裤 - 180件</p>
                                <p class="text-xs text-gray-500 mt-2">当前工序：烧花工序</p>
                            </div>
                            <div class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                                待处理
                            </div>
                        </div>
                    </a>
                    
                    <a href="progress.html" class="block bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="font-medium mb-1">备货单 #TM20230616004</h3>
                                <p class="text-sm text-gray-600">女士上衣 - 120件</p>
                                <p class="text-xs text-gray-500 mt-2">当前工序：烧花工序</p>
                            </div>
                            <div class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                                待处理
                            </div>
                        </div>
                    </a>
                    
                    <a href="progress.html" class="block bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="font-medium mb-1">备货单 #TM20230616005</h3>
                                <p class="text-sm text-gray-600">儿童连衣裙 - 150件</p>
                                <p class="text-xs text-gray-500 mt-2">当前工序：烧花工序</p>
                            </div>
                            <div class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                                待处理
                            </div>
                        </div>
                    </a>
                </div>
                
                <!-- 已处理列表 -->
                <div id="completed-list" class="space-y-3 hidden">
                    <div class="block bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="font-medium mb-1">备货单 #TM20230615001</h3>
                                <p class="text-sm text-gray-600">男士T恤 - 250件</p>
                                <p class="text-xs text-gray-500 mt-2">完成时间：2023/06/15 15:30</p>
                            </div>
                            <div class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                已完成
                            </div>
                        </div>
                    </div>
                    
                    <div class="block bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="font-medium mb-1">备货单 #TM20230615002</h3>
                                <p class="text-sm text-gray-600">女式牛仔裤 - 180件</p>
                                <p class="text-xs text-gray-500 mt-2">完成时间：2023/06/15 14:20</p>
                            </div>
                            <div class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                已完成
                            </div>
                        </div>
                    </div>
                    
                    <div class="block bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="font-medium mb-1">备货单 #TM20230615003</h3>
                                <p class="text-sm text-gray-600">儿童套装 - 150件</p>
                                <p class="text-xs text-gray-500 mt-2">完成时间：2023/06/15 11:45</p>
                            </div>
                            <div class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                已完成
                            </div>
                        </div>
                    </div>
                    
                    <div class="block bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="font-medium mb-1">备货单 #TM20230614001</h3>
                                <p class="text-sm text-gray-600">女式卫衣 - 120件</p>
                                <p class="text-xs text-gray-500 mt-2">完成时间：2023/06/14 16:10</p>
                            </div>
                            <div class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                已完成
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <footer class="bg-white border-t border-gray-200">
                <div class="grid grid-cols-3 text-center">
                    <a href="home.html" class="py-3 flex flex-col items-center text-gray-500">
                        <i class="fas fa-home text-xl"></i>
                        <span class="text-xs mt-1">首页</span>
                    </a>
                    <a href="scan.html" class="py-3 flex flex-col items-center text-gray-500">
                        <i class="fas fa-qrcode text-xl"></i>
                        <span class="text-xs mt-1">扫码</span>
                    </a>
                    <a href="worklist.html" class="py-3 flex flex-col items-center text-green-600">
                        <i class="fas fa-list-ul text-xl"></i>
                        <span class="text-xs mt-1">列表</span>
                    </a>
                </div>
            </footer>
        </div>
    </div>
    
    <script>
        // 标签页切换
        document.getElementById('tab-pending').addEventListener('click', function() {
            document.getElementById('tab-pending').classList.add('tab-active');
            document.getElementById('tab-pending').classList.remove('text-gray-500');
            document.getElementById('tab-completed').classList.remove('tab-active');
            document.getElementById('tab-completed').classList.add('text-gray-500');
            document.getElementById('pending-list').classList.remove('hidden');
            document.getElementById('completed-list').classList.add('hidden');
        });
        
        document.getElementById('tab-completed').addEventListener('click', function() {
            document.getElementById('tab-completed').classList.add('tab-active');
            document.getElementById('tab-completed').classList.remove('text-gray-500');
            document.getElementById('tab-pending').classList.remove('tab-active');
            document.getElementById('tab-pending').classList.add('text-gray-500');
            document.getElementById('completed-list').classList.remove('hidden');
            document.getElementById('pending-list').classList.add('hidden');
        });
    </script>
</body>
</html> 