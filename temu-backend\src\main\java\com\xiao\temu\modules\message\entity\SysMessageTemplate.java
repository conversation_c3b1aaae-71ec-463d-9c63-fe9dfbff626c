package com.xiao.temu.modules.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 消息模板实体类
 */
@Data
@TableName("sys_message_template")
public class SysMessageTemplate {
    
    /**
     * 模板ID
     */
    @TableId(type = IdType.AUTO)
    private Long templateId;
    
    /**
     * 模板编码
     */
    private String templateCode;
    
    /**
     * 模板名称
     */
    private String templateName;
    
    /**
     * 标题模板
     */
    private String titleTemplate;
    
    /**
     * 内容模板
     */
    private String contentTemplate;
    
    /**
     * 模板类型(1系统消息 2任务提醒 3店铺消息)
     */
    private String templateType;
    
    /**
     * 状态(0正常 1禁用)
     */
    private String status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 备注
     */
    private String remark;
} 