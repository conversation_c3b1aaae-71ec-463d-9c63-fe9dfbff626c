import request from '@/utils/request'

// 获取销售数据（分店铺返回）
export function getSalesData(query) {
  return request({
    url: '/api/sales/data',
    method: 'post',
    data: query
  })
}

// 获取综合销售数据（合并所有店铺数据）
export function getCombinedSalesData(query) {
  return request({
    url: '/api/sales/combined-data',
    method: 'post',
    data: query
  })
}

// 导出销售数据
export function exportSalesData(query) {
  return request({
    url: '/api/sales/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
} 