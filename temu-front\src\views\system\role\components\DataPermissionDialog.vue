<template>
  <el-dialog
    title="设置数据权限"
    v-model="dialogVisible"
    width="500px"
    append-to-body
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="角色名称" prop="roleName">
        <el-input v-model="form.roleName" disabled />
      </el-form-item>
      <el-form-item label="权限类型" prop="permissionType">
        <el-radio-group v-model="form.permissionType">
          <el-radio label="0">本人数据权限</el-radio>
          <el-radio label="1">本组数据权限</el-radio>
          <el-radio label="2">全部数据权限</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="请输入备注信息"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <div class="dialog-description">
      <h4>权限说明：</h4>
      <p><strong>本人数据权限：</strong>只能查看自己直接关联的数据</p>
      <p><strong>本组数据权限：</strong>可以查看所在运营组的数据</p>
      <p><strong>全部数据权限：</strong>可以查看所有运营组的数据</p>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getDataPermission, setDataPermission } from '@/api/dataPermission'
import type { DataPermission } from '@/types/dataPermission'

const dialogVisible = ref(false)
const emit = defineEmits(['refreshList'])
const formRef = ref<any>(null)

// 表单数据
const form = reactive<DataPermission>({
  id: undefined,
  roleId: 0,
  roleName: '',
  roleKey: '',
  permissionType: '1', // 默认本组数据权限
  remark: ''
})

// 表单验证规则
const rules = {
  permissionType: [
    { required: true, message: '请选择权限类型', trigger: 'change' }
  ]
}

// 打开对话框
const openDialog = async (role: any) => {
  resetForm()
  form.roleId = role.roleId
  form.roleName = role.roleName
  form.roleKey = role.roleKey
  
  try {
    const res = await getDataPermission(role.roleId)
    if (res.data) {
      form.id = res.data.id
      form.permissionType = res.data.permissionType
      form.remark = res.data.remark
    }
  } catch (error) {
    console.error('获取角色数据权限失败', error)
  }
  
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.id = undefined
  form.roleId = 0
  form.roleName = ''
  form.roleKey = ''
  form.permissionType = '1'
  form.remark = ''
}

// 取消
const cancel = () => {
  dialogVisible.value = false
  resetForm()
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const data: DataPermission = {
          id: form.id,
          roleId: form.roleId,
          permissionType: form.permissionType,
          remark: form.remark
        }
        
        await setDataPermission(data)
        ElMessage.success('设置数据权限成功')
        dialogVisible.value = false
        emit('refreshList')
      } catch (error) {
        console.error('设置数据权限失败', error)
        ElMessage.error('设置数据权限失败')
      }
    }
  })
}

// 暴露方法给父组件调用
defineExpose({
  openDialog
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.dialog-description {
  margin-top: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.dialog-description h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #303133;
}

.dialog-description p {
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}
</style> 