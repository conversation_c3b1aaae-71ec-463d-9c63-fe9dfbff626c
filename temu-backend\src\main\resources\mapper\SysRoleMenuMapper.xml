<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.system.mapper.SysRoleMenuMapper">

    <!-- 通过角色ID删除角色和菜单关联 -->
    <delete id="deleteRoleMenuByRoleId" parameterType="Long">
        delete from sys_role_menu where role_id = #{roleId}
    </delete>

    <!-- 批量删除角色菜单关联 -->
    <delete id="deleteRoleMenu" parameterType="Long">
        delete from sys_role_menu where role_id in
        <foreach collection="array" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

    <!-- 查询菜单使用数量 -->
    <select id="countRoleMenuByMenuId" parameterType="Long" resultType="int">
        select count(1) from sys_role_menu where menu_id = #{menuId}
    </select>

    <!-- 批量新增角色菜单信息 -->
    <insert id="batchInsertRoleMenu">
        insert into sys_role_menu(role_id, menu_id) values
        <foreach collection="list" item="item" separator=",">
            (#{item.roleId}, #{item.menuId})
        </foreach>
    </insert>

    <!-- 查询角色菜单ID集合 -->
    <select id="selectMenuIdsByRoleId" parameterType="Long" resultType="Long">
        select menu_id
        from sys_role_menu
        where role_id = #{roleId}
    </select>

</mapper> 