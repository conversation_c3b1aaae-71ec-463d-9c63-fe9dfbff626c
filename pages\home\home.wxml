<view class="container">
  <page-header title="TEMU备货单追踪"></page-header>
  
  <view class="content">
    
    <!-- 角色列表区域 -->
    <view class="role-section">
      <text class="section-title">我的角色</text>
      
      <!-- 生产组长角色单独一行 -->
      <view class="leader-row" wx:if="{{userInfo.roleNames && userInfo.roleNames.productionLeader}}">
        <view class="role-item leader-item">
          <view class="role-dot leader-dot"></view>
          <text class="role-name">{{userInfo.roleNames.productionLeader}}</text>
        </view>
      </view>
      
      <!-- 其他角色按照指定顺序排列 -->
      <view class="role-list">
        <!-- 使用有序角色数组 -->
        <block wx:if="{{orderedRoles.length > 0}}">
          <view wx:for="{{orderedRoles}}" wx:key="roleKey" class="role-item">
            <view class="role-dot"></view>
            <text class="role-name">{{item.roleName}}</text>
          </view>
        </block>
        
        <!-- 没有角色名称时显示默认 -->
        <view wx:if="{{orderedRoles.length === 0 && userInfo.roleName}}" class="role-item">
          <view class="role-dot"></view>
          <text class="role-name">{{userInfo.roleName}}</text>
        </view>
        <view wx:if="{{orderedRoles.length === 0 && !userInfo.roleName}}" class="role-item">
          <view class="role-dot"></view>
          <text class="role-name">员工</text>
        </view>
      </view>
    </view>
    
    <view class="recent-section">
      <text class="section-title">操作日志</text>
      <view class="activity-list">
        <block wx:if="{{userOperationLogs.length > 0}}">
          <view class="activity-item" wx:for="{{userOperationLogs}}" wx:key="index">
            <view class="activity-status">
              <view class="activity-dot"></view>
              <text class="activity-status-text">{{item.operationType}}:</text>
            </view>
            <text class="activity-text">备货单 #{{item.orderNumber}}</text>
            <text class="activity-time">{{item.completedTime}}</text>
          </view>
        </block>
        <view class="empty-tip" wx:else>
          <text>暂无完成记录</text>
        </view>
      </view>
    </view>
  </view>
</view> 