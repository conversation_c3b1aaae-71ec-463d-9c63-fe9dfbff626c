package com.xiao.temu.modules.shop.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiao.temu.common.response.PageResult;
import com.xiao.temu.modules.operation.dto.GroupLeaderShopAssignmentDTO;
import com.xiao.temu.modules.shop.dto.ShopDTO;
import com.xiao.temu.modules.shop.dto.ShopImportDTO;
import com.xiao.temu.modules.shop.dto.ShopQueryDTO;
import com.xiao.temu.modules.operation.entity.OperationGroup;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.infrastructure.listener.ShopImportListener;
import com.xiao.temu.modules.operation.mapper.OperationGroupMapper;
import com.xiao.temu.modules.shop.mapper.ShopMapper;
import com.xiao.temu.modules.operation.service.GroupLeaderShopAssignmentService;
import com.xiao.temu.modules.operation.service.OperationGroupService;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.shop.entity.ShopGroupAssignment;
import com.xiao.temu.modules.shop.mapper.ShopGroupAssignmentMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 店铺服务实现类
 */
@Slf4j
@Service
public class ShopServiceImpl extends ServiceImpl<ShopMapper, Shop> implements ShopService {

    @Autowired
    private ShopMapper shopMapper;
    
    @Autowired
    private OperationGroupMapper groupMapper;
    
    @Autowired
    private GroupLeaderShopAssignmentService assignmentService;
    
    @Autowired
    private OperationGroupService groupService;
    
    @Autowired
    private ShopGroupAssignmentMapper shopGroupAssignmentMapper;

    @Override
    public PageResult<ShopDTO> listShops(ShopQueryDTO queryDTO) {
        Page<ShopDTO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        IPage<ShopDTO> iPage = shopMapper.selectShopList(page, queryDTO);
        
        return new PageResult<>(iPage.getTotal(), queryDTO.getPageNum(), queryDTO.getPageSize(), iPage.getRecords());
    }

    @Override
    public ShopDTO getShopById(Long shopId) {
        // 获取店铺详情，不屏蔽敏感信息
        ShopDTO shopDTO = shopMapper.selectShopById(shopId);
        return shopDTO;
    }

    @Override
    public Shop getShopByMallId(String mallId) {
        LambdaQueryWrapper<Shop> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Shop::getShopTemuId, mallId)
               .eq(Shop::getStatus, "0");
        return this.getOne(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addShop(Shop shop) {
        // 默认使用系统管理员ID=1并且不设置所属运营组
        return addShop(shop, 1L, null);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addShop(Shop shop, Long userId, Long groupId) {
        // 设置创建时间
        shop.setCreateTime(LocalDateTime.now());
        // 默认状态为正常
        if (shop.getStatus() == null) {
            shop.setStatus("0");
        }
        
        // 设置创建者
        shop.setCreateBy(userId);
        
        // 设置所属运营组
        shop.setBelongGroupId(groupId);
        
        // 保存店铺
        boolean result = this.save(shop);
        
        // 如果指定了所属运营组，自动创建店铺和运营组的关联
        if (result && groupId != null) {
            ShopGroupAssignment assignment = new ShopGroupAssignment();
            assignment.setShopId(shop.getShopId());
            assignment.setGroupId(groupId);
            assignment.setAssignTime(LocalDateTime.now());
            assignment.setAssignBy(userId);
            assignment.setStatus("0"); // 正常状态
            
            try {
                shopGroupAssignmentMapper.insert(assignment);
            } catch (Exception e) {
                log.error("添加店铺时创建店铺与运营组的关联关系失败: shopId={}, groupId={}", shop.getShopId(), groupId, e);
                // 不因为关联创建失败而回滚整个事务，继续返回店铺创建结果
            }
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateShop(Shop shop) {
        // 设置更新时间
        shop.setUpdateTime(LocalDateTime.now());
        return this.updateById(shop);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteShop(Long shopId) {
        // 检查店铺是否被引用，如有任务等关联数据，则不允许删除
        // TODO: 增加相关检查逻辑
        
        return this.removeById(shopId);
    }

    @Override
    public List<Shop> listUserShops(Long userId) {
        // 获取用户被分配的店铺
        List<GroupLeaderShopAssignmentDTO> assignments = assignmentService.getUserAssignments(userId);
        if (assignments == null || assignments.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 提取店铺ID
        List<Long> shopIds = new ArrayList<>();
        for (GroupLeaderShopAssignmentDTO dto : assignments) {
            shopIds.add(dto.getShopId());
        }
        
        // 查询店铺详情
        LambdaQueryWrapper<Shop> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Shop::getShopId, shopIds)
               .eq(Shop::getStatus, "0");
        
        List<Shop> shops = this.list(wrapper);
        
        // 屏蔽敏感信息
        for (Shop shop : shops) {
            shop.setAccessToken(null);
            shop.setApiKey(null);
            shop.setApiSecret(null);
        }
        
        return shops;
    }

    @Override
    public List<Shop> listLeaderShops(Long leaderId) {
        // 查询用户担任组长的运营组
        LambdaQueryWrapper<OperationGroup> groupWrapper = new LambdaQueryWrapper<>();
        groupWrapper.eq(OperationGroup::getLeaderId, leaderId)
                   .eq(OperationGroup::getStatus, "0");
        List<OperationGroup> groups = groupMapper.selectList(groupWrapper);
        
        if (groups == null || groups.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 提取运营组ID
        List<Long> groupIds = new ArrayList<>();
        for (OperationGroup group : groups) {
            groupIds.add(group.getGroupId());
        }
        
        // 使用shopMapper查询这些运营组关联的所有店铺
        List<Shop> shops = new ArrayList<>();
        for (Long groupId : groupIds) {
            List<Shop> groupShops = shopMapper.selectShopsByGroupId(groupId);
            shops.addAll(groupShops);
        }
        
        // 去重
        Set<Long> shopIdSet = new HashSet<>();
        List<Shop> uniqueShops = new ArrayList<>();
        
        for (Shop shop : shops) {
            if (!shopIdSet.contains(shop.getShopId())) {
                shopIdSet.add(shop.getShopId());
                uniqueShops.add(shop);
            }
        }
        
        // 屏蔽敏感信息
        for (Shop shop : uniqueShops) {
            shop.setAccessToken(null);
            shop.setApiKey(null);
            shop.setApiSecret(null);
        }
        
        return uniqueShops;
    }

    @Override
    public List<Shop> listGroupShops(Long groupId) {
        // 使用Mapper查询通过shop_group_assignment关联查询店铺
        List<Shop> shops = shopMapper.selectShopsByGroupId(groupId);
        
        // 屏蔽敏感信息
        for (Shop shop : shops) {
            shop.setAccessToken(null);
            shop.setApiKey(null);
            shop.setApiSecret(null);
        }
        
        return shops;
    }

    @Override
    public List<Shop> listAllGroupShops(Long groupId) {
        // 使用Mapper查询所有与该组关联的店铺，包括那些被移除了belongGroupId的店铺
        List<Shop> shops = shopMapper.selectAllShopsByGroupId(groupId);
        
        // 屏蔽敏感信息
        for (Shop shop : shops) {
            shop.setAccessToken(null);
            shop.setApiKey(null);
            shop.setApiSecret(null);
        }
        
        return shops;
    }

    @Override
    public boolean checkShopPermission(Long userId, Long shopId, boolean requireWrite) {
        // 先获取该店铺所属的所有运营组
        List<Long> groupIds = shopMapper.selectGroupIdsByShopId(shopId);
        if (groupIds == null || groupIds.isEmpty()) {
            return false;
        }
        
        // 检查用户是否为这些运营组中任一组的组长
        for (Long groupId : groupIds) {
            boolean isLeader = assignmentService.isGroupLeader(userId, groupId);
            if (isLeader) {
                // 组长有所有权限
                return true;
            }
        }
        
        // 检查用户是否被分配了店铺权限
        return assignmentService.checkShopPermission(userId, shopId, requireWrite);
    }

    @Override
    public String getUserShopPermissionType(Long userId, Long shopId) {
        // 先获取该店铺所属的所有运营组
        List<Long> groupIds = shopMapper.selectGroupIdsByShopId(shopId);
        if (groupIds == null || groupIds.isEmpty()) {
            return null;
        }
        
        // 检查用户是否为这些运营组中任一组的组长
        for (Long groupId : groupIds) {
            boolean isLeader = assignmentService.isGroupLeader(userId, groupId);
            if (isLeader) {
                // 组长有最高权限
                return "1";
            }
        }
        
        // 查询用户的分配权限
        GroupLeaderShopAssignmentDTO assignment = assignmentService.getUserShopAssignment(userId, shopId);
        if (assignment != null) {
            return assignment.getPermissionType();
        }
        
        return null;
    }
    
    @Override
    public boolean checkShopInGroup(Long shopId, Long groupId) {
        if (shopId == null || groupId == null) {
            return false;
        }
        
        // 查询shop_group_assignment表
        return shopMapper.checkShopInGroup(shopId, groupId);
    }

    @Override
    public boolean checkShopNameUnique(String shopName) {
        LambdaQueryWrapper<Shop> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Shop::getShopName, shopName);
        return this.count(wrapper) == 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchImportShops(List<Shop> shopList, Map<String, List<Long>> shopGroupMap) {
        if (shopList == null || shopList.isEmpty()) {
            return 0;
        }
        
        int successCount = 0;
        
        try {
            // 批量插入店铺
            this.saveBatch(shopList);
            
            // 处理店铺与运营组的关联关系
            for (Shop shop : shopList) {
                List<Long> groupIds = shopGroupMap.get(shop.getShopName());
                if (groupIds != null && !groupIds.isEmpty()) {
                    // 为每个店铺分配对应的运营组
                    for (Long groupId : groupIds) {
                        try {
                            groupService.assignShopsToGroup(groupId, Collections.singletonList(shop.getShopId()));
                        } catch (Exception e) {
                            log.error("分配店铺到运营组失败: 店铺ID={}, 运营组ID={}", shop.getShopId(), groupId, e);
                        }
                    }
                }
                successCount++;
            }
            
            return successCount;
        } catch (Exception e) {
            log.error("批量导入店铺失败", e);
            throw e;
        }
    }
    
    @Override
    public Map<String, Object> importShopsFromExcel(InputStream inputStream) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 创建Excel读取监听器
            ShopImportListener listener = new ShopImportListener(this, groupService);
            
            // 读取Excel
            EasyExcel.read(inputStream, ShopImportDTO.class, listener).sheet().doRead();
            
            // 获取导入结果
            int successCount = listener.getSuccessCount();
            int failCount = listener.getFailCount();
            List<String> errorMsgs = listener.getErrorMsgs();
            
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("errorMsgs", errorMsgs);
            
            return result;
        } catch (Exception e) {
            log.error("解析Excel出错", e);
            result.put("successCount", 0);
            result.put("failCount", 1);
            result.put("errorMsgs", Collections.singletonList(e.getMessage()));
            return result;
        }
    }
    
    @Override
    public void getImportTemplate(HttpServletResponse response) throws IOException {
        // 创建示例数据
        List<ShopImportDTO> list = new ArrayList<>();
        ShopImportDTO example = new ShopImportDTO();
        example.setShopName("示例店铺");
        example.setShopTemuId("TEMU1234567");
        example.setApiKey("API_KEY_EXAMPLE");
        example.setApiSecret("API_SECRET_EXAMPLE");
        example.setAccessToken("ACCESS_TOKEN_EXAMPLE");
        example.setGroupNames("运营组A,运营组B");
        example.setStatus("正常");
        example.setRemark("示例数据");
        list.add(example);
        
        // 设置响应头
        String fileName = "店铺导入模板";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = java.net.URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");
        
        // 写入模板数据
        EasyExcel.write(response.getOutputStream(), ShopImportDTO.class)
                .sheet("店铺数据")
                .doWrite(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteShops(List<Long> shopIds) {
        if (shopIds == null || shopIds.isEmpty()) {
            return false;
        }
        
        try {
            // 检查每个店铺是否被引用，如有任务等关联数据，则不允许删除
            // TODO: 增加相关检查逻辑
            
            // 批量删除店铺
            return this.removeByIds(shopIds);
        } catch (Exception e) {
            log.error("批量删除店铺失败", e);
            throw e;
        }
    }

    /**
     * 获取所有店铺
     *
     * @return 所有店铺列表
     */
    @Override
    public List<Shop> listAllShops() {
        LambdaQueryWrapper<Shop> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Shop::getStatus, "0"); // 只查询状态正常的店铺
        return this.list(wrapper);
    }

    @Override
    public List<Shop> listMemberShops(Long userId) {
        // 获取用户所在的所有运营组ID
        List<Long> groupIds = groupService.getGroupIdsByMemberId(userId);
        if (groupIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 查询这些运营组下的所有店铺
        return shopMapper.selectShopsByGroupIds(groupIds);
    }
} 