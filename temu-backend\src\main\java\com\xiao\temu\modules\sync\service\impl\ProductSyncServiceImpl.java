package com.xiao.temu.modules.sync.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.modules.product.entity.Product;
import com.xiao.temu.modules.product.entity.ProductSku;
import com.xiao.temu.modules.sync.entity.ProductSyncTask;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.sync.vo.ApiResponse;
import com.xiao.temu.modules.product.mapper.ProductMapper;
import com.xiao.temu.modules.product.mapper.ProductSkuMapper;
import com.xiao.temu.modules.sync.mapper.ProductSyncTaskMapper;
import com.xiao.temu.modules.sync.service.ProductSyncService;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.infrastructure.api.TemuApiClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品数据同步服务实现
 */
@Service
@Slf4j
public class ProductSyncServiceImpl implements ProductSyncService {

    @Autowired
    private ProductMapper productMapper;
    
    @Autowired
    private ProductSkuMapper productSkuMapper;

    @Autowired
    private ProductSyncTaskMapper syncTaskMapper;

    @Autowired
    private ShopService shopService;

    @Value("${temu.sync.product-batch-size:50}")
    private int productBatchSize;

    /**
     * 同步商品数据
     * 
     * @param shopId 店铺ID
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse.ProductSyncVO syncProductData(Long shopId) {
        // 获取同步任务
        ProductSyncTask task = syncTaskMapper.getByShopId(shopId);
        if (task == null) {
            // 如果不存在任务，则初始化
            boolean init = initSyncTask(shopId);
            if (!init) {
                ApiResponse.ProductSyncVO errorResponse = new ApiResponse.ProductSyncVO();
                errorResponse.setSuccess(false);
                errorResponse.setErrorCode(500);
                errorResponse.setErrorMsg("初始化同步任务失败");
                errorResponse.setShopId(shopId);
                return errorResponse;
            }
            task = syncTaskMapper.getByShopId(shopId);
        }

        // 获取店铺信息
        Shop shop = shopService.getShopById(shopId).convertToShop();
        if (shop == null) {
            ApiResponse.ProductSyncVO errorResponse = new ApiResponse.ProductSyncVO();
            errorResponse.setSuccess(false);
            errorResponse.setErrorCode(404);
            errorResponse.setErrorMsg("店铺不存在或已被删除");
            errorResponse.setShopId(shopId);
            return errorResponse;
        }

        // 更新任务状态为同步中
        task.setSyncStatus(1);
        task.setErrorMessage(null);
        syncTaskMapper.updateById(task);

        try {
            // 设置API调用所需的公共参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.goods.list.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());

            // 同步商品数据
            boolean hasMoreData = true;
            int pageNo = 1;
            int pageSize = 200; // 每页200条记录
            int totalCount = 0;
            LocalDateTime maxSyncTime = LocalDateTime.now();
            
            // 平台限制，最多只能获取10000条记录
            final int MAX_DATA_LIMIT = 10000;
            
            // 获取数据库中该店铺最新商品的创建时间，用于提前结束同步
            Long latestCreatedAt = getLatestCreatedAt(shopId);
            log.info("店铺ID: {}，数据库中最新商品创建时间戳: {}", shopId, latestCreatedAt);

            while (hasMoreData) {
                log.info("同步商品数据 - 店铺ID: {}, 页码: {}", shopId, pageNo);
                
                // 设置业务参数
                Map<String, Object> businessParams = new HashMap<>();
                businessParams.put("page", pageNo);
                businessParams.put("pageSize", pageSize);
                businessParams.put("timestamp", String.valueOf(Instant.now().getEpochSecond()));

                // 调用API获取结果
                JSONObject result = TemuApiClient.sendRequest(commonParams, businessParams);
                
                if (!result.getBoolean("success")) {
                    String errorMsg = result.getString("errorMsg");
                    if (errorMsg != null && errorMsg.length() > 950) {
                        errorMsg = errorMsg.substring(0, 950) + "...";
                    }
                    throw new RuntimeException("API调用失败: " + errorMsg);
                }

                // 解析结果
                JSONObject resultObj = result.getJSONObject("result");
                if (resultObj == null) {
                    hasMoreData = false;
                    continue;
                }
                
                int totalResultCount = resultObj.getIntValue("totalCount");
                JSONArray dataArray = resultObj.getJSONArray("data");
                
                if (dataArray == null || dataArray.isEmpty()) {
                    hasMoreData = false;
                    continue;
                }
                
                // 提前判断是否可以结束同步
                boolean canTerminate = canTerminateSync(dataArray, latestCreatedAt);
                if (canTerminate) {
                    log.info("检测到当前页所有商品都已同步过，提前结束同步 - 店铺ID: {}, 页码: {}", shopId, pageNo);
                    hasMoreData = false;
                    
                    // 如果是第一页就结束，说明没有新数据，返回成功但不更新数据
                    if (pageNo == 1) {
                        log.info("店铺数据已是最新，无需同步 - 店铺ID: {}", shopId);
                        task.setSyncStatus(2); // 同步成功
                        task.setLastSyncTime(LocalDateTime.now());
                        syncTaskMapper.updateById(task);
                        return ApiResponse.ProductSyncVO.fromTask(task, shop.getShopName());
                    }
                    // 如果不是第一页结束，说明前面有新数据，继续处理当前页
                }
                
                // 解析并保存数据
                List<Product> dataList = parseAndSaveData(dataArray, shopId, maxSyncTime);
                totalCount += dataList.size();
                
                log.info("同步商品数据 - 店铺ID: {}, 页码: {}, 记录数: {}/{}", shopId, pageNo, dataList.size(), totalResultCount);
                
                // 如果返回的数据量小于页大小或解析的数据为空，说明没有更多数据了
                if (dataList.size() < pageSize) {
                    hasMoreData = false;
                } else {
                    pageNo++;
                }

                // 如果API返回的总记录数小于或等于当前已获取的数据量，也停止获取
                if (totalCount >= totalResultCount && totalResultCount > 0) {
                    hasMoreData = false;
                }
                
                // 如果已获取的数据量达到平台限制(10000条)，停止获取更多数据
                if (totalCount >= MAX_DATA_LIMIT) {
                    log.warn("商品数据同步已达到平台最大限制({}条)，自动停止。店铺ID: {}", MAX_DATA_LIMIT, shopId);
                    hasMoreData = false;
                }
            }
            
            log.info("同步商品数据完成 - 店铺ID: {}, 总记录数: {}", shopId, totalCount);
            
            // 更新同步任务状态
            task.setSyncStatus(2); // 同步成功
            task.setLastSyncTime(LocalDateTime.now());
            task.setLastUpdateTime(maxSyncTime);
            // 获取店铺商品实际总数，而不是累加记录
            Integer productCount = productMapper.getProductCountByShopId(shopId);
            task.setTotalRecords(productCount != null ? productCount : 0);
            
            // 获取店铺SKU总数
            Integer skuCount = productSkuMapper.getProductSkuCountByShopId(shopId);
            if (skuCount != null) {
                // 保存SKU总数到数据库字段
                task.setSkuTotalRecords(skuCount);
                log.info("店铺[{}]同步商品SKU成功，总数量: {}", shopId, skuCount);
            } else {
                task.setSkuTotalRecords(0);
            }
            
            syncTaskMapper.updateById(task);
            
            // 返回同步结果
            return ApiResponse.ProductSyncVO.fromTask(task, shop.getShopName());
            
        } catch (Exception e) {
            // 更新任务状态为同步失败
            log.error("同步商品数据异常，店铺ID: {}", shopId, e);
            task.setSyncStatus(3); // 同步失败
            
            // 限制错误消息长度，避免数据库字段溢出
            String errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.length() > 950) {
                errorMessage = errorMessage.substring(0, 950) + "...";
            }
            task.setErrorMessage(errorMessage);
            
            syncTaskMapper.updateById(task);
            
            // 返回错误信息
            ApiResponse.ProductSyncVO errorResponse = new ApiResponse.ProductSyncVO();
            errorResponse.setSuccess(false);
            errorResponse.setErrorCode(500);
            errorResponse.setErrorMsg("同步失败：" + e.getMessage());
            errorResponse.setShopId(shopId);
            errorResponse.setSyncStatus(3);
            return errorResponse;
        }
    }
    
    /**
     * 获取店铺最新商品的创建时间戳
     * 
     * @param shopId 店铺ID
     * @return 最新商品的创建时间戳，如果没有数据则返回0
     */
    private Long getLatestCreatedAt(Long shopId) {
        try {
            // 查询该店铺最新的商品记录
            Product latestProduct = productMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<Product>()
                    .eq("shop_id", shopId)
                    .orderByDesc("created_at")
                    .last("LIMIT 1")
            );
            
            if (latestProduct != null && latestProduct.getCreatedAt() != null) {
                return latestProduct.getCreatedAt();
            }
        } catch (Exception e) {
            log.error("获取最新商品创建时间异常", e);
        }
        return 0L; // 默认为0，表示没有历史数据
    }
    
    /**
     * 判断是否可以提前结束同步
     * 基于商品创建时间判断当前页是否全部都是已同步过的商品
     * 
     * @param dataArray 当前页的商品数据
     * @param latestCreatedAt 数据库中最新商品的创建时间戳
     * @return 是否可以结束同步
     */
    private boolean canTerminateSync(JSONArray dataArray, Long latestCreatedAt) {
        if (dataArray == null || dataArray.isEmpty() || latestCreatedAt == null || latestCreatedAt == 0L) {
            return false;
        }
        
        // 如果没有本地数据，不能提前结束
        if (latestCreatedAt <= 0) {
            return false;
        }
        
        // 检查当前页所有商品是否都已同步过
        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject item = dataArray.getJSONObject(i);
            if (item == null) {
                continue;
            }
            
            Long createdAt = getValueFromPaths(item, Long.class, "createdAt", "productProperties.createdAt", "createAt");
            
            // 如果找到一条记录的创建时间比本地最新的还要新，说明有新数据
            if (createdAt != null && createdAt > latestCreatedAt) {
                return false;
            }
        }
        
        // 所有记录都已经存在于数据库中
        return true;
    }
    
    /**
     * 同步指定SKC ID列表的商品数据
     *
     * @param shopId 店铺ID
     * @param skcIds 需要同步的商品SKC ID集合
     * @return 同步的商品列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Product> syncProductsBySkcIds(Long shopId, Set<Long> skcIds) {
        if (skcIds == null || skcIds.isEmpty()) {
            return Collections.emptyList();
        }

        log.info("根据SKC ID列表同步商品数据 - 店铺ID: {}, SKC ID数量: {}", shopId, skcIds.size());

        // 获取店铺信息
        Shop shop = shopService.getShopById(shopId).convertToShop();
        if (shop == null) {
            log.error("同步商品数据失败，店铺不存在，店铺ID: {}", shopId);
            return Collections.emptyList();
        }

        List<Product> existingProducts = null;
        try {
            // 先从数据库查询已存在的商品SKC
            existingProducts = productMapper.findBySkcIds(shopId, new ArrayList<>(skcIds));

            // 找出未同步的SKC ID
            Set<Long> existingSkcIds = existingProducts.stream()
                    .map(Product::getProductSkcId)
                    .collect(Collectors.toSet());

            Set<Long> missingSkcIds = new HashSet<>(skcIds);
            missingSkcIds.removeAll(existingSkcIds);

            // 如果所有SKC都已存在于数据库，则直接返回
            if (missingSkcIds.isEmpty()) {
                log.info("所有SKC ID已存在于数据库中，无需同步 - 店铺ID: {}", shopId);
                return existingProducts;
            }

            log.info("需要同步的商品SKC数量: {} - 店铺ID: {}", missingSkcIds.size(), shopId);

            // 设置API调用所需的公共参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.goods.list.get"); // 使用针对SKC的商品信息查询API
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());

            // 由于API可能有参数数量限制，我们分批次处理
            List<Product> syncedProducts = new ArrayList<>();
            int batchSize = productBatchSize; // 使用配置的批次大小
            List<List<Long>> batches = new ArrayList<>();

            // 将未同步的SKC ID分成多个批次
            List<Long> missingSkcIdList = new ArrayList<>(missingSkcIds);
            for (int i = 0; i < missingSkcIdList.size(); i += batchSize) {
                batches.add(missingSkcIdList.subList(i, Math.min(i + batchSize, missingSkcIdList.size())));
            }

            // 逐批次同步
            LocalDateTime syncTime = LocalDateTime.now();
            for (List<Long> batch : batches) {
                try {
                    // 设置业务参数
                    Map<String, Object> businessParams = new HashMap<>();
                    businessParams.put("productSkcIds", batch); // 传入SKC ID列表
                    businessParams.put("timestamp", String.valueOf(Instant.now().getEpochSecond()));

                    // 调用API获取结果
                    JSONObject result = TemuApiClient.sendRequest(commonParams, businessParams);

                    if (!result.getBoolean("success")) {
                        String errorMsg = result.getString("errorMsg");
                        log.error("API调用失败: {}", errorMsg);
                        continue;
                    }

                    // 解析结果
                    JSONObject resultObj = result.getJSONObject("result");
                    if (resultObj == null) {
                        continue;
                    }

                    JSONArray dataArray = resultObj.getJSONArray("data");
                    if (dataArray == null || dataArray.isEmpty()) {
                        continue;
                    }

                    // 解析并保存数据
                    List<Product> dataList = parseAndSaveData(dataArray, shopId, syncTime);
                    syncedProducts.addAll(dataList);

                    log.info("同步商品SKC数据 - 店铺ID: {}, 批次大小: {}, 同步成功数量: {}",
                            shopId, batch.size(), dataList.size());

                } catch (Exception e) {
                    log.error("同步商品批次数据异常，店铺ID: {}, 批次大小: {}", shopId, batch.size(), e);
                }
            }

            // 合并已存在和新同步的商品数据
            List<Product> allProducts = new ArrayList<>(existingProducts);
            allProducts.addAll(syncedProducts);

            log.info("商品SKC数据同步完成 - 店铺ID: {}, 已存在: {}, 新同步: {}, 总计: {}",
                    shopId, existingProducts.size(), syncedProducts.size(), allProducts.size());

            return allProducts;

        } catch (Exception e) {
            log.error("同步商品SKC数据异常，店铺ID: {}", shopId, e);
            return existingProducts != null ? existingProducts : Collections.emptyList();
        }
    }
    
    /**
     * 解析并保存商品数据
     * 
     * @param dataArray JSON数据数组
     * @param shopId 店铺ID
     * @param syncTime 同步时间
     * @return 保存的商品列表
     */
    private List<Product> parseAndSaveData(JSONArray dataArray, Long shopId, LocalDateTime syncTime) {
        if (dataArray == null || dataArray.isEmpty()) {
            return Collections.emptyList();
        }
        
        List<Product> productList = new ArrayList<>();
        List<ProductSku> productSkuList = new ArrayList<>();
        List<Long> skcIdList = new ArrayList<>();
        
        // 第一步：提取所有SKC ID，用于批量查询
        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject item = dataArray.getJSONObject(i);
            if (item == null) {
                continue;
            }
            
            Long productSkcId = getValueFromPaths(item, Long.class, "productSkcId", "categories[0].productSkcId", "productProperties.productSkcId");
            if (productSkcId != null) {
                skcIdList.add(productSkcId);
            }
        }
        
        // 如果没有有效的SKC ID，直接返回空列表
        if (skcIdList.isEmpty()) {
            log.warn("没有找到有效的SKC ID，无法同步商品数据");
            return Collections.emptyList();
        }
        
        // 第二步：批量查询已存在的商品数据
        Map<Long, Product> existingProductMap = new HashMap<>();
        if (!skcIdList.isEmpty()) {
            try {
                List<Product> existingProducts = productMapper.findBySkcIds(shopId, skcIdList);
                if (existingProducts != null && !existingProducts.isEmpty()) {
                    for (Product product : existingProducts) {
                        existingProductMap.put(product.getProductSkcId(), product);
                    }
                    log.info("已从数据库找到{}个商品记录", existingProductMap.size());
                }
            } catch (Exception e) {
                log.error("查询已存在商品数据异常", e);
            }
        }
        
        // 第三步：解析数据并过滤/更新已存在的商品
        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject item = dataArray.getJSONObject(i);
            
            if (item == null) {
                continue;
            }
            
            try {
                // 从item获取基本信息
                Long productId = getValueFromPaths(item, Long.class, "productId", "productProperties.productId", "id");
                if (productId == null) {
                    log.warn("无法获取商品ID，跳过此条记录: {}", item);
                    continue;
                }
                
                // 获取商品SKC ID
                Long productSkcId = getValueFromPaths(item, Long.class, "productSkcId", "categories[0].productSkcId", "productProperties.productSkcId");
                if (productSkcId == null) {
                    log.warn("无法获取商品SKC ID，跳过此条记录: productId={}", productId);
                    continue;
                }
                
                // 获取创建时间
                Long createdAt = getValueFromPaths(item, Long.class, "createdAt", "productProperties.createdAt", "createAt");
                if (createdAt == null) {
                    log.warn("无法获取商品创建时间，使用默认值0: productId={}, productSkcId={}", productId, productSkcId);
                    createdAt = 0L;
                }
                
                // 检查商品是否已存在，且创建时间相同
                Product existingProduct = existingProductMap.get(productSkcId);
                if (existingProduct != null && Objects.equals(existingProduct.getCreatedAt(), createdAt)) {
                    log.debug("商品已存在且创建时间相同，跳过: productId={}, productSkcId={}, createdAt={}", 
                            productId, productSkcId, createdAt);
                    continue;
                }
                
                // 获取其他字段
                String productName = getValueFromPaths(item, String.class, "productName", "productSkuSummaries[0].productName", "name");
                Boolean isSupportPersonalization = getValueFromPaths(item, Boolean.class, "isSupportPersonalization", "productProperties.isSupportPersonalization", "supportPersonalization");
                String extCode = getValueFromPaths(item, String.class, "extCode", "productProperties.extCode", "code");
                Integer skcSiteStatus = getValueFromPaths(item, Integer.class, "skcSiteStatus", "categories[0].skcSiteStatus", "status");
                Boolean matchSkcJitMode = getValueFromPaths(item, Boolean.class, "matchSkcJitMode", "categories[0].matchSkcJitMode");
                String mainImageUrl = getValueFromPaths(item, String.class, "mainImageUrl", "categories[0].mainImageUrl", "imageUrl", "imgUrl");
                
                // 创建或更新商品对象
                Product product;
                if (existingProduct != null) {
                    // 使用已存在的商品对象，只更新需要更新的字段
                    product = existingProduct;
                    product.setProductName(productName);
                    product.setCreatedAt(createdAt);
                    product.setIsSupportPersonalization(isSupportPersonalization != null ? isSupportPersonalization : false);
                    product.setExtCode(extCode);
                    product.setSkcSiteStatus(skcSiteStatus != null ? skcSiteStatus : 0);
                    product.setMatchSkcJitMode(matchSkcJitMode != null ? matchSkcJitMode : false);
                    product.setMainImageUrl(mainImageUrl);
                    product.setSyncTime(syncTime);
                    product.setUpdateTime(LocalDateTime.now());
                    
                    log.debug("更新已存在商品: productId={}, productSkcId={}", productId, productSkcId);
                } else {
                    // 创建新的商品对象
                    product = new Product();
                    product.setShopId(shopId);
                    product.setProductId(productId);
                    product.setProductName(productName);
                    product.setProductSkcId(productSkcId);
                    product.setCreatedAt(createdAt);
                    product.setIsSupportPersonalization(isSupportPersonalization != null ? isSupportPersonalization : false);
                    product.setExtCode(extCode);
                    product.setSkcSiteStatus(skcSiteStatus != null ? skcSiteStatus : 0);
                    product.setMatchSkcJitMode(matchSkcJitMode != null ? matchSkcJitMode : false);
                    product.setMainImageUrl(mainImageUrl);
                    product.setSyncTime(syncTime);
                    product.setCreateTime(LocalDateTime.now());
                    product.setUpdateTime(LocalDateTime.now());
                    
                    log.debug("创建新商品: productId={}, productSkcId={}", productId, productSkcId);
                }
                
                productList.add(product);
                
                // 解析SKU数据
                JSONArray skuArray = getValueFromPaths(item, JSONArray.class, "skus", "productSkuSummaries", "items");
                if (skuArray != null && !skuArray.isEmpty()) {
                    for (int j = 0; j < skuArray.size(); j++) {
                        JSONObject skuItem = skuArray.getJSONObject(j);
                        if (skuItem == null) {
                            continue;
                        }
                        
                        // 获取SKU相关字段
                        Long productSkuId = getValueFromPaths(skuItem, Long.class, "productSkuId", "id", "skuId");
                        if (productSkuId == null) {
                            log.warn("无法获取SKU ID，跳过此SKU记录: productId={}, productSkcId={}", productId, productSkcId);
                            continue;
                        }
                        
                        // 创建SKU对象
                        ProductSku sku = new ProductSku();
                        sku.setShopId(shopId);
                        sku.setProductId(productId);
                        sku.setProductSkcId(productSkcId);
                        sku.setProductSkuId(productSkuId);
                        
                        // 从productSkuSpecList中提取颜色和尺码信息
                        JSONArray specList = getValueFromPaths(skuItem, JSONArray.class, "productSkuSpecList");
                        if (specList != null && !specList.isEmpty()) {
                            for (int k = 0; k < specList.size(); k++) {
                                JSONObject specItem = specList.getJSONObject(k);
                                if (specItem != null) {
                                    String parentSpecName = specItem.getString("parentSpecName");
                                    String specName = specItem.getString("specName");
                                    
                                    if (parentSpecName != null && specName != null) {
                                        if ("颜色".equals(parentSpecName)) {
                                            sku.setColor(specName);
                                        } else if ("尺码".equals(parentSpecName)) {
                                            sku.setSize(specName);
                                        }
                                    }
                                }
                            }
                        }
                        
                        sku.setStatus(getValueFromPaths(skuItem, Integer.class, "status", "skuStatus") != null ? 
                                getValueFromPaths(skuItem, Integer.class, "status", "skuStatus") : 1);
                        // 将LocalDateTime转换为Date
                        sku.setSyncTime(java.sql.Timestamp.valueOf(syncTime));
                        
                        productSkuList.add(sku);
                        log.debug("解析SKU数据: productId={}, productSkcId={}, productSkuId={}, color={}, size={}", 
                                productId, productSkcId, productSkuId, sku.getColor(), sku.getSize());
                    }
                }
                
            } catch (Exception e) {
                log.error("解析商品数据异常: {}", e.getMessage(), e);
            }
        }
        
        // 批量保存或更新数据
        if (!productList.isEmpty()) {
            try {
                log.info("开始保存商品数据，数量: {}", productList.size());
                
                // 记录要保存的第一条数据，便于调试
                if (!productList.isEmpty()) {
                    Product firstProduct = productList.get(0);
                    log.info("第一条商品数据: shopId={}, productId={}, productSkcId={}, productName={}",
                            firstProduct.getShopId(), firstProduct.getProductId(),
                            firstProduct.getProductSkcId(), firstProduct.getProductName());
                }
                
                int rows = productMapper.batchSaveOrUpdate(productList);
                
                log.info("商品数据保存完成，尝试保存数量: {}, 实际更新行数: {}", productList.size(), rows);
                
                // 保存后立即查询验证
                if (!productList.isEmpty()) {
                    Product firstProduct = productList.get(0);
                    Long firstSkcId = firstProduct.getProductSkcId();
                    List<Long> checkSkcIdList = Collections.singletonList(firstSkcId);
                    
                    List<Product> verifyProducts = productMapper.findBySkcIds(shopId, checkSkcIdList);
                    if (verifyProducts != null && !verifyProducts.isEmpty()) {
                        log.info("数据已成功保存到数据库，查询验证成功: skcId={}", firstSkcId);
                    } else {
                        log.warn("数据可能未成功保存到数据库，查询验证失败: skcId={}", firstSkcId);
                    }
                }
            } catch (Exception e) {
                log.error("批量保存商品数据异常: {}", e.getMessage(), e);
            }
        } else {
            log.warn("没有有效的商品数据需要保存");
        }
        
        // 批量保存SKU数据
        if (!productSkuList.isEmpty()) {
            try {
                log.info("开始保存商品SKU数据，数量: {}", productSkuList.size());
                
                // 分批处理SKU数据，避免一次性插入过多数据
                int batchSize = 200;
                int totalSize = productSkuList.size();
                int batchCount = (totalSize + batchSize - 1) / batchSize; // 向上取整
                
                for (int i = 0; i < batchCount; i++) {
                    int fromIndex = i * batchSize;
                    int toIndex = Math.min((i + 1) * batchSize, totalSize);
                    List<ProductSku> batchList = productSkuList.subList(fromIndex, toIndex);
                    
                    int rowsAffected = productSkuMapper.batchInsert(batchList);
                    log.info("SKU数据批次{}保存完成，尝试保存数量: {}, 实际影响行数: {}", 
                            i + 1, batchList.size(), rowsAffected);
                }
                
                log.info("商品SKU数据保存完成，总数量: {}", productSkuList.size());
            } catch (Exception e) {
                log.error("批量保存商品SKU数据异常: {}", e.getMessage(), e);
            }
        } else {
            log.warn("没有有效的商品SKU数据需要保存");
        }
        
        return productList;
    }
    
    /**
     * 从JSON对象中通过多个可能的路径获取值
     * 
     * @param <T> 返回值类型
     * @param jsonObject JSON对象
     * @param type 返回值类型的Class
     * @param paths 可能的JSON路径
     * @return 找到的值，如果都没找到则返回null
     */
    @SuppressWarnings("unchecked")
    private <T> T getValueFromPaths(JSONObject jsonObject, Class<T> type, String... paths) {
        if (jsonObject == null || paths.length == 0) {
            return null;
        }
        
        for (String path : paths) {
            try {
                Object value = null;
                
                // 处理数组路径，如 "categories[0].productSkcId"
                if (path.contains("[") && path.contains("]")) {
                    String[] parts = path.split("\\[|\\]\\.");
                    String arrayPath = parts[0];
                    int index = Integer.parseInt(path.substring(path.indexOf("[") + 1, path.indexOf("]")));
                    String fieldPath = path.substring(path.indexOf("].") + 2);
                    
                    JSONArray array = jsonObject.getJSONArray(arrayPath);
                    if (array != null && array.size() > index) {
                        JSONObject item = array.getJSONObject(index);
                        value = item.get(fieldPath);
                    }
                }
                // 处理嵌套对象路径，如 "productProperties.productId"
                else if (path.contains(".")) {
                    String[] parts = path.split("\\.");
                    JSONObject current = jsonObject;
                    
                    for (int i = 0; i < parts.length - 1; i++) {
                        current = current.getJSONObject(parts[i]);
                        if (current == null) {
                            break;
                        }
                    }
                    
                    if (current != null) {
                        value = current.get(parts[parts.length - 1]);
                    }
                }
                // 直接字段
                else {
                    value = jsonObject.get(path);
                }
                
                // 类型转换
                if (value != null) {
                    if (type == String.class) {
                        return (T) value.toString();
                    } else if (type == Long.class && value instanceof Number) {
                        return (T) Long.valueOf(((Number) value).longValue());
                    } else if (type == Integer.class && value instanceof Number) {
                        return (T) Integer.valueOf(((Number) value).intValue());
                    } else if (type == Boolean.class) {
                        if (value instanceof Boolean) {
                            return (T) value;
                        } else if (value instanceof String) {
                            return (T) Boolean.valueOf(((String) value).equalsIgnoreCase("true"));
                        } else if (value instanceof Number) {
                            return (T) Boolean.valueOf(((Number) value).intValue() != 0);
                        }
                    } else if (type == BigDecimal.class) {
                        if (value instanceof BigDecimal) {
                            return (T) value;
                        } else if (value instanceof Number) {
                            return (T) new BigDecimal(value.toString());
                        } else if (value instanceof String) {
                            try {
                                return (T) new BigDecimal((String) value);
                            } catch (NumberFormatException e) {
                                // 忽略转换异常，继续尝试其他路径
                            }
                        }
                    } else if (type == JSONArray.class && value instanceof JSONArray) {
                        return (T) value;
                    } else if (type.isInstance(value)) {
                        return (T) value;
                    }
                }
            } catch (Exception e) {
                // 忽略此路径的异常，继续尝试下一个路径
            }
        }
        
        return null;
    }

    @Override
    public boolean initSyncTask(Long shopId) {
        try {
            // 检查是否已存在任务
            ProductSyncTask existTask = syncTaskMapper.getByShopId(shopId);
            if (existTask != null) {
                // 如果任务已存在，则更新商品总数
                Integer productCount = productMapper.getProductCountByShopId(shopId);
                existTask.setTotalRecords(productCount != null ? productCount : 0);
                syncTaskMapper.updateById(existTask);
                return true;
            }
            
            // 创建新的同步任务
            ProductSyncTask task = new ProductSyncTask();
            task.setShopId(shopId);
            task.setSyncStatus(0); // 未同步
            // 获取当前店铺的商品总数
            Integer productCount = productMapper.getProductCountByShopId(shopId);
            task.setTotalRecords(productCount != null ? productCount : 0);
            task.setCreateTime(LocalDateTime.now());
            task.setUpdateTime(LocalDateTime.now());
            
            return syncTaskMapper.insert(task) > 0;
        } catch (Exception e) {
            log.error("初始化商品同步任务异常，店铺ID: {}", shopId, e);
            return false;
        }
    }

    @Override
    public List<ProductSyncTask> getSyncTasks(List<Long> shopIds) {
        if (shopIds == null || shopIds.isEmpty()) {
            return syncTaskMapper.getAllTasks();
        } else {
            return syncTaskMapper.getByShopIds(shopIds);
        }
    }

    @Override
    public ProductSyncTask getSyncTaskByShopId(Long shopId) {
        return syncTaskMapper.getByShopId(shopId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String executeScheduledSync() {
        List<ProductSyncTask> tasks = syncTaskMapper.getAllTasks();
        int successCount = 0;
        int failCount = 0;
        StringBuilder resultBuilder = new StringBuilder();
        
        for (ProductSyncTask task : tasks) {
            try {
                // 跳过正在同步的任务
                if (task.getSyncStatus() == 1) {
                    resultBuilder.append("店铺ID ").append(task.getShopId()).append(" 正在同步中，跳过。\n");
                    continue;
                }
                
                // 执行同步
                ApiResponse.ProductSyncVO result = syncProductData(task.getShopId());
                
                if (result.getSuccess()) {
                    successCount++;
                    resultBuilder.append("店铺ID ").append(task.getShopId())
                            .append(" 同步成功，记录数: ").append(result.getTotalRecords()).append("\n");
                } else {
                    failCount++;
                    resultBuilder.append("店铺ID ").append(task.getShopId())
                            .append(" 同步失败: ").append(result.getErrorMsg()).append("\n");
                }
            } catch (Exception e) {
                failCount++;
                resultBuilder.append("店铺ID ").append(task.getShopId())
                        .append(" 同步异常: ").append(e.getMessage()).append("\n");
                log.error("定时同步商品数据异常，店铺ID: {}", task.getShopId(), e);
            }
        }
        
        resultBuilder.append("定时同步完成，成功: ").append(successCount)
                .append("，失败: ").append(failCount).append("\n");
        
        return resultBuilder.toString();
    }

    /**
     * 获取指定店铺的商品总数
     *
     * @param shopId 店铺ID
     * @return 商品总数
     */
    @Override
    public Integer getProductCountByShopId(Long shopId) {
        return productMapper.getProductCountByShopId(shopId);
    }

    /**
     * 清空店铺的商品同步数据
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearSyncData(Long shopId) {
        if (shopId == null) {
            log.error("清空同步数据失败: shopId不能为空");
            return false;
        }
        
        try {
            log.info("开始清空店铺[{}]的商品同步数据", shopId);
            
            // 1. 删除SKU数据
            int skuCount = productSkuMapper.deleteByShopId(shopId);
            log.info("已删除店铺[{}]的商品SKU数据记录{}条", shopId, skuCount);
            
            // 2. 删除商品数据
            LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Product::getShopId, shopId);
            int count = productMapper.delete(wrapper);
            log.info("已删除店铺[{}]的商品数据记录{}条", shopId, count);
            
            // 3. 重置同步任务状态 - 使用Mybatis方式清空时间字段
            Map<String, Object> params = new HashMap<>();
            params.put("shopId", shopId);
            int updated = syncTaskMapper.clearTimeFields(params);
            log.info("已重置店铺[{}]的同步任务状态，更新记录数：{}", shopId, updated);
            
            return true;
        } catch (Exception e) {
            log.error("清空店铺[{}]的商品同步数据失败", shopId, e);
            return false;
        }
    }

    /**
     * 获取指定店铺的商品SKU总数
     *
     * @param shopId 店铺ID
     * @return 商品SKU总数
     */
    @Override
    public Integer getProductSkuCountByShopId(Long shopId) {
        return productSkuMapper.getProductSkuCountByShopId(shopId);
    }
} 