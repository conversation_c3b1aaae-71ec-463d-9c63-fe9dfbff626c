<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.purchaseorderv.mapper.ProductionProgressMapper">

    <!-- 查询结果映射 -->
    <resultMap id="BaseResultMap" type="com.xiao.temu.modules.purchaseorderv.entity.ProductionProgress">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="sub_purchase_order_sn" property="subPurchaseOrderSn"/>
        <result column="cutting_status" property="cuttingStatus"/>
        <result column="cutting_time" property="cuttingTime"/>
        <result column="cutting_operator_id" property="cuttingOperatorId"/>
        <result column="workshop_status" property="workshopStatus"/>
        <result column="workshop_time" property="workshopTime"/>
        <result column="workshop_operator_id" property="workshopOperatorId"/>
        <result column="trimming_status" property="trimmingStatus"/>
        <result column="trimming_time" property="trimmingTime"/>
        <result column="trimming_operator_id" property="trimmingOperatorId"/>
        <result column="inspection_status" property="inspectionStatus"/>
        <result column="inspection_time" property="inspectionTime"/>
        <result column="inspection_operator_id" property="inspectionOperatorId"/>
        <result column="packaging_status" property="packagingStatus"/>
        <result column="packaging_time" property="packagingTime"/>
        <result column="packaging_operator_id" property="packagingOperatorId"/>
        <result column="shipping_status" property="shippingStatus"/>
        <result column="shipping_time" property="shippingTime"/>
        <result column="shipping_operator_id" property="shippingOperatorId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 详情视图查询结果映射 -->
    <resultMap id="DetailResultMap" type="com.xiao.temu.modules.purchaseorderv.vo.ProductionProgressVO">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="sub_purchase_order_sn" property="subPurchaseOrderSn"/>
        <result column="cutting_status" property="cuttingStatus"/>
        <result column="cutting_time" property="cuttingTime"/>
        <result column="cutting_operator_id" property="cuttingOperatorId"/>
        <result column="cutting_operator_name" property="cuttingOperatorName"/>
        <result column="workshop_status" property="workshopStatus"/>
        <result column="workshop_time" property="workshopTime"/>
        <result column="workshop_operator_id" property="workshopOperatorId"/>
        <result column="workshop_operator_name" property="workshopOperatorName"/>
        <result column="trimming_status" property="trimmingStatus"/>
        <result column="trimming_time" property="trimmingTime"/>
        <result column="trimming_operator_id" property="trimmingOperatorId"/>
        <result column="trimming_operator_name" property="trimmingOperatorName"/>
        <result column="inspection_status" property="inspectionStatus"/>
        <result column="inspection_time" property="inspectionTime"/>
        <result column="inspection_operator_id" property="inspectionOperatorId"/>
        <result column="inspection_operator_name" property="inspectionOperatorName"/>
        <result column="packaging_status" property="packagingStatus"/>
        <result column="packaging_time" property="packagingTime"/>
        <result column="packaging_operator_id" property="packagingOperatorId"/>
        <result column="packaging_operator_name" property="packagingOperatorName"/>
        <result column="shipping_status" property="shippingStatus"/>
        <result column="shipping_time" property="shippingTime"/>
        <result column="shipping_operator_id" property="shippingOperatorId"/>
        <result column="shipping_operator_name" property="shippingOperatorName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 分页查询生产进度 -->
    <select id="selectProgressPage" resultMap="DetailResultMap">
        SELECT
            pp.*,
            cu.nick_name AS cutting_operator_name,
            wu.nick_name AS workshop_operator_name,
            tu.nick_name AS trimming_operator_name,
            iu.nick_name AS inspection_operator_name,
            pu.nick_name AS packaging_operator_name,
            shu.nick_name AS shipping_operator_name
        FROM
            production_progress pp
        LEFT JOIN sys_user cu ON pp.cutting_operator_id = cu.user_id
        LEFT JOIN sys_user wu ON pp.workshop_operator_id = wu.user_id
        LEFT JOIN sys_user tu ON pp.trimming_operator_id = tu.user_id
        LEFT JOIN sys_user iu ON pp.inspection_operator_id = iu.user_id
        LEFT JOIN sys_user pu ON pp.packaging_operator_id = pu.user_id
        LEFT JOIN sys_user shu ON pp.shipping_operator_id = shu.user_id
        <where>
            <if test="dto.shopId != null">
                AND pp.shop_id = #{dto.shopId}
            </if>
            <if test="dto.subPurchaseOrderSn != null and dto.subPurchaseOrderSn != ''">
                AND pp.sub_purchase_order_sn LIKE CONCAT('%', #{dto.subPurchaseOrderSn}, '%')
            </if>
            <if test="dto.cuttingStatus != null">
                AND pp.cutting_status = #{dto.cuttingStatus}
            </if>
            <if test="dto.workshopStatus != null">
                AND pp.workshop_status = #{dto.workshopStatus}
            </if>
            <if test="dto.trimmingStatus != null">
                AND pp.trimming_status = #{dto.trimmingStatus}
            </if>
            <if test="dto.inspectionStatus != null">
                AND pp.inspection_status = #{dto.inspectionStatus}
            </if>
            <if test="dto.packagingStatus != null">
                AND pp.packaging_status = #{dto.packagingStatus}
            </if>
            <if test="dto.shippingStatus != null">
                AND pp.shipping_status = #{dto.shippingStatus}
            </if>
            <if test="dto.startTime != null">
                AND pp.create_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null">
                AND pp.create_time &lt;= #{dto.endTime}
            </if>
        </where>
        ORDER BY pp.create_time DESC
    </select>

    <!-- 获取生产进度详情 -->
    <select id="getProgressDetail" resultMap="DetailResultMap">
        SELECT
            pp.*,
            cu.nick_name AS cutting_operator_name,
            wu.nick_name AS workshop_operator_name,
            tu.nick_name AS trimming_operator_name,
            iu.nick_name AS inspection_operator_name,
            pu.nick_name AS packaging_operator_name,
            shu.nick_name AS shipping_operator_name
        FROM
            production_progress pp
        LEFT JOIN sys_user cu ON pp.cutting_operator_id = cu.user_id
        LEFT JOIN sys_user wu ON pp.workshop_operator_id = wu.user_id
        LEFT JOIN sys_user tu ON pp.trimming_operator_id = tu.user_id
        LEFT JOIN sys_user iu ON pp.inspection_operator_id = iu.user_id
        LEFT JOIN sys_user pu ON pp.packaging_operator_id = pu.user_id
        LEFT JOIN sys_user shu ON pp.shipping_operator_id = shu.user_id
        WHERE
            pp.shop_id = #{shopId}
        AND
            pp.sub_purchase_order_sn = #{subPurchaseOrderSn}
    </select>

    <!-- 批量获取生产进度详情 -->
    <select id="batchGetProgressDetails" resultMap="DetailResultMap">
        SELECT
            pp.*,
            cu.nick_name AS cutting_operator_name,
            wu.nick_name AS workshop_operator_name,
            tu.nick_name AS trimming_operator_name,
            iu.nick_name AS inspection_operator_name,
            pu.nick_name AS packaging_operator_name,
            shu.nick_name AS shipping_operator_name
        FROM
            production_progress pp
        LEFT JOIN sys_user cu ON pp.cutting_operator_id = cu.user_id
        LEFT JOIN sys_user wu ON pp.workshop_operator_id = wu.user_id
        LEFT JOIN sys_user tu ON pp.trimming_operator_id = tu.user_id
        LEFT JOIN sys_user iu ON pp.inspection_operator_id = iu.user_id
        LEFT JOIN sys_user pu ON pp.packaging_operator_id = pu.user_id
        LEFT JOIN sys_user shu ON pp.shipping_operator_id = shu.user_id
        WHERE
            (pp.shop_id, pp.sub_purchase_order_sn) IN
            <foreach collection="orderInfoList" item="item" open="(" separator="," close=")">
                (#{item.shopId}, #{item.subPurchaseOrderSn})
            </foreach>
    </select>

</mapper> 