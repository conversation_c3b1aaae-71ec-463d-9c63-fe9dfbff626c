.container {
  min-height: 100vh;
  background-color: #f7f7f7;
  box-sizing: border-box;
}

.content {
  flex: 1;
  padding: 30rpx;
  padding-bottom: 130rpx;
}

.role-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  max-width: 300rpx;
}

.role-tag {
  font-size: 22rpx;
  background-color: #e7f1ff;
  color: #0066cc;
  padding: 4rpx 16rpx;
  border-radius: 30rpx;
  z-index: 1;
  margin-left: 10rpx;
  margin-bottom: 6rpx;
  white-space: nowrap;
}

.role-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.leader-row {
  display: flex;
  justify-content: center;
  margin-bottom: 16rpx;
}

.leader-item {
  background-color: #fff1e6;
  border: 1px solid #ffaa5b;
  color: #ff6600;
  width: 220rpx;
}

.leader-dot {
  background-color: #ff6600;
}

.leader-item .role-name {
  color: #ff6600;
  font-weight: 500;
}

.role-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 10rpx 0;
}

.role-item {
  display: flex;
  align-items: center;
  background-color: #e7f1ff;
  padding: 12rpx 0;
  border-radius: 30rpx;
  width: 31%;
  height: 60rpx;
  box-sizing: border-box;
  justify-content: center;
  margin-bottom: 16rpx;
}

.role-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #0066cc;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.role-name {
  font-size: 26rpx;
  color: #0066cc;
  white-space: nowrap;
}

.menu-grid {
  display: flex;
  justify-content: space-between;
  margin: 0 -10rpx;
  margin-bottom: 60rpx;
}

.menu-item {
  flex: 0 0 48%;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.menu-icon {
  margin-bottom: 20rpx;
  position: relative;
}

.scan-icon-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: rgba(82, 196, 26, 0.1);
}

.list-icon-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: rgba(24, 144, 255, 0.1);
}

.icon-scan {
  color: #52c41a;
}

.icon-list {
  color: #1890ff;
}

.menu-text {
  text-align: center;
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: 500;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: block;
}

.activity-list {
  margin-top: 10rpx;
  max-height: 620rpx;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 10rpx 0;
}

.activity-status {
  display: flex;
  align-items: center;
  margin-right: 10rpx;
  min-width: 200rpx;
}

.activity-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #52c41a;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.activity-status-text {
  color: #52c41a;
  font-size: 24rpx;
  white-space: nowrap;
}

.activity-text {
  flex: 1;
  font-size: 24rpx;
  color: var(--text-secondary-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.activity-time {
  font-size: 22rpx;
  color: var(--text-light-color);
  margin-left: 20rpx;
  flex-shrink: 0;
}

.empty-tip {
  text-align: center;
  padding: 20rpx 0;
  color: var(--text-light-color);
  font-size: 24rpx;
}

.tabbar {
  display: flex;
  height: 100rpx;
  border-top: 1rpx solid #eeeeee;
  background-color: #ffffff;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */
  padding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: var(--text-light-color);
}

.tab-active {
  color: var(--primary-color);
}

.tab-text {
  font-size: 22rpx;
  margin-top: 6rpx;
}

.recent-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
} 