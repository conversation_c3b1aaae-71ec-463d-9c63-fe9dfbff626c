<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.shop.mapper.ShopGroupAssignmentMapper">
    
    <resultMap type="com.xiao.temu.modules.shop.entity.ShopGroupAssignment" id="ShopGroupAssignmentResult">
        <id property="id" column="id"/>
        <result property="shopId" column="shop_id"/>
        <result property="groupId" column="group_id"/>
        <result property="assignTime" column="assign_time"/>
        <result property="assignBy" column="assign_by"/>
        <result property="status" column="status"/>
    </resultMap>
    
    <sql id="selectShopGroupAssignmentVo">
        select id, shop_id, group_id, assign_time, assign_by, status from shop_group_assignment
    </sql>
    
    <!-- 根据店铺ID查询所属的所有运营组ID -->
    <select id="selectGroupIdsByShopId" parameterType="Long" resultType="Long">
        select group_id
        from shop_group_assignment
        where shop_id = #{shopId} and status = '0'
    </select>
    
    <!-- 根据运营组ID查询所属的所有店铺ID -->
    <select id="selectShopIdsByGroupId" parameterType="Long" resultType="Long">
        select shop_id
        from shop_group_assignment
        where group_id = #{groupId} and status = '0'
    </select>
    
    <!-- 根据店铺ID查询一个运营组关联 -->
    <select id="selectOneByShopId" parameterType="Long" resultMap="ShopGroupAssignmentResult">
        <include refid="selectShopGroupAssignmentVo"/>
        where shop_id = #{shopId} and status = '0'
        limit 1
    </select>
</mapper> 