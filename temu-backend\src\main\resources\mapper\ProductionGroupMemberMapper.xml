<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.production.mapper.ProductionGroupMemberMapper">

    <resultMap id="productionGroupMemberMap" type="com.xiao.temu.modules.production.entity.ProductionGroupMember">
        <id property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="userId" column="user_id"/>
        <result property="joinTime" column="join_time"/>
        <result property="status" column="status"/>
    </resultMap>
    
    <resultMap id="productionGroupMemberDTOMap" type="com.xiao.temu.modules.production.dto.ProductionGroupMemberDTO">
        <id property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="groupName" column="group_name"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="phone" column="phone"/>
        <result property="joinTime" column="join_time"/>
        <result property="status" column="status"/>
        <result property="roleNames" column="role_names"/>
    </resultMap>
    
    <!-- 分页查询生产组成员列表 -->
    <select id="selectMemberList" resultMap="productionGroupMemberDTOMap">
        SELECT 
            m.id, 
            m.group_id, 
            g.group_name, 
            m.user_id, 
            u.username as user_name, 
            u.nick_name, 
            u.phone, 
            m.join_time, 
            m.status,
            (
                SELECT GROUP_CONCAT(r.role_name SEPARATOR ', ')
                FROM sys_user_role ur
                JOIN sys_role r ON ur.role_id = r.role_id
                WHERE ur.user_id = m.user_id AND r.status = '0'
            ) as role_names
        FROM production_group_member m
        JOIN production_group g ON m.group_id = g.group_id
        JOIN sys_user u ON m.user_id = u.user_id
        <where>
            m.group_id = #{groupId}
            <if test="keyword != null and keyword != ''">
                AND (
                    u.nick_name LIKE CONCAT('%', #{keyword}, '%') OR
                    u.username LIKE CONCAT('%', #{keyword}, '%') OR
                    EXISTS (
                        SELECT 1 FROM sys_user_role ur
                        JOIN sys_role r ON ur.role_id = r.role_id
                        WHERE ur.user_id = m.user_id AND r.status = '0'
                        AND r.role_name LIKE CONCAT('%', #{keyword}, '%')
                    )
                )
            </if>
        </where>
        ORDER BY m.join_time DESC
    </select>
    
    <!-- 查询生产组成员列表（不分页） -->
    <select id="selectAllMembers" resultMap="productionGroupMemberDTOMap">
        SELECT 
            m.id, 
            m.group_id, 
            g.group_name, 
            m.user_id, 
            u.username as user_name, 
            u.nick_name, 
            u.phone, 
            m.join_time, 
            m.status,
            (
                SELECT GROUP_CONCAT(r.role_name SEPARATOR ', ')
                FROM sys_user_role ur
                JOIN sys_role r ON ur.role_id = r.role_id
                WHERE ur.user_id = m.user_id AND r.status = '0'
            ) as role_names
        FROM production_group_member m
        JOIN production_group g ON m.group_id = g.group_id
        JOIN sys_user u ON m.user_id = u.user_id
        WHERE m.group_id = #{groupId}
        ORDER BY m.join_time DESC
    </select>
    
    <!-- 查询用户是否为生产组成员 -->
    <select id="checkUserInGroup" resultType="int">
        SELECT COUNT(1)
        FROM production_group_member
        WHERE group_id = #{groupId} AND user_id = #{userId}
    </select>
    
    <!-- 统计生产组成员数量 -->
    <select id="countGroupMembers" resultType="int">
        SELECT COUNT(1)
        FROM production_group_member m
        JOIN sys_user u ON m.user_id = u.user_id
        WHERE m.group_id = #{groupId} AND m.status = '0'
    </select>
    
    <!-- 批量添加生产组成员 -->
    <insert id="batchAddMembers">
        INSERT INTO production_group_member (group_id, user_id, join_time, status)
        VALUES
        <foreach collection="userIds" item="userId" separator=",">
            (#{groupId}, #{userId}, #{joinTime}, '0')
        </foreach>
        ON DUPLICATE KEY UPDATE 
        status = '0',
        join_time = #{joinTime}
    </insert>
    
    <!-- 删除生产组成员 -->
    <delete id="deleteMember">
        DELETE FROM production_group_member
        WHERE group_id = #{groupId} AND user_id = #{userId}
    </delete>
    
    <!-- 删除生产组的所有成员 -->
    <delete id="deleteAllMembers">
        DELETE FROM production_group_member
        WHERE group_id = #{groupId}
    </delete>
    
    <!-- 查询生产组中的用户ID列表 -->
    <select id="selectUserIdsByGroupId" resultType="java.lang.Long">
        SELECT user_id
        FROM production_group_member
        WHERE group_id = #{groupId} AND status = '0'
    </select>
</mapper> 