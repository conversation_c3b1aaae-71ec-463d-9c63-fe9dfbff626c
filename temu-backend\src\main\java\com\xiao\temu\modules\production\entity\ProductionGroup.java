package com.xiao.temu.modules.production.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产组实体类
 */
@Data
@TableName("production_group")
public class ProductionGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 生产组ID
     */
    @TableId(value = "group_id", type = IdType.AUTO)
    private Long groupId;

    /**
     * 生产组名称
     */
    private String groupName;

    /**
     * 负责人ID
     */
    private Long leaderId;

    /**
     * 状态（0正常 1禁用）
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;
} 