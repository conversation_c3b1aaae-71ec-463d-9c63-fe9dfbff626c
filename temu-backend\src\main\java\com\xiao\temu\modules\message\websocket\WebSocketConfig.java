package com.xiao.temu.modules.message.websocket;

import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

/**
 * WebSocket配置类
 */
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    @Override
    public void configureMessageBroker(MessageBrokerRegistry registry) {
        // 配置消息代理，前缀为/topic用于广播，前缀为/queue用于点对点
        registry.enableSimpleBroker("/topic", "/queue");
        // 配置客户端发送消息的前缀
        registry.setApplicationDestinationPrefixes("/app");
        // 配置点对点消息前缀
        registry.setUserDestinationPrefix("/user");
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        // 注册STOMP端点，客户端通过这个端点进行连接
        registry.addEndpoint("/ws")
                .setAllowedOriginPatterns("*") // 允许所有跨域请求
                .withSockJS(); // 启用SockJS
    }
} 