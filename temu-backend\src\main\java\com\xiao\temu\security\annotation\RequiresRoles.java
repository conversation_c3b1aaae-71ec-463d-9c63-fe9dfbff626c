package com.xiao.temu.security.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 角色注解，用于标识需要特定角色才能访问的方法
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequiresRoles {

    /**
     * 需要的角色标识
     */
    String[] value();

    /**
     * 验证模式：AND | OR
     * AND: 需要满足所有角色
     * OR: 满足任一角色即可
     */
    RequiresPermission.Logical logical() default RequiresPermission.Logical.AND;

    /**
     * 验证未通过时的错误提示信息
     */
    String message() default "角色不足";
} 