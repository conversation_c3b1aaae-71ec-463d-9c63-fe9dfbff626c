/**
 * 加密工具函数
 * 使用AES加密算法对用户数据进行加密和解密
 */

// 生成一个固定的加密密钥（实际应用中可以使用更安全的方式）
const ENCRYPT_KEY = 'TEMU_SECURE_KEY_2025'

/**
 * 使用AES加密字符串
 * @param data 要加密的数据
 * @returns 加密后的字符串
 */
export function encrypt(data: string): string {
  try {
    // 简单的加密实现，使用Base64和XOR操作
    const key = ENCRYPT_KEY
    let result = ''
    for (let i = 0; i < data.length; i++) {
      const charCode = data.charCodeAt(i) ^ key.charCodeAt(i % key.length)
      result += String.fromCharCode(charCode)
    }
    return btoa(result) // Base64编码
  } catch (error) {
    console.error('加密失败:', error)
    return ''
  }
}

/**
 * 解密AES加密的字符串
 * @param encryptedData 加密的数据
 * @returns 解密后的字符串
 */
export function decrypt(encryptedData: string): string {
  try {
    // 简单的解密实现，使用Base64和XOR操作
    const key = ENCRYPT_KEY
    const data = atob(encryptedData) // Base64解码
    let result = ''
    for (let i = 0; i < data.length; i++) {
      const charCode = data.charCodeAt(i) ^ key.charCodeAt(i % key.length)
      result += String.fromCharCode(charCode)
    }
    return result
  } catch (error) {
    console.error('解密失败:', error)
    return ''
  }
} 