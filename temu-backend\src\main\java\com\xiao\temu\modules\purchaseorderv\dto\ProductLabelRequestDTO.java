package com.xiao.temu.modules.purchaseorderv.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 批量打印商品条码请求参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductLabelRequestDTO {
    
    /**
     * 店铺ID与商品SkuID列表的映射
     * key: 店铺ID
     * value: 商品SkuID列表
     */
    private Map<Long, List<String>> shopProductMap;
} 