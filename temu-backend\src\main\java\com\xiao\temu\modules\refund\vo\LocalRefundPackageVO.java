package com.xiao.temu.modules.refund.vo;

import lombok.Data;
import java.util.List;

/**
 * 本地退货包裹返回VO
 */
@Data
public class LocalRefundPackageVO {
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 当前页码
     */
    private Integer pageNo;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
    
    /**
     * 退货包裹列表
     */
    private List<LocalRefundPackageItemVO> items;
    
    /**
     * 店铺名称
     */
    private String shopName;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺备注
     */
    private String shopRemark;

    /**
     * 单个退货包裹展示项
     */
    @Data
    public static class LocalRefundPackageItemVO {
        /**
         * ID
         */
        private Long id;
        
        /**
         * 店铺ID
         */
        private Long shopId;
        
        /**
         * 店铺名称
         */
        private String shopName;
        
        /**
         * 店铺备注
         */
        private String shopRemark;
        
        /**
         * 包裹单号
         */
        private String packageSn;
        
        /**
         * 商品SKU ID
         */
        private Long productSkuId;
        
        /**
         * 商品SKC ID
         */
        private Long productSkcId;
        
        /**
         * 商品SPU ID
         */
        private Long productSpuId;
        
        /**
         * 备货单号
         */
        private String purchaseSubOrderSn;
        
        /**
         * 数量
         */
        private Integer quantity;
        
        /**
         * 主规格
         */
        private String mainSaleSpec;
        
        /**
         * 次规格
         */
        private String secondarySaleSpec;
        
        /**
         * 缩略图
         */
        private String thumbUrl;
        
        /**
         * 订单类型描述
         */
        private String orderTypeDesc;
        
        /**
         * 退货原因描述
         */
        private String reasonDesc;
        
        /**
         * 备注信息
         */
        private String remark;
        
        /**
         * 出库时间
         */
        private String outboundTime;
        
        /**
         * 外部编码
         */
        private String extCode;
    }
} 