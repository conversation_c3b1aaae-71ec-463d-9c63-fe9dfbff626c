<template>
  <div class="icon-select">
    <el-input
      v-model="search"
      placeholder="搜索图标"
      clearable
      @clear="filterIcons"
      @input="filterIcons"
    >
      <template #prefix>
        <el-icon>
          <Search />
        </el-icon>
      </template>
    </el-input>
    
    <div class="icon-list">
      <div 
        v-for="(icon, index) in filteredIcons" 
        :key="index" 
        class="icon-item"
        :class="{active: modelValue === icon.name}"
        @click="selectIcon(icon.name)"
      >
        <el-icon>
          <component :is="icon.component" />
        </el-icon>
        <span class="icon-name">{{ icon.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { Search } from '@element-plus/icons-vue'

// 图标组件列表
const iconList = ref<{ name: string; component: any }[]>([])

// 定义props和emit
const props = defineProps<{
  modelValue: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

// 搜索关键字
const search = ref('')

// 过滤后的图标列表
const filteredIcons = ref<{ name: string; component: any }[]>([])

// 初始化图标列表
onMounted(() => {
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    iconList.value.push({
      name: key,
      component
    })
  }
  filteredIcons.value = [...iconList.value]
})

// 过滤图标
const filterIcons = () => {
  if (!search.value) {
    filteredIcons.value = [...iconList.value]
    return
  }
  
  const keyword = search.value.toLowerCase()
  filteredIcons.value = iconList.value.filter(
    (icon) => icon.name.toLowerCase().includes(keyword)
  )
}

// 选择图标
const selectIcon = (iconName: string) => {
  emit('update:modelValue', iconName)
}
</script>

<style scoped>
.icon-select {
  width: 100%;
}

.icon-list {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12px;
  margin-top: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px;
  cursor: pointer;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  transition: all 0.3s;
}

.icon-item:hover {
  background-color: #f5f7fa;
  color: #409eff;
  border-color: #c6e2ff;
}

.icon-item.active {
  background-color: #ecf5ff;
  color: #409eff;
  border-color: #409eff;
}

.icon-name {
  margin-top: 4px;
  font-size: 12px;
  word-break: break-all;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}
</style> 