package com.xiao.temu.modules.production.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.production.dto.ProductionGroupDTO;
import com.xiao.temu.modules.production.dto.QueryGroupDTO;
import com.xiao.temu.modules.production.entity.ProductionGroup;
import com.xiao.temu.modules.production.entity.ProductionGroupShopAssignment;
import com.xiao.temu.modules.production.mapper.ProductionGroupMapper;
import com.xiao.temu.modules.production.mapper.ProductionGroupShopAssignmentMapper;
import com.xiao.temu.modules.production.service.ProductionGroupMemberService;
import com.xiao.temu.modules.production.service.ProductionGroupRoleAssignmentService;
import com.xiao.temu.modules.production.service.ProductionGroupService;
import com.xiao.temu.modules.shop.dto.ShopWithProductionGroupsDTO;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.shop.mapper.ShopMapper;
import com.xiao.temu.modules.system.service.SysUserService;
import com.xiao.temu.security.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生产组服务实现类
 */
@Service
@RequiredArgsConstructor
public class ProductionGroupServiceImpl implements ProductionGroupService {

    private final ProductionGroupMapper productionGroupMapper;
    private final ProductionGroupMemberService memberService;
    private final ProductionGroupRoleAssignmentService roleAssignmentService;
    private final ProductionGroupShopAssignmentMapper productionGroupShopAssignmentMapper;
    private final ShopMapper shopMapper;
    private final SysUserService userService;

    @Override
    public IPage<ProductionGroupDTO> getGroupList(QueryGroupDTO queryDTO) {
        Page<ProductionGroupDTO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        return productionGroupMapper.selectGroupList(page, queryDTO);
    }

    @Override
    public ProductionGroupDTO getGroupById(Long groupId) {
        return productionGroupMapper.selectGroupById(groupId);
    }

    @Override
    public int insertGroup(ProductionGroupDTO groupDTO) {
        ProductionGroup group = new ProductionGroup();
        BeanUtils.copyProperties(groupDTO, group);
        
        // 设置创建时间
        Date now = new Date();
        group.setCreateTime(now);
        group.setUpdateTime(now);
        
        // 设置默认状态为正常
        if (group.getStatus() == null) {
            group.setStatus("0");
        }
        
        return productionGroupMapper.insert(group);
    }

    @Override
    public int updateGroup(ProductionGroupDTO groupDTO) {
        ProductionGroup group = new ProductionGroup();
        BeanUtils.copyProperties(groupDTO, group);
        
        // 设置更新时间
        group.setUpdateTime(new Date());
        
        return productionGroupMapper.updateById(group);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteGroup(Long groupId) {
        // 检查生产组是否存在
        if (!existsById(groupId)) {
            throw new RuntimeException("生产组不存在");
        }
        
        // 先删除生产组的所有角色分配
        roleAssignmentService.deleteAllGroupAssignments(groupId);
        
        // 再删除生产组的所有成员
        memberService.removeAllMembers(groupId);
        
        // 删除生产组与店铺的关联
        LambdaQueryWrapper<ProductionGroupShopAssignment> assignmentWrapper = new LambdaQueryWrapper<>();
        assignmentWrapper.eq(ProductionGroupShopAssignment::getGroupId, groupId);
        productionGroupShopAssignmentMapper.delete(assignmentWrapper);
        
        // 最后删除生产组
        return productionGroupMapper.deleteById(groupId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteGroups(Long[] groupIds) {
        int rows = 0;
        for (Long groupId : groupIds) {
            rows += deleteGroup(groupId);
        }
        return rows;
    }

    @Override
    public int changeStatus(Long groupId, String status) {
        return productionGroupMapper.updateStatus(groupId, status);
    }

    @Override
    public boolean checkGroupNameUnique(String groupName, Long groupId) {
        int count = productionGroupMapper.checkGroupNameUnique(groupName, groupId);
        return count == 0;
    }

    @Override
    public int setGroupLeader(Long groupId, Long leaderId) {
        // 检查生产组是否存在
        ProductionGroup group = productionGroupMapper.selectById(groupId);
        if (group == null) {
            throw new RuntimeException("生产组不存在");
        }
        
        // 设置新的组长
        group.setLeaderId(leaderId);
        group.setUpdateTime(new Date());
        
        // 确保组长也是组内成员
        if (!memberService.checkUserInGroup(groupId, leaderId)) {
            memberService.addMember(groupId, leaderId);
        }
        
        return productionGroupMapper.updateById(group);
    }

    @Override
    public List<ProductionGroup> getGroupsByLeaderId(Long userId) {
        return productionGroupMapper.selectGroupsByLeaderId(userId);
    }

    @Override
    public List<ProductionGroupDTO> getGroupsByMemberId(Long userId) {
        // 查询用户所在的所有生产组ID
        List<Long> groupIds = memberService.getGroupIdsByUserId(userId);
        
        // 根据组ID查询组详情
        List<ProductionGroupDTO> groups = new ArrayList<>();
        for (Long groupId : groupIds) {
            ProductionGroupDTO group = productionGroupMapper.selectGroupById(groupId);
            if (group != null) {
                groups.add(group);
            }
        }
        
        return groups;
    }

    @Override
    public boolean isGroupLeader(Long userId, Long groupId) {
        ProductionGroup group = productionGroupMapper.selectById(groupId);
        return group != null && userId.equals(group.getLeaderId());
    }

    @Override
    public boolean isUserGroupLeader(Long userId) {
        LambdaQueryWrapper<ProductionGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductionGroup::getLeaderId, userId)
                   .eq(ProductionGroup::getStatus, "0");
        return productionGroupMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean existsById(Long groupId) {
        return productionGroupMapper.selectById(groupId) != null;
    }
    
    @Override
    public List<ShopWithProductionGroupsDTO> getUnassignedShops(Long groupId) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getCurrentUserId();
        
        // 获取生产组信息，验证当前用户是否为该组组长
        ProductionGroup group = productionGroupMapper.selectById(groupId);
        if (group == null) {
            throw new RuntimeException("生产组不存在");
        }
        
        // 校验权限：只有管理员或该组的组长可以获取未分配店铺列表
        boolean isAdmin = SecurityUtils.isAdmin();
        boolean isGroupLeader = group.getLeaderId().equals(currentUserId);
        
        if (!isAdmin && !isGroupLeader) {
            throw new RuntimeException("您没有权限查看未分配的店铺");
        }
        
        // 获取管理员用户ID列表
        final List<Long> adminUserIds = userService.getAdminUserIds();
        
        // 构建查询条件：组长自己创建的店铺和管理员创建的店铺
        List<Shop> filteredShops;
        if (isAdmin) {
            // 管理员可以看到所有店铺
            filteredShops = shopMapper.selectList(new LambdaQueryWrapper<>());
        } else {
            // 组长只能看到自己创建的和管理员创建的店铺
            LambdaQueryWrapper<Shop> shopQueryWrapper = new LambdaQueryWrapper<>();
            shopQueryWrapper.and(wrapper -> 
                wrapper.eq(Shop::getCreateBy, currentUserId)
                      .or()
                      .in(!adminUserIds.isEmpty(), Shop::getCreateBy, adminUserIds)
            );
            filteredShops = shopMapper.selectList(shopQueryWrapper);
        }
        
        List<ShopWithProductionGroupsDTO> resultShops = new ArrayList<>();
        
        // 获取当前生产组已分配的店铺ID列表
        LambdaQueryWrapper<ProductionGroupShopAssignment> currentGroupWrapper = new LambdaQueryWrapper<>();
        currentGroupWrapper.eq(ProductionGroupShopAssignment::getGroupId, groupId)
                          .eq(ProductionGroupShopAssignment::getStatus, "0");
        List<ProductionGroupShopAssignment> currentGroupAssignments = productionGroupShopAssignmentMapper.selectList(currentGroupWrapper);
        Set<Long> currentGroupShopIds = currentGroupAssignments.stream()
                                                          .map(ProductionGroupShopAssignment::getShopId)
                                                          .collect(Collectors.toSet());
        
        // 查询所有店铺的生产组分配信息
        for (Shop shop : filteredShops) {
            // 跳过已分配给当前生产组的店铺
            if (currentGroupShopIds.contains(shop.getShopId())) {
                continue;
            }
            
            ShopWithProductionGroupsDTO shopWithGroups = new ShopWithProductionGroupsDTO();
            BeanUtils.copyProperties(shop, shopWithGroups);
            
            // 查询该店铺所属的所有生产组
            List<ProductionGroup> belongGroups = getGroupsByShopId(shop.getShopId());
            shopWithGroups.setGroups(belongGroups);
            
            // 构建生产组名称字符串
            if (belongGroups != null && !belongGroups.isEmpty()) {
                String groupNames = belongGroups.stream()
                                             .map(ProductionGroup::getGroupName)
                                             .collect(Collectors.joining(", "));
                shopWithGroups.setGroupNames(groupNames);
            } else {
                shopWithGroups.setGroupNames("");
            }
            
            // 屏蔽敏感信息
            shopWithGroups.setApiKey(null);
            shopWithGroups.setApiSecret(null);
            shopWithGroups.setAccessToken(null);
            
            resultShops.add(shopWithGroups);
        }
        
        return resultShops;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int assignShopsToGroup(Long groupId, List<Long> shopIds) {
        if (shopIds == null || shopIds.isEmpty()) {
            return 0;
        }
        
        // 校验生产组是否存在
        if (!existsById(groupId)) {
            throw new RuntimeException("生产组不存在");
        }
        
        int successCount = 0;
        
        for (Long shopId : shopIds) {
            // 查询店铺是否存在
            Shop shop = shopMapper.selectById(shopId);
            if (shop == null) {
                continue;
            }
            
            // 检查是否已经分配给该生产组
            LambdaQueryWrapper<ProductionGroupShopAssignment> existWrapper = new LambdaQueryWrapper<>();
            existWrapper.eq(ProductionGroupShopAssignment::getShopId, shopId)
                       .eq(ProductionGroupShopAssignment::getGroupId, groupId);
            if (productionGroupShopAssignmentMapper.selectCount(existWrapper) > 0) {
                continue;
            }
            
            // 为店铺分配生产组
            ProductionGroupShopAssignment assignment = new ProductionGroupShopAssignment();
            assignment.setShopId(shopId);
            assignment.setGroupId(groupId);
            assignment.setAssignTime(LocalDateTime.now());
            assignment.setAssignBy(SecurityUtils.getCurrentUserId()); // 设置当前操作用户为分配人
            assignment.setStatus("0"); // 设置状态为正常
            
            if (productionGroupShopAssignmentMapper.insert(assignment) > 0) {
                successCount++;
            }
        }
        
        return successCount;
    }
    
    @Override
    public List<ProductionGroup> getGroupsByShopId(Long shopId) {
        // 查询店铺关联的所有生产组ID
        List<Long> groupIds = productionGroupShopAssignmentMapper.selectGroupIdsByShopId(shopId);
        
        if (groupIds == null || groupIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 查询生产组详情
        LambdaQueryWrapper<ProductionGroup> groupWrapper = new LambdaQueryWrapper<>();
        groupWrapper.in(ProductionGroup::getGroupId, groupIds)
                   .eq(ProductionGroup::getStatus, "0");
        
        return productionGroupMapper.selectList(groupWrapper);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeShopFromGroup(Long groupId, Long shopId) {
        // 首先检查生产组是否存在
        if (!existsById(groupId)) {
            throw new RuntimeException("移除店铺失败，生产组不存在");
        }
        
        // 检查店铺是否存在
        Shop shop = shopMapper.selectById(shopId);
        if (shop == null) {
            throw new RuntimeException("移除店铺失败，店铺不存在");
        }
        
        // 删除店铺与生产组的关联记录
        LambdaQueryWrapper<ProductionGroupShopAssignment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductionGroupShopAssignment::getShopId, shopId)
              .eq(ProductionGroupShopAssignment::getGroupId, groupId);
        
        return productionGroupShopAssignmentMapper.delete(wrapper);
    }
    
    @Override
    public List<Shop> getShopsByGroupId(Long groupId) {
        // 检查生产组是否存在
        if (!existsById(groupId)) {
            throw new RuntimeException("获取店铺列表失败，生产组不存在");
        }
        
        // 获取生产组分配的所有店铺ID
        LambdaQueryWrapper<ProductionGroupShopAssignment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductionGroupShopAssignment::getGroupId, groupId)
                  .eq(ProductionGroupShopAssignment::getStatus, "0");
        
        List<ProductionGroupShopAssignment> assignments = productionGroupShopAssignmentMapper.selectList(queryWrapper);
        
        if (assignments.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 收集所有店铺ID
        List<Long> shopIds = assignments.stream()
                                     .map(ProductionGroupShopAssignment::getShopId)
                                     .collect(Collectors.toList());
        
        // 查询店铺信息
        LambdaQueryWrapper<Shop> shopWrapper = new LambdaQueryWrapper<>();
        shopWrapper.in(Shop::getShopId, shopIds);
        List<Shop> shops = shopMapper.selectList(shopWrapper);
        
        // 处理敏感信息
        for (Shop shop : shops) {
            shop.setApiKey(null);
            shop.setApiSecret(null);
            shop.setAccessToken(null);
        }
        
        return shops;
    }
} 