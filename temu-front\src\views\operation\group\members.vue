<template>
  <div class="app-container">
    <div class="toolbar">
      <div class="left-area">
        <h3>运营组：{{ groupName }}</h3>
      </div>
      <div class="right-area">
        <el-button 
          type="primary" 
          icon="Plus" 
          @click="handleAddMember"
          v-hasPermi="['operation:group:edit']"
        >
          添加成员
        </el-button>
        <el-button 
          icon="Back" 
          @click="goBack"
        >
          返回
        </el-button>
      </div>
    </div>

    <!-- 成员列表 -->
    <el-table
      v-loading="loading"
      :data="memberList"
      style="width: 100%"
      border
    >
      <el-table-column label="ID" prop="id" width="80" />
      <el-table-column label="用户名" prop="username" width="150" />
      <el-table-column label="昵称" prop="nickName" width="150" />
      <el-table-column label="加入时间" prop="joinTime" width="180">
        <template #default="scope">
          {{ formatTime(scope.row.joinTime) }}
        </template>
      </el-table-column>
      <el-table-column label="角色" width="100" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.isLeader" type="danger">负责人</el-tag>
          <el-tag v-else>成员</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'info'">
            {{ scope.row.status === '0' ? '正常' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="220" align="center">
        <template #default="scope">
          <el-button
            v-if="!scope.row.isLeader"
            type="primary"
            link
            icon="Top"
            @click="handleSetLeader(scope.row)"
            v-hasPermi="['operation:group:edit']"
          >
            设为负责人
          </el-button>
          <el-button
            v-if="!scope.row.isLeader"
            type="danger"
            link
            icon="Delete"
            @click="handleRemoveMember(scope.row)"
            v-hasPermi="['operation:group:edit']"
          >
            移除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-if="total > 0"
      class="pagination"
      :total="total"
      v-model:current-page="pageNum"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 添加成员对话框 -->
    <el-dialog
      title="添加成员"
      v-model="dialogVisible"
      width="600px"
      append-to-body
    >
      <div class="dialog-content">
        <el-form :inline="true" class="search-form">
          <el-form-item label="搜索用户">
            <el-input
              v-model="searchQuery"
              placeholder="输入用户名或昵称"
              @keyup.enter="searchUsers"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="searchUsers">搜索</el-button>
          </el-form-item>
        </el-form>

        <el-table
          v-loading="userSearchLoading"
          :data="searchUserList"
          @selection-change="handleSelectionChange"
          style="width: 100%"
          border
        >
          <el-table-column type="selection" width="50" />
          <el-table-column label="用户ID" prop="userId" width="80" />
          <el-table-column label="用户名" prop="username" />
          <el-table-column label="昵称" prop="nickName" />
        </el-table>

        <div v-if="searchUserList.length === 0 && !userSearchLoading" class="empty-tip">
          <p>未找到用户，请修改搜索条件</p>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitAddMembers"
            :disabled="selectedUsers.length === 0"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getGroupDetail, getGroupMembers, addGroupMembers, removeGroupMember, setGroupLeader } from '@/api/group'
import { getUserList } from '@/api/user'
import type { GroupMember } from '@/types/group'
import { formatTime } from '@/utils/format'

const route = useRoute()
const router = useRouter()
const groupId = Number(route.params.id)
const groupName = ref('')

// 成员列表数据
const memberList = ref<GroupMember[]>([])
const total = ref(0)
const loading = ref(false)
const pageNum = ref(1)
const pageSize = ref(10)

// 添加成员对话框
const dialogVisible = ref(false)
const searchQuery = ref('')
const searchUserList = ref<any[]>([])
const selectedUsers = ref<any[]>([])
const userSearchLoading = ref(false)

// 获取运营组详情
const loadGroupDetail = async () => {
  try {
    const res = await getGroupDetail(groupId)
    if (res.data) {
      groupName.value = res.data.groupName || ''
    }
  } catch (error) {
    console.error('获取运营组详情失败', error)
  }
}

// 获取成员列表
const loadMembers = async () => {
  loading.value = true
  try {
    const res = await getGroupMembers(groupId, pageNum.value, pageSize.value)
    memberList.value = res.data.records || []
    total.value = res.data.total || 0
  } catch (error) {
    console.error('获取成员列表失败', error)
  } finally {
    loading.value = false
  }
}

// 搜索用户
const searchUsers = async () => {
  if (!searchQuery.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }

  userSearchLoading.value = true
  try {
    // 查询条件设置为模糊查询用户名或昵称
    const params = {
      username: searchQuery.value,
      nickName: searchQuery.value,
      pageNum: 1,
      pageSize: 50
    }
    const res = await getUserList(params)
    const users = res.data.records || []
    
    // 过滤掉已经是成员的用户
    const memberUserIds = memberList.value.map(member => member.userId)
    searchUserList.value = users.filter((user: any) => !memberUserIds.includes(user.userId))
  } catch (error) {
    console.error('搜索用户失败', error)
  } finally {
    userSearchLoading.value = false
  }
}

// 处理表格选择变更
const handleSelectionChange = (selection: any[]) => {
  selectedUsers.value = selection
}

// 提交添加成员
const submitAddMembers = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请选择要添加的成员')
    return
  }

  try {
    const userIds = selectedUsers.value.map(user => user.userId)
    await addGroupMembers(groupId, userIds)
    ElMessage.success('添加成员成功')
    dialogVisible.value = false
    loadMembers() // 重新加载成员列表
  } catch (error) {
    console.error('添加成员失败', error)
  }
}

// 移除成员
const handleRemoveMember = (row: GroupMember) => {
  ElMessageBox.confirm(
    `确认将用户 "${row.nickName}" 从运营组中移除吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await removeGroupMember(groupId, row.userId)
      ElMessage.success('移除成员成功')
      loadMembers() // 重新加载成员列表
    } catch (error) {
      console.error('移除成员失败', error)
    }
  }).catch(() => {
    // 取消删除
  })
}

// 设置负责人
const handleSetLeader = (row: GroupMember) => {
  ElMessageBox.confirm(
    `确认将用户 "${row.nickName}" 设置为运营组负责人吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await setGroupLeader(groupId, row.userId)
      ElMessage.success('设置负责人成功')
      loadMembers() // 重新加载成员列表
    } catch (error) {
      console.error('设置负责人失败', error)
    }
  }).catch(() => {
    // 取消操作
  })
}

// 打开添加成员对话框
const handleAddMember = () => {
  dialogVisible.value = true
  searchQuery.value = ''
  searchUserList.value = []
  selectedUsers.value = []
}

// 返回上一页
const goBack = () => {
  router.push(`/operation/group/detail/${groupId}`)
}

// 分页操作
const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadMembers()
}

const handleCurrentChange = (page: number) => {
  pageNum.value = page
  loadMembers()
}

onMounted(() => {
  if (!groupId) {
    router.push('/operation/group')
    return
  }
  
  loadGroupDetail()
  loadMembers()
})
</script>

<style scoped>
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-content {
  margin-bottom: 20px;
}

.empty-tip {
  text-align: center;
  color: #909399;
  padding: 30px 0;
}

.search-form {
  margin-bottom: 20px;
}
</style> 