<view class="container">
  <page-header title="工作记录"></page-header>

  <view class="content">
    <block wx:if="{{recordList.length}}">
      <view class="order-card" wx:for="{{recordList}}" wx:key="subPurchaseOrderSn" data-item="{{item}}" bindtap="goToProgress">
        <view class="order-title">备货单 {{item.subPurchaseOrderSn}}</view>
        <view class="order-step">工序：{{item.progressTypeName}} | 时间：{{item.operationTime}}</view>
      </view>
    </block>
    <view class="empty-tip" wx:else>暂无工作记录</view>
  </view>
</view> 