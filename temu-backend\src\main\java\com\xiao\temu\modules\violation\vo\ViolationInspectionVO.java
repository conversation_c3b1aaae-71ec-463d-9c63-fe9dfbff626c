package com.xiao.temu.modules.violation.vo;

import com.xiao.temu.modules.violation.dto.ViolationInspectionDTO;
import lombok.Data;

import java.util.List;

/**
 * 违规信息和质检结果综合查询VO
 */
@Data
public class ViolationInspectionVO {
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 总页数
     */
    private Long pages;
    
    /**
     * 当前页码
     */
    private Integer pageNum;
    
    /**
     * 每页记录数
     */
    private Integer pageSize;
    
    /**
     * 查询结果数据列表
     */
    private List<ViolationInspectionDTO> rows;
} 