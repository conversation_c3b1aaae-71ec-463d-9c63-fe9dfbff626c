package com.xiao.temu.modules.sales.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 销售定制信息实体类
 * 对应数据库表：sales_custom_info
 * 存储商品的定制相关信息
 */
@Data
@TableName("sales_custom_info")
public class SalesCustomInfo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 商品SKC ID
     */
    private Long productSkcId;

    /**
     * 是否为定制品
     */
    private Boolean isCustomGoods;

    /**
     * 定制字数限制
     */
    private Integer limitNum;

    /**
     * 定制效果图
     */
    private String effectPicture;

    /**
     * 同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 