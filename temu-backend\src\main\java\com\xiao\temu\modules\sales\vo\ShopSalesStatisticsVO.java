package com.xiao.temu.modules.sales.vo;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 店铺销售统计视图对象
 */
@Data
public class ShopSalesStatisticsVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺名称
     */
    private String shopName;
    
    /**
     * 店铺备注
     */
    private String shopRemark;
    
    /**
     * 统计日期
     */
    private LocalDate statisticsDate;
    
    /**
     * 今日销量
     */
    private Integer todaySales;
    
    /**
     * 近7天销量
     */
    private Integer lastWeekSales;
    
    /**
     * 近30天销量
     */
    private Integer lastMonthSales;
    
    /**
     * 商品总数
     */
    private Integer totalProducts;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 错误信息
     */
    private String errorMsg;
} 