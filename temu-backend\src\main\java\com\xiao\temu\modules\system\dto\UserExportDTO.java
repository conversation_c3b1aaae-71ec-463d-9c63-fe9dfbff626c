package com.xiao.temu.modules.system.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

/**
 * 用户导出DTO
 */
@Data
public class UserExportDTO {

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID", index = 0)
    private Long userId;

    /**
     * 用户名
     */
    @ExcelProperty(value = "用户名", index = 1)
    private String username;

    /**
     * 昵称
     */
    @ExcelProperty(value = "昵称", index = 2)
    private String nickName;

    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱", index = 3)
    private String email;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号码", index = 4)
    private String phone;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", index = 5)
    private String statusLabel;

    /**
     * 角色
     */
    @ExcelProperty(value = "角色", index = 6)
    private String roleNames;

    /**
     * 状态（0正常 1禁用）
     */
    @ExcelIgnore
    private String status;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间", index = 7)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间", index = 8)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 9)
    private String remark;
}