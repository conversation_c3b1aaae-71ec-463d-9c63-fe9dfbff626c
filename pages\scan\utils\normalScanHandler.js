/**
 * 普通扫码处理功能
 */
const productionProgressApi = require('../../../api/productionProgressApi');
const userRoleManager = require('./userRoleManager');

/**
 * 处理扫码结果
 */
function handleScanResult(page, qrData) {
  // 在控制台打印处理的二维码数据
  console.log('正在处理的二维码数据:', qrData);
  
  // 确保shopId是数值类型
  if (typeof qrData.shopId === 'string') {
    qrData.shopId = parseInt(qrData.shopId, 10);
    console.log('转换shopId为数值类型:', qrData.shopId);
  }
  
  wx.showLoading({
    title: '处理中...',
  });

  // 查询备货单生产进度
  productionProgressApi.getProgressDetail(qrData.shopId, qrData.subPurchaseOrderSn)
    .then(res => {
      console.log('获取到的进度信息:', res);
      
      // 保存扫描结果
      const result = {
        ...qrData,
        progressDetail: res
      };
      
      console.log('设置扫描结果到页面数据:', result);
      
      // 检查进度信息是否正确
      if (res && Object.keys(res).length > 0) {
        console.log('进度信息正常，可以显示工序');
        
        // 处理快速扫码模式
        if (page.data.fastScanEnabled) {
          console.log('快速扫码模式已启用，自动选择用户角色的工序');
          // 不设置scanResult和改变页面状态，直接处理工序
          page.autoSelectUserRoleProgress(res, qrData);
          return; // 直接返回，不更新UI
        }
        
        // 非快速扫码模式，显示扫描结果
        page.setData({
          scanResult: result,
          scanVisible: false
        });
        
        // 确保更新工序项目列表
        page.updateProgressItems();
      } else {
        console.error('进度信息为空或不完整');
        wx.showToast({
          title: '无法获取工序信息',
          icon: 'none'
        });
        // 重置扫码界面，可以继续扫码
        page.resetScannerState();
      }
    })
    .catch(err => {
      console.error('获取进度信息失败:', err);
      
      wx.showModal({
        title: '获取失败',
        content: '无法获取备货单进度信息',
        showCancel: false
      });
      
      // 重置扫码界面，可以继续扫码
      page.resetScannerState();
    })
    .finally(() => {
      wx.hideLoading();
    });
}

/**
 * 判断用户是否有权限编辑某个工序
 */
function canEditProgress(page, progressType) {
  try {
    console.log('检查工序权限:', progressType);
    const { userRoles, scanResult, validProgressRoles } = page.data;
    
    // 首先检查是否是有效的生产进度工序类型
    if (!validProgressRoles.includes(progressType)) {
      console.log('非有效生产进度工序类型:', progressType);
      return false;
    }
    
    // 如果没有扫描结果，显示所有工序（调试用）
    if (!scanResult || !scanResult.progressDetail) {
      console.log('没有扫描结果或进度信息，显示所有工序');
      return true;
    }
    
    // 检查该工序是否已完成
    let isCompleted = false;
    if (progressType === 'cutting' && scanResult.progressDetail.cuttingStatus === 1) {
      isCompleted = true;
    } else if (progressType === 'workshop' && scanResult.progressDetail.workshopStatus === 1) {
      isCompleted = true;
    } else if (progressType === 'trimming' && scanResult.progressDetail.trimmingStatus === 1) {
      isCompleted = true;
    } else if (progressType === 'shipping' && scanResult.progressDetail.shippingStatus === 1) {
      isCompleted = true;
    } else if (progressType === 'packaging' && scanResult.progressDetail.packagingStatus === 1) {
      isCompleted = true;
    } else if (progressType === 'inspection' && scanResult.progressDetail.inspectionStatus === 1) {
      isCompleted = true;
    }
    
    // 开发调试模式：始终显示未完成的工序
    if (!isCompleted) {
      console.log('显示未完成工序:', progressType);
      return true;
    } else {
      console.log('工序已完成，不显示:', progressType);
      return false; // 已完成的工序不显示
    }
  } catch (error) {
    console.error('检查工序权限时发生错误:', error);
    // 发生错误时默认返回true，避免界面异常
    return true;
  }
}

/**
 * 切换工序选择状态
 */
function toggleSelectProgress(page, e) {
  try {
    // 打印完整事件对象
    console.log('触发工序切换事件:', e);
    
    // 确保从dataset中获取type值
    const progressType = e.currentTarget.dataset.type;
    const progressName = e.currentTarget.dataset.name || '';
    console.log('获取到的工序类型:', progressType, 'progressName:', progressName, 'dataset完整内容:', e.currentTarget.dataset);
    
    if (!progressType) {
      console.error('未获取到工序类型:', e.currentTarget.dataset);
      return;
    }
    
    console.log('切换工序选择状态, 类型:', progressType);
    
    // 获取当前选中的工序列表
    const selectedProgressTypes = [...page.data.selectedProgressTypes];
    console.log('当前选中的工序列表:', selectedProgressTypes);
    
    // 检查是否已完成
    const { scanResult } = page.data;
    if (scanResult && scanResult.progressDetail) {
      let isCompleted = false;
      
      // 简化判断逻辑，确保正确处理
      if (progressType === 'cutting') {
        isCompleted = scanResult.progressDetail.cuttingStatus === 1;
      } else if (progressType === 'workshop') {
        isCompleted = scanResult.progressDetail.workshopStatus === 1;
      } else if (progressType === 'trimming') {
        isCompleted = scanResult.progressDetail.trimmingStatus === 1;
      } else if (progressType === 'shipping') {
        isCompleted = scanResult.progressDetail.shippingStatus === 1;
      } else if (progressType === 'packaging') {
        isCompleted = scanResult.progressDetail.packagingStatus === 1;
      } else if (progressType === 'inspection') {
        isCompleted = scanResult.progressDetail.inspectionStatus === 1;
      }
      
      if (isCompleted) {
        console.log('工序已完成，不能选择:', progressType);
        wx.showToast({
          title: '该工序已完成',
          icon: 'none'
        });
        return;
      }
    }
    
    // 检查用户是否有权限操作该工序
    const hasPermission = userRoleManager.checkUserProgressPermission(page, progressType);
    if (!hasPermission) {
      console.log('用户没有权限操作此工序:', progressType);
      wx.showToast({
        title: '您没有权限操作此工序',
        icon: 'none'
      });
      return;
    }
    
    // 直接创建一个全新的数组，避免使用splice()或push()等操作原数组的方法
    let newSelectedTypes;
    if (selectedProgressTypes.includes(progressType)) {
      console.log('取消选中工序:', progressType);
      // 过滤数组创建新数组，不使用splice
      newSelectedTypes = selectedProgressTypes.filter(type => type !== progressType);
    } else {
      console.log('选中工序:', progressType);
      // 创建新数组，不使用push
      newSelectedTypes = [...selectedProgressTypes, progressType];
    }
    
    console.log('新的选中工序列表:', newSelectedTypes);
    
    // 更新工序选择状态
    const isSelected = !selectedProgressTypes.includes(progressType);
    
    // 根据不同工序类型更新不同的强制刷新变量
    const updateData = {
      selectedProgressTypes: newSelectedTypes,
      pageVersion: Date.now()
    };
    
    // 为每种工序添加特定的强制刷新变量
    if (progressType === 'cutting') {
      updateData.forceBurningUpdate = isSelected ? 1 : 0;
    } else if (progressType === 'workshop') {
      updateData.forceSewingUpdate = isSelected ? 1 : 0;
    } else if (progressType === 'trimming') {
      updateData.forceTailUpdate = isSelected ? 1 : 0;
    } else if (progressType === 'shipping') {
      updateData.forceShippingUpdate = isSelected ? 1 : 0;
    } else if (progressType === 'packaging') {
      updateData.forceDeliveryUpdate = isSelected ? 1 : 0;
    } else if (progressType === 'inspection') {
      updateData.forceInspectionUpdate = isSelected ? 1 : 0;
    }
    
    // 直接设置新数组，不使用任何回调或其他更新
    page.setData(updateData);
    
    // 显示消息提示用户操作成功
    const nowSelected = page.data.selectedProgressTypes.includes(progressType);
    wx.showToast({
      title: (nowSelected ? '已选择' : '已取消选择') + (progressName ? ` ${progressName}` : ''),
      icon: 'none',
      duration: 500
    });
    
    // 不立即调用updateProgressItems避免递归更新
    setTimeout(() => {
      console.log('延迟更新工序项目列表');
      page.updateProgressItems();
      
      // 强制刷新确保视图更新
      page.setData({
        pageVersion: Date.now() + 1
      });
      
      // 手动更新复选框状态
      const checkboxId = `progress-checkbox-${progressType}`;
      const checkbox = page.selectComponent(`#${checkboxId}`);
      if (checkbox) {
        const isChecked = newSelectedTypes.includes(progressType);
        console.log('更新复选框状态:', checkboxId, isChecked);
        checkbox.setData({ isChecked });
      }
      
      console.log('工序选择处理完成');
    }, 50);
  } catch (error) {
    // 错误捕获处理
    console.error('选择工序时发生错误:', error);
    wx.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    });
  }
}

/**
 * 提交选中的工序
 */
function submitSelectedProgress(page) {
  const { selectedProgressTypes, scanResult } = page.data;
  
  // 防重复提交
  if (page.data.processing) {
    console.log('正在处理中，防止重复提交');
    return;
  }
  
  // 检查是否选择了工序
  if (!selectedProgressTypes || selectedProgressTypes.length === 0) {
    console.log('未选择任何工序');
    wx.showToast({
      title: '请至少选择一个工序',
      icon: 'none'
    });
    return;
  }
  
  // 检查扫码数据完整性
  if (!scanResult || !scanResult.subPurchaseOrderSn || !scanResult.shopId) {
    console.log('扫码数据不完整:', scanResult);
    wx.showToast({
      title: '扫码数据不完整',
      icon: 'none'
    });
    return;
  }
  
  console.log('提交选中的工序:', selectedProgressTypes, '备货单号:', scanResult.subPurchaseOrderSn, '店铺ID:', scanResult.shopId);
  
  // 弹窗确认
  wx.showModal({
    title: '确认提交',
    content: `您选择了 ${selectedProgressTypes.length} 个工序，确认要提交完成吗？`,
    confirmText: '确认提交',
    cancelText: '再想想',
    success: (res) => {
      if (res.confirm) {
        console.log('用户确认提交');
        // 更新工序状态
        updateProgressData(page, selectedProgressTypes);
      } else {
        console.log('用户取消提交');
      }
    }
  });
}

/**
 * 更新进度数据
 */
function updateProgressData(page, progressTypes) {
  if (!progressTypes || progressTypes.length === 0) {
    console.error('未选择工序:', progressTypes);
    wx.showToast({
      title: '未选择工序',
      icon: 'none'
    });
    return;
  }
  
  const { scanResult } = page.data;
  
  console.log('准备更新工序, 数量:', progressTypes.length, '工序列表:', progressTypes, {
    shopId: scanResult.shopId,
    subPurchaseOrderSn: scanResult.subPurchaseOrderSn
  });
  
  // 设置处理中状态并显示加载提示
  page.setData({ processing: true });
  wx.showLoading({ title: '提交中...' });
  
  // 跟踪成功和失败的工序
  let successCount = 0;
  let failedTypes = [];
  
  // 创建请求队列
  const requests = progressTypes.map(progressType => {
    // 准备请求参数
    const params = {
      shopId: scanResult.shopId,
      subPurchaseOrderSn: scanResult.subPurchaseOrderSn,
      progressType: progressType,
      operationType: "1", // 扫码确认完成工序，固定为"1"
      remarks: "" // 备注字段，可以为空字符串
    };
    
    console.log('创建更新请求, 工序:', progressType, params);
    
    // 返回请求Promise
    return productionProgressApi.updateProgress(params)
      .then(res => {
        console.log('工序更新成功:', progressType, res);
        successCount++;
        return { type: progressType, success: true, response: res };
      })
      .catch(err => {
        console.error('工序更新失败:', progressType, err);
        failedTypes.push(progressType);
        return { type: progressType, success: false, error: err };
      });
  });
  
  // 执行所有请求
  Promise.all(requests)
    .then(results => {
      console.log('所有工序更新请求完成, 结果数量:', results.length, '结果:', results);
      
      if (failedTypes.length === 0) {
        // 全部成功
        wx.showModal({
          title: '处理成功',
          content: `已完成工序：${progressTypes.map(type => page.data.roleProgressMap[type] || type).join('、')}`,
          showCancel: false,
          confirmText: '确定',
          confirmColor: '#1890ff',
        });
      } else if (successCount > 0) {
        // 部分成功
        // 获取成功和失败的工序名称
        const successNames = results.filter(r => r.success).map(r => page.data.roleProgressMap[r.type] || r.type);
        const failedNames = results.filter(r => !r.success).map(r => page.data.roleProgressMap[r.type] || r.type);
        
        wx.showModal({
          title: '部分更新成功',
          content: `成功工序：${successNames.join('、')}\n\n失败工序：${failedNames.join('、')}`,
          showCancel: false,
          confirmText: '确定',
          confirmColor: '#1890ff',
          success: () => {
            // 对话框关闭后重置扫码界面
            page.resetScannerState();
          }
        });
      } else {
        // 全部失败
        // 获取所有失败的工序名称
        const failedNames = failedTypes.map(type => page.data.roleProgressMap[type] || type);
        
        wx.showModal({
          title: '更新失败',
          content: `更新失败工序：${failedNames.join('、')}`,
          showCancel: false,
          confirmText: '确定',
          confirmColor: '#1890ff',
          success: () => {
            // 对话框关闭后重置扫码界面
            page.resetScannerState();
          }
        });
      }
      
      // 清空选中状态，不管成功与否都清空
      page.setData({
        selectedProgressTypes: []
      });
      
      // 重新加载进度信息
      refreshProgressDetail(page, scanResult.shopId, scanResult.subPurchaseOrderSn);
    })
    .catch(err => {
      console.error('工序更新过程中发生未处理错误:', err);
      wx.showModal({
        title: '处理失败',
        content: err.message || '无法更新生产进度，请重试',
        showCancel: false
      });
    })
    .finally(() => {
      page.setData({ processing: false });
      wx.hideLoading();
    });
}

/**
 * 刷新进度详情
 */
function refreshProgressDetail(page, shopId, subPurchaseOrderSn) {
  console.log('刷新进度详情');
  
  productionProgressApi.getProgressDetail(shopId, subPurchaseOrderSn)
    .then(res => {
      console.log('刷新获取到的进度信息:', res);
      
      // 更新进度信息
      page.setData({
        'scanResult.progressDetail': res
      });
    })
    .catch(err => {
      console.error('刷新进度信息失败:', err);
    });
}

module.exports = {
  handleScanResult,
  canEditProgress,
  toggleSelectProgress,
  submitSelectedProgress,
  updateProgressData,
  refreshProgressDetail
}; 