/**
 * 用户相关API
 */

const { request } = require('./apiUtils');
const { getBaseUrl } = require('./config');

// 获取API基础URL
const BASE_URL = getBaseUrl();

/**
 * 用户登录
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @param {boolean} rememberMe - 是否记住登录状态
 * @returns {Promise} 登录结果
 */
const login = (username, password, rememberMe = false) => {
  return request(`${BASE_URL}/auth/login`, {
    username,
    password,
    rememberMe
  });
};

/**
 * 获取用户信息
 * @returns {Promise} 用户信息
 */
const getUserInfo = () => {
  return request(`${BASE_URL}/user/info`, {});
};

/**
 * 退出登录
 * @returns {Promise} 退出结果
 */
const logout = () => {
  return request(`${BASE_URL}/user/logout`, {});
};

module.exports = {
  login,
  getUserInfo,
  logout
};