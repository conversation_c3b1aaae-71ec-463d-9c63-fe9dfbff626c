<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.shop.mapper.ShopMapper">

    <resultMap id="ShopResult" type="com.xiao.temu.modules.shop.entity.Shop">
        <id property="shopId" column="shop_id"/>
        <result property="shopName" column="shop_name"/>
        <result property="shopTemuId" column="shop_temu_id"/>
        <result property="apiKey" column="api_key"/>
        <result property="apiSecret" column="api_secret"/>
        <result property="accessToken" column="access_token"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="belongGroupId" column="belong_group_id"/>
    </resultMap>
    
    <resultMap id="ShopDTOResult" type="com.xiao.temu.modules.shop.dto.ShopDTO">
        <id property="shopId" column="shop_id"/>
        <result property="shopName" column="shop_name"/>
        <result property="shopTemuId" column="shop_temu_id"/>
        <result property="apiKey" column="api_key"/>
        <result property="apiSecret" column="api_secret"/>
        <result property="accessToken" column="access_token"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="belongGroupId" column="belong_group_id"/>
        <collection property="groups" ofType="com.xiao.temu.modules.operation.dto.OperationGroupDTO" select="selectGroupsByShopId" column="shop_id"/>
    </resultMap>
    
    <sql id="selectShopSql">
        select s.shop_id, s.shop_name, s.shop_temu_id, s.api_key, s.api_secret, 
               s.access_token, s.status, s.create_time, s.update_time, s.remark,
               s.create_by, s.belong_group_id
        from shop s
    </sql>
    
    <select id="selectShopList" resultMap="ShopDTOResult">
        SELECT DISTINCT
        s.shop_id, s.shop_name, s.shop_temu_id, s.api_key, s.api_secret, 
        s.access_token, s.status, s.create_time, s.update_time, s.remark,
        s.create_by, s.belong_group_id
        FROM shop s
        <if test="query.data_scope != null and query.data_scope != ''">
            INNER JOIN shop_group_assignment sga ON s.shop_id = sga.shop_id
        </if>
        <where>
            <if test="query.shopName != null and query.shopName != ''">
                AND s.shop_name like concat('%', #{query.shopName}, '%')
            </if>
            <if test="query.shopTemuId != null and query.shopTemuId != ''">
                AND s.shop_temu_id like concat('%', #{query.shopTemuId}, '%')
            </if>
            <if test="query.groupId != null">
                AND EXISTS (
                    SELECT 1 FROM shop_group_assignment sga 
                    WHERE sga.shop_id = s.shop_id 
                    AND sga.group_id = #{query.groupId} 
                    AND sga.status = '0'
                )
            </if>
            <if test="query.status != null and query.status != ''">
                AND s.status = #{query.status}
            </if>
            <if test="query.data_scope != null and query.data_scope != ''">
                AND ${query.data_scope}
            </if>
        </where>
        order by 
        <choose>
            <when test="query.orderByColumn != null and query.orderByColumn != ''">
                ${query.orderByColumn} 
                <if test="query.orderDirection != null and query.orderDirection != ''">
                    ${query.orderDirection}
                </if>
            </when>
            <otherwise>
                s.create_time desc
            </otherwise>
        </choose>
    </select>
    
    <select id="selectShopById" parameterType="Long" resultMap="ShopDTOResult">
        <include refid="selectShopSql"/>
        where s.shop_id = #{shopId}
    </select>
    
    <select id="selectShopsByUserId" parameterType="Long" resultMap="ShopResult">
        select distinct s.* 
        from shop s
        left join group_leader_shop_assignment a on s.shop_id = a.shop_id
        left join operation_group og on og.leader_id = #{userId}
        left join shop_group_assignment sga on s.shop_id = sga.shop_id and og.group_id = sga.group_id
        where (
            a.user_id = #{userId} and a.status = '0'
            or (og.leader_id = #{userId} and og.status = '0' and sga.status = '0')
            or s.create_by = #{userId}  <!-- 获取用户创建的店铺 -->
            or (s.belong_group_id is not null and exists (
                select 1 from operation_group og2 
                inner join group_member gm on og2.group_id = gm.group_id
                where og2.group_id = s.belong_group_id and gm.user_id = #{userId} and gm.status = '0'
            ))  <!-- 获取用户所在运营组创建的店铺 -->
        )
        and s.status = '0'
    </select>
    
    <select id="selectShopsByGroupId" parameterType="Long" resultMap="ShopResult">
        select distinct s.* 
        from shop s
        inner join shop_group_assignment sga on s.shop_id = sga.shop_id
        where sga.group_id = #{groupId}
        and sga.status = '0'
        and s.status = '0'
        and (s.belong_group_id is null or s.belong_group_id = #{groupId})
    </select>
    
    <select id="selectAllShopsByGroupId" parameterType="Long" resultMap="ShopResult">
        select distinct s.* 
        from shop s
        inner join shop_group_assignment sga on s.shop_id = sga.shop_id
        where sga.group_id = #{groupId}
        and sga.status = '0'
        and s.status = '0'
    </select>
    
    <select id="selectGroupsByShopId" resultType="com.xiao.temu.modules.operation.dto.OperationGroupDTO">
        select og.group_id, og.group_name, og.status
        from shop_group_assignment sga
        inner join operation_group og on sga.group_id = og.group_id
        where sga.shop_id = #{shopId} and sga.status = '0' and og.status = '0'
        union
        select og.group_id, og.group_name, og.status
        from operation_group og
        where og.group_id = (select belong_group_id from shop where shop_id = #{shopId})
        and og.status = '0'
    </select>
    
    <select id="selectGroupIdsByShopId" resultType="java.lang.Long">
        select sga.group_id
        from shop_group_assignment sga
        where sga.shop_id = #{shopId}
        and sga.status = '0'
        union
        select belong_group_id 
        from shop 
        where shop_id = #{shopId} 
        and belong_group_id is not null
    </select>
    
    <select id="checkShopInGroup" resultType="java.lang.Boolean">
        select count(1) > 0
        from (
            select 1
            from shop_group_assignment sga
            where sga.shop_id = #{shopId}
            and sga.group_id = #{groupId}
            and sga.status = '0'
            union all
            select 1
            from shop s
            where s.shop_id = #{shopId}
            and s.belong_group_id = #{groupId}
        ) t
    </select>
    
    <!-- 根据多个运营组ID查询店铺列表 -->
    <select id="selectShopsByGroupIds" resultType="com.xiao.temu.modules.shop.entity.Shop">
        SELECT DISTINCT s.*
        FROM shop s
        LEFT JOIN shop_group_assignment sga ON s.shop_id = sga.shop_id
        WHERE (sga.group_id IN
        <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
            #{groupId}
        </foreach>
        AND sga.status = '0'
        AND s.status = '0'
        )
        OR (s.belong_group_id IN
        <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
            #{groupId}
        </foreach>
        AND s.status = '0'
        )
        ORDER BY s.create_time DESC
    </select>
    
    <!-- 查询所有启用状态的店铺 -->
    <select id="selectAllEnabledShops" resultMap="ShopResult">
        <include refid="selectShopSql" />
        where s.status = '0'
        order by s.create_time desc
    </select>
    
    <!-- 更新店铺所属运营组ID -->
    <update id="updateBelongGroupId">
        update shop
        set belong_group_id = #{groupId},
            update_time = now()
        where shop_id = #{shopId}
    </update>
    
</mapper> 