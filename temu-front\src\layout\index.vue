<template>
  <div class="app-wrapper" :class="{ 
    mobile: device === 'mobile', 
    'sidebar-collapsed': !appStore.sidebar.opened 
  }">
    <!-- 侧边栏 -->
    <sidebar class="sidebar-container" />
    
    <div class="main-container">
      <!-- 顶部导航栏 -->
      <navbar />
      
      <!-- 主要内容区域 -->
      <app-main />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onBeforeUnmount } from 'vue'
import { useAppStore } from '@/store/modules/app'
import Sidebar from './components/Sidebar/index.vue'
import Navbar from './components/Navbar.vue'
import AppMain from './components/AppMain.vue'

const appStore = useAppStore()
const device = computed(() => appStore.device)

// 监听窗口大小变化
const handleResize = () => {
  const width = document.documentElement.clientWidth
  if (width < 992) {
    appStore.toggleDevice('mobile')
    appStore.closeSidebar(true)
  } else {
    appStore.toggleDevice('desktop')
  }
}

onMounted(() => {
  handleResize()
  window.addEventListener('resize', handleResize)
  // 确保标签视图功能开启
  appStore.setTagsView(true)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  
  &.mobile {
    .sidebar-container {
      transition: transform .28s;
      width: 210px !important;
    }
    
    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-210px, 0, 0);
      }
    }
  }

  &.sidebar-collapsed {
    .sidebar-container {
      width: 54px !important;
    }

    .main-container {
      margin-left: 54px;
      width: calc(100% - 54px);
    }
  }
}

.sidebar-container {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  width: 210px;
  overflow: hidden;
  background-color: #22a699;
  transition: width 0.28s;
  z-index: 1001;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.main-container {
  min-height: 100%;
  margin-left: 210px;
  padding: 0;
  position: relative;
  transition: all 0.28s;
  background-color: #f0f2f5;
  width: calc(100% - 210px);
}
</style>