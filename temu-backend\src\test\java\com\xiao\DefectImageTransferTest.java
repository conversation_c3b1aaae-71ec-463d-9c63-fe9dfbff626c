package com.xiao;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.modules.quality.service.DefectImageService;
import com.xiao.temu.modules.quality.service.impl.DefectImageServiceImpl;
import com.xiao.temu.infrastructure.storage.CosStorageService;
import com.xiao.temu.infrastructure.api.TemuApiClient;
import org.ehcache.shadow.org.terracotta.statistics.Time;

import java.io.File;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 疵点图片转存测试类
 */
public class DefectImageTransferTest {

    // 腾讯云COS配置参数
    private static final String SECRET_ID = "AKIDxpY4MRIBCtNyNfnkX7UdZLxQgpnbGk14";
    private static final String SECRET_KEY = "tZ6RZTCj3A7tSnwyqU2ThldHk3B5QXR8";
    private static final String REGION = "ap-guangzhou";
    private static final String BUCKET_NAME = "temu-api-1302872665";

    // API参数
    private static final String ACCESS_TOKEN = "d37jp01jhfkkkidjqrzdocg9yyl7g7rwkqtnu48qxjomgwe2ntzl81ii";
    private static final String APP_KEY = "2715ae152d7baa483dab21ead39ac222";
    private static final String APP_SECRET = "9d5c08e4f6e66f538c5c8555cab3d60e5d0fef72";

    // 测试参数
    private static final Long SHOP_ID = 1L;
    private static final Long QC_BILL_ID = 1528954283232L;
    private static final Long PRODUCT_SKU_ID = 47324132566016L; // 根据实际情况修改

    public static void main(String[] args) throws Exception {
        // 测试单个指定URL下载
//        testSingleUrlDownload();
        
        // 测试质检详情并转存所有疵点图片
        testQualityInspectionDetailImageTransfer();
    }

    /**
     * 测试单个指定URL的下载和上传
     */
    private static void testSingleUrlDownload() throws Exception {
        System.out.println("======= 测试单个指定URL下载 =======");
        
        // 创建COS存储服务
        CosStorageService cosStorageService = createCosStorageService();
        
        // 获取一个URL进行测试
        String testImageUrl = "https://kjpfs-cn.kuajingmaihuo.com/supplier-private-tag/201a0da915d/6e77a9b2-0937-4e69-b938-801be9f07fb6_624x406.jpeg?sign=q-sign-algorithm%3Dsha1%26q-ak%3Dj6ZRbuaJnssb1CMPNfZGj2mXRsHhcukP%26q-sign-time%3D1743128110%3B1743129910%26q-key-time%3D1743128110%3B1743129910%26q-header-list%3D%26q-url-param-list%3D%26q-signature%3D680c08569d42025dc2aae351d9b7f415840c8234";
        System.out.println("测试图片URL: " + testImageUrl);
        
        // 测试下载图片
        System.out.println("开始下载图片...");
        File downloadedFile = cosStorageService.downloadImage(testImageUrl);
        
        if (downloadedFile != null && downloadedFile.exists()) {
            System.out.println("图片下载成功: " + downloadedFile.getAbsolutePath());
            System.out.println("文件大小: " + downloadedFile.length() + " 字节");
            
            // 测试上传图片
            System.out.println("开始上传图片...");
            String key = "test-defect-images/" + UUID.randomUUID() + ".jpg";
            boolean uploadSuccess = cosStorageService.uploadImage(downloadedFile, key);
            
            if (uploadSuccess) {
                System.out.println("图片上传成功: " + key);
                
                // 生成访问URL
                String imageUrl = cosStorageService.generateImageUrl(key, 1);
                System.out.println("图片访问URL: " + imageUrl);
            } else {
                System.out.println("图片上传失败");
            }
            
            // 删除临时文件
            boolean deleteSuccess = downloadedFile.delete();
            System.out.println("临时文件删除" + (deleteSuccess ? "成功" : "失败"));
        } else {
            System.out.println("图片下载失败");
        }
        
        System.out.println("======= 单个URL测试完成 =======\n");
    }
    
    /**
     * 测试获取质检详情并转存疵点图片
     */
    private static void testQualityInspectionDetailImageTransfer() throws Exception {
        System.out.println("======= 测试质检详情疵点图片转存 =======");
        
        // 创建存储和转存服务
        CosStorageService cosStorageService = createCosStorageService();
        DefectImageServiceImpl defectImageService = new DefectImageServiceImpl();
        injectCosStorageService(defectImageService, cosStorageService);
        
        // 1. 调用API获取质检详情
        JSONObject response = getQualityInspectionDetail();
        if (response == null || !response.getBooleanValue("success")) {
            System.out.println("获取质检详情失败: " + (response != null ? response.toJSONString() : "null"));
            return;
        }
        
        // 2. 提取疵点图片附件
        JSONArray attachments = extractAttachmentsFromResponse(response);
        if (attachments == null || attachments.isEmpty()) {
            System.out.println("未找到疵点图片附件");
            return;
        }
        
        System.out.println("找到 " + attachments.size() + " 个疵点图片附件");
        printAttachments(attachments);
        
        // 3. 保持原始URL格式，不要移除签名参数
        JSONArray testAttachments = new JSONArray();
        for (int i = 0; i < attachments.size(); i++) {
            try {
                // 获取完整URL，保留所有参数
                String url = attachments.getString(i);
                if (url != null && !url.isEmpty()) {
                    // 创建标准格式的附件对象
                    JSONObject attachment = new JSONObject();
                    attachment.put("url", url);
                    testAttachments.add(attachment);
                    System.out.println("处理后的URL对象: " + attachment.toJSONString());
                }
            } catch (Exception e) {
                System.out.println("处理URL异常: " + e.getMessage());
            }
        }
        
        // 如果没有提取到有效的URL，可以使用单个URL测试
        if (testAttachments.isEmpty()) {
            System.out.println("从API中未提取到有效URL，使用测试URL");
            String testUrl = "https://kjpfs-cn.kuajingmaihuo.com/supplier-private-tag/201a0da915d/6e77a9b2-0937-4e69-b938-801be9f07fb6_624x406.jpeg?sign=q-sign-algorithm%3Dsha1%26q-ak%3Dj6ZRbuaJnssb1CMPNfZGj2mXRsHhcukP%26q-sign-time%3D1743128110%3B1743129910%26q-key-time%3D1743128110%3B1743129910%26q-header-list%3D%26q-url-param-list%3D%26q-signature%3D680c08569d42025dc2aae351d9b7f415840c8234";
            JSONObject testAttachment = new JSONObject();
            testAttachment.put("url", testUrl);
            testAttachments.add(testAttachment);
        }
        
        // 4. 转存疵点图片
        System.out.println("开始转存疵点图片...");
        JSONArray processedAttachments = defectImageService.transferDefectImages(
                SHOP_ID, QC_BILL_ID, PRODUCT_SKU_ID, testAttachments);
        
        System.out.println("转存完成，转存后的附件数据: ");
        printAttachments(processedAttachments);
        
        // 5. 验证imageKey字段和对应的URL
        validateTransferredImages(processedAttachments, defectImageService);
        
        System.out.println("======= 质检详情疵点图片转存测试完成 =======");
    }
    
    /**
     * 创建COS存储服务
     */
    private static CosStorageService createCosStorageService() throws Exception {
        CosStorageService service = new CosStorageService();
        
        // 使用反射设置私有属性
        Field secretIdField = CosStorageService.class.getDeclaredField("secretId");
        secretIdField.setAccessible(true);
        secretIdField.set(service, SECRET_ID);
        
        Field secretKeyField = CosStorageService.class.getDeclaredField("secretKey");
        secretKeyField.setAccessible(true);
        secretKeyField.set(service, SECRET_KEY);
        
        Field regionField = CosStorageService.class.getDeclaredField("region");
        regionField.setAccessible(true);
        regionField.set(service, REGION);
        
        Field bucketNameField = CosStorageService.class.getDeclaredField("bucketName");
        bucketNameField.setAccessible(true);
        bucketNameField.set(service, BUCKET_NAME);
        
        return service;
    }
    
    /**
     * 注入COS存储服务到疵点图片服务
     */
    private static void injectCosStorageService(DefectImageServiceImpl defectImageService, 
            CosStorageService cosStorageService) throws Exception {
        Field field = DefectImageServiceImpl.class.getDeclaredField("cosStorageService");
        field.setAccessible(true);
        field.set(defectImageService, cosStorageService);
    }
    
    /**
     * 获取质检详情数据
     */
    private static JSONObject getQualityInspectionDetail() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken(ACCESS_TOKEN);
        commonParams.setType("bg.goods.qualityinspectiondetail.get");
        commonParams.setAppKey(APP_KEY);
        commonParams.setAppSecret(APP_SECRET);

        HashMap<String, Object> businessParams = new HashMap<>();
        businessParams.put("qcBillId", QC_BILL_ID.toString());
        businessParams.put("timestamp", String.valueOf(Time.time()));
        
        return TemuApiClient.sendRequest(commonParams, businessParams);
    }
    
    /**
     * 从响应中提取疵点图片附件
     */
    private static JSONArray extractAttachmentsFromResponse(JSONObject response) {
        JSONObject result = response.getJSONObject("result");
        if (result == null) {
            return null;
        }
        
        JSONArray historyVOS = result.getJSONArray("historyVOS");
        if (historyVOS == null || historyVOS.isEmpty()) {
            return null;
        }
        
        JSONObject firstHistory = historyVOS.getJSONObject(0);
        if (firstHistory == null) {
            return null;
        }
        
        JSONObject qcDetail = firstHistory.getJSONObject("qcDetail");
        if (qcDetail == null) {
            return null;
        }
        
        JSONArray flawDTOList = qcDetail.getJSONArray("flawDTOList");
        if (flawDTOList == null || flawDTOList.isEmpty()) {
            return null;
        }
        
        // 获取第一个缺陷的附件
        for (int i = 0; i < flawDTOList.size(); i++) {
            JSONObject flaw = flawDTOList.getJSONObject(i);
            if (flaw == null) continue;
            
            JSONArray attachments = flaw.getJSONArray("attachments");
            if (attachments != null && !attachments.isEmpty()) {
                return attachments;
            }
        }
        
        return null;
    }
    
    /**
     * 打印附件信息
     */
    private static void printAttachments(JSONArray attachments) {
        if (attachments == null || attachments.isEmpty()) {
            System.out.println("附件为空");
            return;
        }
        
        for (int i = 0; i < attachments.size(); i++) {
            try {
                // 尝试获取为JSONObject
                JSONObject attachment = attachments.getJSONObject(i);
                System.out.println("附件 #" + (i + 1) + " (对象): " + attachment.toJSONString());
            } catch (Exception e) {
                // 如果失败，则尝试获取为String
                try {
                    String url = attachments.getString(i);
                    System.out.println("附件 #" + (i + 1) + " (URL): " + url);
                } catch (Exception e2) {
                    System.out.println("附件 #" + (i + 1) + " (未知类型): " + attachments.get(i));
                }
            }
        }
    }
    
    /**
     * 验证转存后的图片
     */
    private static void validateTransferredImages(JSONArray attachments, DefectImageService defectImageService) {
        if (attachments == null || attachments.isEmpty()) {
            System.out.println("没有附件可验证");
            return;
        }
        
        boolean allValid = true;
        for (int i = 0; i < attachments.size(); i++) {
            JSONObject attachment = attachments.getJSONObject(i);
            String imageKey = attachment.getString("imageKey");
            
            if (imageKey == null || imageKey.isEmpty()) {
                System.out.println("附件 #" + (i + 1) + " 没有imageKey字段");
                allValid = false;
                continue;
            }
            
            String imageUrl = defectImageService.getDefectImageUrl(imageKey);
            if (imageUrl == null || imageUrl.isEmpty()) {
                System.out.println("附件 #" + (i + 1) + " 的imageKey无法生成URL: " + imageKey);
                allValid = false;
                continue;
            }
            
            System.out.println("附件 #" + (i + 1) + " 的转存图片URL: " + imageUrl);
        }
        
        System.out.println("验证结果: " + (allValid ? "所有图片转存成功" : "部分图片转存失败"));
    }
    
    /**
     * 测试异步转存单个图片
     */
    private static void testAsyncTransferImage(DefectImageServiceImpl defectImageService, String imageUrl) throws Exception {
        System.out.println("测试异步转存图片: " + imageUrl);
        
        CompletableFuture<String> future = defectImageService.transferImageAsync(
                SHOP_ID, QC_BILL_ID, PRODUCT_SKU_ID, imageUrl);
        
        String imageKey = future.get();
        if (imageKey != null) {
            System.out.println("图片异步转存成功，imageKey: " + imageKey);
            String accessUrl = defectImageService.getDefectImageUrl(imageKey);
            System.out.println("转存后的图片访问URL: " + accessUrl);
        } else {
            System.out.println("图片异步转存失败");
        }
    }
} 