package com.xiao.temu.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.system.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 用户Mapper接口
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    SysUser selectUserByUsername(@Param("username") String username);
    
    /**
     * 检查用户名是否唯一
     *
     * @param username 用户名
     * @return 结果
     */
    int checkUsernameUnique(@Param("username") String username);

    /**
     * 更新用户密码
     *
     * @param userId   用户ID
     * @param password 密码
     * @return 结果
     */
    int updatePassword(@Param("userId") Long userId, @Param("password") String password);

    /**
     * 更新用户状态
     *
     * @param userId 用户ID
     * @param status 状态
     * @return 结果
     */
    int updateStatus(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 根据角色ID查询用户ID列表
     *
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    List<Long> selectUserIdsByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 查询具有生产角色的用户
     *
     * @param page 分页参数
     * @param roleKeys 角色标识列表
     * @param excludeUserIds 排除的用户ID列表
     * @param keyword 搜索关键词
     * @return 用户分页列表
     */
    IPage<SysUser> selectProductionRoleUsers(
            Page<SysUser> page, 
            @Param("roleKeys") List<String> roleKeys, 
            @Param("excludeUserIds") List<Long> excludeUserIds, 
            @Param("keyword") String keyword);
} 