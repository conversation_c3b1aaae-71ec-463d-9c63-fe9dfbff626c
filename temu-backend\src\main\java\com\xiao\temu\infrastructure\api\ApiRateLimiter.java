package com.xiao.temu.infrastructure.api;

import com.alibaba.fastjson2.JSONObject;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.config.ratelimit.SalesSyncRateLimitConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Supplier;

/**
 * API请求限流工具类
 * 实现API请求速率限制、重试机制和退避策略
 */
@Slf4j
@Component
public class ApiRateLimiter {

    @Autowired
    private SalesSyncRateLimitConfig rateLimitConfig;
    
    // 用于记录每个店铺最后一次请求的时间戳
    private final ConcurrentHashMap<Long, AtomicLong> shopLastRequestTimeMap = new ConcurrentHashMap<>();
    
    // 用于记录每个店铺的连续失败次数，用于动态调整请求间隔
    private final ConcurrentHashMap<Long, AtomicLong> shopFailureCountMap = new ConcurrentHashMap<>();
    
    // 默认的通用请求时间间隔（毫秒）
    private static final long DEFAULT_INTERVAL_MS = 500;
    
    // 最后一次通用API请求的时间戳
    private final AtomicLong lastCommonRequestTime = new AtomicLong(System.currentTimeMillis());
    
    /**
     * 通用的限流方法，适用于不需要指定店铺ID的API请求
     * 使用默认间隔时间进行限流
     */
    public void acquire() {
        long currentTime = System.currentTimeMillis();
        long lastTime = lastCommonRequestTime.get();
        long timeDiff = currentTime - lastTime;
        
        // 使用默认请求间隔
        long requestInterval = DEFAULT_INTERVAL_MS;
        
        // 如果距离上次请求时间小于请求间隔，则等待
        if (timeDiff < requestInterval) {
            long waitTime = requestInterval - timeDiff;
            try {
                if (waitTime > 50) { // 只记录较长的等待时间，避免日志过多
                    log.debug("通用API限流等待 {}ms", waitTime);
                }
                TimeUnit.MILLISECONDS.sleep(waitTime);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("通用API限流等待被中断");
            }
        }
        
        // 更新最后请求时间
        lastCommonRequestTime.set(System.currentTimeMillis());
    }
    
    /**
     * 执行API请求，带速率限制和重试机制
     * 
     * @param shopId 店铺ID
     * @param apiSupplier API请求的函数式接口
     * @param description 请求描述，用于日志记录
     * @return API请求结果
     * @throws RuntimeException 如果所有重试都失败
     */
    public JSONObject executeWithRateLimit(Long shopId, Supplier<JSONObject> apiSupplier, String description) {
        int maxRetries = rateLimitConfig.getMaxRetryTimes();
        int retryCount = 0;
        Exception lastException = null;
        
        // 在首次调用前强制等待，确保请求间隔
        waitForRateLimit(shopId);
        
        while (retryCount <= maxRetries) {
            try {
                // 执行API请求
                JSONObject response = apiSupplier.get();
                
                // 记录本次请求时间
                recordRequestTime(shopId);
                
                // 检查响应是否成功
                if (response != null && response.getBooleanValue("success")) {
                    // 请求成功，重置失败计数
                    resetFailureCount(shopId);
                    return response;
                } else {
                    // API返回错误
                    String errorMsg = response != null ? response.getString("errorMsg") : "API响应为空";
                    
                    // 检查是否是限流错误
                    if (errorMsg != null && errorMsg.contains("请求过于频繁")) {
                        // 增加失败计数，用于动态调整请求间隔
                        incrementFailureCount(shopId);
                        log.warn("店铺 {} API请求触发限流，将进行第 {}/{} 次重试，请求描述: {}", 
                                shopId, retryCount + 1, maxRetries, description);
                    } else {
                        // 其他错误可能不需要重试
                        log.error("店铺 {} API请求失败，错误信息: {}, 请求描述: {}", 
                                shopId, errorMsg, description);
                        throw new RuntimeException("API调用失败: " + errorMsg);
                    }
                }
            } catch (Exception e) {
                lastException = e;
                log.error("店铺 {} API请求异常，将进行第 {}/{} 次重试，异常信息: {}, 请求描述: {}", 
                        shopId, retryCount + 1, maxRetries, e.getMessage(), description);
            }
            
            retryCount++;
            
            // 如果还有重试次数，则等待后重试
            if (retryCount <= maxRetries) {
                // 计算动态退避时间
                long waitTime = calculateWaitTime(shopId, retryCount);
                try {
                    log.info("店铺 {} 等待 {}ms 后进行第 {} 次重试，请求描述: {}", 
                            shopId, waitTime, retryCount, description);
                    TimeUnit.MILLISECONDS.sleep(waitTime);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试等待被中断", ie);
                }
            }
        }
        
        // 所有重试都失败
        String errorMessage = lastException != null ? 
                lastException.getMessage() : "超过最大重试次数，API请求失败";
        throw new RuntimeException(errorMessage, lastException);
    }
    
    /**
     * 使用TemuApiClient发送请求，带速率限制和重试
     * 
     * @param shopId 店铺ID
     * @param commonParams 公共参数
     * @param businessParams 业务参数
     * @param description 请求描述
     * @return API请求结果
     */
    public JSONObject sendRequestWithRateLimit(Long shopId, CommonParams commonParams, 
            Map<String, Object> businessParams, String description) {
        return executeWithRateLimit(shopId, 
                () -> TemuApiClient.sendRequest(commonParams, businessParams),
                description);
    }
    
    /**
     * 等待请求间隔，实现速率限制
     * 
     * @param shopId 店铺ID
     */
    private void waitForRateLimit(Long shopId) {
        AtomicLong lastRequestTime = shopLastRequestTimeMap.computeIfAbsent(shopId, 
                k -> new AtomicLong(System.currentTimeMillis() - rateLimitConfig.getRequestIntervalMs()));
        
        long currentTime = System.currentTimeMillis();
        long lastTime = lastRequestTime.get();
        long timeDiff = currentTime - lastTime;
        
        // 获取当前应该使用的请求间隔时间
        long requestInterval = getRequestInterval(shopId);
        
        // 如果距离上次请求时间小于请求间隔，则等待
        if (timeDiff < requestInterval) {
            long waitTime = requestInterval - timeDiff;
            try {
                if (waitTime > 50) { // 只记录较长的等待时间，避免日志过多
                    log.debug("店铺 {} 限流等待 {}ms", shopId, waitTime);
                }
                TimeUnit.MILLISECONDS.sleep(waitTime);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("店铺 {} 限流等待被中断", shopId);
            }
        }
    }
    
    /**
     * 记录本次请求时间
     * 
     * @param shopId 店铺ID
     */
    private void recordRequestTime(Long shopId) {
        AtomicLong lastRequestTime = shopLastRequestTimeMap.computeIfAbsent(shopId, 
                k -> new AtomicLong());
        lastRequestTime.set(System.currentTimeMillis());
    }
    
    /**
     * 增加失败计数
     * 
     * @param shopId 店铺ID
     */
    private void incrementFailureCount(Long shopId) {
        AtomicLong failureCount = shopFailureCountMap.computeIfAbsent(shopId, 
                k -> new AtomicLong());
        failureCount.incrementAndGet();
    }
    
    /**
     * 重置失败计数
     * 
     * @param shopId 店铺ID
     */
    private void resetFailureCount(Long shopId) {
        AtomicLong failureCount = shopFailureCountMap.get(shopId);
        if (failureCount != null) {
            failureCount.set(0);
        }
    }
    
    /**
     * 获取当前应该使用的请求间隔时间
     * 根据失败次数动态调整请求间隔
     * 
     * @param shopId 店铺ID
     * @return 请求间隔时间(毫秒)
     */
    private long getRequestInterval(Long shopId) {
        long baseInterval = rateLimitConfig.getRequestIntervalMs();
        
        // 如果开启了动态退避策略，则根据失败次数调整间隔
        if (rateLimitConfig.isDynamicBackoff()) {
            AtomicLong failureCount = shopFailureCountMap.get(shopId);
            if (failureCount != null) {
                long count = failureCount.get();
                if (count > 0) {
                    // 根据失败次数指数增加间隔，但最多增加10倍
                    long factor = Math.min(10, (long) Math.pow(2, count - 1));
                    return baseInterval * factor;
                }
            }
        }
        
        return baseInterval;
    }
    
    /**
     * 计算重试等待时间
     * 
     * @param shopId 店铺ID
     * @param retryCount 当前重试次数
     * @return 等待时间(毫秒)
     */
    private long calculateWaitTime(Long shopId, int retryCount) {
        long baseRetryInterval = rateLimitConfig.getRetryIntervalMs();
        
        // 如果开启了动态退避策略，则应用指数退避
        if (rateLimitConfig.isDynamicBackoff()) {
            // 重试次数越多，等待时间越长（指数增长）
            long factor = (long) Math.pow(2, retryCount - 1);
            // 最大等待时间不超过30秒
            return Math.min(30000, baseRetryInterval * factor);
        }
        
        return baseRetryInterval;
    }
} 