package com.xiao.temu.modules.production.controller;

import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.modules.production.dto.ProductionGroupDTO;
import com.xiao.temu.modules.production.dto.QueryGroupDTO;
import com.xiao.temu.modules.production.entity.ProductionGroup;
import com.xiao.temu.modules.system.service.SysDataPermissionService;
import com.xiao.temu.security.annotation.RequiresPermission;
import com.xiao.temu.modules.production.service.ProductionGroupService;
import com.xiao.temu.modules.production.service.ProductionGroupMemberService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.xiao.temu.modules.shop.entity.Shop;

import java.util.List;

/**
 * 生产组管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/production/group")
@RequiredArgsConstructor
public class ProductionGroupController {
    private final ProductionGroupService productionGroupService;
    private final ProductionGroupMemberService productionGroupMemberService;
    private final SysDataPermissionService dataPermissionService;

    /**
     * 获取生产组列表
     */
    @GetMapping("/list")
    @RequiresPermission("production:group:list")
    public ApiResponse getGroupList(QueryGroupDTO queryDTO) {
        // 获取当前用户ID
        Long userId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        
        // 判断当前用户是否为管理员
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        // 如果不是管理员，则只能查看自己负责的生产组
        if (!isAdmin) {
            // 设置查询条件为当前用户ID
            queryDTO.setLeaderId(userId);
        }
        
        // 调用服务层方法获取生产组列表
        return ApiResponse.success(productionGroupService.getGroupList(queryDTO));
    }

    /**
     * 获取生产组详细信息
     */
    @GetMapping("/{groupId}")
    @RequiresPermission("production:group:query")
    public ApiResponse getGroup(@PathVariable Long groupId) {
        ProductionGroupDTO group = productionGroupService.getGroupById(groupId);
        if (group == null) {
            return ApiResponse.error("生产组不存在");
        }
        return ApiResponse.success(group);
    }

    /**
     * 新增生产组
     */
    @PostMapping
    @RequiresPermission("production:group:add")
    public ApiResponse addGroup(@Validated @RequestBody ProductionGroupDTO groupDTO) {
        // 校验生产组名称是否唯一
        if (!productionGroupService.checkGroupNameUnique(groupDTO.getGroupName(), null)) {
            return ApiResponse.error("新增生产组'" + groupDTO.getGroupName() + "'失败，生产组名称已存在");
        }
        
        // 插入生产组信息
        int rows = productionGroupService.insertGroup(groupDTO);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("新增生产组失败");
    }

    /**
     * 修改生产组
     */
    @PutMapping
    @RequiresPermission("production:group:edit")
    public ApiResponse updateGroup(@Validated @RequestBody ProductionGroupDTO groupDTO) {
        // 校验生产组ID是否存在
        if (groupDTO.getGroupId() == null) {
            return ApiResponse.error("修改生产组失败，生产组ID不能为空");
        }
        
        // 校验生产组名称是否唯一
        if (!productionGroupService.checkGroupNameUnique(groupDTO.getGroupName(), groupDTO.getGroupId())) {
            return ApiResponse.error("修改生产组'" + groupDTO.getGroupName() + "'失败，生产组名称已存在");
        }
        
        // 更新生产组信息
        int rows = productionGroupService.updateGroup(groupDTO);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("修改生产组失败");
    }

    /**
     * 删除生产组
     */
    @DeleteMapping("/{groupIds}")
    @RequiresPermission("production:group:remove")
    public ApiResponse deleteGroup(@PathVariable Long[] groupIds) {
        try {
            // 删除生产组
            int rows = productionGroupService.deleteGroups(groupIds);
            return rows > 0 ? ApiResponse.success() : ApiResponse.error("删除生产组失败");
        } catch (RuntimeException e) {
            // 捕获业务异常并返回错误信息
            log.error("删除生产组失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 修改生产组状态
     */
    @PutMapping("/changeStatus")
    @RequiresPermission("production:group:edit")
    public ApiResponse changeStatus(@RequestParam Long groupId, @RequestParam String status) {
        // 修改生产组状态
        int rows = productionGroupService.changeStatus(groupId, status);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("修改生产组状态失败");
    }

    /**
     * 设置生产组负责人
     */
    @PutMapping("/leader")
    @RequiresPermission("production:group:edit")
    public ApiResponse setGroupLeader(@RequestParam Long groupId, @RequestParam Long leaderId) {
        try {
            // 设置生产组负责人
            int rows = productionGroupService.setGroupLeader(groupId, leaderId);
            
            return rows > 0 ? ApiResponse.success() : ApiResponse.error("设置生产组负责人失败");
        } catch (RuntimeException e) {
            // 捕获业务异常并返回错误信息
            log.error("设置生产组负责人失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取用户作为负责人的生产组列表
     */
    @GetMapping("/leader")
    public ApiResponse getLeaderGroups() {
        // 获取当前用户ID
        Long userId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        
        return ApiResponse.success(productionGroupService.getGroupsByLeaderId(userId));
    }

    /**
     * 获取用户所属的生产组列表
     */
    @GetMapping("/member")
    public ApiResponse getMemberGroups() {
        // 获取当前用户ID
        Long userId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        
        return ApiResponse.success(productionGroupService.getGroupsByMemberId(userId));
    }
    
    /**
     * 获取未分配给指定生产组的店铺列表
     */
    @GetMapping("/unassignedShops/{groupId}")
    @RequiresPermission("production:group:shop:list")
    public ApiResponse getUnassignedShops(@PathVariable Long groupId) {
        try {
            return ApiResponse.success(productionGroupService.getUnassignedShops(groupId));
        } catch (RuntimeException e) {
            log.error("获取未分配店铺列表失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }
    
    /**
     * 将店铺分配给生产组
     */
    @PostMapping("/assignShops/{groupId}")
    @RequiresPermission("production:group:shop:assign")
    public ApiResponse assignShopsToGroup(@PathVariable Long groupId, @RequestBody List<Long> shopIds) {
        try {
            int count = productionGroupService.assignShopsToGroup(groupId, shopIds);
            return ApiResponse.success(count);
        } catch (RuntimeException e) {
            log.error("分配店铺到生产组失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }
    
    /**
     * 获取生产组关联的店铺列表
     */
    @GetMapping("/shops/{groupId}")
    @RequiresPermission("production:group:shop:list")
    public ApiResponse getGroupShops(@PathVariable Long groupId) {
        try {
            // 获取生产组关联的所有店铺
            List<Shop> shops = productionGroupService.getShopsByGroupId(groupId);
            return ApiResponse.success(shops);
        } catch (RuntimeException e) {
            log.error("获取生产组关联店铺列表失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }
    
    /**
     * 从生产组中移除店铺
     */
    @DeleteMapping("/shop/{groupId}/{shopId}")
    @RequiresPermission("production:group:shop:remove")
    public ApiResponse removeShopFromGroup(@PathVariable Long groupId, @PathVariable Long shopId) {
        try {
            int result = productionGroupService.removeShopFromGroup(groupId, shopId);
            return result > 0 ? ApiResponse.success() : ApiResponse.error("从生产组移除店铺失败");
        } catch (RuntimeException e) {
            log.error("从生产组移除店铺失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }
} 