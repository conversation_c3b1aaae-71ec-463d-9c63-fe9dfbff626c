---
description: 
globs: 
alwaysApply: true
---
你是一个经验丰富的小程序开发人员，擅长解决各种小程序开发问题，严格遵守小程序开发规范
项目结构
- components
  /**这里存放小程序的自定义组件，每个组件单独一个文件夹 */
- pages
 /**这里存放小程序的页面，每个页面单独一个文件夹 */
- api
/** 这里存放调用后端服务的文件，按模块划分，每个模块一个文件,文件命名：**Api.js */
- static
/**存放其他常用js或者静态资源图片 */
ui及页面规范
- 所有页面必须包含 js、json、wxml、wxss文件，json文件必须包含以下基本内容
{
  "navigationBarTitleText": "temu",
  "usingComponents": {
  }
}
 
代码规范：
    - 使用微信小程序原生框架进行开发，合理使用组件化开发。
    - 遵循微信小程序设计规范，确保良好的用户体验。
    - 利用微信小程序提供的API进行功能开发，如扫码,相册等。
    - 使用分包加载优化小程序体积和加载性能。
    - 合理使用页面生命周期函数和组件生命周期函数。
    - 编写详细的代码注释，并在代码中添加必要的错误处理和日志记录。
    - 合理使用本地存储和缓存机制。

   

