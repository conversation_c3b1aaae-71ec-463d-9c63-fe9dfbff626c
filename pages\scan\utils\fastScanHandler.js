/**
 * 快速扫码处理功能
 */
const productionProgressApi = require('../../../api/productionProgressApi');
const userRoleManager = require('./userRoleManager');
const scanUtils = require('./scanUtils');

/**
 * 处理快速扫码结果
 * 在快速扫码模式下，扫码后直接处理，不显示选择工序界面
 */
function handleFastScanResult(page, qrData) {
  console.log('处理快速扫码结果:', qrData);
  
  if (!qrData || !qrData.shopId || !qrData.subPurchaseOrderSn) {
    wx.showToast({
      title: '无效的二维码数据',
      icon: 'none'
    });
    page.backToScan();
    return;
  }

  // 显示加载中提示
  wx.showLoading({ title: '加载中...' });
  
  // 获取备货单的进度详情
  productionProgressApi.getProgressDetail(qrData.shopId, qrData.subPurchaseOrderSn)
    .then(res => {
      console.log('获取进度详情API结果:', res);
      
      // 检查响应格式，API工具已经处理了响应，直接使用返回的数据
      if (res) {
        console.log('获取进度详情成功:', res);
        // 自动根据用户选择的角色更新进度
        autoSelectUserRoleProgress(page, res, qrData);
      } else {
        throw new Error('获取的进度详情数据无效');
      }
    })
    .catch(err => {
      console.error('获取进度详情出错:', err);
      wx.showModal({
        title: '获取详情失败',
        content: err.message || '无法获取备货单的进度详情',
        showCancel: false,
        confirmText: '确定',
        success: () => {
          // 关闭对话框后重置扫码界面
          page.backToScan();
        }
      });
    })
    .finally(() => {
      wx.hideLoading();
    });
}

/**
 * 自动选择用户角色进行快速扫码
 */
function autoSelectUserRoleProgress(page, progressDetail, qrData) {
  console.log('自动选择用户角色进行快速扫码, 进度详情:', progressDetail);
  
  // 检查进度详情数据是否有效
  if (!progressDetail) {
    console.error('进度详情数据无效');
    wx.showModal({
      title: '数据错误',
      content: '获取到的备货单进度详情数据无效',
      showCancel: false,
      success: () => {
        page.backToScan();
      }
    });
    return;
  }
  
  // 从page.data中获取选中的角色
  const selectedUserRoles = page.data.selectedUserRoles;
  const { validProgressRoles } = page.data;
  
  if (!selectedUserRoles || selectedUserRoles.length === 0) {
    console.log('用户没有选择角色，无法执行快速扫码');
    wx.showToast({
      title: '未选择工序',
      icon: 'none'
    });
    // 重置扫码界面，可以继续扫码
    page.backToScan();
    return;
  }
  
  console.log('用户选中工序列表:', selectedUserRoles);
  
  // 筛选出有效的生产进度角色
  const validUserRoleKeys = selectedUserRoles.filter(roleKey => 
    validProgressRoles.includes(roleKey)
  );
  console.log('用户有效的生产进度工序键:', validUserRoleKeys);
  
  // 如果没有有效角色，显示提示并返回
  if (validUserRoleKeys.length === 0) {
    wx.showModal({
      title: '无可用工序',
      content: '您选择的工序中没有可用于更新生产进度的权限',
      showCancel: false,
      success: () => {
        // 对话框关闭后重置扫码界面
        page.backToScan();
      }
    });
    return;
  }
  
  // 筛选出未完成的、用户选中的工序
  let availableProgressTypes = [];
  
  console.log('检查各工序状态:', {
    cutting: progressDetail.cuttingStatus,
    workshop: progressDetail.workshopStatus,
    trimming: progressDetail.trimmingStatus,
    inspection: progressDetail.inspectionStatus,
    packaging: progressDetail.packagingStatus,
    shipping: progressDetail.shippingStatus
  });
  
  // 获取当前用户拥有的角色键值列表
  const userRoleKeys = page.data.userRoles ? page.data.userRoles.map(role => role.roleKey) : [];
  console.log('当前用户拥有的角色键值列表:', userRoleKeys);
  
  // 检查各个工序的状态，只处理未完成且用户选中且用户拥有的工序角色
  for (const roleKey of validUserRoleKeys) {
    // 确认用户拥有该角色
    if (!userRoleKeys.includes(roleKey)) {
      console.log(`用户未拥有角色 ${roleKey}，跳过`);
      continue;
    }
    
    // 检查工序状态
    const statusField = `${roleKey}Status`;
    if (progressDetail[statusField] !== 1) {
      console.log(`工序 ${roleKey} 未完成，可用于处理`);
      availableProgressTypes.push(roleKey);
    } else {
      console.log(`工序 ${roleKey} 已完成，无需处理`);
    }
  }
  
  console.log('快速扫码可用工序:', availableProgressTypes);
  
  if (availableProgressTypes.length === 0) {
    wx.showModal({
      title: '无可用工序',
      content: '没有找到您选择的工序中未完成的部分',
      showCancel: false,
      success: () => {
        // 对话框关闭后重置扫码界面
        page.backToScan();
      }
    });
    return;
  }
  
  // 创建工序名称列表，用于显示
  const progressNames = availableProgressTypes.map(type => page.data.roleProgressMap[type] || type);
  
  // 直接执行工序完成操作，不弹出确认框
  console.log('快速扫码自动完成工序:', progressNames.join('、'));
  
  // 在快速模式下，直接准备调用API完成工序
  const scanResultData = {
    shopId: qrData.shopId,
    subPurchaseOrderSn: qrData.subPurchaseOrderSn
  };
  
  // 设置处理中状态并显示加载提示
  page.setData({ processing: true });
  wx.showLoading({ title: '处理中...' });
  
  // 创建请求所需的参数数组
  const requestParams = availableProgressTypes.map(progressType => {
    // 准备请求参数
    return {
      shopId: scanResultData.shopId,
      subPurchaseOrderSn: scanResultData.subPurchaseOrderSn,
      progressType: progressType,
      operationType: "1", // 扫码确认完成工序，固定为"1"
      remarks: "" // 备注字段，可以为空字符串
    };
  });

  // 采用串行处理工序，更加可靠
  executeProgressSequentially(page, requestParams, progressNames);
}

/**
 * 串行执行工序更新请求
 * 相比并行请求，这种方式更加可靠，可以避免并发请求可能导致的问题
 */
function executeProgressSequentially(page, requestParams, progressNames) {
  // 保存结果
  const results = [];
  // 当前处理的索引
  let currentIndex = 0;
  
  // 标记是否已显示结果对话框
  page.showedResultDialog = false;
  
  // 处理单个工序
  const processNextProgress = () => {
    // 如果所有工序都已处理完成，显示结果
    if (currentIndex >= requestParams.length) {
      handleProgressResults(page, results, progressNames);
      return;
    }
    
    // 获取当前要处理的工序参数
    const params = requestParams[currentIndex];
    const progressType = params.progressType;
    const progressName = page.data.roleProgressMap[progressType] || progressType;
    
    // 更新加载提示，显示当前处理的工序
    wx.showLoading({
      title: `处理${progressName}中...`,
      mask: true
    });
    
    console.log(`开始处理工序[${progressName}]，参数:`, params);
    
    // 发送API请求
    productionProgressApi.updateProgress(params)
      .then(res => {
        console.log(`工序[${progressName}]更新成功:`, res);
        // 添加成功结果
        results.push({ 
          type: progressType, 
          name: progressName,
          success: true, 
          response: res 
        });
      })
      .catch(err => {
        console.error(`工序[${progressName}]更新失败:`, err);
        // 添加失败结果
        results.push({ 
          type: progressType, 
          name: progressName,
          success: false, 
          error: err,
          errorMessage: err.message || '未知错误'
        });
      })
      .finally(() => {
        try {
          // 处理下一个工序
          currentIndex++;
          console.log(`当前工序[${progressName}]处理完成，进度: ${currentIndex}/${requestParams.length}`);
          processNextProgress();
        } catch (error) {
          console.error('处理下一个工序时出错:', error);
          // 确保出错时也显示结果
          if (!page.showedResultDialog) {
            handleProgressResults(page, results, progressNames);
          }
        }
      });
  };
  
  // 开始处理第一个工序
  try {
    processNextProgress();
  } catch (error) {
    console.error('开始处理工序时出错:', error);
    wx.hideLoading();
    wx.showModal({
      title: '处理失败',
      content: '启动工序处理时出错: ' + (error.message || '未知错误'),
      showCancel: false,
      success: () => {
        page.backToScan();
      }
    });
  }
}

/**
 * 处理工序更新结果
 */
function handleProgressResults(page, results, progressNames) {
  console.log('快速扫码工序更新完成, 结果:', results);
  
  // 隐藏加载提示
  wx.hideLoading();
  
  // 统计成功和失败的数量
  const successResults = results.filter(r => r.success);
  const failedResults = results.filter(r => !r.success);
  const successCount = successResults.length;
  const failedCount = failedResults.length;
  
  // 设置一个标记，表示已经显示了对话框，避免finally中重复处理
  page.showedResultDialog = true;
  
  if (failedCount === 0) {
    // 全部成功
    wx.showModal({
      title: '处理成功',
      content: `已完成工序：${progressNames.join('、')}`,
      showCancel: false,
      confirmText: '确定',
      confirmColor: '#1890ff',
      success: () => {
        // 对话框关闭后重置扫码界面
        page.backToScan();
      }
    });
  } else if (successCount > 0) {
    // 部分成功
    // 获取成功和失败的工序名称
    const successNames = successResults.map(r => r.name);
    const failedNames = failedResults.map(r => r.name);
    
    // 获取失败原因
    const failureReasons = failedResults.map(r => 
      `${r.name}: ${r.errorMessage || '未知错误'}`
    ).join('\n');
    
    wx.showModal({
      title: '部分更新成功',
      content: `成功工序：${successNames.join('、')}\n\n失败工序：${failedNames.join('、')}\n\n失败原因：\n${failureReasons}`,
      showCancel: false,
      confirmText: '确定',
      confirmColor: '#1890ff',
      success: () => {
        // 对话框关闭后重置扫码界面
        page.backToScan();
      }
    });
  } else {
    // 全部失败
    // 获取所有失败的工序名称和原因
    const failedNames = failedResults.map(r => r.name);
    
    // 获取失败原因
    const failureReasons = failedResults.map(r => 
      `${r.name}: ${r.errorMessage || '未知错误'}`
    ).join('\n');
    
    wx.showModal({
      title: '更新失败',
      content: `更新失败工序：${failedNames.join('、')}\n\n失败原因：\n${failureReasons}`,
      showCancel: false,
      confirmText: '确定',
      confirmColor: '#1890ff',
      success: () => {
        // 对话框关闭后重置扫码界面
        page.backToScan();
      }
    });
  }
  
  // 无论成功与否，都重置状态
  page.setData({ processing: false });
}

module.exports = {
  handleFastScanResult,
  autoSelectUserRoleProgress
}; 