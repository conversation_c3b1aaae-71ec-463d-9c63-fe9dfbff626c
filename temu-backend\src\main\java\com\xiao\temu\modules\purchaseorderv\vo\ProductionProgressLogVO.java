package com.xiao.temu.modules.purchaseorderv.vo;

import lombok.Data;

import java.util.Date;

/**
 * 生产进度日志视图对象
 */
@Data
public class ProductionProgressLogVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 备货单号
     */
    private String subPurchaseOrderSn;

    /**
     * 进度类型(burning:烧花,sewing:车缝,tail:尾部,shipping:发货,delivery:送货)
     */
    private String progressType;

    /**
     * 操作类型(1:完成 2:撤销)
     */
    private String operationType;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建时间
     */
    private Date createTime;
} 