package com.xiao.temu.modules.quality.service;

import com.xiao.temu.modules.quality.entity.QualityInspectionDefectDetail;
import com.alibaba.fastjson2.JSONObject;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 质检不合格详情数据服务接口
 */
public interface QualityInspectionDefectDetailService {

    /**
     * 同步指定店铺的不合格质检详情数据
     *
     * @param shopId 店铺ID
     * @return 同步结果
     */
    String syncDefectDetailData(Long shopId);
    
    /**
     * 异步同步不合格质检详情数据
     *
     * @param shopId 店铺ID
     * @param qcBillId 质检单ID
     * @param productSkuIds 产品SKU ID列表
     * @return 异步任务
     */
    CompletableFuture<Void> syncDefectDetailAsync(Long shopId, Long qcBillId, List<Long> productSkuIds);
    
    /**
     * 保存质检不合格详情数据
     *
     * @param shopId 店铺ID
     * @param qcBillId 质检单ID
     * @param productSkuId 产品SKU ID
     * @param responseData API返回的数据
     * @return 是否成功
     */
    boolean saveDefectDetail(Long shopId, Long qcBillId, Long productSkuId, JSONObject responseData);
    
    /**
     * 根据店铺ID和质检单ID查询质检不合格详情数据
     *
     * @param shopId 店铺ID
     * @param qcBillId 质检单ID
     * @return 质检不合格详情数据列表
     */
    List<QualityInspectionDefectDetail> getDefectDetailsByShopIdAndQcBillId(Long shopId, Long qcBillId);
    
    /**
     * 批量同步不合格质检详情数据
     *
     * @param shopId 店铺ID
     * @param batchSize 批次大小
     * @param maxThreads 最大线程数
     * @param apiInterval API调用间隔(毫秒)
     * @return 同步结果
     */
    String batchSyncDefectDetailData(Long shopId, int batchSize, int maxThreads, long apiInterval);
    
    /**
     * 删除指定店铺的不合格质检详情数据
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    boolean deleteDefectDetailByShopId(Long shopId);
} 