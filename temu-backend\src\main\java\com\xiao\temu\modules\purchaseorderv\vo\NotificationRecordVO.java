package com.xiao.temu.modules.purchaseorderv.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 通知记录VO
 */
@Data
public class NotificationRecordVO {
    
    /**
     * 记录ID
     */
    private Long id;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺名称
     */
    private String shopName;
    
    /**
     * 备货单号
     */
    private String subPurchaseOrderSn;
    
    /**
     * 商品名称
     */
    private String productName;
    
    /**
     * 通知类型
     */
    private Integer notificationType;
    
    /**
     * 通知类型名称
     */
    private String notificationTypeName;
    
    /**
     * 通知次数
     */
    private Integer notifyCount;
    
    /**
     * 最大通知次数
     */
    private Integer maxNotifyCount;
    
    /**
     * 最后通知时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastNotifyTime;
    
    /**
     * 通知状态(0:待通知 1:通知中 2:已通知完成)
     */
    private Integer notifyStatus;
    
    /**
     * 运营组ID
     */
    private Long groupId;
    
    /**
     * 运营组名称
     */
    private String groupName;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
} 