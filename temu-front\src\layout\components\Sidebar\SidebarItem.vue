<template>
  <div v-if="!item.meta || item.meta.hidden !== true">
    <!-- 首页菜单单独处理为普通菜单项 -->
    <template v-if="isDashboardMenu(item)">
      <app-link :to="resolvePath(item.path)">
        <el-menu-item :index="resolvePath(item.path)" :class="{'submenu-title-noDropdown': !isNest}">
          <el-icon v-if="item.meta && item.meta.icon && item.meta.icon !== '#'"><component :is="item.meta.icon" /></el-icon>
          <el-icon v-else><HomeFilled /></el-icon>
          <template #title>
            <span class="menu-title">{{ item.meta && item.meta.title ? item.meta.title : '首页' }}</span>
          </template>
        </el-menu-item>
      </app-link>
    </template>
    
    <!-- 常规菜单项处理 -->
    <template v-else-if="hasOneShowingChild(item.children, item) && (!onlyOneChild.children || onlyOneChild.noShowingChildren) && !(item.meta && item.meta.alwaysShow)">
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
        <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{'submenu-title-noDropdown': !isNest}">
          <el-icon v-if="onlyOneChild.meta.icon && onlyOneChild.meta.icon !== '#'"><component :is="onlyOneChild.meta.icon" /></el-icon>
          <el-icon v-else><Menu /></el-icon>
          <template #title>{{ onlyOneChild.meta.title }}</template>
        </el-menu-item>
      </app-link>
    </template>
    
    <!-- 子菜单处理 -->
    <el-sub-menu v-else ref="subMenu" :index="resolvePath(item.path)" popper-append-to-body>
      <template #title>
        <el-icon v-if="item.meta && item.meta.icon && item.meta.icon !== '#'"><component :is="item.meta.icon" /></el-icon>
        <el-icon v-else-if="item.meta"><Menu /></el-icon>
        <span v-if="item.meta">{{ item.meta.title }}</span>
      </template>
      
      <!-- 增加调试信息 -->
      <template v-if="!item.children || item.children.length === 0">
        <el-menu-item :index="resolvePath(item.path) + '/empty'" disabled>
          <span class="menu-empty-tip">无子菜单</span>
        </el-menu-item>
      </template>
      
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-sub-menu>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { isExternal } from '@/utils/validate'
import AppLink from './Link.vue'
import path from 'path-browserify'
import { Menu, HomeFilled } from '@element-plus/icons-vue'
import type { RouteRecordRaw } from 'vue-router'

interface Props {
  item: RouteRecordRaw
  basePath: string
  isNest?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isNest: false
})

const onlyOneChild = ref<any>(null)

/**
 * 检查是否是首页菜单
 */
function isDashboardMenu(route: RouteRecordRaw): boolean {
  if (!route) return false;
  
  // 特殊处理首页路由
  const isDashboardPath = route.path === '/' || 
                          route.path === '/dashboard' || 
                          route.path.endsWith('/dashboard');
                          
  const isDashboardName = route.name === 'Dashboard' || 
                          route.name === '首页' || 
                          (typeof route.name === 'string' && route.name.toLowerCase().includes('dashboard'));
                          
  const isDashboardTitle = route.meta && route.meta.title === '首页' ? true : false;
  
  return isDashboardPath || isDashboardName || isDashboardTitle;
}

/**
 * 判断是否只有一个可显示的子路由
 */
function hasOneShowingChild(children: RouteRecordRaw[] = [], parent: RouteRecordRaw) {
  // 首页已经在模板中单独处理，这里只处理非首页菜单
  
  // 如果没有子路由，直接返回父路由
  if (!children || children.length === 0) {
    onlyOneChild.value = { ...parent, path: '', noShowingChildren: true }
    return true
  }
  
  // 特殊处理：如果菜单是目录类型，总是使用el-sub-menu显示
  if (parent.meta && parent.meta.menuType === 'M') {
    return false
  }
  
  const showingChildren = children.filter(item => {
    // 过滤掉meta.hidden为true的项
    if (item.meta && item.meta.hidden === true) {
      return false
    } else {
      // 不在循环中直接赋值，仅在最后确定
      return true
    }
  })

  // 当只有一个子路由时，默认显示这个子路由
  if (showingChildren.length === 1) {
    // 只有确定只有一个子项时才赋值
    onlyOneChild.value = showingChildren[0]
    return true
  }

  // 如果没有子路由，显示父路由
  if (showingChildren.length === 0) {
    onlyOneChild.value = { ...parent, path: '', noShowingChildren: true }
    return true
  }
  
  // 有多个子菜单，返回false使用el-sub-menu显示
  return false
}

/**
 * 解析路径
 */
function resolvePath(routePath: string) {
  if (isExternal(routePath)) {
    return routePath
  }
  if (isExternal(props.basePath)) {
    return props.basePath
  }
  
  // 确保路径合并正确
  if (!routePath) {
    // 如果routePath为空，直接返回basePath
    return props.basePath
  }
  
  // 使用path.resolve合并路径
  const resolvedPath = path.resolve(props.basePath, routePath)
  return resolvedPath
}
</script>

<style scoped>
.menu-empty-tip {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

.menu-title {
  display: inline-block;
  white-space: nowrap;
  margin-left: 4px;
}
</style> 