package com.xiao.temu.modules.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商品实体类
 */
@Data
@TableName("product")
public class Product {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品SKC ID
     */
    private Long productSkcId;

    /**
     * 创建时间戳
     */
    private Long createdAt;

    /**
     * 是否支持个性化
     */
    private Boolean isSupportPersonalization;

    /**
     * 外部编码
     */
    private String extCode;

    /**
     * SKC站点状态
     */
    private Integer skcSiteStatus;

    /**
     * 是否匹配JIT模式
     */
    private Boolean matchSkcJitMode;

    /**
     * 主图URL
     */
    private String mainImageUrl;

    /**
     * 同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 