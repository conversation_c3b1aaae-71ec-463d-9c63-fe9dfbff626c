package com.xiao.temu.modules.sync.service;

import com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderV;
import com.xiao.temu.modules.sync.entity.PurchaseOrderSyncTask;
import com.xiao.temu.modules.sync.vo.ApiResponse;

import java.util.List;

/**
 * 备货单数据同步服务接口
 */
public interface PurchaseOrderSyncService {

    /**
     * 同步指定店铺的备货单数据
     *
     * @param shopId 店铺ID
     * @return 同步结果
     */
    ApiResponse.PurchaseOrderSyncVO syncPurchaseOrderData(Long shopId);

    /**
     * 初始化店铺的同步任务
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    boolean initSyncTask(Long shopId);

    /**
     * 获取同步任务列表
     *
     * @param shopIds 店铺ID列表，为空则查询所有
     * @return 同步任务列表
     */
    List<PurchaseOrderSyncTask> getSyncTasks(List<Long> shopIds);

    /**
     * 根据店铺ID获取同步任务
     *
     * @param shopId 店铺ID
     * @return 同步任务
     */
    PurchaseOrderSyncTask getSyncTaskByShopId(Long shopId);

    /**
     * 执行定时同步
     * 
     * @return 同步结果
     */
    String executeScheduledSync();

    /**
     * 获取指定店铺的备货单总数
     *
     * @param shopId 店铺ID
     * @param purchaseStockType 备货类型，null表示全部
     * @return 备货单总数
     */
    Integer getPurchaseOrderCountByShopId(Long shopId, Integer purchaseStockType);

    /**
     * 清空店铺的同步数据
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    boolean clearSyncData(Long shopId);
    
    /**
     * 同步指定店铺的JIT备货单数据
     *
     * @param shopId 店铺ID
     * @param pageSize 每页数量
     * @param maxPages 最大页数
     * @return 同步的JIT备货单数量
     */
    int syncJitPurchaseOrders(Long shopId, int pageSize, int maxPages);
    
    /**
     * 同步指定店铺的普通备货单数据
     *
     * @param shopId 店铺ID
     * @param pageSize 每页数量
     * @param maxPages 最大页数
     * @return 同步的普通备货单数量
     */
    int syncNormalPurchaseOrders(Long shopId, int pageSize, int maxPages);
} 