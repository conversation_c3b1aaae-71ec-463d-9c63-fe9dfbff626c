<template>
  <el-dialog
    title="运营组详情"
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    width="800px"
    append-to-body
    destroy-on-close
  >
    <el-skeleton :loading="loading" animated>
      <template #template>
        <div style="padding: 20px">
          <el-skeleton-item variant="text" style="width: 50%" />
          <div style="display: flex; margin-top: 20px">
            <el-skeleton-item variant="text" style="margin-right: 16px" />
            <el-skeleton-item variant="text" style="width: 30%" />
          </div>
          <el-skeleton-item variant="h3" style="margin-top: 20px; width: 50%" />
          <el-skeleton-item variant="text" style="margin-top: 16px; width: 100%" />
          <el-skeleton-item variant="text" style="margin-top: 16px; width: 100%" />
        </div>
      </template>
      
      <template #default>
        <div class="group-detail-container">
          <!-- 基本信息 -->
          <el-descriptions title="基本信息" :column="2" border>
            <el-descriptions-item label="运营组ID">{{ groupDetail.groupId }}</el-descriptions-item>
            <el-descriptions-item label="运营组名称">{{ groupDetail.groupName }}</el-descriptions-item>
            <el-descriptions-item label="负责人">{{ groupDetail.leaderName }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="groupDetail.status === '0' ? 'success' : 'danger'">
                {{ groupDetail.status === '0' ? '正常' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatTime(groupDetail.createTime) }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ formatTime(groupDetail.updateTime) }}</el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">{{ groupDetail.remark || '无' }}</el-descriptions-item>
          </el-descriptions>

          <!-- 统计数据 -->
          <div class="statistics-section" v-if="statistics">
            <h3 class="section-title">统计数据</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card shadow="hover" class="stat-card">
                  <template #header>
                    <div class="card-header">
                      <span>成员数量</span>
                    </div>
                  </template>
                  <div class="stat-value">{{ statistics.memberCount || 0 }}</div>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card shadow="hover" class="stat-card">
                  <template #header>
                    <div class="card-header">
                      <span>店铺数量</span>
                    </div>
                  </template>
                  <div class="stat-value">{{ statistics.shopCount || 0 }}</div>
                </el-card>
              </el-col>
            </el-row>
          </div>

          <!-- 成员列表 -->
          <div class="members-section">
            <h3 class="section-title">成员列表</h3>
            <el-table
              v-loading="membersLoading"
              :data="membersList"
              style="width: 100%"
              border
              stripe
              size="small"
              max-height="250"
            >
              <el-table-column type="index" label="序号" width="80" />
              <el-table-column label="姓名" prop="nickName" />
              <el-table-column label="手机号" prop="phonenumber" width="120" />
              <el-table-column label="角色" prop="roles" width="150">
                <template #default="scope">
                  <span>{{ scope.row.roles ? scope.row.roles.map(role => role.roleName).join(', ') : '' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="加入时间" prop="joinTime" width="180">
                <template #default="scope">
                  {{ formatTime(scope.row.joinTime) }}
                </template>
              </el-table-column>
            </el-table>
            <div class="empty-text" v-if="membersList.length === 0 && !membersLoading">
              暂无成员数据
            </div>
          </div>

          <!-- 店铺列表 -->
          <div class="shops-section">
            <h3 class="section-title">店铺列表</h3>
            <el-table
              v-loading="shopsLoading"
              :data="shopsList"
              style="width: 100%"
              border
              stripe
              size="small"
              max-height="250"
            >
              <el-table-column type="index" label="序号" width="80" />
              <el-table-column label="店铺名称" prop="shopName" />
              <el-table-column label="状态" prop="status" width="80">
                <template #default="scope">
                  <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
                    {{ scope.row.status === '0' ? '正常' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
            <div class="empty-text" v-if="shopsList.length === 0 && !shopsLoading">
              暂无店铺数据
            </div>
          </div>
        </div>
      </template>
    </el-skeleton>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { getGroupDetail, getGroupMembers, getGroupStatistics } from '@/api/group'
import { getGroupShops } from '@/api/shop'
import { formatTime } from '@/utils/format'

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  groupId: {
    type: Number,
    default: 0
  }
})

// 定义事件
const emit = defineEmits(['update:visible', 'close'])

// 数据对象
const loading = ref(false)
const membersLoading = ref(false)
const shopsLoading = ref(false)
const groupDetail = ref<any>({})
const statistics = ref<any>(null)
const membersList = ref<any[]>([])
const shopsList = ref<any[]>([])

// 加载所有数据
const loadAllData = () => {
  if (!props.groupId) return
  
  loadGroupDetail()
  loadGroupStatistics()
  loadGroupMembers()
  loadGroupShops()
}

// 使用一个组合监听器，同时监听 visible 和 groupId
watch(
  () => ({ visible: props.visible, groupId: props.groupId }),
  (newVal) => {
    if (newVal.visible && newVal.groupId) {
      loadAllData()
    }
  },
  { immediate: true }
)

// 加载运营组详情
const loadGroupDetail = async () => {
  loading.value = true
  try {
    const res = await getGroupDetail(props.groupId)
    groupDetail.value = res.data || {}
  } catch (error) {
    console.error('获取运营组详情失败', error)
  } finally {
    loading.value = false
  }
}

// 加载运营组统计数据
const loadGroupStatistics = async () => {
  try {
    const res = await getGroupStatistics(props.groupId)
    statistics.value = res.data || {}
  } catch (error) {
    console.error('获取运营组统计数据失败', error)
  }
}

// 加载运营组成员
const loadGroupMembers = async () => {
  membersLoading.value = true
  try {
    const res = await getGroupMembers(props.groupId, 1, 50)
    membersList.value = res.data?.records || []
  } catch (error) {
    console.error('获取运营组成员失败', error)
  } finally {
    membersLoading.value = false
  }
}

// 加载运营组店铺
const loadGroupShops = async () => {
  shopsLoading.value = true
  try {
    const res = await getGroupShops(props.groupId)
    // 检查返回的数据是否为空数组
    shopsList.value = Array.isArray(res.data) ? res.data : []
    // 如果列表为空，打印一条调试信息
    if (shopsList.value.length === 0) {
      console.log('获取到的店铺列表为空，可能是店铺未正确分配到运营组')
    }
  } catch (error) {
    console.error('获取运营组店铺失败', error)
  } finally {
    shopsLoading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}
</script>

<style scoped>
.group-detail-container {
  padding: 5px;
}

.section-title {
  margin: 20px 0 15px 0;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.statistics-section {
  margin-top: 20px;
}

.stat-card {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.members-section, .shops-section {
  margin-top: 20px;
}

.empty-text {
  text-align: center;
  color: #909399;
  padding: 20px 0;
}
</style> 