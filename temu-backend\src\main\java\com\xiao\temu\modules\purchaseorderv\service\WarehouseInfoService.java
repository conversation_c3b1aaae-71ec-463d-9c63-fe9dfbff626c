package com.xiao.temu.modules.purchaseorderv.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xiao.temu.modules.purchaseorderv.dto.WarehouseInfoBatchDTO;
import com.xiao.temu.modules.purchaseorderv.entity.WarehouseInfo;

import java.util.List;

/**
 * 仓库信息服务接口
 */
public interface WarehouseInfoService extends IService<WarehouseInfo> {
    
    /**
     * 批量保存仓库信息
     *
     * @param warehouseInfoBatchDTO 仓库信息批量DTO
     * @return 是否保存成功
     */
    boolean batchSaveWarehouseInfo(WarehouseInfoBatchDTO warehouseInfoBatchDTO);
} 