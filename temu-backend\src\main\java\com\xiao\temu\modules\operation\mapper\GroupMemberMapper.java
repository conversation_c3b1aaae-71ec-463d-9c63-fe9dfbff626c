package com.xiao.temu.modules.operation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.operation.dto.GroupMemberDTO;
import com.xiao.temu.modules.operation.entity.GroupMember;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 运营组成员Mapper接口
 */
@Mapper
public interface GroupMemberMapper extends BaseMapper<GroupMember> {

    /**
     * 分页查询运营组成员列表
     *
     * @param page    分页参数
     * @param groupId 运营组ID
     * @return 成员列表
     */
    IPage<GroupMemberDTO> selectMemberList(Page<GroupMemberDTO> page, @Param("groupId") Long groupId);

    /**
     * 查询运营组成员列表（不分页）
     *
     * @param groupId 运营组ID
     * @return 成员列表
     */
    List<GroupMemberDTO> selectAllMembers(@Param("groupId") Long groupId);

    /**
     * 查询用户是否为运营组成员
     *
     * @param groupId 运营组ID
     * @param userId  用户ID
     * @return 结果
     */
    int checkUserInGroup(@Param("groupId") Long groupId, @Param("userId") Long userId);

    /**
     * 统计运营组成员数量
     *
     * @param groupId 运营组ID
     * @return 成员数量
     */
    int countGroupMembers(@Param("groupId") Long groupId);

    /**
     * 批量添加运营组成员
     *
     * @param groupId  运营组ID
     * @param userIds  用户ID列表
     * @param joinTime 加入时间
     * @return 结果
     */
    int batchAddMembers(@Param("groupId") Long groupId, @Param("userIds") List<Long> userIds, @Param("joinTime") java.util.Date joinTime);

    /**
     * 删除运营组成员
     *
     * @param groupId 运营组ID
     * @param userId  用户ID
     * @return 结果
     */
    int deleteMember(@Param("groupId") Long groupId, @Param("userId") Long userId);

    /**
     * 根据运营组ID删除所有成员
     *
     * @param groupId 运营组ID
     * @return 结果
     */
    int deleteByGroupId(@Param("groupId") Long groupId);

    /**
     * 根据运营组ID查询用户ID列表
     *
     * @param groupId 运营组ID
     * @return 用户ID列表
     */
    List<Long> selectUserIdsByGroupId(@Param("groupId") Long groupId);
    
    /**
     * 根据运营组ID查询所有成员
     *
     * @param groupId 运营组ID
     * @return 成员列表
     */
    List<GroupMember> selectByGroupId(@Param("groupId") Long groupId);
} 