package com.xiao.temu.modules.violation.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiao.temu.common.response.PageResult;
import com.xiao.temu.modules.violation.entity.ShopViolationInfo;
import com.xiao.temu.modules.violation.dto.ShopViolationInfoDTO;
import com.xiao.temu.modules.violation.dto.ShopViolationInfoQueryDTO;
import com.xiao.temu.modules.violation.vo.ShopViolationInfoVO;
import com.xiao.temu.modules.violation.mapper.ShopViolationInfoMapper;
import com.xiao.temu.modules.violation.service.ShopViolationInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 店铺违规信息Service实现
 */
@Service
@Slf4j
public class ShopViolationInfoServiceImpl extends ServiceImpl<ShopViolationInfoMapper, ShopViolationInfo> implements ShopViolationInfoService {

    /**
     * 分页查询店铺违规信息列表
     *
     * @param queryDTO 查询条件
     * @return 店铺违规信息分页数据
     */
    @Override
    public PageResult<ShopViolationInfoDTO> listViolationInfos(ShopViolationInfoQueryDTO queryDTO) {
        Page<ShopViolationInfoDTO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        IPage<ShopViolationInfoDTO> pageData = baseMapper.selectViolationInfoList(page, queryDTO);

        return new PageResult<>(pageData.getRecords(), pageData.getTotal(), queryDTO.getPageNum(), queryDTO.getPageSize());
    }

    /**
     * 分页查询店铺违规信息列表(返回VO对象)
     *
     * @param queryDTO 查询条件
     * @return 店铺违规信息VO对象
     */
    @Override
    public ShopViolationInfoVO getViolationInfoList(ShopViolationInfoQueryDTO queryDTO) {
        Page<ShopViolationInfoDTO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        IPage<ShopViolationInfoDTO> pageData = baseMapper.selectViolationInfoList(page, queryDTO);
        
        return new ShopViolationInfoVO(pageData.getRecords(), pageData.getTotal(), queryDTO.getPageNum(), queryDTO.getPageSize());
    }

    /**
     * 根据ID查询违规信息详情
     *
     * @param id 违规信息ID
     * @return 违规信息详情
     */
    @Override
    public ShopViolationInfoDTO getViolationInfoById(Long id) {
        return baseMapper.selectViolationInfoById(id);
    }

    /**
     * 新增违规信息
     *
     * @param violationInfo 违规信息
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addViolationInfo(ShopViolationInfo violationInfo) {
        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        if (violationInfo.getCreateTime() == null) {
            violationInfo.setCreateTime(now);
        }
        if (violationInfo.getUpdateTime() == null) {
            violationInfo.setUpdateTime(now);
        }
        
        return this.save(violationInfo);
    }

    /**
     * 修改违规信息
     *
     * @param violationInfo 违规信息
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateViolationInfo(ShopViolationInfo violationInfo) {
        // 设置更新时间
        violationInfo.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(violationInfo);
    }

    /**
     * 删除违规信息
     *
     * @param id 违规信息ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteViolationInfo(Long id) {
        return this.removeById(id);
    }

    /**
     * 批量删除违规信息
     *
     * @param ids 违规信息ID数组
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteViolationInfo(Long[] ids) {
        List<Long> idList = Arrays.asList(ids);
        return this.removeByIds(idList);
    }

    /**
     * 批量添加违规信息
     *
     * @param violationInfoList 违规信息列表
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAddViolationInfo(List<ShopViolationInfo> violationInfoList) {
        if (violationInfoList == null || violationInfoList.isEmpty()) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        int totalCount = violationInfoList.size();
        int skipCount = 0;
        int successCount = 0;
        int updateCount = 0;
        
        // 逐条处理每个记录，而不是使用批量保存
        for (ShopViolationInfo info : violationInfoList) {
            // 设置创建和更新时间
            if (info.getCreateTime() == null) {
                info.setCreateTime(now);
            }
            if (info.getUpdateTime() == null) {
                info.setUpdateTime(now);
            }
            
            // 根据时间戳设置LocalDateTime
            if (info.getViolationStartTime() != null) {
                info.setViolationTime(LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(info.getViolationStartTime()),
                    java.time.ZoneId.systemDefault()
                ));
            }
            
            boolean exists = false;
            ShopViolationInfo existingInfo = null;
            
            // 检查记录是否存在
            if (info.getMallId() != null && !info.getMallId().isEmpty()) {
                existingInfo = baseMapper.selectByMallIdAndPunishSn(info.getMallId(), info.getPunishSn());
                exists = existingInfo != null;
                if (exists) {
                    log.info("违规信息已存在，更新数据: mallId={}, punishSn={}", info.getMallId(), info.getPunishSn());
                }
            } else if (info.getShopId() != null) {
                existingInfo = baseMapper.selectByShopIdAndPunishSn(info.getShopId(), info.getPunishSn());
                exists = existingInfo != null;
                if (exists) {
                    log.info("违规信息已存在，更新数据: shopId={}, punishSn={}", info.getShopId(), info.getPunishSn());
                }
            }
            
            try {
                boolean success;
                // 如果记录存在，则更新
                if (exists && existingInfo != null) {
                    info.setId(existingInfo.getId());
                    success = this.updateById(info);
                    if (success) {
                        updateCount++;
                        successCount++;
                    }
                } else {
                    // 如果记录不存在，则新增
                    success = this.save(info);
                    if (success) {
                        successCount++;
                    }
                }
            } catch (Exception e) {
                log.error("保存/更新违规信息失败: shopId={}, punishSn={}, 错误: {}", 
                    info.getShopId(), info.getPunishSn(), e.getMessage());
                // 如果是因为唯一约束导致的异常，继续处理其他记录
                if (e.getMessage() != null && e.getMessage().contains("Duplicate entry")) {
                    continue;
                } else {
                    // 其他类型的异常则抛出，触发事务回滚
                    throw e;
                }
            }
        }
        
        log.info("批量添加/更新违规信息完成: 总数={}, 成功={}, 更新={}", totalCount, successCount, updateCount);
        return true;
    }

    /**
     * 检查违规信息是否已存在
     *
     * @param shopId 店铺ID
     * @param punishSn 违规编号
     * @return 是否存在
     */
    @Override
    public boolean checkViolationInfoExists(Long shopId, String punishSn) {
        ShopViolationInfo violationInfo = baseMapper.selectByShopIdAndPunishSn(shopId, punishSn);
        return violationInfo != null;
    }
    
    /**
     * 根据MallId检查违规信息是否已存在
     *
     * @param mallId 店铺MallId
     * @param punishSn 违规编号
     * @return 是否存在
     */
    @Override
    public boolean checkViolationInfoExistsByMallId(String mallId, String punishSn) {
        ShopViolationInfo violationInfo = baseMapper.selectByMallIdAndPunishSn(mallId, punishSn);
        return violationInfo != null;
    }
    
    /**
     * 根据店铺ID和违规编号获取违规信息
     *
     * @param shopId 店铺ID
     * @param punishSn 违规编号
     * @return 违规信息
     */
    @Override
    public ShopViolationInfo getViolationInfoByShopIdAndPunishSn(Long shopId, String punishSn) {
        return baseMapper.selectByShopIdAndPunishSn(shopId, punishSn);
    }

    /**
     * 分页查询店铺违规信息列表(包含违规详情，返回VO对象)
     *
     * @param queryDTO 查询条件
     * @return 店铺违规信息VO对象
     */
    @Override
    public ShopViolationInfoVO getViolationInfoListWithDetail(ShopViolationInfoQueryDTO queryDTO) {
        Page<ShopViolationInfoDTO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        IPage<ShopViolationInfoDTO> pageData = baseMapper.selectViolationInfoListWithDetail(page, queryDTO);
        
        return new ShopViolationInfoVO(pageData.getRecords(), pageData.getTotal(), queryDTO.getPageNum(), queryDTO.getPageSize());
    }
} 