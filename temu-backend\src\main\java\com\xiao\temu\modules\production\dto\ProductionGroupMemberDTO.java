package com.xiao.temu.modules.production.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 生产组成员数据传输对象
 */
@Data
public class ProductionGroupMemberDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 生产组ID
     */
    private Long groupId;

    /**
     * 生产组名称
     */
    private String groupName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 加入时间
     */
    private Date joinTime;

    /**
     * 状态（0正常 1禁用）
     */
    private String status;
    
    /**
     * 用户角色ID列表
     */
    private List<Long> roleIds;
    
    /**
     * 用户角色名称列表，用逗号分隔
     */
    private String roleNames;
} 