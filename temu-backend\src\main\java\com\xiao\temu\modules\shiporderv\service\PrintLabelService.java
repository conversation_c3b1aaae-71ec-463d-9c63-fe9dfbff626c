package com.xiao.temu.modules.shiporderv.service;

import com.xiao.temu.modules.shiporderv.dto.PrintLabelRequestDTO;
import com.xiao.temu.modules.sync.vo.ApiResponse;

import java.util.List;

/**
 * 打印标签服务接口
 */
public interface PrintLabelService {
    
    /**
     * 获取商品打包标签信息
     *
     * @param printLabelRequestList 打印标签请求列表
     * @return API响应，包含标签数据
     */
    ApiResponse getBoxMarkInfo(List<PrintLabelRequestDTO> printLabelRequestList);
} 