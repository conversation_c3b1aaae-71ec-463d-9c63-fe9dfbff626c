package com.xiao.temu.modules.sync.job;

import com.xiao.temu.infrastructure.task.ExportTaskManager;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

/**
 * 导出文件清理定时任务
 * 定期清理过期的导出文件和任务记录
 * 已完成任务文件保留24小时，失败任务2小时，孤儿文件48小时
 */
@Component
@Slf4j
public class ExportFileCleanupJob extends QuartzJobBean {

    @Autowired
    private ExportTaskManager exportTaskManager;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        log.info("开始执行导出文件清理任务... 触发时间: {}", context.getFireTime());
        
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("导出文件清理");
        
        try {
            log.info("调用清理过期文件方法...");
            exportTaskManager.cleanupOldTasks();
            
            stopWatch.stop();
            log.info("导出文件清理任务执行完成，耗时: {}ms", stopWatch.getTotalTimeMillis());
        } catch (Exception e) {
            log.error("导出文件清理任务执行异常", e);
            throw new JobExecutionException(e);
        }
    }
} 