package com.xiao.temu.modules.system.service;

import com.xiao.temu.modules.system.vo.SysUserVO;
import java.util.List;

/**
 * 系统用户服务接口
 */
public interface SysUserService {

    /**
     * 获取未分配到任何运营组的用户列表
     *
     * @param roleKey 角色标识，可选，如果提供则只返回具有该角色的用户
     * @return 未分配用户列表
     */
    List<SysUserVO> getUnassignedUsers(String roleKey);

    /**
     * 获取所有具有管理员角色的用户ID列表
     * 
     * @return 管理员用户ID列表
     */
    List<Long> getAdminUserIds();
}