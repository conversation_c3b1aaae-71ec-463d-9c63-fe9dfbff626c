package com.xiao.temu.modules.shop.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.shop.dto.ShopDTO;
import com.xiao.temu.modules.shop.dto.ShopQueryDTO;
import com.xiao.temu.modules.shop.entity.Shop;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 店铺Mapper接口
 */
@Mapper
public interface ShopMapper extends BaseMapper<Shop> {
    
    /**
     * 分页查询店铺列表
     * 
     * @param page 分页参数
     * @param query 查询条件
     * @return 店铺列表（包含运营组名称）
     */
    IPage<ShopDTO> selectShopList(Page<ShopDTO> page, @Param("query") ShopQueryDTO query);
    
    /**
     * 根据ID查询店铺详情
     * 
     * @param shopId 店铺ID
     * @return 店铺详情（包含运营组名称）
     */
    ShopDTO selectShopById(@Param("shopId") Long shopId);
    
    /**
     * 根据用户ID查询有权限的店铺列表
     * 
     * @param userId 用户ID
     * @return 店铺列表
     */
    List<Shop> selectShopsByUserId(@Param("userId") Long userId);
    
    /**
     * 根据运营组ID查询店铺列表
     * 
     * @param groupId 运营组ID
     * @return 店铺列表
     */
    List<Shop> selectShopsByGroupId(@Param("groupId") Long groupId);
    
    /**
     * 根据运营组ID查询所有关联的店铺列表，包括那些被移除了belongGroupId的店铺
     * 
     * @param groupId 运营组ID
     * @return 店铺列表
     */
    List<Shop> selectAllShopsByGroupId(@Param("groupId") Long groupId);
    
    /**
     * 根据店铺ID查询所属的运营组ID列表
     * 
     * @param shopId 店铺ID
     * @return 运营组ID列表
     */
    List<Long> selectGroupIdsByShopId(@Param("shopId") Long shopId);
    
    /**
     * 检查店铺是否属于指定运营组
     * 
     * @param shopId 店铺ID
     * @param groupId 运营组ID
     * @return 是否属于
     */
    boolean checkShopInGroup(@Param("shopId") Long shopId, @Param("groupId") Long groupId);
    
    /**
     * 根据多个运营组ID查询店铺列表
     *
     * @param groupIds 运营组ID列表
     * @return 店铺列表
     */
    List<Shop> selectShopsByGroupIds(@Param("groupIds") List<Long> groupIds);
    
    /**
     * 更新店铺所属运营组ID
     *
     * @param shopId 店铺ID
     * @param groupId 运营组ID，可以为null
     * @return 影响的行数
     */
    int updateBelongGroupId(@Param("shopId") Long shopId, @Param("groupId") Long groupId);
    
    /**
     * 查询所有启用状态的店铺
     *
     * @return 所有启用状态的店铺列表
     */
    List<Shop> selectAllEnabledShops();
} 