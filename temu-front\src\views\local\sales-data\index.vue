<template>
  <div class="app-container sales-data-container">
    <el-card class="box-card form-table-card">
      <!-- 添加快速筛选区域 -->
      <div class="quick-filter-container">
        <div class="quick-filter-title">快速筛选：</div>
        <div class="quick-filter-buttons">
          <el-button 
            size="small" 
            :type="quickFilter === 'isLack' ? 'primary' : 'default'" 
            @click="handleQuickFilter('isLack')"
          >
            已缺货
          </el-button>
        </div>
      </div>
      
      <!-- 基本查询条件区域 - 每行4个，共2行，显示8个常用条件 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px" size="small" class="compact-form">
        <el-row :gutter="6">
          <!-- 第一行 4个控件 -->
          <el-col :span="6">
            <el-form-item label="选择店铺" prop="shopIds">
              <el-select
                v-model="queryParams.shopIds"
                multiple
                collapse-tags
                placeholder="请选择店铺"
                clearable
                filterable
                :filter-method="filterShops"
                style="width: 240px;"
                @change="handleShopChange"
                @clear="handleClearShops"
              >
                <!-- 添加全选选项 -->
                <el-option
                  key="select-all"
                  label="全选"
                  :value="-1"
                />
                <el-option
                  v-for="shop in filteredShops"
                  :key="shop.shopId"
                  :label="shop.shopName"
                  :value="shop.shopId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="SPU" prop="spuIdList">
              <el-input v-model="tempInputs.spuIdListStr" placeholder="多个用逗号分隔" clearable style="width: 240px;" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="SKC" prop="skcIdList">
              <el-input v-model="tempInputs.skcIdListStr" placeholder="多个用逗号分隔" clearable style="width: 240px;" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否VMI" prop="settlementType">
              <el-select v-model="queryParams.settlementType" placeholder="请选择" clearable style="width: 240px;">
                <el-option label="是 (VMI)" :value="1" />
                <el-option label="否 (非VMI)" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="6">
          <!-- 第二行 4个控件 -->
          <el-col :span="6">
            <el-form-item label="是否JIT" prop="purchaseStockType">
              <el-select v-model="queryParams.purchaseStockType" placeholder="请选择" clearable style="width: 240px;">
                <el-option label="是 (JIT备货)" :value="1" />
                <el-option label="否 (普通)" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否热销款" prop="hotTag">
              <el-select
                v-model="queryParams.hotTag"
                placeholder="请选择是否热销款"
                clearable
                style="width: 240px;"
              >
                <el-option :key="1" label="是" :value="1"></el-option>
                <el-option :key="0" label="否" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否定制商品" prop="isCustomGoods">
              <el-select v-model="queryParams.isCustomGoods" placeholder="请选择" clearable style="width: 240px;">
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="备货状态" prop="supplyStatusList">
              <el-select v-model="advancedQuery.supplyStatusList" multiple collapse-tags placeholder="请选择备货状态" clearable style="width: 240px;">
                <el-option label="正常供货" :value="0" />
                <el-option label="暂时无货" :value="1" />
                <el-option label="停产" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 高级查询条件区域 - 折叠状态 -->
      <el-form :model="advancedQuery" :inline="true" label-width="80px" size="small" class="compact-form" v-if="showAdvanced">
        <!-- 高级查询第一行 -->
        <el-row :gutter="6">
          <el-col :span="6">
            <el-form-item label="备货区域" prop="inventoryRegionList">
              <el-select
                v-model="advancedQuery.inventoryRegionList"
                multiple
                collapse-tags
                placeholder="请选择备货区域"
                clearable
                style="width: 240px;"
              >
                <el-option label="国内备货" :value="1" />
                <el-option label="海外备货" :value="2" />
                <el-option label="保税仓备货" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="备货仓组" prop="warehouseGroupId">
              <el-select
                v-model="advancedQuery.warehouseGroupId"
                placeholder="请选择备货仓组"
                clearable
                style="width: 240px;"
                :loading="warehouseGroupsLoading"
              >
                <el-option
                  v-for="group in warehouseGroups"
                  :key="group.warehouseGroupId"
                  :label="group.warehouseGroupName"
                  :value="group.warehouseGroupId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="JIT进度" prop="closeJitStatus">
              <el-select
                v-model="advancedQuery.closeJitStatus"
                multiple
                collapse-tags
                placeholder="请选择JIT转备货进度"
                clearable
                style="width: 240px;"
              >
                <el-option label="未申请" :value="0" />
                <el-option label="待调价" :value="1" />
                <el-option label="待备货" :value="2" />
                <el-option label="备货完成" :value="3" />
                <el-option label="JIT已关闭" :value="4" />
                <el-option label="调价失败" :value="5" />
                <el-option label="备货失败" :value="6" />
                <el-option label="发起涨价" :value="7" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="自动关闭JIT" prop="autoCloseJit">
              <el-select
                v-model="advancedQuery.autoCloseJit"
                placeholder="请选择是否自动关闭JIT"
                clearable
                style="width: 240px;"
              >
                <el-option :key="true" label="是" :value="true"></el-option>
                <el-option :key="false" label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 高级查询第二行 -->
        <el-row :gutter="6">
          <el-col :span="6">
            <el-form-item label="图片审核" prop="pictureAuditStatusList">
              <el-select
                v-model="advancedQuery.pictureAuditStatusList"
                multiple
                collapse-tags
                placeholder="请选择图片审核状态"
                clearable
                style="width: 240px;"
              >
                <el-option label="未完成" :value="1" />
                <el-option label="已完成" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="SKC货号" prop="skcExtCodeListStr">
              <el-input 
                v-model="advancedQuery.skcExtCodeListStr"
                placeholder="多个用逗号分隔"
                clearable
                style="width: 240px;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="SKU货号" prop="skuExtCodeListStr">
              <el-input 
                v-model="advancedQuery.skuExtCodeListStr"
                placeholder="多个用逗号分隔"
                clearable
                style="width: 240px;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="可售天数" prop="availableSaleDays">
              <div class="input-with-select">
                <el-select 
                  v-model="advancedQuery.availableSaleDaysOp" 
                  placeholder="请选择" 
                  style="width: 70px"
                  size="small"
                >
                  <el-option label=">=" value="gte" />
                  <el-option label="<=" value="lte" />
                </el-select>
                <el-input
                  v-model="advancedQuery.availableSaleDaysValue"
                  placeholder="请输入可售天数"
                  clearable
                  size="small"
                  style="width: 170px;"
                />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 高级查询第三行 -->
        <el-row :gutter="6">
          <el-col :span="6">
            <el-form-item label="加入站点时长">
              <div class="site-duration-container">
                <el-input
                  v-model.number="advancedQuery.onSalesDurationOfflineGte"
                  placeholder="最小天数"
                  type="number"
                  :min="0"
                  clearable
                  size="small"
                  style="width: 115px;"
                />
                <span class="site-duration-separator">-</span>
                <el-input
                  v-model.number="advancedQuery.onSalesDurationOfflineLte"
                  placeholder="最大天数"
                  type="number"
                  :min="0"
                  clearable
                  size="small"
                  style="width: 115px;"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="销量范围">
              <div class="sales-query-range-container">
                <el-select 
                  v-model="advancedQuery.saleVolumeTimeRange" 
                  placeholder="时间范围" 
                  clearable 
                  size="small"
                  style="width: 100px;"
                  @change="handleSaleVolumeTimeRangeChange"
                >
                  <el-option label="今日" :value="1" />
                  <el-option label="近7日" :value="2" />
                  <el-option label="近30天" :value="3" />
                </el-select>
                <el-input
                  v-model.number="advancedQuery.saleVolumeMin"
                  placeholder="Min"
                  type="number"
                  :min="0"
                  clearable
                  size="small"
                  style="width: 70px;"
                />
                <span class="sales-query-range-separator">~</span>
                <el-input
                  v-model.number="advancedQuery.saleVolumeMax"
                  placeholder="Max"
                  type="number"
                  :min="0"
                  clearable
                  size="small"
                  style="width: 70px;"
                />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 按钮区域 - 固定在右下角 -->
      <div class="action-buttons-container">
        <div class="left-buttons">
          <el-button 
            type="primary" 
            plain
            size="small" 
            @click="showAdvanced = !showAdvanced" 
          >
            {{ showAdvanced ? '收起高级查询' : '展开高级查询' }}
            <el-icon class="el-icon--right">
              <component :is="showAdvanced ? 'ArrowUp' : 'ArrowDown'" />
            </el-icon>
          </el-button>
        </div>
        <div class="right-buttons">
          <el-button type="primary" size="small" @click="handleQuery">查询</el-button>
          <el-button plain size="small" @click="resetQuery">重置</el-button>
          <el-button type="warning" size="small" @click="handleExport">导出</el-button>
        </div>
      </div>

      <!-- Wrapper for table to handle overflow -->
      <div class="table-wrapper">
        <el-table
          v-loading="loading"
          :data="groupedSalesList"
          border
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          row-key="rowId"
          :span-method="objectSpanMethod"
          height="100%"
          :row-class-name="rowClassName"
        >
          <el-table-column type="selection" width="55" align="center" />
          
          <!-- 店铺列 -->
          <el-table-column label="店铺信息" width="150">
            <template #default="scope">
              <div class="shop-info">
                <div class="shop-name">{{ scope.row.shopName }}</div>
                <div v-if="scope.row.shopRemark" class="shop-remark">{{ scope.row.shopRemark }}</div>
              </div>
            </template>
          </el-table-column>
          
          <!-- 商品信息列 -->
          <el-table-column label="商品信息" width="300">
            <template #default="scope">
              <div class="product-info">
                <el-image
                  v-if="scope.row.productSkcPicture"
                  :src="scope.row.productSkcPicture"
                  class="product-image"
                  :preview-src-list="scope.row.productSkcPicture ? [scope.row.productSkcPicture] : []"
                  fit="contain"
                  :preview-teleported="true"
                />
                <div class="info-content">
                  <div v-if="scope.row.productName" class="product-name" style="white-space: normal;">{{ scope.row.productName }}</div>
                  <div v-if="scope.row.productSkcId">SKC ID: {{ scope.row.productSkcId }}</div>
                  <div v-if="scope.row.productId">SPU: {{ scope.row.productId }}</div>
                  <div v-if="scope.row.skcExtCode">SKC货号: {{ scope.row.skcExtCode }}</div>
                  <div v-if="scope.row.onSalesDurationOffline !== undefined">加入站点时长: {{ scope.row.onSalesDurationOffline }} 天</div>
                  <div class="tags" style="margin-top: 5px;">
                    <el-tag v-if="scope.row.inventoryRegion === 1" size="mini" type="primary" style="margin-right: 5px;">国内备货</el-tag>
                    <el-tag v-if="scope.row.inventoryRegion === 2" size="mini" type="warning" style="margin-right: 5px;">海外备货</el-tag>
                    <el-tag v-if="scope.row.inventoryRegion === 3" size="mini" type="success" style="margin-right: 5px;">保税仓备货</el-tag>
                    
                    <el-tag v-if="scope.row.purchaseStockType === 1" size="mini" type="warning" style="margin-right: 5px;">JIT</el-tag>
                    
                    <el-tag v-if="scope.row.hotTag === 1" size="mini" type="danger" style="margin-right: 5px;">🔥热销款</el-tag>
                    
                    <el-tag v-if="scope.row.supplyStatus === 1" size="mini" type="warning" style="margin-right: 5px;">已缺货</el-tag>
                    <!-- <el-tag v-if="scope.row.supplyStatus === 0" size="mini" type="success" style="margin-right: 5px;">正常供货</el-tag> -->
                    <!-- <el-tag v-if="scope.row.supplyStatus === 2" size="mini" type="danger">停产</el-tag> -->
                    
                    <!-- JIT转备货状态，根据截图调整文本 -->
                    <!-- <el-tag v-if="scope.row.closeJitStatus === 0" size="mini" type="primary" style="margin-right: 5px;">JIT未申请</el-tag> -->
                    <el-tag v-if="scope.row.closeJitStatus === 1" size="mini" type="primary" style="margin-right: 5px;">JIT转备货-待调价</el-tag>
                    <el-tag v-if="scope.row.closeJitStatus === 2" size="mini" type="warning" style="margin-right: 5px;">JIT转备货-备货中</el-tag>
                    <!-- <el-tag v-if="scope.row.closeJitStatus === 3" size="mini" type="success" style="margin-right: 5px;">JIT备货完成,待关闭</el-tag> -->
                    <el-tag v-if="scope.row.closeJitStatus === 4" size="mini" type="info" style="margin-right: 5px;">JIT已关闭</el-tag>
                    <el-tag v-if="scope.row.closeJitStatus === 5" size="mini" type="danger" style="margin-right: 5px;">JIT调价失败</el-tag>
                    <el-tag v-if="scope.row.closeJitStatus === 6" size="mini" type="danger" style="margin-right: 5px;">JIT备货失败</el-tag>
                    <el-tag v-if="scope.row.closeJitStatus === 7" size="mini" type="warning" style="margin-right: 5px;">JIT降价后涨价</el-tag>

                    <!-- 新增标签：仅在SKC行显示 -->
                    <template v-if="!scope.row.isSkuRow && !scope.row.isSummaryRow">
                      <el-tag v-if="scope.row.totalData && scope.row.totalData.totalWaitDeliveryInventoryNum > 0" size="mini" type="warning" style="margin-right: 5px;">有待发货备货单</el-tag>
                      <el-tag v-if="scope.row.totalData && scope.row.totalData.totalAdviceQuantity > 0" size="mini" type="primary" style="margin-right: 5px;">建议备货</el-tag>
                    </template>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <!-- SKU信息列 -->
          <el-table-column label="SKU信息" width="220">
            <template #default="scope">
              <div v-if="scope.row.isSkuRow">
                <div v-if="scope.row.className">{{ scope.row.className }}</div>
                <div v-if="scope.row.productSkuId">SKU ID: {{ scope.row.productSkuId }}</div>
                <div v-if="scope.row.skuExtCode">SKU货号: {{ scope.row.skuExtCode }}</div>
              </div>
              <div v-else-if="scope.row.isSummaryRow" class="sku-summary">
                <div class="sku-summary-title">{{ scope.row.className }}</div>
              </div>
            </template>
          </el-table-column>
          
          <!-- 申报价格列 -->
          <el-table-column label="申报价格(CNY)(平台未返回)" width="120" align="right">
            <template #default="scope">
              <span v-if="scope.row.isSkuRow && typeof scope.row.supplierPrice === 'number'">¥{{ scope.row.supplierPrice.toFixed(2) }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          
          <!-- 开款价格状态列 -->
          <el-table-column label="开款价格状态 (平台未返回)" width="120">
            <template #default="scope">
              <span v-if="scope.row.isSkuRow">
                {{ scope.row.priceReviewStatus === 2 ? '已生效' : (scope.row.priceReviewStatus !== undefined ? `状态 ${scope.row.priceReviewStatus}` : '-') }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          
          <!-- 缺货数量列 -->
          <el-table-column label="缺货数量" width="100" align="center">
            <template #default="scope">
              <span v-if="scope.row.isSkuRow">{{ scope.row.lackQuantity !== undefined ? scope.row.lackQuantity : 0 }}</span>
              <span v-else-if="scope.row.isSummaryRow">{{ scope.row.totalData.lackQuantity }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          
          <!-- 销售数据列 -->
          <el-table-column label="销售数据" align="center">
            <el-table-column label="今日" width="80" align="center">
              <template #header>
                <div class="sortable-header">
                  <span>今日</span>
                  <div class="sort-icons">
                    <el-icon 
                      :class="{ active: queryParams.sortField === 1 && queryParams.sortDirection === 'asc' }"
                      @click="handleSort(1, 'asc')">
                      <ArrowUp />
                    </el-icon>
                    <el-icon 
                      :class="{ active: queryParams.sortField === 1 && queryParams.sortDirection === 'desc' }"
                      @click="handleSort(1, 'desc')">
                      <ArrowDown />
                    </el-icon>
                  </div>
                </div>
              </template>
              <template #default="scope">
                <span v-if="scope.row.isSkuRow">{{ scope.row.todaySaleVolume !== undefined ? scope.row.todaySaleVolume : 0 }}</span>
                <span v-else-if="scope.row.isSummaryRow">{{ scope.row.totalData.todaySaleVolume }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="近7天" width="80" align="center">
              <template #header>
                <div class="sortable-header">
                  <span>近7天</span>
                  <div class="sort-icons">
                    <el-icon 
                      :class="{ active: queryParams.sortField === 2 && queryParams.sortDirection === 'asc' }"
                      @click="handleSort(2, 'asc')">
                      <ArrowUp />
                    </el-icon>
                    <el-icon 
                      :class="{ active: queryParams.sortField === 2 && queryParams.sortDirection === 'desc' }"
                      @click="handleSort(2, 'desc')">
                      <ArrowDown />
                    </el-icon>
                  </div>
                </div>
              </template>
              <template #default="scope">
                <span v-if="scope.row.isSkuRow">{{ scope.row.lastSevenDaysSaleVolume !== undefined ? scope.row.lastSevenDaysSaleVolume : 0 }}</span>
                <span v-else-if="scope.row.isSummaryRow">{{ scope.row.totalData.lastSevenDaysSaleVolume }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="近30天" width="80" align="center">
              <template #header>
                <div class="sortable-header">
                  <span>近30天</span>
                  <div class="sort-icons">
                    <el-icon 
                      :class="{ active: queryParams.sortField === 3 && queryParams.sortDirection === 'asc' }"
                      @click="handleSort(3, 'asc')">
                      <ArrowUp />
                    </el-icon>
                    <el-icon 
                      :class="{ active: queryParams.sortField === 3 && queryParams.sortDirection === 'desc' }"
                      @click="handleSort(3, 'desc')">
                      <ArrowDown />
                    </el-icon>
                  </div>
                </div>
              </template>
              <template #default="scope">
                <span v-if="scope.row.isSkuRow">{{ scope.row.lastThirtyDaysSaleVolume !== undefined ? scope.row.lastThirtyDaysSaleVolume : 0 }}</span>
                <span v-else-if="scope.row.isSummaryRow">{{ scope.row.totalData.lastThirtyDaysSaleVolume }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table-column>
          
          <!-- 库存数据列 -->
          <el-table-column label="库存数据" align="center">
            <el-table-column label="仓内可用库存" width="120" align="center">
              <template #default="scope">
                <span v-if="scope.row.isSkuRow">{{ scope.row.warehouseInventoryNum !== undefined ? scope.row.warehouseInventoryNum : 0 }}</span>
                <span v-else-if="scope.row.isSummaryRow">{{ scope.row.totalData.warehouseInventoryNum }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="仓内预占用库存(平台未返回)" width="130" align="center">
              <template #default="scope">
                <!-- <span v-if="scope.row.isSkuRow">{{ scope.row.expectedOccupiedInventoryNum !== undefined ? scope.row.expectedOccupiedInventoryNum : 0 }}</span> -->
                <!-- <span v-else>-</span> -->
                <span >-</span>
              </template>
            </el-table-column>
            <el-table-column label="仓内暂不可用库存(平台未返回)" width="140" align="center">
              <template #default="scope">
                <!-- <span v-if="scope.row.isSkuRow">{{ scope.row.unavailableWarehouseInventoryNum !== undefined ? scope.row.unavailableWarehouseInventoryNum : 0 }}</span>
                <span v-else>-</span> -->
                <span >-</span>

              </template>
            </el-table-column>
            <el-table-column label="已发货库存" width="120" align="center">
              <template #default="scope">
                <span v-if="scope.row.isSkuRow">{{ scope.row.waitReceiveNum !== undefined ? scope.row.waitReceiveNum : 0 }}</span>
                <span v-else-if="scope.row.isSummaryRow">{{ scope.row.totalData.waitReceiveNum }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="待发货库存" width="120" align="center">
              <template #default="scope">
                <span v-if="scope.row.isSkuRow">{{ scope.row.waitDeliveryInventoryNum !== undefined ? scope.row.waitDeliveryInventoryNum : 0 }}</span>
                <span v-else-if="scope.row.isSummaryRow">{{ scope.row.totalData.waitDeliveryInventoryNum }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="待审核备货库存(平台未返回)" width="130" align="center">
              <template #default="scope">
                <!-- <span v-if="scope.row.isSkuRow">{{ scope.row.waitApproveInventoryNum !== undefined ? scope.row.waitApproveInventoryNum : 0 }}</span>
                <span v-else>-</span> -->
                <span >-</span>

              </template>
            </el-table-column>
          </el-table-column>
          
          <!-- 备货计划列 -->
          <el-table-column label="备货计划" align="center">
            <el-table-column label="备货逻辑" width="100" align="center">
              <template #default="scope">
                <span v-if="scope.row.isSkuRow">{{ scope.row.purchaseConfig || '-' }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="建议备货量" width="100" align="center">
              <template #default="scope">
                <span v-if="scope.row.isSkuRow">{{ scope.row.adviceQuantity !== undefined ? scope.row.adviceQuantity : 0 }}</span>
                <span v-else-if="scope.row.isSummaryRow">{{ scope.row.totalData.adviceQuantity }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="库存可售天数" width="120" align="center">
              <template #default="scope">
                <span v-if="scope.row.isSkuRow">{{ scope.row.availableSaleDaysFromInventory !== undefined ? scope.row.availableSaleDaysFromInventory : '-' }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="仓内库存可售天数" width="140" align="center">
              <template #default="scope">
                <span v-if="scope.row.isSkuRow">{{ scope.row.warehouseAvailableSaleDays !== undefined ? scope.row.warehouseAvailableSaleDays : '-' }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="可售天数" width="100" align="center">
              <template #default="scope">
                <span v-if="scope.row.isSkuRow">{{ scope.row.availableSaleDays !== undefined ? scope.row.availableSaleDays : '-' }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- Pagination fixed at the bottom -->
    <div class="bottom-pagination-bar">
      <el-pagination
        v-show="total > 0"
        :total="total"
        :current-page.sync="queryParams.pageNo"
        :page-size.sync="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        background
      />
    </div>
    
    <!-- 添加导出对话框组件 -->
    <ExportDialog
      v-model:visible="exportDialog.visible"
      data-type="sales"
      :default-file-name="exportDialog.fileName"
      :current-page-data="salesList"
      :total="total"
      :fetch-data-fn="createExportTask"
      :query-params="queryParams"
      :use-backend-export="true"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessageBox, ElMessage, ElLoading } from 'element-plus'
import { getLocalSalesList, getWarehouseGroups, getWarehouseGroupsByShopIds, createSalesExportTask, getSalesExportProgress, downloadSalesExcelFile } from '@/api/local/sales'
import { fetchUserAccessibleShops } from '@/utils/shop-helper'
import type { LocalSalesRequestParams, SalesItemInfo, SkuInfo } from '@/types/local/sales'
import type { Shop } from '@/types/shop'
import { getToken } from '@/utils/auth'
import { ElForm } from 'element-plus' // 导入 ElForm 类型
import { ExportDialog } from '@/components/temu'
import { useExportTaskStore } from '@/store/modules/exportTask'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'

// 定义用于 groupedSalesList 的接口
interface GroupedSalesItem {
  // 通用标识字段
  id: number;
  rowId: string;
  isSkuRow: boolean;
  parentIndex: number;
  skuCount: number;
  isSummaryRow?: boolean; // 新增：标识是否为规格合计行
  
  // 店铺信息
  shopId: number;
  shopName: string;
  shopRemark?: string;
  
  // 商品基本信息
  productId: number;
  productSkcId: number;
  productSkuId: number | null;
  productName: string;
  skcExtCode: string | null;
  skuExtCode: string | null;
  category: string;
  productSkcPicture: string;
  className: string | null;
  
  // 商品特性
  isCustomGoods: boolean;
  inventoryRegion: number;
  inventoryRegionName: string;
  onSalesDurationOffline: number;
  autoCloseJit: boolean;
  closeJitStatus: number;
  closeJitStatusName: string;
  hasHotSku: boolean;
  isEnoughStock: boolean;
  hotTag?: boolean;
  supplyStatus?: number;
  pictureAuditStatus?: number; // 图片审核状态
  
  // 销量数据
  todaySaleVolume?: number;
  totalSaleVolume?: number;
  lastSevenDaysSaleVolume?: number;
  lastThirtyDaysSaleVolume?: number;
  inCartNumber?: number;
  inCartNumber7d?: number;
  
  // 库存数据
  stockDays?: number;
  warehouseInventoryNum?: number;
  waitOnShelfNum?: number;
  waitDeliveryInventoryNum?: number;
  expectedOccupiedInventoryNum?: number;
  waitApproveInventoryNum?: number;
  waitQcNum?: number;
  unavailableWarehouseInventoryNum?: number;
  waitInStock?: number;
  waitReceiveNum?: number;
  lackQuantity?: number;
  adviceQuantity?: number;
  
  // 仓库信息
  warehouseGroupId?: number;
  warehouseGroupName?: string;
  safeInventoryDays?: number;
  purchaseConfig?: string;
  availableSaleDays?: string;
  availableSaleDaysFromInventory?: string;
  warehouseAvailableSaleDays?: string;
  sevenDaysSaleReference?: string;
  sevenDaysReferenceSaleType?: number;
  
  // 价格和审核
  supplierPrice?: number;
  priceReviewStatus?: number;
  
  // 其他信息
  syncTime: string;
  isTotalRow?: boolean; // 新增，标识是否为合计行
  totalData?: {
    todaySaleVolume: number;
    lastSevenDaysSaleVolume: number;
    lastThirtyDaysSaleVolume: number;
    warehouseInventoryNum: number;
    waitDeliveryInventoryNum: number;
    waitReceiveNum: number;
    lackQuantity: number;
    adviceQuantity: number;
    totalWaitDeliveryInventoryNum: number;
    totalAdviceQuantity: number;
  };
}

// 店铺列表
const shops = ref<Shop[]>([])
// 销售数据列表
const salesList = ref<SalesItemInfo[]>([])
// 加载状态
const loading = ref(false)
// 总记录数
const total = ref(0)

// 导出任务store
const exportTaskStore = useExportTaskStore()

// 备货仓组列表
const warehouseGroups = ref<any[]>([])
// 备货仓组加载状态
const warehouseGroupsLoading = ref(false)

// 导出对话框配置
const exportDialog = reactive({
  visible: false,
  fileName: '本地销售数据',
})

// 获取 Form 实例引用 (可选, 如果需要显式校验)
const queryFormRef = ref<InstanceType<typeof ElForm> | null>(null)
const advancedQueryFormRef = ref<InstanceType<typeof ElForm> | null>(null)

// 是否显示高级查询
const showAdvanced = ref(false)

// 查询参数
const queryParams = reactive<LocalSalesRequestParams>({
  pageNo: 1,
  pageSize: 10,
  shopIds: [],
  spuIdList: undefined as number[] | undefined,
  skcIdList: undefined as number[] | undefined,
  productNameKeyword: '',
  isCustomGoods: undefined as boolean | undefined,
  settlementType: undefined as number | undefined,
  purchaseStockType: undefined as number | undefined,
  hotTag: undefined as number | undefined,
  supplyStatusList: undefined as number[] | undefined,
  sortField: undefined as number | undefined, // 添加排序字段
  sortDirection: undefined as string | undefined, // 添加排序方向
})

// 快速筛选类型
const quickFilter = ref<string | null>(null)

// 高级查询参数
const advancedQuery = reactive({
  onSalesDurationOfflineGte: undefined as number | undefined,
  onSalesDurationOfflineLte: undefined as number | undefined,
  inventoryRegionList: [] as number[],
  minRemanentInventoryNum: undefined,
  maxRemanentInventoryNum: undefined,
  minAvailableSaleDays: undefined,
  maxAvailableSaleDays: undefined,
  pictureAuditStatusList: [] as number[],
  supplyStatusList: [] as number[],
  closeJitStatus: [],
  isLack: undefined,
  todaySaleVolumMin: undefined,
  todaySaleVolumMax: undefined,
  hotTag: undefined as number | undefined,
  suggestCloseJit: undefined,
  stockStatusList: [],
  availableProduceNumGreaterThanzero: undefined,
  skcExtCodeListStr: '',
  skuExtCodeListStr: '',
  availableSaleDaysOp: 'gte' as 'gte' | 'lte', // 默认为大于等于
  availableSaleDaysValue: undefined as number | undefined,
  // 新增销量查询相关字段
  saleVolumeTimeRange: undefined as number | undefined, // 1-今日, 2-近7日, 3-近30天
  saleVolumeMin: undefined as number | undefined, // 最小销量
  saleVolumeMax: undefined as number | undefined, // 最大销量
  autoCloseJit: undefined as boolean | undefined,
  warehouseGroupId: undefined as number | undefined, // 新增仓库组ID字段
})

// 添加用于字符串输入的临时变量
const tempInputs = reactive({
  spuIdListStr: '',
  skcIdListStr: ''
})

// 用于将逗号分隔的ID字符串转换为数字数组的函数
const parseIdString = (idStr: string): number[] | undefined => {
  if (!idStr || idStr.trim() === '') return undefined
  
  const ids = idStr.trim()
    .split(',')
    .map(id => parseInt(id.trim()))
    .filter(id => !isNaN(id))
  
  return ids.length > 0 ? ids : undefined
}

// 监听字符串输入变化，转换为数字数组
watch(() => tempInputs.spuIdListStr, (newVal) => {
  queryParams.spuIdList = parseIdString(newVal)
})

watch(() => tempInputs.skcIdListStr, (newVal) => {
  queryParams.skcIdList = parseIdString(newVal)
})

// 分组后的销售数据列表
const groupedSalesList = computed(() => {
  const result: GroupedSalesItem[] = [];

  salesList.value.forEach((skcItem, skcIndex) => {
    const skuCount = skcItem.skuList?.length || 0;
    const skcRowId = `skc-${skcItem.productSkcId}-${skcIndex}`;

    // 创建一个对象用于存储合计数据
    const totalData = {
      todaySaleVolume: 0,
      lastSevenDaysSaleVolume: 0,
      lastThirtyDaysSaleVolume: 0,
      warehouseInventoryNum: 0,
      waitDeliveryInventoryNum: 0,
      waitReceiveNum: 0,
      lackQuantity: 0,
      adviceQuantity: 0,
      totalWaitDeliveryInventoryNum: 0,
      totalAdviceQuantity: 0
    };

    // 添加SKC行
    result.push({
      ...skcItem,
      rowId: skcRowId,
      isSkuRow: false,
      parentIndex: skcIndex,
      skuCount: skuCount,
      productSkuId: null,
      skuExtCode: null,
      className: null,
      totalData: {
        todaySaleVolume: 0,
        lastSevenDaysSaleVolume: 0,
        lastThirtyDaysSaleVolume: 0,
        warehouseInventoryNum: 0,
        waitDeliveryInventoryNum: 0,
        waitReceiveNum: 0,
        lackQuantity: 0,
        adviceQuantity: 0,
        totalWaitDeliveryInventoryNum: 0,
        totalAdviceQuantity: 0
      }
    });

    if (skcItem.skuList && skcItem.skuList.length > 0) {
      skcItem.skuList.forEach((skuInfo, skuIndex) => {
        // 查找对应的库存信息
        const inventoryInfo = skcItem.inventoryList?.find(inv => inv.productSkuId === skuInfo.productSkuId);
        // 查找对应的仓库信息
        const warehouseInfo = skcItem.warehouseList?.find(wh => wh.productSkuId === skuInfo.productSkuId);
        
        // 构造SKU行，合并SKU信息、库存信息和仓库信息
        const skuItem: GroupedSalesItem = {
          // 保留基础SKC信息
          id: skcItem.id,
          shopId: skcItem.shopId,
          shopName: skcItem.shopName,
          shopRemark: undefined,
          productId: skcItem.productId,
          productSkcId: skcItem.productSkcId,
          productName: skcItem.productName,
          skcExtCode: skcItem.skcExtCode,
          category: skcItem.category,
          productSkcPicture: skcItem.productSkcPicture,
          isCustomGoods: skcItem.isCustomGoods,
          inventoryRegion: skcItem.inventoryRegion,
          inventoryRegionName: skcItem.inventoryRegionName,
          onSalesDurationOffline: skcItem.onSalesDurationOffline,
          autoCloseJit: skcItem.autoCloseJit,
          closeJitStatus: skcItem.closeJitStatus,
          closeJitStatusName: skcItem.closeJitStatusName,
          hasHotSku: skcItem.hasHotSku,
          isEnoughStock: skcItem.isEnoughStock,
          syncTime: skcItem.syncTime,
          pictureAuditStatus: skcItem.pictureAuditStatus,
          
          // SKU行专有属性
          rowId: `sku-${skuInfo.productSkuId}-${skuIndex}`,
          isSkuRow: true,
          parentIndex: skcIndex,
          skuCount: 0,
          
          // 从skuInfo中获取的信息
          productSkuId: skuInfo.productSkuId,
          skuExtCode: skuInfo.skuExtCode || null,
          className: skuInfo.className || null,
          stockDays: skuInfo.stockDays,
          todaySaleVolume: skuInfo.todaySaleVolume,
          totalSaleVolume: skuInfo.totalSaleVolume,
          lastSevenDaysSaleVolume: skuInfo.lastSevenDaysSaleVolume,
          lastThirtyDaysSaleVolume: skuInfo.lastThirtyDaysSaleVolume,
          inCartNumber: skuInfo.inCartNumber,
          inCartNumber7d: skuInfo.inCartNumber7d,
          lackQuantity: skuInfo.lackQuantity,
          
          // 从inventoryInfo中获取的信息
          warehouseInventoryNum: inventoryInfo?.warehouseInventoryNum,
          waitOnShelfNum: inventoryInfo?.waitOnShelfNum,
          waitDeliveryInventoryNum: inventoryInfo?.waitDeliveryInventoryNum,
          expectedOccupiedInventoryNum: inventoryInfo?.expectedOccupiedInventoryNum,
          waitApproveInventoryNum: inventoryInfo?.waitApproveInventoryNum,
          waitQcNum: inventoryInfo?.waitQcNum,
          unavailableWarehouseInventoryNum: inventoryInfo?.unavailableWarehouseInventoryNum,
          waitInStock: inventoryInfo?.waitInStock,
          waitReceiveNum: inventoryInfo?.waitReceiveNum,
          
          // 从warehouseInfo中获取的信息
          warehouseGroupId: warehouseInfo?.warehouseGroupId,
          warehouseGroupName: warehouseInfo?.warehouseGroupName,
          safeInventoryDays: warehouseInfo?.safeInventoryDays,
          purchaseConfig: warehouseInfo?.purchaseConfig,
          adviceQuantity: warehouseInfo?.adviceQuantity,
          availableSaleDays: warehouseInfo?.availableSaleDays,
          availableSaleDaysFromInventory: warehouseInfo?.availableSaleDaysFromInventory, 
          warehouseAvailableSaleDays: warehouseInfo?.warehouseAvailableSaleDays,
          sevenDaysSaleReference: warehouseInfo?.sevenDaysSaleReference,
          sevenDaysReferenceSaleType: warehouseInfo?.sevenDaysReferenceSaleType,
          
          // 其他字段，从warehouseInfo或其他地方获取
          hotTag: false,
          supplyStatus: undefined,
          supplierPrice: undefined,
          priceReviewStatus: undefined
        };
        
        // 添加SKU行数据到结果集
        result.push(skuItem);
        
        // 累计数值型数据到合计对象
        totalData.todaySaleVolume += skuInfo.todaySaleVolume || 0;
        totalData.lastSevenDaysSaleVolume += skuInfo.lastSevenDaysSaleVolume || 0;
        totalData.lastThirtyDaysSaleVolume += skuInfo.lastThirtyDaysSaleVolume || 0;
        totalData.warehouseInventoryNum += inventoryInfo?.warehouseInventoryNum || 0;
        totalData.waitDeliveryInventoryNum += inventoryInfo?.waitDeliveryInventoryNum || 0;
        totalData.waitReceiveNum += inventoryInfo?.waitReceiveNum || 0;
        totalData.lackQuantity += skuInfo.lackQuantity || 0;
        totalData.adviceQuantity += warehouseInfo?.adviceQuantity || 0;
        totalData.totalWaitDeliveryInventoryNum += inventoryInfo?.waitDeliveryInventoryNum || 0;
        totalData.totalAdviceQuantity += warehouseInfo?.adviceQuantity || 0;
      });
      
      // 更新SKC行中的合计数据
      const skcRow = result.find(item => item.rowId === skcRowId);
      if (skcRow) {
        skcRow.totalData = totalData;
      }
      
      // 添加单独的合计行（在所有SKU之后）
      if (skuCount > 0) {
        result.push({
          id: skcItem.id,
          rowId: `skc-total-${skcItem.productSkcId}-${skcIndex}`,
          isSkuRow: false,
          parentIndex: skcIndex,
          skuCount: 0,
          isSummaryRow: true,  // 标记为合计行
          shopId: skcItem.shopId,
          shopName: skcItem.shopName,
          productId: skcItem.productId,
          productSkcId: skcItem.productSkcId,
          productName: skcItem.productName,
          skcExtCode: null,
          skuExtCode: null,  // 添加缺少的属性
          category: skcItem.category,
          productSkcPicture: "",
          isCustomGoods: false,
          inventoryRegion: 0,
          inventoryRegionName: "",
          onSalesDurationOffline: 0,
          autoCloseJit: false,
          closeJitStatus: 0,
          closeJitStatusName: "",
          hasHotSku: false,
          isEnoughStock: false,
          syncTime: "",
          productSkuId: null,
          className: "规格合计",  // 在SKU信息列显示"合计"
          totalData: totalData  // 使用累计的合计数据
        });
      }
    }
  });

  return result;
});

// 添加全选状态跟踪变量
const isAllSelected = ref(false)

// 添加过滤后的店铺列表
const filteredShops = ref<Shop[]>([])
// 用于存储过滤搜索关键词
const filterKeyword = ref('')

// 获取店铺列表
const getShops = async () => {
  try {
    const shopsData = await fetchUserAccessibleShops()
    // 使用类型断言解决可能的类型不匹配问题
    shops.value = shopsData as unknown as Shop[]
    // 初始化过滤后的店铺列表
    filteredShops.value = [...shops.value]
  } catch (error) {
    console.error('获取店铺列表失败', error)
    ElMessage.error('获取店铺列表失败')
  }
}

// 过滤店铺方法
const filterShops = (query: string) => {
  filterKeyword.value = query
  if (query) {
    filteredShops.value = shops.value.filter(shop => 
      shop.shopName.toLowerCase().includes(query.toLowerCase())
    )
    
    // 当过滤条件发生变化时，重置全选状态
    isAllSelected.value = false
    
    // 如果当前已选中店铺，保留它们（如果它们在过滤结果中）
    if (queryParams.shopIds && queryParams.shopIds.length > 0) {
      // 保留那些在过滤结果中的店铺ID
      queryParams.shopIds = queryParams.shopIds.filter(id => 
        filteredShops.value.some(shop => shop.shopId === id)
      )
    }
  } else {
    filteredShops.value = [...shops.value]
    
    // 检查是否全部店铺都被选中
    if (queryParams.shopIds && shops.value.length > 0) {
      const validShopIds = queryParams.shopIds.filter(id => id !== null) as number[];
      isAllSelected.value = validShopIds.length === shops.value.length &&
        shops.value.every(shop => shop.shopId !== null && validShopIds.includes(shop.shopId));
    }
  }
}

// 处理清除所有店铺选择
const handleClearShops = () => {
  // 重置全选状态
  isAllSelected.value = false
}

// 处理店铺变更
const handleShopChange = async (shopIds: number[]) => {
  // 检查是否点击了全选选项
  if (shopIds.includes(-1)) {
    // 如果当前不是全选状态，则进行全选
    if (!isAllSelected.value) {
      // 标记为全选状态
      isAllSelected.value = true
      
      // 全选所有店铺（过滤后的）
      let allShopIds: number[] = []
      
      // 如果有搜索关键词，只选择过滤后的店铺
      if (filterKeyword.value) {
        // 过滤掉可能的null值
        allShopIds = filteredShops.value
          .filter(shop => shop.shopId !== null)
          .map(shop => shop.shopId as number);
      } else {
        // 否则选择所有店铺，同样过滤掉null值
        allShopIds = shops.value
          .filter(shop => shop.shopId !== null)
          .map(shop => shop.shopId as number);
      }
      
      // 更新选中的店铺，并移除全选选项值(-1)
      queryParams.shopIds = allShopIds
      
      // 避免-1被包含在选中项中
      setTimeout(() => {
        if (queryParams.shopIds && queryParams.shopIds.includes(-1)) {
          const index = queryParams.shopIds.indexOf(-1)
          if (index > -1) {
            queryParams.shopIds.splice(index, 1)
          }
        }
      }, 0)
    } else {
      // 如果当前已经是全选状态，则取消全选
      isAllSelected.value = false
      queryParams.shopIds = []
    }
  } else {
    // 如果选择了具体的店铺且数量与总店铺数相同，标记为全选状态
    const totalShops = filterKeyword.value ? 
      filteredShops.value.filter(shop => shop.shopId !== null).length : 
      shops.value.filter(shop => shop.shopId !== null).length;
    isAllSelected.value = shopIds.length > 0 && shopIds.length === totalShops
  }
  
  // 当店铺选择变更时，清空备货仓组选择
  advancedQuery.warehouseGroupId = undefined
  
  // 如果没有选择店铺，清空仓库组列表
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    warehouseGroups.value = []
    return
  }
  
  // 加载选中店铺的仓库组（支持多店铺）
  if (queryParams.shopIds.length === 1) {
    // 单店铺模式，确保shopId不为null
    const shopId = queryParams.shopIds[0];
    if (typeof shopId === 'number') {
      await loadWarehouseGroups(shopId)
    }
  } else if (queryParams.shopIds.length > 0) {
    // 多店铺模式，过滤掉可能的null值
    const validShopIds = queryParams.shopIds.filter(id => typeof id === 'number') as number[];
    if (validShopIds.length > 0) {
      await loadMultipleWarehouseGroups(validShopIds)
    }
  }
}

// 加载单个店铺的备货仓组列表
const loadWarehouseGroups = async (shopId: number) => {
  warehouseGroupsLoading.value = true
  try {
    const res = await getWarehouseGroups(shopId)
    warehouseGroups.value = res.data || [] // 确保是数组
  } catch (error) {
    console.error('获取备货仓组列表失败', error)
    ElMessage.error('获取备货仓组列表失败')
    warehouseGroups.value = []
  } finally {
    warehouseGroupsLoading.value = false
  }
}

// 加载多个店铺的备货仓组列表
const loadMultipleWarehouseGroups = async (shopIds: number[]) => {
  warehouseGroupsLoading.value = true
  try {
    // 需要在API中添加支持多店铺的方法
    const res = await getWarehouseGroupsByShopIds(shopIds)
    warehouseGroups.value = res.data || [] // 确保是数组
  } catch (error) {
    console.error('获取多店铺备货仓组列表失败', error)
    ElMessage.error('获取多店铺备货仓组列表失败')
    warehouseGroups.value = []
  } finally {
    warehouseGroupsLoading.value = false
  }
}

// 销量标签显示计算属性
const saleVolumeLabel = computed(() => {
  switch (advancedQuery.saleVolumeTimeRange) {
    case 1:
      return '今日销量范围'
    case 2:
      return '近7日销量范围'
    case 3:
      return '近30天销量范围'
    default:
      return '销量范围'
  }
})

// 处理销量时间范围变化
const handleSaleVolumeTimeRangeChange = () => {
  // 切换时间范围时清空销量范围
  advancedQuery.saleVolumeMin = undefined
  advancedQuery.saleVolumeMax = undefined
}

// 查询数据
const handleQuery = async () => {
  // 检查是否选择了店铺
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请至少选择一个店铺');
    return;
  }
  
  // 准备基本查询参数
  const apiParams: LocalSalesRequestParams = { 
    pageNo: queryParams.pageNo,
    pageSize: queryParams.pageSize,
    shopIds: queryParams.shopIds,
    productNameKeyword: queryParams.productNameKeyword,
    isCustomGoods: queryParams.isCustomGoods,
    settlementType: queryParams.settlementType,
    purchaseStockType: queryParams.purchaseStockType,
    hotTag: queryParams.hotTag,
    quickFilter: quickFilter.value || undefined, // 添加快速筛选参数
    // spuIdList, skcIdList, skuIdList 会在下面处理
  };
  
  // 添加排序参数（只有在有效值存在时才添加）
  if (queryParams.sortField !== undefined && queryParams.sortDirection) {
    apiParams.sortField = queryParams.sortField;
    apiParams.sortDirection = queryParams.sortDirection;
    console.log('添加排序参数:', queryParams.sortField, queryParams.sortDirection);
  }
  
  // 处理SPU ID列表 - 检查空字符串
  if (queryParams.spuIdList) {
    apiParams.spuIdList = queryParams.spuIdList;
  }
  
  // 处理SKC ID列表 - 检查空字符串
  if (queryParams.skcIdList) {
    apiParams.skcIdList = queryParams.skcIdList;
  }
  
  // 添加高级查询参数（只有在显示高级查询时才添加）
  if (showAdvanced) {
    // 处理SKC和SKU货号字符串
    if (advancedQuery.skcExtCodeListStr && advancedQuery.skcExtCodeListStr.trim()) {
      apiParams.skcExtCodeList = advancedQuery.skcExtCodeListStr.trim(); // 使用 DTO 中已有的 skcExtCodeList 字段
    }
     if (advancedQuery.skuExtCodeListStr && advancedQuery.skuExtCodeListStr.trim()) {
      apiParams.skuExtCodeList = advancedQuery.skuExtCodeListStr.trim(); // 使用 DTO 中已有的 skuExtCodeList 字段
    }
    
    // 处理数组类型参数（只添加非空数组）
    ['inventoryRegionList', 'pictureAuditStatusList', 
      'supplyStatusList', 'stockStatusList', 'closeJitStatus'].forEach(key => { // 添加 closeJitStatus
      if (advancedQuery[key] && advancedQuery[key].length > 0) {
        apiParams[key] = advancedQuery[key];
      }
    });
    
    // 处理布尔类型参数（只添加已设置的值）
    ['isLack', 'suggestCloseJit', 'availableProduceNumGreaterThanzero', 'autoCloseJit'].forEach(key => {
      if (advancedQuery[key] !== undefined) {
        apiParams[key] = advancedQuery[key];
      }
    });
    
    // 处理数值类型参数（只添加已设置的值）
    ['onSalesDurationOfflineGte', 'onSalesDurationOfflineLte',
      'minRemanentInventoryNum', 'maxRemanentInventoryNum', 'todaySaleVolumMin', 
      'todaySaleVolumMax', 'hotTag', 'warehouseGroupId'].forEach(key => {
      // 检查是否为数字0，如果是，也应包含
      if (advancedQuery[key] !== undefined && advancedQuery[key] !== null && advancedQuery[key] !== '') {
         apiParams[key] = advancedQuery[key];
      }
    });

    // 处理可售天数查询条件
    if (advancedQuery.availableSaleDaysValue !== undefined && advancedQuery.availableSaleDaysValue !== null) {
      if (advancedQuery.availableSaleDaysOp === 'gte') {
        apiParams.minAvailableSaleDays = advancedQuery.availableSaleDaysValue;
      } else {
        apiParams.maxAvailableSaleDays = advancedQuery.availableSaleDaysValue;
      }
    }
    
    // 处理销量范围查询条件
    if (advancedQuery.saleVolumeTimeRange !== undefined) {
      apiParams.saleVolumeTimeRange = advancedQuery.saleVolumeTimeRange;
      
      if (advancedQuery.saleVolumeMin !== undefined || advancedQuery.saleVolumeMax !== undefined) {
        switch (advancedQuery.saleVolumeTimeRange) {
          case 1: // 今日
            if (advancedQuery.saleVolumeMin !== undefined) {
              apiParams.todaySaleVolumMin = advancedQuery.saleVolumeMin;
            }
            if (advancedQuery.saleVolumeMax !== undefined) {
              apiParams.todaySaleVolumMax = advancedQuery.saleVolumeMax;
            }
            break;
          case 2: // 近7日
            if (advancedQuery.saleVolumeMin !== undefined) {
              apiParams.lastSevenDaysSaleVolumeMin = advancedQuery.saleVolumeMin;
            }
            if (advancedQuery.saleVolumeMax !== undefined) {
              apiParams.lastSevenDaysSaleVolumeMax = advancedQuery.saleVolumeMax;
            }
            break;
          case 3: // 近30天
            if (advancedQuery.saleVolumeMin !== undefined) {
              apiParams.lastThirtyDaysSaleVolumeMin = advancedQuery.saleVolumeMin;
            }
            if (advancedQuery.saleVolumeMax !== undefined) {
              apiParams.lastThirtyDaysSaleVolumeMax = advancedQuery.saleVolumeMax;
            }
            break;
        }
      }
    }
  }
  
  loading.value = true
  try {
    const res = await getLocalSalesList(apiParams)
    salesList.value = res.data.list || []
    total.value = res.data.total || 0
  } catch (error) {
    console.error('获取销售数据失败', error)
    ElMessage.error('获取销售数据失败')
  } finally {
    loading.value = false
  }
}

// 处理快速筛选
const handleQuickFilter = (filter: string) => {
  if (quickFilter.value === filter) {
    // 如果点击了已激活的筛选按钮，取消筛选
    quickFilter.value = null
  } else {
    // 设置新的筛选类型
    quickFilter.value = filter
  }
  
  // 重置分页到第一页
  queryParams.pageNo = 1
  
  // 执行查询
  handleQuery()
}

// 重置查询条件
const resetQuery = () => {
  // 保留店铺选择
  // queryParams.shopIds = [] 
  
  // 重置全选状态
  isAllSelected.value = false
  
  queryParams.spuIdList = undefined
  queryParams.skcIdList = undefined
  queryParams.productNameKeyword = ''
  queryParams.isCustomGoods = undefined
  queryParams.settlementType = undefined
  queryParams.purchaseStockType = undefined
  queryParams.hotTag = undefined
  queryParams.sortField = undefined // 重置排序字段
  queryParams.sortDirection = undefined // 重置排序方向
  
  // 重置临时输入变量
  tempInputs.spuIdListStr = ''
  tempInputs.skcIdListStr = ''
  
  // 重置高级查询表单的字段
  Object.keys(advancedQuery).forEach(key => {
    if (Array.isArray(advancedQuery[key])) {
      advancedQuery[key] = []
    } else if (typeof advancedQuery[key] === 'string') { // 处理字符串类型
      advancedQuery[key] = ''
    } else if (key !== 'hotTag') { // Keep hotTag logic in queryParams
      advancedQuery[key] = undefined
    }
  })
  
  // 确保特定字段被显式设置为undefined
  advancedQuery.autoCloseJit = undefined
  advancedQuery.warehouseGroupId = undefined
  
  // 重置可售天数查询条件的操作符为默认值
  advancedQuery.availableSaleDaysOp = 'gte'
  
  // 重置日期范围选择器 (如果将来添加)
  // dateRange.value = [] 
  
  // 重置完成后，可以考虑重新获取数据，或者让用户手动点击查询
  // handleQuery() 
  
  // 清除快速筛选状态
  quickFilter.value = null
}

// 导出Excel
const handleExport = () => {
  // 检查是否选择了店铺
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请至少选择一个店铺');
    return;
  }
  
  // 检查是否有数据
  if (salesList.value.length === 0 && total.value === 0) {
    ElMessage.warning('暂无数据可导出，请先执行查询');
    return;
  }
  
  // 生成默认文件名：店铺备注 + 日期时间
  let fileName = '本地销售数据';
  
  // 获取选中店铺的备注
  if (shops.value && shops.value.length > 0) {
    // 使用非空断言，因为前面已经检查过shopIds是否存在
    const shopIds = queryParams.shopIds!;
    // 查找选中店铺的备注，过滤掉shopId为null的店铺
    const selectedShops = shops.value.filter(shop => 
      shop.shopId !== null && shopIds.includes(shop.shopId)
    );
    if (selectedShops.length > 0) {
      // 提取店铺备注，最多使用3个店铺备注，避免文件名过长
      const shopRemarks = selectedShops.slice(0, 3).map(shop => {
        // 优先使用remark，如果不存在则使用shopName
        return (shop.remark && shop.remark.trim()) ? shop.remark : shop.shopName;
      });
      fileName = shopRemarks.join('-');
      
      // 如果选择的店铺超过3个，添加省略标记
      if (selectedShops.length > 3) {
        fileName += '等';
      }
    }
  }
  
  // 添加日期时间后缀
  const now = new Date();
  const dateStr = now.getFullYear() + 
    (now.getMonth() + 1).toString().padStart(2, '0') + 
    now.getDate().toString().padStart(2, '0');
  const timeStr = now.getHours().toString().padStart(2, '0') + 
    now.getMinutes().toString().padStart(2, '0');
  
  fileName += '_' + dateStr + timeStr;
  
  console.log('导出文件名:', fileName); // 添加调试输出
  
  // 显示导出对话框
  exportDialog.fileName = fileName;
  exportDialog.visible = true;
}

// 创建导出任务函数 - 提供给ExportDialog组件使用
const createExportTask = async (params: any) => {
  try {
    // 创建导出任务
    const response = await createSalesExportTask(params);
    
    if (response.code !== 200) {
      if (params.taskId) {
        // 通知任务失败
        exportTaskStore.failTask(params.taskId, response.message || '创建导出任务失败');
      }
      return { success: false };
    }
    
    // 获取任务ID
    const serverTaskId = response.data.taskId;
    if (!serverTaskId) {
      if (params.taskId) {
        exportTaskStore.failTask(params.taskId, '获取导出任务ID失败');
      }
      return { success: false };
    }
    
    // 开始轮询任务进度
    startExportProgressPolling(serverTaskId, params.taskId);
    
    return { success: true, data: { taskId: serverTaskId } };
  } catch (error: any) {
    console.error('导出失败:', error);
    if (params.taskId) {
      exportTaskStore.failTask(params.taskId, error.message || '导出请求失败');
    }
    return { success: false };
  }
}

// 轮询导出进度
const startExportProgressPolling = (serverTaskId: string, clientTaskId?: string) => {
  if (!clientTaskId) return;
  
  let pollingTimer: number | null = null;
  let failedAttempts = 0;
  const MAX_FAILED_ATTEMPTS = 3;
  
  // 创建轮询检查进度的函数
  const checkExportProgress = () => {
    getSalesExportProgress(serverTaskId)
      .then(progressRes => {
        // 重置失败计数
        failedAttempts = 0;
        
        const progress = progressRes.data;
        
        // 更新任务进度
        if (clientTaskId) {
          exportTaskStore.updateTaskProgress(clientTaskId, progress.progress);
        }
        
        // 根据状态处理
        if (progress.status === 'completed') {
          // 下载文件
          downloadExportFile(serverTaskId, clientTaskId);
        } else if (progress.status === 'failed') {
          if (clientTaskId) {
            exportTaskStore.failTask(clientTaskId, progress.message || '导出失败');
          }
          if (pollingTimer) {
            clearTimeout(pollingTimer);
            pollingTimer = null;
          }
        } else {
          // 继续轮询
          pollingTimer = window.setTimeout(() => {
            checkExportProgress();
          }, 1000) as unknown as number;
        }
      })
      .catch(err => {
        failedAttempts++;
        console.error('检查导出进度失败:', err);
        
        if (failedAttempts >= MAX_FAILED_ATTEMPTS) {
          if (clientTaskId) {
            exportTaskStore.failTask(clientTaskId, '无法获取导出进度');
          }
          if (pollingTimer) {
            clearTimeout(pollingTimer);
            pollingTimer = null;
          }
        } else {
          // 失败后等待稍长时间再重试
          pollingTimer = window.setTimeout(() => {
            checkExportProgress();
          }, 2000) as unknown as number;
        }
      });
  };
  
  // 开始检查进度
  checkExportProgress();
}

// 下载导出的文件
const downloadExportFile = (serverTaskId: string, clientTaskId: string) => {
  // 使用fetch API下载文件并携带token
  const token = getToken();
  const downloadUrl = `${import.meta.env.VITE_API_BASE_URL}/local/sales/downloadExcel/${serverTaskId}`;
  
  fetch(downloadUrl, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`);
    }
    return response.blob();
  })
  .then(blob => {
    // 标记任务完成
    exportTaskStore.completeTask(clientTaskId);
    
    // 创建下载链接并触发下载
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = `${exportDialog.fileName}.xlsx`;
    document.body.appendChild(a);
    a.click();
    
    // 清理资源
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    
    ElMessage.success('文件下载已开始');
  })
  .catch(error => {
    exportTaskStore.failTask(clientTaskId, '下载文件失败: ' + error.message);
    ElMessage.error('下载文件失败: ' + error.message);
  });
}

// 分页大小改变
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  handleQuery()
}

// 当前页改变
const handleCurrentChange = (page) => {
  queryParams.pageNo = page
  handleQuery()
}

// 表格合并单元格方法
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  // 需要合并的列：序号(0), 店铺(1), 商品信息(2)
  const skcMergeColumnIndices = [0, 1, 2];

  if (skcMergeColumnIndices.includes(columnIndex)) {
    // 处理垂直合并的列 (店铺, 商品信息)
    if (!row.isSkuRow && !row.isSummaryRow) { // SKC 行
      // 计算 rowspan: 1 (SKC) + skuCount (SKUs) + 1 (合计行，如果存在)
      const rowspan = 1 + row.skuCount + (row.skuCount > 0 ? 1 : 0);
      return {
        rowspan: rowspan > 0 ? rowspan : 1, // 确保 rowspan 至少为 1
        colspan: 1
      };
    } else { // SKU 行或合计行
      // 这些单元格被 SKC 行的 rowspan 覆盖
      return {
        rowspan: 0,
        colspan: 0
      };
    }
  } else {
    // 处理非垂直合并的列 (SKU 信息及之后)
    if (!row.isSkuRow && !row.isSummaryRow) { // SKC 行
      // 如果 SKC 行有 SKU 或合计行，则隐藏其在非合并列中的单元格
      if (row.skuCount > 0) {
        return { rowspan: 0, colspan: 0 };
      } else {
        // 如果 SKC 没有 SKU，理论上不应该发生，但也隐藏
         return { rowspan: 0, colspan: 0 };
      }
    } else { // SKU 行或合计行
      // 正常显示
      return {
        rowspan: 1,
        colspan: 1
      };
    }
  }
};

// 表格行样式
const rowClassName = ({ row }) => {
  if (row.isSummaryRow) {
    return 'summary-row';
  }
  return row.isSkuRow ? 'sku-row' : '';
}

// 组件挂载时获取店铺列表
onMounted(() => {
  getShops()
})

// 处理排序
const handleSort = (field: number, direction: string) => {
  // 如果已经是这个排序字段和方向，则取消排序
  if (queryParams.sortField === field && queryParams.sortDirection === direction) {
    queryParams.sortField = undefined;
    queryParams.sortDirection = undefined;
  } else {
    // 设置新的排序字段和方向
    queryParams.sortField = field;
    queryParams.sortDirection = direction;
  }
  
  // 重置分页到第一页
  queryParams.pageNo = 1;
  
  // 重新查询数据
  handleQuery();
}
</script>

<style scoped>
/* 使用API页面的样式 */
.sales-data-container {
  height: calc(100vh - 84px);
  display: flex;
  flex-direction: column;
  background-color: #f0f2f5;
}

/* 各类输入框组合样式 */
.input-with-select {
  display: flex;
  align-items: center;
  gap: 2px; /* 减小间距 */
}

.site-duration-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.site-duration-separator {
  margin: 0 2px; /* 减小间距 */
  color: #909399;
}

.sales-query-range-container {
  display: flex;
  align-items: center;
  gap: 2px; /* 减小间距 */
}

.sales-query-range-separator {
  margin: 0 2px; /* 减小间距 */
  color: var(--el-text-color-secondary);
}

/* 按钮区域样式 */
.action-buttons-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0 15px;
  padding-top: 5px;
  border-top: 1px solid #ebeef5;
}

.left-buttons {
  display: flex;
  align-items: center;
}

.right-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 店铺信息相关样式 */
.shop-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.shop-name {
  font-size: 14px;
  font-weight: bold;
}

.shop-remark {
  font-size: 14px;
  color: #409EFF;
  line-height: 1.3;
  word-break: break-all;
}

.form-table-card {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-bottom: 0;
  border-bottom: none;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

.form-table-card :deep(.el-card__body) {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  padding: 15px 20px 15px 20px;
  overflow: hidden;
}

/* 快速筛选区域样式 */
.quick-filter-container {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  background-color: #f5f7fa;
  padding: 10px 15px;
  border-radius: 4px;
}

.quick-filter-title {
  font-weight: bold;
  margin-right: 15px;
  color: #606266;
}

.quick-filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

/* 简化紧凑型表单样式，移除强制高度和行高 */
.compact-form :deep(.el-form-item) {
  margin-bottom: 5px; /* 减小底部间距 */
  margin-right: 2px; /* 减小右侧间距 */
  vertical-align: middle;
}

.compact-form :deep(.el-form-item__label) {
  font-size: 13px;
  padding-right: 3px;
  line-height: normal;
}

.compact-form :deep(.el-input--small .el-input__inner),
.compact-form :deep(.el-select--small .el-input__wrapper) {
  /* 移除强制高度、行高和阴影 */
  /* height: 30px !important; */
  /* line-height: 30px !important; */
  /* box-shadow: none !important; */
  /* 确保 select 的 wrapper 垂直居中内容 */
  display: inline-flex;
  align-items: center;
}

/* --- 调整 Prepend Select 样式 --- */
/* 使其适应默认控件高度 */
.compact-form :deep(.el-input-group__prepend) {
  background-color: var(--el-fill-color-light);
  padding: 0;
  border-color: var(--el-border-color);
  /* 让 prepend 容器适应内容高度 */
  display: flex;
  align-items: center;
}

.compact-form :deep(.el-input-group__prepend .el-select) {
  margin: 0;
}

/* 调整 Select Wrapper 样式 */
.compact-form :deep(.el-input-group__prepend .el-select .el-input__wrapper) {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 5px 0 11px;
  /* 让 wrapper 高度自适应 */
  height: auto !important;
  /* line-height: 28px !important; */ /* 移除固定行高 */
}

/* 调整 Select Input 样式 */
.compact-form :deep(.el-input-group__prepend .el-select .el-input__inner) {
  /* height: 28px !important; */ /* 移除固定高度 */
  /* line-height: 28px !important; */ /* 移除固定行高 */
  padding: 0 !important;
  text-align: center;
}
/* --- End Prepend Select Fix --- */

.compact-form :deep(.el-form-item__content) {
  /* 移除强制 line-height */
  /* line-height: 30px; */
  line-height: normal; /* 使用默认行高 */
  vertical-align: middle; /* 尝试保持垂直居中对齐 */
  /* display: flex; */ /* 可能需要 flex 来对齐复杂内容 */
  /* align-items: center; */
}


/* 其他样式保持不变 */
.table-wrapper {
  flex-grow: 1;
  overflow: auto;
  position: relative;
}

.bottom-pagination-bar {
  flex-shrink: 0;
  padding: 5px 20px;
  background-color: #fff;
  border-top: 1px solid #ebeef5;
  text-align: right;
}

.product-info {
  display: flex;
  align-items: flex-start;
}

.product-image {
  width: 70px;
  height: 70px;
  margin-right: 10px;
  flex-shrink: 0;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.product-image:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.info-content {
  display: flex;
  flex-direction: column;
  font-size: 12px;
  color: #606266;
  overflow: hidden;
}

.product-name {
  font-weight: bold;
  margin-bottom: 4px;
  font-size: 13px;
  white-space: normal;
  word-break: break-all;
}

.el-table :deep(th) > .cell {
  text-align: center;
}

.el-table :deep(.cell) {
  padding-left: 8px;
  padding-right: 8px;
}

/* 为SKU行添加背景色 */
.el-table :deep(.sku-row) {
  background-color: #f8fafd;
}

/* SKU行样式 */
:deep(.sku-row) {
  background-color: #f8fafc;
  font-size: 13px;
}

:deep(.sku-row td) {
  padding-top: 4px;
  padding-bottom: 4px;
}

/* 合计行样式 */
:deep(.summary-row) {
  background-color: #f0f7ff;
  font-weight: bold;
  font-size: 13px;
}

:deep(.summary-row td) {
  padding-top: 4px;
  padding-bottom: 4px;
  border-top: 1px dashed #dcdfe6;
  border-bottom: 1px dashed #dcdfe6;
}

/* 规格合计信息样式 */
.sku-summary {
  padding: 5px 0;
}

.sku-summary-title {
  font-weight: bold;
  color: #409EFF;
}

/* 排序相关样式 */
.sortable-header {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.sort-icons {
  display: flex;
  flex-direction: column;
  margin-left: 5px;
  height: 24px;
}

.sort-icons .el-icon {
  cursor: pointer;
  font-size: 14px;
  color: #c0c4cc;
  padding: 2px;
  margin: -2px 0;
}

.sort-icons .el-icon.active {
  color: #409EFF;
  transform: scale(1.2);
}
</style> 