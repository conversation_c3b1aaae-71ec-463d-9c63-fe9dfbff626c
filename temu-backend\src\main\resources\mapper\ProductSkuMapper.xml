<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xiao.temu.modules.product.mapper.ProductSkuMapper">
    
    <resultMap id="BaseResultMap" type="com.xiao.temu.modules.product.entity.ProductSku">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="product_id" property="productId"/>
        <result column="product_skc_id" property="productSkcId"/>
        <result column="product_sku_id" property="productSkuId"/>
        <result column="color" property="color"/>
        <result column="size" property="size"/>
        <result column="status" property="status"/>
        <result column="sync_time" property="syncTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    
    <sql id="Base_Column_List">
        id, shop_id, product_id, product_skc_id, product_sku_id, color, size, 
        status, sync_time, create_time, update_time
    </sql>
    
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO product_sku (
            shop_id, product_id, product_skc_id, product_sku_id, 
            color, size, status, sync_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.shopId}, #{item.productId}, #{item.productSkcId}, #{item.productSkuId}, 
                #{item.color}, #{item.size}, #{item.status}, #{item.syncTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE 
            color = VALUES(color),
            size = VALUES(size),
            status = VALUES(status),
            sync_time = VALUES(sync_time),
            update_time = NOW()
    </insert>
    
    <select id="selectByShopIdAndSkcId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM product_sku
        WHERE shop_id = #{shopId} AND product_skc_id = #{productSkcId}
    </select>
    
    <select id="selectByShopId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM product_sku
        WHERE shop_id = #{shopId}
    </select>
    
    <delete id="deleteByShopId">
        DELETE FROM product_sku
        WHERE shop_id = #{shopId}
    </delete>
    
    <select id="countByShopId" resultType="java.lang.Integer">
        SELECT COUNT(1) 
        FROM product_sku
        WHERE shop_id = #{shopId}
    </select>
    
    <select id="getProductSkuCountByShopId" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT product_sku_id) 
        FROM product_sku
        WHERE shop_id = #{shopId}
    </select>
    
</mapper> 