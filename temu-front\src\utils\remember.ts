/**
 * 记住我功能工具函数
 * 用于存储和获取用户登录信息
 */
import { encrypt, decrypt } from './encrypt'

// 存储键名
const REMEMBER_ME_KEY = 'temu_remember_me'

// 用户信息接口
interface RememberMeInfo {
  username: string
  password: string
  rememberMe: boolean
  timestamp: number // 添加时间戳，用于控制有效期
}

/**
 * 保存用户登录信息
 * @param username 用户名
 * @param password 密码
 * @param rememberMe 是否记住
 */
export function saveLoginInfo(username: string, password: string, rememberMe: boolean): void {
  if (!rememberMe) {
    // 如果不记住，则清除之前存储的信息
    removeLoginInfo()
    return
  }
  
  // 创建要保存的信息对象
  const info: RememberMeInfo = {
    username,
    password,
    rememberMe,
    timestamp: new Date().getTime() // 记录当前时间戳
  }
  
  // 加密信息并保存到localStorage
  const encryptedData = encrypt(JSON.stringify(info))
  localStorage.setItem(REMEMBER_ME_KEY, encryptedData)
}

/**
 * 获取保存的登录信息
 * @returns 登录信息，如果没有或已过期则返回null
 */
export function getLoginInfo(): { username: string; password: string; rememberMe: boolean } | null {
  try {
    // 从localStorage获取加密数据
    const encryptedData = localStorage.getItem(REMEMBER_ME_KEY)
    if (!encryptedData) {
      return null
    }
    
    // 解密数据
    const decryptedData = decrypt(encryptedData)
    if (!decryptedData) {
      return null
    }
    
    // 解析JSON数据
    const info: RememberMeInfo = JSON.parse(decryptedData)
    
    // 验证数据有效性
    if (!info || !info.username || !info.password) {
      return null
    }
    
    // 检查是否过期（默认30天有效期）
    const now = new Date().getTime()
    const validDuration = 30 * 24 * 60 * 60 * 1000 // 30天的毫秒数
    if (now - info.timestamp > validDuration) {
      // 已过期，删除数据
      removeLoginInfo()
      return null
    }
    
    return {
      username: info.username,
      password: info.password,
      rememberMe: info.rememberMe
    }
  } catch (error) {
    console.error('获取登录信息失败:', error)
    return null
  }
}

/**
 * 移除保存的登录信息
 */
export function removeLoginInfo(): void {
  localStorage.removeItem(REMEMBER_ME_KEY)
} 