package com.xiao.temu.modules.purchaseorderv.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xiao.temu.common.constant.MessageConstants;
import com.xiao.temu.common.constant.PurchaseOrderNotificationConstants;
import com.xiao.temu.modules.message.dto.SendMessageDTO;
import com.xiao.temu.modules.message.service.MessageService;
import com.xiao.temu.modules.operation.mapper.GroupMemberMapper;
import com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderNotificationConfig;
import com.xiao.temu.modules.shop.dto.ShopDTO;
import com.xiao.temu.modules.shop.mapper.ShopGroupAssignmentMapper;
import com.xiao.temu.modules.shop.entity.ShopGroupAssignment;
import com.xiao.temu.modules.operation.entity.GroupMember;
import com.xiao.temu.modules.purchaseorderv.entity.ProductionProgress;
import com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderNotification;
import com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderV;
import com.xiao.temu.modules.purchaseorderv.mapper.ProductionProgressMapper;
import com.xiao.temu.modules.purchaseorderv.mapper.PurchaseOrderNotificationMapper;
import com.xiao.temu.modules.purchaseorderv.mapper.PurchaseOrderVMapper;
import com.xiao.temu.modules.purchaseorderv.service.PurchaseOrderNotificationService;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.system.mapper.SysUserMapper;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.production.mapper.ProductionGroupShopAssignmentMapper;
import com.xiao.temu.modules.production.mapper.ProductionGroupMemberMapper;
import com.xiao.temu.modules.production.mapper.ProductionGroupMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.xiao.temu.modules.purchaseorderv.dto.BatchPurchaseOrderNotificationData;
import com.xiao.temu.modules.purchaseorderv.dto.BatchPurchaseOrderNotificationData.PurchaseOrderNotificationItem;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

/**
 * 备货单通知服务实现类
 */
@Slf4j
@Service
public class PurchaseOrderNotificationServiceImpl implements PurchaseOrderNotificationService {

    @Autowired
    private PurchaseOrderVMapper purchaseOrderVMapper;
    
    @Autowired
    private ProductionProgressMapper productionProgressMapper;
    
    @Autowired
    private PurchaseOrderNotificationMapper notificationMapper;
    
    @Autowired
    private ShopGroupAssignmentMapper shopGroupAssignmentMapper;
    
    @Autowired
    private GroupMemberMapper groupMemberMapper;
    
    @Autowired
    private SysUserMapper userMapper;
    
    @Autowired
    private MessageService messageService;
    
    @Autowired
    private com.xiao.temu.modules.purchaseorderv.mapper.PurchaseOrderNotificationConfigMapper configMapper;
    
    @Autowired
    private ShopService shopService;
    
    @Autowired
    private ProductionGroupShopAssignmentMapper productionGroupShopAssignmentMapper;
    
    @Autowired
    private ProductionGroupMemberMapper productionGroupMemberMapper;
    
    @Autowired
    private ProductionGroupMapper productionGroupMapper;
    
    // 测试标志
    private boolean skipHoursCheck = false;
    private boolean skipDaysCheck = false;
    private boolean skipNotifyCountCheck = false;
    private String mockCurrentTime = null;
    
    @Override
    public void setSkipHoursCheck(boolean skipHoursCheck) {
        this.skipHoursCheck = skipHoursCheck;
    }
    
    @Override
    public void setSkipDaysCheck(boolean skipDaysCheck) {
        this.skipDaysCheck = skipDaysCheck;
    }
    
    @Override
    public void setSkipNotifyCountCheck(boolean skipNotifyCountCheck) {
        this.skipNotifyCountCheck = skipNotifyCountCheck;
    }
    
    @Override
    public void setMockCurrentTime(String mockCurrentTime) {
        this.mockCurrentTime = mockCurrentTime;
    }
    
    @Override
    public void resetTestFlags() {
        this.skipHoursCheck = false;
        this.skipDaysCheck = false;
        this.skipNotifyCountCheck = false;
        this.mockCurrentTime = null;
    }
    
    /**
     * 获取当前时间，支持模拟时间
     */
    private Date getCurrentTime() {
        if (mockCurrentTime != null) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return sdf.parse(mockCurrentTime);
            } catch (Exception e) {
                log.error("解析模拟时间失败：{}", mockCurrentTime, e);
            }
        }
        return new Date();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processJitSoonOverdueNotification() {
        log.info("开始处理JIT即将逾期通知");
        
        // 获取配置
        PurchaseOrderNotificationConfig config =
                configMapper.getByNotificationType(PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_SOON_OVERDUE);
        
        // 如果配置不存在或被禁用，则跳过
        if (config == null || !config.getEnabled()) {
            log.info("JIT即将逾期通知已禁用，跳过处理");
            return;
        }
        
        // 计算时间范围：未来24小时内要到货的JIT订单
        Date now = getCurrentTime();
        Date endTime = DateUtils.addHours(now, config.getJitTriggerHours() != null ? 
                config.getJitTriggerHours() : PurchaseOrderNotificationConstants.JIT_SOON_OVERDUE_HOURS);
        
        // 查询未来24小时内将到达最晚到货时间的JIT备货单
        List<PurchaseOrderV> jitSoonOverdueOrders = purchaseOrderVMapper.findJitOrdersAboutToExpire(now, endTime);
        log.info("找到{}个JIT即将逾期备货单", jitSoonOverdueOrders.size());
        
        if (jitSoonOverdueOrders.isEmpty()) {
            return;
        }
        
        // 按店铺分组，准备批量通知数据
        Map<Long, BatchPurchaseOrderNotificationData.ShopGroup> shopGroupMap = new HashMap<>();
        BatchPurchaseOrderNotificationData notificationData = new BatchPurchaseOrderNotificationData();
        notificationData.setNotifyTypeName("JIT备货单即将逾期");
        
        for (PurchaseOrderV order : jitSoonOverdueOrders) {
            try {
                // 检查是否已经存在通知记录
                PurchaseOrderNotification notification = notificationMapper.selectOne(
                        new QueryWrapper<PurchaseOrderNotification>()
                                .eq("shop_id", order.getShopId())
                                .eq("sub_purchase_order_sn", order.getSubPurchaseOrderSn())
                                .eq("notification_type", PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_SOON_OVERDUE)
                );
                
                // 处理通知记录
                if (notification == null) {
                    // 不存在通知记录，创建新记录
                    notification = new PurchaseOrderNotification();
                    notification.setShopId(order.getShopId());
                    notification.setSubPurchaseOrderSn(order.getSubPurchaseOrderSn());
                    notification.setNotificationType(PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_SOON_OVERDUE);
                    notification.setNotifyCount(0);
                    notification.setNotifyStatus(PurchaseOrderNotificationConstants.NOTIFY_STATUS_WAITING);
                    
                    // 获取店铺所属运营组ID
                    Long groupId = getShopOperationGroupId(order.getShopId());
                    notification.setGroupId(groupId);
                    notification.setCreateTime(now);
                    notification.setUpdateTime(now);
                    
                    // 保存通知记录
                    notificationMapper.insert(notification);
                }
                
                // 如果不跳过通知次数检查，则检查通知次数和状态
                int maxNotifyCount = config.getMaxNotifyCount() != null ? config.getMaxNotifyCount() : PurchaseOrderNotificationConstants.MAX_NOTIFY_COUNT;
                if (!skipNotifyCountCheck && (notification.getNotifyCount() >= maxNotifyCount || 
                    notification.getNotifyStatus() == PurchaseOrderNotificationConstants.NOTIFY_STATUS_COMPLETED)) {
                    log.info("通知次数已达最大值或已完成，不再发送通知，订单号：{}", order.getSubPurchaseOrderSn());
                    continue;
                }
                
                // 获取店铺信息
                ShopDTO shopDTO = shopService.getShopById(order.getShopId());
                Shop shop = shopDTO != null ? shopDTO.convertToShop() : null;
                
                // 获取店铺名称和备注
                String shopName = shop != null ? shop.getShopName() : "店铺" + order.getShopId();
                String shopRemark = shop != null && shop.getRemark() != null ? shop.getRemark() : "";
                
                // 获取或创建该店铺的组
                BatchPurchaseOrderNotificationData.ShopGroup shopGroup = shopGroupMap.computeIfAbsent(
                    order.getShopId(), 
                    k -> {
                        BatchPurchaseOrderNotificationData.ShopGroup group = new BatchPurchaseOrderNotificationData.ShopGroup();
                        group.setShopId(order.getShopId());
                        group.setShopName(shopName);
                        group.setShopRemark(shopRemark);
                        return group;
                    }
                );
                
                // 添加订单项
                BatchPurchaseOrderNotificationData.PurchaseOrderNotificationItem item = new BatchPurchaseOrderNotificationData.PurchaseOrderNotificationItem();
                item.setShopId(order.getShopId());
                item.setShopName(shopName);
                item.setSubPurchaseOrderSn(order.getSubPurchaseOrderSn());
                item.setProductName(order.getProductName());
                item.setExpectLatestArrivalTime(order.getExpectLatestArrivalTime());
                shopGroup.getItems().add(item);
                
            } catch (Exception e) {
                log.error("处理JIT即将逾期通知出错，订单号：{}，错误：{}", order.getSubPurchaseOrderSn(), e.getMessage(), e);
            }
        }
        
        // 将所有分组添加到通知数据中
        notificationData.getShopGroups().addAll(shopGroupMap.values());
        notificationData.setTotalOrderCount(shopGroupMap.values().stream()
                .mapToInt(group -> group.getItems().size())
                .sum());
        
        // 批量发送通知
        if (!shopGroupMap.isEmpty()) {
            Map<Long, BatchPurchaseOrderNotificationData> notificationDataMap = new HashMap<>();
            notificationDataMap.put(1L, notificationData); // 使用一个固定的键，因为我们只会发送一条聚合消息
            sendBatchNotification(notificationDataMap, PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_SOON_OVERDUE);
        }
        
        log.info("JIT即将逾期通知处理完成");
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processJitOverdueNotification() {
        log.info("开始处理JIT已逾期通知");
        
        // 获取配置
        PurchaseOrderNotificationConfig config = 
                configMapper.getByNotificationType(PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_OVERDUE);
        
        // 如果配置不存在或被禁用，则跳过
        if (config == null || !config.getEnabled()) {
            log.info("JIT已逾期通知已禁用，跳过处理");
            return;
        }
        
        // 计算已逾期的时间范围：从昨天开始往前三天的逾期订单
        Date now = getCurrentTime();
        Date yesterday = DateUtils.addDays(now, -1);
        Date startTime = DateUtils.addDays(yesterday, -3);  // 三天前
        
        // 查询已逾期的JIT备货单
        List<PurchaseOrderV> jitOverdueOrders = purchaseOrderVMapper.findExpiredJitOrders(startTime, yesterday);
        log.info("找到{}个JIT已逾期备货单", jitOverdueOrders.size());
        
        if (jitOverdueOrders.isEmpty()) {
            return;
        }
        
        // 按店铺分组，准备批量通知数据
        Map<Long, BatchPurchaseOrderNotificationData.ShopGroup> shopGroupMap = new HashMap<>();
        BatchPurchaseOrderNotificationData notificationData = new BatchPurchaseOrderNotificationData();
        notificationData.setNotifyTypeName("JIT备货单已逾期");
        
        for (PurchaseOrderV order : jitOverdueOrders) {
            try {
                // 检查是否已经存在通知记录
                PurchaseOrderNotification notification = notificationMapper.selectOne(
                        new QueryWrapper<PurchaseOrderNotification>()
                                .eq("shop_id", order.getShopId())
                                .eq("sub_purchase_order_sn", order.getSubPurchaseOrderSn())
                                .eq("notification_type", PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_OVERDUE)
                );
                
                // 处理通知记录
                if (notification == null) {
                    // 不存在通知记录，创建新记录
                    notification = new PurchaseOrderNotification();
                    notification.setShopId(order.getShopId());
                    notification.setSubPurchaseOrderSn(order.getSubPurchaseOrderSn());
                    notification.setNotificationType(PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_OVERDUE);
                    notification.setNotifyCount(0);
                    notification.setNotifyStatus(PurchaseOrderNotificationConstants.NOTIFY_STATUS_WAITING);
                    
                    // 获取店铺所属运营组ID
                    Long groupId = getShopOperationGroupId(order.getShopId());
                    notification.setGroupId(groupId);
                    notification.setCreateTime(now);
                    notification.setUpdateTime(now);
                    
                    // 保存通知记录
                    notificationMapper.insert(notification);
                }
                
                // 如果不跳过通知次数检查，则检查通知次数和状态
                int maxNotifyCount = config.getMaxNotifyCount() != null ? config.getMaxNotifyCount() : PurchaseOrderNotificationConstants.MAX_NOTIFY_COUNT;
                if (!skipNotifyCountCheck && (notification.getNotifyCount() >= maxNotifyCount || 
                    notification.getNotifyStatus() == PurchaseOrderNotificationConstants.NOTIFY_STATUS_COMPLETED)) {
                    log.info("通知次数已达最大值或已完成，不再发送通知，订单号：{}", order.getSubPurchaseOrderSn());
                    continue;
                }
                
                // 获取店铺信息
                ShopDTO shopDTO = shopService.getShopById(order.getShopId());
                Shop shop = shopDTO != null ? shopDTO.convertToShop() : null;
                
                // 获取店铺名称和备注
                String shopName = shop != null ? shop.getShopName() : "店铺" + order.getShopId();
                String shopRemark = shop != null && shop.getRemark() != null ? shop.getRemark() : "";
                
                // 获取或创建该店铺的组
                BatchPurchaseOrderNotificationData.ShopGroup shopGroup = shopGroupMap.computeIfAbsent(
                    order.getShopId(), 
                    k -> {
                        BatchPurchaseOrderNotificationData.ShopGroup group = new BatchPurchaseOrderNotificationData.ShopGroup();
                        group.setShopId(order.getShopId());
                        group.setShopName(shopName);
                        group.setShopRemark(shopRemark);
                        return group;
                    }
                );
                
                // 添加订单项
                BatchPurchaseOrderNotificationData.PurchaseOrderNotificationItem item = new BatchPurchaseOrderNotificationData.PurchaseOrderNotificationItem();
                item.setShopId(order.getShopId());
                item.setShopName(shopName);
                item.setSubPurchaseOrderSn(order.getSubPurchaseOrderSn());
                item.setProductName(order.getProductName());
                item.setExpectLatestArrivalTime(order.getExpectLatestArrivalTime());
                shopGroup.getItems().add(item);
                
            } catch (Exception e) {
                log.error("处理JIT已逾期通知出错，订单号：{}，错误：{}", order.getSubPurchaseOrderSn(), e.getMessage(), e);
            }
        }
        
        // 将所有分组添加到通知数据中
        notificationData.getShopGroups().addAll(shopGroupMap.values());
        notificationData.setTotalOrderCount(shopGroupMap.values().stream()
                .mapToInt(group -> group.getItems().size())
                .sum());
        
        // 批量发送通知
        if (!shopGroupMap.isEmpty()) {
            Map<Long, BatchPurchaseOrderNotificationData> notificationDataMap = new HashMap<>();
            notificationDataMap.put(1L, notificationData); // 使用一个固定的键，因为我们只会发送一条聚合消息
            sendBatchNotification(notificationDataMap, PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_OVERDUE);
        }
        
        log.info("JIT已逾期通知处理完成");
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processNormalNotDeliveredNotification() {
        log.info("开始处理普通备货未发货通知");
        
        // 获取配置
        PurchaseOrderNotificationConfig config = 
                configMapper.getByNotificationType(PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_DELIVERED);
        
        // 如果配置不存在或被禁用，则跳过
        if (config == null || !config.getEnabled()) {
            log.info("普通备货未发货通知已禁用，跳过处理");
            return;
        }
        
        // 计算天数
        int days = config.getNormalTriggerDays() != null ? 
                config.getNormalTriggerDays() : PurchaseOrderNotificationConstants.NORMAL_NOT_DELIVERED_DAYS;
        
        // 计算5天前的时间
        Date now = getCurrentTime();
        Date createdBefore = DateUtils.addDays(now, -days);
        
        // 查询5天前创建但仍未发货的普通备货单
        List<PurchaseOrderV> normalNotDeliveredOrders = purchaseOrderVMapper.findLongTimeNotShippedOrders(createdBefore);
        log.info("找到{}个普通备货未发货订单", normalNotDeliveredOrders.size());
        
        if (normalNotDeliveredOrders.isEmpty()) {
            return;
        }
        
        // 按店铺分组，准备批量通知数据
        Map<Long, BatchPurchaseOrderNotificationData.ShopGroup> shopGroupMap = new HashMap<>();
        BatchPurchaseOrderNotificationData notificationData = new BatchPurchaseOrderNotificationData();
        notificationData.setNotifyTypeName("普通备货未发货");
        
        for (PurchaseOrderV order : normalNotDeliveredOrders) {
            try {
                // 检查是否已经存在通知记录
                PurchaseOrderNotification notification = notificationMapper.selectOne(
                        new QueryWrapper<PurchaseOrderNotification>()
                                .eq("shop_id", order.getShopId())
                                .eq("sub_purchase_order_sn", order.getSubPurchaseOrderSn())
                                .eq("notification_type", PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_DELIVERED)
                );
                
                // 处理通知记录
                if (notification == null) {
                    // 不存在通知记录，创建新记录
                    notification = new PurchaseOrderNotification();
                    notification.setShopId(order.getShopId());
                    notification.setSubPurchaseOrderSn(order.getSubPurchaseOrderSn());
                    notification.setNotificationType(PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_DELIVERED);
                    notification.setNotifyCount(0);
                    notification.setNotifyStatus(PurchaseOrderNotificationConstants.NOTIFY_STATUS_WAITING);
                    
                    // 获取店铺所属运营组ID
                    Long groupId = getShopOperationGroupId(order.getShopId());
                    notification.setGroupId(groupId);
                    notification.setCreateTime(now);
                    notification.setUpdateTime(now);
                    
                    // 保存通知记录
                    notificationMapper.insert(notification);
                }
                
                // 如果不跳过通知次数检查，则检查通知次数和状态
                int maxNotifyCount = config.getMaxNotifyCount() != null ? config.getMaxNotifyCount() : PurchaseOrderNotificationConstants.MAX_NOTIFY_COUNT;
                if (!skipNotifyCountCheck && (notification.getNotifyCount() >= maxNotifyCount || 
                    notification.getNotifyStatus() == PurchaseOrderNotificationConstants.NOTIFY_STATUS_COMPLETED)) {
                    log.info("通知次数已达最大值或已完成，不再发送通知，订单号：{}", order.getSubPurchaseOrderSn());
                    continue;
                }
                
                // 获取店铺信息
                ShopDTO shopDTO = shopService.getShopById(order.getShopId());
                Shop shop = shopDTO != null ? shopDTO.convertToShop() : null;
                
                // 获取店铺名称和备注
                String shopName = shop != null ? shop.getShopName() : "店铺" + order.getShopId();
                String shopRemark = shop != null && shop.getRemark() != null ? shop.getRemark() : "";
                
                // 获取或创建该店铺的组
                BatchPurchaseOrderNotificationData.ShopGroup shopGroup = shopGroupMap.computeIfAbsent(
                    order.getShopId(), 
                    k -> {
                        BatchPurchaseOrderNotificationData.ShopGroup group = new BatchPurchaseOrderNotificationData.ShopGroup();
                        group.setShopId(order.getShopId());
                        group.setShopName(shopName);
                        group.setShopRemark(shopRemark);
                        return group;
                    }
                );
                
                // 添加订单项
                BatchPurchaseOrderNotificationData.PurchaseOrderNotificationItem item = new BatchPurchaseOrderNotificationData.PurchaseOrderNotificationItem();
                item.setShopId(order.getShopId());
                item.setShopName(shopName);
                item.setSubPurchaseOrderSn(order.getSubPurchaseOrderSn());
                item.setProductName(order.getProductName());
                item.setPurchaseTime(order.getPurchaseTime());
                shopGroup.getItems().add(item);
                
            } catch (Exception e) {
                log.error("处理普通备货未发货通知出错，订单号：{}，错误：{}", order.getSubPurchaseOrderSn(), e.getMessage(), e);
            }
        }
        
        // 将所有分组添加到通知数据中
        notificationData.getShopGroups().addAll(shopGroupMap.values());
        notificationData.setTotalOrderCount(shopGroupMap.values().stream()
                .mapToInt(group -> group.getItems().size())
                .sum());
        
        // 批量发送通知
        if (!shopGroupMap.isEmpty()) {
            Map<Long, BatchPurchaseOrderNotificationData> notificationDataMap = new HashMap<>();
            notificationDataMap.put(1L, notificationData); // 使用一个固定的键，因为我们只会发送一条聚合消息
            sendBatchNotification(notificationDataMap, PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_DELIVERED);
        }
        
        log.info("普通备货未发货通知处理完成");
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processNormalNotReceivedNotification() {
        log.info("开始处理普通备货未到货通知");
        
        // 获取配置
        PurchaseOrderNotificationConfig config = 
                configMapper.getByNotificationType(PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_RECEIVED);
        
        // 如果配置不存在或被禁用，则跳过
        if (config == null || !config.getEnabled()) {
            log.info("普通备货未到货通知已禁用，跳过处理");
            return;
        }
        
        // 计算天数
        int days = config.getNormalTriggerDays() != null ? 
                config.getNormalTriggerDays() : PurchaseOrderNotificationConstants.NORMAL_NOT_RECEIVED_DAYS;
        
        // 计算N天前的时间
        Date now = getCurrentTime();
        Date shippedBefore = DateUtils.addDays(now, -days);
        
        // 查询生产进度表中N天前已完成发货但仍未收货的普通备货单
        // 注意：使用了关联查询和生产进度表的shipping_time
        List<PurchaseOrderV> normalNotReceivedOrders = purchaseOrderVMapper.findShippedButNotReceivedOrders(shippedBefore);
        log.info("找到{}个已发货但未到货的普通备货订单", normalNotReceivedOrders.size());
        
        if (normalNotReceivedOrders.isEmpty()) {
            return;
        }
        
        // 按店铺分组，准备批量通知数据
        Map<Long, BatchPurchaseOrderNotificationData.ShopGroup> shopGroupMap = new HashMap<>();
        BatchPurchaseOrderNotificationData notificationData = new BatchPurchaseOrderNotificationData();
        notificationData.setNotifyTypeName("普通备货未到货");
        
        // 批量查询订单对应的生产进度信息
        List<String> orderSnList = normalNotReceivedOrders.stream()
                .map(PurchaseOrderV::getSubPurchaseOrderSn)
                .collect(Collectors.toList());
        
        // 批量查询生产进度
        List<ProductionProgress> progressList = productionProgressMapper.selectList(
                new QueryWrapper<ProductionProgress>()
                        .in("sub_purchase_order_sn", orderSnList)
        );
        
        // 创建映射，便于快速查找
        Map<String, ProductionProgress> progressMap = progressList.stream()
                .collect(Collectors.toMap(ProductionProgress::getSubPurchaseOrderSn, p -> p, (p1, p2) -> p1));
        
            for (PurchaseOrderV order : normalNotReceivedOrders) {
            try {
                // 获取对应的生产进度信息
                ProductionProgress progress = progressMap.get(order.getSubPurchaseOrderSn());
                if (progress == null) {
                    log.warn("未找到对应的生产进度信息，订单号：{}", order.getSubPurchaseOrderSn());
                    continue;
                }
                
                // 检查是否已经存在通知记录
                PurchaseOrderNotification notification = notificationMapper.selectOne(
                        new QueryWrapper<PurchaseOrderNotification>()
                                .eq("shop_id", order.getShopId())
                                .eq("sub_purchase_order_sn", order.getSubPurchaseOrderSn())
                                .eq("notification_type", PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_RECEIVED)
                );
                
                // 处理通知记录
            if (notification == null) {
                    // 不存在通知记录，创建新记录
                notification = new PurchaseOrderNotification();
                notification.setShopId(order.getShopId());
                notification.setSubPurchaseOrderSn(order.getSubPurchaseOrderSn());
                    notification.setNotificationType(PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_RECEIVED);
                notification.setNotifyCount(0);
                notification.setNotifyStatus(PurchaseOrderNotificationConstants.NOTIFY_STATUS_WAITING);
                    
                    // 获取店铺所属运营组ID
                    Long groupId = getShopOperationGroupId(order.getShopId());
                notification.setGroupId(groupId);
                notification.setCreateTime(now);
                notification.setUpdateTime(now);
                
                // 保存通知记录
                notificationMapper.insert(notification);
            }
            
                // 如果不跳过通知次数检查，则检查通知次数和状态
                int maxNotifyCount = config.getMaxNotifyCount() != null ? config.getMaxNotifyCount() : PurchaseOrderNotificationConstants.MAX_NOTIFY_COUNT;
                if (!skipNotifyCountCheck && (notification.getNotifyCount() >= maxNotifyCount || 
                notification.getNotifyStatus() == PurchaseOrderNotificationConstants.NOTIFY_STATUS_COMPLETED)) {
                log.info("通知次数已达最大值或已完成，不再发送通知，订单号：{}", order.getSubPurchaseOrderSn());
                    continue;
                }
                
                // 获取店铺信息
                ShopDTO shopDTO = shopService.getShopById(order.getShopId());
                Shop shop = shopDTO != null ? shopDTO.convertToShop() : null;
                
                // 获取店铺名称和备注
                String shopName = shop != null ? shop.getShopName() : "店铺" + order.getShopId();
                String shopRemark = shop != null && shop.getRemark() != null ? shop.getRemark() : "";
                
                // 获取或创建该店铺的组
                BatchPurchaseOrderNotificationData.ShopGroup shopGroup = shopGroupMap.computeIfAbsent(
                    order.getShopId(), 
                    k -> {
                        BatchPurchaseOrderNotificationData.ShopGroup group = new BatchPurchaseOrderNotificationData.ShopGroup();
                        group.setShopId(order.getShopId());
                        group.setShopName(shopName);
                        group.setShopRemark(shopRemark);
                        return group;
                    }
                );
                
                // 添加订单项
                BatchPurchaseOrderNotificationData.PurchaseOrderNotificationItem item = new BatchPurchaseOrderNotificationData.PurchaseOrderNotificationItem();
                item.setShopId(order.getShopId());
                item.setShopName(shopName);
                item.setSubPurchaseOrderSn(order.getSubPurchaseOrderSn());
                item.setProductName(order.getProductName());
                
                // 使用生产进度表中的shipping_time
                if (progress.getShippingTime() != null) {
                    item.setShippingTime(progress.getShippingTime());
                } else {
                    // 兼容处理，如果没有生产进度信息的shipping_time，则使用备货单的deliver_time
                    item.setShippingTime(order.getDeliverTime());
                }
                
                shopGroup.getItems().add(item);
                
            } catch (Exception e) {
                log.error("处理普通备货未到货通知出错，订单号：{}，错误：{}", order.getSubPurchaseOrderSn(), e.getMessage(), e);
            }
        }
        
        // 将所有分组添加到通知数据中
        notificationData.getShopGroups().addAll(shopGroupMap.values());
        notificationData.setTotalOrderCount(shopGroupMap.values().stream()
                .mapToInt(group -> group.getItems().size())
                .sum());
        
        // 批量发送通知
        if (!shopGroupMap.isEmpty()) {
            Map<Long, BatchPurchaseOrderNotificationData> notificationDataMap = new HashMap<>();
            notificationDataMap.put(1L, notificationData); // 使用一个固定的键，因为我们只会发送一条聚合消息
            sendBatchNotification(notificationDataMap, PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_RECEIVED);
        }
        
        log.info("普通备货未到货通知处理完成");
    }
    

    

    /**
     * 获取店铺所属的所有运营组ID
     * 
     * @param shopId 店铺ID
     * @return 运营组ID列表
     */
    private List<Long> getShopOperationGroupIds(Long shopId) {
        // 查询店铺-运营组关联
        return shopGroupAssignmentMapper.selectGroupIdsByShopId(shopId);
    }
    
    /**
     * 获取店铺所属的一个运营组ID（向后兼容，现在使用第一个找到的运营组）
     * 
     * @param shopId 店铺ID
     * @return 运营组ID，如果没有找到则返回null
     */
    private Long getShopOperationGroupId(Long shopId) {
        List<Long> groupIds = getShopOperationGroupIds(shopId);
        return groupIds != null && !groupIds.isEmpty() ? groupIds.get(0) : null;
    }
    
    /**
     * 获取运营组成员ID列表
     * 
     * @param groupId 运营组ID
     * @return 成员ID列表
     */
    private List<Long> getGroupMemberIds(Long groupId) {
        List<GroupMember> members = groupMemberMapper.selectByGroupId(groupId);
        return members.stream()
                .map(GroupMember::getUserId)
                .collect(Collectors.toList());
    }

    /**
     * 获取店铺所属的所有生产组ID
     * 
     * @param shopId 店铺ID
     * @return 生产组ID列表
     */
    private List<Long> getShopProductionGroupIds(Long shopId) {
        // 查询店铺-生产组关联
        return productionGroupShopAssignmentMapper.selectGroupIdsByShopId(shopId);
    }
    
    /**
     * 获取生产组成员ID列表
     * 
     * @param groupId 生产组ID
     * @return 成员ID列表
     */
    private List<Long> getProductionGroupMemberIds(Long groupId) {
        // 创建结果集，用于存储成员ID和组长ID
        Set<Long> userIds = new HashSet<>();
        
        // 1. 查询生产组中的普通成员
        List<Long> memberIds = productionGroupMemberMapper.selectUserIdsByGroupId(groupId);
        if (memberIds != null && !memberIds.isEmpty()) {
            userIds.addAll(memberIds);
        }
        
        try {
            // 2. 查询生产组组长ID
            Long leaderId = productionGroupMapper.selectLeaderIdByGroupId(groupId);
            if (leaderId != null) {
                userIds.add(leaderId);
                log.info("添加生产组{}的组长ID: {}", groupId, leaderId);
            }
        } catch (Exception e) {
            log.error("查询生产组{}组长ID时出错: {}", groupId, e.getMessage());
        }
        
        return new ArrayList<>(userIds);
    }

    /**
     * 批量发送备货单通知
     *
     * @param notificationDataMap 按通知类型分组的通知数据
     * @param notificationType 通知类型
     */
    private void sendBatchNotification(Map<Long, BatchPurchaseOrderNotificationData> notificationDataMap, int notificationType) {
        if (notificationDataMap == null || notificationDataMap.isEmpty()) {
            return;
        }
        
        try {
            // 获取消息模板编码
            String templateCode = getTemplateCodeByNotificationType(notificationType);
            
            // 获取配置信息
            PurchaseOrderNotificationConfig config = configMapper.getByNotificationType(notificationType);
            if (config == null) {
                log.error("未找到通知配置，类型：{}", notificationType);
                return;
            }

            // 获取通知数据
            BatchPurchaseOrderNotificationData notificationData = notificationDataMap.values().iterator().next();
            
            // 如果没有任何店铺组或订单，则跳过
            if (notificationData.getShopGroups().isEmpty() || notificationData.getTotalOrderCount() == 0) {
                log.warn("没有店铺组或订单，跳过发送通知");
                return;
            }
            
            // 收集所有需要接收通知的运营组成员ID和生产组成员ID
            Set<Long> allReceiverIds = new HashSet<>();
            Set<Long> operationGroupMemberIds = new HashSet<>(); // 运营组成员ID集合
            Set<Long> productionGroupMemberIds = new HashSet<>(); // 生产组成员ID集合
            
            int shopWithNoOperationGroup = 0;
            int shopWithNoProductionGroup = 0;
            
            // 按店铺分组数据
            for (BatchPurchaseOrderNotificationData.ShopGroup shopGroup : notificationData.getShopGroups()) {
                Long shopId = shopGroup.getShopId();
                
                // 获取店铺所属的所有运营组ID
                List<Long> operationGroupIds = getShopOperationGroupIds(shopId);
                if (!operationGroupIds.isEmpty()) {
                    // 获取所有运营组成员的用户ID列表，添加到运营组成员集合中
                    for (Long groupId : operationGroupIds) {
                        List<Long> memberIds = getGroupMemberIds(groupId);
                        operationGroupMemberIds.addAll(memberIds);
                    }
                    log.info("店铺{}关联了{}个运营组", shopId, operationGroupIds.size());
                } else {
                    shopWithNoOperationGroup++;
                    log.warn("店铺{}未分配运营组", shopId);
                }
                
                // 获取店铺所属的所有生产组ID
                List<Long> productionGroupIds = getShopProductionGroupIds(shopId);
                if (!productionGroupIds.isEmpty()) {
                    // 获取所有生产组成员的用户ID列表，添加到生产组成员集合中
                    for (Long groupId : productionGroupIds) {
                        List<Long> memberIds = getProductionGroupMemberIds(groupId);
                        if (memberIds != null && !memberIds.isEmpty()) {
                            productionGroupMemberIds.addAll(memberIds);
                            log.info("店铺{}的生产组{}有{}名成员", shopId, groupId, memberIds.size());
                        } else {
                            log.warn("店铺{}的生产组{}没有成员", shopId, groupId);
                        }
                    }
                    log.info("店铺{}关联了{}个生产组", shopId, productionGroupIds.size());
                } else {
                    shopWithNoProductionGroup++;
                    log.warn("店铺{}未分配生产组", shopId);
                }
            }
            
            // 将运营组和生产组成员ID合并
            allReceiverIds.addAll(operationGroupMemberIds);
            allReceiverIds.addAll(productionGroupMemberIds);
            
            log.info("通知接收者统计 - 运营组成员: {}人, 生产组成员: {}人, 合并去重后: {}人", 
                operationGroupMemberIds.size(), productionGroupMemberIds.size(), allReceiverIds.size());
            log.info("店铺统计 - 无运营组: {}个, 无生产组: {}个, 总店铺数: {}个", 
                shopWithNoOperationGroup, shopWithNoProductionGroup, notificationData.getShopGroups().size());
            
            if (log.isDebugEnabled()) {
                log.debug("运营组成员IDs: {}", operationGroupMemberIds);
                log.debug("生产组成员IDs: {}", productionGroupMemberIds);
                log.debug("所有接收者IDs: {}", allReceiverIds);
            }
            
            // 如果没有收件人，则跳过
            if (allReceiverIds.isEmpty()) {
                log.warn("没有接收者，跳过发送通知");
                return;
            }
            
            // 确保shopGroup和items中的所有数据都已正确设置
            for (BatchPurchaseOrderNotificationData.ShopGroup shopGroup : notificationData.getShopGroups()) {
                // 确保店铺名称不为空
                if (shopGroup.getShopName() == null) {
                    shopGroup.setShopName("未知店铺");
                }
                
                // 确保店铺备注不为空
                if (shopGroup.getShopRemark() == null) {
                    shopGroup.setShopRemark("");
                }
                
                // 确保每个item的数据都已设置
                for (BatchPurchaseOrderNotificationData.PurchaseOrderNotificationItem item : shopGroup.getItems()) {
                    // 确保备货单号不为空
                    if (item.getSubPurchaseOrderSn() == null) {
                        item.setSubPurchaseOrderSn("未知单号");
                    }
                    
                    // 确保商品名称不为空
                    if (item.getProductName() == null) {
                        item.setProductName("未知商品");
                    }
                }
            }
            
            // 准备模板参数
            Map<String, Object> templateParams = new HashMap<>();
            templateParams.put("notifyTypeName", notificationData.getNotifyTypeName());
            templateParams.put("totalOrderCount", notificationData.getTotalOrderCount());
            templateParams.put("shopGroups", notificationData.getShopGroups());
            
            // 构建消息发送参数
            SendMessageDTO messageDTO = new SendMessageDTO();
            messageDTO.setTitle(notificationData.getNotifyTypeName() + "通知");  // 这个会被模板覆盖
            messageDTO.setContent("");  // 这个会被模板覆盖
            messageDTO.setMessageType(MessageConstants.MESSAGE_TYPE_TASK);  // 任务提醒类型
            messageDTO.setTargetType(MessageConstants.TARGET_TYPE_USER);  // 指定用户
            messageDTO.setTargetIds(String.join(",", allReceiverIds.stream().map(String::valueOf).collect(Collectors.toList())));
            messageDTO.setImportance(MessageConstants.IMPORTANCE_IMPORTANT);  // 设为重要
            messageDTO.setUseTemplate(true);  // 使用模板
            messageDTO.setTemplateCode(templateCode);
            
            log.info("最终发送的接收者IDs: {}", messageDTO.getTargetIds());
            
            // 使用支持复杂对象的JSON序列化方式
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
            objectMapper.registerModule(new JavaTimeModule());
            
            // 确保日期格式在传递过程中不丢失
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            objectMapper.setDateFormat(dateFormat);
            
            String jsonParams = objectMapper.writeValueAsString(templateParams);
            
            // 日志记录参数内容，便于调试
            if (log.isDebugEnabled()) {
                log.debug("模板参数: {}", jsonParams);
            }
            
            messageDTO.setTemplateParams(jsonParams);
            
            // 发送消息
            Long messageId = messageService.sendMessage(messageDTO, null);
            log.info("批量备货单通知发送成功，类型：{}，总订单数：{}，消息ID：{}", notificationData.getNotifyTypeName(), notificationData.getTotalOrderCount(), messageId);
            
            // 更新所有通知记录状态
            Date now = getCurrentTime();
            int maxNotifyCount = config.getMaxNotifyCount() != null ? config.getMaxNotifyCount() : PurchaseOrderNotificationConstants.MAX_NOTIFY_COUNT;
            
            // 遍历每个店铺组的每个订单项，更新通知记录
            for (BatchPurchaseOrderNotificationData.ShopGroup shopGroup : notificationData.getShopGroups()) {
                Long shopId = shopGroup.getShopId();
                
                for (BatchPurchaseOrderNotificationData.PurchaseOrderNotificationItem item : shopGroup.getItems()) {
                    // 查询通知记录
                    PurchaseOrderNotification notification = notificationMapper.selectOne(
                            new QueryWrapper<PurchaseOrderNotification>()
                                    .eq("shop_id", shopId)
                                    .eq("sub_purchase_order_sn", item.getSubPurchaseOrderSn())
                                    .eq("notification_type", notificationType)
                    );
                    
                    if (notification != null) {
                        // 更新通知记录
                        notification.setNotifyCount(notification.getNotifyCount() + 1);
                        notification.setLastNotifyTime(now);
                        notification.setNotifyStatus(notification.getNotifyCount() >= maxNotifyCount ? 
                                PurchaseOrderNotificationConstants.NOTIFY_STATUS_COMPLETED : 
                                PurchaseOrderNotificationConstants.NOTIFY_STATUS_NOTIFYING);
                        notification.setUpdateTime(now);
                        notificationMapper.updateById(notification);
                    }
                }
            }
        } catch (Exception e) {
            log.error("批量发送备货单通知失败，类型：{}，错误：{}", notificationType, e.getMessage(), e);
        }
    }

    /**
     * 根据通知类型获取模板编码
     * 
     * @param notificationType 通知类型
     * @return 模板编码
     */
    private String getTemplateCodeByNotificationType(int notificationType) {
        switch (notificationType) {
            case PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_SOON_OVERDUE:
                return PurchaseOrderNotificationConstants.TEMPLATE_CODE_JIT_SOON_OVERDUE_BATCH;
            case PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_OVERDUE:
                return PurchaseOrderNotificationConstants.TEMPLATE_CODE_JIT_OVERDUE_BATCH;
            case PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_DELIVERED:
                return PurchaseOrderNotificationConstants.TEMPLATE_CODE_NORMAL_NOT_DELIVERED_BATCH;
            case PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_RECEIVED:
                return PurchaseOrderNotificationConstants.TEMPLATE_CODE_NORMAL_NOT_RECEIVED_BATCH;
            default:
                throw new IllegalArgumentException("不支持的通知类型：" + notificationType);
        }
    }
} 