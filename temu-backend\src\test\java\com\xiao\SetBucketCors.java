package com.xiao;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.BucketCrossOriginConfiguration;
import com.qcloud.cos.model.CORSRule;
import com.qcloud.cos.region.Region;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class SetBucketCors {
    public static void main(String[] args) {
        // 1. 初始化用户身份信息
        String secretId = "AKIDxpY4MRIBCtNyNfnkX7UdZLxQgpnbGk14";
        String secretKey = "tZ6RZTCj3A7tSnwyqU2ThldHk3B5QXR8";
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        
        // 2. 设置存储桶区域
        String region = "ap-guangzhou";
        ClientConfig clientConfig = new ClientConfig(new Region(region));
        
        // 3. 创建COSClient实例
        COSClient cosClient = new COSClient(cred, clientConfig);
        
        try {
            // 4. 设置存储桶名称
            String bucketName = "temu-api-1302872665"; // 例如 temu-api-1302872665
            
            // 5. 创建CORS配置
            BucketCrossOriginConfiguration corsConfiguration = new BucketCrossOriginConfiguration();
            List<CORSRule> corsRules = new ArrayList<>();
            
            // 6. 创建CORS规则
            CORSRule corsRule = new CORSRule();
            
            // 7. 允许的来源
            List<String> allowedOrigins = new ArrayList<>();
            allowedOrigins.add("*");  // 允许所有来源，或者设置为您的域名
            corsRule.setAllowedOrigins(allowedOrigins);
            
            // 8. 允许的方法
            List<CORSRule.AllowedMethods> allowedMethods = new ArrayList<>();
            allowedMethods.add(CORSRule.AllowedMethods.GET);
            allowedMethods.add(CORSRule.AllowedMethods.HEAD);
            corsRule.setAllowedMethods(allowedMethods);
            
            // 9. 允许的头部
            List<String> allowedHeaders = new ArrayList<>();
            allowedHeaders.add("*");  // 允许所有头部
            corsRule.setAllowedHeaders(allowedHeaders);
            
            // 10. 暴露的头部
            List<String> exposeHeaders = new ArrayList<>();
            exposeHeaders.add("Content-Length");
            exposeHeaders.add("ETag");
            exposeHeaders.add("Content-Type");
            corsRule.setExposedHeaders(exposeHeaders);
            
            // 11. 最大缓存时间
            corsRule.setMaxAgeSeconds(3600);
            
            // 12. 添加规则到配置
            corsRules.add(corsRule);
            corsConfiguration.setRules(corsRules);
            
            // 13. 设置存储桶CORS配置
            cosClient.setBucketCrossOriginConfiguration(bucketName, corsConfiguration);
            
            System.out.println("CORS设置成功");
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 14. 关闭客户端
            cosClient.shutdown();
        }
    }
}