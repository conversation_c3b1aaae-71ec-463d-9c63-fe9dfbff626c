<template>
  <div class="app-container">
    <!-- 顶部操作栏 -->
    <div class="toolbar">
      <el-form :model="queryParams" ref="queryRef" :inline="true" class="search-form">
        <el-form-item label="生产组名称" prop="groupName">
          <el-input
            v-model="queryParams.groupName"
            placeholder="请输入生产组名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="负责人" prop="leaderName">
          <el-input
            v-model="queryParams.leaderName"
            placeholder="请输入负责人姓名"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status" style="width: 180px">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="action-btns">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['production:group:add']"
        >新增</el-button>
      </div>
    </div>

    <!-- 生产组表格 -->
    <el-table
      v-loading="loading"
      :data="groupList"
      style="width: 100%"
      border
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="45" />
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column label="生产组名称" prop="groupName" :show-overflow-tooltip="true" min-width="120"/>
      <el-table-column label="负责人" prop="leaderName" width="100" />
      <el-table-column label="成员数量" prop="memberCount" width="80" align="center" />
      <el-table-column label="状态" prop="status" width="80" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
            {{ scope.row.status === '0' ? '正常' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="160">
        <template #default="scope">
          {{ formatTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="280" align="center" fixed="right">
        <template #default="scope">
          <el-button 
            type="primary"   
            link 
            icon="View" 
            @click="handleView(scope.row)"
            v-hasPermi="['production:group:query']"
          >查看</el-button>
          <el-button 
            type="primary" 
            link 
            icon="Edit" 
            @click="handleUpdate(scope.row)"
            v-hasPermi="['production:group:edit']"
          >编辑</el-button>
          <el-button
            type="primary"
            link
            icon="User"
            @click="handleMembers(scope.row)"
            v-hasPermi="['production:member:list']"
          >成员管理</el-button>
          <el-button
            type="primary"
            link
            icon="Shop"
            @click="handleAssignShops(scope.row)"
            v-hasPermi="['production:group:shop:list']"
          >分配店铺</el-button>
          <el-button
            type="danger"
            link
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['production:group:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-if="total > 0"
      class="pagination"
      :total="total"
      v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 添加/修改生产组对话框 -->
    <el-dialog
      :title="dialog.title"
      v-model="dialog.visible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="groupFormRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="组名称" prop="groupName">
          <el-input v-model="form.groupName" placeholder="请输入生产组名称" />
        </el-form-item>
        <el-form-item label="负责人" prop="leaderId">
          <el-select
            v-model="form.leaderId"
            filterable
            placeholder="请选择负责人"
          >
            <el-option
              v-for="item in leaderOptions"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看生产组对话框 -->
    <el-dialog
      title="生产组详情"
      v-model="viewDialog.visible"
      width="600px"
      append-to-body
    >
      <el-descriptions border :column="2">
        <el-descriptions-item label="ID">{{ viewDialog.data.groupId }}</el-descriptions-item>
        <el-descriptions-item label="生产组名称">{{ viewDialog.data.groupName }}</el-descriptions-item>
        <el-descriptions-item label="负责人">{{ viewDialog.data.leaderName }}</el-descriptions-item>
        <el-descriptions-item label="成员数量">{{ viewDialog.data.memberCount }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="viewDialog.data.status === '0' ? 'success' : 'danger'">
            {{ viewDialog.data.status === '0' ? '正常' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatTime(viewDialog.data.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewDialog.data.remark }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewDialog.visible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 成员管理对话框 -->
    <el-dialog
      title="成员管理"
      v-model="memberDialog.visible"
      width="800px"
      append-to-body
    >
      <div class="member-dialog-content">
        <div class="member-toolbar">
          <el-input
            v-model="memberQueryParam.keyword"
            placeholder="请输入昵称、账号或角色搜索"
            clearable
            style="width: 250px"
            @keyup.enter="searchMembers"
          />
          <el-button type="primary" icon="Search" @click="searchMembers">搜索</el-button>
          <el-button icon="Plus" @click="handleAssignMembers(currentGroup)" v-hasPermi="['production:member:add']">分配成员</el-button>
        </div>

        <!-- 成员列表 -->
        <el-table
          v-loading="memberLoading"
          :data="memberList"
          style="width: 100%"
          border
        >
          <el-table-column type="index" width="50" />
          <el-table-column label="昵称" prop="nickName" width="100" />
          <el-table-column label="账号" prop="userName" width="120" />
          <el-table-column label="角色" prop="roleNames" min-width="150" :show-overflow-tooltip="true">
            <template #default="scope">
              <div class="role-tags">
                <template v-if="scope.row.roleNames">
                  <el-tag 
                    v-for="(role, index) in scope.row.roleNames.split(', ')" 
                    :key="index"
                    size="small"
                    class="role-tag"
                  >
                    {{ role }}
                  </el-tag>
                </template>
                <span v-else>-</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="加入时间" prop="joinTime" width="160">
            <template #default="scope">
              {{ formatTime(scope.row.joinTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template #default="scope">
              <el-button
                type="danger"
                link
                icon="Delete"
                @click="handleRemoveMember(scope.row)"
                v-hasPermi="['production:member:remove']"
              >移除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 成员分页 -->
        <el-pagination
          v-if="memberTotal > 0"
          class="pagination"
          :total="memberTotal"
          v-model:current-page="memberQueryParam.pageNum"
          v-model:page-size="memberQueryParam.pageSize"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleMemberSizeChange"
          @current-change="handleMemberPageChange"
        />
      </div>
    </el-dialog>

    <!-- 分配成员对话框 -->
    <el-dialog
      title="分配成员"
      v-model="assignMemberDialog.visible"
      width="650px"
      append-to-body
    >
      <el-form>
        <el-form-item label="生产组" prop="groupName">
          <span>{{ assignMemberDialog.groupName }}</span>
        </el-form-item>
        <el-form-item label="已分配成员" v-if="existingMemberList.length > 0">
          <div class="existing-members">
            <el-tag
              v-for="member in existingMemberList"
              :key="member.userId"
              class="member-tag"
              type="success"
              closable
              @close="handleRemoveMemberTag(member)"
            >
              {{ member.nickName }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item label="选择成员" prop="selectedUsers">
          <div class="member-search-bar">
            <el-input
              v-model="availableUserQueryParam.keyword"
              placeholder="请输入姓名或工号搜索"
              clearable
              style="width: 250px"
              @keyup.enter="searchAvailableUsers"
            />
            <el-button type="primary" icon="Search" @click="searchAvailableUsers">搜索</el-button>
          </div>
          <el-select
            v-model="assignMemberDialog.selectedUsers"
            filterable
            multiple
            placeholder="请选择生产人员"
            style="width: 100%"
            v-loading="availableUserLoading"
          >
            <el-option
              v-for="item in availableUserList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
              :disabled="existingMemberIds.includes(item.userId)"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="assignMemberDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitAssignMembers">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 分配店铺对话框 -->
    <el-dialog
      title="分配店铺"
      v-model="shopDialog.visible"
      width="600px"
      append-to-body
    >
      <el-form>
        <el-form-item label="生产组" prop="groupName">
          <span>{{ shopDialog.groupName }}</span>
        </el-form-item>
        <el-form-item label="已分配店铺" v-if="assignedShops.length > 0">
          <div class="assigned-shops">
            <el-tag
              v-for="shop in assignedShops"
              :key="shop.shopId"
              class="shop-tag"
              type="success"
              size="small"
              closable
              @close="handleRemoveShop(shop)"
            >
              {{ shop.shopName }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="filter-options">
            <el-switch
              v-model="shopDialog.showOtherGroupShops"
              active-text="显示所有未分配给本组的店铺"
              inactive-text="仅显示未分配的店铺"
              @change="handleFilterChange"
            />
          </div>
        </el-form-item>
        <el-form-item label="选择店铺" prop="selectedShops">
          <el-select
            v-model="shopDialog.selectedShops"
            filterable
            multiple
            placeholder="请选择店铺"
            style="width: 100%"
            v-loading="shopLoading"
          >
            <el-option-group label="未分配给任何生产组的店铺">
              <el-option
                v-for="item in unassignedGroupShops"
                :key="item.shopId"
                :label="item.shopName"
                :value="item.shopId"
              />
            </el-option-group>
            <el-option-group v-if="shopDialog.showOtherGroupShops && otherGroupShops.length > 0" label="已分配给其他生产组的店铺">
              <el-option
                v-for="item in otherGroupShops"
                :key="item.shopId"
                :label="`${item.shopName} (${item.groupNames})`"
                :value="item.shopId"
              />
            </el-option-group>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="shopDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitAssignShops">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, toRefs, nextTick, computed } from 'vue';
import type { FormInstance } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  getGroupList,
  getGroupDetail,
  addGroup,
  updateGroup,
  deleteGroup,
  getUnassignedShops,
  getGroupShops,
  assignShopsToGroup,
  removeShopFromGroup
} from '@/api/production/group';
import { getUserList, getProductionLeaders } from '@/api/user';
import { getGroupMembers, addGroupMember as batchAddMembers, removeGroupMember, getAvailableUsers, getAllMembers } from '@/api/production/member';
import { useRouter } from 'vue-router';
import { formatTime as formatDateTime } from '@/utils/format';

const router = useRouter();

// 状态选项
const statusOptions = [
  { label: '正常', value: '0' },
  { label: '禁用', value: '1' }
];

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  groupName: '',
  leaderName: '',
  status: ''
});

// 表格数据
const loading = ref(false);
const groupList = ref<any[]>([]);
const total = ref(0);
const multipleSelection = ref<any[]>([]);

// 对话框属性
const dialog = reactive({
  visible: false,
  title: ''
});

// 查看对话框
const viewDialog = reactive({
  visible: false,
  data: {} as any
});

// 表单参数
const form = reactive({
  groupId: undefined,
  groupName: '',
  leaderId: undefined,
  status: '0',
  remark: ''
});

// 表单校验规则
const rules = reactive({
  groupName: [
    { required: true, message: '请输入生产组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  leaderId: [
    { required: true, message: '请选择负责人', trigger: 'change' }
  ]
});

// 负责人选项
const leaderOptions = ref<any[]>([]);
const groupFormRef = ref<FormInstance>();
const queryRef = ref<FormInstance>();

// 当前操作的生产组信息
const currentGroup = reactive({
  groupId: undefined as unknown as number,
  groupName: ''
});

// 成员管理对话框
const memberDialog = reactive({
  visible: false,
  groupId: undefined as unknown as number,
  groupName: ''
});

// 成员查询参数
const memberQueryParam = reactive({
  groupId: undefined as unknown as number,
  pageNum: 1,
  pageSize: 10,
  keyword: ''
});

// 成员列表数据
const memberLoading = ref(false);
const memberList = ref<any[]>([]);
const memberTotal = ref(0);

// 分配成员对话框
const assignMemberDialog = reactive({
  visible: false,
  groupId: undefined as unknown as number,
  groupName: '',
  selectedUsers: [] as number[]
});

// 已存在的成员列表和ID集
const existingMemberList = ref<any[]>([]);
const existingMemberIds = computed(() => existingMemberList.value.map(item => item.userId));

// 可添加成员数据
const availableUserQueryParam = reactive({
  groupId: undefined as unknown as number,
  pageNum: 1,
  pageSize: 10,
  keyword: ''
});
const availableUserLoading = ref(false);
const availableUserList = ref<any[]>([]);
const availableUserTotal = ref(0);

// 分配店铺对话框
const shopDialog = reactive({
  visible: false,
  groupId: undefined as unknown as number,
  groupName: '',
  selectedShops: [] as number[],
  showOtherGroupShops: false
});

// 店铺列表数据
const unassignedShops = ref<any[]>([]);
const unassignedGroupShops = ref<any[]>([]); // 未分配给任何生产组的店铺
const otherGroupShops = ref<any[]>([]); // 已分配给其他生产组的店铺
const assignedShops = ref<any[]>([]); // 已分配给当前生产组的店铺
const shopLoading = ref(false);

// 生命周期钩子
onMounted(() => {
  getList();
  getLeaders();
});

// 获取负责人选项
const getLeaders = async () => {
  try {
    // 获取具有生产组长角色的用户列表
    const res = await getProductionLeaders('productionLeader');
    leaderOptions.value = res.data;
    
    // 如果没有找到生产组长，可以获取所有用户作为备选
    if (leaderOptions.value.length === 0) {
      console.warn('未找到生产组长角色的用户，将获取所有用户作为备选');
      const allUsers = await getUserList({});
      leaderOptions.value = allUsers.data.rows;
    }
  } catch (error) {
    console.error('获取负责人列表失败', error);
    
    // 出错时获取所有用户作为备选
    try {
      const allUsers = await getUserList({});
      leaderOptions.value = allUsers.data.rows;
    } catch (err) {
      console.error('获取所有用户列表失败', err);
    }
  }
};

// 获取生产组列表
const getList = async () => {
  loading.value = true;
  try {
    const res = await getGroupList(queryParams);
    groupList.value = res.data.records || [];
    total.value = res.data.total || 0;
  } catch (error) {
    console.error('获取生产组列表失败', error);
    groupList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 查询操作
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 重置查询
const resetQuery = () => {
  queryRef.value?.resetFields();
  handleQuery();
};

// 多选框事件
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection;
};

// 分页大小变化
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size;
  getList();
};

// 分页页码变化
const handleCurrentChange = (current: number) => {
  queryParams.pageNum = current;
  getList();
};

// 查看生产组详情
const handleView = async (row: any) => {
  try {
    const res = await getGroupDetail(row.groupId);
    viewDialog.data = res.data;
    viewDialog.visible = true;
  } catch (error) {
    console.error('获取生产组详情失败', error);
    ElMessage.error('获取生产组详情失败');
  }
};

// 新增生产组
const handleAdd = () => {
  resetForm();
  dialog.title = '新增生产组';
  dialog.visible = true;
};

// 修改生产组
const handleUpdate = async (row: any) => {
  resetForm();
  dialog.title = '修改生产组';
  
  try {
    const res = await getGroupDetail(row.groupId);
    Object.assign(form, res.data);
    dialog.visible = true;
  } catch (error) {
    console.error('获取生产组详情失败', error);
    ElMessage.error('获取生产组详情失败');
  }
};

// 删除生产组
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确定要删除"${row.groupName}"生产组吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteGroup(row.groupId);
      ElMessage.success('删除成功');
      getList();
    } catch (error) {
      console.error('删除生产组失败', error);
    }
  }).catch(() => {});
};

// 重置表单
const resetForm = () => {
  form.groupId = undefined;
  form.groupName = '';
  form.leaderId = undefined;
  form.status = '0';
  form.remark = '';

  nextTick(() => {
    groupFormRef.value?.clearValidate();
  });
};

// 跳转到成员管理页面 - 修改为打开对话框
const handleMembers = (row: any) => {
  // 设置当前操作的生产组信息
  currentGroup.groupId = row.groupId;
  currentGroup.groupName = row.groupName;
  
  // 设置成员查询参数
  memberQueryParam.groupId = row.groupId;
  memberQueryParam.pageNum = 1;
  memberQueryParam.keyword = '';
  
  // 打开对话框
  memberDialog.visible = true;
  memberDialog.groupId = row.groupId;
  memberDialog.groupName = row.groupName;
  
  // 获取成员列表
  getGroupMemberList();
};

// 获取生产组成员列表
const getGroupMemberList = async () => {
  if (!memberQueryParam.groupId) return;
  
  memberLoading.value = true;
  try {
    const res = await getGroupMembers(memberQueryParam);
    memberList.value = res.data.records || [];
    memberTotal.value = res.data.total || 0;
  } catch (error) {
    console.error('获取生产组成员列表失败', error);
    ElMessage.error('获取成员列表失败');
    memberList.value = [];
    memberTotal.value = 0;
  } finally {
    memberLoading.value = false;
  }
};

// 搜索成员
const searchMembers = () => {
  memberQueryParam.pageNum = 1;
  getGroupMemberList();
};

// 成员分页大小变化
const handleMemberSizeChange = (size: number) => {
  memberQueryParam.pageSize = size;
  getGroupMemberList();
};

// 成员分页页码变化
const handleMemberPageChange = (current: number) => {
  memberQueryParam.pageNum = current;
  getGroupMemberList();
};

// 显示分配成员对话框
const handleAssignMembers = async (row: any) => {
  assignMemberDialog.groupId = row.groupId;
  assignMemberDialog.groupName = row.groupName;
  assignMemberDialog.selectedUsers = [];
  
  // 清空可选成员数据
  availableUserList.value = [];
  availableUserTotal.value = 0;
  availableUserQueryParam.groupId = row.groupId;
  availableUserQueryParam.pageNum = 1;
  availableUserQueryParam.keyword = '';
  
  // 获取已有成员列表
  await getExistingMembers(assignMemberDialog.groupId);
  
  // 获取可添加的成员列表
  await searchAvailableUsers();
  
  assignMemberDialog.visible = true;
};

// 获取已存在的成员列表
const getExistingMembers = async (groupId: number) => {
  try {
    const res = await getAllMembers(groupId);
    existingMemberList.value = res.data || [];
  } catch (error) {
    console.error('获取生产组成员列表失败', error);
    ElMessage.error('获取成员列表失败');
    existingMemberList.value = [];
  }
};

// 获取可添加的用户列表
const searchAvailableUsers = async () => {
  if (!availableUserQueryParam.groupId) return;
  
  availableUserLoading.value = true;
  try {
    const res = await getAvailableUsers(availableUserQueryParam);
    availableUserList.value = res.data.records || [];
    availableUserTotal.value = res.data.total || 0;
  } catch (error) {
    console.error('获取可添加成员列表失败', error);
    ElMessage.error('获取可添加成员列表失败');
    availableUserList.value = [];
    availableUserTotal.value = 0;
  } finally {
    availableUserLoading.value = false;
  }
};

// 提交分配成员
const submitAssignMembers = async () => {
  if (assignMemberDialog.selectedUsers.length === 0) {
    ElMessage.warning('请选择要添加的成员');
    return;
  }
  
  try {
    // 组装请求数据
    await batchAddMembers({
      groupId: assignMemberDialog.groupId,
      userIds: assignMemberDialog.selectedUsers
    });
    
    ElMessage.success('分配成员成功');
    assignMemberDialog.visible = false;
    
    // 刷新成员列表
    if (memberDialog.visible) {
      await getGroupMemberList();
    }
    
    // 刷新生产组列表（更新成员数量）
    getList();
  } catch (error) {
    console.error('分配成员失败', error);
    ElMessage.error('分配成员失败');
  }
};

// 从标签中移除成员
const handleRemoveMemberTag = async (member: any) => {
  try {
    await removeGroupMember({
      groupId: assignMemberDialog.groupId,
      userId: member.userId
    });
    
    // 从已有成员列表中移除
    existingMemberList.value = existingMemberList.value.filter(item => item.userId !== member.userId);
    
    ElMessage.success(`成员 ${member.nickName} 已移除`);
    
    // 重新加载可用成员列表
    await searchAvailableUsers();
    
    // 刷新生产组列表
    getList();
  } catch (error) {
    console.error('移除成员失败', error);
    ElMessage.error('移除成员失败');
  }
};

// 移除成员
const handleRemoveMember = (row: any) => {
  if (!memberQueryParam.groupId) {
    ElMessage.error('生产组ID不能为空');
    return;
  }

  ElMessageBox.confirm(`确定要将"${row.nickName}"从生产组中移除吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await removeGroupMember({
        groupId: memberQueryParam.groupId,
        userId: row.userId
      });
      ElMessage.success('移除成员成功');
      
      // 刷新成员列表
      await getGroupMemberList();
      
      // 刷新生产组列表（更新成员数量）
      getList();
    } catch (error) {
      console.error('移除成员失败', error);
      ElMessage.error('移除成员失败');
    }
  }).catch(() => {});
};

// 提交表单
const submitForm = () => {
  groupFormRef.value?.validate(async (valid) => {
    if (!valid) return;
    
    try {
      if (form.groupId !== undefined) {
        // 修改
        await updateGroup(form);
        ElMessage.success('修改成功');
      } else {
        // 新增
        await addGroup(form);
        ElMessage.success('新增成功');
      }
      dialog.visible = false;
      getList();
    } catch (error: any) {
      console.error('操作失败', error);
      ElMessage.error(error.message || '操作失败');
    }
  });
};

// 格式化时间
const formatTime = (timestamp: string) => {
  return formatDateTime(timestamp);
};

// 打开分配店铺对话框
const handleAssignShops = async (row: any) => {
  shopDialog.groupId = row.groupId;
  shopDialog.groupName = row.groupName;
  shopDialog.selectedShops = [];
  shopDialog.showOtherGroupShops = false; // 默认只显示未分配的店铺
  
  // 获取已分配给该生产组的店铺
  await getAssignedShopsList(shopDialog.groupId);
  
  // 获取未分配的店铺列表
  await getUnassignedShopsList(shopDialog.groupId);
  
  shopDialog.visible = true;
};

// 获取已分配给生产组的店铺列表
const getAssignedShopsList = async (groupId: number) => {
  try {
    const res = await getGroupShops(groupId);
    assignedShops.value = res.data || [];
  } catch (error) {
    console.error('获取已分配店铺列表失败', error);
    ElMessage.error('获取已分配店铺列表失败');
    assignedShops.value = [];
  }
};

// 获取未分配的店铺列表
const getUnassignedShopsList = async (groupId: number) => {
  shopLoading.value = true;
  try {
    const res = await getUnassignedShops(groupId);
    unassignedShops.value = res.data || [];
    
    // 分组处理店铺列表
    unassignedGroupShops.value = unassignedShops.value.filter(shop => !shop.groups || shop.groups.length === 0);
    otherGroupShops.value = unassignedShops.value.filter(shop => shop.groups && shop.groups.length > 0);
  } catch (error) {
    console.error('获取未分配店铺列表失败', error);
    ElMessage.error('获取未分配店铺列表失败');
    unassignedShops.value = [];
    unassignedGroupShops.value = [];
    otherGroupShops.value = [];
  } finally {
    shopLoading.value = false;
  }
};

// 处理过滤选项变化
const handleFilterChange = () => {
  // 重新获取未分配的店铺列表
  getUnassignedShopsList(shopDialog.groupId);
};

// 提交分配店铺
const submitAssignShops = async () => {
  if (shopDialog.selectedShops.length === 0) {
    ElMessage.warning('请选择至少一个店铺');
    return;
  }
  
  try {
    await assignShopsToGroup(shopDialog.groupId, shopDialog.selectedShops);
    ElMessage.success('分配店铺成功');
    
    // 刷新已分配店铺列表
    await getAssignedShopsList(shopDialog.groupId);
    
    shopDialog.visible = false;
    // 刷新列表
    getList();
  } catch (error) {
    console.error('分配店铺失败', error);
    ElMessage.error('分配店铺失败');
  }
};

// 处理移除店铺
const handleRemoveShop = async (shop: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要将店铺 "${shop.shopName}" 从生产组 "${shopDialog.groupName}" 中移除吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    // 调用移除店铺API
    await removeShopFromGroup(shopDialog.groupId, shop.shopId);
    
    // 从已分配店铺列表中移除
    assignedShops.value = assignedShops.value.filter(item => item.shopId !== shop.shopId);
    
    // 将该店铺添加到未分配店铺列表中
    const removedShop = {
      shopId: shop.shopId,
      shopName: shop.shopName,
      groups: []
    };
    unassignedGroupShops.value.push(removedShop);
    
    // 按店铺名称排序
    unassignedGroupShops.value.sort((a, b) => a.shopName.localeCompare(b.shopName));
    
    ElMessage.success(`店铺 ${shop.shopName} 已从该生产组移除`);
    
    // 刷新列表
    getList();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除店铺失败', error);
      ElMessage.error('移除店铺失败');
    }
  }
};
</script>

<style scoped>
.toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.search-form {
  flex: 1;
}

.action-btns {
  display: flex;
  align-items: flex-start;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.member-dialog-content {
  padding: 10px 0;
}

.member-toolbar {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.existing-members {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
  min-height: 40px;
  max-height: 120px;
  overflow-y: auto;
}

.member-tag {
  margin-bottom: 5px;
  margin-right: 8px;
  cursor: pointer;
}

.member-search-bar {
  margin-bottom: 10px;
  display: flex;
  gap: 10px;
}

.role-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.role-tag {
  margin-bottom: 4px;
}

.assigned-shops {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
  min-height: 40px;
  max-height: 120px;
  overflow-y: auto;
}

.shop-tag {
  margin-bottom: 5px;
  margin-right: 8px;
  cursor: pointer;
}

.filter-options {
  margin-bottom: 10px;
}
</style> 