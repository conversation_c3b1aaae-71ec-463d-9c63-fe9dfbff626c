package com.xiao.temu.modules.system.controller;

import com.xiao.temu.modules.sync.vo.ApiResponse;
import com.xiao.temu.security.annotation.RequiresPermission;
import com.xiao.temu.modules.system.service.SysUserService;
import com.xiao.temu.modules.system.vo.SysUserVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/system/user")
public class SysUserController {

    private final SysUserService userService;

    public SysUserController(SysUserService userService) {
        this.userService = userService;
    }

    /**
     * 获取未分配到任何运营组的用户列表
     * 
     * @param roleKey 角色标识
     * @return 未分配用户列表
     */
    @GetMapping("/unassignedUsers")
    @RequiresPermission("system:user:list")
    public ApiResponse getUnassignedUsers(@RequestParam(required = false) String roleKey) {
        List<SysUserVO> users = userService.getUnassignedUsers(roleKey);
        return ApiResponse.success(users);
    }
} 