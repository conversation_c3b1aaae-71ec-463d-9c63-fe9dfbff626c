<template>
  <!-- 拣货单预览弹窗 -->
  <el-dialog
    v-model="dialogVisible"
    title="拣货单预览"
    width="80%"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    class="picking-preview-dialog"
  >
    <!-- 添加排序选项 -->
    <div class="sorting-options">
      <span class="sorting-label">排序方式：</span>
      <el-radio-group v-model="sortMethod" @change="handleSortChange">
        <el-radio :label="1">按商品勾选顺序</el-radio>
        <el-radio :label="2">按商品SKC ID升序</el-radio>
        <el-radio :label="3">按备货单创建时间最新的在前</el-radio>
      </el-radio-group>
    </div>
    
    <div class="picking-preview-content">
      <iframe
        ref="pickingPreviewFrame"
        style="width: 100%; height: 600px; border: 1px solid #ddd; border-radius: 4px;"
      ></iframe>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <!-- <el-button type="primary" @click="handleSavePdf">保存</el-button> -->
        <el-button type="primary" @click="handlePrintPickingOrder">打印</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { formatTime } from '@/utils/format'
import QRCode from 'qrcode'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  pickingData: {
    type: Array as () => any[],
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'print-success'])

// 对话框可见性
const dialogVisible = ref(false)

// 排序方法：1-按商品勾选顺序, 2-按商品SKC ID升序(默认), 3-按备货单创建时间最新的在前
const sortMethod = ref(2)

// 生成二维码的数据URL缓存
const qrCodeCache = ref<Record<string, string>>({})

// 监听visible属性变化
watch(() => props.visible, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.pickingData.length > 0) {
    // 预先生成所有二维码
    preGenerateQrCodes()
    // 打开时加载预览
    setTimeout(() => {
      loadPreview()
    }, 100)
  }
})

// 监听dialogVisible变化，同步回父组件
watch(() => dialogVisible.value, (newValue) => {
  emit('update:visible', newValue)
})

// 预先生成所有二维码
const preGenerateQrCodes = async () => {
  const orderSnSet = new Set<string>()
  
  // 收集所有不重复的备货单号
  props.pickingData.forEach(order => {
    if (order.subPurchaseOrderSn && order.qrCodeDataJson) {
      orderSnSet.add(order.subPurchaseOrderSn.toString())
    }
  })
  
  // 为每个备货单号生成二维码
  for (const orderSn of orderSnSet) {
    // 查找对应的订单数据
    const order = props.pickingData.find(o => o.subPurchaseOrderSn?.toString() === orderSn)
    if (order && order.qrCodeDataJson) {
      try {
        const dataUrl = await generateQrCodeDataUrl(order.qrCodeDataJson)
        qrCodeCache.value[orderSn] = dataUrl
      } catch (error) {
        console.error('生成二维码失败:', error)
      }
    }
  }
}

// 生成单个二维码data URL
const generateQrCodeDataUrl = async (qrCodeDataJson: string): Promise<string> => {
  try {
    // 直接使用原始JSON字符串生成二维码
    return await QRCode.toDataURL(qrCodeDataJson, {
      width: 100,
      margin: 1,
      errorCorrectionLevel: 'M'
    })
  } catch (error) {
    console.error('生成二维码失败:', error)
    return ''
  }
}

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
}

// 处理排序方式变更
const handleSortChange = () => {
  // 重新加载预览
  loadPreview()
}

// 加载预览内容
const loadPreview = () => {
  const iframe = document.querySelector('.picking-preview-dialog iframe') as HTMLIFrameElement;
  if (iframe) {
    const iframeDoc = iframe.contentDocument || (iframe.contentWindow?.document as Document);
    iframeDoc.open();
    iframeDoc.write(generatePickingOrderHTML());
    iframeDoc.close();
  } else {
    console.error('找不到预览框架元素');
    ElMessage.error('无法创建预览，请稍后重试');
  }
}

// 处理打印拣货单
const handlePrintPickingOrder = () => {
  const iframe = document.querySelector('.picking-preview-dialog iframe') as HTMLIFrameElement;
  if (iframe) {
    // 调用iframe内的打印函数
    iframe.contentWindow?.print();
    emit('print-success', props.pickingData);
  } else {
    ElMessage.error('无法找到预览内容，请重试');
  }
}

// 处理保存PDF
const handleSavePdf = () => {
  // 目前浏览器打印功能可以选择"另存为PDF"选项
  // 这里只是简单地调用打印功能
  handlePrintPickingOrder();
  
  ElMessage.success('请在打印对话框中选择"另存为PDF"选项来保存文件');
}

// 根据选择的排序方式对订单分组进行排序
const sortOrderGroups = (orderGroups: { [key: string]: any[] }) => {
  // 创建包含排序相关信息的数组
  const ordersWithSortInfo = Object.keys(orderGroups).map(key => {
    // 获取该组的第一个订单作为代表
    const firstOrder = orderGroups[key][0];
    return {
      key,
      // SKC ID用于"按商品SKC ID升序"排序
      productSkcId: firstOrder.productSkcId || 0,
      // 创建时间用于"按备货单创建时间最新的在前"排序
      purchaseTime: firstOrder.purchaseTime || 0,
    }
  });
  
  // 根据选择的排序方式进行排序
  switch (sortMethod.value) {
    case 1: // 按商品勾选顺序 (保持原始顺序)
      return Object.keys(orderGroups);
    
    case 2: // 按商品SKC ID升序
      ordersWithSortInfo.sort((a, b) => Number(a.productSkcId) - Number(b.productSkcId));
      return ordersWithSortInfo.map(item => item.key);
    
    case 3: // 按备货单创建时间最新的在前
      ordersWithSortInfo.sort((a, b) => Number(b.purchaseTime) - Number(a.purchaseTime));
      return ordersWithSortInfo.map(item => item.key);
    
    default:
      return Object.keys(orderGroups);
  }
}

// 生成拣货单HTML
const generatePickingOrderHTML = (): string => {
  if (!props.pickingData || props.pickingData.length === 0) {
    return '<html><body><h1>无数据</h1></body></html>';
  }

  const currentDate = new Date();
  const formattedDate = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')} ${String(currentDate.getHours()).padStart(2, '0')}:${String(currentDate.getMinutes()).padStart(2, '0')}:${String(currentDate.getSeconds()).padStart(2, '0')}`;
  
  // 按备货单号分组并计算商品行的合并单元格，同时处理skuQuantityDetailList
  const orderGroups: { [key: string]: any[] } = {};
  props.pickingData.forEach(order => {
    const orderSn = order.subPurchaseOrderSn?.toString() || '';
    
    // 如果有SKU明细列表
    if (order.skuQuantityDetailList && order.skuQuantityDetailList.length > 0) {
      // 为每个SKU创建一个项目，并将订单主信息与SKU信息合并
      order.skuQuantityDetailList.forEach((skuDetail: any) => {
        const itemWithSku = {
          ...order,
          className: skuDetail.className,
          fulfilmentProductSkuId: skuDetail.fulfilmentProductSkuId,
          productSkuId: skuDetail.productSkuId,
          extCode: skuDetail.extCode,
          purchaseQuantity: skuDetail.purchaseQuantity
        };
        
        if (!orderGroups[orderSn]) {
          orderGroups[orderSn] = [];
        }
        orderGroups[orderSn].push(itemWithSku);
      });
    } else {
      // 如果没有SKU明细，使用订单本身
      if (!orderGroups[orderSn]) {
        orderGroups[orderSn] = [];
      }
      orderGroups[orderSn].push(order);
    }
  });
  
  // 生成表格行HTML
  let tableRows = '';
  let currentIndex = 1;
  
  // 使用自定义排序函数
  const orderedKeys = sortOrderGroups(orderGroups);
  
  orderedKeys.forEach(orderSn => {
    const items = orderGroups[orderSn];
    
    // 判断是否需要显示合计行（当SKU数量大于1时）
    const needSummary = items.length > 1;
    
    // 行高需要包含合计行
    const rowspan = needSummary ? items.length + 1 : items.length;
    
    // 计算该商品所有SKU的数量总和
    let totalQuantity = 0;
    items.forEach(item => {
      totalQuantity += (item.purchaseQuantity || 0);
    });
    
    // 获取二维码图片数据URL
    const qrCodeDataUrl = qrCodeCache.value[orderSn] || '';
    
    items.forEach((item, itemIndex) => {
      const firstInGroup = itemIndex === 0;
      
      // 获取商品的标签
      const tags: string[] = [];
      if (item.purchaseStockType === 1) tags.push('<span style="display: inline-block; margin-right: 3px; padding: 1px 3px; font-size: 11px; background-color: #E6A23C; color: white; border-radius: 2px;">JIT</span>');
      if (item.settlementType === 1) tags.push('<span style="display: inline-block; margin-right: 3px; padding: 1px 3px; font-size: 11px; background-color: #409EFF; color: white; border-radius: 2px;">VMI</span>');
      if (item.isFirst === true) tags.push('<span style="display: inline-block; margin-right: 3px; padding: 1px 3px; font-size: 11px; background-color: #67C23A; color: white; border-radius: 2px;">首单</span>');
      
      // 判断是否为缺货商品
      if (item.skuLackSnapshot === 1) {
        tags.push('<span style="display: inline-block; margin-right: 3px; padding: 1px 3px; font-size: 11px; background-color: #F56C6C; color: white; border-radius: 2px;">缺货</span>');
      }
      
      tableRows += '<tr>';
      
      // 序号列 - 只在第一行显示并合并
      if (firstInGroup) {
        tableRows += `<td rowspan="${rowspan}" style="text-align: center; vertical-align: middle; border: 1px solid #000; font-weight: bold; padding: 3px;">${currentIndex}</td>`;
      }
      
      // 调整位置 - 店铺代号列 - 只在第一行显示并合并
      if (firstInGroup) {
        tableRows += `<td rowspan="${rowspan}" style="text-align: center; vertical-align: middle; border: 1px solid #000; padding: 3px; font-size: 12px; font-weight: bold;">${item.shopRemark || '-'}</td>`;
      }
      
      // 商品信息列 - 只在第一行显示并合并，采用更紧凑的显示方式
      if (firstInGroup) {
        tableRows += `
          <td rowspan="${rowspan}" style="vertical-align: top; border: 1px solid #000; padding: 3px; line-height: 1.2;">
            <div style="display: flex; flex-wrap: nowrap;">
              ${item.productSkcPicture ? `<img src="${item.productSkcPicture}" style="width: 40px; height: 50px; object-fit: cover; border: 1px solid #ddd; margin-right: 5px; flex-shrink: 0;">` : ''}
              <div style="flex: 1; min-width: 0;">
                <div style="font-size: 12px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"><b>SKC:</b> ${item.productSkcId || '-'}</div>
                <div style="font-size: 12px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"><b>SKC货号:</b> ${item.productSn || '-'}</div>
                <div style="font-size: 12px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"><b>备货母单号:</b> ${item.originalPurchaseOrderSn || '-'}</div>
                <div style="font-size: 12px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"><b>备货单号:</b> ${item.subPurchaseOrderSn || '-'}</div>
                <div style="font-size: 12px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"><b>创建时间:</b> ${formatTime(item.purchaseTime) || '-'}</div>
                <div style="font-size: 12px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"><b>要求发货时间:</b> ${item.deliverInfo ? formatTime(item.deliverInfo.expectLatestDeliverTimeOrDefault) : '-'}</div>
                <div style="margin-top: 2px;">${tags.join('')}</div>
              </div>
            </div>
          </td>
        `;
      }
      
      // 属性集列 - 使用 className 字段
      tableRows += `<td style="text-align: center; vertical-align: middle; border: 1px solid #000; padding: 3px; font-size: 12px;">${item.className || '-'}</td>`;
      
      // SKU ID列 - 使用 fulfilmentProductSkuId 或 productSkuId 字段
      tableRows += `<td style="text-align: center; vertical-align: middle; border: 1px solid #000; padding: 3px; font-size: 12px;">${item.fulfilmentProductSkuId || item.productSkuId || '-'}</td>`;
      
      // SKU货号列
      tableRows += `<td style="text-align: center; vertical-align: middle; border: 1px solid #000; padding: 3px; font-size: 12px;">${item.extCode || '-'}</td>`;
      
      // 数量列 - 使用 purchaseQuantity 字段作为备货件数
      tableRows += `<td style="text-align: center; vertical-align: middle; border: 1px solid #000; padding: 3px; font-size: 12px;">${item.purchaseQuantity || 0}</td>`;
      
      // 调整位置 - 二维码列 - 只在第一行显示并合并
      if (firstInGroup) {
        tableRows += `
          <td rowspan="${rowspan}" style="text-align: center; vertical-align: middle; border: 1px solid #000; padding: 3px;">
            ${qrCodeDataUrl ? 
              `<div style="display: flex; flex-direction: column; align-items: center;">
                <img src="${qrCodeDataUrl}" style="width: 80px; height: 80px; margin-bottom: 2px;">
                <div style="font-size: 10px; color: #606266;">${item.subPurchaseOrderSn || ''}</div>
              </div>` : 
              '<span style="color: #999; font-size: 12px;">无二维码</span>'
            }
          </td>
        `;
      }
      
      // 拣货数列
      tableRows += `<td style="text-align: center; vertical-align: middle; border: 1px solid #000; padding: 3px; font-size: 12px;"></td>`;
      
      tableRows += '</tr>';
    });
    
    // 如果需要显示合计行，添加合计行
    if (needSummary) {
      tableRows += '<tr class="summary-row">';
      
      // 不需要添加序号列、店铺代号列、商品信息列和二维码列（这些列已经被上面的行合并了）
      
      // 属性集、SKU ID、SKU货号合并为一个单元格，显示"合计"
      tableRows += `<td colspan="3" style="text-align: center; vertical-align: middle; border: 1px solid #000; padding: 3px; font-size: 12px;">合计</td>`;
      
      // 数量列显示总计数量
      tableRows += `<td style="text-align: center; vertical-align: middle; border: 1px solid #000; padding: 3px; font-size: 12px; font-weight: bold;">${totalQuantity}</td>`;
      
      // 拣货数列保留为空（注意位置已变）
      tableRows += `<td style="text-align: center; vertical-align: middle; border: 1px solid #000; padding: 3px; font-size: 12px;"></td>`;
      
      tableRows += '</tr>';
    }
    
    currentIndex++;
  });
  
  // 生成完整的HTML
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>拣货单</title>
      <style>
        body { 
          font-family: Arial, "Microsoft YaHei", sans-serif; 
          margin: 0; 
          padding: 10px;
          font-size: 12px;
        }
        table { 
          width: 100%; 
          border-collapse: collapse; 
          margin-bottom: 10px;
          table-layout: fixed;
          border: 1px solid #000;
        }
        th, td { 
          padding: 3px; 
          border: 1px solid #000; 
          font-size: 12px;
          line-height: 1.2;
          empty-cells: show;
        }
        th { 
          background-color: #f2f2f2; 
          text-align: center; 
          font-weight: bold;
          padding: 3px;
          font-size: 12px;
        }
        img {
          max-width: 80px;
          max-height: 80px;
        }
        .page-footer { 
          text-align: right; 
          font-size: 11px; 
          margin-top: 10px;
        }
        /* 合计行样式 */
        tr.summary-row td {
          border-top: 1px solid #000;
          border-bottom: 1px solid #000;
          background-color: #fafafa;
        }
        tr.summary-row td:first-child {
          text-align: right;
          padding-right: 10px;
        }
        /* 移除表尾边框 */
        tfoot tr td {
          border: none !important;
          height: 0 !important;
          padding: 0 !important;
          line-height: 0 !important;
        }
        @media print {
          @page { 
            size: A4 portrait; 
            margin: 20mm 10mm 10mm 10mm; /* 调整边距以适应页眉内容 */

            /* 页眉右侧：页码 */
            @top-right {
              content: "第 " counter(page) " 页"; /* 仅当前页 */
              font-size: 10px;
              color: #666;
              padding-top: 5mm; /* 微调位置 */
            }

            /* 清空默认页脚内容 */
            @bottom-left {
              content: "";
            }
            @bottom-right {
              content: "";
            }
            @bottom-center {
              content: "";
            }
          }

          /* 仅第一页的页眉中间：标题 */
          @page :first {
            @top-center {
              content: "拣货单";
              font-size: 18px; /* 标题字体 */
              font-weight: bold;
              padding-top: 5mm; /* 微调位置 */
              vertical-align: top; /* 尝试顶部对齐 */
            }
          }

          body { 
            margin: 0;
            padding: 5px;
          }
          
          /* 添加表格行分页控制 */
          table { page-break-inside: auto; }
          tr { 
            page-break-inside: avoid; /* 防止行内分页 */
            page-break-after: auto; 
          }
          thead { display: table-header-group; } /* 表头在每页重复显示 */
          tfoot { display: table-footer-group; } /* 表尾在每页重复显示 */
          
          /* 确保图片不会导致页面过高 */
          img {
            max-height: 80px;
          }
          
          /* 优化商品信息列高度 */
          td[style*="vertical-align: top"] {
            max-height: 150px;
            overflow: hidden;
          }
        }
      </style>
    </head>
    <body data-print-time="${formattedDate}">
      <table>
        <thead>
          <tr>
            <th style="width: 4%;">序号</th>
            <th style="width: 5%;">店铺代号</th>
            <th style="width: 35%;">商品信息</th>
            <th style="width: 12%;">属性集</th>
            <th style="width: 12%;">SKU ID</th>
            <th style="width: 8%;">SKU货号</th>
            <th style="width: 6%;">数量</th>
            <th style="width: 12%;">二维码</th>
            <th style="width: 6%;">拣货数</th>
          </tr>
        </thead>
        <tbody>
          ${tableRows}
        </tbody>
        <tfoot>
          <tr><td colspan="9" style="border: none; height: 0; padding: 0;"></td></tr>
        </tfoot>
      </table>
    </body>
    </html>
  `;
}
</script>

<style scoped>
/* 拣货单预览弹窗样式 */
.picking-preview-dialog {
  margin-top: 5vh;
}

.picking-preview-dialog :deep(.el-dialog__body) {
  padding: 10px 20px;
}

.picking-preview-content {
  width: 100%;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 排序选项样式 */
.sorting-options {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.sorting-label {
  font-weight: bold;
  margin-right: 10px;
}
</style> 