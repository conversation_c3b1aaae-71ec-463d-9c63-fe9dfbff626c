package com.xiao.temu.modules.violation.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 违规信息和质检结果综合查询DTO
 */
@Data
public class ViolationInspectionQueryDTO {
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页记录数
     */
    private Integer pageSize = 10;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺ID列表
     */
    private List<Long> shopIds;
    
    /**
     * 运营组ID
     */
    private Long groupId;
    
    /**
     * 违规编号
     */
    private String punishSn;
    
    /**
     * 备货单号
     */
    private String subPurchaseOrderSn;
    
    /**
     * 违规开始时间起始
     */
    private LocalDateTime violationTimeStart;
    
    /**
     * 违规开始时间结束
     */
    private LocalDateTime violationTimeEnd;
    
    /**
     * 商品SKU ID
     */
    private Long productSkuId;
    
    /**
     * 外部编码
     */
    private String extCode;
    
    /**
     * 违规类型代码
     */
    private Integer punishTypeCode;
    
    /**
     * 违规状态码
     */
    private Integer punishStatus;
} 