<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.production.mapper.ProductionGroupRoleAssignmentMapper">

    <resultMap id="productionGroupRoleAssignmentMap" type="com.xiao.temu.modules.production.entity.ProductionGroupRoleAssignment">
        <id property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="userId" column="user_id"/>
        <result property="roleId" column="role_id"/>
        <result property="assignTime" column="assign_time"/>
        <result property="assignBy" column="assign_by"/>
        <result property="status" column="status"/>
    </resultMap>
    
    <resultMap id="productionGroupRoleAssignmentDTOMap" type="com.xiao.temu.modules.production.dto.ProductionGroupRoleAssignmentDTO">
        <id property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="groupName" column="group_name"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="assignTime" column="assign_time"/>
        <result property="assignBy" column="assign_by"/>
        <result property="assignByName" column="assign_by_name"/>
        <result property="status" column="status"/>
    </resultMap>
    
    <!-- 分页查询角色分配列表 -->
    <select id="selectAssignmentList" resultMap="productionGroupRoleAssignmentDTOMap">
        SELECT 
            a.id, 
            a.group_id, 
            g.group_name, 
            a.user_id, 
            u.username as user_name, 
            u.nick_name, 
            a.role_id, 
            r.role_name, 
            r.role_key,
            a.assign_time, 
            a.assign_by, 
            au.nick_name as assign_by_name, 
            a.status
        FROM production_group_role_assignment a
        JOIN production_group g ON a.group_id = g.group_id
        JOIN sys_user u ON a.user_id = u.user_id
        JOIN sys_role r ON a.role_id = r.role_id
        JOIN sys_user au ON a.assign_by = au.user_id
        <where>
            a.group_id = #{groupId}
            <if test="userId != null">
                AND a.user_id = #{userId}
            </if>
            <if test="roleId != null">
                AND a.role_id = #{roleId}
            </if>
        </where>
        ORDER BY a.assign_time DESC
    </select>
    
    <!-- 查询用户在生产组中的角色ID列表 -->
    <select id="selectUserRoleIds" resultType="java.lang.Long">
        SELECT role_id
        FROM production_group_role_assignment
        WHERE group_id = #{groupId} AND user_id = #{userId} AND status = '0'
    </select>
    
    <!-- 查询角色分配详情 -->
    <select id="selectAssignmentById" resultMap="productionGroupRoleAssignmentDTOMap">
        SELECT 
            a.id, 
            a.group_id, 
            g.group_name, 
            a.user_id, 
            u.username as user_name, 
            u.nick_name, 
            a.role_id, 
            r.role_name, 
            r.role_key,
            a.assign_time, 
            a.assign_by, 
            au.nick_name as assign_by_name, 
            a.status
        FROM production_group_role_assignment a
        JOIN production_group g ON a.group_id = g.group_id
        JOIN sys_user u ON a.user_id = u.user_id
        JOIN sys_role r ON a.role_id = r.role_id
        JOIN sys_user au ON a.assign_by = au.user_id
        WHERE a.id = #{id}
    </select>
    
    <!-- 删除用户在生产组中的所有角色 -->
    <delete id="deleteUserRoles">
        DELETE FROM production_group_role_assignment
        WHERE group_id = #{groupId} AND user_id = #{userId}
    </delete>
    
    <!-- 删除生产组的所有角色分配 -->
    <delete id="deleteAllGroupAssignments">
        DELETE FROM production_group_role_assignment
        WHERE group_id = #{groupId}
    </delete>
    
    <!-- 检查用户是否拥有指定角色 -->
    <select id="checkUserHasRole" resultType="int">
        SELECT COUNT(1)
        FROM production_group_role_assignment
        WHERE group_id = #{groupId} AND user_id = #{userId} AND role_id = #{roleId} AND status = '0'
    </select>
    
    <!-- 批量查询多个用户的角色ID列表 -->
    <select id="batchSelectUserRoleIds" resultType="java.util.Map">
        SELECT user_id, role_id
        FROM production_group_role_assignment
        WHERE group_id = #{groupId} AND status = '0' AND user_id IN
        <foreach item="userId" collection="userIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>
    
    <!-- 批量查询多个用户的角色分配信息 -->
    <select id="batchSelectUserRoleAssignments" resultMap="productionGroupRoleAssignmentDTOMap">
        SELECT 
            a.id, 
            a.group_id, 
            g.group_name, 
            a.user_id, 
            u.username as user_name, 
            u.nick_name, 
            a.role_id, 
            r.role_name, 
            r.role_key,
            a.assign_time, 
            a.assign_by, 
            au.nick_name as assign_by_name, 
            a.status
        FROM production_group_role_assignment a
        JOIN production_group g ON a.group_id = g.group_id
        JOIN sys_user u ON a.user_id = u.user_id
        JOIN sys_role r ON a.role_id = r.role_id
        JOIN sys_user au ON a.assign_by = au.user_id
        WHERE a.group_id = #{groupId} AND a.status = '0' AND a.user_id IN
        <foreach item="userId" collection="userIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
        ORDER BY a.user_id, a.assign_time DESC
    </select>
</mapper> 