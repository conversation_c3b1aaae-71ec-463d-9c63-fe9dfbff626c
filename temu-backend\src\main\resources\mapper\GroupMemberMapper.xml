<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.operation.mapper.GroupMemberMapper">

    <!-- 结果映射 -->
    <resultMap id="MemberResultMap" type="com.xiao.temu.modules.operation.dto.GroupMemberDTO">
        <id property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="userId" column="user_id"/>
        <result property="username" column="username"/>
        <result property="nickName" column="nick_name"/>
        <result property="phonenumber" column="phone"/>
        <result property="joinTime" column="join_time"/>
        <result property="status" column="status"/>
        <result property="isLeader" column="is_leader"/>
        <collection property="roles" ofType="com.xiao.temu.modules.system.dto.RoleDTO">
            <id property="roleId" column="role_id"/>
            <result property="roleName" column="role_name"/>
            <result property="roleKey" column="role_key"/>
        </collection>
    </resultMap>
    
    <!-- 基本结果映射 -->
    <resultMap id="BaseResultMap" type="com.xiao.temu.modules.operation.entity.GroupMember">
        <id property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="userId" column="user_id"/>
        <result property="joinTime" column="join_time"/>
        <result property="status" column="status"/>
    </resultMap>
    
    <!-- 分页查询运营组成员列表 -->
    <select id="selectMemberList" resultMap="MemberResultMap">
        SELECT gm.*, 
               u.username,
               u.nick_name,
               u.phone,
               CASE WHEN g.leader_id = gm.user_id THEN 1 ELSE 0 END AS is_leader,
               r.role_id,
               r.role_name,
               r.role_key
        FROM group_member gm
        LEFT JOIN sys_user u ON gm.user_id = u.user_id
        LEFT JOIN operation_group g ON gm.group_id = g.group_id
        LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
        LEFT JOIN sys_role r ON ur.role_id = r.role_id
        WHERE gm.group_id = #{groupId}
          AND gm.status = '0'
        ORDER BY is_leader DESC, gm.join_time ASC
    </select>
    
    <!-- 查询运营组成员列表（不分页） -->
    <select id="selectAllMembers" resultMap="MemberResultMap">
        SELECT gm.*, 
               u.username,
               u.nick_name,
               u.phone,
               CASE WHEN g.leader_id = gm.user_id THEN 1 ELSE 0 END AS is_leader,
               r.role_id,
               r.role_name,
               r.role_key
        FROM group_member gm
        LEFT JOIN sys_user u ON gm.user_id = u.user_id
        LEFT JOIN operation_group g ON gm.group_id = g.group_id
        LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
        LEFT JOIN sys_role r ON ur.role_id = r.role_id
        WHERE gm.group_id = #{groupId}
          AND gm.status = '0'
        ORDER BY is_leader DESC, gm.join_time ASC
    </select>
    
    <!-- 查询用户是否为运营组成员 -->
    <select id="checkUserInGroup" resultType="int">
        SELECT COUNT(1)
        FROM group_member
        WHERE group_id = #{groupId}
          AND user_id = #{userId}
          AND status = '0'
    </select>
    
    <!-- 统计运营组成员数量 -->
    <select id="countGroupMembers" resultType="int">
        SELECT COUNT(1)
        FROM group_member
        WHERE group_id = #{groupId}
          AND status = '0'
    </select>
    
    <!-- 批量添加运营组成员 -->
    <insert id="batchAddMembers">
        INSERT IGNORE INTO group_member(group_id, user_id, join_time, status)
        VALUES
        <foreach collection="userIds" item="userId" separator=",">
            (#{groupId}, #{userId}, #{joinTime}, '0')
        </foreach>
    </insert>
    
    <!-- 删除运营组成员 -->
    <delete id="deleteMember">
        DELETE FROM group_member
        WHERE group_id = #{groupId}
          AND user_id = #{userId}
    </delete>
    
    <!-- 根据运营组ID删除所有成员 -->
    <delete id="deleteByGroupId">
        DELETE FROM group_member
        WHERE group_id = #{groupId}
    </delete>
    
    <!-- 根据运营组ID查询用户ID列表 -->
    <select id="selectUserIdsByGroupId" resultType="java.lang.Long">
        SELECT user_id
        FROM group_member
        WHERE group_id = #{groupId}
          AND status = '0'
    </select>
    
    <!-- 根据运营组ID查询所有成员 -->
    <select id="selectByGroupId" resultMap="BaseResultMap">
        SELECT id, group_id, user_id, join_time, status
        FROM group_member
        WHERE group_id = #{groupId}
          AND status = '0'
    </select>
    
</mapper> 