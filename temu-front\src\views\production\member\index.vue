<template>
  <div class="app-container">
    <div class="group-info">
      <el-card class="box-card">
        <div class="card-header">
          <span class="header-title">生产组信息</span>
          <el-select 
            v-if="accessibleGroups.length > 0" 
            v-model="selectedGroupId" 
            placeholder="切换生产组"
            @change="handleGroupChange"
            class="group-selector"
            filterable
          >
            <template #prefix>
              <el-icon><folder-add /></el-icon>
            </template>
            <el-option
              v-for="group in accessibleGroups"
              :key="group.groupId"
              :label="group.groupName"
              :value="group.groupId"
            />
          </el-select>
        </div>
     
        <el-descriptions column="3" border>
          <el-descriptions-item label="生产组名称">{{ groupInfo.groupName }}</el-descriptions-item>
          <el-descriptions-item label="负责人">{{ groupInfo.leaderName }}</el-descriptions-item>
          <el-descriptions-item label="成员数量">{{ total }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <div class="member-container">
      <div class="member-header">
        <span class="header-title">成员列表</span>
      </div>
      <div class="toolbar">
        <el-form :model="queryParams" ref="queryRef" :inline="true" class="search-form">
          <el-form-item label="成员名称" prop="keyword">
            <el-input
              v-model="queryParams.keyword"
              placeholder="请输入成员名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="action-btns">
          <el-button
            type="success"
            icon="Plus"
            @click="handleCreateUser"
            v-hasPermi="['production:member:add']"
          >创建新用户</el-button>
        </div>
      </div>

      <!-- 成员表格 -->
      <el-table
        v-loading="loading"
        :data="memberList"
        style="width: 100%"
        border
      >
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column label="用户名称" prop="nickName" min-width="120" />
        <el-table-column label="手机号码" prop="phone" width="120" />
        <el-table-column label="加入时间" prop="joinTime" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.joinTime) }}
          </template>
        </el-table-column>
        <el-table-column label="拥有角色" prop="roleNames" min-width="150">
          <template #default="scope">
            <el-tag
              v-for="role in scope.row.roles"
              :key="role.roleId"
              class="role-tag"
              type="success"
            >{{ role.roleName }}</el-tag>
            <span v-if="!scope.row.roles || scope.row.roles.length === 0">无</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" align="center">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="Edit"
              @click="handleAssignRole(scope.row)"
              v-hasPermi="['production:role:assign']"
            >分配角色</el-button>
            <el-dropdown @command="(command) => handleUserAction(command, scope.row)" v-hasPermi="['production:member:remove']">
              <el-button type="danger" link icon="Delete">
                操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="remove">从组中移除</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>彻底删除用户</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="total > 0"
        class="pagination"
        :total="total"
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建用户对话框 -->
    <el-dialog
      title="创建新用户"
      v-model="createUserDialog.visible"
      width="500px"
      append-to-body
    >
      <div class="dialog-content">
        <el-form :model="createUserForm" :rules="createUserRules" ref="createUserRef" label-width="100px">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="createUserForm.username" placeholder="请输入用户名" />
          </el-form-item>
          <el-form-item label="昵称" prop="nickName">
            <el-input v-model="createUserForm.nickName" placeholder="请输入昵称" />
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="createUserForm.email" placeholder="请输入邮箱" />
          </el-form-item>
          <el-form-item label="手机号码" prop="phone">
            <el-input v-model="createUserForm.phone" placeholder="请输入手机号码" />
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input v-model="createUserForm.password" placeholder="请输入密码" type="password" show-password />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="createUserForm.status">
              <el-radio label="0">正常</el-radio>
              <el-radio label="1">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="createUserForm.remark" type="textarea" :rows="2" placeholder="请输入备注信息" />
          </el-form-item>
          <el-form-item label="选择角色" prop="roleIds">
            <el-select v-model="createUserForm.roleIds" multiple placeholder="请选择角色">
              <el-option
                v-for="role in roleOptions"
                :key="role.roleId"
                :label="role.roleName"
                :value="role.roleId"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="createUserDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitCreateUser" :loading="createUserLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 分配角色对话框 -->
    <el-dialog
      title="分配角色"
      v-model="assignRoleDialog.visible"
      width="1000px"
      append-to-body
    >
      <div class="dialog-content">
        <el-form label-width="80px">
          <el-form-item label="用户">
            <span>{{ assignRoleDialog.userData.nickName }}</span>
          </el-form-item>
          <el-form-item label="选择角色">
            <el-transfer
              v-model="assignRoleDialog.selectedRoles"
              :data="roleOptions"
              :titles="['可分配角色', '已分配角色']"
              :props="{
                key: 'roleId',
                label: 'roleName'
              }"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="assignRoleDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitAssignRole" :loading="assignRoleLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import type { FormInstance } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getMemberList, removeMember, createUserAndAddToGroup, deleteUser } from '@/api/production/member';
import { getUserRoles, assignRole, getAssignableRoles, removeRole, batchAssignRoles, getRoleAssignmentList, batchGetUserRoles, batchGetUserRoleAssignments } from '@/api/production/role';
import { useRouter, useRoute } from 'vue-router';
import { formatTime as formatDateTime } from '@/utils/format';
import { getLeaderGroups, getMemberGroups, getGroupList } from '@/api/production/group';
import { useUserStore } from '@/store/modules/user';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 判断当前用户是否是管理员
const isAdmin = computed(() => {
  // 方法1：通过角色名称判断
  const adminRoles = ['admin', 'administrator', '管理员'];
  const hasAdminRole = userStore.roles.some(role => adminRoles.includes(role.toLowerCase()));
  
  // 方法2：通过权限判断
  const hasFullPermission = userStore.permissions.includes('*:*:*');
  
  console.log('用户角色:', userStore.roles);
  console.log('用户权限:', userStore.permissions);
  console.log('是否有管理员角色:', hasAdminRole);
  console.log('是否有完全权限:', hasFullPermission);
  
  return hasAdminRole || hasFullPermission;
});

// 从路由获取生产组ID
const groupId = computed(() => {
  const idFromRoute = route.query.groupId;
  if (idFromRoute) {
    return parseInt(idFromRoute.toString(), 10);
  }
  return 0;
});

// 生产组信息
const groupInfo = ref<any>({
  groupId: 0,
  groupName: '',
  leaderName: '',
});

// 当前用户所能访问的生产组列表
const accessibleGroups = ref<any[]>([]);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  keyword: '',
  groupId: 0
});

// 表格数据
const loading = ref(false);
const memberList = ref<any[]>([]);
const total = ref(0);

// 创建用户对话框
const createUserDialog = reactive({
  visible: false
});
const createUserForm = reactive({
  username: '',
  nickName: '',
  email: '',
  phone: '',
  password: '',
  status: '0',
  remark: '',
  roleIds: [] as number[]
});
const createUserRef = ref<FormInstance>();
const createUserLoading = ref(false);
const createUserRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  nickName: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  roleIds: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
};

// 分配角色对话框
const assignRoleDialog = reactive({
  visible: false,
  userData: {} as any,
  selectedRoles: [] as number[],
  originalRoles: [] as number[]
});
const roleOptions = ref<any[]>([]);
const assignRoleLoading = ref(false);

// 引用
const queryRef = ref<FormInstance>();
const multipleTable = ref();

// 当前选中的生产组ID（用于选择器的v-model绑定）
const selectedGroupId = ref<number>(0);

// 监听groupInfo.groupId的变化，同步到selectedGroupId
watch(() => groupInfo.value.groupId, (newGroupId) => {
  if (newGroupId && newGroupId !== selectedGroupId.value) {
    console.log('groupInfo.groupId发生变化，同步到selectedGroupId:', newGroupId);
    selectedGroupId.value = newGroupId;
  }
});

// 监听accessibleGroups变化，确保初始化时选择器有正确的值
watch(() => accessibleGroups.value, (groups) => {
  if (groups.length > 0 && groupInfo.value.groupId) {
    console.log('可访问组列表更新，确保选择器值正确');
    // 确保选择器的值与当前组ID一致
    selectedGroupId.value = groupInfo.value.groupId;
  }
}, { immediate: true });

// 生命周期钩子
onMounted(async () => {
  console.log('当前用户是否为管理员:', isAdmin.value);
  
  // 获取当前用户可访问的生产组列表
  await getAccessibleGroups();
  console.log('获取到可访问生产组数量:', accessibleGroups.value.length);
  
  // 如果路由中有指定groupId
  if (groupId.value) {
    console.log('从路由获取到的groupId:', groupId.value);
    // 如果是管理员或者该组在可访问组列表中，则加载该组信息
    const canAccess = isAdmin.value || accessibleGroups.value.some(group => group.groupId === groupId.value);
    console.log('是否可以访问该组:', canAccess);
    
    if (canAccess) {
      groupInfo.value.groupId = groupId.value;
      // 初始化选择器的选中值
      selectedGroupId.value = groupId.value;
      
      // 查找组名和负责人信息
      const targetGroup = accessibleGroups.value.find(group => group.groupId === groupId.value);
      if (targetGroup) {
        groupInfo.value.groupName = targetGroup.groupName;
        groupInfo.value.leaderName = targetGroup.leaderName;
        // 存储到localStorage中，方便其他页面使用
        localStorage.setItem('productionGroup', JSON.stringify(groupInfo.value));
      } else if (isAdmin.value) {
        // 如果是管理员但没找到组信息，直接使用ID并请求后续数据
        console.log('管理员访问组，但组信息未在列表中找到，使用ID:', groupId.value);
        groupInfo.value = {
          groupId: groupId.value,
          groupName: `生产组${groupId.value}`,
          leaderName: '未知'
        };
        localStorage.setItem('productionGroup', JSON.stringify(groupInfo.value));
      }
    } else {
      // 没有访问权限，提示并跳转到生产组列表
      ElMessage.error('您没有权限访问该生产组');
      router.push('/production/group');
      return;
    }
  } else {
    // 如果没有指定groupId
    
    // 如果当前用户是管理员或普通用户，先尝试从localStorage获取最近访问的组
    const groupInfoStr = localStorage.getItem('productionGroup');
    if (groupInfoStr) {
      try {
        const savedGroup = JSON.parse(groupInfoStr);
        // 验证该组是否仍然在可访问组列表中
        const canAccess = isAdmin.value || accessibleGroups.value.some(group => group.groupId === savedGroup.groupId);
        
        if (canAccess && savedGroup.groupId) {
          groupInfo.value = savedGroup;
          // 设置选择器初始值
          selectedGroupId.value = savedGroup.groupId;
        } else if (accessibleGroups.value.length > 0) {
          // 使用第一个可访问的组
          groupInfo.value = accessibleGroups.value[0];
          // 设置选择器初始值
          selectedGroupId.value = accessibleGroups.value[0].groupId;
          localStorage.setItem('productionGroup', JSON.stringify(groupInfo.value));
        } else {
          // 没有可访问的组，提示并跳转回生产组列表
          ElMessage.error('未找到您可访问的生产组');
          router.push('/production/group');
          return;
        }
      } catch (e) {
        console.error('解析生产组信息失败', e);
        if (accessibleGroups.value.length > 0) {
          groupInfo.value = accessibleGroups.value[0];
          // 设置选择器初始值
          selectedGroupId.value = accessibleGroups.value[0].groupId;
          localStorage.setItem('productionGroup', JSON.stringify(groupInfo.value));
        } else {
          ElMessage.error('未找到您可访问的生产组');
          router.push('/production/group');
          return;
        }
      }
    } else if (accessibleGroups.value.length > 0) {
      // 使用第一个可访问的组
      groupInfo.value = accessibleGroups.value[0];
      // 设置选择器初始值
      selectedGroupId.value = accessibleGroups.value[0].groupId;
      localStorage.setItem('productionGroup', JSON.stringify(groupInfo.value));
    } else {
      // 没有可访问的组，提示并跳转回生产组列表
      ElMessage.error('未找到您可访问的生产组');
      router.push('/production/group');
      return;
    }
  }
  
  // 初始化groupId到查询参数中
  queryParams.groupId = groupInfo.value.groupId;
  
  // 确保有生产组ID
  if (!groupInfo.value.groupId) {
    ElMessage.error('生产组ID不能为空');
    router.push('/production/group');
    return;
  }
  
  // 获取成员列表
  getList();
  
  // 获取可分配的角色
  getRoleOptions();
});

// 获取当前用户可访问的生产组列表
const getAccessibleGroups = async () => {
  try {
    if (isAdmin.value) {
      // 管理员可以访问所有组，使用获取所有组列表的接口，设置较大的页大小确保获取全部
      const res = await getGroupList({
        pageNum: 1,
        pageSize: 100, // 设置较大值以获取全部
        status: '0' // 只获取正常状态的组
      });
      console.log('管理员获取所有生产组:', res);
      accessibleGroups.value = res.data.records || [];
    } else {
      // 非管理员只能查看自己的组
      const leaderGroupsRes = await getLeaderGroups();
      const memberGroupsRes = await getMemberGroups();
      
      console.log('组长获取的组:', leaderGroupsRes);
      console.log('成员获取的组:', memberGroupsRes);
      
      // 合并数组并去重
      const leaderGroups = leaderGroupsRes.data || [];
      const memberGroups = memberGroupsRes.data || [];
      
      const combinedGroups = [...leaderGroups];
      
      // 将memberGroups中不在leaderGroups中的组添加进来
      for (const memberGroup of memberGroups) {
        if (!combinedGroups.some(group => group.groupId === memberGroup.groupId)) {
          combinedGroups.push(memberGroup);
        }
      }
      
      accessibleGroups.value = combinedGroups;
    }
    
    console.log('获取到的可访问生产组列表:', accessibleGroups.value);
  } catch (error) {
    console.error('获取可访问的生产组失败', error);
    accessibleGroups.value = [];
  }
};

// 获取成员列表
const getList = async () => {
  if (!groupInfo.value.groupId) {
    ElMessage.error('生产组ID不能为空');
    return;
  }
  
  loading.value = true;
  try {
    // 获取当前生产组ID
    const currentGroupId = groupInfo.value.groupId;
    console.log('获取成员列表，当前组ID:', currentGroupId);
    
    // 组装查询参数
    const params = {
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      keyword: queryParams.keyword
    };
    
    // 1. 获取成员列表
    console.log('发送获取成员列表请求:', currentGroupId, params);
    const res = await getMemberList(currentGroupId, params);
    console.log('获取成员列表结果:', res);
    
    memberList.value = res.data.records;
    total.value = res.data.total;
    
    if (memberList.value.length === 0) {
      loading.value = false;
      return;
    }
    
    // 2. 提取用户ID列表
    const userIds = memberList.value.map(member => member.userId);
    
    // 3. 批量获取角色ID列表
    const roleIdsRes = await batchGetUserRoles(currentGroupId, userIds);
    const userRolesMap = roleIdsRes.data || {}; 
    
    // 4. 批量获取角色分配详情
    const assignmentsRes = await batchGetUserRoleAssignments(currentGroupId, userIds);
    const userAssignmentsMap = assignmentsRes.data || {};
    
    // 5. 为每个成员设置角色信息
    for (const member of memberList.value) {
      // 获取当前用户的角色ID列表和角色分配信息
      const roleIds = userRolesMap[member.userId] || [];
      member.roleAssignments = userAssignmentsMap[member.userId] || [];
      
      // 通过roleId查找角色名称和分配ID
      member.roles = roleIds.map((roleId: number) => {
        const role = roleOptions.value.find(r => r.roleId === roleId);
        // 找到对应的分配记录，获取分配ID
        const assignment = member.roleAssignments.find((a: any) => a.roleId === roleId);
        return { 
          roleId, 
          roleName: role ? role.roleName : `角色${roleId}`,
          assignmentId: assignment ? assignment.id : undefined
        };
      });
    }
  } catch (error) {
    console.error('获取成员列表失败', error);
    memberList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 获取可分配的角色选项
const getRoleOptions = async () => {
  try {
    const res = await getAssignableRoles();
    roleOptions.value = res.data;
  } catch (error) {
    console.error('获取角色选项失败', error);
    ElMessage.error('获取角色选项失败');
  }
};

// 查询操作
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 重置查询
const resetQuery = () => {
  queryRef.value?.resetFields();
  queryParams.keyword = '';
  handleQuery();
};

// 分页大小变化
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size;
  getList();
};

// 分页页码变化
const handleCurrentChange = (current: number) => {
  queryParams.pageNum = current;
  getList();
};

// 创建新用户
const handleCreateUser = () => {
  // 重置创建用户表单
  createUserForm.username = '';
  createUserForm.nickName = '';
  createUserForm.email = '';
  createUserForm.phone = '';
  createUserForm.password = '';
  createUserForm.status = '0';
  createUserForm.remark = '';
  createUserForm.roleIds = [];
  
  // 打开创建用户对话框
  createUserDialog.visible = true;
};

// 提交创建用户
const submitCreateUser = async () => {
  if (!createUserRef.value) return;
  
  await createUserRef.value.validate(async (valid) => {
    if (!valid) {
      return;
    }
    
    createUserLoading.value = true;
    try {
      // 确保roleIds是有效的数组
      const roleIdsArray = Array.isArray(createUserForm.roleIds) ? createUserForm.roleIds : [];
      console.log('提交的角色IDs:', roleIdsArray);
      
      // 创建用户数据
      const userData = {
        username: createUserForm.username,
        nickName: createUserForm.nickName,
        email: createUserForm.email,
        phone: createUserForm.phone,
        password: createUserForm.password,
        status: createUserForm.status,
        remark: createUserForm.remark,
        roleIds: roleIdsArray // 确保是数组
      };
      
      // 使用当前选择的生产组ID
      const currentGroupId = groupInfo.value.groupId;
      
      // 使用新API一步完成用户创建和添加到生产组
      const res = await createUserAndAddToGroup(currentGroupId, userData);
      
      ElMessage.success('创建用户并添加到生产组成功');
      createUserDialog.visible = false;
      getList(); // 刷新成员列表
    } catch (error: any) {
      console.error('创建用户失败', error);
      ElMessage.error(error.message || '创建用户失败');
    } finally {
      createUserLoading.value = false;
    }
  });
};

// 删除成员
const handleRemoveMember = (row: any) => {
  ElMessageBox.confirm(`确定要将成员"${row.nickName}"从生产组中移除吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await removeMember(groupInfo.value.groupId, row.userId);
      ElMessage.success('从生产组移除成员成功');
      getList();
    } catch (error: any) {
      console.error('移除成员失败', error);
      ElMessage.error(error.message || '移除成员失败');
    }
  }).catch(() => {});
};

// 彻底删除用户
const handleDeleteUser = (row: any) => {
  ElMessageBox.confirm(`确定要彻底删除用户"${row.nickName}"吗？此操作不可恢复！`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteUser(groupInfo.value.groupId, row.userId);
      ElMessage.success('删除用户成功');
      getList();
    } catch (error: any) {
      console.error('删除用户失败', error);
      ElMessage.error(error.message || '删除用户失败');
    }
  }).catch(() => {});
};

// 用户操作处理
const handleUserAction = (command: string, row: any) => {
  if (command === 'remove') {
    handleRemoveMember(row);
  } else if (command === 'delete') {
    handleDeleteUser(row);
  }
};

// 分配角色
const handleAssignRole = async (row: any) => {
  assignRoleDialog.userData = row;
  assignRoleDialog.selectedRoles = [];
  assignRoleLoading.value = true;
  
  try {
    // 直接使用成员对象上已有的角色信息
    const roleIds = row.roles.map((r: any) => r.roleId);
    assignRoleDialog.selectedRoles = roleIds;
    assignRoleDialog.originalRoles = [...roleIds];
    assignRoleDialog.visible = true;
  } catch (error) {
    console.error('获取用户角色失败', error);
    ElMessage.error('获取用户角色失败');
  } finally {
    assignRoleLoading.value = false;
  }
};

// 提交分配角色
const submitAssignRole = async () => {
  assignRoleLoading.value = true;
  try {
    // 找出需要新增的角色
    const toAdd = assignRoleDialog.selectedRoles.filter(
      roleId => !assignRoleDialog.originalRoles.includes(roleId)
    );
    
    // 找出需要移除的角色
    const toRemove = assignRoleDialog.originalRoles.filter(
      roleId => !assignRoleDialog.selectedRoles.includes(roleId)
    );
    
    let success = true;
    
    // 获取当前选择的生产组ID
    const currentGroupId = groupInfo.value.groupId;
    
    // 如果有添加或删除操作，直接使用批量分配一次性处理所有角色
    if (toAdd.length > 0 || toRemove.length > 0) {
      try {
        // 批量分配所有应该保留的角色（包括原有未移除的和新增的）
        await batchAssignRoles(
          currentGroupId,
          assignRoleDialog.userData.userId,
          assignRoleDialog.selectedRoles // 直接使用完整的选中角色列表
        );
        
        ElMessage.success('角色分配成功');
        assignRoleDialog.visible = false;
        getList(); // 刷新列表
        return;
      } catch (error) {
        console.error('批量分配角色失败', error);
        ElMessage.error('批量分配角色失败');
        return;
      }
    }
    
    // 如果没有任何变化，直接关闭对话框
    assignRoleDialog.visible = false;
    
  } catch (error: any) {
    console.error('分配角色失败', error);
    ElMessage.error(error.message || '分配角色失败');
  } finally {
    assignRoleLoading.value = false;
  }
};

// 格式化时间
const formatTime = (timestamp: string) => {
  return timestamp ? formatDateTime(timestamp) : '-';
};

// 处理生产组切换
const handleGroupChange = (newGroupId: number) => {
  console.log('切换生产组，新的组ID:', newGroupId, '当前组ID:', groupInfo.value.groupId);
  
  if (newGroupId && newGroupId !== groupInfo.value.groupId) {
    // 查找新的生产组信息
    const newGroup = accessibleGroups.value.find(group => group.groupId === newGroupId);
    
    if (newGroup) {
      console.log('找到新生产组信息:', newGroup);
      
      // 更新生产组信息
      groupInfo.value = {
        groupId: newGroup.groupId,
        groupName: newGroup.groupName,
        leaderName: newGroup.leaderName || '未知'
      };
      
      // 保存到localStorage
      localStorage.setItem('productionGroup', JSON.stringify(groupInfo.value));
      
      // 更新查询参数
      queryParams.groupId = newGroupId;
      queryParams.pageNum = 1;
      
      // 清空当前列表，显示加载中状态
      memberList.value = [];
      loading.value = true;
      
      // 立即重新加载成员列表
      console.log('开始重新加载成员列表');
      getList();
      
      // 更新URL，但不重新加载页面
      router.push({
        path: router.currentRoute.value.path,
        query: { ...router.currentRoute.value.query, groupId: newGroupId }
      });
    } else {
      console.error('未找到生产组信息:', newGroupId);
      ElMessage.error('未找到该生产组信息');
    }
  } else {
    console.log('选择的是当前组或无效组ID，不执行切换');
  }
};
</script>

<style scoped>
.group-info {
  margin-bottom: 20px;
}

.box-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 15px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  position: relative;
  padding-left: 10px;
}

.header-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 2px;
  height: 16px;
  width: 3px;
  background-color: #16a085;
  border-radius: 1px;
}

.group-selector {
  width: 220px;
}

.member-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 20px;
}

.member-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 15px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.search-form {
  flex: 1;
}

.action-btns {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.role-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

/* Transfer组件的样式 */
:deep(.el-transfer) {
  width: 100%;
}

:deep(.el-transfer__buttons) {
  padding: 0 10px;
}

:deep(.el-transfer-panel) {
  width: 40%;
}

:deep(.el-transfer-panel__body) {
  height: 300px;
}
</style> 