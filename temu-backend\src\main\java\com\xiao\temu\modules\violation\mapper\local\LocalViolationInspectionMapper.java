package com.xiao.temu.modules.violation.mapper.local;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.violation.entity.ShopViolationInfo;
import com.xiao.temu.modules.violation.dto.ViolationInspectionDTO;
import com.xiao.temu.modules.violation.dto.ViolationInspectionQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 违规信息和质检结果综合查询Mapper接口
 */
@Mapper
public interface LocalViolationInspectionMapper extends BaseMapper<ShopViolationInfo> {
    
    /**
     * 分页查询违规信息和质检结果数据
     *
     * @param page 分页对象
     * @param queryDTO 查询条件
     * @return 查询结果
     */
    List<ViolationInspectionDTO> selectViolationInspectionList(Page<ViolationInspectionDTO> page, @Param("query") ViolationInspectionQueryDTO queryDTO);
    
    /**
     * 查询指定违规编号的详细信息
     *
     * @param shopId 店铺ID
     * @param punishSn 违规编号
     * @return 详细信息
     */
    List<ViolationInspectionDTO> selectViolationInspectionDetail(@Param("shopId") Long shopId, @Param("punishSn") String punishSn);
} 