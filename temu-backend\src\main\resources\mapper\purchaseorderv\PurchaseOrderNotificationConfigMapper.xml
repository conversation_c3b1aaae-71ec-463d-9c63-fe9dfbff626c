<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.purchaseorderv.mapper.PurchaseOrderNotificationConfigMapper">

    <resultMap id="BaseResultMap" type="com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderNotificationConfig">
        <id column="id" property="id"/>
        <result column="notification_type" property="notificationType"/>
        <result column="notification_name" property="notificationName"/>
        <result column="enabled" property="enabled"/>
        <result column="check_interval" property="checkInterval"/>
        <result column="normal_trigger_days" property="normalTriggerDays"/>
        <result column="jit_trigger_hours" property="jitTriggerHours"/>
        <result column="max_notify_count" property="maxNotifyCount"/>
        <result column="notify_interval_hours" property="notifyIntervalHours"/>
        <result column="template_code" property="templateCode"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="selectPurchaseOrderNotificationConfigVo">
        SELECT id, notification_type, notification_name, enabled, check_interval, normal_trigger_days,
               jit_trigger_hours, max_notify_count, notify_interval_hours, template_code, create_time, update_time
        FROM purchase_order_notification_config
    </sql>

    <select id="getByNotificationType" resultMap="BaseResultMap">
        <include refid="selectPurchaseOrderNotificationConfigVo"/>
        WHERE notification_type = #{notificationType}
    </select>
</mapper> 