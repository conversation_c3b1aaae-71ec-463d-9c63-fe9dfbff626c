<template>
  <div class="app-container">
    <!-- 头部区域：选择运营组 -->
    <div class="group-selection-container" >
      <el-card shadow="never" class="group-card"  >
        <template #header>
          <div class="card-header">
            <span class="header-title">运营组选择</span>
            <span v-if="isAdmin" class="admin-badge">
              <el-tag type="danger">管理员模式</el-tag>
            </span>
          </div>
        </template>
        <el-select 
          v-model="queryParams.groupId" 
          placeholder="请选择运营组" 
          @change="handleGroupChange" 
          style="width: 100%"
          :loading="isStoreLoading"
        >
          <el-option
            v-for="group in userGroups"
            :key="group.groupId"
            :label="group.groupName"
            :value="group.groupId"
          />
        </el-select>
      </el-card>
    </div>

    <!-- 无数据提示 -->
    <el-empty v-if="!selectedGroupId" description="请先选择一个运营组"></el-empty>
    
    <!-- 主体区域：分为组员和店铺两部分 -->
    <div v-if="selectedGroupId" class="main-container">
      <!-- 左侧：组员列表 -->
      <el-card shadow="hover" class="member-card">
        <template #header>
          <div class="card-header">
            <span class="header-title">组员列表</span>
            <div class="header-actions">
              <el-input
                v-model="memberSearchKeyword"
                placeholder="搜索组员"
                prefix-icon="Search"
                clearable
                class="search-input"
              />
              <el-button type="success" size="small" class="add-member-btn" @click="openCreateMemberDialog" v-if="selectedGroupId">
                <el-icon><Plus /></el-icon>
                <span>添加组员</span>
              </el-button>
            </div>
          </div>
        </template>
        <div v-if="isStoreLoading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>
        <div v-else-if="filteredGroupMembers.length === 0" class="empty-container">
          <el-empty description="暂无组员数据" />
        </div>
        <div v-else class="member-list">
          <div
            v-for="member in filteredGroupMembers"
            :key="member.userId"
            class="member-item"
            :class="{ 'member-item-active': selectedMember?.userId === member.userId }"
            @click="selectMember(member)"
          >
            <div class="member-avatar">
              <el-avatar :size="40">{{ member.nickName?.substring(0, 1) || member.userName?.substring(0, 1) || 'U' }}</el-avatar>
            </div>
            <div class="member-info">
              <div class="member-name">{{ member.nickName || member.userName }}</div>
              <div class="member-role">{{ member.roleName || '组员' }}</div>
            </div>
            <div class="member-status">
              <el-tag size="small" :type="getAssignedShopCount(member.userId) > 0 ? 'success' : 'info'">
                {{ getAssignedShopCount(member.userId) }} 家店铺
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 右侧：店铺分配区域 -->
      <el-card shadow="hover" class="shop-card">
        <template #header>
          <div class="card-header">
            <span class="header-title">
              {{ selectedMember ? `${selectedMember.nickName || selectedMember.userName} 的店铺分配` : '店铺分配' }}
            </span>
            <div class="header-actions">
              <el-input
                v-model="shopSearchKeyword"
                placeholder="搜索店铺"
                prefix-icon="Search"
                clearable
                class="search-input"
              />
              <el-button type="primary" :disabled="!selectedMember" @click="openAssignDialog">
                分配店铺
              </el-button>
            </div>
          </div>
        </template>
        
        <!-- 未选择组员的提示 -->
        <el-empty v-if="!selectedMember" description="请先选择左侧的组员"></el-empty>
        
        <!-- 组员的已分配店铺 -->
        <div v-else>
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="5" animated />
          </div>
          <div v-else-if="memberAssignments.length === 0" class="empty-container">
            <el-empty description="该组员暂未分配店铺">
              <el-button type="primary" @click="openAssignDialog">立即分配</el-button>
            </el-empty>
          </div>
          <div v-else>
            <el-table
              :data="filteredMemberAssignments"
              border
              style="width: 100%"
              row-key="id"
              v-loading="loading"
            >
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column label="店铺信息" min-width="200">
                <template #default="scope">
                  <div class="shop-info">
                    <div class="shop-name">{{ scope.row.shopName }}</div>
                    <div class="shop-id">ID: {{ scope.row.shopId }}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="权限" width="100" align="center">
                <template #default="scope">
                  <el-tag type="info">只读</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="状态" width="100" align="center">
                <template #default="scope">
                  <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
                    {{ scope.row.status === '0' ? '正常' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="分配时间" width="160" align="center">
                <template #default="scope">
                  {{ formatTime(scope.row.assignTime, 'YYYY-MM-DD HH:mm') }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180" align="center">
                <template #default="scope">
                  <el-button
                    size="small"
                    type="danger"
                    @click="handleDelete(scope.row)"
                  >
                    取消分配
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 分配店铺对话框 -->
    <AssignDialog
      v-model:visible="dialogVisible"
      :groupId="selectedGroupId"
      :userId="selectedMember?.userId"
      :userName="selectedMember?.nickName || selectedMember?.userName"
      @success="handleAssignSuccess"
    />
    
    <!-- 添加组员对话框 -->
    <CreateMemberDialog
      v-model:visible="createMemberDialogVisible"
      :groupId="selectedGroupId || 0"
      @success="handleCreateMemberSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount, watch } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useLeaderAssignStore } from '@/store/modules/leaderAssign'
import type { ShopAssignmentQuery } from '@/types/leaderAssign'
import AssignDialog from './components/AssignDialog.vue'
import CreateMemberDialog from './components/CreateMemberDialog.vue'
import { formatTime } from '@/utils/format'
import { Plus } from '@element-plus/icons-vue'

// 查询参数
const queryParams = reactive<ShopAssignmentQuery>({
  pageNum: 1,
  pageSize: 10,
  groupId: undefined,
  shopId: undefined,
  shopName: undefined,
  userId: undefined,
  userName: undefined,
  permissionType: undefined,
  status: undefined
})

// 搜索关键字
const memberSearchKeyword = ref('')
const shopSearchKeyword = ref('')

// 引用
const leaderAssignStore = useLeaderAssignStore()

// 状态变量
const loading = ref(false)
const isStoreLoading = computed(() => leaderAssignStore.loading)
const allAssignments = ref<any[]>([]) // 本地存储的所有分配信息
const memberAssignments = ref<any[]>([]) // 当前选中组员的分配记录
const total = computed(() => leaderAssignStore.total)
const userGroups = computed(() => leaderAssignStore.userGroups)
const selectedGroupId = computed(() => leaderAssignStore.selectedGroupId as number | undefined)
const groupMembers = computed(() => leaderAssignStore.groupMembers)
const assignableShops = computed(() => leaderAssignStore.assignableShops)
const isAdmin = computed(() => leaderAssignStore.isAdmin)

// 过滤后的组员列表
const filteredGroupMembers = computed(() => {
  if (!memberSearchKeyword.value) return groupMembers.value
  const keyword = memberSearchKeyword.value.toLowerCase()
  return groupMembers.value.filter(member => 
    (member.userName?.toLowerCase().includes(keyword) || member.nickName?.toLowerCase().includes(keyword))
  )
})

const dialogVisible = ref(false)
const createMemberDialogVisible = ref(false)
const selectedIds = ref<number[]>([])
const selectedMember = ref<any>(null)

// 过滤后的组员分配记录
const filteredMemberAssignments = computed(() => {
  if (!shopSearchKeyword.value) return memberAssignments.value
  const keyword = shopSearchKeyword.value.toLowerCase()
  return memberAssignments.value.filter(item => 
    item.shopName.toLowerCase().includes(keyword)
  )
})

// 获取组员已分配的店铺数量
const getAssignedShopCount = (userId: number) => {
  return allAssignments.value.filter(item => item.userId === userId).length
}

// 选择组员
const selectMember = (member: any) => {
  selectedMember.value = member
  loadMemberAssignments()
}

// 加载组员的分配记录
const loadMemberAssignments = async () => {
  if (!selectedMember.value || !selectedGroupId.value) return
  
  try {
    loading.value = true
    
    // 从本地缓存的所有分配中筛选当前选中组员的记录
    memberAssignments.value = allAssignments.value.filter(
      item => item.userId === selectedMember.value.userId
    )
  } catch (error) {
    console.error('加载组员分配记录失败:', error)
    ElMessage.error('加载组员分配记录失败')
    memberAssignments.value = []
  } finally {
    loading.value = false
  }
}

// 获取列表数据 (获取完整的分配列表)
const getList = async () => {
  console.log('正在获取完整分配列表数据...')
  try {
    // 使用新的API方法获取指定运营组的所有分配数据（不分页）
    if (!selectedGroupId.value) {
      console.warn('未选择运营组，无法获取数据')
      return
    }
    
    await leaderAssignStore.getAllAssignmentsByGroupId(selectedGroupId.value)
    // 将完整的分配列表保存到本地变量
    allAssignments.value = [...leaderAssignStore.assignmentList]
    console.log('获取到的完整数据:', allAssignments.value)
  } catch (error) {
    console.error('获取分配列表失败:', error)
    ElMessage.error('获取分配列表失败')
  }
}

// 加载关联数据
const loadRelatedData = async () => {
  if (!selectedGroupId.value) {
    console.warn('未选择运营组，无法加载关联数据')
    return
  }
  
  try {
    // 加载可分配的店铺
    console.log('正在加载可分配的店铺...')
    await leaderAssignStore.getAssignableShops(selectedGroupId.value)
    
    // 加载组员列表
    console.log('正在加载组员列表...')
    await leaderAssignStore.getGroupMembers(selectedGroupId.value)
  } catch (error) {
    console.error('加载关联数据失败:', error)
  }
}

// 组长验证
const isGroupLeader = (userId: number, groupId: number): boolean => {
  if (isAdmin.value) {
    return true; // 管理员拥有所有权限
  }
  
  // 从运营组中查找该组长
  const group = userGroups.value.find(g => g.groupId === groupId);
  if (!group) return false;
  
  return group.leaderId === userId;
}

// 运营组变更
const handleGroupChange = async (val: number) => {
  if (val) {
    leaderAssignStore.setSelectedGroup(val)
    selectedMember.value = null // 清除选中的组员
    memberAssignments.value = [] // 清空组员分配记录
    
    // 获取该组织的所有数据
    await loadRelatedData()
    await getList() // 获取完整的分配列表
  }
}

// 分配成功回调
const handleAssignSuccess = async () => {
  // 重新加载完整数据
  await getList()
  
  // 如果有选中的组员，重新加载该组员的分配记录
  if (selectedMember.value) {
    await loadMemberAssignments()
  }
}

// 打开分配对话框
const openAssignDialog = () => {
  if (!selectedGroupId.value) {
    ElMessage.warning('请先选择一个运营组')
    return
  }
  
  if (!selectedMember.value) {
    ElMessage.warning('请先选择一个组员')
    return
  }
  
  dialogVisible.value = true
}

// 打开创建组员对话框
const openCreateMemberDialog = () => {
  if (!selectedGroupId.value) {
    ElMessage.warning('请先选择一个运营组')
    return
  }
  
  createMemberDialogVisible.value = true
}

// 创建组员成功回调
const handleCreateMemberSuccess = async () => {
  // 重新加载组员列表
  if (selectedGroupId.value) {
    await leaderAssignStore.getGroupMembers(selectedGroupId.value)
  }
  
  ElMessage.success('组员已成功添加')
}

// 删除单条
const handleDelete = (row: any) => {
  ElMessageBox.confirm(
    `确定要取消 ${row.shopName} 对 ${selectedMember.value?.nickName || selectedMember.value?.userName} 的分配吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        await leaderAssignStore.unassignShop(row.id)
        ElMessage.success('取消分配成功')
        
        // 更新本地数据
        allAssignments.value = allAssignments.value.filter(item => item.id !== row.id)
        memberAssignments.value = memberAssignments.value.filter(item => item.id !== row.id)
        
        // 取消分配后，重新获取完整的分配列表，确保数据一致性
        await getList()
      } catch (error: any) {
        ElMessage.error(error.message || '操作失败')
      }
    })
    .catch(() => {})
}

onMounted(async () => {
  console.log('组件挂载，正在初始化数据...')
  
  // 检查当前用户是否为管理员
  leaderAssignStore.checkIsAdmin()
  
  // 获取用户所在的运营组
  await leaderAssignStore.getUserGroups()
  
  // 如果有选中的运营组，则加载该运营组的分配列表和关联数据
  if (selectedGroupId.value) {
    queryParams.groupId = selectedGroupId.value
    await getList()
    await loadRelatedData()
  }
})

onBeforeUnmount(() => {
  // 重置状态
  leaderAssignStore.resetState()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
  min-height: calc(100vh - 100px);
}

.group-selection-container {
  margin-bottom: 20px;
}

.group-card {
  width: 100%;
  margin: 0 auto;
}

.main-container {
  display: flex;
  gap: 20px;
  margin-top: 20px;
  height: calc(100vh - 250px);
}

.member-card {
  flex: 0 0 380px;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.shop-card {
  flex: 1;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  padding-right: 10px;
}

.header-title {
  font-size: 16px;
  font-weight: bold;
  white-space: nowrap;
  min-width: 80px;
  flex-shrink: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: nowrap;
  min-width: 240px;
}

.search-input {
  width: 150px;
  flex-shrink: 1;
}

.member-list {
  overflow-y: auto;
  height: 100%;
  padding-right: 5px;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s;
}

.member-item:hover {
  background-color: #f5f7fa;
}

.member-item-active {
  background-color: #ecf5ff;
}

.member-avatar {
  margin-right: 12px;
}

.member-info {
  flex: 1;
}

.member-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.member-role {
  font-size: 12px;
  color: #909399;
}

.member-status {
  margin-left: 12px;
}

.shop-info {
  display: flex;
  flex-direction: column;
}

.shop-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.shop-id {
  font-size: 12px;
  color: #909399;
}

.empty-container, .loading-container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 添加响应式布局 */
@media screen and (max-width: 1366px) {
  .main-container {
    flex-direction: column;
    height: auto;
  }
  
  .member-card, .shop-card {
    flex: 1;
    max-height: 500px;
    margin-bottom: 20px;
  }
  
  .header-actions {
    min-width: 200px;
  }
  
  .search-input {
    width: 120px;
  }
}

.add-member-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  white-space: nowrap;
}
</style> 