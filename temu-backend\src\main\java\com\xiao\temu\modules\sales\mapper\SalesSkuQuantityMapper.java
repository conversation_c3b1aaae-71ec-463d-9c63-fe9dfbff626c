package com.xiao.temu.modules.sales.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.sales.entity.SalesSkuQuantity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 销售SKU数量Mapper接口
 */
@Mapper
public interface SalesSkuQuantityMapper extends BaseMapper<SalesSkuQuantity> {

    /**
     * 获取指定店铺的销售SKU数量
     *
     * @param shopId 店铺ID
     * @return 数量
     */
    @Select("SELECT COUNT(*) FROM sales_sku_quantity WHERE shop_id = #{shopId}")
    Integer countByShopId(@Param("shopId") Long shopId);
} 