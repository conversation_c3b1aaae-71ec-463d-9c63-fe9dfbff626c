<template>
  <el-dialog v-model="dialogVisible" title="导出数据" width="500px" destroy-on-close>
    <el-form label-width="100px">
      <el-form-item label="导出类型">
        <el-radio-group v-model="exportType">
          <el-radio label="current">当前页数据</el-radio>
          <el-radio label="custom">自定义数量</el-radio>
          <el-radio label="all">所有数据</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="文件名称">
        <el-input v-model="fileName" placeholder="请输入导出的文件名称" />
      </el-form-item>
      <el-form-item v-if="exportType === 'custom'" label="导出页码范围">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-input-number
            v-model="startPage"
            :min="1"
            :max="Math.ceil(total / (queryParams.pageSize || 10))"
            @change="updateExportInfo"
          />
          <span>至</span>
          <el-input-number
            v-model="endPage"
            :min="startPage"
            :max="Math.ceil(total / (queryParams.pageSize || 10))"
            @change="updateExportInfo"
          />
          <span>页</span>
        </div>
        <div style="font-size: 12px; color: #909399; margin-top: 5px">
          总数据量: {{ exportDataCount }} 条
        </div>
      </el-form-item>
    </el-form>
    <div class="export-info-block" v-if="exportType === 'all' || exportType === 'custom'">
      <el-alert
        title="导出任务将在后台执行"
        type="info"
        description="导出过程中请勿关闭页面，导出完成后将自动下载"
        show-icon
      />
    </div>
    <template #footer>
      <el-button @click="cancelExport">取消</el-button>
      <el-button type="primary" @click="confirmExport">确认导出</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits, toRefs, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Warning } from '@element-plus/icons-vue'
import { useExportTaskStore } from '@/store'
import { saveAs } from 'file-saver'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dataType: {
    type: String,
    required: true,
    validator: (value: string) => ['qc', 'refund', 'violation'].includes(value)
  },
  defaultFileName: {
    type: String,
    default: '导出数据'
  },
  currentPageData: {
    type: Array,
    default: () => []
  },
  total: {
    type: Number,
    default: 0
  },
  fetchDataFn: {
    type: Function,
    required: true
  },
  queryParams: {
    type: Object,
    default: () => ({})
  },
  useBackendExport: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible'])

const { visible, dataType, defaultFileName, currentPageData, total, fetchDataFn, queryParams, useBackendExport } = toRefs(props)

// 导出任务管理
const exportTaskStore = useExportTaskStore()

// 导出对话框状态
const dialogVisible = ref(false)

// 同步props和内部状态
watch(visible, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:visible', val)
  if (val && defaultFileName.value) {
    // 对话框打开时，更新文件名
    fileName.value = defaultFileName.value
  }
})

// 导出设置
const exportType = ref('current')
const fileName = ref('')
const startPage = ref(1) // 起始页码
const endPage = ref(1) // 结束页码

// 监听defaultFileName的变化，更新文件名
watch(defaultFileName, (newVal) => {
  if (newVal) {
    fileName.value = newVal;
  }
}, { immediate: true });

// 计算导出的数据量
const exportDataCount = computed(() => {
  if (exportType.value === 'current') {
    return Math.min(queryParams.value.pageSize || 10, currentPageData.value.length)
  } else if (exportType.value === 'custom') {
    const pageCount = endPage.value - startPage.value + 1
    return Math.min(pageCount * (queryParams.value.pageSize || 10), total.value)
  } else {
    return total.value
  }
})

// 更新导出信息
const updateExportInfo = () => {
  // 确保结束页不小于开始页
  if (endPage.value < startPage.value) {
    endPage.value = startPage.value
  }
  
  // 确保不超过最大页数
  const maxPage = Math.ceil(total.value / (queryParams.value.pageSize || 10))
  if (startPage.value > maxPage) {
    startPage.value = maxPage
  }
  if (endPage.value > maxPage) {
    endPage.value = maxPage
  }
}

// 取消导出
const cancelExport = () => {
  dialogVisible.value = false
}

// 获取数据类型文本
const getDataTypeText = (type: string) => {
  switch (type) {
    case 'qc':
      return '本地抽检明细'
    case 'refund':
      return '本地退货明细'
    case 'violation':
      return '店铺违规信息'
    default:
      return '数据'
  }
}

// 获取Worker动作
const getWorkerAction = (type: string) => {
  switch (type) {
    case 'qc':
      return 'exportQualityInspection'
    case 'refund':
      return 'exportLocalRefund'
    case 'violation':
      return 'exportViolation'
    default:
      return 'exportData'
  }
}

// 确认导出
const confirmExport = async () => {
  try {
    // 关闭导出对话框，在后台执行导出任务
    dialogVisible.value = false
    
    // 创建导出任务
    const taskName = `导出${
      exportType.value === 'current' 
        ? '当前页' 
        : exportType.value === 'custom' 
          ? `自定义页码` 
          : '全部'
    }${getDataTypeText(dataType.value)}`
    
    const task = exportTaskStore.createTask(taskName, fileName.value)
    
    // 模拟取消函数
    const abortController = new AbortController()
    exportTaskStore.setTaskCancelFn(task.id, () => {
      abortController.abort()
    })
    
    if (useBackendExport.value) {
      // 使用后端导出 - 调用fetchDataFn
      let exportParams: any = {
        fileName: fileName.value,
        exportType: exportType.value,
        queryParams: {
          ...queryParams.value
        }
      }
      
      if (exportType.value === 'current') {
        // 当前页数据导出，使用当前页码和每页大小
        exportParams.queryParams.pageNum = queryParams.value.pageNo || queryParams.value.pageNum || 1
        exportParams.queryParams.pageSize = queryParams.value.pageSize || 10
      } else if (exportType.value === 'custom') {
        // 自定义导出 - 使用页码范围
        exportParams.queryParams.pageNum = startPage.value
        exportParams.queryParams.pageSize = queryParams.value.pageSize || 10
        exportParams.queryParams.exportPageCount = endPage.value - startPage.value + 1
      } else {
        // 导出全部
        exportParams.queryParams.pageNum = 1
        exportParams.queryParams.pageSize = total.value
      }
      
      const result = await fetchDataFn.value({
        ...exportParams,
        taskId: task.id
      })
      
      if (result && result.success) {
        // 已经创建了后端导出任务，等待轮询更新进度
        return
      } else {
        exportTaskStore.failTask(task.id, '创建导出任务失败')
      }
    } else if (exportType.value === 'current') {
      // 当前页数据导出(前端处理)
      // 更新进度
      exportTaskStore.updateTaskProgress(task.id, 10)
      
      // 当前页数据直接导出
      const worker = new Worker('/excelWorker.js')
      worker.onmessage = (e) => {
        if (e.data.type === 'progress') {
          exportTaskStore.updateTaskProgress(task.id, e.data.progress)
        } else if (e.data.type === 'complete') {
          exportTaskStore.completeTask(task.id)
          worker.terminate()
          
          // 下载文件
          if (e.data.blob) {
            saveAs(new Blob([e.data.blob]), `${fileName.value}.xlsx`)
          }
        } else if (e.data.type === 'error') {
          exportTaskStore.failTask(task.id, e.data.message)
          worker.terminate()
        }
      }
      
      worker.postMessage({
        action: getWorkerAction(dataType.value),
        data: currentPageData.value,
        fileName: fileName.value
      })
    } else {
      // 自定义数量或全部导出(需要分批获取)
      // 更新进度
      exportTaskStore.updateTaskProgress(task.id, 5)
      
      // 记录配置信息
      const requestConfig = {
        dataType: dataType.value,
        fetchDataFn: fetchDataFn.value,
        params: {
          ...queryParams.value,
          pageNum: queryParams.value.pageNo || 1, // 从第一页开始
          pageSize: 500 // 每次获取的数量
        },
        totalCount: exportType.value === 'custom' ? 
          (endPage.value - startPage.value + 1) * (queryParams.value.pageSize || 10) : 
          total.value,
        fileName: fileName.value,
        workerAction: getWorkerAction(dataType.value),
        abortController: abortController,
        taskId: task.id,
        collected: [] // 收集的数据
      }
      
      // 开始批量导出
      await batchExport(requestConfig)
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败: ' + (error instanceof Error ? error.message : String(error)))
  }
}

/* 批量导出函数 */
const batchExport = async (config: any) => {
  try {
    const {
      dataType,
      fetchDataFn,
      params,
      totalCount,
      fileName,
      workerAction,
      abortController,
      taskId,
      collected
    } = config

    // 如果被终止，则中断
    if (abortController.signal.aborted) {
      throw new Error('导出已取消')
    }

    // 当前已收集的数据量
    const collectedCount = collected.length

    // 如果已收集的数据达到了需求量，开始处理Excel
    if (collectedCount >= totalCount) {
      // 更新进度，表示数据收集完成
      exportTaskStore.updateTaskProgress(taskId, 20)
      
      // 使用WebWorker创建Excel
      const worker = new Worker('/excelWorker.js')
      
      worker.onmessage = (e) => {
        const { type, progress, blob, error } = e.data
        
        if (type === 'progress') {
          // 数据收集占15%进度，剩余85%是Excel处理进度
          const adjustedProgress = 20 + Math.floor(progress * 0.8)
          exportTaskStore.updateTaskProgress(taskId, adjustedProgress)
        } else if (type === 'complete') {
          exportTaskStore.completeTask(taskId)
          worker.terminate()
          
          if (blob) {
            saveAs(new Blob([blob]), `${fileName}.xlsx`)
          }
        } else if (type === 'error') {
          exportTaskStore.failTask(taskId, error)
          worker.terminate()
        }
      }
      
      worker.onerror = (e) => {
        exportTaskStore.failTask(taskId, 'Excel处理错误')
        worker.terminate()
      }
      
      // 发送数据到Worker
      worker.postMessage({
        action: workerAction,
        data: collected,
        fileName: fileName
      })
      
      return
    }

    // 计算进度
    const progress = Math.floor((collectedCount / totalCount) * 15)
    exportTaskStore.updateTaskProgress(taskId, progress)
    
    // 继续获取下一批数据
    try {
      // 设置下一页
      const currentPage = Math.floor(collectedCount / params.pageSize) + 1
      const pageParams = { ...params, pageNum: currentPage }
      
      // 获取数据
      const response = await fetchDataFn(pageParams)
      
      if (response?.data?.items) {
        // 添加到收集的数据中
        const newItems = response.data.items
        
        // 计算本批次需要添加的量
        const remainingNeeded = totalCount - collectedCount
        let itemsToAdd
        
        if (remainingNeeded >= newItems.length) {
          // 全部添加
          itemsToAdd = newItems
        } else {
          // 只添加需要的部分
          itemsToAdd = newItems.slice(0, remainingNeeded)
        }
        
        collected.push(...itemsToAdd)
        
        // 递归处理下一批
        setTimeout(() => {
          batchExport(config)
        }, 50)
      } else {
        // 没有更多数据了
        if (collected.length > 0) {
          // 如果已经有收集的数据，开始处理Excel
          setTimeout(() => {
            batchExport({ ...config, totalCount: collected.length })
          }, 50)
        } else {
          exportTaskStore.failTask(taskId, '没有获取到可导出的数据')
        }
      }
    } catch (error) {
      if (abortController.signal.aborted) {
        // 如果是取消导致的，不显示错误
        return
      }
      
      if (collected.length > 0) {
        // 如果已经有收集的数据，开始处理Excel
        setTimeout(() => {
          batchExport({ ...config, totalCount: collected.length })
        }, 50)
      } else {
        const errorMsg = error instanceof Error ? error.message : '未知错误'
        exportTaskStore.failTask(taskId, `获取数据失败: ${errorMsg}`)
      }
    }
  } catch (error) {
    if (config.abortController.signal.aborted) {
      // 如果是取消导致的，不显示错误
      return
    }
    
    const errorMsg = error instanceof Error ? error.message : '未知错误'
    exportTaskStore.failTask(config.taskId, errorMsg)
  }
}
</script>

<style scoped>
.export-tip {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  gap: 5px;
}

.export-info-block {
  margin-top: 15px;
}
</style> 