package com.xiao.temu.modules.sync.job;

import com.xiao.temu.modules.sales.service.SalesStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

/**
 * 销售统计数据同步Quartz任务
 * 每日凌晨0点执行
 */
@Component
@Slf4j
public class SalesStatisticsQuartzJob extends QuartzJobBean {

    @Autowired
    private SalesStatisticsService salesStatisticsService;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        log.info("开始执行销售统计数据同步Quartz任务... 触发时间: {}", context.getFireTime());
        try {
            String result = salesStatisticsService.executeScheduledStatistics();
            log.info("销售统计数据同步Quartz任务执行结果: {}", result);
        } catch (Exception e) {
            log.error("销售统计数据同步Quartz任务执行异常", e);
            throw new JobExecutionException(e);
        }
    }
} 