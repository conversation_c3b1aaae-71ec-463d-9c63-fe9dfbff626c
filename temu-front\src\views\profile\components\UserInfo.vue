<template>
  <div class="user-info-container">
    <el-form 
      ref="formRef" 
      :model="userForm" 
      :rules="formRules" 
      label-width="100px" 
      v-loading="loading"
      class="user-form"
    >
      <div class="form-grid">
        <el-form-item label="用户名" prop="username">
          <el-input 
            v-model="userForm.username" 
            disabled 
            prefix-icon="User"
          />
        </el-form-item>
        
        <el-form-item label="昵称" prop="nickName">
          <el-input 
            v-model="userForm.nickName" 
            placeholder="请输入昵称" 
            prefix-icon="UserFilled"
          />
        </el-form-item>
        
        <el-form-item label="手机号码" prop="phone">
          <el-input 
            v-model="userForm.phone" 
            placeholder="请输入手机号码"
            maxlength="11"
            prefix-icon="Iphone"
          />
        </el-form-item>
        
        <el-form-item label="电子邮箱" prop="email">
          <el-input 
            v-model="userForm.email" 
            placeholder="请输入电子邮箱" 
            prefix-icon="Message"
          />
        </el-form-item>
      </div>
      

      
      <el-form-item class="form-actions">
        <el-button type="primary" @click="submitForm" :loading="submitting" icon="Check">保存更新</el-button>
        <el-button @click="resetForm" icon="RefreshRight">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { getUserProfile, updateUserProfile } from '@/api/user'

const emit = defineEmits(['rolesLoaded'])
const userStore = useUserStore()
const formRef = ref<FormInstance>()
const loading = ref(false)
const submitting = ref(false)

// 表单数据
const userForm = reactive({
  userId: null as null | number,
  username: '',
  nickName: '',
  phone: '',
  email: ''
})

// 表单校验规则
const formRules = reactive<FormRules>({
  nickName: [{ required: true, message: '昵称不能为空', trigger: 'blur' }],
  email: [
    { pattern: /^(\w+\.?)*\w+@(\w+\.)+\w+$/, message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
})

// 获取用户信息
const getInfo = async () => {
  loading.value = true
  try {
    const res = await getUserProfile()
    const userData = res.data.user
    
    // 更新表单数据
    userForm.userId = userData.userId
    userForm.username = userData.username
    userForm.nickName = userData.nickName || ''
    userForm.phone = userData.phone || ''
    userForm.email = userData.email || ''
    
    // 将角色信息发送给父组件
    if (res.data.roles && res.data.roles.length > 0) {
      emit('rolesLoaded', res.data.roles)
    }
  } catch (error) {
    console.error('获取用户信息失败', error)
    ElMessage.error('获取用户信息失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (!valid) return
    
    submitting.value = true
    try {
      await updateUserProfile(userForm)
      ElMessage.success('个人信息更新成功')
      
      // 更新Pinia中的用户信息
      userStore.updateUserInfo({
        nickName: userForm.nickName
      })
    } catch (error) {
      console.error('更新个人信息失败', error)
      ElMessage.error('更新个人信息失败')
    } finally {
      submitting.value = false
    }
  })
}

// 重置表单
const resetForm = () => {
  getInfo()
}

// 页面加载时获取用户信息
onMounted(() => {
  getInfo()
})
</script>

<style lang="scss" scoped>
.user-info-container {
  width: 100%;
  padding: 10px 0;
  display: flex;
  justify-content: center;
}

.user-form {
  width: 100%;
  max-width: 700px;
  
  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
  }
  
  :deep(.el-input.is-disabled .el-input__wrapper) {
    background-color: rgba(var(--el-fill-color-light), 0.6);
  }
  
  :deep(.el-input__wrapper) {
    padding-right: 15px;
    box-shadow: 0 0 0 1px var(--theme-border-color, #dcdfe6) inset;
    
    &:hover, &.is-focus {
      box-shadow: 0 0 0 1px #22a699 inset;
    }
  }
  
  :deep(.el-form-item__label) {
    font-weight: 500;
    color: var(--theme-text-color, #303133);
  }
  
  .form-actions {
    margin-top: 35px;
    display: flex;
    justify-content: center;
    
    :deep(.el-form-item__content) {
      justify-content: center;
      gap: 20px;
    }
  }
  
  :deep(.el-divider__text) {
    background-color: var(--theme-bg-color, #fff);
    color: var(--theme-text-color-secondary, #909399);
    font-size: 14px;
    font-weight: 500;
  }
  
  :deep(.el-textarea__inner) {
    min-height: 120px !important;
    font-family: inherit;
    line-height: 1.6;
    resize: none;
  }
  
  :deep(.el-button--primary) {
    background-color: #22a699;
    border-color: #22a699;
    padding: 10px 24px;
    
    &:hover, &:focus {
      background-color: #1c8a80;
      border-color: #1c8a80;
    }
  }
  
  :deep(.el-button:not(.el-button--primary)) {
    padding: 10px 24px;
  }
}

// 响应式布局
@media screen and (max-width: 768px) {
  .user-form {
    .form-grid {
      grid-template-columns: 1fr;
      gap: 15px;
    }
  }
}
</style> 