package com.xiao.temu.modules.purchaseorderv.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * 扫码更新进度数据传输对象
 */
@Data
public class ScanUpdateProgressDTO {

    /**
     * 店铺ID
     */
    @NotNull(message = "店铺ID不能为空")
    private Long shopId;

    /**
     * 备货单号
     */
    @NotBlank(message = "备货单号不能为空")
    private String subPurchaseOrderSn;

    /**
     * 进度类型(burning:烧花,sewing:车缝,tail:尾部,shipping:发货,delivery:送货)
     */
    @NotBlank(message = "进度类型不能为空")
    private String progressType;

    /**
     * 操作类型(1:完成 2:撤销) 默认为1完成
     */
    private String operationType = "1";

    /**
     * 备注
     */
    private String remarks;
} 