// 自定义复选框组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否选中
    checked: {
      type: Boolean,
      value: false,
      observer: function(newVal) {
        // 直接观察者立即更新isChecked
        console.log('复选框checked属性变更:', newVal);
        this.setData({ isChecked: newVal });
      }
    },
    // 选项值
    value: {
      type: String,
      value: ''
    },
    // 选项文本
    label: {
      type: String,
      value: ''
    },
    // 禁用状态
    disabled: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isChecked: false
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached: function() {
      // 组件加载时确保初始状态正确
      console.log('复选框组件初始化, checked:', this.properties.checked);
      this.setData({
        isChecked: this.properties.checked
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 切换选中状态
     */
    toggleCheck: function(e) {
      // 阻止事件冒泡，防止触发父容器的点击事件
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      
      if (this.data.disabled) return;

      const isChecked = !this.data.isChecked;
      console.log('复选框切换状态:', isChecked);
      
      this.setData({
        isChecked: isChecked
      });

      // 触发自定义事件
      this.triggerEvent('change', {
        value: this.data.value,
        checked: isChecked
      });
    }
  }
}) 