package com.xiao.temu.modules.purchaseorderv.dto;

import lombok.Data;

import java.util.Date;

/**
 * 生产进度数据传输对象
 */
@Data
public class ProductionProgressDTO {

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 备货单号
     */
    private String subPurchaseOrderSn;

    /**
     * 烧花/剪图状态(0未完成 1已完成)
     */
    private Integer cuttingStatus;

    /**
     * 车间/拣货状态(0未完成 1已完成)
     */
    private Integer workshopStatus;

    /**
     * 剪线/压图状态(0未完成 1已完成)
     */
    private Integer trimmingStatus;

    /**
     * 查货状态(0未完成 1已完成)
     */
    private Integer inspectionStatus;

    /**
     * 包装状态(0未完成 1已完成)
     */
    private Integer packagingStatus;

    /**
     * 发货状态(0未完成 1已完成)
     */
    private Integer shippingStatus;

    /**
     * 查询开始时间
     */
    private Date startTime;

    /**
     * 查询结束时间
     */
    private Date endTime;

    /**
     * 当前页
     */
    private Integer current;

    /**
     * 每页条数
     */
    private Integer size;
} 