package com.xiao.temu.infrastructure.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.xiao.temu.modules.system.entity.SysRole;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.system.dto.UserImportDTO;
import com.xiao.temu.modules.system.service.RoleService;
import com.xiao.temu.modules.system.service.UserService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户导入Excel监听器
 */
@Slf4j
public class UserImportListener extends AnalysisEventListener<UserImportDTO> {

    /**
     * 默认密码
     */
    private static final String DEFAULT_PASSWORD = "123456";

    /**
     * 数据处理批次大小
     */
    private static final int BATCH_SIZE = 100;

    /**
     * 用户服务
     */
    private final UserService userService;

    /**
     * 角色服务
     */
    private final RoleService roleService;

    /**
     * 密码编码器
     */
    private final PasswordEncoder passwordEncoder;

    /**
     * 存储待导入的用户数据
     */
    @Getter
    private final List<SysUser> userList = new ArrayList<>();

    /**
     * 存储用户与角色的映射关系
     */
    @Getter
    private final Map<String, List<Long>> userRoleMap = new HashMap<>();

    /**
     * 所有系统角色的映射表 - 角色名称 -> 角色ID
     */
    private Map<String, Long> roleNameMap;

    /**
     * 成功导入计数
     */
    @Getter
    private int successCount = 0;

    /**
     * 失败导入计数
     */
    @Getter
    private int failCount = 0;

    /**
     * 失败信息列表
     */
    @Getter
    private final List<String> errorMsgs = new ArrayList<>();

    /**
     * 构造函数
     */
    public UserImportListener(UserService userService, RoleService roleService, PasswordEncoder passwordEncoder) {
        this.userService = userService;
        this.roleService = roleService;
        this.passwordEncoder = passwordEncoder;
        initRoleNameMap();
    }

    /**
     * 初始化角色名称映射表
     */
    private void initRoleNameMap() {
        // 获取所有角色
        List<SysRole> roles = roleService.getAllRoles();
        // 构建角色名称到角色ID的映射
        this.roleNameMap = roles.stream()
                .collect(Collectors.toMap(SysRole::getRoleName, SysRole::getRoleId, (v1, v2) -> v1));
    }

    /**
     * 每解析一行数据，调用该方法
     */
    @Override
    public void invoke(UserImportDTO data, AnalysisContext context) {
        try {
            // 表格行号
            int rowIndex = context.readRowHolder().getRowIndex() + 1;
            
            // 校验数据
            String errorMsg = validateData(data, rowIndex);
            if (errorMsg != null) {
                failCount++;
                errorMsgs.add(errorMsg);
                return;
            }
            
            // 检查用户名是否唯一
            if (!userService.checkUsernameUnique(data.getUsername())) {
                failCount++;
                errorMsgs.add("第" + rowIndex + "行: 用户名 '" + data.getUsername() + "' 已存在");
                return;
            }
            
            // 转换为SysUser对象
            SysUser user = convertToSysUser(data);
            userList.add(user);
            
            // 处理角色信息
            if (StringUtils.isNotBlank(data.getRoleNames())) {
                String[] roleNames = data.getRoleNames().split(",|，"); // 支持中英文逗号分隔
                List<Long> roleIds = new ArrayList<>();
                
                for (String roleName : roleNames) {
                    String trimmedRoleName = roleName.trim();
                    Long roleId = roleNameMap.get(trimmedRoleName);
                    if (roleId != null) {
                        roleIds.add(roleId);
                    }
                }
                
                if (!roleIds.isEmpty()) {
                    userRoleMap.put(data.getUsername(), roleIds);
                }
            }
            
            // 如果达到批处理大小，则批量保存
            if (userList.size() >= BATCH_SIZE) {
                saveUserBatch();
            }
            
            successCount++;
        } catch (Exception e) {
            log.error("解析Excel第{}行数据出错", context.readRowHolder().getRowIndex() + 1, e);
            failCount++;
            errorMsgs.add("第" + (context.readRowHolder().getRowIndex() + 1) + "行: 数据解析出错 - " + e.getMessage());
        }
    }

    /**
     * 所有数据解析完成后调用
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 保存剩余用户
        saveUserBatch();
        log.info("Excel解析完成，成功导入{}条，失败{}条", successCount, failCount);
    }

    /**
     * 批量保存用户
     */
    private void saveUserBatch() {
        if (userList.isEmpty()) {
            return;
        }
        
        try {
            userService.batchImportUsers(userList, userRoleMap);
            userList.clear();
            userRoleMap.clear();
        } catch (Exception e) {
            log.error("批量保存用户出错", e);
            failCount += userList.size();
            successCount -= userList.size();
            errorMsgs.add("批量保存用户出错: " + e.getMessage());
            userList.clear();
            userRoleMap.clear();
        }
    }

    /**
     * 校验数据
     * @return 错误信息，如果没有错误则返回null
     */
    private String validateData(UserImportDTO data, int rowIndex) {
        // 校验用户名
        if (StringUtils.isBlank(data.getUsername())) {
            return "第" + rowIndex + "行: 用户名不能为空";
        }
        
        // 校验其他必填字段
        if (StringUtils.isBlank(data.getNickName())) {
            return "第" + rowIndex + "行: 昵称不能为空";
        }
        
        return null;
    }

    /**
     * 将DTO转换为SysUser对象
     */
    private SysUser convertToSysUser(UserImportDTO data) {
        SysUser user = new SysUser();
        user.setUsername(data.getUsername());
        user.setNickName(data.getNickName());
        user.setEmail(data.getEmail());
        user.setPhone(data.getPhone());
        user.setRemark(data.getRemark());
        user.setPassword(passwordEncoder.encode(DEFAULT_PASSWORD));
        user.setStatus("0"); // 默认正常状态
        user.setCreateTime(new Date());
        return user;
    }
} 