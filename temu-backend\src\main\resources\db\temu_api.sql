/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : temu_api

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 04/06/2025 01:20:51
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for api_call_log
-- ----------------------------
DROP TABLE IF EXISTS `api_call_log`;
CREATE TABLE `api_call_log`  (
  `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `shop_id` bigint NULL DEFAULT NULL COMMENT '店铺ID',
  `api_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'API名称',
  `request_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '请求URL',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '请求方法',
  `request_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '请求参数',
  `response_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '响应数据',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0成功 1失败）',
  `error_msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '错误信息',
  `cost_time` bigint NULL DEFAULT NULL COMMENT '耗时(毫秒)',
  `ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`log_id`) USING BTREE,
  INDEX `idx_shop_api`(`shop_id` ASC, `api_name` ASC) USING BTREE,
  INDEX `idx_user`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'API调用日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for group_leader_shop_assignment
-- ----------------------------
DROP TABLE IF EXISTS `group_leader_shop_assignment`;
CREATE TABLE `group_leader_shop_assignment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `group_id` bigint NOT NULL COMMENT '运营组ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `user_id` bigint NOT NULL COMMENT '被分配用户ID',
  `permission_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '权限类型(0只读1读写)',
  `assign_time` datetime NULL DEFAULT NULL COMMENT '分配时间',
  `assign_by` bigint NULL DEFAULT NULL COMMENT '分配人ID(组长)',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态(0正常1禁用)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_group_shop_user`(`group_id` ASC, `shop_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_user`(`user_id` ASC) USING BTREE,
  INDEX `idx_shop`(`shop_id` ASC) USING BTREE,
  CONSTRAINT `fk_shop_assignment_shop` FOREIGN KEY (`shop_id`) REFERENCES `shop` (`shop_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 79 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '组长店铺分配表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for group_member
-- ----------------------------
DROP TABLE IF EXISTS `group_member`;
CREATE TABLE `group_member`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `group_id` bigint NOT NULL COMMENT '运营组ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `join_time` datetime NULL DEFAULT NULL COMMENT '加入时间',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_group_user`(`group_id` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 43 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '运营组成员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for operation_group
-- ----------------------------
DROP TABLE IF EXISTS `operation_group`;
CREATE TABLE `operation_group`  (
  `group_id` bigint NOT NULL AUTO_INCREMENT COMMENT '运营组ID',
  `group_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '运营组名称',
  `leader_id` bigint NOT NULL COMMENT '负责人ID',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`group_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '运营组表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product
-- ----------------------------
DROP TABLE IF EXISTS `product`;
CREATE TABLE `product`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `product_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `product_skc_id` bigint NOT NULL COMMENT '商品SKC ID',
  `created_at` bigint NULL DEFAULT NULL COMMENT '创建时间戳',
  `is_support_personalization` tinyint(1) NULL DEFAULT 0 COMMENT '是否支持个性化',
  `ext_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '外部编码',
  `skc_site_status` int NULL DEFAULT NULL COMMENT 'SKC站点状态',
  `match_skc_jit_mode` tinyint(1) NULL DEFAULT 0 COMMENT '是否匹配JIT模式',
  `main_image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主图URL',
  `sync_time` datetime NOT NULL COMMENT '同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_product_skc_id`(`shop_id` ASC, `product_id` ASC, `product_skc_id` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_product_skc_id`(`product_skc_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 86075 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商品数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_new_arrival
-- ----------------------------
DROP TABLE IF EXISTS `product_new_arrival`;
CREATE TABLE `product_new_arrival`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `goods_id` bigint NOT NULL COMMENT '商品goods_id',
  `product_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `skc_id` bigint NULL DEFAULT NULL COMMENT '主SKC ID',
  `sync_time` datetime NOT NULL COMMENT '同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_product_goods`(`shop_id` ASC, `product_id` ASC, `goods_id` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1519 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商品上新基本信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_new_arrival_skc
-- ----------------------------
DROP TABLE IF EXISTS `product_new_arrival_skc`;
CREATE TABLE `product_new_arrival_skc`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `skc_id` bigint NOT NULL COMMENT 'SKC ID',
  `goods_skc_id` bigint NOT NULL COMMENT '商品goods_skc_id',
  `select_id` bigint NULL DEFAULT NULL COMMENT 'select_id',
  `color_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '颜色名称',
  `ext_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '外部编码',
  `price_verification_time` datetime NULL DEFAULT NULL COMMENT '核价时间',
  `added_to_site_time` datetime NULL DEFAULT NULL COMMENT '加入站点时间',
  `sample_posting_finished_time` datetime NULL DEFAULT NULL COMMENT '样品发布完成时间',
  `selected_time` datetime NULL DEFAULT NULL COMMENT '选品时间',
  `first_purchase_time` datetime NULL DEFAULT NULL COMMENT '首次采购时间',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `qc_completed_time` datetime NULL DEFAULT NULL COMMENT '质检完成时间',
  `sync_time` datetime NOT NULL COMMENT '同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_product_skc`(`shop_id` ASC, `product_id` ASC, `skc_id` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_skc_id`(`skc_id` ASC) USING BTREE,
  INDEX `idx_added_to_site_time`(`added_to_site_time` ASC) USING BTREE,
  INDEX `idx_first_purchase_time`(`first_purchase_time` ASC) USING BTREE,
  INDEX `idx_created_time`(`created_time` ASC) USING BTREE,
  CONSTRAINT `fk_new_arrival_skc_product` FOREIGN KEY (`shop_id`, `product_id`) REFERENCES `product_new_arrival` (`shop_id`, `product_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 5892 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商品上新SKC信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_sku
-- ----------------------------
DROP TABLE IF EXISTS `product_sku`;
CREATE TABLE `product_sku`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `product_skc_id` bigint NOT NULL COMMENT '商品SKC ID',
  `product_sku_id` bigint NOT NULL COMMENT '商品SKU ID',
  `color` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '颜色',
  `size` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '尺码',
  `status` int NULL DEFAULT 1 COMMENT 'SKU状态',
  `sync_time` datetime NOT NULL COMMENT '同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_product_skc_sku_id`(`shop_id` ASC, `product_id` ASC, `product_skc_id` ASC, `product_sku_id` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_product_skc_id`(`product_skc_id` ASC) USING BTREE,
  INDEX `idx_product_sku_id`(`product_sku_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 49242 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商品SKU数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_sync_task
-- ----------------------------
DROP TABLE IF EXISTS `product_sync_task`;
CREATE TABLE `product_sync_task`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `last_sync_time` datetime NULL DEFAULT NULL COMMENT '上次同步时间',
  `last_update_time` datetime NULL DEFAULT NULL COMMENT '数据最新更新时间',
  `sync_status` tinyint NOT NULL DEFAULT 0 COMMENT '同步状态：0-未同步，1-同步中，2-同步成功，3-同步失败',
  `error_message` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '错误信息',
  `total_records` int NULL DEFAULT 0 COMMENT '总记录数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `sku_total_records` int NULL DEFAULT 0 COMMENT 'SKU总记录数',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_sync_status`(`sync_status` ASC) USING BTREE,
  INDEX `idx_last_sync_time`(`last_sync_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商品数据同步任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for production_group
-- ----------------------------
DROP TABLE IF EXISTS `production_group`;
CREATE TABLE `production_group`  (
  `group_id` bigint NOT NULL AUTO_INCREMENT COMMENT '生产组ID',
  `group_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '生产组名称',
  `leader_id` bigint NOT NULL COMMENT '负责人ID',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`group_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '生产组表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for production_group_member
-- ----------------------------
DROP TABLE IF EXISTS `production_group_member`;
CREATE TABLE `production_group_member`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `group_id` bigint NOT NULL COMMENT '生产组ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `join_time` datetime NULL DEFAULT NULL COMMENT '加入时间',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_group_user`(`group_id` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '生产组成员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for production_group_role_assignment
-- ----------------------------
DROP TABLE IF EXISTS `production_group_role_assignment`;
CREATE TABLE `production_group_role_assignment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `group_id` bigint NOT NULL COMMENT '生产组ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `assign_time` datetime NULL DEFAULT NULL COMMENT '分配时间',
  `assign_by` bigint NULL DEFAULT NULL COMMENT '分配人ID(组长)',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态(0正常1禁用)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_group_user_role`(`group_id` ASC, `user_id` ASC, `role_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 76 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '生产组角色分配表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for production_group_shop_assignment
-- ----------------------------
DROP TABLE IF EXISTS `production_group_shop_assignment`;
CREATE TABLE `production_group_shop_assignment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `group_id` bigint NOT NULL COMMENT '生产组ID',
  `assign_time` datetime NULL DEFAULT NULL COMMENT '分配时间',
  `assign_by` bigint NULL DEFAULT NULL COMMENT '分配人ID',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_group_shop`(`group_id` ASC, `shop_id` ASC) USING BTREE,
  INDEX `idx_shop`(`shop_id` ASC) USING BTREE,
  INDEX `idx_group`(`group_id` ASC) USING BTREE,
  CONSTRAINT `fk_production_shop_assignment_group` FOREIGN KEY (`group_id`) REFERENCES `production_group` (`group_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_production_shop_assignment_shop` FOREIGN KEY (`shop_id`) REFERENCES `shop` (`shop_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '生产组店铺分配表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for production_progress
-- ----------------------------
DROP TABLE IF EXISTS `production_progress`;
CREATE TABLE `production_progress`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `sub_purchase_order_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '备货单号',
  `burning_status` tinyint(1) NULL DEFAULT 0 COMMENT '烧花状态(0未完成 1已完成)',
  `burning_time` datetime NULL DEFAULT NULL COMMENT '烧花完成时间',
  `burning_operator_id` bigint NULL DEFAULT NULL COMMENT '烧花操作人ID',
  `sewing_status` tinyint(1) NULL DEFAULT 0 COMMENT '车缝状态(0未完成 1已完成)',
  `sewing_time` datetime NULL DEFAULT NULL COMMENT '车缝完成时间',
  `sewing_operator_id` bigint NULL DEFAULT NULL COMMENT '车缝操作人ID',
  `tail_status` tinyint(1) NULL DEFAULT 0 COMMENT '尾部处理状态(0未完成 1已完成)',
  `tail_time` datetime NULL DEFAULT NULL COMMENT '尾部处理完成时间',
  `tail_operator_id` bigint NULL DEFAULT NULL COMMENT '尾部处理操作人ID',
  `shipping_status` tinyint(1) NULL DEFAULT 0 COMMENT '发货状态(0未完成 1已完成)',
  `shipping_time` datetime NULL DEFAULT NULL COMMENT '发货完成时间',
  `shipping_operator_id` bigint NULL DEFAULT NULL COMMENT '发货操作人ID',
  `delivery_status` tinyint(1) NULL DEFAULT 0 COMMENT '送货状态(0未完成 1已完成)',
  `delivery_time` datetime NULL DEFAULT NULL COMMENT '送货完成时间',
  `delivery_operator_id` bigint NULL DEFAULT NULL COMMENT '送货操作人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_order_sn`(`shop_id` ASC, `sub_purchase_order_sn` ASC) USING BTREE,
  INDEX `idx_sub_purchase_order_sn`(`sub_purchase_order_sn` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8746 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '生产进度表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for production_progress_log
-- ----------------------------
DROP TABLE IF EXISTS `production_progress_log`;
CREATE TABLE `production_progress_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `sub_purchase_order_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '备货单号',
  `progress_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '进度类型(burning:烧花,sewing:车缝,tail:尾部,shipping:发货,delivery:送货)',
  `operation_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作类型(1:完成 2:撤销)',
  `operator_id` bigint NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作人姓名',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_order_sn`(`shop_id` ASC, `sub_purchase_order_sn` ASC) USING BTREE,
  INDEX `idx_operator`(`operator_id` ASC) USING BTREE,
  INDEX `idx_operation_time`(`operation_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 145 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '生产进度日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_order_notification
-- ----------------------------
DROP TABLE IF EXISTS `purchase_order_notification`;
CREATE TABLE `purchase_order_notification`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `sub_purchase_order_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '备货单号',
  `notification_type` tinyint NOT NULL COMMENT '通知类型(1:JIT即将逾期 2:JIT已逾期 3:通备货未发货 4:普通备货未到货)',
  `notify_count` int NULL DEFAULT 0 COMMENT '通知次数',
  `last_notify_time` datetime NULL DEFAULT NULL COMMENT '最后通知时间',
  `notify_status` tinyint NULL DEFAULT 0 COMMENT '通知状态(0:待通知 1:通知中 2:已通知完成)',
  `group_id` bigint NULL DEFAULT NULL COMMENT '通知的运营组ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_order_type`(`shop_id` ASC, `sub_purchase_order_sn` ASC, `notification_type` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_sub_purchase_order_sn`(`sub_purchase_order_sn` ASC) USING BTREE,
  INDEX `idx_notification_type`(`notification_type` ASC) USING BTREE,
  INDEX `idx_notify_status`(`notify_status` ASC) USING BTREE,
  INDEX `idx_last_notify_time`(`last_notify_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 311 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '备货单通知记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for purchase_order_notification_config
-- ----------------------------
DROP TABLE IF EXISTS `purchase_order_notification_config`;
CREATE TABLE `purchase_order_notification_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `notification_type` int NOT NULL COMMENT '通知类型(1:JIT即将逾期 2:JIT已逾期 3:普通备货未发货 4:普通备货未到货)',
  `notification_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知名称',
  `enabled` int NOT NULL DEFAULT 1 COMMENT '是否启用(0:禁用 1:启用)',
  `check_interval` int NULL DEFAULT 24 COMMENT '检查间隔(小时)',
  `normal_trigger_days` int NULL DEFAULT NULL COMMENT '普通备货触发天数',
  `jit_trigger_hours` int NULL DEFAULT NULL COMMENT 'JIT备货触发小时数',
  `max_notify_count` int NOT NULL DEFAULT 3 COMMENT '最大通知次数',
  `notify_interval_hours` int NULL DEFAULT 24 COMMENT '通知间隔(小时)',
  `template_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息模板代码',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_notification_type`(`notification_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '备货单通知配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for purchase_order_sync_task
-- ----------------------------
DROP TABLE IF EXISTS `purchase_order_sync_task`;
CREATE TABLE `purchase_order_sync_task`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `last_sync_time` datetime NULL DEFAULT NULL COMMENT '上次同步时间',
  `last_update_time` datetime NULL DEFAULT NULL COMMENT '数据最新更新时间',
  `sync_status` tinyint NOT NULL DEFAULT 0 COMMENT '同步状态：0-未同步，1-同步中，2-同步成功，3-同步失败',
  `error_message` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '错误信息',
  `normal_order_count` int NULL DEFAULT 0 COMMENT '普通备货单总记录数',
  `jit_order_count` int NULL DEFAULT 0 COMMENT 'JIT备货单总记录数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_sync_status`(`sync_status` ASC) USING BTREE,
  INDEX `idx_last_sync_time`(`last_sync_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '备货单数据同步任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for purchase_order_v
-- ----------------------------
DROP TABLE IF EXISTS `purchase_order_v`;
CREATE TABLE `purchase_order_v`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `sub_purchase_order_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '备货单号',
  `original_purchase_order_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原始采购单号',
  `product_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `product_id` bigint NULL DEFAULT NULL COMMENT '商品ID',
  `product_skc_id` bigint NULL DEFAULT NULL COMMENT '商品SKC ID',
  `supplier_id` bigint NOT NULL COMMENT '供应商ID',
  `supplier_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商名称',
  `purchase_time` datetime NULL DEFAULT NULL COMMENT '备货单创建时间',
  `deliver_time` datetime NULL DEFAULT NULL COMMENT '发货时间',
  `delivery_order_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发货单号',
  `expect_latest_deliver_time` datetime NULL DEFAULT NULL COMMENT '预计最晚发货时间',
  `expect_latest_arrival_time` datetime NULL DEFAULT NULL COMMENT '预计最晚到货时间',
  `receive_warehouse_id` bigint NULL DEFAULT NULL COMMENT '收货仓库ID',
  `receive_warehouse_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收货仓库名称',
  `status` tinyint NULL DEFAULT NULL COMMENT '备货单状态(0-待接单；1-已接单，待发货；2-已送货；3-已收货；4-已拒收；5-已验收，全部退回；6-已验收；7-已入库；8-作废；9-已超时)',
  `purchase_stock_type` tinyint NULL DEFAULT 0 COMMENT '备货类型(0:普通备货 1:JIT备货)',
  `receive_time` datetime NULL DEFAULT NULL COMMENT '实际收货时间',
  `purchase_quantity` int NULL DEFAULT 0 COMMENT '采购数量',
  `deliver_quantity` int NULL DEFAULT 0 COMMENT '实际发货数量',
  `receive_quantity` int NULL DEFAULT 0 COMMENT '实际收货数量',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除(0:否 1:是)',
  `sync_time` datetime NOT NULL COMMENT '同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_order_sn`(`shop_id` ASC, `sub_purchase_order_sn` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_sub_purchase_order_sn`(`sub_purchase_order_sn` ASC) USING BTREE,
  INDEX `idx_supplier_id`(`supplier_id` ASC) USING BTREE,
  INDEX `idx_purchase_time`(`purchase_time` ASC) USING BTREE,
  INDEX `idx_deliver_time`(`deliver_time` ASC) USING BTREE,
  INDEX `idx_expect_latest_arrival_time`(`expect_latest_arrival_time` ASC) USING BTREE,
  INDEX `idx_status_type`(`status` ASC, `purchase_stock_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41476 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '备货单信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for quality_inspection
-- ----------------------------
DROP TABLE IF EXISTS `quality_inspection`;
CREATE TABLE `quality_inspection`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `qc_bill_id` bigint NOT NULL COMMENT '质检单ID',
  `product_sku_id` bigint NOT NULL COMMENT '商品SKU ID',
  `product_skc_id` bigint NULL DEFAULT NULL COMMENT '商品SKC ID',
  `spu_id` bigint NULL DEFAULT NULL COMMENT 'SPU ID',
  `sku_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'SKU名称',
  `cat_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类目名称',
  `purchase_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备货单号',
  `spec` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格',
  `thumb_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品缩略图',
  `qc_result` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '质检结果 1:合格,2:不合格',
  `qc_result_update_time` datetime NOT NULL COMMENT '质检结果更新时间',
  `sync_time` datetime NOT NULL COMMENT '同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_sku_qcbill`(`shop_id` ASC, `product_sku_id` ASC, `qc_bill_id` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_product_sku_id`(`product_sku_id` ASC) USING BTREE,
  INDEX `idx_qc_result_update_time`(`qc_result_update_time` ASC) USING BTREE,
  INDEX `idx_purchase_no`(`purchase_no` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 84273 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '质检结果数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for quality_inspection_defect_detail
-- ----------------------------
DROP TABLE IF EXISTS `quality_inspection_defect_detail`;
CREATE TABLE `quality_inspection_defect_detail`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `qc_bill_id` bigint NOT NULL COMMENT '质检单ID',
  `product_skc_id` bigint NOT NULL COMMENT '产品SKC ID',
  `product_sku_id` bigint NOT NULL COMMENT '产品SKU ID',
  `flaw_name_desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '疵点描述',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '问题备注',
  `attachments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '疵点证明图片链接，JSON格式',
  `sync_time` datetime NOT NULL COMMENT '同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_qcbill_sku`(`shop_id` ASC, `qc_bill_id` ASC, `product_sku_id` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_qc_bill_id`(`qc_bill_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4495 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '质检不合格详情数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for quality_inspection_sync_task
-- ----------------------------
DROP TABLE IF EXISTS `quality_inspection_sync_task`;
CREATE TABLE `quality_inspection_sync_task`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `last_sync_time` datetime NULL DEFAULT NULL COMMENT '上次同步时间',
  `last_update_time` datetime NULL DEFAULT NULL COMMENT '数据最新更新时间',
  `sync_status` tinyint NOT NULL DEFAULT 0 COMMENT '同步状态：0-未同步，1-同步中，2-同步成功，3-同步失败',
  `error_message` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '错误信息',
  `total_records` int NULL DEFAULT 0 COMMENT '总记录数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_sync_status`(`sync_status` ASC) USING BTREE,
  INDEX `idx_last_sync_time`(`last_sync_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 44 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '质检数据同步任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for refund_package_detail
-- ----------------------------
DROP TABLE IF EXISTS `refund_package_detail`;
CREATE TABLE `refund_package_detail`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `package_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '包裹单号',
  `product_sku_id` bigint NOT NULL COMMENT '商品SKU ID',
  `product_skc_id` bigint NULL DEFAULT NULL COMMENT '商品SKC ID',
  `product_spu_id` bigint NULL DEFAULT NULL COMMENT '商品SPU ID',
  `purchase_sub_order_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备货单号',
  `quantity` int NULL DEFAULT 1 COMMENT '数量',
  `main_sale_spec` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主规格',
  `secondary_sale_spec` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '次规格',
  `thumb_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '缩略图',
  `order_type_desc` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单类型描述',
  `reason_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '退货原因描述，JSON格式',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注信息',
  `outbound_time` bigint NULL DEFAULT NULL COMMENT '出库时间戳',
  `sync_time` datetime NOT NULL COMMENT '同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_package_sku_skc_spu`(`shop_id` ASC, `package_sn` ASC, `product_sku_id` ASC, `product_skc_id` ASC, `product_spu_id` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_package_sn`(`package_sn` ASC) USING BTREE,
  INDEX `idx_product_sku_id`(`product_sku_id` ASC) USING BTREE,
  INDEX `idx_purchase_sub_order_sn`(`purchase_sub_order_sn` ASC) USING BTREE,
  INDEX `idx_outbound_time`(`outbound_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 49399 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '退货包裹明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for refund_package_sync_task
-- ----------------------------
DROP TABLE IF EXISTS `refund_package_sync_task`;
CREATE TABLE `refund_package_sync_task`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `last_sync_time` datetime NULL DEFAULT NULL COMMENT '上次同步时间',
  `last_update_time` datetime NULL DEFAULT NULL COMMENT '数据最新更新时间',
  `sync_status` tinyint NOT NULL DEFAULT 0 COMMENT '同步状态：0-未同步，1-同步中，2-同步成功，3-同步失败',
  `error_message` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '错误信息',
  `total_records` int NULL DEFAULT 0 COMMENT '总记录数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_sync_status`(`sync_status` ASC) USING BTREE,
  INDEX `idx_last_sync_time`(`last_sync_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '退货包裹数据同步任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sales_custom_info
-- ----------------------------
DROP TABLE IF EXISTS `sales_custom_info`;
CREATE TABLE `sales_custom_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `product_skc_id` bigint NOT NULL COMMENT '商品SKC ID',
  `is_custom_goods` tinyint(1) NULL DEFAULT 0 COMMENT '是否为定制品',
  `limit_num` int NULL DEFAULT NULL COMMENT '定制字数限制',
  `effect_picture` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '定制效果图',
  `sync_time` datetime NOT NULL COMMENT '同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_product_skc`(`shop_id` ASC, `product_skc_id` ASC) USING BTREE,
  CONSTRAINT `fk_custom_info_sales_sub_order` FOREIGN KEY (`shop_id`, `product_skc_id`) REFERENCES `sales_sub_order` (`shop_id`, `product_skc_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '销售定制信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sales_inventory_info
-- ----------------------------
DROP TABLE IF EXISTS `sales_inventory_info`;
CREATE TABLE `sales_inventory_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `product_sku_id` bigint NOT NULL COMMENT '商品SKU ID',
  `warehouse_group_id` bigint NULL DEFAULT NULL COMMENT '仓库组ID',
  `warehouse_inventory_num` int NULL DEFAULT 0 COMMENT '仓内库存',
  `wait_on_shelf_num` int NULL DEFAULT 0 COMMENT '待上架库存',
  `wait_delivery_inventory_num` int NULL DEFAULT 0 COMMENT '待发货库存',
  `expected_occupied_inventory_num` int NULL DEFAULT 0 COMMENT '预计占用库存',
  `wait_approve_inventory_num` int NULL DEFAULT 0 COMMENT '待审核备货库存',
  `wait_qc_num` int NULL DEFAULT 0 COMMENT '已上架待质检库存',
  `unavailable_warehouse_inventory_num` int NULL DEFAULT 0 COMMENT '仓内暂不可用库存',
  `wait_in_stock` int NULL DEFAULT 0 COMMENT '待入库库存',
  `wait_receive_num` int NULL DEFAULT 0 COMMENT '待收货库存',
  `sync_time` datetime NOT NULL COMMENT '同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_sku_warehouse`(`shop_id` ASC, `product_sku_id` ASC, `warehouse_group_id` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_product_sku_id`(`product_sku_id` ASC) USING BTREE,
  INDEX `idx_warehouse_group_id`(`warehouse_group_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 265995 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '销售库存信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sales_sku_quantity
-- ----------------------------
DROP TABLE IF EXISTS `sales_sku_quantity`;
CREATE TABLE `sales_sku_quantity`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `product_skc_id` bigint NOT NULL COMMENT '商品SKC ID',
  `product_sku_id` bigint NOT NULL COMMENT '商品SKU ID',
  `warehouse_group_id` bigint NULL DEFAULT NULL COMMENT '备货仓组ID',
  `warehouse_group_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备货仓组名称',
  `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '尺码名称',
  `sku_ext_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'sku货号',
  `can_purchase` tinyint(1) NULL DEFAULT 1 COMMENT '仓组下是否可以备货',
  `stock_days` int NULL DEFAULT NULL COMMENT '备货天数',
  `safe_inventory_days` int NULL DEFAULT NULL COMMENT '安全库存天数',
  `purchase_config` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '下单逻辑：安全库存天数+备货天数',
  `price_review_status` int NULL DEFAULT NULL COMMENT '核价状态',
  `is_verify_price` tinyint(1) NULL DEFAULT 0 COMMENT '是否核价通过，默认=false，降级时=null',
  `is_reduce_price_pass` tinyint(1) NULL DEFAULT 0 COMMENT '是否降低供货价通过',
  `today_sale_volume` int NULL DEFAULT 0 COMMENT '今日销量',
  `total_sale_volume` int NULL DEFAULT 0 COMMENT '总销量',
  `last_seven_days_sale_volume` int NULL DEFAULT 0 COMMENT '近7天销量',
  `last_thirty_days_sale_volume` int NULL DEFAULT 0 COMMENT '近30天销量',
  `in_cart_number` int NULL DEFAULT 0 COMMENT '用户加购数量',
  `in_cart_number_7d` int NULL DEFAULT 0 COMMENT '近7天用户加购数量',
  `lack_quantity` int NULL DEFAULT 0 COMMENT '缺货数量',
  `advice_quantity` int NULL DEFAULT 0 COMMENT '建议下单量',
  `supplier_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '申报价格',
  `available_sale_days` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '可售天数',
  `available_sale_days_from_inventory` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '库存可售天数',
  `warehouse_available_sale_days` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '仓内库存可售天数:保留一位小数',
  `seven_days_sale_reference` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '7日销量参考',
  `seven_days_reference_sale_type` int NULL DEFAULT NULL COMMENT '七日销量参考类型 1.7日最大销量 2.7日日均销量',
  `sync_time` datetime NOT NULL COMMENT '同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_product_skc_sku`(`shop_id` ASC, `product_id` ASC, `product_skc_id` ASC, `product_sku_id` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_product_sku_id`(`product_sku_id` ASC) USING BTREE,
  INDEX `idx_warehouse_group_id`(`warehouse_group_id` ASC) USING BTREE,
  CONSTRAINT `fk_sku_quantity_sales_sub_order` FOREIGN KEY (`shop_id`, `product_id`, `product_skc_id`) REFERENCES `sales_sub_order` (`shop_id`, `product_id`, `product_skc_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 265995 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '销售SKU数量详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sales_sub_order
-- ----------------------------
DROP TABLE IF EXISTS `sales_sub_order`;
CREATE TABLE `sales_sub_order`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `product_skc_id` bigint NOT NULL COMMENT '商品SKC ID',
  `product_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `skc_ext_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'SKC货号',
  `category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类目',
  `product_skc_picture` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '货品图片',
  `is_custom_goods` tinyint(1) NULL DEFAULT 0 COMMENT '是否为定制品',
  `inventory_region` int NULL DEFAULT NULL COMMENT '备货区域，1-国内备货，2-海外备货，3-保税仓备货',
  `supply_status` int NULL DEFAULT 0 COMMENT '供应状态',
  `supply_status_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应状态备注',
  `in_black_list` tinyint(1) NULL DEFAULT 0 COMMENT '是否在备货黑名单内，在：禁止备货',
  `picture_audit_status` int NULL DEFAULT NULL COMMENT '图片审核状态 1-未完成；2-已完成',
  `on_sales_duration_offline` int NULL DEFAULT 0 COMMENT '加入站点时长',
  `close_jit_status` int NULL DEFAULT 0 COMMENT 'JIT 转备货状态 0-未申请 1-调价中 2-待备货 3-备货完成，待关闭JIT 4-JIT已关闭 5-调价失败，流程结束 6-备货失败，流程结束 7-降价后又涨价，流程结束',
  `auto_close_jit` tinyint(1) NULL DEFAULT 0 COMMENT '是否会自动关闭jit',
  `hot_tag` tinyint(1) NULL DEFAULT 0 COMMENT '是否热销款',
  `has_hot_sku` tinyint(1) NULL DEFAULT 0 COMMENT '是否存在爆旺款sku',
  `is_enough_stock` tinyint(1) NULL DEFAULT 0 COMMENT '是否备货充足无需下单',
  `is_first` tinyint(1) NULL DEFAULT 0 COMMENT '是否首单 0-否 1-是',
  `illegal_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '违规原因',
  `expect_normal_supply_time` bigint NULL DEFAULT NULL COMMENT '预计正常供货时间',
  `purchase_stock_type` int NULL DEFAULT 0 COMMENT '是否是JIT备货， 0-普通，1-JIT备货',
  `settlement_type` int NULL DEFAULT 1 COMMENT '结算类型 0-非vmi 1-vmi',
  `sync_time` datetime NOT NULL COMMENT '同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_product_skc`(`shop_id` ASC, `product_id` ASC, `product_skc_id` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_product_skc_id`(`product_skc_id` ASC) USING BTREE,
  INDEX `idx_skc_ext_code`(`skc_ext_code` ASC) USING BTREE,
  INDEX `idx_shop_skc`(`shop_id` ASC, `product_skc_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 48917 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '销售子订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sales_sync_task
-- ----------------------------
DROP TABLE IF EXISTS `sales_sync_task`;
CREATE TABLE `sales_sync_task`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `last_sync_time` datetime NULL DEFAULT NULL COMMENT '上次同步时间',
  `last_update_time` datetime NULL DEFAULT NULL COMMENT '数据最新更新时间',
  `sync_status` tinyint NOT NULL DEFAULT 0 COMMENT '同步状态：0-未同步，1-同步中，2-同步成功，3-同步失败',
  `error_message` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '错误信息',
  `total_records` int NULL DEFAULT 0 COMMENT '总记录数',
  `sku_total_records` int NULL DEFAULT 0 COMMENT 'SKU总记录数',
  `warehouse_total_records` int NULL DEFAULT 0 COMMENT '仓库总记录数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_sync_status`(`sync_status` ASC) USING BTREE,
  INDEX `idx_last_sync_time`(`last_sync_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '销售数据同步任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sales_warehouse_info
-- ----------------------------
DROP TABLE IF EXISTS `sales_warehouse_info`;
CREATE TABLE `sales_warehouse_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `product_sku_id` bigint NOT NULL COMMENT '商品SKU ID',
  `warehouse_group_id` bigint NULL DEFAULT NULL COMMENT '仓库组ID',
  `warehouse_group_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备货仓组名称',
  `stock_days` int NULL DEFAULT NULL COMMENT '备货天数',
  `safe_inventory_days` int NULL DEFAULT NULL COMMENT '安全库存天数',
  `purchase_config` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '下单逻辑：安全库存天数+备货天数',
  `today_sale_volume` int NULL DEFAULT 0 COMMENT '今日销量',
  `total_sale_volume` int NULL DEFAULT 0 COMMENT '总销量',
  `last_seven_days_sale_volume` int NULL DEFAULT 0 COMMENT '近7天销量',
  `last_thirty_days_sale_volume` int NULL DEFAULT 0 COMMENT '近30天销量',
  `advice_quantity` int NULL DEFAULT 0 COMMENT '建议下单量',
  `lack_quantity` int NULL DEFAULT 0 COMMENT '缺货数量',
  `available_sale_days` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '可售天数',
  `available_sale_days_from_inventory` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '库存可售天数',
  `warehouse_available_sale_days` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '仓内库存可售天数:保留一位小数',
  `seven_days_sale_reference` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '7日销量参考',
  `seven_days_reference_sale_type` int NULL DEFAULT NULL COMMENT '七日销量参考类型 1.7日最大销量 2.7日日均销量',
  `sync_time` datetime NOT NULL COMMENT '同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_sku_warehouse`(`shop_id` ASC, `product_sku_id` ASC, `warehouse_group_id` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_product_sku_id`(`product_sku_id` ASC) USING BTREE,
  INDEX `idx_warehouse_group_id`(`warehouse_group_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 265995 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '销售仓库信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sales_warehouse_inventory
-- ----------------------------
DROP TABLE IF EXISTS `sales_warehouse_inventory`;
CREATE TABLE `sales_warehouse_inventory`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `product_sku_id` bigint NOT NULL COMMENT '商品SKU ID',
  `warehouse_group_id` bigint NOT NULL COMMENT '仓库组ID',
  `wait_on_shelf_num` int NULL DEFAULT 0 COMMENT '待上架库存',
  `warehouse_inventory_num` int NULL DEFAULT 0 COMMENT '仓内库存',
  `expected_occupied_inventory_num` int NULL DEFAULT 0 COMMENT '预计占用库存',
  `wait_approve_inventory_num` int NULL DEFAULT 0 COMMENT '待审核备货库存',
  `wait_qc_num` int NULL DEFAULT 0 COMMENT '已上架待质检库存',
  `unavailable_warehouse_inventory_num` int NULL DEFAULT 0 COMMENT '仓内暂不可用库存',
  `wait_in_stock` int NULL DEFAULT 0 COMMENT '待入库库存',
  `wait_receive_num` int NULL DEFAULT 0 COMMENT '待收货库存',
  `wait_delivery_inventory_num` int NULL DEFAULT 0 COMMENT '待发货库存',
  `sync_time` datetime NOT NULL COMMENT '同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_sku_warehouse`(`shop_id` ASC, `product_sku_id` ASC, `warehouse_group_id` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_product_sku_id`(`product_sku_id` ASC) USING BTREE,
  INDEX `idx_warehouse_group_id`(`warehouse_group_id` ASC) USING BTREE,
  CONSTRAINT `fk_warehouse_inventory_warehouse_info` FOREIGN KEY (`shop_id`, `product_sku_id`, `warehouse_group_id`) REFERENCES `sales_warehouse_info` (`shop_id`, `product_sku_id`, `warehouse_group_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 265995 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '销售仓库库存详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for shop
-- ----------------------------
DROP TABLE IF EXISTS `shop`;
CREATE TABLE `shop`  (
  `shop_id` bigint NOT NULL AUTO_INCREMENT COMMENT '店铺ID',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '店铺名称',
  `shop_temu_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'temu平台店铺id',
  `api_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'API密钥',
  `api_secret` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'API密钥Secret',
  `access_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'access_token',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者ID',
  `belong_group_id` bigint NULL DEFAULT NULL COMMENT '所属运营组ID',
  PRIMARY KEY (`shop_id`) USING BTREE,
  UNIQUE INDEX `idx_shop_code`(`shop_temu_id` ASC) USING BTREE,
  INDEX `idx_shop_create_by`(`create_by` ASC) USING BTREE,
  INDEX `idx_shop_belong_group`(`belong_group_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '店铺表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for shop_assignment
-- ----------------------------
DROP TABLE IF EXISTS `shop_assignment`;
CREATE TABLE `shop_assignment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `permission_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '权限类型（0只读 1读写）',
  `assign_time` datetime NULL DEFAULT NULL COMMENT '分配时间',
  `assign_by` bigint NULL DEFAULT NULL COMMENT '分配人ID',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_shop_user`(`shop_id` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '店铺分配表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for shop_group_assignment
-- ----------------------------
DROP TABLE IF EXISTS `shop_group_assignment`;
CREATE TABLE `shop_group_assignment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `group_id` bigint NOT NULL COMMENT '运营组ID',
  `assign_time` datetime NULL DEFAULT NULL COMMENT '分配时间',
  `assign_by` bigint NULL DEFAULT NULL COMMENT '分配人ID',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_shop_group`(`shop_id` ASC, `group_id` ASC) USING BTREE,
  INDEX `idx_group`(`group_id` ASC) USING BTREE,
  INDEX `idx_shop`(`shop_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 89 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '店铺运营组关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for shop_violation_detail
-- ----------------------------
DROP TABLE IF EXISTS `shop_violation_detail`;
CREATE TABLE `shop_violation_detail`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `punish_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '违规编号',
  `sub_purchase_order_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '备货单号',
  `product_sku_id` bigint NOT NULL COMMENT '商品SKU ID',
  `stock_quantity` int NULL DEFAULT 0 COMMENT '库存数量',
  `lack_quantity` int NULL DEFAULT 0 COMMENT '缺货数量',
  `unqualified_quantity` int NULL DEFAULT 0 COMMENT '不合格数量',
  `sync_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_punish_sn`(`shop_id` ASC, `punish_sn` ASC) USING BTREE,
  INDEX `idx_sub_purchase_order_sn`(`sub_purchase_order_sn` ASC) USING BTREE,
  INDEX `idx_product_sku_id`(`product_sku_id` ASC) USING BTREE,
  CONSTRAINT `fk_violation_detail_info` FOREIGN KEY (`shop_id`, `punish_sn`) REFERENCES `shop_violation_info` (`shop_id`, `punish_sn`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_violation_detail_shop` FOREIGN KEY (`shop_id`) REFERENCES `shop` (`shop_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 3259 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '店铺违规详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for shop_violation_info
-- ----------------------------
DROP TABLE IF EXISTS `shop_violation_info`;
CREATE TABLE `shop_violation_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `mall_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺Temu平台ID',
  `punish_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '违规编号',
  `sub_purchase_order_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '备货单号',
  `punish_type_code` int NULL DEFAULT NULL COMMENT '违规类型代码',
  `punish_type_desc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '违规类型描述',
  `punish_first_type_code` int NULL DEFAULT NULL COMMENT '违规一级类型代码',
  `punish_first_type_desc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '违规一级类型描述',
  `punish_second_type_desc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '违规二级类型描述',
  `violation_start_time` bigint NULL DEFAULT NULL COMMENT '违规发起时间戳(毫秒)',
  `show_voucher` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '显示凭证',
  `violation_time` datetime NULL DEFAULT NULL COMMENT '违规发起时间',
  `punish_amount` decimal(10, 0) NULL DEFAULT NULL COMMENT '违规金额',
  `punish_amount_currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'CNY' COMMENT '违规金额币种',
  `punish_status` int NULL DEFAULT NULL COMMENT '违规状态码：0-公示中(待申诉)，1-已取消违规处理,3-已按违规处理,4-逾期未申诉，平台处理中,5-已申诉(平台处理中)，其他值待确认,6-申诉驳回，平台处理中',
  `punish_status_desc` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '违规状态描述',
  `view_details_status` int NULL DEFAULT NULL COMMENT '详情查看状态码：1-需要查看详情(显示\"去申诉\"按钮)，3-无需查看详情(显示\"查看详情\"按钮)',
  `countdown_time` bigint NULL DEFAULT NULL COMMENT '倒计时时间戳(毫秒)',
  `sync_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_punish_sn`(`shop_id` ASC, `punish_sn` ASC) USING BTREE,
  UNIQUE INDEX `uk_mall_punish_sn`(`mall_id` ASC, `punish_sn` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_mall_id`(`mall_id` ASC) USING BTREE,
  INDEX `idx_punish_sn`(`punish_sn` ASC) USING BTREE,
  INDEX `idx_sub_purchase_order_sn`(`sub_purchase_order_sn` ASC) USING BTREE,
  INDEX `idx_violation_time`(`violation_time` ASC) USING BTREE,
  INDEX `idx_punish_status`(`punish_status` ASC) USING BTREE,
  CONSTRAINT `fk_violation_shop` FOREIGN KEY (`shop_id`) REFERENCES `shop` (`shop_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 267 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '店铺违规信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '配置名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '配置键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '配置键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE,
  UNIQUE INDEX `idx_config_key`(`config_key` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '参数配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_data_permission
-- ----------------------------
DROP TABLE IF EXISTS `sys_data_permission`;
CREATE TABLE `sys_data_permission`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `permission_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '权限类型(0:本人数据 1:本组数据 2:全部数据)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_role_id`(`role_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色数据权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件路径',
  `is_frame` int NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7136 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '菜单权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_message
-- ----------------------------
DROP TABLE IF EXISTS `sys_message`;
CREATE TABLE `sys_message`  (
  `message_id` bigint NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '消息内容',
  `message_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息类型（1系统消息 2任务提醒 3店铺消息）',
  `to_user_id` bigint NULL DEFAULT NULL COMMENT '接收用户ID',
  `from_user_id` bigint NULL DEFAULT NULL COMMENT '发送用户ID',
  `target_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '接收对象类型(0全部用户 1指定用户 2指定角色 3运营组)',
  `target_ids` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '接收对象ID列表,逗号分隔',
  `shop_id` bigint NULL DEFAULT NULL COMMENT '相关店铺ID',
  `read_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '已读状态（0未读 1已读）',
  `importance` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '重要程度(1普通 2重要 3紧急)',
  `important` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '重要程度（0普通 1重要）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `publish_time` datetime NULL DEFAULT NULL COMMENT '发布时间',
  `expire_time` datetime NULL DEFAULT NULL COMMENT '过期时间',
  `read_time` datetime NULL DEFAULT NULL COMMENT '阅读时间',
  `deleted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '是否删除(0未删除 1已删除)',
  `ext_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '扩展数据，存储额外JSON信息',
  PRIMARY KEY (`message_id`) USING BTREE,
  INDEX `idx_to_user`(`to_user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 434 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '消息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_message_template
-- ----------------------------
DROP TABLE IF EXISTS `sys_message_template`;
CREATE TABLE `sys_message_template`  (
  `template_id` bigint NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模板编码',
  `template_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模板名称',
  `title_template` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题模板',
  `content_template` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '内容模板',
  `template_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '模板类型(1系统消息 2任务提醒 3店铺消息)',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '状态(0正常 1禁用)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`template_id`) USING BTREE,
  UNIQUE INDEX `idx_template_code`(`template_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '消息模板表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_message_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_message_user`;
CREATE TABLE `sys_message_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `message_id` bigint NOT NULL COMMENT '消息ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `read_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '已读状态(0未读 1已读)',
  `read_time` datetime NULL DEFAULT NULL COMMENT '阅读时间',
  `deleted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '是否删除(0未删除 1已删除)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_message_user`(`message_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_user_read`(`user_id` ASC, `read_status` ASC) USING BTREE,
  INDEX `idx_message_id`(`message_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1113 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '消息接收表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_operation_log`;
CREATE TABLE `sys_operation_log`  (
  `oper_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '模块标题',
  `business_type` int NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作人员',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '返回参数',
  `status` int NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  PRIMARY KEY (`oper_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '操作日志记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码',
  `nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE,
  UNIQUE INDEX `idx_username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 42 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户和角色关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_sales_statistics
-- ----------------------------
DROP TABLE IF EXISTS `t_sales_statistics`;
CREATE TABLE `t_sales_statistics`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺名称',
  `statistics_date` date NOT NULL COMMENT '统计日期',
  `today_sales` int NULL DEFAULT 0 COMMENT '今日销量',
  `last_week_sales` int NULL DEFAULT 0 COMMENT '近7天销量',
  `last_month_sales` int NULL DEFAULT 0 COMMENT '近30天销量',
  `total_products` int NULL DEFAULT 0 COMMENT '商品总数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_date`(`shop_id` ASC, `statistics_date` ASC) USING BTREE COMMENT '店铺和日期唯一索引',
  INDEX `idx_statistics_date`(`statistics_date` ASC) USING BTREE COMMENT '日期索引'
) ENGINE = InnoDB AUTO_INCREMENT = 66 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '销售统计数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for warehouse_info
-- ----------------------------
DROP TABLE IF EXISTS `warehouse_info`;
CREATE TABLE `warehouse_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sub_wid` bigint NOT NULL COMMENT '子仓库ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '仓库名称',
  `receive_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收货地址',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_sub_wid`(`sub_wid` ASC) USING BTREE,
  INDEX `idx_sub_wid`(`sub_wid` ASC) USING BTREE COMMENT '子仓库ID索引'
) ENGINE = InnoDB AUTO_INCREMENT = 357 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '仓库信息表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
