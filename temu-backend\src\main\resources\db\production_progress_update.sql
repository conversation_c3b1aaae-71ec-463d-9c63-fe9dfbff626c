/*
 生产进度表结构修改脚本
 
 功能：将生产进度从5个步骤修改为6个步骤
 原流程：烧花 -> 车缝 -> 尾部处理 -> 发货 -> 送货
 新流程：烧花/剪图 -> 车间/拣货 -> 剪线/压图 -> 查货 -> 包装 -> 发货
 
 日期: 2025-06-06
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 备份原始数据
-- ----------------------------
-- 备份production_progress表
CREATE TABLE IF NOT EXISTS `temp_production_progress` AS SELECT * FROM `production_progress`;

-- 备份production_progress_log表
CREATE TABLE IF NOT EXISTS `temp_production_progress_log` AS SELECT * FROM `production_progress_log`;

-- 备份角色表
CREATE TABLE IF NOT EXISTS `temp_sys_role` AS SELECT * FROM `sys_role`;

-- ----------------------------
-- 1. 修改 production_progress 表结构
-- ----------------------------
ALTER TABLE `production_progress` 
-- 修改字段名和注释
CHANGE COLUMN `burning_status` `cutting_status` tinyint(1) NULL DEFAULT 0 COMMENT '烧花/剪图状态(0未完成 1已完成)',
CHANGE COLUMN `burning_time` `cutting_time` datetime NULL DEFAULT NULL COMMENT '烧花/剪图完成时间',
CHANGE COLUMN `burning_operator_id` `cutting_operator_id` bigint NULL DEFAULT NULL COMMENT '烧花/剪图操作人ID',

CHANGE COLUMN `sewing_status` `workshop_status` tinyint(1) NULL DEFAULT 0 COMMENT '车间/拣货状态(0未完成 1已完成)',
CHANGE COLUMN `sewing_time` `workshop_time` datetime NULL DEFAULT NULL COMMENT '车间/拣货完成时间',
CHANGE COLUMN `sewing_operator_id` `workshop_operator_id` bigint NULL DEFAULT NULL COMMENT '车间/拣货操作人ID',

CHANGE COLUMN `tail_status` `trimming_status` tinyint(1) NULL DEFAULT 0 COMMENT '剪线/压图状态(0未完成 1已完成)',
CHANGE COLUMN `tail_time` `trimming_time` datetime NULL DEFAULT NULL COMMENT '剪线/压图完成时间',
CHANGE COLUMN `tail_operator_id` `trimming_operator_id` bigint NULL DEFAULT NULL COMMENT '剪线/压图操作人ID',

CHANGE COLUMN `delivery_status` `packaging_status` tinyint(1) NULL DEFAULT 0 COMMENT '包装状态(0未完成 1已完成)',
CHANGE COLUMN `delivery_time` `packaging_time` datetime NULL DEFAULT NULL COMMENT '包装完成时间',
CHANGE COLUMN `delivery_operator_id` `packaging_operator_id` bigint NULL DEFAULT NULL COMMENT '包装操作人ID',

-- 添加新字段 - 查货
ADD COLUMN `inspection_status` tinyint(1) NULL DEFAULT 0 COMMENT '查货状态(0未完成 1已完成)' AFTER `trimming_operator_id`,
ADD COLUMN `inspection_time` datetime NULL DEFAULT NULL COMMENT '查货完成时间' AFTER `inspection_status`,
ADD COLUMN `inspection_operator_id` bigint NULL DEFAULT NULL COMMENT '查货操作人ID' AFTER `inspection_time`;

-- 注意：shipping相关字段（发货）保持不变

-- ----------------------------
-- 2. 更新 sys_role 表数据
-- ----------------------------
-- 更新现有角色名称和描述
UPDATE `sys_role` SET `role_name` = '烧花/剪图', `remark` = '负责烧花/剪图工序的操作员' WHERE `role_key` = 'burning';
UPDATE `sys_role` SET `role_name` = '车间/拣货', `remark` = '负责车间/拣货工序的操作员' WHERE `role_key` = 'sewing';
UPDATE `sys_role` SET `role_name` = '剪线/压图', `remark` = '负责剪线/压图工序的操作员' WHERE `role_key` = 'tail';
-- 发货角色保持不变
UPDATE `sys_role` SET `role_name` = '包装', `remark` = '负责包装工序的操作员' WHERE `role_key` = 'delivery';

-- 添加新角色 - 查货
INSERT INTO `sys_role` VALUES (16, '查货', 'inspection', 8, '0', NOW(), NULL, '负责查货工序的操作员');

-- ----------------------------
-- 3. 数据迁移 - 更新已有数据到新结构
-- ----------------------------
-- 将旧数据映射到新结构
UPDATE `production_progress` pp
SET 
    pp.cutting_status = pp.burning_status,
    pp.cutting_time = pp.burning_time,
    pp.cutting_operator_id = pp.burning_operator_id,
    
    pp.workshop_status = pp.sewing_status,
    pp.workshop_time = pp.sewing_time,
    pp.workshop_operator_id = pp.sewing_operator_id,
    
    pp.trimming_status = pp.tail_status,
    pp.trimming_time = pp.tail_time,
    pp.trimming_operator_id = pp.tail_operator_id,
    
    pp.inspection_status = 0,  -- 新增字段，默认为未完成
    
    pp.packaging_status = pp.delivery_status,
    pp.packaging_time = pp.delivery_time,
    pp.packaging_operator_id = pp.delivery_operator_id;
    
    -- shipping字段（发货）保持不变，不需要迁移

-- ----------------------------
-- 4. 更新 production_progress_log 表中的进度类型
-- ----------------------------
UPDATE `production_progress_log` 
SET progress_type = 'cutting' WHERE progress_type = 'burning';

UPDATE `production_progress_log` 
SET progress_type = 'workshop' WHERE progress_type = 'sewing';

UPDATE `production_progress_log` 
SET progress_type = 'trimming' WHERE progress_type = 'tail';

UPDATE `production_progress_log` 
SET progress_type = 'packaging' WHERE progress_type = 'delivery';

-- shipping (发货) 保持不变

-- ----------------------------
-- 5. 创建回滚脚本 (如需回滚，请取消下面代码的注释并执行)
-- ----------------------------
/*
-- 恢复production_progress表
DROP TABLE IF EXISTS `production_progress`;
CREATE TABLE `production_progress` AS SELECT * FROM `temp_production_progress`;

-- 恢复production_progress_log表
DROP TABLE IF EXISTS `production_progress_log`;
CREATE TABLE `production_progress_log` AS SELECT * FROM `temp_production_progress_log`;

-- 恢复角色表
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` AS SELECT * FROM `temp_sys_role`;

-- 删除临时表
DROP TABLE IF EXISTS `temp_production_progress`;
DROP TABLE IF EXISTS `temp_production_progress_log`;
DROP TABLE IF EXISTS `temp_sys_role`;
*/

SET FOREIGN_KEY_CHECKS = 1; 