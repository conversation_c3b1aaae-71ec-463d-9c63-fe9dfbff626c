/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : temu_api

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 05/06/2025 21:45:06
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for group_leader_shop_assignment
-- ----------------------------
DROP TABLE IF EXISTS `group_leader_shop_assignment`;
CREATE TABLE `group_leader_shop_assignment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `group_id` bigint NOT NULL COMMENT '运营组ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `user_id` bigint NOT NULL COMMENT '被分配用户ID',
  `permission_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '权限类型(0只读1读写)',
  `assign_time` datetime NULL DEFAULT NULL COMMENT '分配时间',
  `assign_by` bigint NULL DEFAULT NULL COMMENT '分配人ID(组长)',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态(0正常1禁用)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_group_shop_user`(`group_id` ASC, `shop_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_user`(`user_id` ASC) USING BTREE,
  INDEX `idx_shop`(`shop_id` ASC) USING BTREE,
  CONSTRAINT `fk_shop_assignment_shop` FOREIGN KEY (`shop_id`) REFERENCES `shop` (`shop_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 127 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '组长店铺分配表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of group_leader_shop_assignment
-- ----------------------------
INSERT INTO `group_leader_shop_assignment` VALUES (55, 1, 12, 7, '0', '2025-03-25 19:06:17', 7, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (56, 1, 12, 6, '0', '2025-03-25 19:06:21', 7, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (63, 1, 8, 6, '0', '2025-04-03 10:43:53', 22, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (109, 4, 7, 5, '0', '2025-06-05 21:40:33', 1, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (110, 4, 11, 5, '0', '2025-06-05 21:40:33', 1, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (111, 4, 10, 5, '0', '2025-06-05 21:40:33', 1, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (112, 4, 13, 5, '0', '2025-06-05 21:40:33', 1, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (113, 4, 40, 5, '0', '2025-06-05 21:40:33', 1, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (114, 4, 6, 5, '0', '2025-06-05 21:40:33', 1, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (115, 4, 11, 8, '0', '2025-06-05 21:40:49', 1, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (116, 4, 7, 8, '0', '2025-06-05 21:40:49', 1, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (117, 4, 10, 8, '0', '2025-06-05 21:40:49', 1, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (118, 4, 13, 8, '0', '2025-06-05 21:40:49', 1, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (119, 4, 40, 8, '0', '2025-06-05 21:40:49', 1, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (120, 4, 6, 8, '0', '2025-06-05 21:40:49', 1, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (121, 4, 11, 42, '0', '2025-06-05 21:41:00', 1, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (122, 4, 7, 42, '0', '2025-06-05 21:41:00', 1, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (123, 4, 13, 42, '0', '2025-06-05 21:41:00', 1, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (124, 4, 10, 42, '0', '2025-06-05 21:41:00', 1, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (125, 4, 40, 42, '0', '2025-06-05 21:41:00', 1, '0');
INSERT INTO `group_leader_shop_assignment` VALUES (126, 4, 6, 42, '0', '2025-06-05 21:41:00', 1, '0');

SET FOREIGN_KEY_CHECKS = 1;
