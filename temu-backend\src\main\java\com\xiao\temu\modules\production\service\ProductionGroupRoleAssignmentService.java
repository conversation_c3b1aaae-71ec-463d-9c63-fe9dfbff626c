package com.xiao.temu.modules.production.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xiao.temu.modules.production.dto.ProductionGroupRoleAssignmentDTO;
import com.xiao.temu.modules.production.entity.ProductionGroupRoleAssignment;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 生产组角色分配服务接口
 */
public interface ProductionGroupRoleAssignmentService extends IService<ProductionGroupRoleAssignment> {

    /**
     * 分页查询角色分配列表
     *
     * @param groupId 生产组ID
     * @param userId 用户ID，可选
     * @param roleId 角色ID，可选
     * @param pageNum 当前页码
     * @param pageSize 每页大小
     * @return 分页数据
     */
    IPage<ProductionGroupRoleAssignmentDTO> listAssignments(Long groupId, Long userId, Long roleId, Integer pageNum, Integer pageSize);

    /**
     * 根据ID获取角色分配详情
     *
     * @param id 主键ID
     * @return 角色分配详情
     */
    ProductionGroupRoleAssignmentDTO getAssignmentById(Long id);

    /**
     * 新增角色分配
     *
     * @param assignment 角色分配信息
     * @return 操作结果 true-成功 false-失败
     */
    boolean addAssignment(ProductionGroupRoleAssignment assignment);

    /**
     * 修改角色分配信息
     *
     * @param assignment 角色分配信息
     * @return 操作结果 true-成功 false-失败
     */
    boolean updateAssignment(ProductionGroupRoleAssignment assignment);

    /**
     * 修改角色分配状态
     *
     * @param id 主键ID
     * @param status 状态(0正常1禁用)
     * @return 操作结果 true-成功 false-失败
     */
    boolean changeStatus(Long id, String status);

    /**
     * 删除角色分配
     *
     * @param id 主键ID
     * @return 操作结果 true-成功 false-失败
     */
    boolean deleteAssignment(Long id);

    /**
     * 删除用户在生产组中的所有角色
     *
     * @param groupId 生产组ID
     * @param userId 用户ID
     * @return 操作结果 true-成功 false-失败
     */
    boolean deleteUserRoles(Long groupId, Long userId);

    /**
     * 删除生产组的所有角色分配
     *
     * @param groupId 生产组ID
     * @return 操作结果 true-成功 false-失败
     */
    boolean deleteAllGroupAssignments(Long groupId);

    /**
     * 批量分配角色给用户
     *
     * @param groupId 生产组ID
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @param assignBy 分配人ID
     * @return 操作结果 true-成功 false-失败
     */
    boolean batchAssignRoles(Long groupId, Long userId, List<Long> roleIds, Long assignBy);

    /**
     * 查询用户在生产组中的角色ID列表
     *
     * @param groupId 生产组ID
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> getUserRoleIds(Long groupId, Long userId);

    /**
     * 检查用户是否拥有指定角色
     *
     * @param groupId 生产组ID
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 是否拥有角色
     */
    boolean checkUserHasRole(Long groupId, Long userId, Long roleId);

    /**
     * 批量获取多个用户的角色ID列表
     *
     * @param groupId 生产组ID
     * @param userIds 用户ID列表
     * @return Map<用户ID, 角色ID列表>
     */
    Map<Long, List<Long>> batchGetUserRoleIds(Long groupId, List<Long> userIds);

    /**
     * 批量获取多个用户的角色分配信息
     *
     * @param groupId 生产组ID
     * @param userIds 用户ID列表
     * @return Map<用户ID, 角色分配列表>
     */
    Map<Long, List<ProductionGroupRoleAssignmentDTO>> batchGetUserRoleAssignments(Long groupId, List<Long> userIds);
} 