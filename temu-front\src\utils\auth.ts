const TokenKey = 'Admin-Token'

// 获取token，优先从localStorage获取，如果没有则尝试从cookie获取
export function getToken(): string | null {
  const token = localStorage.getItem(TokenKey)
  
  // 如果localStorage中有token，则使用localStorage中的
  if (token) {
    return token
  }
  
  // 尝试从cookie中获取
  return getCookie(TokenKey)
}

// 设置token，同时存储在localStorage和cookie中
export function setToken(token: string): void {
  localStorage.setItem(TokenKey, token)
  
  // 设置cookie，过期时间设为1天，设置secure标志，确保只在HTTPS下传输
  // 将SameSite设置为Lax，允许从外部链接导航到本站时发送cookie
  setCookie(TokenKey, token, 1, true, 'Lax')
}

// 移除token
export function removeToken(): void {
  localStorage.removeItem(TokenKey)
  removeCookie(TokenKey)
}

// 设置cookie的辅助函数
function setCookie(name: string, value: string, days = 1, secure = true, sameSite = 'Lax'): void {
  const date = new Date()
  date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000)
  const expires = `expires=${date.toUTCString()}`
  const secureFlag = secure ? '; secure' : ''
  const sameSiteFlag = sameSite ? `; samesite=${sameSite}` : ''
  document.cookie = `${name}=${value}; ${expires}; path=/; domain=${window.location.hostname}${secureFlag}${sameSiteFlag}`
}

// 获取cookie的辅助函数
function getCookie(name: string): string | null {
  const nameEQ = `${name}=`
  const ca = document.cookie.split(';')
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i]
    while (c.charAt(0) === ' ') {
      c = c.substring(1, c.length)
    }
    if (c.indexOf(nameEQ) === 0) {
      return c.substring(nameEQ.length, c.length)
    }
  }
  return null
}

// 删除cookie的辅助函数
function removeCookie(name: string): void {
  document.cookie = `${name}=; Max-Age=-99999999; path=/; domain=${window.location.hostname}`
} 