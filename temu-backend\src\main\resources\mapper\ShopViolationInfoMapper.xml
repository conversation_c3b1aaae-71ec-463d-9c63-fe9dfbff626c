<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.violation.mapper.ShopViolationInfoMapper">

    <resultMap id="ShopViolationInfoResult" type="com.xiao.temu.modules.violation.entity.ShopViolationInfo">
        <id property="id" column="id"/>
        <result property="shopId" column="shop_id"/>
        <result property="mallId" column="mall_id"/>
        <result property="punishSn" column="punish_sn"/>
        <result property="subPurchaseOrderSn" column="sub_purchase_order_sn"/>
        <result property="punishTypeCode" column="punish_type_code"/>
        <result property="punishTypeDesc" column="punish_type_desc"/>
        <result property="punishFirstTypeCode" column="punish_first_type_code"/>
        <result property="punishFirstTypeDesc" column="punish_first_type_desc"/>
        <result property="punishSecondTypeDesc" column="punish_second_type_desc"/>
        <result property="violationStartTime" column="violation_start_time"/>
        <result property="violationTime" column="violation_time"/>
        <result property="punishAmount" column="punish_amount"/>
        <result property="punishAmountCurrency" column="punish_amount_currency"/>
        <result property="punishStatus" column="punish_status"/>
        <result property="punishStatusDesc" column="punish_status_desc"/>
        <result property="viewDetailsStatus" column="view_details_status"/>
        <result property="countdownTime" column="countdown_time"/>
        <result property="syncTime" column="sync_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    
    <resultMap id="ShopViolationInfoDTOResult" type="com.xiao.temu.modules.violation.dto.ShopViolationInfoDTO">
        <id property="id" column="id"/>
        <result property="shopId" column="shop_id"/>
        <result property="mallId" column="mall_id"/>
        <result property="shopName" column="shop_name"/>
        <result property="shopRemark" column="shop_remark"/>
        <result property="punishSn" column="punish_sn"/>
        <result property="subPurchaseOrderSn" column="sub_purchase_order_sn"/>
        <result property="punishTypeCode" column="punish_type_code"/>
        <result property="punishTypeDesc" column="punish_type_desc"/>
        <result property="punishFirstTypeCode" column="punish_first_type_code"/>
        <result property="punishFirstTypeDesc" column="punish_first_type_desc"/>
        <result property="punishSecondTypeDesc" column="punish_second_type_desc"/>
        <result property="violationStartTime" column="violation_start_time"/>
        <result property="violationTime" column="violation_time"/>
        <result property="punishAmount" column="punish_amount"/>
        <result property="punishAmountCurrency" column="punish_amount_currency"/>
        <result property="punishStatus" column="punish_status"/>
        <result property="punishStatusDesc" column="punish_status_desc"/>
        <result property="viewDetailsStatus" column="view_details_status"/>
        <result property="countdownTime" column="countdown_time"/>
        <result property="syncTime" column="sync_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="productSkuId" column="product_sku_id"/>
        <result property="stockQuantity" column="stock_quantity"/>
        <result property="lackQuantity" column="lack_quantity"/>
        <result property="unqualifiedQuantity" column="unqualified_quantity"/>
    </resultMap>
    
    <sql id="selectViolationInfoSql">
        select v.id, v.shop_id, v.mall_id, s.shop_name, s.remark as shop_remark, v.punish_sn, v.sub_purchase_order_sn, v.punish_type_code,
               v.punish_type_desc, v.punish_first_type_code, v.punish_first_type_desc, v.punish_second_type_desc,
               v.violation_start_time, v.violation_time, v.punish_amount, v.punish_amount_currency,
               v.punish_status, v.punish_status_desc, v.view_details_status, v.countdown_time,
               v.sync_time, v.create_time, v.update_time
        from shop_violation_info v
        left join shop s on v.shop_id = s.shop_id
    </sql>
    
    <sql id="selectViolationInfoWithDetailSql">
        select v.id, v.shop_id, v.mall_id, s.shop_name, s.remark as shop_remark, v.punish_sn, v.sub_purchase_order_sn, v.punish_type_code,
               v.punish_type_desc, v.punish_first_type_code, v.punish_first_type_desc, v.punish_second_type_desc,
               v.violation_start_time, v.violation_time, v.punish_amount, v.punish_amount_currency,
               v.punish_status, v.punish_status_desc, v.view_details_status, v.countdown_time,
               v.sync_time, v.create_time, v.update_time,
               d.product_sku_id, d.stock_quantity, d.lack_quantity, d.unqualified_quantity
        from shop_violation_info v
        left join shop s on v.shop_id = s.shop_id
        left join shop_violation_detail d on v.shop_id = d.shop_id and v.punish_sn = d.punish_sn
    </sql>
    
    <select id="selectViolationInfoList" resultMap="ShopViolationInfoDTOResult">
        <include refid="selectViolationInfoSql"/>
        <if test="query.data_scope != null and query.data_scope != ''">
            inner join shop_group_assignment sga on v.shop_id = sga.shop_id
        </if>
        <where>
            <if test="query.shopId != null">
                AND v.shop_id = #{query.shopId}
            </if>
            <if test="query.shopIds != null and query.shopIds.size() > 0">
                AND v.shop_id IN
                <foreach collection="query.shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test="query.punishSn != null and query.punishSn != ''">
                AND v.punish_sn like concat('%', #{query.punishSn}, '%')
            </if>
            <if test="query.subPurchaseOrderSn != null and query.subPurchaseOrderSn != ''">
                AND v.sub_purchase_order_sn like concat('%', #{query.subPurchaseOrderSn}, '%')
            </if>
            <if test="query.punishStatus != null">
                AND v.punish_status = #{query.punishStatus}
            </if>
            <if test="query.violationTimeBegin != null">
                AND v.violation_time &gt;= #{query.violationTimeBegin}
            </if>
            <if test="query.violationTimeEnd != null">
                AND v.violation_time &lt;= #{query.violationTimeEnd}
            </if>
            <if test="query.groupId != null">
                AND EXISTS (
                    SELECT 1 FROM shop_group_assignment sga 
                    WHERE sga.shop_id = v.shop_id 
                    AND sga.group_id = #{query.groupId} 
                    AND sga.status = '0'
                )
            </if>
            <if test="query.data_scope != null and query.data_scope != ''">
                AND ${query.data_scope}
            </if>
        </where>
        order by v.violation_time desc
    </select>
    
    <select id="selectViolationInfoById" parameterType="Long" resultMap="ShopViolationInfoDTOResult">
        <include refid="selectViolationInfoSql"/>
        where v.id = #{id}
    </select>
    
    <select id="selectByShopIdAndPunishSn" resultMap="ShopViolationInfoResult">
        select * from shop_violation_info
        where shop_id = #{shopId} and punish_sn = #{punishSn}
    </select>
    
    <select id="selectByMallIdAndPunishSn" resultMap="ShopViolationInfoResult">
        select * from shop_violation_info
        where mall_id = #{mallId} and punish_sn = #{punishSn}
    </select>
    
    <select id="selectViolationInfoListWithDetail" resultMap="ShopViolationInfoDTOResult">
        <include refid="selectViolationInfoWithDetailSql"/>
        <if test="query.data_scope != null and query.data_scope != ''">
            inner join shop_group_assignment sga on v.shop_id = sga.shop_id
        </if>
        <where>
            <if test="query.shopId != null">
                AND v.shop_id = #{query.shopId}
            </if>
            <if test="query.shopIds != null and query.shopIds.size() > 0">
                AND v.shop_id IN
                <foreach collection="query.shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test="query.punishSn != null and query.punishSn != ''">
                AND v.punish_sn like concat('%', #{query.punishSn}, '%')
            </if>
            <if test="query.subPurchaseOrderSn != null and query.subPurchaseOrderSn != ''">
                AND v.sub_purchase_order_sn like concat('%', #{query.subPurchaseOrderSn}, '%')
            </if>
            <if test="query.punishStatus != null">
                AND v.punish_status = #{query.punishStatus}
            </if>
            <if test="query.violationTimeBegin != null">
                AND v.violation_time &gt;= #{query.violationTimeBegin}
            </if>
            <if test="query.violationTimeEnd != null">
                AND v.violation_time &lt;= #{query.violationTimeEnd}
            </if>
            <if test="query.groupId != null">
                AND EXISTS (
                    SELECT 1 FROM shop_group_assignment sga 
                    WHERE sga.shop_id = v.shop_id 
                    AND sga.group_id = #{query.groupId} 
                    AND sga.status = '0'
                )
            </if>
            <if test="query.data_scope != null and query.data_scope != ''">
                AND ${query.data_scope}
            </if>
        </where>
        order by v.violation_time desc
    </select>
    
</mapper> 