# 项目基础配置
spring:
  profiles:
    active: prod  # 默认使用开发环境配置
  application:
    name: temu-backend  # 应用名称
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss  # JSON日期格式化
    time-zone: GMT+8  # 时区设置
    serialization:
      WRITE_DATES_AS_TIMESTAMPS: false  # 不以时间戳形式序列化日期
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: false  # 忽略未知属性
      ACCEPT_EMPTY_STRING_AS_NULL_OBJECT: true  # 接受空字符串作为null对象
  servlet:
    multipart:
      max-file-size: 10MB  # 上传文件大小限制
      max-request-size: 20MB  # 请求大小限制
    session:
      timeout: 30m  # 会话超时时间
  security:
    filter:
      order: -100  # 安全过滤器优先级设置

# 日志配置
logging:
  level:
    com.qcloud.cos: WARN    # 设置腾讯云COS SDK的日志级别为WARN
    com.qcloud.cos.http.DefaultCosHttpClient: ERROR  # 设置HTTP客户端的日志级别为ERROR
    com.qcloud.cos.internal: ERROR  # 设置COS内部实现的日志级别为ERROR
    # 设置MyBatis相关的日志级别
    org.apache.ibatis: WARN # 设置ibatis的基础日志级别为WARN
    com.baomidou.mybatisplus: WARN # 设置MyBatis-Plus的日志级别为WARN

# 服务器配置
server:
  servlet:
    context-path: /api  # 应用上下文路径
  tomcat:
    uri-encoding: UTF-8  # URI编码格式
    connection-timeout: 180000  # 连接超时时间,单位毫秒(3分钟)
    keep-alive-timeout: 180000  # 长连接超时时间,单位毫秒(3分钟)
    max-keep-alive-requests: 1000  # 长连接最大请求次数
    max-http-form-post-size: 1073741824  # 1GB的HTTP表单POST请求最大大小

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/**/*.xml  # Mapper XML文件位置
  type-aliases-package: com.xiao.temu.entity,com.xiao.temu.modules.*.entity,com.xiao.temu.infrastructure.*.entity  # 实体类包路径
  configuration:
    map-underscore-to-camel-case: true  # 开启驼峰命名转换
    cache-enabled: true  # 开启二级缓存
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl  # SQL日志实现类

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/**/*.xml  # Mapper XML文件位置
  type-aliases-package: com.xiao.temu.entity,com.xiao.temu.modules.*.entity,com.xiao.temu.infrastructure.*.entity  # 实体类包路径
  configuration:
    map-underscore-to-camel-case: true  # 开启驼峰命名转换
    cache-enabled: true  # 开启二级缓存
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl  # SQL日志实现类
  global-config:
    db-config:
      id-type: auto  # 主键类型
      table-underline: true  # 表名使用下划线命名，默认数据库表
      logic-delete-field: deleted  # 逻辑删除字段
      logic-delete-value: 1  # 逻辑删除值
      logic-not-delete-value: 0  # 逻辑未删除值

# JWT配置
jwt:
  # JWT加密密钥
  secret: asdfghjklmnbvcxzqwertyuiopweroipqtuiyfjklsdagznvbmcxJLKFSAHDJASJFSAUIOIOEWRQNHVACA
  # Token有效期（单位：分钟）
  expiration: 1440
  # Token请求头名称
  header: Authorization
  # Token前缀
  tokenPrefix: Bearer

# 线程池配置
thread:
  pool:
    core-size: 20  # 核心线程数从10增加到20
    max-size: 40  # 最大线程数从20增加到40
    queue-capacity: 500  # 队列容量从100增加到500
    keep-alive-seconds: 60  # 线程空闲时间
  # 添加图片处理专用线程池配置
  image-pool:
    core-size: 24  # 图片处理核心线程数从16增加到24
    max-size: 48  # 图片处理最大线程数从32增加到48
    queue-capacity: 1000  # 图片处理队列容量从300增加到1000
    keep-alive-seconds: 120  # 线程空闲时间从60增加到120秒
  # 添加销售数据同步专用线程池配置
  sales-sync-pool:
    core-size: 5  # 销售数据同步核心线程数
    max-size: 10  # 销售数据同步最大线程数
    queue-capacity: 200  # 销售数据同步队列容量
    keep-alive-seconds: 60  # 线程空闲时间
    # API调用速率限制配置
    request-interval-ms: 500  # 请求间隔(毫秒)
    max-retry-times: 5  # 最大重试次数
    retry-interval-ms: 1000  # 重试间隔(毫秒)
    dynamic-backoff: true  # 是否启用动态退避策略

# 系统配置
system:
  # 是否开启验证码
  captchaEnabled: true
  # 是否允许多点登录
  multipleLogin: false
  # 记住我功能过期时间（单位：天）
  rememberMeDays: 7
  # 密码错误次数锁定账户
  maxRetryCount: 5
  # 锁定时间（单位：分钟）
  lockDuration: 30

# TEMU API相关配置
temu:
  sync:
    # 是否在同步质检数据时自动同步相关商品
    auto-sync-product: true
    # 商品同步批次大小
    product-batch-size: 10
    # 质检数据同步页大小
    quality-page-size: 100
    # 最大同步页数限制
    max-sync-pages: 100