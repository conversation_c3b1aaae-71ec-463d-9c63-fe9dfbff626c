package com.xiao.temu.modules.production.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xiao.temu.modules.production.dto.ProductionGroupDTO;
import com.xiao.temu.modules.production.dto.QueryGroupDTO;
import com.xiao.temu.modules.production.entity.ProductionGroup;
import com.xiao.temu.modules.shop.dto.ShopWithProductionGroupsDTO;
import com.xiao.temu.modules.shop.entity.Shop;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 生产组服务接口
 */
public interface ProductionGroupService {

    /**
     * 分页查询生产组列表
     *
     * @param queryDTO 查询条件
     * @return 生产组分页列表
     */
    IPage<ProductionGroupDTO> getGroupList(QueryGroupDTO queryDTO);

    /**
     * 根据ID查询生产组
     *
     * @param groupId 生产组ID
     * @return 生产组详情
     */
    ProductionGroupDTO getGroupById(Long groupId);

    /**
     * 新增生产组
     *
     * @param groupDTO 生产组信息
     * @return 结果
     */
    int insertGroup(ProductionGroupDTO groupDTO);

    /**
     * 修改生产组
     *
     * @param groupDTO 生产组信息
     * @return 结果
     */
    int updateGroup(ProductionGroupDTO groupDTO);

    /**
     * 删除生产组
     *
     * @param groupId 生产组ID
     * @return 结果
     */
    int deleteGroup(Long groupId);

    /**
     * 批量删除生产组
     *
     * @param groupIds 生产组ID数组
     * @return 结果
     */
    int deleteGroups(Long[] groupIds);

    /**
     * 修改生产组状态
     *
     * @param groupId 生产组ID
     * @param status 状态
     * @return 结果
     */
    int changeStatus(Long groupId, String status);

    /**
     * 检查生产组名称是否唯一
     *
     * @param groupName 生产组名称
     * @param groupId 生产组ID（更新时排除自身）
     * @return 结果 true-唯一 false-不唯一
     */
    boolean checkGroupNameUnique(String groupName, Long groupId);

    /**
     * 设置生产组负责人
     *
     * @param groupId 生产组ID
     * @param leaderId 负责人ID
     * @return 结果
     */
    int setGroupLeader(Long groupId, Long leaderId);

    /**
     * 获取用户负责的生产组列表
     *
     * @param userId 用户ID
     * @return 生产组列表
     */
    List<ProductionGroup> getGroupsByLeaderId(Long userId);
    
    /**
     * 获取用户所属的生产组列表
     *
     * @param userId 用户ID
     * @return 生产组列表
     */
    List<ProductionGroupDTO> getGroupsByMemberId(Long userId);
    
    /**
     * 检查用户是否是某个生产组的组长
     *
     * @param userId 用户ID
     * @param groupId 生产组ID
     * @return 是否为组长
     */
    boolean isGroupLeader(Long userId, Long groupId);
    
    /**
     * 检查用户是否是生产组长
     *
     * @param userId 用户ID
     * @return 是否为生产组长
     */
    boolean isUserGroupLeader(Long userId);
    
    /**
     * 检查生产组是否存在
     * 
     * @param groupId 生产组ID
     * @return 是否存在
     */
    boolean existsById(Long groupId);
    
    /**
     * 获取未分配给指定生产组的店铺列表
     *
     * @param groupId 生产组ID
     * @return 店铺列表
     */
    List<ShopWithProductionGroupsDTO> getUnassignedShops(Long groupId);
    
    /**
     * 将店铺分配给生产组
     *
     * @param groupId 生产组ID
     * @param shopIds 店铺ID列表
     * @return 分配成功数量
     */
    int assignShopsToGroup(Long groupId, List<Long> shopIds);
    
    /**
     * 根据店铺ID获取所属生产组列表
     *
     * @param shopId 店铺ID
     * @return 生产组列表
     */
    List<ProductionGroup> getGroupsByShopId(Long shopId);
    
    /**
     * 从生产组中移除店铺
     *
     * @param groupId 生产组ID
     * @param shopId 店铺ID
     * @return 结果
     */
    int removeShopFromGroup(Long groupId, Long shopId);

    /**
     * 获取生产组关联的店铺列表
     *
     * @param groupId 生产组ID
     * @return 店铺列表
     */
    List<Shop> getShopsByGroupId(Long groupId);
} 