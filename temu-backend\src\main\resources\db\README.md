# 生产进度流程修改说明

## 修改内容

将生产进度流程从5个步骤修改为6个步骤：

- 原流程：烧花 -> 车缝 -> 尾部处理 -> 发货 -> 送货
- 新流程：烧花/剪图 -> 车间/拣货 -> 剪线/压图 -> 查货 -> 包装 -> 发货

## 修改的文件

### 1. 数据库脚本
- `production_progress_update.sql`: 包含表结构修改、角色表更新和数据迁移

### 2. 后台代码
- `ProgressTypeEnum.java`: 修改进度类型枚举
- `ProductionProgress.java`: 修改实体类字段
- `ProductionProgressLog.java`: 更新注释
- `ProductionProgressServiceImpl.java`: 修改初始化和更新/撤销逻辑

### 3. 前端代码（待修改）
需要根据新的进度类型更新前端界面，包括：
- 生产进度展示
- 进度条和状态图标
- 扫码操作界面
- 角色权限管理

## 部署步骤

### 1. 数据库更新
1. 备份数据库（建议全库备份）
2. 执行 `production_progress_update.sql` 脚本

### 2. 后台代码部署
1. 编译并部署最新的后台代码
2. 检查日志，确保启动正常

### 3. 测试
1. 测试新的生产进度流程
2. 验证数据迁移是否正确
3. 检查各角色权限

### 4. 回滚方案
如需回滚，可执行 `production_progress_update.sql` 中被注释的回滚脚本部分。

## 注意事项

1. 原有的发货字段保持不变，确保与其他模块的兼容性
2. 前端代码需要单独更新
3. 需要培训用户使用新的生产进度流程 