<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.violation.mapper.local.LocalViolationInspectionMapper">

    <!-- 查询结果映射 -->
    <resultMap id="ViolationInspectionResult" type="com.xiao.temu.modules.violation.dto.ViolationInspectionDTO">
        <id property="violationId" column="id"/>
        <result property="shopId" column="shop_id"/>
        <result property="shopName" column="shop_name"/>
        <result property="shopRemark" column="shop_remark"/>
        <result property="punishSn" column="punish_sn"/>
        <result property="subPurchaseOrderSn" column="sub_purchase_order_sn"/>
        <result property="violationTime" column="violation_time"/>
        <result property="punishAmount" column="punish_amount"/>
        <result property="punishTypeDesc" column="punish_type_desc"/>
        <result property="punishReasonDesc" column="punish_reason_desc"/>
        <result property="productSkuId" column="product_sku_id"/>
        <result property="productSkcId" column="product_skc_id"/>
        <result property="productName" column="product_name"/>
        <result property="extCode" column="ext_code"/>
        <result property="thumbUrl" column="thumb_url"/>
        <result property="lackQuantity" column="lack_quantity"/>
        <result property="unqualifiedQuantity" column="unqualified_quantity"/>
        <result property="stockQuantity" column="stock_quantity"/>
        <result property="flawNameDesc" column="flaw_name_desc"/>
        <result property="attachments" column="attachments"/>
    </resultMap>

    <!-- 分页查询违规信息和质检结果数据 -->
    <select id="selectViolationInspectionList" resultMap="ViolationInspectionResult">
        SELECT DISTINCT
            svi.id,
            svi.shop_id,
            s.shop_name,
            s.remark AS shop_remark,
            svi.punish_sn,
            svi.sub_purchase_order_sn,
            svi.violation_time,
            svi.punish_amount,
            svi.punish_type_desc,
            CONCAT_WS(' ', svi.punish_first_type_desc, svi.punish_second_type_desc) AS punish_reason_desc,
            svd.product_sku_id,
            ps.product_skc_id,
            p.product_name,
            p.ext_code,
            COALESCE(p.main_image_url, qi.thumb_url) AS thumb_url,
            svd.lack_quantity,
            svd.unqualified_quantity,
            svd.stock_quantity,
            qidd.flaw_name_desc,
            qidd.attachments
        FROM
            shop_violation_info svi
        LEFT JOIN 
            shop_violation_detail svd ON svi.shop_id = svd.shop_id AND svi.punish_sn = svd.punish_sn
        LEFT JOIN 
            shop s ON svi.shop_id = s.shop_id
        LEFT JOIN 
            product_sku ps ON svd.product_sku_id = ps.product_sku_id AND svi.shop_id = ps.shop_id
        LEFT JOIN 
            product p ON ps.product_skc_id = p.product_skc_id AND svi.shop_id = p.shop_id
        LEFT JOIN 
            quality_inspection qi ON svd.product_sku_id = qi.product_sku_id AND svi.shop_id = qi.shop_id
                                  AND svd.sub_purchase_order_sn = qi.purchase_no
        LEFT JOIN 
            quality_inspection_defect_detail qidd ON qi.qc_bill_id = qidd.qc_bill_id 
                                                 AND qi.product_sku_id = qidd.product_sku_id 
                                                 AND qi.shop_id = qidd.shop_id
        <where>
            <if test="query.shopId != null">
                AND svi.shop_id = #{query.shopId}
            </if>
            <if test="query.shopIds != null and query.shopIds.size() > 0">
                AND svi.shop_id IN
                <foreach collection="query.shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test="query.punishSn != null and query.punishSn != ''">
                AND svi.punish_sn = #{query.punishSn}
            </if>
            <if test="query.subPurchaseOrderSn != null and query.subPurchaseOrderSn != ''">
                AND svi.sub_purchase_order_sn = #{query.subPurchaseOrderSn}
            </if>
            <if test="query.violationTimeStart != null">
                AND svi.violation_time &gt;= #{query.violationTimeStart}
            </if>
            <if test="query.violationTimeEnd != null">
                AND svi.violation_time &lt;= #{query.violationTimeEnd}
            </if>
            <if test="query.productSkuId != null">
                AND svd.product_sku_id = #{query.productSkuId}
            </if>
            <if test="query.extCode != null and query.extCode != ''">
                AND p.ext_code = #{query.extCode}
            </if>
            <if test="query.punishTypeCode != null">
                AND svi.punish_type_code = #{query.punishTypeCode}
            </if>
            <if test="query.punishStatus != null">
                AND svi.punish_status = #{query.punishStatus}
            </if>
        </where>
        ORDER BY svi.violation_time DESC
    </select>

    <!-- 查询指定违规编号的详细信息 -->
    <select id="selectViolationInspectionDetail" resultMap="ViolationInspectionResult">
        SELECT DISTINCT
            svi.id,
            svi.shop_id,
            s.shop_name,
            s.remark AS shop_remark,
            svi.punish_sn,
            svi.sub_purchase_order_sn,
            svi.violation_time,
            svi.punish_amount,
            svi.punish_type_desc,
            CONCAT_WS(' ', svi.punish_first_type_desc, svi.punish_second_type_desc) AS punish_reason_desc,
            svd.product_sku_id,
            ps.product_skc_id,
            p.product_name,
            p.ext_code,
            COALESCE(p.main_image_url, qi.thumb_url) AS thumb_url,
            svd.lack_quantity,
            svd.unqualified_quantity,
            svd.stock_quantity,
            qidd.flaw_name_desc,
            qidd.attachments
        FROM
            shop_violation_info svi
        LEFT JOIN 
            shop_violation_detail svd ON svi.shop_id = svd.shop_id AND svi.punish_sn = svd.punish_sn
        LEFT JOIN 
            shop s ON svi.shop_id = s.shop_id
        LEFT JOIN 
            product_sku ps ON svd.product_sku_id = ps.product_sku_id AND svi.shop_id = ps.shop_id
        LEFT JOIN 
            product p ON ps.product_skc_id = p.product_skc_id AND svi.shop_id = p.shop_id
        LEFT JOIN 
            quality_inspection qi ON svd.product_sku_id = qi.product_sku_id AND svi.shop_id = qi.shop_id
                                  AND svd.sub_purchase_order_sn = qi.purchase_no
        LEFT JOIN 
            quality_inspection_defect_detail qidd ON qi.qc_bill_id = qidd.qc_bill_id 
                                                 AND qi.product_sku_id = qidd.product_sku_id 
                                                 AND qi.shop_id = qidd.shop_id
        WHERE
            svi.shop_id = #{shopId} AND svi.punish_sn = #{punishSn}
    </select>
</mapper> 