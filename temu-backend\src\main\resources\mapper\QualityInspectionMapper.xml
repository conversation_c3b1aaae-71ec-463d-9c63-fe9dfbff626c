<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.quality.mapper.QualityInspectionMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.xiao.temu.modules.quality.entity.QualityInspection">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="qc_bill_id" property="qcBillId" />
        <result column="product_sku_id" property="productSkuId" />
        <result column="product_skc_id" property="productSkcId" />
        <result column="spu_id" property="spuId" />
        <result column="sku_name" property="skuName" />
        <result column="cat_name" property="catName" />
        <result column="purchase_no" property="purchaseNo" />
        <result column="spec" property="spec" />
        <result column="thumb_url" property="thumbUrl" />
        <result column="qc_result" property="qcResult" />
        <result column="qc_result_update_time" property="qcResultUpdateTime" />
        <result column="sync_time" property="syncTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 批量插入或更新质检数据 -->
    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO quality_inspection (
            shop_id, qc_bill_id, product_sku_id, product_skc_id, spu_id,
            sku_name, cat_name, purchase_no, spec, thumb_url,
            qc_result, qc_result_update_time, sync_time, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.shopId}, #{item.qcBillId}, #{item.productSkuId}, #{item.productSkcId}, #{item.spuId},
            #{item.skuName}, #{item.catName}, #{item.purchaseNo}, #{item.spec}, #{item.thumbUrl},
            #{item.qcResult}, #{item.qcResultUpdateTime}, #{item.syncTime}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            product_skc_id = VALUES(product_skc_id),
            spu_id = VALUES(spu_id),
            sku_name = VALUES(sku_name),
            cat_name = VALUES(cat_name),
            purchase_no = VALUES(purchase_no),
            spec = VALUES(spec),
            thumb_url = VALUES(thumb_url),
            qc_result = VALUES(qc_result),
            qc_result_update_time = VALUES(qc_result_update_time),
            sync_time = VALUES(sync_time),
            update_time = VALUES(update_time)
    </insert>

    <!-- 根据店铺ID获取最新的质检数据更新时间 -->
    <select id="getLatestUpdateTimeByShopId" resultType="java.time.LocalDateTime">
        SELECT MAX(qc_result_update_time) FROM quality_inspection
        WHERE shop_id = #{shopId}
    </select>
    
    <!-- 根据店铺ID查询不合格的质检数据 -->
    <select id="selectDefectiveByShopId" resultMap="BaseResultMap">
        SELECT * FROM quality_inspection
        WHERE shop_id = #{shopId} AND qc_result = 2
    </select>
    
    <!-- 根据店铺ID查询不合格的质检单ID和SKU ID -->
    <select id="selectDefectiveQcBillAndSkuIdsByShopId" resultType="java.util.Map">
        SELECT qc_bill_id, product_sku_id FROM quality_inspection
        WHERE shop_id = #{shopId} AND qc_result = 2
    </select>

</mapper> 