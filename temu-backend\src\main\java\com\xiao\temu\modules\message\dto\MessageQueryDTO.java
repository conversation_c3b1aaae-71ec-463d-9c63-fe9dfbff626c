package com.xiao.temu.modules.message.dto;

import com.xiao.temu.common.response.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 消息查询DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MessageQueryDTO extends PageRequest {

    /**
     * 消息类型（1系统消息 2任务提醒 3店铺消息）
     */
    private String messageType;

    /**
     * 已读状态（0未读 1已读）
     */
    private String readStatus;

    /**
     * 重要程度(1普通 2重要 3紧急)
     */
    private String importance;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 关键词搜索(标题或内容)
     */
    private String keyword;

    /**
     * 消息ID
     */
    private Long messageId;

    /**
     * 是否删除(0未删除 1已删除)
     */
    private String deleted = "0";
} 