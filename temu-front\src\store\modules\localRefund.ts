import { defineStore } from 'pinia'
import { getLocalRefundPackageList } from '@/api/local/refund'
import { fetchUserAccessibleShops } from '@/utils/shop-helper'
import type { LocalRefundRequestParams, LocalRefundPackageResponse, LocalRefundPackageData } from '@/types/local/refund'
import type { Shop } from '@/types/refund'
import type { ApiResponse } from '@/types/api'

interface LocalRefundState {
  loading: boolean;
  shops: Shop[];
  refundResult: LocalRefundPackageData | null;
  shopIds: number[];
  timeRange: [string, string] | null;
}

export const useLocalRefundStore = defineStore('localRefund', {
  state: (): LocalRefundState => ({
    loading: false,
    shops: [],
    refundResult: null,
    shopIds: [],
    timeRange: null
  }),
  
  actions: {
    /**
     * 获取本地退货包裹列表
     */
    async getLocalRefundPackages(params: LocalRefundRequestParams) {
      this.loading = true
      
      // 确保请求参数完整
      if (this.shopIds.length > 0 && (!params.shopIds || params.shopIds.length === 0)) {
        params.shopIds = this.shopIds;
      }
      
      try {
        // 先转换为unknown再转为ApiResponse，避免类型错误
        const response = await getLocalRefundPackageList(params) as unknown as ApiResponse<LocalRefundPackageData>;
        
        // API响应格式为: { code, message, data }
        if (response && response.code === 200 && response.data) {
          this.refundResult = response.data;
          return response;
        } else {
          console.warn('API响应格式异常或无数据', response);
          return null;
        }
      } catch (error) {
        console.error('获取本地退货明细失败:', error);
        return null;
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 获取用户店铺列表
     */
    async getUserShops() {
      try {
        this.shops = await fetchUserAccessibleShops();
        return this.shops;
      } catch (error) {
        console.error('获取店铺列表失败:', error);
        return [];
      }
    },
    
    /**
     * 设置店铺ID列表
     */
    setShopIds(shopIds: number[]) {
      this.shopIds = shopIds;
    },
    
    /**
     * 设置时间范围
     */
    setTimeRange(timeRange: [string, string] | null) {
      this.timeRange = timeRange;
    },
    
    /**
     * 重置查询
     */
    resetQuery() {
      this.shopIds = [];
      this.timeRange = null;
      this.refundResult = null;
    }
  }
}) 