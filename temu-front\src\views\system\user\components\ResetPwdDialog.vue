<!-- 重置密码弹窗 -->
<template>
  <el-dialog 
    :title="`重置密码: ${userName}`" 
    v-model="dialogVisible" 
    width="500px" 
    append-to-body
    destroy-on-close
  >
    <el-form 
      ref="formRef" 
      :model="form" 
      :rules="rules" 
      label-width="100px"
    >
      <el-form-item label="新密码" prop="password">
        <el-input 
          v-model="form.password" 
          placeholder="请输入新密码" 
          type="password" 
          show-password 
        />
      </el-form-item>
      
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input 
          v-model="form.confirmPassword" 
          placeholder="请再次输入新密码" 
          type="password" 
          show-password 
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { resetUserPwd } from '@/api/user'

const props = defineProps({
  // 是否显示
  visible: {
    type: Boolean,
    default: false
  },
  // 用户ID
  userId: {
    type: Number,
    default: 0
  },
  // 用户名
  userName: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  password: '',
  confirmPassword: ''
})

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 验证密码是否一致
const validateConfirmPassword = (rule: any, value: string, callback: Function) => {
  if (value !== form.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 表单校验规则
const rules = reactive({
  password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
})

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  form.password = ''
  form.confirmPassword = ''
}

// 取消操作
const cancel = () => {
  dialogVisible.value = false
  resetForm()
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await resetUserPwd(props.userId, form.password)
        ElMessage.success('重置密码成功')
        dialogVisible.value = false
        resetForm()
        emit('success')
      } catch (error) {
        console.error('重置密码失败', error)
        ElMessage.error('重置密码失败')
      }
    }
  })
}

// 监听对话框打开，重置表单
watch(() => dialogVisible.value, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})

// 导入watch方法
import { watch } from 'vue'
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style> 