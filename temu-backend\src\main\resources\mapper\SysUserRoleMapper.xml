<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.system.mapper.SysUserRoleMapper">

    <!-- 批量删除用户和角色关联（按用户ID列表） -->
    <delete id="deleteUserRoleByUserIds" parameterType="java.util.List">
        delete from sys_user_role where user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <!-- 通过用户ID删除用户和角色关联 -->
    <delete id="deleteUserRoleByUserId" parameterType="Long">
        delete from sys_user_role where user_id = #{userId}
    </delete>

    <!-- 通过角色ID删除用户和角色关联 -->
    <delete id="deleteUserRoleByRoleId" parameterType="Long">
        delete from sys_user_role where role_id = #{roleId}
    </delete>

    <!-- 批量删除用户和角色关联 -->
    <delete id="deleteUserRole" parameterType="Long">
        delete from sys_user_role where user_id in
        <foreach collection="array" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <!-- 批量新增用户角色信息 -->
    <insert id="batchInsertUserRole">
        insert into sys_user_role(user_id, role_id) values
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.roleId})
        </foreach>
    </insert>

    <!-- 查询用户拥有的角色ID列表 -->
    <select id="selectRoleIdsByUserId" parameterType="Long" resultType="Long">
        select role_id
        from sys_user_role
        where user_id = #{userId}
    </select>

    <!-- 查询角色拥有的用户ID列表 -->
    <select id="selectUserIdsByRoleId" parameterType="Long" resultType="Long">
        select user_id
        from sys_user_role
        where role_id = #{roleId}
    </select>

    <!-- 根据角色ID列表查询用户ID列表 -->
    <select id="selectUserIdsByRoleIds" resultType="Long">
        select distinct user_id
        from sys_user_role
        where role_id in
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </select>

    <!-- 检查用户是否拥有指定角色 -->
    <select id="checkUserHasRole" resultType="int">
        select count(1)
        from sys_user_role
        where user_id = #{userId} and role_id = #{roleId}
    </select>
    
    <!-- 删除用户指定角色 -->
    <delete id="deleteUserRoleByUserIdAndRoleId">
        delete from sys_user_role
        where user_id = #{userId} and role_id = #{roleId}
    </delete>
    
    <!-- 新增用户角色信息 -->
    <insert id="insertUserRole">
        insert into sys_user_role(user_id, role_id)
        values (#{userId}, #{roleId})
    </insert>

</mapper> 