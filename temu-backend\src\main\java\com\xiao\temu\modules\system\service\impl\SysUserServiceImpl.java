package com.xiao.temu.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.operation.entity.GroupMember;
import com.xiao.temu.modules.system.entity.SysRole;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.system.entity.SysUserRole;
import com.xiao.temu.modules.operation.mapper.GroupMemberMapper;
import com.xiao.temu.modules.system.mapper.SysRoleMapper;
import com.xiao.temu.modules.system.mapper.SysUserMapper;
import com.xiao.temu.modules.system.mapper.SysUserRoleMapper;
import com.xiao.temu.modules.system.service.SysUserService;
import com.xiao.temu.modules.system.vo.SysUserVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.xiao.temu.security.utils.SecurityUtils;

/**
 * 系统用户服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysUserServiceImpl implements SysUserService {

    private final SysUserMapper userMapper;
    private final SysRoleMapper roleMapper;
    private final SysUserRoleMapper userRoleMapper;
    private final GroupMemberMapper groupMemberMapper;

    /**
     * 获取未分配到任何运营组的用户列表
     *
     * @param roleKey 角色标识，可选
     * @return 未分配用户列表
     */
    @Override
    public List<SysUserVO> getUnassignedUsers(String roleKey) {
        // 获取所有已分配到运营组的用户ID
        List<Long> assignedUserIds = groupMemberMapper.selectList(null)
                .stream()
                .map(GroupMember::getUserId)
                .distinct()
                .collect(Collectors.toList());
        
        // 构建查询条件
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        
        // 如果有已分配用户，则排除这些用户
        if (!assignedUserIds.isEmpty()) {
            queryWrapper.notIn(SysUser::getUserId, assignedUserIds);
        }
        
        List<SysUser> userList;
        
        // 如果指定了角色标识，则只查询具有该角色的用户
        if (roleKey != null && !roleKey.isEmpty()) {
            // 先获取角色ID
            SysRole role = roleMapper.selectOne(
                new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleKey, roleKey)
            );
            
            if (role != null) {
                // 获取拥有此角色的用户ID
                List<Long> roleUserIds = userRoleMapper.selectList(
                    new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getRoleId, role.getRoleId())
                ).stream().map(SysUserRole::getUserId).collect(Collectors.toList());
                
                if (!roleUserIds.isEmpty()) {
                    // 同时符合：1.拥有指定角色 2.未分配到运营组
                    queryWrapper.in(SysUser::getUserId, roleUserIds);
                    userList = userMapper.selectList(queryWrapper);
                } else {
                    // 没有用户拥有该角色
                    return new ArrayList<>();
                }
            } else {
                // 角色不存在
                return new ArrayList<>();
            }
        } else {
            // 查询所有未分配的用户
            userList = userMapper.selectList(queryWrapper);
        }
        
        // 转换为VO对象
        return userList.stream().map(user -> {
            SysUserVO vo = new SysUserVO();
            vo.setUserId(user.getUserId());
            vo.setUsername(user.getUsername());
            vo.setNickName(user.getNickName());
            vo.setStatus(user.getStatus());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取所有具有管理员角色的用户ID列表
     * 
     * @return 管理员用户ID列表
     */
    @Override
    public List<Long> getAdminUserIds() {
        // 从角色表中查询管理员角色ID
        Long adminRoleId = roleMapper.selectRoleIdByRoleKey("admin");
        if (adminRoleId == null) {
            // 如果找不到管理员角色，返回空列表
            return Collections.emptyList();
        }
        
        // 根据角色ID查询用户ID列表
        return userRoleMapper.selectUserIdsByRoleId(adminRoleId);
    }
}