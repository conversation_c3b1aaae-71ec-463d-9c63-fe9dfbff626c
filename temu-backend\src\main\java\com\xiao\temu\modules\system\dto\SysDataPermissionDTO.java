package com.xiao.temu.modules.system.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 角色数据权限数据传输对象
 */
@Data
public class SysDataPermissionDTO {
    
    /**
     * ID
     */
    private Long id;
    
    /**
     * 角色ID
     */
    @NotNull(message = "角色ID不能为空")
    private Long roleId;
    
    /**
     * 角色名称（仅查询结果展示使用）
     */
    private String roleName;
    
    /**
     * 角色标识（仅查询结果展示使用）
     */
    private String roleKey;
    
    /**
     * 权限类型(0:本人数据 1:本组数据 2:全部数据)
     */
    @NotNull(message = "权限类型不能为空")
    private String permissionType;
    
    /**
     * 备注
     */
    private String remark;
} 