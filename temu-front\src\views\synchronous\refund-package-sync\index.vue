<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>退货包裹数据同步管理</span>
        </div>
      </template>
      
      <!-- 搜索条件 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="100px">
        <el-form-item label="店铺选择" prop="shopIds">
          <el-select
            v-model="selectValue"
            multiple
            filterable
            placeholder="请选择店铺"
            style="width: 360px"
          >
            <el-option
              key="all"
              label="全选"
              :value="-1"
            />
            <el-option
              v-for="item in shopOptions"
              :key="item.shopId"
              :label="item.shopName"
              :value="item.shopId"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>查询
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            @click="handleRefreshList"
          >
            <el-icon><Refresh /></el-icon>刷新
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            @click="handleBatchInit"
            :disabled="selectValue.length === 0 || (selectValue.length === 1 && selectValue[0] === -1)"
          >
            <el-icon><Plus /></el-icon>批量初始化
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            @click="handleBatchSync"
            :disabled="selectValue.length === 0 || (selectValue.length === 1 && selectValue[0] === -1)"
          >
            <el-icon><Connection /></el-icon>批量同步
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            @click="handleBatchClear"
            :disabled="selectValue.length === 0 || (selectValue.length === 1 && selectValue[0] === -1)"
          >
            <el-icon><Delete /></el-icon>批量清空数据
          </el-button>
        </el-col>
      </el-row>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="syncTaskList" @sort-change="handleSortChange">
        <el-table-column label="店铺ID" align="center" prop="shopId" sortable="custom" :sort-orders="['ascending', 'descending']" />
        <el-table-column label="店铺名称" align="center" prop="shopName" :show-overflow-tooltip="true" />
        <el-table-column label="店铺备注" align="center" prop="shopRemark" :show-overflow-tooltip="true" />
        <el-table-column label="上次同步时间" align="center" prop="lastSyncTime" width="160">
          <template #default="scope">
            <span>{{ formatDateTime(scope.row.lastSyncTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="数据最新时间" align="center" prop="lastUpdateTime" width="160">
          <template #default="scope">
            <span>{{ formatDateTime(scope.row.lastUpdateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="同步状态" align="center" prop="syncStatus">
          <template #default="scope">
            <el-tag v-if="scope.row.syncStatus === 0" type="info">未同步</el-tag>
            <el-tag v-else-if="scope.row.syncStatus === 1" type="warning">同步中</el-tag>
            <el-tag v-else-if="scope.row.syncStatus === 2" type="success">同步成功</el-tag>
            <el-tag v-else-if="scope.row.syncStatus === 3" type="danger">同步失败</el-tag>
            <el-tag v-else type="info">未知状态</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="记录总数" align="center" prop="totalRecords" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="handleTriggerSync(scope.row)"
              :disabled="scope.row.syncStatus === 1"
            >
              手动同步
            </el-button>
            <el-button
              type="success"
              link
              @click="handleInitTask(scope.row)"
              :disabled="scope.row.syncStatus === 1"
            >
              初始化任务
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleClearData(scope.row)"
              :disabled="scope.row.syncStatus === 1"
            >
              清空数据
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Connection, Delete } from '@element-plus/icons-vue'
import { getSyncTasks, triggerSync, initSyncTask, batchInitSyncTasks, batchTriggerSync, 
  clearSyncData, batchClearSyncData } from '@/api/refundPackageSync'
import { getUserShops } from '@/api/shop'
import type { RefundPackageSyncParams, RefundPackageSyncTask } from '@/api/refundPackageSync'
import Pagination from '@/components/Pagination/index.vue'

// 接口返回类型
interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 加载状态
const loading = ref(false)
// 总数
const total = ref(0)
// 同步任务列表
const syncTaskList = ref<RefundPackageSyncTask[]>([])
// 店铺选项
const shopOptions = ref<any[]>([])

// 选择值（包含全选选项）
const selectValue = ref<number[]>([])

// 是否已全选
const isAllSelected = ref(false)

// 查询参数
const queryParams = reactive<RefundPackageSyncParams & { pageNum: number, pageSize: number }>({
  shopIds: [] as number[],
  pageNum: 1,
  pageSize: 10,
  sortField: 'shopId',
  sortOrder: 'asc'
})

// 监听选择变化
watch(selectValue, (newVal) => {
  if (newVal.includes(-1)) {
    // 当选择了"全选"选项
    if (!isAllSelected.value) {
      // 全选所有店铺
      isAllSelected.value = true
      const allShopIds = shopOptions.value.map(shop => shop.shopId)
      selectValue.value = [-1, ...allShopIds]
      queryParams.shopIds = [...allShopIds]
    }
  } else {
    // 取消全选
    isAllSelected.value = false
    
    // 判断是否所有店铺都被选中
    if (shopOptions.value.length > 0 && newVal.length === shopOptions.value.length) {
      // 如果所有店铺都被选中，添加全选选项
      isAllSelected.value = true
      selectValue.value = [-1, ...newVal]
    }
    
    // 更新查询参数
    queryParams.shopIds = [...newVal]
  }
}, { deep: true })

// 初始化函数
onMounted(() => {
  // 获取店铺列表
  getShopOptions()
  // 获取同步任务列表
  getList()
})

// 获取店铺选项
const getShopOptions = async () => {
  try {
    const res = await getUserShops() as unknown as ApiResponse<any[]>
    if (res.code === 200) {
      shopOptions.value = res.data
    }
  } catch (error) {
    console.error('获取店铺列表失败', error)
  }
}

// 获取同步任务列表
const getList = async () => {
  loading.value = true
  try {
    const res = await getSyncTasks(queryParams) as unknown as ApiResponse<{
      total: number,
      list: RefundPackageSyncTask[],
      pageNum: number,
      pageSize: number
    }>
    if (res.code === 200) {
      // 后端现在返回的是分页结构
      const { list, total: totalCount } = res.data
      syncTaskList.value = list || []
      total.value = totalCount || 0
      console.log('分页数据加载完成，总记录数：', total.value)
    } else {
      ElMessage.error(res.msg || '获取同步任务列表失败')
      syncTaskList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取同步任务列表失败', error)
    syncTaskList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 查询按钮
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置按钮
const resetQuery = () => {
  isAllSelected.value = false
  selectValue.value = []
  queryParams.shopIds = []
  handleQuery()
}

// 刷新列表
const handleRefreshList = () => {
  getList()
}

// 格式化日期时间
const formatDateTime = (time: string | null) => {
  if (!time) return '--'
  return time
}

// 手动触发同步
const handleTriggerSync = (row: RefundPackageSyncTask) => {
  ElMessageBox.confirm(
    `确认要对店铺 "${row.shopName || row.shopId}" 进行退货包裹数据同步吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const res = await triggerSync(row.shopId) as unknown as ApiResponse<any>
      if (res.code === 200) {
        ElMessage.success('同步任务已触发')
        // 延迟1秒刷新列表
        setTimeout(() => {
          getList()
        }, 1000)
      } else {
        ElMessage.error(res.msg || '触发同步失败')
      }
    } catch (error) {
      console.error('触发同步失败', error)
      ElMessage.error('触发同步失败')
    }
  }).catch(() => {
    // 取消操作
  })
}

// 初始化同步任务
const handleInitTask = (row: RefundPackageSyncTask) => {
  ElMessageBox.confirm(
    `确认要初始化店铺 "${row.shopName || row.shopId}" 的退货包裹同步任务吗？这将重置该店铺的同步状态。`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const res = await initSyncTask(row.shopId) as unknown as ApiResponse<boolean>
      if (res.code === 200) {
        ElMessage.success('同步任务初始化成功')
        getList()
      } else {
        ElMessage.error(res.msg || '初始化同步任务失败')
      }
    } catch (error) {
      console.error('初始化同步任务失败', error)
      ElMessage.error('初始化同步任务失败')
    }
  }).catch(() => {
    // 取消操作
  })
}

// 批量初始化店铺同步任务
const handleBatchInit = () => {
  const shopIds = queryParams.shopIds || [];
  if (shopIds.length === 0) {
    ElMessage.warning('请选择要初始化的店铺')
    return
  }

  ElMessageBox.confirm(
    `确认要初始化 ${shopIds.length} 个选中的店铺退货包裹同步任务吗？这将重置这些店铺的同步状态。`,
    '批量初始化确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    loading.value = true
    try {
      // 使用批量初始化API
      const params: RefundPackageSyncParams = {
        shopIds: shopIds
      }
      const res = await batchInitSyncTasks(params) as unknown as ApiResponse<{
        total: number,
        success: number,
        results: any[]
      }>
      
      if (res.code === 200) {
        const { total, success } = res.data
        if (success > 0) {
          ElMessage.success(`成功初始化 ${success}/${total} 个店铺同步任务`)
        } else {
          ElMessage.warning(`所有店铺初始化失败，请检查日志`)
        }
        // 刷新列表
        getList()
      } else {
        ElMessage.error(res.msg || '批量初始化失败')
      }
    } catch (error) {
      console.error('批量初始化异常', error)
      ElMessage.error('批量初始化过程中发生异常')
    } finally {
      loading.value = false
    }
  }).catch(() => {
    // 取消操作
  })
}

// 批量同步店铺数据
const handleBatchSync = () => {
  const shopIds = queryParams.shopIds || [];
  if (shopIds.length === 0) {
    ElMessage.warning('请选择要同步的店铺')
    return
  }

  ElMessageBox.confirm(
    `确认要对 ${shopIds.length} 个选中的店铺进行退货包裹数据同步吗？`,
    '批量同步确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    loading.value = true
    try {
      // 使用批量同步API
      const params: RefundPackageSyncParams = {
        shopIds: shopIds
      }
      const res = await batchTriggerSync(params) as unknown as ApiResponse<{
        total: number,
        success: number,
        results: any[]
      }>
      
      if (res.code === 200) {
        const { total, success } = res.data
        if (success > 0) {
          ElMessage.success(`成功触发 ${success}/${total} 个店铺的退货包裹数据同步`)
        } else {
          ElMessage.warning(`所有店铺同步触发失败，请检查日志`)
        }
        // 延迟2秒后刷新列表，让后台有时间更新状态
        setTimeout(() => {
          getList()
        }, 2000)
      } else {
        ElMessage.error(res.msg || '批量同步失败')
      }
    } catch (error) {
      console.error('批量同步异常', error)
      ElMessage.error('批量同步过程中发生异常')
    } finally {
      loading.value = false
    }
  }).catch(() => {
    // 取消操作
  })
}

// 清空单个店铺同步数据
const handleClearData = (row: RefundPackageSyncTask) => {
  ElMessageBox.confirm(
    `确认要清空店铺 "${row.shopName || row.shopId}" 的同步数据吗？这将删除该店铺的所有退货包裹数据记录，且无法恢复。`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const res = await clearSyncData(row.shopId) as unknown as ApiResponse<boolean>
      if (res.code === 200) {
        ElMessage.success('同步数据已清空')
        getList()
      } else {
        ElMessage.error(res.msg || '清空同步数据失败')
      }
    } catch (error) {
      console.error('清空同步数据失败', error)
      ElMessage.error('清空同步数据失败')
    }
  }).catch(() => {
    // 取消操作
  })
}

// 批量清空店铺同步数据
const handleBatchClear = () => {
  const shopIds = queryParams.shopIds || [];
  if (shopIds.length === 0) {
    ElMessage.warning('请选择要清空数据的店铺')
    return
  }

  ElMessageBox.confirm(
    `确认要清空 ${shopIds.length} 个选中的店铺同步数据吗？这将删除这些店铺的所有退货包裹数据记录，且无法恢复。`,
    '批量清空确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    loading.value = true
    try {
      // 使用批量清空API
      const params: RefundPackageSyncParams = {
        shopIds: shopIds
      }
      const res = await batchClearSyncData(params) as unknown as ApiResponse<{
        total: number,
        success: number,
        results: any[]
      }>
      
      if (res.code === 200) {
        const { total, success } = res.data
        if (success > 0) {
          ElMessage.success(`成功清空 ${success}/${total} 个店铺的同步数据`)
        } else {
          ElMessage.warning(`所有店铺清空失败，请检查日志`)
        }
        // 刷新列表
        getList()
      } else {
        ElMessage.error(res.msg || '批量清空失败')
      }
    } catch (error) {
      console.error('批量清空异常', error)
      ElMessage.error('批量清空过程中发生异常')
    } finally {
      loading.value = false
    }
  }).catch(() => {
    // 取消操作
  })
}

// 处理排序变化
const handleSortChange = (column: { prop: string, order: string }) => {
  if (column.prop && column.order) {
    queryParams.sortField = column.prop
    queryParams.sortOrder = column.order === 'ascending' ? 'asc' : 'desc'
  } else {
    // 重置为默认排序
    queryParams.sortField = 'shopId'
    queryParams.sortOrder = 'asc'
  }
  getList()
}
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}

/* 确保分页组件显示在正确的位置 */
.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}
</style> 