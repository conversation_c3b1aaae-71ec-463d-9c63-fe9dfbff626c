/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : temu_api

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 03/06/2025 15:41:25
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for group_member
-- ----------------------------
DROP TABLE IF EXISTS `group_member`;
CREATE TABLE `group_member`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `group_id` bigint NOT NULL COMMENT '运营组ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `join_time` datetime NULL DEFAULT NULL COMMENT '加入时间',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_group_user`(`group_id` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 42 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '运营组成员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of group_member
-- ----------------------------
INSERT INTO `group_member` VALUES (3, 2, 3, '2025-03-22 12:37:57', '0');
INSERT INTO `group_member` VALUES (4, 3, 3, '2025-03-22 12:41:21', '0');
INSERT INTO `group_member` VALUES (5, 4, 5, '2025-03-22 13:28:16', '0');
INSERT INTO `group_member` VALUES (21, 1, 7, '2025-03-22 23:54:35', '0');
INSERT INTO `group_member` VALUES (34, 1, 6, '2025-03-24 20:14:13', '0');
INSERT INTO `group_member` VALUES (35, 4, 8, '2025-03-24 20:14:20', '0');

SET FOREIGN_KEY_CHECKS = 1;
