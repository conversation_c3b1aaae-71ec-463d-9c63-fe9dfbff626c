package com.xiao.temu.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.system.entity.SysRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 角色Mapper接口
 */
@Mapper
public interface SysRoleMapper extends BaseMapper<SysRole> {

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRole> selectRolesByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查询角色权限标识
     *
     * @param userId 用户ID
     * @return 角色权限标识列表
     */
    List<String> selectRoleKeysByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查询角色权限标识和角色名称的映射
     *
     * @param userId 用户ID
     * @return 角色标识和名称的映射
     */
    List<Map<String, String>> selectRoleKeysAndNamesByUserId(@Param("userId") Long userId);

    /**
     * 检查角色名称是否唯一
     *
     * @param roleName 角色名称
     * @return 结果
     */
    int checkRoleNameUnique(@Param("roleName") String roleName);

    /**
     * 检查角色权限标识是否唯一
     *
     * @param roleKey 角色权限标识
     * @return 结果
     */
    int checkRoleKeyUnique(@Param("roleKey") String roleKey);

    /**
     * 更新角色状态
     *
     * @param roleId 角色ID
     * @param status 状态
     * @return 结果
     */
    int updateStatus(@Param("roleId") Long roleId, @Param("status") String status);

    /**
     * 根据权限标识查询角色ID列表
     *
     * @param permission 权限标识
     * @return 角色ID列表
     */
    List<Long> selectRoleIdsByPermission(@Param("permission") String permission);

    /**
     * 根据角色标识查询角色ID列表
     *
     * @param roleKey 角色标识
     * @return 角色ID列表
     */
    List<Long> selectRoleIdsByRoleKey(@Param("roleKey") String roleKey);

    /**
     * 根据角色权限标识查询角色ID
     *
     * @param roleKey 角色权限标识
     * @return 角色ID
     */
    Long selectRoleIdByRoleKey(@Param("roleKey") String roleKey);

    /**
     * 根据角色标识查询所有拥有此角色的用户ID列表
     *
     * @param roleKey 角色标识
     * @return 用户ID列表
     */
    List<Long> selectUserIdsByRoleKey(@Param("roleKey") String roleKey);
} 