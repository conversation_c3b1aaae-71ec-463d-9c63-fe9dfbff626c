package com.xiao.temu.modules.message.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 消息详情VO
 */
@Data
public class MessageDetailVO {

    /**
     * 消息ID
     */
    private Long messageId;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息类型（1系统消息 2任务提醒 3店铺消息）
     */
    private String messageType;

    /**
     * 消息类型名称
     */
    private String messageTypeName;

    /**
     * 发送人ID
     */
    private Long fromUserId;

    /**
     * 发送人名称
     */
    private String fromUserName;

    /**
     * 相关店铺ID
     */
    private Long shopId;

    /**
     * 相关店铺名称
     */
    private String shopName;

    /**
     * 已读状态（0未读 1已读）
     */
    private String readStatus;

    /**
     * 重要程度(1普通 2重要 3紧急)
     */
    private String importance;

    /**
     * 重要程度名称
     */
    private String importanceName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 阅读时间
     */
    private LocalDateTime readTime;
} 