package com.xiao.temu.modules.shop.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 店铺导出DTO，专门用于Excel导出
 */
@Data
public class ShopExportDTO {
    
    /** 店铺ID */
    @ExcelProperty("店铺ID")
    private Long shopId;
    
    /** 店铺名称 */
    @ExcelProperty("店铺名称")
    private String shopName;
    
    /** temu平台店铺id */
    @ExcelProperty("Temu店铺ID")
    private String shopTemuId;
    
    /** 运营组名称（拼接字符串） */
    @ExcelProperty("所属运营组")
    private String groupNames;
    
    /** 状态（0正常 1禁用） */
    @ExcelProperty("状态")
    private String statusDesc;
    
    /** 创建时间 */
    @ExcelProperty("创建时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /** 更新时间 */
    @ExcelProperty("更新时间") 
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /** 备注 */
    @ExcelProperty("备注")
    private String remark;
} 