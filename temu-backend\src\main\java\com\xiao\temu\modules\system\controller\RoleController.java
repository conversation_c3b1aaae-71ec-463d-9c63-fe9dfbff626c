package com.xiao.temu.modules.system.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.modules.system.entity.SysRole;
import com.xiao.temu.security.annotation.RequiresPermission;
import com.xiao.temu.modules.system.service.RoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 角色管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/system/role")
@RequiredArgsConstructor
public class RoleController {

    private final RoleService roleService;

    /**
     * 获取角色列表
     */
    @GetMapping("/list")
    @RequiresPermission("system:role:list")
    public ApiResponse getRoleList(SysRole role,
                                  @RequestParam(defaultValue = "1") Integer pageNum,
                                  @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<SysRole> page = new Page<>(pageNum, pageSize);
        return ApiResponse.success(roleService.getRoleList(role, page));
    }

    /**
     * 获取所有角色列表
     */
    @GetMapping("/all")
    @RequiresPermission("system:role:list")
    public ApiResponse getAllRoles() {
        return ApiResponse.success(roleService.getAllRoles());
    }

    /**
     * 获取角色详细信息
     */
    @GetMapping("/{roleId}")
    @RequiresPermission("system:role:query")
    public ApiResponse getRole(@PathVariable Long roleId) {
        SysRole role = roleService.getRoleById(roleId);
        if (role == null) {
            return ApiResponse.error("角色不存在");
        }
        
        // 查询角色的菜单ID列表
        Set<Long> menuIds = roleService.getRoleMenuIds(roleId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("role", role);
        result.put("menuIds", menuIds);
        
        return ApiResponse.success(result);
    }

    /**
     * 新增角色
     */
    @PostMapping
    @RequiresPermission("system:role:add")
    public ApiResponse addRole(@Validated @RequestBody SysRole role) {
        // 校验角色名称是否唯一
        if (!roleService.checkRoleNameUnique(role.getRoleName())) {
            return ApiResponse.error("新增角色'" + role.getRoleName() + "'失败，角色名称已存在");
        }
        
        // 校验角色权限标识是否唯一
        if (!roleService.checkRoleKeyUnique(role.getRoleKey())) {
            return ApiResponse.error("新增角色'" + role.getRoleName() + "'失败，角色权限标识已存在");
        }
        
        // 插入角色信息
        int rows = roleService.insertRole(role);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("新增角色失败");
    }

    /**
     * 修改角色
     */
    @PutMapping
    @RequiresPermission("system:role:edit")
    public ApiResponse updateRole(@Validated @RequestBody SysRole role) {
        // 更新角色信息
        int rows = roleService.updateRole(role);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("修改角色失败");
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{roleIds}")
    @RequiresPermission("system:role:remove")
    public ApiResponse deleteRole(@PathVariable Long[] roleIds) {
        // 删除角色
        try {
            int rows = roleService.deleteRoles(roleIds);
            return rows > 0 ? ApiResponse.success() : ApiResponse.error("删除角色失败");
        } catch (RuntimeException e) {
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 修改角色状态
     */
    @PutMapping("/changeStatus")
    @RequiresPermission("system:role:edit")
    public ApiResponse changeStatus(@RequestParam Long roleId, @RequestParam String status) {
        // 修改角色状态
        int rows = roleService.changeStatus(roleId, status);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("修改角色状态失败");
    }

    /**
     * 分配权限
     */
    @PutMapping("/assignPermissions")
    @RequiresPermission("system:role:edit")
    public ApiResponse assignPermissions(@RequestParam Long roleId, @RequestBody Set<Long> menuIds) {
        // 分配权限
        int rows = roleService.assignPermissions(roleId, menuIds);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("分配权限失败");
    }
} 