package com.xiao.temu.modules.violation.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiao.temu.modules.violation.entity.ShopViolationDetail;
import com.xiao.temu.modules.violation.vo.MmsPurchaseOrderViolationInfoVO;
import com.xiao.temu.modules.violation.dto.ViolationInfoItem;
import com.xiao.temu.modules.violation.mapper.ShopViolationDetailMapper;
import com.xiao.temu.modules.violation.service.ShopViolationDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 店铺违规详情Service实现类
 */
@Slf4j
@Service
public class ShopViolationDetailServiceImpl extends ServiceImpl<ShopViolationDetailMapper, ShopViolationDetail> implements ShopViolationDetailService {

    @Autowired
    private ShopViolationDetailMapper violationDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveViolationDetail(Long shopId, List<ViolationInfoItem> violationItems) {
        if (violationItems == null || violationItems.isEmpty() || shopId == null) {
            return false;
        }

        List<ShopViolationDetail> detailList = new ArrayList<>();
        Date now = new Date();

        for (ViolationInfoItem item : violationItems) {
            // 跳过没有详情的记录
            if (item.getDetail() == null || item.getDetail().getMmspurchaseOrderViolationInfoVO() == null) {
                continue;
            }

            // 检查是否已存在，如果存在则先删除
            if (checkDetailExists(shopId, item.getPunishSn())) {
                log.info("违规详情已存在，先删除后更新: shopId={}, punishSn={}", shopId, item.getPunishSn());
                deleteDetailByShopIdAndPunishSn(shopId, item.getPunishSn());
            }

            // 获取详情数据
            MmsPurchaseOrderViolationInfoVO mmsPurchaseOrder = item.getDetail().getMmspurchaseOrderViolationInfoVO();
            
            ShopViolationDetail detail = new ShopViolationDetail();
            detail.setShopId(shopId);
            detail.setPunishSn(item.getPunishSn());
            detail.setSubPurchaseOrderSn(mmsPurchaseOrder.getSubPurchaseOrderSn());
            detail.setProductSkuId(mmsPurchaseOrder.getProductSkuId());
            detail.setStockQuantity(mmsPurchaseOrder.getStockQuantity());
            detail.setLackQuantity(mmsPurchaseOrder.getLackQuantity());
            detail.setUnqualifiedQuantity(mmsPurchaseOrder.getUnqualifiedQuantity());
            detail.setSyncTime(now);

            detailList.add(detail);
        }

        if (!detailList.isEmpty()) {
            return this.saveBatch(detailList);
        }
        
        return true;
    }

    @Override
    public boolean saveViolationDetail(ShopViolationDetail detail) {
        if (detail == null) {
            return false;
        }
        
        // 检查是否已存在，如果存在则先删除
        if (checkDetailExists(detail.getShopId(), detail.getPunishSn())) {
            log.info("违规详情已存在，先删除后更新: shopId={}, punishSn={}", detail.getShopId(), detail.getPunishSn());
            deleteDetailByShopIdAndPunishSn(detail.getShopId(), detail.getPunishSn());
        }
        
        detail.setSyncTime(new Date());
        return this.save(detail);
    }

    @Override
    public boolean checkDetailExists(Long shopId, String punishSn) {
        Integer count = violationDetailMapper.checkDetailExists(shopId, punishSn);
        return count != null && count > 0;
    }

    @Override
    public boolean deleteDetailByShopIdAndPunishSn(Long shopId, String punishSn) {
        Integer result = violationDetailMapper.deleteByShopIdAndPunishSn(shopId, punishSn);
        return result != null && result > 0;
    }
} 