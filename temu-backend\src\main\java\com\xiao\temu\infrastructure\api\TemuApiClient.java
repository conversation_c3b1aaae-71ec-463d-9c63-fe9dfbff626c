package com.xiao.temu.infrastructure.api;

import com.alibaba.fastjson2.JSONObject;
import com.xiao.temu.common.params.CommonParams;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import java.security.MessageDigest;
import java.time.Instant;
import java.util.*;

/**
 * Temu API 请求接口
 *
 * <AUTHOR>
 */
public class TemuApiClient {
    // API请求的URL地址
    private static String url;

    // 用于发送HTTP请求的WebClient对象
    private static WebClient webClient;

    // HTTP请求头
    private static HttpHeaders httpHeaders;

    // 签名的字段名
    private static String SIGN = "sign";

    // 静态代码块，在类加载时初始化必要的变量
    static {
        // 设置CN区API网关的URL
        url = "https://openapi.kuajingmaihuo.com/openapi/router";
        ExchangeStrategies strategies = ExchangeStrategies.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
                .build();
        // 创建WebClient实例，使用自定义的ExchangeStrategies
        webClient = WebClient.builder()
                .baseUrl(url)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .exchangeStrategies(strategies) // 添加这一行
                .build();

        // 初始化HTTP请求头
        httpHeaders = new HttpHeaders();
        httpHeaders.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
    }
    /**
     * 发送请求到 Temu API 接口并返回响应数据。
     * <p>
     * 该方法将公共参数与业务参数组合，生成完整的请求，发送到 Temu API，并解析返回的响应数据。
     *
     * @param commonParams   包含 Temu API 的公共参数，如应用密钥、访问令牌、时间戳等。
     * @param businessParams 包含特定业务操作所需的参数，例如页号、页码等。
     * @return 返回一个 JSONObject，包含 Temu API 返回的响应数据。
     */
    public static JSONObject sendRequest(CommonParams commonParams, Map<String, Object> businessParams) {
        if (businessParams == null) {
            businessParams = new HashMap<>(10);
        }
        // 如果参数中包含签名字段，则移除该字段
        if (businessParams.containsKey(SIGN)) {
            businessParams.remove(SIGN);
        }

        // 获取temuPublicParam中的各个参数
        String type = commonParams.getType();
        String appKey = commonParams.getAppKey();
        String appSecret = commonParams.getAppSecret();
        String accessToken = commonParams.getAccessToken();

        // 将必要的参数加入到paramMap中
        businessParams.put("type", type);
        businessParams.put("app_key", appKey);
        businessParams.put("access_token", accessToken);
        businessParams.put("timestamp", Instant.now().getEpochSecond());  // 当前时间戳

        // 生成签名
        String sign = generateSign(businessParams, appSecret);

        // 将生成的签名添加到paramMap中
        businessParams.put(SIGN, sign);

        // 发送HTTP请求并获取响应
        Mono<String> responseMono = webClient.post()
                .bodyValue(businessParams)
                .retrieve()
                .bodyToMono(String.class);

        // 获取响应体内容
        String body = responseMono.block();

        // 将响应体转换为JSON对象并返回
        JSONObject bodyJson = JSONObject.parseObject(body);
        return bodyJson;
    }

    /**
     * 生成签名的方法
     *
     * @param paramMap  请求参数的键值对
     * @param appSecret 应用的密钥
     * @return 生成的签名字符串
     */
    private static String generateSign(Map<String, Object> paramMap, String appSecret) {
        // Step 1: 按照键的ASCII码顺序对参数进行排序
        List<String> keys = new ArrayList<>(paramMap.keySet());
        Collections.sort(keys);

        // Step 2: 按照"key + value"的顺序将所有参数拼接成一个字符串
        StringBuilder stringBuilder = new StringBuilder();
        for (String key : keys) {
            Object value = paramMap.get(key);
            if (value != null) {
                String jsonValue = null;
                if (value instanceof String) {
                    jsonValue = (String) value;
                } else {
                    jsonValue = JSONObject.toJSONString(value);
                }
                stringBuilder.append(key).append(jsonValue);
            }
        }

        // Step 3: 在拼接好的字符串前后分别添加appSecret
        String signString = appSecret + stringBuilder.toString() + appSecret;
        // Step 4: 使用MD5算法对字符串进行加密，并将结果转换为大写
        return md5(signString).toUpperCase();
    }

    /**
     * 对字符串进行MD5加密
     *
     * @param input 输入的字符串
     * @return MD5加密后的字符串
     */
    private static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xFF & b);
                if (hex.length() == 1) {
                    hexString.append('0'); // 补零确保两位十六进制
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("生成MD5哈希失败", e);
        }
    }

    public static void addParameterIfPresent(Map<String, Object> businessParams, String key, Object value) {
        if (value != null) {
            if (value instanceof String && ((String) value).isEmpty()) {
                return; // 空字符串不加入
            }
            if (value instanceof Collection && ((Collection<?>) value).isEmpty()) {
                return; // 空集合不加入
            }
            businessParams.put(key, value);
        }
    }
}
