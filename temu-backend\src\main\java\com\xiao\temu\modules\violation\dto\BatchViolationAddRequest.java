package com.xiao.temu.modules.violation.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

/**
 * 批量新增违规信息请求类
 */
@Data
public class BatchViolationAddRequest {
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 错误码
     */
    private Integer errorCode;
    
    /**
     * 错误信息
     */
    private String errorMsg;
    
    /**
     * 结果数据
     */
    private ResultData result;
    
    /**
     * 店铺Temu平台ID
     */
    @JsonProperty("MallId") // 映射 JSON 中的大写 MallId
    private String MallId;
    
    /**
     * 店铺ID (兼容旧接口)
     */
    private Long shopId;
    
    /**
     * 违规信息列表 (兼容旧接口)
     */
    private List<ViolationInfoItem> list;
    
    /**
     * 结果数据内部类
     */
    @Data
    public static class ResultData {
        /**
         * 违规信息列表
         */
        private List<ViolationInfoItem> list;
    }
    
    /**
     * 获取处理后的违规信息列表
     * 兼容新旧两种数据结构
     */
    public List<ViolationInfoItem> getViolationList() {
        // 优先使用result.list，如果为空则使用直接的list字段
        if (result != null && result.getList() != null && !result.getList().isEmpty()) {
            return result.getList();
        }
        return list;
    }
    
    /**
     * 获取MallId（忽略大小写）
     */
    public String getMallId() {
        if (MallId != null && !MallId.isEmpty()) {
            return MallId;
        }
        // 如果直接字段为空，可能是首字母大写的"MallId"
        return null;
    }
} 