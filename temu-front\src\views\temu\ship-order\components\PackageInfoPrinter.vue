<template>
  <div class="package-info-printer">
    <el-button type="primary" size="small" @click="handlePrint" :loading="loading">
      <el-icon class="button-icon"><Printer /></el-icon>
      打印包裹信息 {{ progressText }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Printer } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { jsPDF } from 'jspdf'
import type { ShipOrder } from '@/types/shipOrder'
// 引入中文字体文件
import '@/assets/fonts/msyh-normal.js'
import '@/assets/fonts/msyh-bold.js'
import { printShipOrderLabels } from '@/api/temu/shipOrder'
import QRCode from 'qrcode'

const props = defineProps({
  selectedRows: {
    type: Array as () => ShipOrder[],
    default: () => []
  }
})

// 加载状态
const loading = ref(false)

// 标签数据
const labelData = ref<any[]>([])

// 按仓库分组的标签数据
const warehouseGroupedData = ref<{[key: string]: any[]}>({})

// PDF 分页和生成相关变量
const pdfInstance = ref<jsPDF | null>(null)
const currentWarehouseIndex = ref(0) // 当前处理的仓库索引
const totalWarehouses = ref(0)
const warehouseKeys = ref<string[]>([])
const currentItemStartIndexInWarehouse = ref(0) // 当前仓库中，当前页面处理的发货单起始索引
const isFirstPageDrawn = ref(false) // 标记PDF的第一页是否已绘制
const MAX_ITEMS_PER_PDF_PAGE = 10 // 每个仓库的每张标签（PDF页面）最多包含的发货单数量

// 进度文本
const progressText = computed(() => {
  if (totalWarehouses.value > 0 && loading.value) {
    return `(${currentWarehouseIndex.value + 1}/${totalWarehouses.value})`
  }
  return ''
})

// 绘制表格边框
const drawTableBorder = (pdf: jsPDF, x: number, y: number, width: number, height: number) => {
  pdf.rect(x, y, width, height, 'S')
}

// 绘制表格单元格
const drawTableCell = (pdf: jsPDF, text: string, x: number, y: number, width: number, height: number, align = 'center') => {
  drawTableBorder(pdf, x, y, width, height)
  
  // 计算文本位置以居中显示
  const fontSize = pdf.getFontSize()
  const textWidth = pdf.getStringUnitWidth(text) * fontSize / pdf.internal.scaleFactor
  
  let textX = x
  if (align === 'center') {
    textX = x + (width - textWidth) / 2
  } else if (align === 'right') {
    textX = x + width - textWidth - 2
  } else {
    textX = x + 2 // 左对齐加2mm边距
  }
  
  // 垂直居中
  const textHeight = fontSize / pdf.internal.scaleFactor
  const textY = y + (height + textHeight) / 2
  
  pdf.text(text, textX, textY)
}

// 生成二维码 - 与PackageLabelPrinter.vue保持一致
const generateQRCodeDataURL = async (text: string): Promise<string> => {
  try {
    if (!text || text.trim() === '') {
      return ''
    }
    return await QRCode.toDataURL(text, {
      width: 1000,
      margin: 0,
      errorCorrectionLevel: 'H'
    })
  } catch (error) {
    console.error('生成二维码失败:', error)
    return ''
  }
}

// 安全的文本处理函数
const safeText = (text: any, defaultValue: string = ''): string => {
  if (text === null || text === undefined) return defaultValue
  return String(text)
}

// 绘制汇总包裹信息标签 - 将多个包裹信息汇总到一张标签
const drawSummaryPackageLabel = async (pdf: jsPDF, labelDataList: any[], currentPage: number, totalPagesForWarehouse: number) => {
  try {
    // 设置字体为微软雅黑
    pdf.setFont('msyh')
    
    const margin = 0 // 页面边距
    let currentY = margin
    
    // 设置线条宽度
    pdf.setLineWidth(0.3)
    
    // 计算总的包裹数量
    const totalPackageSkcNum = labelDataList.reduce((total, item) => {
      return total + (item.packageSkcNum || 0)
    }, 0)
    
    // 获取第一个包裹的仓库信息和司机信息（如果所有包裹都是同一仓库）
    const firstItem = labelDataList[0]
    
    // 第一行：仓库信息 (20mm x 100mm)
    drawTableBorder(pdf, margin, currentY, 100, 20)
    
    // 收集所有不同的仓库名称（去重）
    const warehouseNames = Array.from(new Set(
      labelDataList.map(item => safeText(item.subWarehouseName, ''))
        .filter(name => name.trim() !== '')
    ));
    
    // 如果没有有效的仓库名，则显示默认文本
    if (warehouseNames.length === 0) {
      warehouseNames.push('未知仓库');
    }
    
    // 仓库名称
    pdf.setFontSize(10)
    pdf.setFont('msyh', 'normal')
    
    // 如果只有一个仓库，居中显示
    if (warehouseNames.length === 1) {
      const warehouseName = warehouseNames[0];
      const warehouseTextWidth = pdf.getStringUnitWidth(warehouseName) * 10 / pdf.internal.scaleFactor
      pdf.text(warehouseName, (100 - warehouseTextWidth) / 2, currentY + 12)
    } else {
      // 多个仓库，需要处理多行显示
      const warehouseText = warehouseNames.join(', ');
      
      // 处理文本换行
      const maxTextWidth = 90 // mm，留出边距
      const warehouseLines = splitTextIntoLines(pdf, warehouseText, maxTextWidth);
      
      // 计算垂直居中的起始位置
      const lineHeight = 6 // 行高
      const startY = currentY + (20 - lineHeight * warehouseLines.length) / 2;
      
      // 显示每一行
      warehouseLines.forEach((line, idx) => {
        pdf.text(line, margin + 5, startY + lineHeight * idx);
      });
    }
    
    currentY += 20 // 移动到下一行
    
    // 第二行左侧：发货包裹数 (50mm x 30mm)
    drawTableBorder(pdf, margin, currentY, 50, 30)
    
    // 包裹数量 - 显示总数
    pdf.setFontSize(14)
    pdf.setFont('msyh', 'bold')
    const packageNumText = `PC包裹数: ${totalPackageSkcNum}`
    const packageNumWidth = pdf.getStringUnitWidth(packageNumText) * 14 / pdf.internal.scaleFactor
    pdf.text(packageNumText, 25 - packageNumWidth / 2, currentY + 15)

    // 显示发货单数量 (在PC包裹数下方, 同一单元格内)
    pdf.setFontSize(8)
    pdf.setFont('msyh', 'normal') // Ensure font is normal for this text
    const deliveryOrderCount = labelDataList.length
    const deliveryOrderCountText = `发货单数量: ${deliveryOrderCount}`
    const deliveryOrderCountWidth = pdf.getStringUnitWidth(deliveryOrderCountText) * 8 / pdf.internal.scaleFactor
    pdf.text(deliveryOrderCountText, 25 - deliveryOrderCountWidth / 2, currentY + 23) // Y position below PC包裹数
    
    // 第二行右侧：二维码 (50mm x 30mm)
    drawTableBorder(pdf, margin + 50, currentY, 50, 30)
    
    // 生成带有所需信息的二维码内容
    let qrCodeText = ''
    const currentTimestamp = Date.now() // 当前时间戳
    
    // 根据发货单数量决定使用哪种格式
    if (labelDataList.length === 1) {
      // 单个发货单格式
      const item = labelDataList[0]
      const orderInfo = {
        shopId: Number(item.shopId) || 0,
        subPurchaseOrderSn: safeText(item.subPurchaseOrderSn, ''),
        timestamp: currentTimestamp
      }
      qrCodeText = JSON.stringify(orderInfo)
    } else {
      // 多个发货单格式
      const orders = labelDataList.map(item => ({
        shopId: Number(item.shopId) || 0,
        subPurchaseOrderSn: safeText(item.subPurchaseOrderSn, '')
      }))
      
      const batchInfo = {
        orders: orders,
        timestamp: currentTimestamp,
        type: 'batch'
      }
      qrCodeText = JSON.stringify(batchInfo)
    }
    
    // 生成二维码
    const qrCodeDataUrl = await generateQRCodeDataURL(qrCodeText)
    
    // 检查二维码是否生成成功并调整位置
    if (qrCodeDataUrl) {
      const qrSize = 26
      const qrX = margin + 50 + (50 - qrSize) / 2
      const qrY = currentY + (30 - qrSize) / 2
      pdf.addImage(qrCodeDataUrl, 'PNG', qrX, qrY, qrSize, qrSize)
    }
    
    currentY += 30 // 移动到下一行
    
    // 第三行：备货单号和包裹号信息 (40mm x 100mm)
    drawTableBorder(pdf, margin, currentY, 100, 40)
    
    // 显示发货单汇总信息
    pdf.setFontSize(8) // 将字体从8pt减小到6pt
    pdf.setFont('msyh', 'normal')
    
    // 设置行高和最大宽度
    const maxTextWidth = 92 // 增加可用宽度，原来是90mm
    const lineHeight = 4 // 减小行高，原来是6mm
    let nextY = currentY + 6 // 调整起始位置，原来是8
    
    // 备货单号信息
    const purchaseOrders = labelDataList.map(item => item.subPurchaseOrderSn).filter(Boolean)
    if (purchaseOrders.length > 0) {
      // 拼接备货单号文本
      const purchaseOrderText = `备货单号: ${purchaseOrders.join(', ')}`
      
      // 处理文本换行
      const purchaseOrderLines = splitTextIntoLines(pdf, purchaseOrderText, maxTextWidth)
      // 限制最大展示行数，确保有空间留给包裹号
      const maxPurchaseOrderLines = Math.min(purchaseOrderLines.length, 5) // 最多显示5行备货单号
      for (let idx = 0; idx < maxPurchaseOrderLines; idx++) {
        pdf.text(purchaseOrderLines[idx], margin + 5, nextY)
        nextY += lineHeight
      }
    }
    
    // 包裹号信息
    const packageSns = labelDataList.map(item => item.packageSn).filter(Boolean)
    if (packageSns.length > 0 && nextY < currentY + 38) {
      // 拼接包裹号文本
      const packageSnText = `包裹号: ${packageSns.join(', ')}`
      
      // 处理文本换行
      const packageSnLines = splitTextIntoLines(pdf, packageSnText, maxTextWidth)
      // 确保不超出区域高度
      const remainingHeight = currentY + 40 - nextY - 2 // 减去2mm底部边距
      const maxRemainingLines = Math.floor(remainingHeight / lineHeight)
      const maxPackageSnLines = Math.min(packageSnLines.length, maxRemainingLines)
      
      for (let idx = 0; idx < maxPackageSnLines; idx++) {
        pdf.text(packageSnLines[idx], margin + 5, nextY)
        nextY += lineHeight
      }
    }
    
    currentY += 40 // 移动到下一行
    
    // 第四行：司机信息 (10mm x 100mm)
    drawTableBorder(pdf, margin, currentY, 100, 10)
    
    // 司机姓名
    pdf.setFontSize(10)
    pdf.setFont('msyh', 'normal')
    const driverInfo = firstItem.driverName ? `司机: ${safeText(firstItem.driverName)}` : '司机: 暂无司机信息'
    const driverInfoWidth = pdf.getStringUnitWidth(driverInfo) * 10 / pdf.internal.scaleFactor
    pdf.text(driverInfo, (100 - driverInfoWidth) / 2, currentY + 6)

    // 绘制页码 (右上角)
    if (totalPagesForWarehouse > 0) {
      pdf.setFontSize(8)
      pdf.setFont('msyh', 'normal')
      const pageNumText = `${currentPage}/${totalPagesForWarehouse}`
      const pageNumTextWidth = pdf.getStringUnitWidth(pageNumText) * 8 / pdf.internal.scaleFactor
      const pageNumX = 100 - pageNumTextWidth - 2 // 2mm 边距从右侧
      const pageNumY = 5 // 5mm 边距从顶部
      pdf.text(pageNumText, pageNumX, pageNumY)
    }

  } catch (error) {
    console.error('绘制汇总标签出错:', error)
    // 继续执行，不中断流程
  }
}

// 文本换行处理函数
const splitTextIntoLines = (pdf: jsPDF, text: string, maxWidth: number): string[] => {
  const words = text.split(' ')
  const lines: string[] = []
  let currentLine = ''
  
  // 对于中文文本，按字符处理更合适
  const chars = text.split('')
  
  let tempLine = ''
  for (let i = 0; i < chars.length; i++) {
    const char = chars[i]
    const tempLineWidth = pdf.getStringUnitWidth(tempLine + char) * pdf.getFontSize() / pdf.internal.scaleFactor
    
    if (tempLineWidth > maxWidth) {
      lines.push(tempLine)
      tempLine = char
    } else {
      tempLine += char
    }
  }
  
  // 添加最后一行
  if (tempLine) {
    lines.push(tempLine)
  }
  
  return lines
}

// 重命名并重构分批处理标签的逻辑
const processAndGeneratePdfPages = async () => {
  if (!pdfInstance.value || !loading.value) return

  try {
    if (currentWarehouseIndex.value < totalWarehouses.value) {
      const warehouseKey = warehouseKeys.value[currentWarehouseIndex.value]
      const warehouseSpecificData = warehouseGroupedData.value[warehouseKey]

      // 如果当前仓库数据为空，则跳至下一个仓库
      if (!warehouseSpecificData || warehouseSpecificData.length === 0) {
        currentWarehouseIndex.value++
        currentItemStartIndexInWarehouse.value = 0 // 为下一个仓库重置项目索引
        setTimeout(processAndGeneratePdfPages, 20) // 异步处理，避免阻塞UI
        return
      }

      const totalPagesForCurrentWarehouse = Math.ceil(warehouseSpecificData.length / MAX_ITEMS_PER_PDF_PAGE)
      const currentPageNumberInWarehouse = Math.floor(currentItemStartIndexInWarehouse.value / MAX_ITEMS_PER_PDF_PAGE) + 1

      const dataForThisPage = warehouseSpecificData.slice(
        currentItemStartIndexInWarehouse.value,
        currentItemStartIndexInWarehouse.value + MAX_ITEMS_PER_PDF_PAGE
      )

      if (dataForThisPage.length > 0) {
        if (isFirstPageDrawn.value) {
          // 如果不是PDF的第一页，则添加新页面
          pdfInstance.value.addPage([100, 100])
        } else {
          // 标记第一页即将被绘制
          isFirstPageDrawn.value = true
        }
        
        await drawSummaryPackageLabel(pdfInstance.value, dataForThisPage, currentPageNumberInWarehouse, totalPagesForCurrentWarehouse)
      }

      // 移动到当前仓库的下一批数据
      currentItemStartIndexInWarehouse.value += MAX_ITEMS_PER_PDF_PAGE

      if (currentItemStartIndexInWarehouse.value >= warehouseSpecificData.length) {
        // 当前仓库的所有页面已处理完毕，移动到下一个仓库
        currentWarehouseIndex.value++
        currentItemStartIndexInWarehouse.value = 0 // 为新仓库重置项目索引
      }
      
      // 使用setTimeout延迟处理下一批次，给UI更新和GC留出时间
      setTimeout(processAndGeneratePdfPages, 20)
    } else {
      // 所有仓库及其所有页面都处理完成
      if (isFirstPageDrawn.value) { // 确保至少绘制了一页
        pdfInstance.value.save(`包裹信息汇总_${new Date().toISOString().split('T')[0]}.pdf`)
        ElMessage.success('包裹信息汇总PDF生成成功')
      } else {
        ElMessage.warning('没有可打印的数据来生成PDF。')
      }
      resetPdfGenerationState()
    }
  } catch (e) {
    console.error('PDF生成过程中出错:', e)
    ElMessage.error('生成PDF文件失败')
    resetPdfGenerationState()
  }
}

// 用于重置PDF生成相关状态的辅助函数
const resetPdfGenerationState = () => {
  pdfInstance.value = null
  loading.value = false
  // currentWarehouseIndex 和 currentItemStartIndexInWarehouse 会在 handlePrint 开始时或切换仓库时重置
  // isFirstPageDrawn 也会在 handlePrint 开始时重置
}

// 按仓库分组数据
const groupDataByWarehouse = (data: any[]) => {
  const groups: {[key: string]: any[]} = {}
  
  data.forEach(item => {
    // 使用仓库ID作为键，如果没有ID则使用仓库名称
    const key = item.subWarehouseId 
      ? `${item.subWarehouseId}-${safeText(item.subWarehouseName)}`
      : safeText(item.subWarehouseName, '未知仓库')
    
    if (!groups[key]) {
      groups[key] = []
    }
    
    groups[key].push(item)
  })
  
  return groups
}

const handlePrint = async () => {
  if (props.selectedRows.length === 0) {
    ElMessage.warning('请先选择要打印包裹信息的发货单')
    return
  }
  if (loading.value) {
    ElMessage.warning('正在处理，请稍候')
    return
  }
  
  loading.value = true
  // 初始化/重置PDF生成状态变量
  currentWarehouseIndex.value = 0
  currentItemStartIndexInWarehouse.value = 0
  isFirstPageDrawn.value = false
  
  try {
    // 收集所需数据 - 与PackageLabelPrinter.vue保持一致
    const printData = props.selectedRows.map(row => ({
      deliveryOrderSn: row.deliveryOrderSn,
      subPurchaseOrderSn: row.subPurchaseOrderBasicVO?.subPurchaseOrderSn || row.subPurchaseOrderSn,
      shopId: Number(row.shopId)
    }))
    
    // 调用API获取标签数据 - 使用与PackageLabelPrinter.vue相同的API函数
    const res = await printShipOrderLabels(printData)
    
    // 使用类型断言来处理响应
    type ApiResponse = { code: number; message: string; data: { labelData: any[], totalCount: number } }
    const apiRes = res as unknown as ApiResponse
    
    if (!apiRes || apiRes.code !== 200 || !apiRes.data?.labelData) {
      ElMessage.error((apiRes && apiRes.message) || '获取标签数据失败')
      loading.value = false
      return
    }
    
    // 获取标签数据
    labelData.value = apiRes.data.labelData
    
    if (!labelData.value || labelData.value.length === 0) {
      ElMessage.warning('未获取到打印数据')
      loading.value = false
      return
    }
    
    // 按仓库分组标签数据
    warehouseGroupedData.value = groupDataByWarehouse(labelData.value)
    warehouseKeys.value = Object.keys(warehouseGroupedData.value)
    totalWarehouses.value = warehouseKeys.value.length
    
    // 如果没有有效的仓库分组
    if (totalWarehouses.value === 0) {
      ElMessage.warning('未能根据仓库分组数据')
      loading.value = false
      return
    }
    
    // 创建PDF实例
    pdfInstance.value = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: [100, 100],
      compress: true
    })
    
    // 添加中文字体支持 - 使用与PackageLabelPrinter.vue一致的方式
    try {
      pdfInstance.value.addFont('msyh-normal.ttf', 'msyh', 'normal')
      pdfInstance.value.addFont('msyh-bold.ttf', 'msyh', 'bold')
    } catch (fontError) {
      console.error('字体加载错误，使用默认字体:', fontError)
    }
    
    // 开始处理并生成PDF页面
    processAndGeneratePdfPages()
  } catch (error) {
    console.error('打印包裹信息失败:', error)
    ElMessage.error('生成包裹信息PDF失败')
    resetPdfGenerationState() // 确保在捕获到顶层错误时也重置状态
  }
}
</script>

<style scoped>
.package-info-printer {
  /* 移除margin: 10px 0 */
}
.button-icon {
  margin-right: 4px;
}
</style> 