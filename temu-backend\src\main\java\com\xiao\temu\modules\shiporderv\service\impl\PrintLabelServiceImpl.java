package com.xiao.temu.modules.shiporderv.service.impl;


import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.infrastructure.api.TemuApiClient;
import com.xiao.temu.modules.shiporderv.dto.PrintLabelRequestDTO;
import com.xiao.temu.modules.shiporderv.service.PrintLabelService;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.sync.vo.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 打印标签服务实现
 */
@Slf4j
@Service
public class PrintLabelServiceImpl implements PrintLabelService {

    @Autowired
    private ShopService shopService;

    /**
     * 获取商品打包标签信息
     *
     * @param printLabelRequestList 打印标签请求列表
     * @return API响应，包含标签数据
     */
    @Override
    public ApiResponse getBoxMarkInfo(List<PrintLabelRequestDTO> printLabelRequestList) {
        if (CollectionUtils.isEmpty(printLabelRequestList)) {
            return ApiResponse.error("请求参数不能为空");
        }

        try {
            // 获取第一个请求的店铺ID，用于获取店铺信息
            Long shopId = printLabelRequestList.get(0).getShopId();
            Shop shop = shopService.getShopById(shopId).convertToShop();
            
            if (shop == null) {
                return ApiResponse.error("店铺信息不存在");
            }
            
            // 提取所有发货单号
            List<String> deliveryOrderSnList = printLabelRequestList.stream()
                    .map(PrintLabelRequestDTO::getDeliveryOrderSn)
                    .distinct()
                    .collect(Collectors.toList());
            
            log.info("准备调用Temu API获取标签数据，发货单号列表: {}", deliveryOrderSnList);
            
            // 设置API调用公共参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.logistics.boxmarkinfo.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());
            
            // 设置API调用业务参数
            HashMap<String, Object> businessParams = new HashMap<>();
            businessParams.put("deliveryOrderSnList", deliveryOrderSnList.toArray(new String[0]));
            
            // 调用Temu API
            JSONObject jsonResult = TemuApiClient.sendRequest(commonParams, businessParams);
            log.info("Temu API返回结果: {}", jsonResult);
            
            if (jsonResult == null) {
                return ApiResponse.error("调用Temu API失败，返回结果为空");
            }
            
            // 检查API调用是否成功
            boolean success = jsonResult.getBooleanValue("success");
            if (!success) {
                String errorMsg = jsonResult.getString("errorMsg");
                String errorCode = jsonResult.getString("errorCode");
                return ApiResponse.error("调用Temu API失败: " + errorMsg + " (错误码: " + errorCode + ")");
            }
            
            // 获取标签数据
            List<Map> labelDataList = jsonResult.getList("result", Map.class);
            
            if (CollectionUtils.isEmpty(labelDataList)) {
                return ApiResponse.error("未找到对应的标签数据");
            }

            // 提取所有备货单号
            List<String> subPurchaseOrderSnList = printLabelRequestList.stream()
                    .map(PrintLabelRequestDTO::getSubPurchaseOrderSn)
                    .filter(sn -> sn != null && !sn.isEmpty())
                    .distinct()
                    .collect(Collectors.toList());

            // 如果有备货单号，则获取备货单详情
            Map<String, List<Map>> purchaseOrderDetails = new HashMap<>();
            if (!CollectionUtils.isEmpty(subPurchaseOrderSnList)) {
                purchaseOrderDetails = getPurchaseOrderDetails(shop, subPurchaseOrderSnList);
            }

            // 将备货单信息合并到标签数据中
            for (Map labelData : labelDataList) {
                String deliveryOrderSn = (String) labelData.get("deliveryOrderSn");
                // 默认添加第一个请求的店铺ID
                labelData.put("shopId", shopId);
                
                if (deliveryOrderSn != null) {
                    // 查找对应的请求对象，获取备货单号和指定的店铺ID
                    for (PrintLabelRequestDTO request : printLabelRequestList) {
                        if (deliveryOrderSn.equals(request.getDeliveryOrderSn())) {
                            // 添加该发货单对应的准确店铺ID
                            labelData.put("shopId", request.getShopId());
                            
                            if (request.getSubPurchaseOrderSn() != null && 
                                !request.getSubPurchaseOrderSn().isEmpty()) {
                                String subPurchaseOrderSn = request.getSubPurchaseOrderSn();
                                // 将备货单号添加到标签数据中
                                labelData.put("subPurchaseOrderSn", subPurchaseOrderSn);
                                
                                // 如果找到对应的备货单详情，添加skuQuantityDetailList
                                if (purchaseOrderDetails.containsKey(subPurchaseOrderSn)) {
                                    labelData.put("skuQuantityDetailList", purchaseOrderDetails.get(subPurchaseOrderSn));
                                }
                            }
                            
                            break;
                        }
                    }
                }
            }
            
            // 构建返回数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("labelData", labelDataList);
            responseData.put("totalCount", labelDataList.size());
            
            return ApiResponse.success("获取标签数据成功", responseData);
        } catch (Exception e) {
            log.error("获取标签数据异常", e);
            return ApiResponse.error("获取标签数据异常: " + e.getMessage());
        }
    }

    /**
     * 获取备货单详情
     *
     * @param shop 店铺信息
     * @param subPurchaseOrderSnList 备货单号列表
     * @return 备货单详情Map，key为备货单号，value为skuQuantityDetailList
     */
    private Map<String, List<Map>> getPurchaseOrderDetails(Shop shop, List<String> subPurchaseOrderSnList) {
        Map<String, List<Map>> result = new HashMap<>();
        
        try {
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.purchaseorderv2.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());
            
            HashMap<String, Object> businessParams = new HashMap<>();
            businessParams.put("pageNo", 1);
            businessParams.put("pageSize", 100);
            businessParams.put("subPurchaseOrderSnList", subPurchaseOrderSnList.toArray(new String[0]));
            
            JSONObject jsonResult = TemuApiClient.sendRequest(commonParams, businessParams);
            log.info("调用备货单接口返回结果: {}", jsonResult);
            
            if (jsonResult != null && jsonResult.getBooleanValue("success")) {
                JSONObject resultObj = jsonResult.getJSONObject("result");
                if (resultObj != null) {
                    List<Map> orderList = resultObj.getList("subOrderForSupplierList", Map.class);
                    
                    if (orderList != null) {
                        for (Map order : orderList) {
                            String subPurchaseOrderSn = (String) order.get("subPurchaseOrderSn");
                            if (subPurchaseOrderSn != null) {
                                // 提取skuQuantityDetailList
                                List<Map> skuDetailList = (List<Map>) order.get("skuQuantityDetailList");
                                if (skuDetailList != null) {
                                    result.put(subPurchaseOrderSn, new ArrayList<>(skuDetailList));
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取备货单详情异常", e);
        }
        
        return result;
    }
} 