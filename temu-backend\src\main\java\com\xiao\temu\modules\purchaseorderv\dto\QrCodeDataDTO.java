package com.xiao.temu.modules.purchaseorderv.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 二维码数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QrCodeDataDTO {

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 备货单号
     */
    private String subPurchaseOrderSn;

    /**
     * 时间戳
     */
    private Long timestamp;
} 