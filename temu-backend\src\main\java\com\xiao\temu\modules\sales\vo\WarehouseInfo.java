package com.xiao.temu.modules.sales.vo;

import lombok.Data;

/**
 * 仓库信息VO
 */
@Data
public class WarehouseInfo {
    /**
     * 商品SKU ID
     */
    private Long productSkuId;
    
    /**
     * 仓库组ID
     */
    private Long warehouseGroupId;
    
    /**
     * 仓库组名称
     */
    private String warehouseGroupName;
    
    /**
     * 备货天数
     */
    private Integer stockDays;
    
    /**
     * 安全库存天数
     */
    private Integer safeInventoryDays;
    
    /**
     * 采购配置
     */
    private String purchaseConfig;
    
    /**
     * 今日销量
     */
    private Integer todaySaleVolume;
    
    /**
     * 总销量
     */
    private Integer totalSaleVolume;
    
    /**
     * 近7天销量
     */
    private Integer lastSevenDaysSaleVolume;
    
    /**
     * 近30天销量
     */
    private Integer lastThirtyDaysSaleVolume;
    
    /**
     * 缺货数量
     */
    private Integer lackQuantity;
    
    /**
     * 建议下单量
     */
    private Integer adviceQuantity;
    
    /**
     * 可售天数
     */
    private Integer availableSaleDays;
    
    /**
     * 库存可售天数
     */
    private Integer availableSaleDaysFromInventory;
    
    /**
     * 仓内库存可售天数
     */
    private Integer warehouseAvailableSaleDays;
    
    /**
     * 7日销量参考
     */
    private Integer sevenDaysSaleReference;
    
    /**
     * 七日销量参考类型 1.7日最大销量 2.7日日均销量
     */
    private Integer sevenDaysReferenceSaleType;
} 