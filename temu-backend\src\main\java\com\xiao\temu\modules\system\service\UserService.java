package com.xiao.temu.modules.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.infrastructure.exporter.dto.ExportRequestDTO;
import com.xiao.temu.modules.system.dto.UserExportDTO;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 根据用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    SysUser getUserById(Long userId);

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    SysUser getUserByUsername(String username);

    /**
     * 查询用户列表
     *
     * @param user 用户查询条件
     * @param page 分页信息
     * @param orderBy 排序字段
     * @param orderDir 排序方向
     * @return 用户分页列表
     */
    IPage<SysUser> getUserList(SysUser user, Page<SysUser> page, String orderBy, String orderDir);

    /**
     * 新增用户
     *
     * @param user 用户信息
     * @return 结果
     */
    int insertUser(SysUser user);

    /**
     * 修改用户
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUser(SysUser user);

    /**
     * 删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    int deleteUser(Long userId);

    /**
     * 批量删除用户
     *
     * @param userIds 用户ID数组
     * @return 结果
     */
    int deleteUsers(Long[] userIds);

    /**
     * 重置用户密码
     *
     * @param userId 用户ID
     * @param password 新密码
     * @return 结果
     */
    int resetPassword(Long userId, String password);

    /**
     * 修改用户状态
     *
     * @param userId 用户ID
     * @param status 状态
     * @return 结果
     */
    int changeStatus(Long userId, String status);

    /**
     * 检查用户名是否唯一
     *
     * @param username 用户名
     * @return 结果 true-唯一 false-不唯一
     */
    boolean checkUsernameUnique(String username);

    /**
     * 分配角色
     *
     * @param userId 用户ID
     * @param roleIds 角色ID数组
     * @return 结果
     */
    int assignRoles(Long userId, Long[] roleIds);

    /**
     * 获取用户角色ID列表
     *
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> getUserRoleIds(Long userId);

    /**
     * 检查密码是否正确
     *
     * @param userId 用户ID
     * @param password 密码
     * @return 结果 true-正确 false-不正确
     */
    boolean checkPassword(Long userId, String password);

    /**
     * 根据权限字符获取用户列表
     *
     * @param permission 权限字符
     * @return 用户列表
     */
    List<SysUser> getUsersByPermission(String permission);

    /**
     * 根据角色标识获取用户列表
     *
     * @param roleKey 角色标识
     * @return 用户列表
     */
    List<SysUser> getUsersByRoleKey(String roleKey);

    /**
     * 获取用户导出数据
     *
     * @param exportDTO 导出请求参数
     * @return 用户导出数据列表
     */
    List<UserExportDTO> getExportData(ExportRequestDTO exportDTO);
    
    /**
     * 批量导入用户
     *
     * @param userList 用户列表
     * @param userRoleMap 用户角色映射
     * @return 导入成功的数量
     */
    int batchImportUsers(List<SysUser> userList, Map<String, List<Long>> userRoleMap);
    
    /**
     * 从Excel导入用户
     *
     * @param inputStream Excel文件输入流
     * @return 导入结果，包括成功数量、失败数量、错误信息等
     */
    Map<String, Object> importUserFromExcel(InputStream inputStream);
    
    /**
     * 获取用户导入Excel模板
     *
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    void getImportTemplate(HttpServletResponse response) throws IOException;

    /**
     * 判断用户是否为管理员
     *
     * @param userId 用户ID
     * @return 是否为管理员
     */
    boolean isAdmin(Long userId);
}