
# 微信小程序原型设计提示词

我想开发一个TEMU备货单生产进度追踪小程序

## 目标用户：
- 主要用户：工厂生产线上的工人（烧花工、车缝工、尾部处理工等）以及发货、送货专员
- 管理人员：需要实时查看生产进度的车间主管和管理员
- 用户痛点：1、生产进度无法实时更新和追踪，导致管理效率低下
           2、纸质记录容易出错且无法实时同步
           3、各工序人员需要简单便捷的操作方式

## 核心功能：
1、用户使用工号和密码登录系统，与后台账户系统保持一致
2、扫描备货单二维码，根据用户角色显示对应的进度更新操作
3、不同角色的用户只能更新自己负责的工序进度
4、生产人员可查看自己已处理和待处理的工作列表

## 原型设计要求：
1、用户体验分析：分析生产工人使用场景，确保操作简单直观，减少学习成本
2、产品界面规划：设计登录页面、扫码页面、进度更新页面和工作列表页面
3、高保真UI设计：符合微信小程序设计规范，界面简洁明了，适合工厂环境使用
4、HTML原型实现：使用HTML + Tailwind CSS生成所有原型界面，包括：
   - 登录页面(login.html)：工号/密码登录界面，包含记住密码功能
   - 主页面(home.html)：显示扫码入口和工作列表入口，顶部显示用户角色
   - 扫码页面(scan.html)：集成扫码功能，扫描成功后显示备货单信息
   - 进度更新页面(progress.html)：根据用户角色显示可操作的进度状态更新按钮
   - 工作列表页面(worklist.html)：分为已处理和待处理两个标签页

5、界面设计细节：
   - 界面尺寸模拟iPhone尺寸，增加圆角设计，更接近真实小程序界面
   - 使用微信小程序标准配色方案，主色调为绿色(#07c160)
   - 登录按钮使用大按钮设计，方便工人操作
   - 扫码界面保持简洁，突出扫码框和操作提示
   - 进度更新页面使用大按钮，不同状态用不同颜色区分
   - 工作列表使用卡片式设计，显示备货单号、时间等关键信息

请按照以上要求生成完整的HTML原型代码，确保界面简洁易用，适合工厂环境中的生产人员使用。
