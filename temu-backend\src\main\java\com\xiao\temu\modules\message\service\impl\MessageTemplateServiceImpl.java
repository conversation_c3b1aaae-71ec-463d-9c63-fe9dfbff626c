package com.xiao.temu.modules.message.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiao.temu.modules.message.entity.SysMessageTemplate;
import com.xiao.temu.modules.message.mapper.SysMessageTemplateMapper;
import com.xiao.temu.modules.message.service.MessageTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 消息模板服务实现类
 */
@Service
public class MessageTemplateServiceImpl extends ServiceImpl<SysMessageTemplateMapper, SysMessageTemplate> implements MessageTemplateService {

    @Autowired
    private SysMessageTemplateMapper messageTemplateMapper;

    /**
     * 根据模板编码获取模板
     *
     * @param templateCode 模板编码
     * @return 消息模板
     */
    @Override
    public SysMessageTemplate getByTemplateCode(String templateCode) {
        return messageTemplateMapper.selectByTemplateCode(templateCode);
    }
} 