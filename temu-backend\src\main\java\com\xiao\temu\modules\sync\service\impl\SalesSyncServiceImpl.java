package com.xiao.temu.modules.sync.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.modules.sales.entity.SalesCustomInfo;
import com.xiao.temu.modules.sales.entity.SalesInventoryInfo;
import com.xiao.temu.modules.sales.entity.SalesSkuQuantity;
import com.xiao.temu.modules.sales.entity.SalesSubOrder;
import com.xiao.temu.modules.sales.entity.SalesWarehouseInfo;
import com.xiao.temu.modules.sales.entity.SalesWarehouseInventory;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.sync.entity.SalesSyncTask;
import com.xiao.temu.modules.sync.vo.ApiResponse;
import com.xiao.temu.modules.sales.mapper.SalesCustomInfoMapper;
import com.xiao.temu.modules.sales.mapper.SalesInventoryInfoMapper;
import com.xiao.temu.modules.sales.mapper.SalesSkuQuantityMapper;
import com.xiao.temu.modules.sales.mapper.SalesSubOrderMapper;
import com.xiao.temu.modules.sync.mapper.SalesSyncTaskMapper;
import com.xiao.temu.modules.sales.mapper.SalesWarehouseInfoMapper;
import com.xiao.temu.modules.sales.mapper.SalesWarehouseInventoryMapper;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.sync.service.SalesSyncService;
import com.xiao.temu.infrastructure.api.ApiRateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.ehcache.shadow.org.terracotta.statistics.Time;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.CompletableFuture;

/**
 * 销售数据同步服务实现类
 */
@Service
@Slf4j
public class SalesSyncServiceImpl extends ServiceImpl<SalesSyncTaskMapper, SalesSyncTask> implements SalesSyncService {

    @Autowired
    private SalesSyncTaskMapper salesSyncTaskMapper;

    @Autowired
    private SalesSubOrderMapper salesSubOrderMapper;

    @Autowired
    private SalesSkuQuantityMapper salesSkuQuantityMapper;

    @Autowired
    private SalesWarehouseInfoMapper salesWarehouseInfoMapper;

    @Autowired
    private SalesInventoryInfoMapper salesInventoryInfoMapper;
    
    @Autowired
    private SalesCustomInfoMapper salesCustomInfoMapper;
    
    @Autowired
    private SalesWarehouseInventoryMapper salesWarehouseInventoryMapper;

    @Autowired
    private ShopService shopService;

    @Autowired
    private ThreadPoolTaskExecutor salesSyncExecutor;
    
    @Autowired
    private ApiRateLimiter apiRateLimiter;

    /**
     * 同步指定店铺的销售数据
     *
     * @param shopId 店铺ID
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED)
    public ApiResponse.SalesSyncVO syncSalesData(Long shopId) {
        log.info("开始同步店铺销售数据，店铺ID: {}", shopId);
        ApiResponse.SalesSyncVO result = new ApiResponse.SalesSyncVO();
        result.setShopId(shopId);

        // 获取店铺信息
        Shop shop = shopService.getShopById(shopId).convertToShop();
        if (shop == null) {
            result.setSuccess(false);
            result.setErrorCode(404);
            result.setErrorMsg("店铺不存在");
            return result;
        }
        result.setShopName(shop.getShopName());

        // 获取同步任务
        SalesSyncTask task = salesSyncTaskMapper.findByShopId(shopId);
        if (task == null) {
            // 如果不存在则创建
            boolean initResult = initSyncTask(shopId);
            if (!initResult) {
                result.setSuccess(false);
                result.setErrorCode(500);
                result.setErrorMsg("初始化同步任务失败");
                return result;
            }
            task = salesSyncTaskMapper.findByShopId(shopId);
        }

        // 检查当前同步状态
        if (task.getSyncStatus() == 1) {
            result.setSuccess(false);
            result.setErrorCode(400);
            result.setErrorMsg("该店铺正在同步中，请稍后再试");
            result.setSyncStatus(1);
            return result;
        }

        // 更新任务状态为同步中
        task.setSyncStatus(1);
        task.setLastSyncTime(LocalDateTime.now());
        this.updateById(task);

        try {
            // 准备API请求参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.goods.salesv2.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());

            // 设置计数器
            AtomicInteger totalCounter = new AtomicInteger(0);
            AtomicInteger skuCounter = new AtomicInteger(0);
            AtomicInteger warehouseCounter = new AtomicInteger(0);

            // 设置分页参数
            int pageNo = 1;
            int pageSize = 100; // API最大支持100条每页
            boolean hasMore = true;

            while (hasMore) {
                // 构建业务参数
                HashMap<String, Object> businessParams = new HashMap<>();
                businessParams.put("timestamp", String.valueOf(Time.time()));
                businessParams.put("pageNo", String.valueOf(pageNo));
                businessParams.put("pageSize", String.valueOf(pageSize));

                // 调用API - 使用限流工具
                log.info("调用销售数据API，店铺ID: {}, 页码: {}, 每页条数: {}", shopId, pageNo, pageSize);
                JSONObject response = apiRateLimiter.sendRequestWithRateLimit(
                        shopId, commonParams, businessParams, "普通同步页码" + pageNo);
                
                if (response == null) {
                    log.error("API调用返回为空，店铺ID: {}", shopId);
                    throw new RuntimeException("API调用返回为空");
                }

                // 检查API返回状态
                Boolean success = response.getBoolean("success");
                if (success == null || !success) {
                    String errorMsg = response.getString("errorMsg");
                    log.error("API调用失败，店铺ID: {}, 错误信息: {}", shopId, errorMsg);
                    throw new RuntimeException("API调用失败: " + errorMsg);
                }

                JSONObject result2 = response.getJSONObject("result");
                if (result2 == null) {
                    log.error("API调用响应数据格式错误，店铺ID: {}", shopId);
                    throw new RuntimeException("API调用响应数据格式错误");
                }

                // 处理子订单列表
                processSalesData(result2, shopId, totalCounter, skuCounter, warehouseCounter);

                // 检查是否有更多页
                Integer total = result2.getInteger("total");
                if (total == null || pageNo * pageSize >= total) {
                    hasMore = false;
                } else {
                    pageNo++;
                }
            }

            // 更新同步任务状态为成功
            task.setSyncStatus(2);
            task.setTotalRecords(totalCounter.get());
            task.setSkuTotalRecords(skuCounter.get());
            task.setWarehouseTotalRecords(warehouseCounter.get());
            task.setLastUpdateTime(LocalDateTime.now());
            this.updateById(task);

            // 设置返回结果
            result.setSuccess(true);
            result.setSyncStatus(2);
            result.setTotalRecords(totalCounter.get());
            result.setSkuTotalRecords(skuCounter.get());
            result.setWarehouseTotalRecords(warehouseCounter.get());
            result.setLastSyncTime(task.getLastSyncTime());
            result.setLastUpdateTime(task.getLastUpdateTime());
            result.setMessage("同步成功，共同步" + totalCounter.get() + "条数据，包含" + skuCounter.get() + "个SKU，" + warehouseCounter.get() + "条仓库信息");
            
            log.info("店铺{}同步完成，总记录数：{}，SKU总数：{}，仓库信息总数：{}", 
                    shopId, totalCounter.get(), skuCounter.get(), warehouseCounter.get());

        } catch (Exception e) {
            log.error("同步销售数据出错，店铺ID: " + shopId, e);
            
            // 更新同步任务状态为失败
            task.setSyncStatus(3);
            task.setErrorMessage(e.getMessage());
            task.setLastUpdateTime(LocalDateTime.now());
            this.updateById(task);
            
            // 设置返回结果
            result.setSuccess(false);
            result.setSyncStatus(3);
            result.setErrorCode(500);
            result.setErrorMsg("同步失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 处理销售数据
     *
     * @param result API响应结果
     * @param shopId 店铺ID
     * @param totalCounter 总数计数器
     * @param skuCounter SKU计数器
     * @param warehouseCounter 仓库计数器
     */
    private void processSalesData(JSONObject result, Long shopId, AtomicInteger totalCounter, AtomicInteger skuCounter, AtomicInteger warehouseCounter) {
        List<JSONObject> subOrderList = result.getJSONArray("subOrderList").toList(JSONObject.class);
        if (subOrderList == null || subOrderList.isEmpty()) {
            log.warn("店铺 {} 未获取到销售子订单数据", shopId);
            return;
        }

        // 初始化本次批次处理的计数器
        int batchOrderCount = 0;
        int batchSkuCount = 0;
        int batchWarehouseCount = 0;
        int batchUpdateOrderCount = 0;
        int batchInsertOrderCount = 0;

        for (JSONObject subOrder : subOrderList) {
            try {
                // 保存子订单信息
                SalesSubOrder salesSubOrder = parseSalesSubOrder(subOrder, shopId);
                // 检查是否已存在
                LambdaQueryWrapper<SalesSubOrder> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(SalesSubOrder::getShopId, shopId)
                        .eq(SalesSubOrder::getProductId, salesSubOrder.getProductId())
                        .eq(SalesSubOrder::getProductSkcId, salesSubOrder.getProductSkcId());
                
                SalesSubOrder existOrder = salesSubOrderMapper.selectOne(wrapper);
                if (existOrder != null) {
                    // 更新已有记录
                    salesSubOrder.setId(existOrder.getId());
                    salesSubOrderMapper.updateById(salesSubOrder);
                    batchUpdateOrderCount++;
                } else {
                    // 插入新记录
                    salesSubOrderMapper.insert(salesSubOrder);
                    batchInsertOrderCount++;
                }
                totalCounter.incrementAndGet();
                batchOrderCount++;
                
                // 处理定制信息
                JSONObject customInfoVO = subOrder.getJSONObject("customInfoVO");
                if (customInfoVO != null && salesSubOrder.getIsCustomGoods()) {
                    // 保存定制信息
                    SalesCustomInfo salesCustomInfo = parseSalesCustomInfo(customInfoVO, shopId, salesSubOrder.getProductSkcId());
                    
                    // 检查是否已存在
                    LambdaQueryWrapper<SalesCustomInfo> customWrapper = new LambdaQueryWrapper<>();
                    customWrapper.eq(SalesCustomInfo::getShopId, shopId)
                            .eq(SalesCustomInfo::getProductSkcId, salesSubOrder.getProductSkcId());
                    
                    SalesCustomInfo existCustomInfo = salesCustomInfoMapper.selectOne(customWrapper);
                    if (existCustomInfo != null) {
                        // 更新已有记录
                        salesCustomInfo.setId(existCustomInfo.getId());
                        salesCustomInfoMapper.updateById(salesCustomInfo);
                    } else {
                        // 插入新记录
                        salesCustomInfoMapper.insert(salesCustomInfo);
                    }
                }

                // 处理SKU数量信息
                int skuCount = 0;
                int warehouseCount = 0;
                List<JSONObject> skuQuantityList = subOrder.getJSONArray("skuQuantityDetailList").toList(JSONObject.class);
                if (skuQuantityList != null && !skuQuantityList.isEmpty()) {
                    for (JSONObject skuQuantity : skuQuantityList) {
                        try {
                            // 保存SKU数量信息
                            SalesSkuQuantity salesSkuQuantity = parseSalesSkuQuantity(skuQuantity, shopId, salesSubOrder.getProductId(), salesSubOrder.getProductSkcId());
                            
                            // 检查是否已存在
                            LambdaQueryWrapper<SalesSkuQuantity> skuWrapper = new LambdaQueryWrapper<>();
                            skuWrapper.eq(SalesSkuQuantity::getShopId, shopId)
                                    .eq(SalesSkuQuantity::getProductId, salesSubOrder.getProductId())
                                    .eq(SalesSkuQuantity::getProductSkcId, salesSubOrder.getProductSkcId())
                                    .eq(SalesSkuQuantity::getProductSkuId, salesSkuQuantity.getProductSkuId());
                            
                            SalesSkuQuantity existSku = salesSkuQuantityMapper.selectOne(skuWrapper);
                            if (existSku != null) {
                                // 更新已有记录
                                salesSkuQuantity.setId(existSku.getId());
                                salesSkuQuantityMapper.updateById(salesSkuQuantity);
                            } else {
                                // 插入新记录
                                salesSkuQuantityMapper.insert(salesSkuQuantity);
                            }
                            skuCounter.incrementAndGet();
                            batchSkuCount++;
                            skuCount++;

                            // 处理仓库信息
                            int skuWarehouseCount = 0;
                            List<JSONObject> warehouseInfoList = skuQuantity.getJSONArray("warehouseInfoList").toList(JSONObject.class);
                            if (warehouseInfoList != null && !warehouseInfoList.isEmpty()) {
                                for (JSONObject warehouseInfo : warehouseInfoList) {
                                    // 保存仓库信息
                                    SalesWarehouseInfo salesWarehouseInfo = parseSalesWarehouseInfo(warehouseInfo, shopId, salesSkuQuantity.getProductSkuId());
                                    
                                    // 检查是否已存在
                                    LambdaQueryWrapper<SalesWarehouseInfo> warehouseWrapper = new LambdaQueryWrapper<>();
                                    warehouseWrapper.eq(SalesWarehouseInfo::getShopId, shopId)
                                            .eq(SalesWarehouseInfo::getProductSkuId, salesSkuQuantity.getProductSkuId())
                                            .eq(SalesWarehouseInfo::getWarehouseGroupId, salesWarehouseInfo.getWarehouseGroupId());
                                    
                                    SalesWarehouseInfo existWarehouse = salesWarehouseInfoMapper.selectOne(warehouseWrapper);
                                    if (existWarehouse != null) {
                                        // 更新已有记录
                                        salesWarehouseInfo.setId(existWarehouse.getId());
                                        salesWarehouseInfoMapper.updateById(salesWarehouseInfo);
                                    } else {
                                        // 插入新记录
                                        salesWarehouseInfoMapper.insert(salesWarehouseInfo);
                                    }
                                    warehouseCounter.incrementAndGet();
                                    batchWarehouseCount++;
                                    skuWarehouseCount++;

                                    // 处理库存信息
                                    JSONObject inventoryNumInfo = warehouseInfo.getJSONObject("inventoryNumInfo");
                                    if (inventoryNumInfo != null) {
                                        // 保存仓库库存详情
                                        SalesWarehouseInventory salesWarehouseInventory = parseSalesWarehouseInventory(inventoryNumInfo, shopId, 
                                                salesSkuQuantity.getProductSkuId(), salesWarehouseInfo.getWarehouseGroupId());
                                                
                                        // 检查是否已存在
                                        LambdaQueryWrapper<SalesWarehouseInventory> warehouseInventoryWrapper = new LambdaQueryWrapper<>();
                                        warehouseInventoryWrapper.eq(SalesWarehouseInventory::getShopId, shopId)
                                                .eq(SalesWarehouseInventory::getProductSkuId, salesSkuQuantity.getProductSkuId())
                                                .eq(SalesWarehouseInventory::getWarehouseGroupId, salesWarehouseInfo.getWarehouseGroupId());
                                                
                                        SalesWarehouseInventory existWarehouseInventory = salesWarehouseInventoryMapper.selectOne(warehouseInventoryWrapper);
                                        if (existWarehouseInventory != null) {
                                            // 更新已有记录
                                            salesWarehouseInventory.setId(existWarehouseInventory.getId());
                                            salesWarehouseInventoryMapper.updateById(salesWarehouseInventory);
                                        } else {
                                            // 插入新记录
                                            salesWarehouseInventoryMapper.insert(salesWarehouseInventory);
                                        }
                                        
                                        // 保存库存信息 (兼容旧版本，保留原有的SalesInventoryInfo处理)
                                        SalesInventoryInfo salesInventoryInfo = parseSalesInventoryInfo(inventoryNumInfo, shopId, salesSkuQuantity.getProductSkuId(), salesWarehouseInfo.getWarehouseGroupId());
                                        
                                        // 检查是否已存在
                                        LambdaQueryWrapper<SalesInventoryInfo> inventoryWrapper = new LambdaQueryWrapper<>();
                                        inventoryWrapper.eq(SalesInventoryInfo::getShopId, shopId)
                                                .eq(SalesInventoryInfo::getProductSkuId, salesSkuQuantity.getProductSkuId())
                                                .eq(SalesInventoryInfo::getWarehouseGroupId, salesWarehouseInfo.getWarehouseGroupId());
                                        
                                        SalesInventoryInfo existInventory = salesInventoryInfoMapper.selectOne(inventoryWrapper);
                                        if (existInventory != null) {
                                            // 更新已有记录
                                            salesInventoryInfo.setId(existInventory.getId());
                                            salesInventoryInfoMapper.updateById(salesInventoryInfo);
                                        } else {
                                            // 插入新记录
                                            salesInventoryInfoMapper.insert(salesInventoryInfo);
                                        }
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.error("处理SKU数量信息出错: shopId={}, productId={}, error={}", 
                                    shopId, salesSubOrder.getProductId(), e.getMessage(), e);
                        }
                    }
                }
                
                // 每处理完一个子订单，记录SKU和仓库数量（替换原来的每个SKU详细日志）
                if (log.isDebugEnabled()) {
//                    log.debug("店铺 {} 处理订单 SKC ID {} 的 {} 个SKU, {} 条仓库信息",
//                        shopId, salesSubOrder.getProductSkcId(), skuCount, warehouseCount);
                }
            } catch (Exception e) {
                log.error("处理销售子订单数据出错: shopId={}, error={}", shopId, e.getMessage(), e);
                // 继续处理下一个子订单
            }
        }
        
        // 每批次处理完成后，记录批次汇总日志
        log.info("店铺 {} 完成一批数据处理: {} 条子订单(新增:{},更新:{}), {} 个SKU, {} 条仓库信息", 
                shopId, batchOrderCount, batchInsertOrderCount, batchUpdateOrderCount, batchSkuCount, batchWarehouseCount);
    }

    /**
     * 解析销售子订单
     */
    private SalesSubOrder parseSalesSubOrder(JSONObject jsonObject, Long shopId) {
        SalesSubOrder salesSubOrder = new SalesSubOrder();
        salesSubOrder.setShopId(shopId);
        salesSubOrder.setProductId(jsonObject.getLong("productId"));
        salesSubOrder.setProductSkcId(jsonObject.getLong("productSkcId"));
        salesSubOrder.setProductName(jsonObject.getString("productName"));
        salesSubOrder.setSkcExtCode(jsonObject.getString("skcExtCode"));
        salesSubOrder.setCategory(jsonObject.getString("category"));
        salesSubOrder.setProductSkcPicture(jsonObject.getString("productSkcPicture"));
        
        // 处理自定义商品信息
        JSONObject customInfoVO = jsonObject.getJSONObject("customInfoVO");
        if (customInfoVO != null) {
            salesSubOrder.setIsCustomGoods(customInfoVO.getBooleanValue("isCustomGoods"));
        } else {
            salesSubOrder.setIsCustomGoods(false);
        }
        
        // 确保Integer类型字段使用getInteger方法获取，防止类型转换错误
        salesSubOrder.setInventoryRegion(jsonObject.getInteger("inventoryRegion"));
        salesSubOrder.setSupplyStatus(jsonObject.getInteger("supplyStatus"));
        salesSubOrder.setSupplyStatusRemark(jsonObject.getString("supplyStatusRemark"));
        salesSubOrder.setInBlackList(jsonObject.getBooleanValue("inBlackList"));
        salesSubOrder.setPictureAuditStatus(jsonObject.getInteger("pictureAuditStatus"));
        salesSubOrder.setOnSalesDurationOffline(jsonObject.getInteger("onSalesDurationOffline"));
        salesSubOrder.setCloseJitStatus(jsonObject.getInteger("closeJitStatus"));
        salesSubOrder.setAutoCloseJit(jsonObject.getBooleanValue("autoCloseJit"));
        salesSubOrder.setHotTag(jsonObject.getBooleanValue("hotTag"));
        salesSubOrder.setHasHotSku(jsonObject.getBooleanValue("hasHotSku"));
        salesSubOrder.setIsEnoughStock(jsonObject.getBooleanValue("isEnoughStock"));
        salesSubOrder.setPurchaseStockType(jsonObject.getInteger("purchaseStockType"));
        salesSubOrder.setSettlementType(jsonObject.getInteger("settlementType"));
        salesSubOrder.setSyncTime(LocalDateTime.now());
        
        return salesSubOrder;
    }

    /**
     * 解析销售SKU数量信息
     */
    private SalesSkuQuantity parseSalesSkuQuantity(JSONObject jsonObject, Long shopId, Long productId, Long productSkcId) {
        SalesSkuQuantity salesSkuQuantity = new SalesSkuQuantity();
        try {
            salesSkuQuantity.setShopId(shopId);
            salesSkuQuantity.setProductId(productId);
            salesSkuQuantity.setProductSkcId(productSkcId);
            salesSkuQuantity.setProductSkuId(jsonObject.getLong("productSkuId"));
            salesSkuQuantity.setWarehouseGroupId(jsonObject.getLong("warehouseGroupId"));
            salesSkuQuantity.setWarehouseGroupName(jsonObject.getString("warehouseGroupName"));
            salesSkuQuantity.setClassName(jsonObject.getString("className"));
            salesSkuQuantity.setSkuExtCode(jsonObject.getString("skuExtCode"));
            salesSkuQuantity.setCanPurchase(jsonObject.getBooleanValue("canPurchase"));
            salesSkuQuantity.setStockDays(jsonObject.getInteger("stockDays"));
            salesSkuQuantity.setSafeInventoryDays(jsonObject.getInteger("safeInventoryDays"));
            salesSkuQuantity.setPurchaseConfig(jsonObject.getString("purchaseConfig"));
            salesSkuQuantity.setPriceReviewStatus(jsonObject.getInteger("priceReviewStatus"));
            salesSkuQuantity.setIsVerifyPrice(jsonObject.getBooleanValue("isVerifyPrice"));
            salesSkuQuantity.setIsReducePricePass(jsonObject.getBooleanValue("isReducePricePass"));
            salesSkuQuantity.setTodaySaleVolume(jsonObject.getInteger("todaySaleVolume"));
            salesSkuQuantity.setTotalSaleVolume(jsonObject.getInteger("totalSaleVolume"));
            salesSkuQuantity.setLastSevenDaysSaleVolume(jsonObject.getInteger("lastSevenDaysSaleVolume"));
            salesSkuQuantity.setLastThirtyDaysSaleVolume(jsonObject.getInteger("lastThirtyDaysSaleVolume"));
            
            // 处理购物车数量，API返回的字段是inCardNumber，实体类字段是inCartNumber
            Integer inCartNumber = jsonObject.getInteger("inCardNumber");
            salesSkuQuantity.setInCartNumber(inCartNumber);
            
            salesSkuQuantity.setInCartNumber7d(jsonObject.getInteger("inCartNumber7d"));
            salesSkuQuantity.setLackQuantity(jsonObject.getInteger("lackQuantity"));
            salesSkuQuantity.setAdviceQuantity(jsonObject.getInteger("adviceQuantity"));
            
            // 处理可用天数等字段，确保转换为字符串
            Double availableSaleDays = jsonObject.getDoubleValue("availableSaleDays");
            Double availableSaleDaysFromInventory = jsonObject.getDoubleValue("availableSaleDaysFromInventory");
            Double warehouseAvailableSaleDays = jsonObject.getDoubleValue("warehouseAvailableSaleDays");
            Double sevenDaysSaleReference = jsonObject.getDoubleValue("sevenDaysSaleReference");
            
            salesSkuQuantity.setAvailableSaleDays(String.valueOf(availableSaleDays));
            salesSkuQuantity.setAvailableSaleDaysFromInventory(String.valueOf(availableSaleDaysFromInventory));
            salesSkuQuantity.setWarehouseAvailableSaleDays(String.valueOf(warehouseAvailableSaleDays));
            salesSkuQuantity.setSevenDaysSaleReference(String.valueOf(sevenDaysSaleReference));
            salesSkuQuantity.setSevenDaysReferenceSaleType(jsonObject.getInteger("sevenDaysReferenceSaleType"));
            salesSkuQuantity.setSyncTime(LocalDateTime.now());
        } catch (Exception e) {
            log.error("解析销售SKU数量信息出错，SKU ID: {}, 错误: {}", 
                    jsonObject.getString("productSkuId"), e.getMessage(), e);
        }
        return salesSkuQuantity;
    }

    /**
     * 解析销售仓库信息
     */
    private SalesWarehouseInfo parseSalesWarehouseInfo(JSONObject jsonObject, Long shopId, Long productSkuId) {
        SalesWarehouseInfo salesWarehouseInfo = new SalesWarehouseInfo();
        salesWarehouseInfo.setShopId(shopId);
        salesWarehouseInfo.setProductSkuId(productSkuId);
        salesWarehouseInfo.setWarehouseGroupId(jsonObject.getLong("warehouseGroupId"));
        salesWarehouseInfo.setWarehouseGroupName(jsonObject.getString("warehouseGroupName"));
        salesWarehouseInfo.setStockDays(jsonObject.getInteger("stockDays"));
        salesWarehouseInfo.setSafeInventoryDays(jsonObject.getInteger("safeInventoryDays"));
        salesWarehouseInfo.setPurchaseConfig(jsonObject.getString("purchaseConfig"));
        salesWarehouseInfo.setTodaySaleVolume(jsonObject.getInteger("todaySaleVolume"));
        salesWarehouseInfo.setTotalSaleVolume(jsonObject.getInteger("totalSaleVolume"));
        salesWarehouseInfo.setLastSevenDaysSaleVolume(jsonObject.getInteger("lastSevenDaysSaleVolume"));
        salesWarehouseInfo.setLastThirtyDaysSaleVolume(jsonObject.getInteger("lastThirtyDaysSaleVolume"));
        salesWarehouseInfo.setAdviceQuantity(jsonObject.getInteger("adviceQuantity"));
        salesWarehouseInfo.setLackQuantity(jsonObject.getInteger("lackQuantity"));
        salesWarehouseInfo.setAvailableSaleDays((int) jsonObject.getDoubleValue("availableSaleDays"));
        salesWarehouseInfo.setAvailableSaleDaysFromInventory((int)jsonObject.getDoubleValue("availableSaleDaysFromInventory"));
        salesWarehouseInfo.setWarehouseAvailableSaleDays((int)jsonObject.getDoubleValue("warehouseAvailableSaleDays"));
        salesWarehouseInfo.setSevenDaysSaleReference((int)jsonObject.getDoubleValue("sevenDaysSaleReference"));
        salesWarehouseInfo.setSevenDaysReferenceSaleType(jsonObject.getInteger("sevenDaysReferenceSaleType"));
        salesWarehouseInfo.setSyncTime(LocalDateTime.now());
        
        return salesWarehouseInfo;
    }

    /**
     * 解析销售库存信息
     */
    private SalesInventoryInfo parseSalesInventoryInfo(JSONObject jsonObject, Long shopId, Long productSkuId, Long warehouseGroupId) {
        SalesInventoryInfo salesInventoryInfo = new SalesInventoryInfo();
        salesInventoryInfo.setShopId(shopId);
        salesInventoryInfo.setProductSkuId(productSkuId);
        salesInventoryInfo.setWarehouseGroupId(warehouseGroupId);
        salesInventoryInfo.setWarehouseInventoryNum(jsonObject.getInteger("warehouseInventoryNum"));
        salesInventoryInfo.setWaitOnShelfNum(jsonObject.getInteger("waitOnShelfNum"));
        salesInventoryInfo.setWaitDeliveryInventoryNum(jsonObject.getInteger("waitDeliveryInventoryNum"));
        salesInventoryInfo.setExpectedOccupiedInventoryNum(jsonObject.getInteger("expectedOccupiedInventoryNum"));
        salesInventoryInfo.setWaitApproveInventoryNum(jsonObject.getInteger("waitApproveInventoryNum"));
        salesInventoryInfo.setWaitQcNum(jsonObject.getInteger("waitQcNum"));
        salesInventoryInfo.setUnavailableWarehouseInventoryNum(jsonObject.getInteger("unavailableWarehouseInventoryNum"));
        salesInventoryInfo.setWaitInStock(jsonObject.getInteger("waitInStock"));
        salesInventoryInfo.setWaitReceiveNum(jsonObject.getInteger("waitReceiveNum"));
        salesInventoryInfo.setSyncTime(LocalDateTime.now());
        
        return salesInventoryInfo;
    }

    /**
     * 解析销售仓库库存信息
     */
    private SalesWarehouseInventory parseSalesWarehouseInventory(JSONObject jsonObject, Long shopId, Long productSkuId, Long warehouseGroupId) {
        SalesWarehouseInventory salesWarehouseInventory = new SalesWarehouseInventory();
        salesWarehouseInventory.setShopId(shopId);
        salesWarehouseInventory.setProductSkuId(productSkuId);
        salesWarehouseInventory.setWarehouseGroupId(warehouseGroupId);
        salesWarehouseInventory.setWaitOnShelfNum(jsonObject.getInteger("waitOnShelfNum"));
        salesWarehouseInventory.setWarehouseInventoryNum(jsonObject.getInteger("warehouseInventoryNum"));
        salesWarehouseInventory.setExpectedOccupiedInventoryNum(jsonObject.getInteger("expectedOccupiedInventoryNum"));
        salesWarehouseInventory.setWaitApproveInventoryNum(jsonObject.getInteger("waitApproveInventoryNum"));
        salesWarehouseInventory.setWaitQcNum(jsonObject.getInteger("waitQcNum"));
        salesWarehouseInventory.setUnavailableWarehouseInventoryNum(jsonObject.getInteger("unavailableWarehouseInventoryNum"));
        salesWarehouseInventory.setWaitInStock(jsonObject.getInteger("waitInStock"));
        salesWarehouseInventory.setWaitReceiveNum(jsonObject.getInteger("waitReceiveNum"));
        salesWarehouseInventory.setWaitDeliveryInventoryNum(jsonObject.getInteger("waitDeliveryInventoryNum"));
        salesWarehouseInventory.setSyncTime(LocalDateTime.now());
        
        return salesWarehouseInventory;
    }
    
    /**
     * 解析销售定制信息
     */
    private SalesCustomInfo parseSalesCustomInfo(JSONObject jsonObject, Long shopId, Long productSkcId) {
        SalesCustomInfo salesCustomInfo = new SalesCustomInfo();
        salesCustomInfo.setShopId(shopId);
        salesCustomInfo.setProductSkcId(productSkcId);
        salesCustomInfo.setIsCustomGoods(jsonObject.getBooleanValue("isCustomGoods"));
        salesCustomInfo.setLimitNum(jsonObject.getInteger("limitNum"));
        salesCustomInfo.setEffectPicture(jsonObject.getString("effectPicture"));
        salesCustomInfo.setSyncTime(LocalDateTime.now());
        
        return salesCustomInfo;
    }

    /**
     * 同步指定SKC ID列表的销售数据
     *
     * @param shopId 店铺ID
     * @param skcIds 需要同步的商品SKC ID集合
     * @return 同步的销售子订单列表
     */
    @Override
    public List<SalesSubOrder> syncSalesBySkcIds(Long shopId, Set<Long> skcIds) {
        // 根据SKC ID列表查询销售子订单
        List<SalesSubOrder> result = new ArrayList<>();
        if (skcIds == null || skcIds.isEmpty()) {
            return result;
        }

        // 同步全部销售数据 (使用已优化过的方法)
        ApiResponse.SalesSyncVO syncResult = syncSalesData(shopId);
        
        // 只有同步成功才查询订单
        if (syncResult.getSuccess()) {
            // 查询指定SKC ID的销售子订单
            for (Long skcId : skcIds) {
                LambdaQueryWrapper<SalesSubOrder> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(SalesSubOrder::getShopId, shopId)
                       .eq(SalesSubOrder::getProductSkcId, skcId);
                List<SalesSubOrder> orders = salesSubOrderMapper.selectList(wrapper);
                result.addAll(orders);
            }
        } else {
            log.error("同步销售数据失败，无法获取SKC ID列表对应的子订单，店铺ID: {}, 错误: {}", 
                    shopId, syncResult.getErrorMsg());
        }

        return result;
    }

    /**
     * 初始化店铺的同步任务
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    @Override
    public boolean initSyncTask(Long shopId) {
        Shop shop = shopService.getShopById(shopId).convertToShop();
        if (shop == null) {
            return false;
        }

        // 查询是否已存在
        SalesSyncTask task = salesSyncTaskMapper.findByShopId(shopId);
        if (task == null) {
            // 创建新任务
            task = new SalesSyncTask();
            task.setShopId(shopId);
            task.setSyncStatus(0);
            task.setTotalRecords(0);
            task.setSkuTotalRecords(0);
            task.setWarehouseTotalRecords(0);
            task.setCreateTime(LocalDateTime.now());
            return this.save(task);
        } else {
            // 重置任务状态
            task.setSyncStatus(0);
            task.setErrorMessage(null);
            task.setUpdateTime(LocalDateTime.now());
            return this.updateById(task);
        }
    }

    /**
     * 获取同步任务列表
     *
     * @param shopIds 店铺ID列表，为空则查询所有
     * @return 同步任务列表
     */
    @Override
    public List<SalesSyncTask> getSyncTasks(List<Long> shopIds) {
        if (shopIds == null || shopIds.isEmpty()) {
            return this.list();
        } else {
            return salesSyncTaskMapper.findByShopIds(shopIds);
        }
    }

    /**
     * 根据店铺ID获取同步任务
     *
     * @param shopId 店铺ID
     * @return 同步任务
     */
    @Override
    public SalesSyncTask getSyncTaskByShopId(Long shopId) {
        return salesSyncTaskMapper.findByShopId(shopId);
    }

    /**
     * 执行定时同步
     *
     * @return 同步结果
     */
    @Override
    public String executeScheduledSync() {
        log.info("开始执行销售数据定时同步任务");
        StringBuilder result = new StringBuilder();

        // 获取所有店铺
        List<Shop> shops = shopService.listAllShops();
        if (shops == null || shops.isEmpty()) {
            return "没有可同步的店铺";
        }

        int successCount = 0;
        int failCount = 0;
        List<String> failShops = new ArrayList<>();

        for (Shop shop : shops) {
            try {
                // 检查店铺状态
                if (!"0".equals(shop.getStatus())) {
                    log.info("店铺已禁用，跳过同步: {}", shop.getShopName());
                    continue;
                }

                // 同步店铺数据
                ApiResponse.SalesSyncVO syncResult = syncSalesData(shop.getShopId());
                if (syncResult.getSuccess()) {
                    successCount++;
                } else {
                    failCount++;
                    failShops.add(shop.getShopName() + "(" + syncResult.getErrorMsg() + ")");
                }
            } catch (Exception e) {
                log.error("店铺同步出错: " + shop.getShopName(), e);
                failCount++;
                failShops.add(shop.getShopName() + "(" + e.getMessage() + ")");
            }
        }

        // 构建结果消息
        result.append("销售数据同步完成，成功: ").append(successCount).append("，失败: ").append(failCount);
        if (!failShops.isEmpty()) {
            result.append("，失败店铺: ").append(String.join(", ", failShops));
        }

        log.info(result.toString());
        return result.toString();
    }

    /**
     * 获取指定店铺的销售子订单总数
     *
     * @param shopId 店铺ID
     * @return 销售子订单总数
     */
    @Override
    public Integer getSalesSubOrderCountByShopId(Long shopId) {
        return salesSubOrderMapper.countByShopId(shopId);
    }

    /**
     * 获取指定店铺的销售SKU总数
     *
     * @param shopId 店铺ID
     * @return 销售SKU总数
     */
    @Override
    public Integer getSalesSkuCountByShopId(Long shopId) {
        return salesSkuQuantityMapper.countByShopId(shopId);
    }

    /**
     * 获取指定店铺的仓库信息总数
     *
     * @param shopId 店铺ID
     * @return 仓库信息总数
     */
    @Override
    public Integer getWarehouseInfoCountByShopId(Long shopId) {
        return salesWarehouseInfoMapper.countByShopId(shopId);
    }

    /**
     * 清空店铺的销售同步数据
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearSyncData(Long shopId) {
        try {
            // 删除销售子订单
            LambdaQueryWrapper<SalesSubOrder> subOrderWrapper = new LambdaQueryWrapper<>();
            subOrderWrapper.eq(SalesSubOrder::getShopId, shopId);
            salesSubOrderMapper.delete(subOrderWrapper);

            // 删除销售SKU数量
            LambdaQueryWrapper<SalesSkuQuantity> skuQuantityWrapper = new LambdaQueryWrapper<>();
            skuQuantityWrapper.eq(SalesSkuQuantity::getShopId, shopId);
            salesSkuQuantityMapper.delete(skuQuantityWrapper);

            // 删除销售仓库信息
            LambdaQueryWrapper<SalesWarehouseInfo> warehouseInfoWrapper = new LambdaQueryWrapper<>();
            warehouseInfoWrapper.eq(SalesWarehouseInfo::getShopId, shopId);
            salesWarehouseInfoMapper.delete(warehouseInfoWrapper);

            // 删除销售库存信息
            LambdaQueryWrapper<SalesInventoryInfo> inventoryInfoWrapper = new LambdaQueryWrapper<>();
            inventoryInfoWrapper.eq(SalesInventoryInfo::getShopId, shopId);
            salesInventoryInfoMapper.delete(inventoryInfoWrapper);
            
            // 删除销售定制信息
            LambdaQueryWrapper<SalesCustomInfo> customInfoWrapper = new LambdaQueryWrapper<>();
            customInfoWrapper.eq(SalesCustomInfo::getShopId, shopId);
            salesCustomInfoMapper.delete(customInfoWrapper);
            
            // 删除销售仓库库存详情
            LambdaQueryWrapper<SalesWarehouseInventory> warehouseInventoryWrapper = new LambdaQueryWrapper<>();
            warehouseInventoryWrapper.eq(SalesWarehouseInventory::getShopId, shopId);
            salesWarehouseInventoryMapper.delete(warehouseInventoryWrapper);

            // 重置同步任务状态
            SalesSyncTask task = salesSyncTaskMapper.findByShopId(shopId);
            if (task != null) {
                task.setSyncStatus(0);
                task.setTotalRecords(0);
                task.setSkuTotalRecords(0);
                task.setWarehouseTotalRecords(0);
                task.setErrorMessage(null);
                task.setLastUpdateTime(LocalDateTime.now());
                this.updateById(task);
            }

            return true;
        } catch (Exception e) {
            log.error("清空销售同步数据出错，店铺ID: " + shopId, e);
            throw e;
        }
    }

    /**
     * 执行销售数据全量同步
     *
     * @param shopId 店铺ID
     * @param limit 最大同步数量限制
     * @return 是否同步成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED)
    public boolean syncSalesDataFull(Long shopId, int limit) {
        log.info("开始执行销售数据全量同步，店铺ID: {}, 最大同步数量: {}", shopId, limit);
        
        try {
            // 获取店铺信息
            Shop shop = shopService.getShopById(shopId).convertToShop();
            if (shop == null) {
                log.error("店铺不存在，店铺ID: {}", shopId);
                return false;
            }
            
            // 获取同步任务
            SalesSyncTask task = salesSyncTaskMapper.findByShopId(shopId);
            if (task == null) {
                // 如果不存在则创建
                boolean initResult = initSyncTask(shopId);
                if (!initResult) {
                    log.error("初始化同步任务失败，店铺ID: {}", shopId);
                    return false;
                }
                task = salesSyncTaskMapper.findByShopId(shopId);
            }
            
            // 检查当前同步状态
            if (task.getSyncStatus() == 1) {
                log.warn("该店铺正在同步中，跳过本次全量同步，店铺ID: {}", shopId);
                return false;
            }
            
            // 更新任务状态为同步中
            task.setSyncStatus(1);
            task.setLastSyncTime(LocalDateTime.now());
            this.updateById(task);
            
            // 准备API请求参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.goods.salesv2.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());
            
            // 设置计数器
            AtomicInteger totalCounter = new AtomicInteger(0);
            AtomicInteger skuCounter = new AtomicInteger(0);
            AtomicInteger warehouseCounter = new AtomicInteger(0);
            
            // 设置分页参数
            int pageNo = 1;
            int pageSize = 100; // API最大支持100条每页
            boolean hasMore = true;
            
            // 计算最多需要查询的页数以满足限制
            int maxPages = (int) Math.ceil((double) limit / pageSize);
            
            while (hasMore && pageNo <= maxPages) {
                // 构建业务参数
                HashMap<String, Object> businessParams = new HashMap<>();
                businessParams.put("timestamp", String.valueOf(Time.time()));
                businessParams.put("pageNo", String.valueOf(pageNo));
                businessParams.put("pageSize", String.valueOf(pageSize));
                
                // 调用API - 使用限流工具
                log.info("调用销售数据API，店铺ID: {}, 页码: {}, 每页条数: {}", shopId, pageNo, pageSize);
                JSONObject response = apiRateLimiter.sendRequestWithRateLimit(
                        shopId, commonParams, businessParams, "全量同步页码" + pageNo);
                        
                if (response == null) {
                    log.error("API调用返回为空，店铺ID: {}", shopId);
                    throw new RuntimeException("API调用返回为空");
                }
                
                // 检查API返回状态
                Boolean success = response.getBoolean("success");
                if (success == null || !success) {
                    String errorMsg = response.getString("errorMsg");
                    log.error("API调用失败，店铺ID: {}, 错误信息: {}", shopId, errorMsg);
                    throw new RuntimeException("API调用失败: " + errorMsg);
                }
                
                JSONObject result = response.getJSONObject("result");
                if (result == null) {
                    log.error("API调用响应数据格式错误，店铺ID: {}", shopId);
                    throw new RuntimeException("API调用响应数据格式错误");
                }
                
                // 处理销售数据
                processSalesData(result, shopId, totalCounter, skuCounter, warehouseCounter);
                
                // 检查是否达到限制或是否有更多页
                Integer total = result.getInteger("total");
                if (total == null || pageNo * pageSize >= total || totalCounter.get() >= limit) {
                    hasMore = false;
                } else {
                    pageNo++;
                }
            }
            
            // 更新同步任务状态为成功
            task.setSyncStatus(2);
            task.setTotalRecords(totalCounter.get());
            task.setSkuTotalRecords(skuCounter.get());
            task.setWarehouseTotalRecords(warehouseCounter.get());
            task.setLastUpdateTime(LocalDateTime.now());
            this.updateById(task);
            
            log.info("销售数据全量同步完成，店铺ID: {}, 同步数量: {}, SKU数量: {}, 仓库信息数量: {}", 
                    shopId, totalCounter.get(), skuCounter.get(), warehouseCounter.get());
            return true;
            
        } catch (Exception e) {
            log.error("销售数据全量同步异常，店铺ID: {}, 错误: {}", shopId, e.getMessage(), e);
            
            // 更新任务状态为失败
            try {
                SalesSyncTask task = salesSyncTaskMapper.findByShopId(shopId);
                if (task != null && task.getSyncStatus() == 1) {
                    task.setSyncStatus(3); // 3表示同步失败
                    task.setLastUpdateTime(LocalDateTime.now());
                    this.updateById(task);
                }
            } catch (Exception ex) {
                log.error("更新同步任务状态失败，店铺ID: {}, 错误: {}", shopId, ex.getMessage(), ex);
            }
            
            return false;
        }
    }
    
    /**
     * 执行销售数据增量同步
     *
     * @param shopId 店铺ID
     * @return 是否同步成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED)
    public boolean syncSalesDataIncremental(Long shopId) {
        log.info("开始执行销售数据增量同步，店铺ID: {}", shopId);
        
        try {
            // 获取店铺信息
            Shop shop = shopService.getShopById(shopId).convertToShop();
            if (shop == null) {
                log.error("店铺不存在，店铺ID: {}", shopId);
                return false;
            }
            
            // 获取同步任务
            SalesSyncTask task = salesSyncTaskMapper.findByShopId(shopId);
            if (task == null) {
                // 如果不存在则创建
                boolean initResult = initSyncTask(shopId);
                if (!initResult) {
                    log.error("初始化同步任务失败，店铺ID: {}", shopId);
                    return false;
                }
                task = salesSyncTaskMapper.findByShopId(shopId);
            }
            
            // 检查当前同步状态
            if (task.getSyncStatus() == 1) {
                log.warn("该店铺正在同步中，跳过本次增量同步，店铺ID: {}", shopId);
                return false;
            }
            
            // 更新任务状态为同步中
            task.setSyncStatus(1);
            task.setLastSyncTime(LocalDateTime.now());
            this.updateById(task);
            
            // 准备API请求参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.goods.salesv2.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());
            
            // 获取上次更新时间，用于增量同步
            LocalDateTime lastUpdateTime = task.getLastUpdateTime();
            String updateTimeStr = null;
            if (lastUpdateTime != null) {
                updateTimeStr = lastUpdateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            
            // 设置计数器
            AtomicInteger totalCounter = new AtomicInteger(0);
            AtomicInteger skuCounter = new AtomicInteger(0);
            AtomicInteger warehouseCounter = new AtomicInteger(0);
            
            // 设置分页参数
            int pageNo = 1;
            int pageSize = 100; // API最大支持100条每页
            boolean hasMore = true;
            
            while (hasMore) {
                // 构建业务参数
                HashMap<String, Object> businessParams = new HashMap<>();
                businessParams.put("timestamp", String.valueOf(Time.time()));
                businessParams.put("pageNo", String.valueOf(pageNo));
                businessParams.put("pageSize", String.valueOf(pageSize));
                
                // 增量同步需要传入上次更新时间
                if (updateTimeStr != null) {
                    businessParams.put("updateAfter", updateTimeStr);
                }
                
                // 调用API - 使用限流工具
                log.info("调用销售数据API，店铺ID: {}, 页码: {}, 每页条数: {}, 更新时间后: {}", 
                        shopId, pageNo, pageSize, updateTimeStr);
                JSONObject response = apiRateLimiter.sendRequestWithRateLimit(
                        shopId, commonParams, businessParams, "增量同步页码" + pageNo);
                        
                if (response == null) {
                    log.error("API调用返回为空，店铺ID: {}", shopId);
                    throw new RuntimeException("API调用返回为空");
                }
                
                // 检查API返回状态
                Boolean success = response.getBoolean("success");
                if (success == null || !success) {
                    String errorMsg = response.getString("errorMsg");
                    log.error("API调用失败，店铺ID: {}, 错误信息: {}", shopId, errorMsg);
                    throw new RuntimeException("API调用失败: " + errorMsg);
                }
                
                JSONObject result = response.getJSONObject("result");
                if (result == null) {
                    log.error("API调用响应数据格式错误，店铺ID: {}", shopId);
                    throw new RuntimeException("API调用响应数据格式错误");
                }
                
                // 处理销售数据
                processSalesData(result, shopId, totalCounter, skuCounter, warehouseCounter);
                
                // 检查是否有更多页
                Integer total = result.getInteger("total");
                if (total == null || pageNo * pageSize >= total) {
                    hasMore = false;
                } else {
                    pageNo++;
                }
            }
            
            // 更新同步任务状态为成功
            task.setSyncStatus(2);
            task.setTotalRecords(totalCounter.get() + (task.getTotalRecords() != null ? task.getTotalRecords() : 0));
            task.setSkuTotalRecords(skuCounter.get() + (task.getSkuTotalRecords() != null ? task.getSkuTotalRecords() : 0));
            task.setWarehouseTotalRecords(warehouseCounter.get() + (task.getWarehouseTotalRecords() != null ? task.getWarehouseTotalRecords() : 0));
            task.setLastUpdateTime(LocalDateTime.now());
            this.updateById(task);
            
            log.info("销售数据增量同步完成，店铺ID: {}, 同步数量: {}, SKU数量: {}, 仓库信息数量: {}", 
                    shopId, totalCounter.get(), skuCounter.get(), warehouseCounter.get());
            return true;
            
        } catch (Exception e) {
            log.error("销售数据增量同步异常，店铺ID: {}, 错误: {}", shopId, e.getMessage(), e);
            
            // 更新任务状态为失败
            try {
                SalesSyncTask task = salesSyncTaskMapper.findByShopId(shopId);
                if (task != null && task.getSyncStatus() == 1) {
                    task.setSyncStatus(3); // 3表示同步失败
                    task.setLastUpdateTime(LocalDateTime.now());
                    this.updateById(task);
                }
            } catch (Exception ex) {
                log.error("更新同步任务状态失败，店铺ID: {}, 错误: {}", shopId, ex.getMessage(), ex);
            }
            
            return false;
        }
    }

    /**
     * 使用多线程执行销售数据全量同步
     *
     * @param shopId 店铺ID
     * @param limit 最大同步数量限制
     * @param threadCount 线程数量,默认为配置文件中的设置
     * @return 同步结果
     */
    @Override
    public ApiResponse.SalesSyncVO syncSalesDataFullAsync(Long shopId, int limit, Integer threadCount) {
        log.info("开始多线程执行销售数据全量同步，店铺ID: {}, 最大同步数量: {}, 线程数: {}", 
                shopId, limit, threadCount != null ? threadCount : "默认");
                
        ApiResponse.SalesSyncVO result = new ApiResponse.SalesSyncVO();
        result.setShopId(shopId);
        
        try {
            // 获取店铺信息
            Shop shop = shopService.getShopById(shopId).convertToShop();
            if (shop == null) {
                result.setSuccess(false);
                result.setErrorCode(404);
                result.setErrorMsg("店铺不存在");
                return result;
            }
            result.setShopName(shop.getShopName());
            
            // 获取同步任务
            SalesSyncTask task = salesSyncTaskMapper.findByShopId(shopId);
            if (task == null) {
                // 如果不存在则创建
                boolean initResult = initSyncTask(shopId);
                if (!initResult) {
                    result.setSuccess(false);
                    result.setErrorCode(500);
                    result.setErrorMsg("初始化同步任务失败");
                    return result;
                }
                task = salesSyncTaskMapper.findByShopId(shopId);
            }
            
            // 检查当前同步状态
            if (task.getSyncStatus() == 1) {
                result.setSuccess(false);
                result.setErrorCode(400);
                result.setErrorMsg("该店铺正在同步中，请稍后再试");
                result.setSyncStatus(1);
                return result;
            }
            
            // 更新任务状态为同步中
            task.setSyncStatus(1);
            task.setLastSyncTime(LocalDateTime.now());
            this.updateById(task);
            
            // 准备API请求参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.goods.salesv2.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());
            
            // 设置计数器
            AtomicInteger totalCounter = new AtomicInteger(0);
            AtomicInteger skuCounter = new AtomicInteger(0);
            AtomicInteger warehouseCounter = new AtomicInteger(0);
            
            // 首先查询总记录数
            HashMap<String, Object> totalQueryParams = new HashMap<>();
            totalQueryParams.put("timestamp", String.valueOf(Time.time()));
            totalQueryParams.put("pageNo", "1");
            totalQueryParams.put("pageSize", "1");
            
            // 使用限流工具调用API
            JSONObject response = apiRateLimiter.sendRequestWithRateLimit(
                    shopId, commonParams, totalQueryParams, "查询销售数据总数");
            
            if (response == null || !response.getBooleanValue("success")) {
                String errorMsg = response != null ? response.getString("errorMsg") : "API调用返回为空";
                throw new RuntimeException("API调用失败: " + errorMsg);
            }
            
            JSONObject totalResult = response.getJSONObject("result");
            Integer total = totalResult != null ? totalResult.getInteger("total") : 0;
            
            if (total == null || total == 0) {
                log.info("店铺 {} 没有销售数据", shopId);
                
                // 更新同步任务状态为成功但无数据
                task.setSyncStatus(2);
                task.setTotalRecords(0);
                task.setSkuTotalRecords(0);
                task.setWarehouseTotalRecords(0);
                task.setLastUpdateTime(LocalDateTime.now());
                this.updateById(task);
                
                result.setSuccess(true);
                result.setSyncStatus(2);
                result.setMessage("同步成功，但该店铺没有销售数据");
                return result;
            }
            
            // 限制同步数量
            int actualTotal = Math.min(total, limit);
            
            // 计算需要的页数
            int pageSize = 100; // API最大支持100条每页
            int totalPages = (actualTotal + pageSize - 1) / pageSize; // 向上取整
            
            // 确定实际使用的线程数
            int actualThreadCount;
            if (threadCount != null && threadCount > 0) {
                actualThreadCount = Math.min(threadCount, totalPages); // 不超过页数
            } else {
                actualThreadCount = Math.min(salesSyncExecutor.getMaxPoolSize(), totalPages);
            }
            
            // 每个线程处理的页数
            int pagesPerThread = totalPages / actualThreadCount;
            int remainingPages = totalPages % actualThreadCount;
            
            log.info("店铺 {} 销售数据总记录数: {}, 限制: {}, 总页数: {}, 使用线程数: {}, 每线程页数: {}, 剩余页数: {}", 
                    shopId, total, limit, totalPages, actualThreadCount, pagesPerThread, remainingPages);
            
            // 创建多线程任务
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            
            for (int i = 0; i < actualThreadCount; i++) {
                int startPage = i * pagesPerThread + 1;
                int endPage = (i + 1) * pagesPerThread;
                
                // 最后一个线程处理剩余页
                if (i == actualThreadCount - 1) {
                    endPage += remainingPages;
                }
                
                // 每个线程处理的页码范围
                final int finalStartPage = startPage;
                final int finalEndPage = endPage;

                int finalI = i;
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        Thread.currentThread().setName("sales-sync-" + (finalI + 1));
                        log.info("线程开始处理店铺 {} 的销售数据, 页码范围: {}-{}", shopId, finalStartPage, finalEndPage);
                        
                        for (int pageNo = finalStartPage; pageNo <= finalEndPage; pageNo++) {
                            try {
                                // 构建业务参数
                                HashMap<String, Object> businessParams = new HashMap<>();
                                businessParams.put("timestamp", String.valueOf(Time.time()));
                                businessParams.put("pageNo", String.valueOf(pageNo));
                                businessParams.put("pageSize", String.valueOf(pageSize));
                                
                                // 调用API - 使用限流工具类替代直接调用
                                log.info("线程调用销售数据API，店铺ID: {}, 页码: {}/{}, 每页条数: {}", 
                                        shopId, pageNo, finalEndPage, pageSize);
                                        
                                // 使用限流工具调用API
                                JSONObject pageResponse = apiRateLimiter.sendRequestWithRateLimit(
                                        shopId, commonParams, businessParams, 
                                        "全量同步页码" + pageNo + "/" + finalEndPage);
                                
                                if (pageResponse == null) {
                                    log.error("API调用返回为空，店铺ID: {}, 页码: {}", shopId, pageNo);
                                    continue;
                                }
                                
                                // 检查API返回状态
                                Boolean success = pageResponse.getBoolean("success");
                                if (success == null || !success) {
                                    String errorMsg = pageResponse.getString("errorMsg");
                                    log.error("API调用失败，店铺ID: {}, 页码: {}, 错误信息: {}", shopId, pageNo, errorMsg);
                                    continue;
                                }
                                
                                JSONObject pageResult = pageResponse.getJSONObject("result");
                                if (pageResult == null) {
                                    log.error("API调用响应数据格式错误，店铺ID: {}, 页码: {}", shopId, pageNo);
                                    continue;
                                }
                                
                                // 处理销售数据
                                processSalesData(pageResult, shopId, totalCounter, skuCounter, warehouseCounter);
                                
                                // 每处理完一页，记录当前进度（修改为每10页或处理量增加100条记录一次）
                                if (pageNo % 10 == 0 || totalCounter.get() % 100 == 0) {
                                    log.info("店铺 {} 处理进度: 页码:{}/{}, 总记录数: {}, SKU数: {}, 仓库信息数: {}", 
                                            shopId, pageNo, finalEndPage, totalCounter.get(), skuCounter.get(), warehouseCounter.get());
                                }
                                
                                // 如果已达到限制数量，提前结束
                                if (totalCounter.get() >= limit) {
                                    log.info("店铺 {} 已达到同步数量限制 {}, 提前结束同步", shopId, limit);
                                    break;
                                }
                            } catch (Exception e) {
                                log.error("处理店铺 {} 第 {} 页数据异常: {}", shopId, pageNo, e.getMessage(), e);
                            }
                        }
                        
                        log.info("线程完成处理店铺 {} 的销售数据, 页码范围: {}-{}", shopId, finalStartPage, finalEndPage);
                    } catch (Exception e) {
                        log.error("线程处理店铺 {} 销售数据异常: {}", shopId, e.getMessage(), e);
                    }
                }, salesSyncExecutor);
                
                futures.add(future);
            }
            
            // 等待所有线程完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allFutures.join();
            
            // 更新同步任务状态为成功
            task.setSyncStatus(2);
            task.setTotalRecords(totalCounter.get());
            task.setSkuTotalRecords(skuCounter.get());
            task.setWarehouseTotalRecords(warehouseCounter.get());
            task.setLastUpdateTime(LocalDateTime.now());
            this.updateById(task);
            
            // 设置返回结果
            result.setSuccess(true);
            result.setSyncStatus(2);
            result.setTotalRecords(totalCounter.get());
            result.setSkuTotalRecords(skuCounter.get());
            result.setWarehouseTotalRecords(warehouseCounter.get());
            result.setLastSyncTime(task.getLastSyncTime());
            result.setLastUpdateTime(task.getLastUpdateTime());
            result.setMessage("多线程同步成功，共同步" + totalCounter.get() + "条数据，包含" + skuCounter.get() + 
                    "个SKU，" + warehouseCounter.get() + "条仓库信息");
            
            log.info("店铺 {} 多线程全量同步完成，总记录数: {}, SKU数: {}, 仓库信息数: {}", 
                    shopId, totalCounter.get(), skuCounter.get(), warehouseCounter.get());
            
        } catch (Exception e) {
            log.error("店铺 {} 多线程全量同步异常: {}", shopId, e.getMessage(), e);
            
            // 更新同步任务状态为失败
            try {
                SalesSyncTask task = salesSyncTaskMapper.findByShopId(shopId);
                if (task != null && task.getSyncStatus() == 1) {
                    task.setSyncStatus(3); // 3表示同步失败
                    task.setErrorMessage(e.getMessage());
                    task.setLastUpdateTime(LocalDateTime.now());
                    this.updateById(task);
                }
            } catch (Exception ex) {
                log.error("更新同步任务状态失败: {}", ex.getMessage(), ex);
            }
            
            result.setSuccess(false);
            result.setSyncStatus(3);
            result.setErrorCode(500);
            result.setErrorMsg("同步失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 使用多线程执行销售数据增量同步
     *
     * @param shopId 店铺ID
     * @param threadCount 线程数量,默认为配置文件中的设置
     * @return 同步结果
     */
    @Override
    public ApiResponse.SalesSyncVO syncSalesDataIncrementalAsync(Long shopId, Integer threadCount) {
        log.info("开始多线程执行销售数据增量同步，店铺ID: {}, 线程数: {}", 
                shopId, threadCount != null ? threadCount : "默认");
                
        ApiResponse.SalesSyncVO result = new ApiResponse.SalesSyncVO();
        result.setShopId(shopId);
        
        try {
            // 获取店铺信息
            Shop shop = shopService.getShopById(shopId).convertToShop();
            if (shop == null) {
                result.setSuccess(false);
                result.setErrorCode(404);
                result.setErrorMsg("店铺不存在");
                return result;
            }
            result.setShopName(shop.getShopName());
            
            // 获取同步任务
            SalesSyncTask task = salesSyncTaskMapper.findByShopId(shopId);
            if (task == null) {
                // 如果不存在则创建
                boolean initResult = initSyncTask(shopId);
                if (!initResult) {
                    result.setSuccess(false);
                    result.setErrorCode(500);
                    result.setErrorMsg("初始化同步任务失败");
                    return result;
                }
                task = salesSyncTaskMapper.findByShopId(shopId);
            }
            
            // 检查当前同步状态
            if (task.getSyncStatus() == 1) {
                result.setSuccess(false);
                result.setErrorCode(400);
                result.setErrorMsg("该店铺正在同步中，请稍后再试");
                result.setSyncStatus(1);
                return result;
            }
            
            // 更新任务状态为同步中
            task.setSyncStatus(1);
            task.setLastSyncTime(LocalDateTime.now());
            this.updateById(task);
            
            // 准备API请求参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.goods.salesv2.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());
            
            // 获取上次更新时间，用于增量同步
            LocalDateTime lastUpdateTime = task.getLastUpdateTime();
            String updateTimeStr = null;
            if (lastUpdateTime != null) {
                updateTimeStr = lastUpdateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            
            // 设置计数器
            AtomicInteger totalCounter = new AtomicInteger(0);
            AtomicInteger skuCounter = new AtomicInteger(0);
            AtomicInteger warehouseCounter = new AtomicInteger(0);
            
            // 首先查询增量数据总记录数
            HashMap<String, Object> totalQueryParams = new HashMap<>();
            totalQueryParams.put("timestamp", String.valueOf(Time.time()));
            totalQueryParams.put("pageNo", "1");
            totalQueryParams.put("pageSize", "1");
            if (updateTimeStr != null) {
                totalQueryParams.put("updateAfter", updateTimeStr);
            }
            
            // 使用限流工具调用API
            JSONObject response = apiRateLimiter.sendRequestWithRateLimit(
                    shopId, commonParams, totalQueryParams, "查询增量销售数据总数");
            
            if (response == null || !response.getBooleanValue("success")) {
                String errorMsg = response != null ? response.getString("errorMsg") : "API调用返回为空";
                throw new RuntimeException("API调用失败: " + errorMsg);
            }
            
            JSONObject totalResult = response.getJSONObject("result");
            Integer total = totalResult != null ? totalResult.getInteger("total") : 0;
            
            if (total == null || total == 0) {
                log.info("店铺 {} 没有需要增量同步的销售数据", shopId);
                
                // 更新同步任务状态为成功但无新数据
                task.setSyncStatus(2);
                task.setLastUpdateTime(LocalDateTime.now());
                this.updateById(task);
                
                result.setSuccess(true);
                result.setSyncStatus(2);
                result.setMessage("增量同步成功，但没有新的销售数据");
                return result;
            }
            
            // 计算需要的页数
            int pageSize = 100; // API最大支持100条每页
            int totalPages = (total + pageSize - 1) / pageSize; // 向上取整
            
            // 确定实际使用的线程数
            int actualThreadCount;
            if (threadCount != null && threadCount > 0) {
                actualThreadCount = Math.min(threadCount, totalPages); // 不超过页数
            } else {
                actualThreadCount = Math.min(salesSyncExecutor.getMaxPoolSize(), totalPages);
            }
            
            // 每个线程处理的页数
            int pagesPerThread = totalPages / actualThreadCount;
            int remainingPages = totalPages % actualThreadCount;
            
            log.info("店铺 {} 增量销售数据总记录数: {}, 总页数: {}, 使用线程数: {}, 每线程页数: {}, 剩余页数: {}", 
                    shopId, total, totalPages, actualThreadCount, pagesPerThread, remainingPages);
            
            // 创建多线程任务
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            
            for (int i = 0; i < actualThreadCount; i++) {
                int startPage = i * pagesPerThread + 1;
                int endPage = (i + 1) * pagesPerThread;
                
                // 最后一个线程处理剩余页
                if (i == actualThreadCount - 1) {
                    endPage += remainingPages;
                }
                
                // 每个线程处理的页码范围
                final int finalStartPage = startPage;
                final int finalEndPage = endPage;
                final String finalUpdateTimeStr = updateTimeStr;

                int finalI = i;
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        Thread.currentThread().setName("sales-sync-" + (finalI + 1));
                        log.info("线程开始处理店铺 {} 的增量销售数据, 页码范围: {}-{}, 更新时间: {}", 
                                shopId, finalStartPage, finalEndPage, finalUpdateTimeStr);
                        
                        for (int pageNo = finalStartPage; pageNo <= finalEndPage; pageNo++) {
                            try {
                                // 构建业务参数
                                HashMap<String, Object> businessParams = new HashMap<>();
                                businessParams.put("timestamp", String.valueOf(Time.time()));
                                businessParams.put("pageNo", String.valueOf(pageNo));
                                businessParams.put("pageSize", String.valueOf(pageSize));
                                
                                // 增量同步需要传入上次更新时间
                                if (finalUpdateTimeStr != null) {
                                    businessParams.put("updateAfter", finalUpdateTimeStr);
                                }
                                
                                // 调用API - 使用限流工具类
                                log.info("线程调用增量销售数据API，店铺ID: {}, 页码: {}/{}, 每页条数: {}, 更新时间: {}", 
                                        shopId, pageNo, finalEndPage, pageSize, finalUpdateTimeStr);
                                        
                                // 使用限流工具调用API
                                JSONObject pageResponse = apiRateLimiter.sendRequestWithRateLimit(
                                        shopId, commonParams, businessParams, 
                                        "增量同步页码" + pageNo + "/" + finalEndPage);
                                
                                if (pageResponse == null) {
                                    log.error("API调用返回为空，店铺ID: {}, 页码: {}", shopId, pageNo);
                                    continue;
                                }
                                
                                // 检查API返回状态
                                Boolean success = pageResponse.getBoolean("success");
                                if (success == null || !success) {
                                    String errorMsg = pageResponse.getString("errorMsg");
                                    log.error("API调用失败，店铺ID: {}, 页码: {}, 错误信息: {}", shopId, pageNo, errorMsg);
                                    continue;
                                }
                                
                                JSONObject pageResult = pageResponse.getJSONObject("result");
                                if (pageResult == null) {
                                    log.error("API调用响应数据格式错误，店铺ID: {}, 页码: {}", shopId, pageNo);
                                    continue;
                                }
                                
                                // 处理销售数据
                                processSalesData(pageResult, shopId, totalCounter, skuCounter, warehouseCounter);
                                
                                // 每处理完一页，记录当前进度（修改为每10页或处理量增加100条记录一次）
                                if (pageNo % 10 == 0 || totalCounter.get() % 100 == 0) {
                                    log.info("店铺 {} 增量处理进度: 页码:{}/{}, 总记录数: {}, SKU数: {}, 仓库信息数: {}", 
                                            shopId, pageNo, finalEndPage, totalCounter.get(), skuCounter.get(), warehouseCounter.get());
                                }
                            } catch (Exception e) {
                                log.error("处理店铺 {} 第 {} 页增量数据异常: {}", shopId, pageNo, e.getMessage(), e);
                            }
                        }
                        
                        log.info("线程完成处理店铺 {} 的增量销售数据, 页码范围: {}-{}", shopId, finalStartPage, finalEndPage);
                    } catch (Exception e) {
                        log.error("线程处理店铺 {} 增量销售数据异常: {}", shopId, e.getMessage(), e);
                    }
                }, salesSyncExecutor);
                
                futures.add(future);
            }
            
            // 等待所有线程完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allFutures.join();
            
            // 更新同步任务状态为成功
            task.setSyncStatus(2);
            task.setTotalRecords(totalCounter.get() + (task.getTotalRecords() != null ? task.getTotalRecords() : 0));
            task.setSkuTotalRecords(skuCounter.get() + (task.getSkuTotalRecords() != null ? task.getSkuTotalRecords() : 0));
            task.setWarehouseTotalRecords(warehouseCounter.get() + (task.getWarehouseTotalRecords() != null ? task.getWarehouseTotalRecords() : 0));
            task.setLastUpdateTime(LocalDateTime.now());
            this.updateById(task);
            
            // 设置返回结果
            result.setSuccess(true);
            result.setSyncStatus(2);
            result.setTotalRecords(task.getTotalRecords());
            result.setSkuTotalRecords(task.getSkuTotalRecords());
            result.setWarehouseTotalRecords(task.getWarehouseTotalRecords());
            result.setLastSyncTime(task.getLastSyncTime());
            result.setLastUpdateTime(task.getLastUpdateTime());
            result.setMessage("多线程增量同步成功，新增" + totalCounter.get() + "条数据，包含" + skuCounter.get() + 
                    "个SKU，" + warehouseCounter.get() + "条仓库信息");
            
            log.info("店铺 {} 多线程增量同步完成，新增记录数: {}, SKU数: {}, 仓库信息数: {}", 
                    shopId, totalCounter.get(), skuCounter.get(), warehouseCounter.get());
            
        } catch (Exception e) {
            log.error("店铺 {} 多线程增量同步异常: {}", shopId, e.getMessage(), e);
            
            // 更新同步任务状态为失败
            try {
                SalesSyncTask task = salesSyncTaskMapper.findByShopId(shopId);
                if (task != null && task.getSyncStatus() == 1) {
                    task.setSyncStatus(3); // 3表示同步失败
                    task.setErrorMessage(e.getMessage());
                    task.setLastUpdateTime(LocalDateTime.now());
                    this.updateById(task);
                }
            } catch (Exception ex) {
                log.error("更新同步任务状态失败: {}", ex.getMessage(), ex);
            }
            
            result.setSuccess(false);
            result.setSyncStatus(3);
            result.setErrorCode(500);
            result.setErrorMsg("同步失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 批量多线程同步多个店铺的销售数据
     *
     * @param shopIds 店铺ID列表
     * @param isFullSync 是否全量同步,true为全量,false为增量
     * @param limit 全量同步时的最大同步数量限制,增量同步时忽略
     * @param threadCount 线程数量,默认为配置文件中的设置
     * @return 同步结果
     */
    @Override
    public String batchSyncSalesDataAsync(List<Long> shopIds, boolean isFullSync, int limit, Integer threadCount) {
        if (shopIds == null || shopIds.isEmpty()) {
            return "没有指定要同步的店铺";
        }
        
        log.info("开始批量{}同步多个店铺的销售数据，店铺数量: {}, 线程数: {}", 
                isFullSync ? "全量" : "增量", shopIds.size(), threadCount != null ? threadCount : "默认");
        
        int successCount = 0;
        int failCount = 0;
        List<String> failShops = new ArrayList<>();
        
        for (Long shopId : shopIds) {
            try {
                // 获取店铺信息
                Shop shop = shopService.getShopById(shopId).convertToShop();
                if (shop == null) {
                    log.warn("店铺不存在，跳过同步: {}", shopId);
                    failCount++;
                    failShops.add("店铺ID: " + shopId + " (不存在)");
                    continue;
                }
                
                if (!"0".equals(shop.getStatus())) {
                    log.info("店铺已禁用，跳过同步: {}", shop.getShopName());
                    continue;
                }
                
                ApiResponse.SalesSyncVO syncResult;
                if (isFullSync) {
                    syncResult = syncSalesDataFullAsync(shopId, limit, threadCount);
                } else {
                    syncResult = syncSalesDataIncrementalAsync(shopId, threadCount);
                }
                
                if (syncResult.getSuccess()) {
                    successCount++;
                } else {
                    failCount++;
                    failShops.add(shop.getShopName() + " (" + syncResult.getErrorMsg() + ")");
                }
            } catch (Exception e) {
                log.error("店铺 {} 同步异常: {}", shopId, e.getMessage(), e);
                failCount++;
                failShops.add("店铺ID: " + shopId + " (" + e.getMessage() + ")");
            }
        }
        
        // 构建结果消息
        StringBuilder result = new StringBuilder();
        result.append("销售数据").append(isFullSync ? "全量" : "增量").append("同步完成，成功: ")
              .append(successCount).append("，失败: ").append(failCount);
              
        if (!failShops.isEmpty()) {
            result.append("，失败店铺: ").append(String.join(", ", failShops));
        }
        
        String resultMessage = result.toString();
        log.info(resultMessage);
        return resultMessage;
    }
    
    /**
     * 根据ID更新同步任务对象
     *
     * @param entity 同步任务对象
     * @return 是否更新成功
     */
    @Override
    public boolean updateById(SalesSyncTask entity) {
        return super.updateById(entity);
    }
}