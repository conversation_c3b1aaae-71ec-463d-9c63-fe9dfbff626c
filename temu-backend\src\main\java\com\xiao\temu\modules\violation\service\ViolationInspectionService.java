package com.xiao.temu.modules.violation.service;

import com.xiao.temu.modules.violation.dto.ViolationInspectionQueryDTO;
import com.xiao.temu.modules.violation.vo.ViolationInspectionVO;

/**
 * 违规信息和质检结果综合查询Service接口
 */
public interface ViolationInspectionService {
    
    /**
     * 查询违规信息和质检结果综合数据
     *
     * @param queryDTO 查询条件
     * @return 查询结果
     */
    ViolationInspectionVO getViolationInspectionData(ViolationInspectionQueryDTO queryDTO);

}