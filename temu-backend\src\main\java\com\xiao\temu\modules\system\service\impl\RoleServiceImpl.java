package com.xiao.temu.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.system.entity.SysRole;
import com.xiao.temu.modules.system.entity.SysRoleMenu;
import com.xiao.temu.modules.system.mapper.SysRoleMapper;
import com.xiao.temu.modules.system.mapper.SysRoleMenuMapper;
import com.xiao.temu.modules.system.mapper.SysUserRoleMapper;
import com.xiao.temu.security.service.PermissionService;
import com.xiao.temu.modules.system.service.RoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoleServiceImpl implements RoleService {

    private final SysRoleMapper roleMapper;
    private final SysRoleMenuMapper roleMenuMapper;
    private final SysUserRoleMapper userRoleMapper;
    private final PermissionService permissionService;

    @Override
    public SysRole getRoleById(Long roleId) {
        return roleMapper.selectById(roleId);
    }

    @Override
    public IPage<SysRole> getRoleList(SysRole role, Page<SysRole> page) {
        LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (role != null) {
            // 按角色名称模糊查询
            if (StringUtils.isNotBlank(role.getRoleName())) {
                queryWrapper.like(SysRole::getRoleName, role.getRoleName());
            }
            
            // 按角色权限标识模糊查询
            if (StringUtils.isNotBlank(role.getRoleKey())) {
                queryWrapper.like(SysRole::getRoleKey, role.getRoleKey());
            }
            
            // 按状态查询
            if (StringUtils.isNotBlank(role.getStatus())) {
                queryWrapper.eq(SysRole::getStatus, role.getStatus());
            }
        }
        
        // 根据显示顺序排序
        queryWrapper.orderByAsc(SysRole::getRoleSort);
        
        // 执行分页查询
        return roleMapper.selectPage(page, queryWrapper);
    }

    @Override
    public List<SysRole> getAllRoles() {
        // 查询所有状态为正常的角色
        LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysRole::getStatus, "0");
        queryWrapper.orderByAsc(SysRole::getRoleSort);
        return roleMapper.selectList(queryWrapper);
    }

    @Override
    public List<SysRole> getRolesByUserId(Long userId) {
        return roleMapper.selectRolesByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertRole(SysRole role) {
        // 校验角色名称是否唯一
        if (!checkRoleNameUnique(role.getRoleName())) {
            throw new RuntimeException("新增角色'" + role.getRoleName() + "'失败，角色名称已存在");
        }
        
        // 校验角色权限标识是否唯一
        if (!checkRoleKeyUnique(role.getRoleKey())) {
            throw new RuntimeException("新增角色'" + role.getRoleName() + "'失败，角色权限标识已存在");
        }
        
        // 设置默认值
        role.setCreateTime(new Date());
        
        // 插入角色
        return roleMapper.insert(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRole(SysRole role) {
        // 校验角色是否存在
        SysRole existingRole = roleMapper.selectById(role.getRoleId());
        if (existingRole == null) {
            throw new RuntimeException("修改角色失败，角色不存在");
        }
        
        // 校验角色名称是否唯一（排除自身）
        if (StringUtils.isNotBlank(role.getRoleName()) 
                && !role.getRoleName().equals(existingRole.getRoleName())
                && !checkRoleNameUnique(role.getRoleName())) {
            throw new RuntimeException("修改角色'" + role.getRoleName() + "'失败，角色名称已存在");
        }
        
        // 校验角色权限标识是否唯一（排除自身）
        if (StringUtils.isNotBlank(role.getRoleKey()) 
                && !role.getRoleKey().equals(existingRole.getRoleKey())
                && !checkRoleKeyUnique(role.getRoleKey())) {
            throw new RuntimeException("修改角色'" + role.getRoleName() + "'失败，角色权限标识已存在");
        }
        
        // 设置更新时间
        role.setUpdateTime(new Date());
        
        // 更新角色
        int result = roleMapper.updateById(role);
        
        // 清除权限缓存
        if (result > 0) {
            permissionService.clearAllPermissionCache();
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRole(Long roleId) {
        // 查询角色是否有关联用户
        List<Long> userIds = userRoleMapper.selectUserIdsByRoleId(roleId);
        if (!userIds.isEmpty()) {
            throw new RuntimeException("删除角色失败，该角色已分配给用户");
        }
        
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(roleId);
        
        // 删除角色
        int result = roleMapper.deleteById(roleId);
        
        // 清除权限缓存
        if (result > 0) {
            permissionService.clearAllPermissionCache();
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRoles(Long[] roleIds) {
        // 检查所有角色是否有关联用户
        for (Long roleId : roleIds) {
            List<Long> userIds = userRoleMapper.selectUserIdsByRoleId(roleId);
            if (!userIds.isEmpty()) {
                SysRole role = roleMapper.selectById(roleId);
                String roleName = role != null ? role.getRoleName() : "未知角色";
                throw new RuntimeException("删除角色失败，角色'" + roleName + "'已分配给用户");
            }
        }
        
        // 批量删除角色与菜单关联
        for (Long roleId : roleIds) {
            roleMenuMapper.deleteRoleMenuByRoleId(roleId);
        }
        
        // 批量删除角色
        int result = roleMapper.deleteBatchIds(Arrays.asList(roleIds));
        
        // 清除权限缓存
        if (result > 0) {
            permissionService.clearAllPermissionCache();
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int changeStatus(Long roleId, String status) {
        // 检查角色是否存在
        SysRole existingRole = roleMapper.selectById(roleId);
        if (existingRole == null) {
            throw new RuntimeException("修改角色状态失败，角色不存在");
        }
        
        // 更新状态
        SysRole updateRole = new SysRole();
        updateRole.setRoleId(roleId);
        updateRole.setStatus(status);
        updateRole.setUpdateTime(new Date());
        
        int result = roleMapper.updateById(updateRole);
        
        // 清除权限缓存
        if (result > 0) {
            permissionService.clearAllPermissionCache();
        }
        
        return result;
    }

    @Override
    public boolean checkRoleNameUnique(String roleName) {
        return roleMapper.checkRoleNameUnique(roleName) == 0;
    }

    @Override
    public boolean checkRoleKeyUnique(String roleKey) {
        return roleMapper.checkRoleKeyUnique(roleKey) == 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int assignPermissions(Long roleId, Set<Long> menuIds) {
        // 校验角色是否存在
        SysRole role = roleMapper.selectById(roleId);
        if (role == null) {
            throw new RuntimeException("分配权限失败，角色不存在");
        }
        
        // 删除原有权限关系
        roleMenuMapper.deleteRoleMenuByRoleId(roleId);
        
        // 如果没有分配权限，直接返回
        if (menuIds == null || menuIds.isEmpty()) {
            return 1;
        }
        
        // 构建角色菜单关系
        List<SysRoleMenu> roleMenuList = menuIds.stream()
                .map(menuId -> {
                    SysRoleMenu roleMenu = new SysRoleMenu();
                    roleMenu.setRoleId(roleId);
                    roleMenu.setMenuId(menuId);
                    return roleMenu;
                })
                .collect(Collectors.toList());
        
        // 批量插入角色菜单关系
        roleMenuMapper.batchInsertRoleMenu(roleMenuList);
        
        // 清除权限缓存
        permissionService.clearAllPermissionCache();
        
        return 1;
    }

    @Override
    public Set<Long> getRoleMenuIds(Long roleId) {
        return roleMenuMapper.selectMenuIdsByRoleId(roleId);
    }
} 