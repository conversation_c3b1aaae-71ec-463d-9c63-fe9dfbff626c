package com.xiao.temu.modules.purchaseorderv.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xiao.temu.modules.purchaseorderv.dto.NotificationMatchDTO;
import com.xiao.temu.modules.purchaseorderv.dto.NotificationQueryDTO;
import com.xiao.temu.modules.purchaseorderv.dto.NotificationTriggerDTO;
import com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderNotificationConfig;
import com.xiao.temu.modules.purchaseorderv.service.PurchaseOrderNotificationConfigService;
import com.xiao.temu.modules.purchaseorderv.vo.NotificationConfigVO;
import com.xiao.temu.modules.purchaseorderv.vo.NotificationMatchOrderVO;
import com.xiao.temu.modules.purchaseorderv.vo.NotificationRecordVO;
import com.xiao.temu.modules.sync.vo.ApiResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 备货单通知配置控制器
 */
@Slf4j
@RestController
@RequestMapping("/purchase-order/notification")
public class PurchaseOrderNotificationConfigController {
    
    @Autowired
    private PurchaseOrderNotificationConfigService notificationConfigService;
    
    /**
     * 获取所有通知配置
     */
    @GetMapping("/config/list")
    public ApiResponse getAllConfigs() {
        try {
            List<NotificationConfigVO> configs = notificationConfigService.getAllNotificationConfigs();
            return ApiResponse.success(configs);
        } catch (Exception e) {
            log.error("获取通知配置列表失败：", e);
            return ApiResponse.error("获取通知配置列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取单个通知配置
     */
    @GetMapping("/config/{id}")
    public ApiResponse getConfigById(@PathVariable Long id) {
        try {
            NotificationConfigVO config = notificationConfigService.getNotificationConfigById(id);
            if (config == null) {
                return ApiResponse.error("通知配置不存在");
            }
            return ApiResponse.success(config);
        } catch (Exception e) {
            log.error("获取通知配置失败：", e);
            return ApiResponse.error("获取通知配置失败：" + e.getMessage());
        }
    }
    
    /**
     * 更新通知配置
     */
    @PutMapping("/config/update")
    public ApiResponse updateConfig(@RequestBody @Valid PurchaseOrderNotificationConfig config) {
        try {
            boolean success = notificationConfigService.updateNotificationConfig(config);
            if (success) {
                return ApiResponse.success("更新通知配置成功");
            } else {
                return ApiResponse.error("更新通知配置失败，配置不存在");
            }
        } catch (Exception e) {
            log.error("更新通知配置失败：", e);
            return ApiResponse.error("更新通知配置失败：" + e.getMessage());
        }
    }
    
    /**
     * 启用/禁用通知配置
     */
    @PutMapping("/config/toggle/{id}/{enabled}")
    public ApiResponse toggleConfig(@PathVariable Long id, @PathVariable Boolean enabled) {
        try {
            boolean success = notificationConfigService.toggleNotificationConfig(id, enabled);
            if (success) {
                return ApiResponse.success("操作成功");
            } else {
                return ApiResponse.error("操作失败，配置不存在");
            }
        } catch (Exception e) {
            log.error("启用/禁用通知配置失败：", e);
            return ApiResponse.error("操作失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取通知记录列表
     */
    @PostMapping("/record/list")
    public ApiResponse getNotificationRecords(@RequestBody NotificationQueryDTO queryDTO) {
        try {
            IPage<NotificationRecordVO> page = notificationConfigService.getNotificationRecords(queryDTO);
            return ApiResponse.success(page);
        } catch (Exception e) {
            log.error("获取通知记录列表失败：", e);
            return ApiResponse.error("获取通知记录列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 预览匹配通知条件的订单
     */
    @PostMapping("/match/preview")
    public ApiResponse previewMatchOrders(@RequestBody @Valid NotificationMatchDTO matchDTO) {
        try {
            IPage<NotificationMatchOrderVO> page = notificationConfigService.previewMatchOrders(matchDTO);
            return ApiResponse.success(page);
        } catch (Exception e) {
            log.error("预览匹配订单失败：", e);
            return ApiResponse.error("预览匹配订单失败：" + e.getMessage());
        }
    }
    
    /**
     * 手动触发通知
     */
    @PostMapping("/trigger")
    public ApiResponse manualTriggerNotification(@RequestBody @Valid NotificationTriggerDTO triggerDTO) {
        try {
            int count = notificationConfigService.manualTriggerNotification(triggerDTO);
            if (count > 0) {
                return ApiResponse.success("成功触发" + count + "条通知");
            } else {
                return ApiResponse.error("没有成功触发的通知");
            }
        } catch (Exception e) {
            log.error("手动触发通知失败：", e);
            return ApiResponse.error("手动触发通知失败：" + e.getMessage());
        }
    }
} 