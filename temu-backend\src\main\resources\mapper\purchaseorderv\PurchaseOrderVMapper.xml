<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.purchaseorderv.mapper.PurchaseOrderVMapper">

    <resultMap id="BaseResultMap" type="com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderV">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="sub_purchase_order_sn" property="subPurchaseOrderSn"/>
        <result column="original_purchase_order_sn" property="originalPurchaseOrderSn"/>
        <result column="product_name" property="productName"/>
        <result column="product_id" property="productId"/>
        <result column="product_skc_id" property="productSkcId"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="purchase_time" property="purchaseTime"/>
        <result column="deliver_time" property="deliverTime"/>
        <result column="delivery_order_sn" property="deliveryOrderSn"/>
        <result column="expect_latest_deliver_time" property="expectLatestDeliverTime"/>
        <result column="expect_latest_arrival_time" property="expectLatestArrivalTime"/>
        <result column="receive_warehouse_id" property="receiveWarehouseId"/>
        <result column="receive_warehouse_name" property="receiveWarehouseName"/>
        <result column="status" property="status"/>
        <result column="purchase_stock_type" property="purchaseStockType"/>
        <result column="receive_time" property="receiveTime"/>
        <result column="purchase_quantity" property="purchaseQuantity"/>
        <result column="deliver_quantity" property="deliverQuantity"/>
        <result column="receive_quantity" property="receiveQuantity"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="sync_time" property="syncTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="selectPurchaseOrderVVo">
        SELECT id, shop_id, sub_purchase_order_sn, original_purchase_order_sn, product_name, product_id, product_skc_id,
               supplier_id, supplier_name, purchase_time, deliver_time, delivery_order_sn, expect_latest_deliver_time,
               expect_latest_arrival_time, receive_warehouse_id, receive_warehouse_name, status, purchase_stock_type,
               receive_time, purchase_quantity, deliver_quantity, receive_quantity, is_deleted, sync_time,
               create_time, update_time
        FROM purchase_order_v
    </sql>

    <select id="getByShopIdAndOrderSn" resultMap="BaseResultMap">
        <include refid="selectPurchaseOrderVVo"/>
        WHERE shop_id = #{shopId} AND sub_purchase_order_sn = #{subPurchaseOrderSn} AND is_deleted = 0
    </select>

    <select id="getByShopIdAndOrderSnIncludeDeleted" resultMap="BaseResultMap">
        <include refid="selectPurchaseOrderVVo"/>
        WHERE shop_id = #{shopId} AND sub_purchase_order_sn = #{subPurchaseOrderSn}
    </select>

    <update id="updateByShopIdAndOrderSnForce">
        UPDATE purchase_order_v
        SET 
            original_purchase_order_sn = #{order.originalPurchaseOrderSn},
            product_name = #{order.productName},
            product_id = #{order.productId},
            product_skc_id = #{order.productSkcId},
            supplier_id = #{order.supplierId},
            supplier_name = #{order.supplierName},
            purchase_time = #{order.purchaseTime},
            deliver_time = #{order.deliverTime},
            delivery_order_sn = #{order.deliveryOrderSn},
            expect_latest_deliver_time = #{order.expectLatestDeliverTime},
            expect_latest_arrival_time = #{order.expectLatestArrivalTime},
            receive_warehouse_id = #{order.receiveWarehouseId},
            receive_warehouse_name = #{order.receiveWarehouseName},
            status = #{order.status},
            purchase_stock_type = #{order.purchaseStockType},
            receive_time = #{order.receiveTime},
            purchase_quantity = #{order.purchaseQuantity},
            deliver_quantity = #{order.deliverQuantity},
            receive_quantity = #{order.receiveQuantity},
            is_deleted = 0,
            sync_time = #{order.syncTime},
            update_time = now()
        WHERE shop_id = #{shopId} AND sub_purchase_order_sn = #{subPurchaseOrderSn}
    </update>

    <select id="countByShopId" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM purchase_order_v
        WHERE shop_id = #{shopId} AND is_deleted = 0
        <if test="purchaseStockType != null">
            AND purchase_stock_type = #{purchaseStockType}
        </if>
    </select>

    <update id="deleteByShopId">
        UPDATE purchase_order_v
        SET is_deleted = 1
        WHERE shop_id = #{shopId}
        <if test="purchaseStockType != null">
            AND purchase_stock_type = #{purchaseStockType}
        </if>
    </update>

    <delete id="physicalDeleteByShopId">
        DELETE FROM purchase_order_v
        WHERE shop_id = #{shopId}
        <if test="purchaseStockType != null">
            AND purchase_stock_type = #{purchaseStockType}
        </if>
    </delete>

    <select id="findJitOrdersAboutToExpire" resultMap="BaseResultMap">
        <include refid="selectPurchaseOrderVVo"/>
        WHERE purchase_stock_type = 1
        AND status = 2
        AND is_deleted = 0
        AND expect_latest_arrival_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <select id="findExpiredJitOrders" resultMap="BaseResultMap">
        <include refid="selectPurchaseOrderVVo"/>
        WHERE purchase_stock_type = 1
        AND status = 2
        AND is_deleted = 0
        AND expect_latest_arrival_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <select id="findLongTimeNotShippedOrders" resultMap="BaseResultMap">
        <include refid="selectPurchaseOrderVVo"/>
        WHERE purchase_stock_type = 0
        AND status = 1
        AND is_deleted = 0
        AND purchase_time &lt; #{createdBefore}
    </select>

    <select id="findShippedButNotReceivedOrders" resultMap="BaseResultMap">
        SELECT pov.id, pov.shop_id, pov.sub_purchase_order_sn, pov.original_purchase_order_sn, 
               pov.product_name, pov.product_id, pov.product_skc_id, pov.supplier_id, pov.supplier_name, 
               pov.purchase_time, pov.deliver_time, pov.delivery_order_sn, pov.expect_latest_deliver_time,
               pov.expect_latest_arrival_time, pov.receive_warehouse_id, pov.receive_warehouse_name, 
               pov.status, pov.purchase_stock_type, pov.receive_time, pov.purchase_quantity, 
               pov.deliver_quantity, pov.receive_quantity, pov.is_deleted, pov.sync_time,
               pov.create_time, pov.update_time
        FROM purchase_order_v pov
        INNER JOIN production_progress pp ON pov.shop_id = pp.shop_id AND pov.sub_purchase_order_sn = pp.sub_purchase_order_sn
        WHERE pov.purchase_stock_type = 0
        AND pov.status = 2
        AND pov.is_deleted = 0
        AND pp.shipping_status = 1
        AND pp.shipping_time &lt; #{shippedBefore}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO purchase_order_v (
        shop_id, sub_purchase_order_sn, original_purchase_order_sn, product_name, product_id,
        product_skc_id, supplier_id, supplier_name, purchase_time, deliver_time,
        delivery_order_sn, expect_latest_deliver_time, expect_latest_arrival_time,
        receive_warehouse_id, receive_warehouse_name, status, purchase_stock_type, receive_time,
        purchase_quantity, deliver_quantity, receive_quantity, is_deleted, sync_time,
        create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.shopId}, #{item.subPurchaseOrderSn}, #{item.originalPurchaseOrderSn},
            #{item.productName}, #{item.productId}, #{item.productSkcId}, #{item.supplierId},
            #{item.supplierName}, #{item.purchaseTime}, #{item.deliverTime}, #{item.deliveryOrderSn},
            #{item.expectLatestDeliverTime}, #{item.expectLatestArrivalTime}, #{item.receiveWarehouseId},
            #{item.receiveWarehouseName}, #{item.status}, #{item.purchaseStockType}, #{item.receiveTime},
            #{item.purchaseQuantity}, #{item.deliverQuantity}, #{item.receiveQuantity},
            #{item.isDeleted}, #{item.syncTime}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        original_purchase_order_sn = VALUES(original_purchase_order_sn),
        product_name = VALUES(product_name),
        product_id = VALUES(product_id),
        product_skc_id = VALUES(product_skc_id),
        supplier_id = VALUES(supplier_id),
        supplier_name = VALUES(supplier_name),
        purchase_time = VALUES(purchase_time),
        deliver_time = VALUES(deliver_time),
        delivery_order_sn = VALUES(delivery_order_sn),
        expect_latest_deliver_time = VALUES(expect_latest_deliver_time),
        expect_latest_arrival_time = VALUES(expect_latest_arrival_time),
        receive_warehouse_id = VALUES(receive_warehouse_id),
        receive_warehouse_name = VALUES(receive_warehouse_name),
        status = VALUES(status),
        purchase_stock_type = VALUES(purchase_stock_type),
        receive_time = VALUES(receive_time),
        purchase_quantity = VALUES(purchase_quantity),
        deliver_quantity = VALUES(deliver_quantity),
        receive_quantity = VALUES(receive_quantity),
        is_deleted = VALUES(is_deleted),
        sync_time = VALUES(sync_time),
        update_time = VALUES(update_time)
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE purchase_order_v
            <set>
                original_purchase_order_sn = #{item.originalPurchaseOrderSn},
                product_name = #{item.productName},
                product_id = #{item.productId},
                product_skc_id = #{item.productSkcId},
                supplier_id = #{item.supplierId},
                supplier_name = #{item.supplierName},
                purchase_time = #{item.purchaseTime},
                deliver_time = #{item.deliverTime},
                delivery_order_sn = #{item.deliveryOrderSn},
                expect_latest_deliver_time = #{item.expectLatestDeliverTime},
                expect_latest_arrival_time = #{item.expectLatestArrivalTime},
                receive_warehouse_id = #{item.receiveWarehouseId},
                receive_warehouse_name = #{item.receiveWarehouseName},
                status = #{item.status},
                purchase_stock_type = #{item.purchaseStockType},
                receive_time = #{item.receiveTime},
                purchase_quantity = #{item.purchaseQuantity},
                deliver_quantity = #{item.deliverQuantity},
                receive_quantity = #{item.receiveQuantity},
                is_deleted = #{item.isDeleted},
                sync_time = #{item.syncTime},
                update_time = now()
            </set>
            WHERE shop_id = #{item.shopId} AND sub_purchase_order_sn = #{item.subPurchaseOrderSn}
        </foreach>
    </update>
</mapper> 