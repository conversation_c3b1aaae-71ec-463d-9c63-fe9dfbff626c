package com.xiao.temu.modules.operation.service;

import com.xiao.temu.common.response.PageResult;
import com.xiao.temu.modules.operation.dto.GroupLeaderShopAssignmentDTO;
import com.xiao.temu.modules.operation.dto.GroupLeaderShopAssignmentQueryDTO;
import com.xiao.temu.modules.operation.entity.GroupLeaderShopAssignment;

import java.util.List;

/**
 * 组长店铺分配服务接口
 */
public interface GroupLeaderShopAssignmentService {

    /**
     * 分页查询分配列表
     *
     * @param queryDTO 查询条件
     * @return 分页数据
     */
    PageResult<GroupLeaderShopAssignmentDTO> listAssignments(GroupLeaderShopAssignmentQueryDTO queryDTO);

    /**
     * 获取指定运营组的所有店铺分配数据（不分页）
     *
     * @param groupId 运营组ID
     * @return 该运营组的所有店铺分配数据列表
     */
    List<GroupLeaderShopAssignmentDTO> listAllAssignmentsByGroupId(Long groupId);

    /**
     * 根据ID查询分配详情
     *
     * @param id 主键ID
     * @return 分配详情
     */
    GroupLeaderShopAssignmentDTO getAssignmentById(Long id);

    /**
     * 新增分配
     *
     * @param assignment 分配信息
     * @return 操作结果 true-成功 false-失败
     */
    boolean addAssignment(GroupLeaderShopAssignment assignment);

    /**
     * 修改分配信息
     *
     * @param assignment 分配信息
     * @return 操作结果 true-成功 false-失败
     */
    boolean updateAssignment(GroupLeaderShopAssignment assignment);

    /**
     * 修改分配状态
     *
     * @param id 主键ID
     * @param status 状态(0正常1禁用)
     * @return 操作结果 true-成功 false-失败
     */
    boolean changeStatus(Long id, String status);

    /**
     * 删除分配
     *
     * @param id 主键ID
     * @return 操作结果 true-成功 false-失败
     */
    boolean deleteAssignment(Long id);

    /**
     * 组长批量分配店铺给用户
     *
     * @param groupId 运营组ID
     * @param shopIds 店铺ID列表
     * @param userId 用户ID
     * @param permissionType 权限类型
     * @param assignBy 分配人ID
     * @return 操作结果 true-成功 false-失败
     */
    boolean batchAssignShops(Long groupId, List<Long> shopIds, Long userId, String permissionType, Long assignBy);

    /**
     * 批量取消用户的店铺分配
     *
     * @param ids 分配ID列表
     * @return 操作结果 true-成功 false-失败
     */
    boolean batchDeleteAssignments(List<Long> ids);

    /**
     * 查询用户在指定店铺的分配信息
     *
     * @param userId 用户ID
     * @param shopId 店铺ID
     * @return 分配信息
     */
    GroupLeaderShopAssignmentDTO getUserShopAssignment(Long userId, Long shopId);

    /**
     * 查询用户的所有店铺分配
     *
     * @param userId 用户ID
     * @return 分配列表
     */
    List<GroupLeaderShopAssignmentDTO> getUserAssignments(Long userId);

    /**
     * 查询店铺的所有用户分配
     *
     * @param shopId 店铺ID
     * @return 分配列表
     */
    List<GroupLeaderShopAssignmentDTO> getShopAssignments(Long shopId);

    /**
     * 查询运营组的所有分配
     *
     * @param groupId 运营组ID
     * @return 分配列表
     */
    List<GroupLeaderShopAssignmentDTO> getGroupAssignments(Long groupId);

    /**
     * 检查用户是否有店铺权限
     *
     * @param userId 用户ID
     * @param shopId 店铺ID
     * @param requireWrite 是否需要写权限
     * @return true-有权限 false-无权限
     */
    boolean checkShopPermission(Long userId, Long shopId, boolean requireWrite);

    /**
     * 检查用户是否为组长
     *
     * @param userId 用户ID
     * @param groupId 运营组ID
     * @return true-是组长 false-不是组长
     */
    boolean isGroupLeader(Long userId, Long groupId);
} 