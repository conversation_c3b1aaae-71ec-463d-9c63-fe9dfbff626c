package com.xiao.temu.modules.production.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.production.dto.ProductionGroupMemberDTO;
import com.xiao.temu.modules.production.entity.ProductionGroupMember;
import com.xiao.temu.modules.production.mapper.ProductionGroupMemberMapper;
import com.xiao.temu.modules.production.service.ProductionGroupMemberService;
import com.xiao.temu.modules.production.service.ProductionGroupRoleAssignmentService;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.system.mapper.SysRoleMapper;
import com.xiao.temu.modules.system.mapper.SysUserMapper;
import com.xiao.temu.modules.system.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 生产组成员服务实现类
 */
@Service
@RequiredArgsConstructor
public class ProductionGroupMemberServiceImpl implements ProductionGroupMemberService {

    private final ProductionGroupMemberMapper memberMapper;
    private final ProductionGroupRoleAssignmentService roleAssignmentService;
    private final SysUserMapper userMapper;
    private final SysRoleMapper roleMapper;
    private final UserService userService;

    @Override
    public IPage<ProductionGroupMemberDTO> getMemberList(Long groupId, Page<ProductionGroupMemberDTO> page, String keyword) {
        return memberMapper.selectMemberList(page, groupId, keyword);
    }

    @Override
    public List<ProductionGroupMemberDTO> getAllMembers(Long groupId) {
        return memberMapper.selectAllMembers(groupId);
    }

    @Override
    public int addMember(Long groupId, Long userId) {
        // 检查用户是否已经是成员
        if (checkUserInGroup(groupId, userId)) {
            return 0;
        }
        
        // 创建成员记录
        ProductionGroupMember member = new ProductionGroupMember();
        member.setGroupId(groupId);
        member.setUserId(userId);
        member.setJoinTime(new Date());
        member.setStatus("0");
        
        return memberMapper.insert(member);
    }

    @Override
    public int batchAddMembers(Long groupId, List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return 0;
        }
        
        // 过滤掉已经是成员的用户
        List<Long> filteredUserIds = userIds.stream()
                .filter(userId -> !checkUserInGroup(groupId, userId))
                .collect(Collectors.toList());
        
        if (filteredUserIds.isEmpty()) {
            return 0;
        }
        
        return memberMapper.batchAddMembers(groupId, filteredUserIds, new Date());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeMember(Long groupId, Long userId) {
        // 先删除该成员的所有角色分配
        roleAssignmentService.deleteUserRoles(groupId, userId);
        
        // 再删除成员记录
        return memberMapper.deleteMember(groupId, userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeAllMembers(Long groupId) {
        return memberMapper.deleteAllMembers(groupId);
    }

    @Override
    public boolean checkUserInGroup(Long groupId, Long userId) {
        return memberMapper.checkUserInGroup(groupId, userId) > 0;
    }

    @Override
    public int countGroupMembers(Long groupId) {
        return memberMapper.countGroupMembers(groupId);
    }

    @Override
    public List<Long> getGroupIdsByUserId(Long userId) {
        LambdaQueryWrapper<ProductionGroupMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductionGroupMember::getUserId, userId)
                   .eq(ProductionGroupMember::getStatus, "0")
                   .select(ProductionGroupMember::getGroupId);
        
        return memberMapper.selectList(queryWrapper).stream()
                .map(ProductionGroupMember::getGroupId)
                .collect(Collectors.toList());
    }

    @Override
    public IPage<SysUser> getAvailableUsers(Long groupId, Page<SysUser> page, String keyword) {
        // 定义生产相关的角色标识列表
        List<String> productionRoleKeys = Arrays.asList(
                "burning", // 烧花角色
                "sewing", // 车缝角色
                "tail", // 尾部处理角色
                "inspection", // 查货角色
                "shipping", // 发货角色
                "delivery" // 送货角色
        );
        
        // 获取已在生产组的用户ID列表
        List<Long> existingUserIds = memberMapper.selectUserIdsByGroupId(groupId);
        
        // 查询具有生产角色的用户
        return userMapper.selectProductionRoleUsers(page, productionRoleKeys, existingUserIds, keyword);
    }
} 