// 违规信息和质检结果综合查询API服务
import request from '@/utils/request'
import type { ViolationInspectionQueryParams, ViolationInspectionResult } from '@/types/local/violationInspection'
import { getToken } from '@/utils/auth' // 导出时可能需要token

// 获取违规信息和质检结果综合数据列表
export function getViolationInspectionList(params: ViolationInspectionQueryParams) {
  return request<ViolationInspectionResult>({
    url: '/local/violation-inspection/list',
    method: 'post',
    data: params
  }).then(res => res.data) // 注意：这里返回的是 res.data
}

// 获取违规信息和质检结果综合详情
export function getViolationInspectionDetail(shopId: number, punishSn: string) {
  return request<ViolationInspectionResult>({
    url: `/local/violation-inspection/detail/${shopId}/${punishSn}`,
    method: 'get'
  }).then(res => res.data) // 注意：这里返回的是 res.data
}

// 创建违规信息导出任务 - 返回 data 部分 { taskId: "..." }
export function createViolationExportTask(exportParams: any) {
  return request<{ taskId: string }>({
    url: '/local/violation-inspection/createExportTask',
    method: 'post',
    data: exportParams
    // 请求头通常由 request 拦截器自动添加 token
  }).then(res => res.data) // 明确返回 data 部分
}

// 获取导出任务进度 - 返回 data 部分
export function getExportProgress(taskId: string) {
  return request<any>({
    url: `/local/violation-inspection/exportProgress/${taskId}`,
    method: 'get'
    // 请求头通常由 request 拦截器自动添加 token
  }).then(res => res.data) // 明确返回 data 部分
}

// 获取导出文件下载链接 (只返回URL字符串，不发起请求)
export function getExportDownloadUrl(taskId: string): string {
  // 假设API基础路径已在 request 工具中配置
  const baseUrl = request.defaults.baseURL || ''; 
  // 重要：确保返回的是相对或绝对路径，供 fetch 使用
  return `${baseUrl}/local/violation-inspection/downloadExcel/${taskId}`;
} 