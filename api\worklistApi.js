/**
 * 工作列表相关API
 */

const { request } = require('./apiUtils');
const { getBaseUrl } = require('./config');

// 获取API基础URL
const BASE_URL = getBaseUrl();

/**
 * 获取待处理工作列表
 * @returns {Promise} 待处理工作列表
 */
const getPendingList = () => {
  return request(`${BASE_URL}/work/pending`, {});
};

/**
 * 获取已处理工作列表
 * @returns {Promise} 已处理工作列表
 */
const getCompletedList = () => {
  return request(`${BASE_URL}/work/completed`, {});
};

/**
 * 获取当前用户的工作操作记录
 * @param {number} limit - 限制返回数量
 * @returns {Promise} 工作记录列表
 */
const getOperationRecords = (limit = 20) => {
  return request(`${BASE_URL}/v1/production/progress/user-logs`, {}, 'GET', {
    limit: limit
  });
};

/**
 * 获取工单详情
 * @param {string} orderNumber - 备货单号
 * @returns {Promise} 工单详情
 */
const getOrderDetail = (orderNumber) => {
  return request(`${BASE_URL}/work/detail`, {
    orderNumber
  });
};

/**
 * 更新工序状态
 * @param {string} orderNumber - 备货单号
 * @param {string} processId - 工序ID
 * @returns {Promise} 更新结果
 */
const updateProcess = (orderNumber, processId) => {
  return request(`${BASE_URL}/work/update-process`, {
    orderNumber,
    processId
  });
};

/**
 * 通过扫码获取工单信息
 * @param {string} qrCode - 二维码内容
 * @returns {Promise} 工单信息
 */
const getOrderByQrCode = (qrCode) => {
  return request(`${BASE_URL}/work/scan`, {
    qrCode
  });
};

module.exports = {
  getPendingList,
  getCompletedList,
  getOrderDetail,
  updateProcess,
  getOrderByQrCode,
  getOperationRecords
}; 