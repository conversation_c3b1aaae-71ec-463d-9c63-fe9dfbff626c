package com.xiao.temu.modules.system.controller;

import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.security.model.AuthResult;
import com.xiao.temu.security.model.LoginRequest;
import com.xiao.temu.security.service.AuthService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ApiResponse<AuthResult> login(@Valid @RequestBody LoginRequest loginRequest) {
        log.info("用户登录：{}", loginRequest.getUsername());
        AuthResult authResult = authService.login(loginRequest);
        return ApiResponse.success("登录成功", authResult);
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public ApiResponse<AuthResult> register(@RequestParam String username,
                                           @RequestParam String password,
                                           @RequestParam String nickName) {
        log.info("用户注册：{}", username);
        AuthResult authResult = authService.register(username, password, nickName);
        return ApiResponse.success("注册成功", authResult);
    }

    /**
     * 刷新Token
     */
    @PostMapping("/refresh-token")
    public ApiResponse<AuthResult> refreshToken(@RequestParam String token) {
        log.info("刷新Token");
        AuthResult authResult = authService.refreshToken(token);
        return ApiResponse.success("刷新Token成功", authResult);
    }

    /**
     * 获取当前登录用户信息
     */
    @GetMapping("/user-info")
    public ApiResponse<AuthResult> getUserInfo() {
        log.info("获取当前用户信息");
        AuthResult authResult = authService.getCurrentUser();
        return ApiResponse.success(authResult);
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public ApiResponse<Void> logout() {
        log.info("用户登出");
        authService.logout();
        return ApiResponse.success("登出成功");
    }
} 