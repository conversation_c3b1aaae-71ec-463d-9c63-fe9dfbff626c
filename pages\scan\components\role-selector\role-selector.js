// 角色选择组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 角色列表
    roles: {
      type: Array,
      value: []
    },
    // 已选择的角色键值
    selectedRoles: {
      type: Array,
      value: [],
      observer: function(newVal) {
        // 直接观察者立即更新内部的选中状态
        console.log('角色选择器-外部selectedRoles变更:', newVal);
        const roles = Array.isArray(newVal) ? newVal : [];
        this.setData({
          currentSelectedRoles: roles
        });
      }
    }
  },
  
  /**
   * 组件的初始数据
   */
  data: {
    currentSelectedRoles: []
  },
  
  /**
   * 组件生命周期
   */
  lifetimes: {
    attached: function() {
      // 确保初始值为数组
      console.log('角色选择器-组件加载, 初始选中角色:', this.data.selectedRoles);
      const roles = Array.isArray(this.data.selectedRoles) ? this.data.selectedRoles : [];
      this.setData({
        currentSelectedRoles: roles
      });
    },
    ready: function() {
      // 组件准备完成后检查状态
      console.log('角色选择器-组件准备完成, 当前选中角色:', this.data.currentSelectedRoles);
    }
  },
  
  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 处理单个复选框变更
     */
    handleSingleCheckboxChange: function(e) {
      const { value, checked } = e.detail;
      console.log(`角色选择器-复选框变化: ${value} => ${checked ? '选中' : '取消'}`);
      
      let currentSelectedRoles = [...this.data.currentSelectedRoles];
      
      if (checked) {
        // 添加选中项
        if (!currentSelectedRoles.includes(value)) {
          currentSelectedRoles.push(value);
        }
      } else {
        // 移除选中项
        currentSelectedRoles = currentSelectedRoles.filter(v => v !== value);
      }
      
      console.log('角色选择器-更新后的选中角色:', currentSelectedRoles);
      
      // 更新当前选中状态
      this.setData({
        currentSelectedRoles: currentSelectedRoles
      });
      
      // 触发选择变更事件
      this.triggerEvent('change', {
        value: currentSelectedRoles
      });
    },
    
    /**
     * 处理角色选择变更（兼容旧接口）
     */
    handleRoleChange: function(e) {
      const selectedValues = e.detail.value;
      console.log('角色选择器-旧接口角色变更:', selectedValues);
      
      // 更新当前选中状态
      this.setData({
        currentSelectedRoles: selectedValues
      });
      
      // 触发选择变更事件
      this.triggerEvent('change', {
        value: selectedValues
      });
    }
  }
}) 