Component({
  properties: {
    title: {
      type: String,
      value: ''
    },
    showBack: {
      type: Boolean,
      value: false
    }
  },
  
  data: {
    statusBarHeight: 0,
    menuButtonInfo: {}
  },
  
  lifetimes: {
    attached() {
      // 获取状态栏高度
      const systemInfo = wx.getSystemInfoSync();
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight
      });
      
      // 获取胶囊按钮信息
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      this.setData({
        menuButtonInfo
      });
    }
  },
  
  methods: {
    // 返回上一页
    goBack() {
      wx.navigateBack({
        delta: 1
      });
      
      // 触发返回事件
      this.triggerEvent('back');
    }
  }
}) 