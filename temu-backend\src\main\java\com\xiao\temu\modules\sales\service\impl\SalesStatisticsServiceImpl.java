package com.xiao.temu.modules.sales.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.infrastructure.api.TemuApiClient;
import com.xiao.temu.modules.sales.dto.SalesStatisticsQueryDTO;
import com.xiao.temu.modules.sales.entity.SalesStatistics;
import com.xiao.temu.modules.sales.mapper.SalesStatisticsMapper;
import com.xiao.temu.modules.sales.service.SalesStatisticsService;
import com.xiao.temu.modules.sales.vo.ShopSalesStatisticsVO;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.shop.mapper.ShopMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.ehcache.shadow.org.terracotta.statistics.Time;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 销售统计服务实现类
 */
@Service
public class SalesStatisticsServiceImpl extends ServiceImpl<SalesStatisticsMapper, SalesStatistics> implements SalesStatisticsService {

    private static final Logger logger = LoggerFactory.getLogger(SalesStatisticsServiceImpl.class);
    
    // 线程池配置，核心线程数为处理器核心数的两倍
    private static final int CORE_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 2;
    private static final ExecutorService THREAD_POOL = Executors.newFixedThreadPool(CORE_POOL_SIZE);
    
    // 重试配置
    private static final int MAX_RETRY_ATTEMPTS = 10; // 最大重试次数
    private static final long INITIAL_RETRY_DELAY_MS = 1000; // 初始重试延迟（毫秒）
    private static final int PAGE_RETRY_ATTEMPTS = 5; // 单个页面API调用最大重试次数
    private static final long PAGE_RETRY_DELAY_MS = 500; // 单个页面API调用初始重试延迟（毫秒）

    @Autowired
    private SalesStatisticsMapper salesStatisticsMapper;

    @Autowired
    private ShopMapper shopMapper;
    
    /**
     * 初始化钩子，添加JVM关闭时的线程池关闭处理
     */
    @PostConstruct
    public void init() {
        // 添加JVM关闭钩子，确保线程池正确关闭
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logger.info("应用关闭，正在关闭销售统计线程池...");
            shutdownThreadPool();
        }));
        logger.info("销售统计服务初始化完成，线程池大小: {}", CORE_POOL_SIZE);
    }
    
    /**
     * 应用关闭时关闭线程池
     */
    @PreDestroy
    public void destroy() {
        shutdownThreadPool();
    }
    
    /**
     * 关闭线程池的方法
     */
    private void shutdownThreadPool() {
        if (THREAD_POOL != null && !THREAD_POOL.isShutdown()) {
            logger.info("正在关闭销售统计线程池...");
            THREAD_POOL.shutdown();
            try {
                // 等待所有任务完成，最多等待30秒
                if (!THREAD_POOL.awaitTermination(30, TimeUnit.SECONDS)) {
                    // 强制关闭未完成的任务
                    THREAD_POOL.shutdownNow();
                    logger.warn("销售统计线程池强制关闭，可能有任务未完成");
                    // 再次等待，确保任务响应中断
                    if (!THREAD_POOL.awaitTermination(5, TimeUnit.SECONDS)) {
                        logger.error("销售统计线程池无法完全关闭");
                    }
                } else {
                    logger.info("销售统计线程池已正常关闭");
                }
            } catch (InterruptedException e) {
                // 如果当前线程被中断，重新尝试关闭
                THREAD_POOL.shutdownNow();
                Thread.currentThread().interrupt();
                logger.error("关闭销售统计线程池时被中断", e);
            }
        }
    }

    @Override
    public List<ShopSalesStatisticsVO> getShopSalesStatistics(SalesStatisticsQueryDTO queryDTO) {
        List<ShopSalesStatisticsVO> result = new ArrayList<>();
        
        if (queryDTO.getShopIds() == null || queryDTO.getShopIds().isEmpty()) {
            logger.error("店铺ID列表不能为空");
            return result;
        }
        
        // 查询数据库中的统计数据
        List<SalesStatistics> statisticsList = salesStatisticsMapper.findByShopIdsAndDateBetween(
            queryDTO.getShopIds(), queryDTO.getStartDate(), queryDTO.getEndDate());
        
        // 转换为VO
        for (SalesStatistics statistics : statisticsList) {
            ShopSalesStatisticsVO vo = new ShopSalesStatisticsVO();
            BeanUtils.copyProperties(statistics, vo);
            vo.setSuccess(true);
            
            // 查询店铺信息以获取备注
            Shop shop = shopMapper.selectById(statistics.getShopId());
            if (shop != null) {
                vo.setShopRemark(shop.getRemark());
            }
            
            result.add(vo);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String executeScheduledStatistics() {
        logger.info("开始执行销售数据统计定时任务");
        StringBuilder result = new StringBuilder();
        
        // 获取所有启用的店铺
        List<Shop> shops = shopMapper.selectAllEnabledShops();
        if (shops == null || shops.isEmpty()) {
            return "没有可同步的店铺";
        }
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        List<String> failShops = new ArrayList<>();
        
        // 使用CompletableFuture并行处理店铺同步
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        for (Shop shop : shops) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 同步店铺数据，带重试机制
                    ApiResponse<ShopSalesStatisticsVO> syncResult = syncShopSalesStatisticsWithRetry(shop.getShopId());
                    if (syncResult.getCode() == 200) {
                        successCount.incrementAndGet();
                    } else {
                        failCount.incrementAndGet();
                        synchronized (failShops) {
                            failShops.add(shop.getShopName() + "(" + syncResult.getMessage() + ")");
                        }
                    }
                } catch (Exception e) {
                    logger.error("店铺销售统计同步出错: " + shop.getShopName(), e);
                    failCount.incrementAndGet();
                    synchronized (failShops) {
                        failShops.add(shop.getShopName() + "(" + e.getMessage() + ")");
                    }
                }
            }, THREAD_POOL);
            
            futures.add(future);
        }
        
        // 等待所有异步任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(30, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("等待店铺同步任务完成时发生异常", e);
            result.append("同步过程中发生异常: ").append(e.getMessage());
            return result.toString();
        }
        
        // 构建结果消息
        result.append("销售数据统计完成，成功: ").append(successCount.get()).append("，失败: ").append(failCount.get());
        if (!failShops.isEmpty()) {
            result.append("，失败店铺: ").append(String.join(", ", failShops));
        }
        
        logger.info(result.toString());
        return result.toString();
    }

    /**
     * 带有重试机制的店铺销售统计同步方法
     * 
     * @param shopId 店铺ID
     * @return API响应结果
     */
    public ApiResponse<ShopSalesStatisticsVO> syncShopSalesStatisticsWithRetry(Long shopId) {
        int attempts = 0;
        long retryDelay = INITIAL_RETRY_DELAY_MS;
        
        while (attempts < MAX_RETRY_ATTEMPTS) {
            try {
                return syncShopSalesStatistics(shopId);
            } catch (Exception e) {
                attempts++;
                if (attempts >= MAX_RETRY_ATTEMPTS) {
                    logger.error("店铺ID[{}]同步失败，已达到最大重试次数{}", shopId, MAX_RETRY_ATTEMPTS, e);
                    
                    // 创建失败响应
                    ApiResponse<ShopSalesStatisticsVO> response = new ApiResponse<>();
                    ShopSalesStatisticsVO statisticsVO = new ShopSalesStatisticsVO();
                    statisticsVO.setShopId(shopId);
                    statisticsVO.setStatisticsDate(LocalDate.now());
                    statisticsVO.setSuccess(false);
                    statisticsVO.setErrorMsg("同步失败，重试" + MAX_RETRY_ATTEMPTS + "次后仍然异常: " + e.getMessage());
                    
                    response.setCode(500);
                    response.setMessage("同步失败，重试后仍然异常: " + e.getMessage());
                    response.setData(statisticsVO);
                    return response;
                }
                
                // 计算下一次重试的延迟时间（指数退避）
                retryDelay = retryDelay * 2;
                logger.warn("店铺ID[{}]同步失败，将在{}ms后进行第{}次重试", shopId, retryDelay, attempts + 1);
                
                try {
                    Thread.sleep(retryDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试等待被中断", ie);
                }
            }
        }
        
        // 不应该到达这里，但为了编译通过
        ApiResponse<ShopSalesStatisticsVO> response = new ApiResponse<>();
        response.setCode(500);
        response.setMessage("同步异常");
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<ShopSalesStatisticsVO> syncShopSalesStatistics(Long shopId) {
        logger.info("开始同步店铺销售统计数据，店铺ID: {}", shopId);
        
        // 返回结果初始化
        ApiResponse<ShopSalesStatisticsVO> response = new ApiResponse<>();
        ShopSalesStatisticsVO statisticsVO = new ShopSalesStatisticsVO();
        statisticsVO.setShopId(shopId);
        statisticsVO.setStatisticsDate(LocalDate.now());
        
        // 获取店铺信息
        Shop shop = shopMapper.selectById(shopId);
        if (shop == null) {
            logger.error("店铺不存在，ID: {}", shopId);
            statisticsVO.setSuccess(false);
            statisticsVO.setErrorMsg("店铺不存在");
            response.setCode(404);
            response.setMessage("店铺不存在");
            response.setData(statisticsVO);
            return response;
        }
        
        statisticsVO.setShopName(shop.getShopName());
        statisticsVO.setShopRemark(shop.getRemark());
        
        // 检查API密钥信息
        if ("1".equals(shop.getStatus()) || 
            shop.getApiKey() == null || 
            shop.getApiSecret() == null || 
            shop.getAccessToken() == null) {
            statisticsVO.setSuccess(false);
            statisticsVO.setErrorMsg("店铺被禁用或API密钥信息不完整");
            response.setCode(400);
            response.setMessage("店铺被禁用或API密钥信息不完整");
            response.setData(statisticsVO);
            return response;
        }
        
        try {
            // 设置API调用参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.goods.salesv2.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());
            
            // 初始化计数器
            int totalProducts = 0;
            int todaySales = 0;
            int lastWeekSales = 0;
            int lastMonthSales = 0;
            
            // 设置分页参数
            int pageNo = 1;
            int pageSize = 100; // API最大支持100条每页
            boolean hasMore = true;
            Integer totalItems = null; // 保存API返回的总数
            
            while (hasMore) {
                // 构建业务参数
                Map<String, Object> businessParams = new HashMap<>();
                businessParams.put("timestamp", String.valueOf(Time.time()));
                businessParams.put("pageNo", String.valueOf(pageNo));
                businessParams.put("pageSize", String.valueOf(pageSize));
                businessParams.put("thirtyDaysSaleVolumMin", 1); // 只查询有销量的商品
                
                // 为当前页API调用添加重试机制
                boolean apiCallSuccess = false;
                int apiRetryAttempts = 0;
                long apiRetryDelay = PAGE_RETRY_DELAY_MS;
                JSONObject jsonResult = null;
                
                while (!apiCallSuccess && apiRetryAttempts < PAGE_RETRY_ATTEMPTS) {
                    try {
                        // 调用TEMU API
                        jsonResult = TemuApiClient.sendRequest(commonParams, businessParams);
                        logger.info("店铺[{}]第{}页销售数据API调用结果:{}", shop.getShopName(), pageNo, jsonResult != null ? jsonResult.size() : "null");
                        
                        // 检查API调用是否成功
                        if (jsonResult != null && jsonResult.getBooleanValue("success") && jsonResult.getInteger("errorCode") == 1000000) {
                            apiCallSuccess = true;
                        } else {
                            String errorMsg = jsonResult != null ? jsonResult.getString("errorMsg") : "API调用失败";
                            apiRetryAttempts++;
                            
                            if (apiRetryAttempts >= PAGE_RETRY_ATTEMPTS) {
                                logger.error("店铺[{}]第{}页数据获取失败，已达到最大重试次数{}，错误: {}", 
                                    shop.getShopName(), pageNo, PAGE_RETRY_ATTEMPTS, errorMsg);
                            } else {
                                // 计算下一次重试的延迟时间（指数退避）
                                apiRetryDelay = apiRetryDelay * 2;
                                logger.warn("店铺[{}]第{}页数据获取失败，将在{}ms后进行第{}次重试, 错误: {}", 
                                    shop.getShopName(), pageNo, apiRetryDelay, apiRetryAttempts + 1, errorMsg);
                                
                                try {
                                    Thread.sleep(apiRetryDelay);
                                } catch (InterruptedException ie) {
                                    Thread.currentThread().interrupt();
                                    throw new RuntimeException("API重试等待被中断", ie);
                                }
                            }
                        }
                    } catch (Exception e) {
                        apiRetryAttempts++;
                        if (apiRetryAttempts >= PAGE_RETRY_ATTEMPTS) {
                            logger.error("店铺[{}]第{}页数据获取异常，已达到最大重试次数{}", 
                                shop.getShopName(), pageNo, PAGE_RETRY_ATTEMPTS, e);
                            throw e; // 重新抛出异常，会被外层捕获
                        } else {
                            // 计算下一次重试的延迟时间（指数退避）
                            apiRetryDelay = apiRetryDelay * 2;
                            logger.warn("店铺[{}]第{}页数据获取异常，将在{}ms后进行第{}次重试", 
                                shop.getShopName(), pageNo, apiRetryDelay, apiRetryAttempts + 1);
                            
                            try {
                                Thread.sleep(apiRetryDelay);
                            } catch (InterruptedException ie) {
                                Thread.currentThread().interrupt();
                                throw new RuntimeException("API重试等待被中断", ie);
                            }
                        }
                    }
                }
                
                // 如果重试后仍然失败，跳出循环
                if (!apiCallSuccess) {
                    logger.error("店铺[{}]第{}页数据获取失败，重试{}次后仍然失败，中止同步过程，已同步商品数量: {}", 
                        shop.getShopName(), pageNo, PAGE_RETRY_ATTEMPTS, totalProducts);
                    
                    // 记录更详细的错误信息用于诊断
                    if (jsonResult != null) {
                        logger.error("最后一次API调用返回: errorCode={}, errorMsg={}, success={}", 
                            jsonResult.getInteger("errorCode"), 
                            jsonResult.getString("errorMsg"),
                            jsonResult.getBooleanValue("success"));
                    }
                    break;
                }
                
                // 解析数据
                JSONObject data = jsonResult.getJSONObject("result");
                if (data != null) {
                    // 保存API返回的total总数
                    Integer total = data.getInteger("total");
                    if (totalItems == null) {
                        totalItems = total; // 只在第一页时记录total值
                        logger.info("店铺[{}]API返回商品总数: {}", shop.getShopName(), totalItems);
                    } else if (total != null && !total.equals(totalItems)) {
                        logger.warn("店铺[{}]不同页返回的商品总数不一致: 之前={}, 当前={}", 
                            shop.getShopName(), totalItems, total);
                    }
                    
                    if (total != null && total > 0) {
                        JSONArray subOrderList = data.getJSONArray("subOrderList");
                        if (subOrderList != null && !subOrderList.isEmpty()) {
                            // 遍历每个商品
                            for (int i = 0; i < subOrderList.size(); i++) {
                                JSONObject product = subOrderList.getJSONObject(i);
                                totalProducts++;
                                
                                // 优先从skuQuantityDetailList获取销量并累加 - 这是每个SKU的实际销量
                                boolean detailDataProcessed = false;
                                JSONArray skuQuantityDetailList = product.getJSONArray("skuQuantityDetailList");
                                if (skuQuantityDetailList != null && !skuQuantityDetailList.isEmpty()) {
                                    int skuTodaySales = 0;
                                    int skuLastWeekSales = 0;
                                    int skuLastMonthSales = 0;
                                    
                                    for (int j = 0; j < skuQuantityDetailList.size(); j++) {
                                        JSONObject skuDetail = skuQuantityDetailList.getJSONObject(j);
                                        skuTodaySales += skuDetail.getIntValue("todaySaleVolume");
                                        skuLastWeekSales += skuDetail.getIntValue("lastSevenDaysSaleVolume");
                                        skuLastMonthSales += skuDetail.getIntValue("lastThirtyDaysSaleVolume");
//                                        logger.debug("SKU[{}]销量数据: 今日={}, 周={}, 月={}",
//                                            skuDetail.getString("className"),
//                                            skuDetail.getIntValue("todaySaleVolume"),
//                                            skuDetail.getIntValue("lastSevenDaysSaleVolume"),
//                                            skuDetail.getIntValue("lastThirtyDaysSaleVolume"));
                                    }
                                    
                                    todaySales += skuTodaySales;
                                    lastWeekSales += skuLastWeekSales;
                                    lastMonthSales += skuLastMonthSales;
                                    detailDataProcessed = true;
                                    
//                                    logger.info("商品[{}]的skuQuantityDetailList统计: 今日={}, 周={}, 月={}",
//                                        product.getString("productName"), skuTodaySales, skuLastWeekSales, skuLastMonthSales);
                                }
                                
                                // 如果没有获取到明细数据，则尝试从skuQuantityTotalInfo获取
                                if (!detailDataProcessed) {
                                    JSONObject skuQuantityTotalInfo = product.getJSONObject("skuQuantityTotalInfo");
                                    if (skuQuantityTotalInfo != null) {
                                        // 累加销量数据
                                        int totalTodaySales = skuQuantityTotalInfo.getIntValue("todaySaleVolume");
                                        int totalLastWeekSales = skuQuantityTotalInfo.getIntValue("lastSevenDaysSaleVolume");
                                        int totalLastMonthSales = skuQuantityTotalInfo.getIntValue("lastThirtyDaysSaleVolume");
                                        
                                        todaySales += totalTodaySales;
                                        lastWeekSales += totalLastWeekSales;
                                        lastMonthSales += totalLastMonthSales;
                                        
                                        logger.info("商品[{}]的skuQuantityTotalInfo数据: 今日={}, 周={}, 月={}", 
                                            product.getString("productName"), totalTodaySales, totalLastWeekSales, totalLastMonthSales);
                                    } else {
                                        logger.warn("商品[{}]无法获取销量数据，skuQuantityDetailList和skuQuantityTotalInfo均无效", product.getString("productName"));
                                    }
                                }
                            }
                        }
                    }
                    
                    // 检查是否有更多页
                    if (total == null) {
                        logger.warn("店铺[{}]第{}页返回的total为null，停止分页", shop.getShopName(), pageNo);
                        hasMore = false;
                    } else if (pageNo * pageSize >= total) {
                        logger.info("店铺[{}]已获取所有数据，共{}页，总商品数={}", shop.getShopName(), pageNo, total);
                        hasMore = false;
                    } else {
                        pageNo++;
                        logger.info("店铺[{}]继续获取第{}页数据，总商品数={}", shop.getShopName(), pageNo, total);
                    }
                } else {
                    logger.warn("店铺[{}]第{}页返回的result为null，停止分页", shop.getShopName(), pageNo);
                    hasMore = false;
                }
            }
            
            // 保存统计数据
            SalesStatistics statistics = new SalesStatistics();
            statistics.setShopId(shopId);
            statistics.setShopName(shop.getShopName());
            statistics.setStatisticsDate(LocalDate.now());
            statistics.setTodaySales(todaySales);
            statistics.setLastWeekSales(lastWeekSales);
            statistics.setLastMonthSales(lastMonthSales);
            statistics.setTotalProducts(totalProducts);
            statistics.setCreateTime(LocalDateTime.now());
            statistics.setUpdateTime(LocalDateTime.now());
            
            // 记录最终统计结果
            logger.info("店铺[{}]最终统计结果: 商品总数={}, 今日销量={}, 周销量={}, 月销量={}",
                shop.getShopName(), totalProducts, todaySales, lastWeekSales, lastMonthSales);
            
            // 检查是否已存在今天的记录
            SalesStatistics existingStats = salesStatisticsMapper.findByShopIdAndDate(shopId, LocalDate.now());
            if (existingStats != null) {
                statistics.setId(existingStats.getId());
                this.updateById(statistics);
            } else {
                this.save(statistics);
            }
            
            // 设置返回数据
            statisticsVO.setSuccess(true);
            statisticsVO.setTodaySales(todaySales);
            statisticsVO.setLastWeekSales(lastWeekSales);
            statisticsVO.setLastMonthSales(lastMonthSales);
            statisticsVO.setTotalProducts(totalProducts);
            
            response.setCode(200);
            response.setMessage("同步成功");
            response.setData(statisticsVO);
            
        } catch (Exception e) {
            logger.error("同步店铺销售统计数据异常，店铺ID: {}", shopId, e);
            statisticsVO.setSuccess(false);
            statisticsVO.setErrorMsg("同步异常: " + e.getMessage());
            
            response.setCode(500);
            response.setMessage("同步异常: " + e.getMessage());
            response.setData(statisticsVO);
        }
        
        return response;
    }

    @Override
    public SalesStatistics getSalesStatisticsByShopIdAndDate(Long shopId, LocalDate date) {
        return salesStatisticsMapper.findByShopIdAndDate(shopId, date);
    }
} 