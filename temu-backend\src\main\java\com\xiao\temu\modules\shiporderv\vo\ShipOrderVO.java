package com.xiao.temu.modules.shiporderv.vo;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

/**
 * Temu发货单响应VO
 */
@Data
public class ShipOrderVO {
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 错误代码
     */
    private Integer errorCode;
    
    /**
     * 错误信息
     */
    private String errorMsg;
    
    /**
     * API返回结果
     */
    private JSONObject result;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺名称
     */
    private String shopName;
    
    /**
     * 店铺备注
     */
    private String shopRemark;
} 