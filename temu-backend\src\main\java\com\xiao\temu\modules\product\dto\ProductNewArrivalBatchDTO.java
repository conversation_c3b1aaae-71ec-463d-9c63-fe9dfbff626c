package com.xiao.temu.modules.product.dto;

import lombok.Data;

import java.util.List;

/**
 * 商品上新批量保存DTO
 */
@Data
public class ProductNewArrivalBatchDTO {
    
    /**
     * 店铺Temu平台ID
     */
    private String mallId;
    
    /**
     * 商品列表
     */
    private List<ProductDTO> products;
    
    /**
     * 商品DTO
     */
    @Data
    public static class ProductDTO {
        /**
         * 商品ID
         */
        private Long productId;
        
        /**
         * 商品goods_id
         */
        private Long goodsId;
        
        /**
         * 商品名称
         */
        private String productName;
        
        /**
         * 主SKC ID (可选)
         */
        private Long skcId;
        
        /**
         * SKC列表
         */
        private List<SkcDTO> skcs;
    }
    
    /**
     * SKC DTO
     */
    @Data
    public static class SkcDTO {
        /**
         * SKC ID
         */
        private Long skcId;
        
        /**
         * 商品goods_skc_id
         */
        private Long goodsSkcId;
        
        /**
         * select_id
         */
        private Long selectId;
        
        /**
         * 颜色名称
         */
        private String colorName;
        
        /**
         * 外部编码
         */
        private String extCode;
        
        /**
         * 时间状态
         */
        private StatusTimeDTO statusTime;
    }
    
    /**
     * 时间状态DTO
     */
    @Data
    public static class StatusTimeDTO {
        /**
         * 核价时间
         */
        private Long priceVerificationTime;
        
        /**
         * 加入站点时间
         */
        private Long addedToSiteTime;
        
        /**
         * 样品发布完成时间
         */
        private Long samplePostingFinishedTime;
        
        /**
         * 选品时间
         */
        private Long selectedTime;
        
        /**
         * 首次采购时间
         */
        private Long firstPurchaseTime;
        
        /**
         * 创建时间
         */
        private Long createdTime;
        
        /**
         * 质检完成时间
         */
        private Long qcCompletedTime;
    }
} 