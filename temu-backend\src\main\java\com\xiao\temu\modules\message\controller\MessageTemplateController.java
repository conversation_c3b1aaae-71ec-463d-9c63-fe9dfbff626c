package com.xiao.temu.modules.message.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.common.response.PageRequest;
import com.xiao.temu.modules.message.entity.SysMessageTemplate;
import com.xiao.temu.modules.message.service.MessageTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;

/**
 * 消息模板控制器
 */
@Slf4j
@RestController
@RequestMapping("/message-templates")
@Tag(name = "消息模板管理", description = "消息模板管理相关接口")
public class MessageTemplateController {

    @Autowired
    private MessageTemplateService messageTemplateService;

    /**
     * 查询模板列表
     */
    @GetMapping
    @Operation(summary = "查询模板列表", description = "分页查询消息模板列表")
    @PreAuthorize("hasAuthority('message:template:query')")
    public ApiResponse<IPage<SysMessageTemplate>> getTemplateList(
            PageRequest pageRequest,
            @Parameter(description = "模板名称") @RequestParam(required = false) String templateName,
            @Parameter(description = "模板类型") @RequestParam(required = false) String templateType,
            @Parameter(description = "状态") @RequestParam(required = false) String status) {
        
        IPage<SysMessageTemplate> page = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        LambdaQueryWrapper<SysMessageTemplate> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.isNotBlank(templateName)) {
            queryWrapper.like(SysMessageTemplate::getTemplateName, templateName);
        }
        
        if (StringUtils.isNotBlank(templateType)) {
            queryWrapper.eq(SysMessageTemplate::getTemplateType, templateType);
        }
        
        if (StringUtils.isNotBlank(status)) {
            queryWrapper.eq(SysMessageTemplate::getStatus, status);
        }
        
        queryWrapper.orderByDesc(SysMessageTemplate::getCreateTime);
        
        return ApiResponse.success(messageTemplateService.page(page, queryWrapper));
    }

    /**
     * 获取模板详情
     */
    @GetMapping("/{templateId}")
    @Operation(summary = "获取模板详情", description = "获取指定模板的详细信息")
    @PreAuthorize("hasAuthority('message:template:query')")
    public ApiResponse<SysMessageTemplate> getTemplate(
            @Parameter(description = "模板ID") @PathVariable Long templateId) {
        return ApiResponse.success(messageTemplateService.getById(templateId));
    }

    /**
     * 添加模板
     */
    @PostMapping
    @Operation(summary = "添加模板", description = "添加新的消息模板")
    @PreAuthorize("hasAuthority('message:template:add')")
    public ApiResponse<Boolean> addTemplate(@RequestBody @Valid SysMessageTemplate template) {
        // 检查模板编码是否已存在
        LambdaQueryWrapper<SysMessageTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysMessageTemplate::getTemplateCode, template.getTemplateCode());
        
        if (messageTemplateService.count(queryWrapper) > 0) {
            return ApiResponse.error("模板编码已存在");
        }
        
        template.setCreateTime(LocalDateTime.now());
        return ApiResponse.success(messageTemplateService.save(template));
    }

    /**
     * 修改模板
     */
    @PutMapping("/{templateId}")
    @Operation(summary = "修改模板", description = "修改已有的消息模板")
    @PreAuthorize("hasAuthority('message:template:edit')")
    public ApiResponse<Boolean> updateTemplate(
            @Parameter(description = "模板ID") @PathVariable Long templateId,
            @RequestBody @Valid SysMessageTemplate template) {
        
        template.setTemplateId(templateId);
        template.setUpdateTime(LocalDateTime.now());
        return ApiResponse.success(messageTemplateService.updateById(template));
    }

    /**
     * 删除模板
     */
    @DeleteMapping("/{templateId}")
    @Operation(summary = "删除模板", description = "删除指定的消息模板")
    @PreAuthorize("hasAuthority('message:template:remove')")
    public ApiResponse<Boolean> deleteTemplate(
            @Parameter(description = "模板ID") @PathVariable Long templateId) {
        return ApiResponse.success(messageTemplateService.removeById(templateId));
    }

    /**
     * 启用/禁用模板
     */
    @PutMapping("/{templateId}/status/{status}")
    @Operation(summary = "更新模板状态", description = "启用或禁用消息模板")
    @PreAuthorize("hasAuthority('message:template:edit')")
    public ApiResponse<Boolean> updateTemplateStatus(
            @Parameter(description = "模板ID") @PathVariable Long templateId,
            @Parameter(description = "状态") @PathVariable String status) {
        
        SysMessageTemplate template = new SysMessageTemplate();
        template.setTemplateId(templateId);
        template.setStatus(status);
        template.setUpdateTime(LocalDateTime.now());
        
        return ApiResponse.success(messageTemplateService.updateById(template));
    }
} 