package com.xiao.temu.modules.violation.dto;

import com.xiao.temu.common.response.PageRequest;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 店铺违规信息查询条件DTO
 */
@Data
public class ShopViolationInfoQueryDTO extends PageRequest {
    
    /**
     * 当前页码
     */
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    private Integer pageSize = 10;
    
    /** 店铺ID */
    private Long shopId;
    
    /** 店铺ID列表，支持多个店铺查询 */
    private List<Long> shopIds;
    
    /** 违规编号 */
    private String punishSn;
    
    /** 备货单号 */
    private String subPurchaseOrderSn;
    
    /** 违规状态码 */
    private Integer punishStatus;
    
    /** 违规发起时间范围-开始 */
    private LocalDateTime violationTimeBegin;
    
    /** 违规发起时间范围-结束 */
    private LocalDateTime violationTimeEnd;
    
    /** 运营组ID */
    private Long groupId;
    
    /**
     * 数据权限SQL
     */
    private String data_scope;
} 