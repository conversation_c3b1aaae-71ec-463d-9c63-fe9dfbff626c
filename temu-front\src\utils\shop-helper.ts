import { getUserAccessibleShops } from '@/api/shop'
import type { Shop } from '@/types/refund'

/**
 * 获取用户可访问的店铺列表（公共方法）
 * 封装了店铺数据获取逻辑，可以在任何页面使用
 * @returns Promise<Shop[]> 店铺列表
 */
export async function fetchUserAccessibleShops(): Promise<Shop[]> {
  try {
    const result = await getUserAccessibleShops()
    // 处理响应数据
    if (result && Array.isArray(result)) {
      return result
    } else if (result && result.data && Array.isArray(result.data)) {
      return result.data
    } else {
      return []
    }
  } catch (error) {
    console.error('获取可访问店铺列表失败:', error)
    return []
  }
}

/**
 * 根据店铺ID获取店铺名称
 * @param shopId 店铺ID
 * @param shops 店铺列表
 * @returns 店铺名称
 */
export function getShopNameById(shopId: number | undefined, shops: Shop[]): string {
  if (!shopId || !shops || shops.length === 0) {
    return ''
  }
  
  const shop = shops.find(item => item.shopId === shopId)
  return shop ? shop.shopName : ''
} 