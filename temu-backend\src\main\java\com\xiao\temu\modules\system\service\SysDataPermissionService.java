package com.xiao.temu.modules.system.service;

import com.xiao.temu.modules.system.dto.SysDataPermissionDTO;
import java.util.List;

/**
 * 数据权限服务接口
 */
public interface SysDataPermissionService {
    
    /**
     * 获取角色的数据权限
     *
     * @param roleId 角色ID
     * @return 数据权限
     */
    SysDataPermissionDTO getByRoleId(Long roleId);
    
    /**
     * 获取所有角色的数据权限
     *
     * @return 数据权限列表
     */
    List<SysDataPermissionDTO> listAll();
    
    /**
     * 获取用户的数据权限
     *
     * @param userId 用户ID
     * @return 数据权限
     */
    List<SysDataPermissionDTO> getByUserId(Long userId);
    
    /**
     * 设置角色数据权限
     *
     * @param dataPermission 数据权限
     * @return 影响行数
     */
    int setDataPermission(SysDataPermissionDTO dataPermission);
    
    /**
     * 获取用户最高数据权限
     * 
     * @param userId 用户ID
     * @return 最高数据权限类型（0:本人 1:本组 2:全部）
     */
    String getUserMaxDataPermission(Long userId);
} 