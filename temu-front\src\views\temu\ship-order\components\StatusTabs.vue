<template>
  <div class="status-tabs">
    <!-- 状态标签栏 -->
    <div class="tabs-wrapper">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick" type="border-card">
        <el-tab-pane label="待装箱发货" name="0"></el-tab-pane>
        <el-tab-pane label="待仓库发货" name="1"></el-tab-pane>
        <el-tab-pane label="已收货" name="2"></el-tab-pane>
        <el-tab-pane label="已取消" name="5"></el-tab-pane>
        <el-tab-pane label="部分收货" name="6"></el-tab-pane>
        <el-tab-pane label="全部" name="all"></el-tab-pane>
      </el-tabs>
    </div>
    
    <!-- 操作按钮区域 - 移至标签页下方 -->
    <div class="action-buttons-row">
      <div class="selected-count" v-if="selectedRows.length > 0">已选：{{ selectedRows.length }}</div>
      <div class="buttons-group">
        <!-- 使用新的标签打印组件 -->
        <package-label-printer :selected-rows="selectedRows" />
        <package-info-printer :selected-rows="selectedRows" />
        <!-- 使用新的导出组件替换原有的导出按钮 -->
        <export-button :selected-rows="selectedRows" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Download } from '@element-plus/icons-vue'
import type { ShipOrder } from '@/types/shipOrder'
import PackageLabelPrinter from './PackageLabelPrinter.vue'
import PackageInfoPrinter from './PackageInfoPrinter.vue'
import ExportButton from './ExportButton.vue'

// 定义props，接收选中行数据
const props = defineProps({
  selectedRows: {
    type: Array as () => ShipOrder[],
    default: () => []
  }
})

// 定义emit
const emit = defineEmits(['statusChange'])

// 默认选中"全部"标签
const activeTab = ref('all')

// 处理标签点击事件
const handleTabClick = () => {
  // 当选中"全部"时，不传递status参数
  const status = activeTab.value === 'all' ? undefined : parseInt(activeTab.value)
  emit('statusChange', status)
}
</script>

<style scoped>
.status-tabs {
  margin-bottom: 0;
}

.tabs-wrapper {
  background-color: #f5f7fa;
  border-bottom: none;
}

.action-buttons-row {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  background-color: #fff;
  border-bottom: none;
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
}

.selected-count {
  margin-left: 8px;
  margin-right: 15px;
  color: #606266;
  font-size: 14px;
}

.buttons-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.export-button {
  /* 移除margin */
}

.action-button {
  font-size: 12px;
  height: 28px;
  padding: 0 12px;
  border-radius: 3px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  color: #606266;
}

.button-icon {
  margin-right: 4px;
  font-size: 12px;
}

/* 标签页样式重置 */
.status-tabs :deep(.el-tabs__header) {
  margin-bottom: 0;
}

.status-tabs :deep(.el-tabs__nav) {
  border: none;
}

.status-tabs :deep(.el-tabs--border-card) {
  box-shadow: none;
  border: none;
}

.status-tabs :deep(.el-tabs--border-card > .el-tabs__header) {
  background-color: #f5f7fa;
  border: none;
}

.status-tabs :deep(.el-tabs--border-card > .el-tabs__header .el-tabs__item) {
  border: none;
  margin: 0 5px;
  height: 40px;
  line-height: 40px;
}

.status-tabs :deep(.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active) {
  color: #409eff;
  background-color: #fff;
  border-bottom: 2px solid #409eff;
}

.status-tabs :deep(.el-tabs--border-card > .el-tabs__content) {
  display: none;
}
</style> 