package com.xiao.temu.modules.message.websocket;

import com.xiao.temu.common.constant.MessageConstants;
import com.xiao.temu.modules.message.entity.SysMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 消息通知服务
 */
@Slf4j
@Service
public class MessageNotificationService {

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    /**
     * 发送系统广播消息
     *
     * @param message 消息对象
     */
    public void sendBroadcastMessage(SysMessage message) {
        Map<String, Object> payload = createMessagePayload(message);
        messagingTemplate.convertAndSend(MessageConstants.WS_TOPIC_MESSAGE, payload);
        log.info("发送系统广播消息：{}", message.getTitle());
    }

    /**
     * 发送点对点消息
     *
     * @param message 消息对象
     * @param userId  接收用户ID
     */
    public void sendUserMessage(SysMessage message, Long userId) {
        Map<String, Object> payload = createMessagePayload(message);
        String destination = "/queue/message";
        messagingTemplate.convertAndSendToUser(String.valueOf(userId), destination, payload);
        log.info("发送点对点消息，用户ID：{}，消息标题：{}", userId, message.getTitle());
    }

    /**
     * 批量发送点对点消息
     *
     * @param message 消息对象
     * @param userIds 接收用户ID列表
     */
    public void sendUserMessages(SysMessage message, List<Long> userIds) {
        Map<String, Object> payload = createMessagePayload(message);
        String destination = "/queue/message";

        for (Long userId : userIds) {
            messagingTemplate.convertAndSendToUser(String.valueOf(userId), destination, payload);
        }
        log.info("批量发送点对点消息，用户数量：{}，消息标题：{}", userIds.size(), message.getTitle());
    }

    /**
     * 创建消息载荷
     *
     * @param message 消息对象
     * @return 消息载荷
     */
    private Map<String, Object> createMessagePayload(SysMessage message) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("messageId", message.getMessageId());
        payload.put("title", message.getTitle());
        payload.put("content", message.getContent());
        payload.put("messageType", message.getMessageType());
        payload.put("importance", message.getImportance());
        payload.put("createTime", message.getCreateTime());
        return payload;
    }
} 