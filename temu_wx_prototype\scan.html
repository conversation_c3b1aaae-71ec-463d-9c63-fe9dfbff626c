<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扫码 - TEMU备货单追踪</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f7f7f7;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }
        .wx-container {
            max-width: 414px;
            min-height: 100vh;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 0;
            box-shadow: 0 0 10px rgba(0,0,0,0.05);
            overflow: hidden;
            position: relative;
        }
        @media (min-width: 500px) {
            .wx-container {
                min-height: 90vh;
                margin: 20px auto;
                border-radius: 30px;
            }
        }
        .wx-btn-primary {
            background-color: #07c160;
            color: white;
        }
        .wx-btn-primary:hover {
            background-color: #06ad56;
        }
        .scanner-container {
            position: relative;
            width: 280px;
            height: 280px;
            margin: 0 auto;
            overflow: hidden;
        }
        .scanner-frame {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 2px solid #07c160;
            box-sizing: border-box;
            border-radius: 12px;
        }
        .scan-line {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #07c160;
            animation: scan 2s linear infinite;
        }
        @keyframes scan {
            0% { top: 0; }
            100% { top: 100%; }
        }
        .scanner-corner {
            position: absolute;
            width: 20px;
            height: 20px;
            border-color: #07c160;
            border-style: solid;
            border-width: 0;
        }
        .corner-top-left {
            top: -2px;
            left: -2px;
            border-top-width: 4px;
            border-left-width: 4px;
            border-top-left-radius: 8px;
        }
        .corner-top-right {
            top: -2px;
            right: -2px;
            border-top-width: 4px;
            border-right-width: 4px;
            border-top-right-radius: 8px;
        }
        .corner-bottom-left {
            bottom: -2px;
            left: -2px;
            border-bottom-width: 4px;
            border-left-width: 4px;
            border-bottom-left-radius: 8px;
        }
        .corner-bottom-right {
            bottom: -2px;
            right: -2px;
            border-bottom-width: 4px;
            border-right-width: 4px;
            border-bottom-right-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="wx-container">
        <div class="flex flex-col h-screen">
            <header class="py-4 px-4 flex justify-between items-center border-b border-gray-200">
                <div class="flex items-center">
                    <a href="home.html" class="mr-2">
                        <i class="fas fa-arrow-left text-gray-600"></i>
                    </a>
                    <h1 class="text-xl font-medium">扫描二维码</h1>
                </div>
                <div class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full">烧花工</div>
            </header>
            
            <div class="flex-grow flex flex-col justify-center p-6">
                <div class="scanner-container mb-8">
                    <div class="scanner-frame">
                        <div class="scan-line"></div>
                        <div class="scanner-corner corner-top-left"></div>
                        <div class="scanner-corner corner-top-right"></div>
                        <div class="scanner-corner corner-bottom-left"></div>
                        <div class="scanner-corner corner-bottom-right"></div>
                    </div>
                </div>
                
                <div class="text-center mb-6">
                    <p class="text-gray-600 mb-2">请将备货单上的二维码对准扫描框</p>
                    <a href="progress.html" class="text-green-600">
                        <i class="fas fa-lightbulb mr-1"></i> 
                        <span>点击此处进入演示</span>
                    </a>
                </div>
                
                <div class="bg-gray-50 p-4 rounded-lg mb-4 hidden" id="result-box">
                    <h3 class="font-medium mb-2">扫描结果</h3>
                    <div class="text-gray-700">
                        <p>备货单号：<span class="font-medium">TM20230616001</span></p>
                        <p>产品名称：女式连衣裙</p>
                        <p>订单数量：200件</p>
                    </div>
                    <div class="mt-3">
                        <a href="progress.html" class="block w-full py-2 bg-[#07c160] text-white text-center rounded-md font-medium">
                            进入处理
                        </a>
                    </div>
                </div>
                
                <div class="text-center">
                    <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none">
                        <i class="fas fa-image mr-2"></i>
                        从相册选择
                    </button>
                </div>
            </div>
            
            <footer class="bg-white border-t border-gray-200">
                <div class="grid grid-cols-3 text-center">
                    <a href="home.html" class="py-3 flex flex-col items-center text-gray-500">
                        <i class="fas fa-home text-xl"></i>
                        <span class="text-xs mt-1">首页</span>
                    </a>
                    <a href="scan.html" class="py-3 flex flex-col items-center text-green-600">
                        <i class="fas fa-qrcode text-xl"></i>
                        <span class="text-xs mt-1">扫码</span>
                    </a>
                    <a href="worklist.html" class="py-3 flex flex-col items-center text-gray-500">
                        <i class="fas fa-list-ul text-xl"></i>
                        <span class="text-xs mt-1">列表</span>
                    </a>
                </div>
            </footer>
        </div>
    </div>
    
    <script>
        // 演示用，点击扫描区域显示结果
        document.querySelector('.scanner-container').addEventListener('click', function() {
            document.getElementById('result-box').classList.remove('hidden');
        });
    </script>
</body>
</html> 