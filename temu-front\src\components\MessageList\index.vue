<template>
  <div class="message-list">
    <el-empty v-if="!loading && (!messages || messages.length === 0)" description="暂无消息" />
    
    <el-skeleton v-if="loading" :rows="3" animated />
    
    <template v-else>
      <div 
        v-for="message in messages" 
        :key="message.messageId" 
        class="message-item" 
        :class="{ 'unread': message.readStatus === '0' }"
        :data-read-status="message.readStatus"
        @click="showMessageDetail(message)"
      >
        <div class="message-icon" :class="getMessageTypeClass(message.messageType)">
          <el-icon v-if="message.messageType === '1'"><Bell /></el-icon>
          <el-icon v-else-if="message.messageType === '2'"><AlarmClock /></el-icon>
          <el-icon v-else-if="message.messageType === '3'"><Shop /></el-icon>
        </div>
        
        <div class="message-content">
          <div class="message-title">
            <span>{{ message.title }}</span>
            <div class="message-tags">
              <el-tag 
                v-if="message.importance === '3'" 
                size="small" 
                type="danger"
              >紧急</el-tag>
              <el-tag 
                v-else-if="message.importance === '2'" 
                size="small" 
                type="warning"
              >重要</el-tag>
            </div>
          </div>
          
          <div class="message-footer">
            <span class="message-time">{{ formatMessageTime(message.createTime) }}</span>
          </div>
        </div>
      </div>
    </template>
    
    <!-- 消息详情弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="currentMessage?.title || '消息详情'"
      width="1000px"
      :close-on-click-modal="true"
      :show-close="true"
    >
      <div class="message-detail-content" v-if="currentMessage">
        <div class="message-meta">
          <div class="message-meta-item">
            <span class="label">类型:</span>
            <span class="value">
              {{ getMessageTypeName(currentMessage.messageType) }}
              <el-tag 
                v-if="currentMessage.importance === '3'" 
                size="small" 
                type="danger"
              >紧急</el-tag>
              <el-tag 
                v-else-if="currentMessage.importance === '2'" 
                size="small" 
                type="warning"
              >重要</el-tag>
            </span>
          </div>
          <div class="message-meta-item">
            <span class="label">时间:</span>
            <span class="value">{{ formatMessageTime(currentMessage.createTime) }}</span>
          </div>
          <div class="message-meta-item" v-if="currentMessage.readTime">
            <span class="label">已读时间:</span>
            <span class="value">{{ formatMessageTime(currentMessage.readTime) }}</span>
          </div>
        </div>
        
        <div class="message-detail-body" v-html="formatMessageContent(currentMessage.content)">
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button 
            v-if="currentMessage && currentMessage.readStatus === '0'" 
            type="primary" 
            @click="handleMarkAsRead(currentMessage)"
          >标为已读</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Bell, AlarmClock, Shop } from '@element-plus/icons-vue'
import { useMessageStore } from '@/store'
import type { Message } from '@/types/message'
import { formatTime as formatDatetime } from '@/utils/format'
import { ElMessageBox } from 'element-plus'

// 属性定义
const props = defineProps({
  messages: {
    type: Array as () => Message[],
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'full', // 'full' 或 'preview'
    validator: (value: string) => ['full', 'preview'].includes(value)
  }
})

// 事件
const emit = defineEmits(['view-detail', 'reload'])

// 获取消息Store
const messageStore = useMessageStore()

// 弹窗控制
const dialogVisible = ref(false)
const currentMessage = ref<Message | null>(null)

// 根据消息类型获取图标样式类
const getMessageTypeClass = (type: string) => {
  switch (type) {
    case '1': return 'system-message'
    case '2': return 'task-message'
    case '3': return 'shop-message'
    default: return ''
  }
}

// 获取消息类型名称
const getMessageTypeName = (type: string) => {
  switch (type) {
    case '1': return '系统消息'
    case '2': return '任务消息'
    case '3': return '店铺消息'
    default: return '其他消息'
  }
}

// 格式化时间
const formatMessageTime = (time: string | number) => {
  if (!time) return ''
  
  // 处理时间戳格式
  if (typeof time === 'number' || !isNaN(Number(time))) {
    // 如果是13位时间戳，直接使用；如果是10位时间戳，转换为13位
    const timestamp = String(time).length === 13 ? Number(time) : Number(time) * 1000
    const date = new Date(timestamp)
    
    // 格式化为 YYYY-MM-DD HH:MM:SS
    return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}:${padZero(date.getSeconds())}`
  }
  
  // 如果是日期字符串，使用原有工具函数处理
  return formatDatetime(time.toString())
}

// 格式化消息内容，识别并转换时间戳
const formatMessageContent = (content: string) => {
  if (!content) return '';
  
  // 匹配常见的时间戳模式（例如"时间：1748676191000"）
  const processedContent = content.replace(/([：:]\s*)(\d{13})(?!\d)/g, (match, prefix, timestamp) => {
    const formattedTime = formatMessageTime(Number(timestamp));
    return `${prefix}${formattedTime}`;
  });
  
  return processedContent;
}

// 补零函数
const padZero = (num: number) => {
  return num < 10 ? `0${num}` : num
}

// 显示消息详情
const showMessageDetail = (message: Message) => {
  currentMessage.value = message
  dialogVisible.value = true
  
  // 如果是未读消息，标记为已读
  if (message.readStatus === '0') {
    handleMarkAsRead(message, false) // 不刷新列表
  }
}

// 标记消息为已读
const handleMarkAsRead = async (message: Message, closeDialog = true) => {
  if (!message) return
  
  try {
    // 确保 messageId 被转换为 number 类型
    const messageId = typeof message.messageId === 'string' ? parseInt(message.messageId, 10) : message.messageId
    await messageStore.markMessageRead(messageId)
    
    // 更新当前消息状态
    if (currentMessage.value && currentMessage.value.messageId === message.messageId) {
      currentMessage.value.readStatus = '1'
      currentMessage.value.readTime = new Date().toISOString()
    }
    
    // 通知父组件重新加载
    emit('reload')
    
    // 如果需要关闭弹窗
    if (closeDialog) {
      dialogVisible.value = false
    }
  } catch (error) {
    console.error('标记已读失败', error)
  }
}

// 删除消息
const handleDelete = async (message: Message) => {
  if (!message) return
  
  try {
    await ElMessageBox.confirm('确定要删除该消息吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 确保 messageId 被转换为 number 类型
    const messageId = typeof message.messageId === 'string' ? parseInt(message.messageId, 10) : message.messageId
    await messageStore.deleteMessage(messageId)
    
    // 关闭弹窗
    dialogVisible.value = false
    
    // 通知父组件重新加载
    emit('reload')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除消息失败', error)
    }
  }
}
</script>

<style scoped>
.message-list {
  width: 100%;
}

.message-item {
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
  border-left: 3px solid transparent;
}

.message-item:hover {
  background-color: #f5f7fa;
}

.message-item.unread {
  background-color: #f0f7ff;
  border-left: 3px solid #409eff;
  position: relative;
}

.message-item.unread:hover {
  background-color: #e6f1ff;
}

/* 未读消息中的标题更加醒目 */
.message-item.unread .message-title {
  font-weight: bold;
  color: #303133;
}

.message-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #fff;
}

.system-message {
  background-color: #409eff;
}

.task-message {
  background-color: #67c23a;
}

.shop-message {
  background-color: #e6a23c;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-title {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.message-tags {
  display: flex;
  align-items: center;
  gap: 5px;
}

.message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.message-time {
  flex-shrink: 0;
}

/* 消息详情弹窗样式 */
.message-detail-content {
  padding: 10px 0;
}

.message-meta {
  margin-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.message-meta-item {
  margin-bottom: 8px;
  display: flex;
  font-size: 14px;
}

.message-meta-item .label {
  color: #909399;
  width: 70px;
  text-align: right;
  margin-right: 10px;
}

.message-meta-item .value {
  color: #606266;
  display: flex;
  align-items: center;
  gap: 5px;
}

.message-detail-body {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
}

/* HTML内容样式 */
.message-detail-body :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 10px 0;
}

.message-detail-body :deep(th),
.message-detail-body :deep(td) {
  border: 1px solid #dcdfe6;
  padding: 8px;
  text-align: left;
}

.message-detail-body :deep(th) {
  background-color: #f5f7fa;
  font-weight: bold;
}

.message-detail-body :deep(h3) {
  font-size: 16px;
  margin: 15px 0 10px;
  color: #303133;
}

.message-detail-body :deep(p) {
  margin: 10px 0;
  line-height: 1.6;
}

.message-detail-body :deep(strong) {
  font-weight: bold;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 