package com.xiao.temu.modules.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 消息实体类
 */
@Data
@TableName("sys_message")
public class SysMessage {
    
    /**
     * 消息ID
     */
    @TableId(type = IdType.AUTO)
    private Long messageId;
    
    /**
     * 消息标题
     */
    private String title;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息类型（1系统消息 2任务提醒 3店铺消息）
     */
    private String messageType;
    
    /**
     * 接收用户ID
     */
    private Long toUserId;
    
    /**
     * 发送用户ID
     */
    private Long fromUserId;
    
    /**
     * 接收对象类型(0全部用户 1指定用户 2指定角色 3运营组)
     */
    private String targetType;
    
    /**
     * 接收对象ID列表,逗号分隔
     */
    private String targetIds;
    
    /**
     * 相关店铺ID
     */
    private Long shopId;
    
    /**
     * 已读状态（0未读 1已读）
     */
    private String readStatus;
    
    /**
     * 重要程度(1普通 2重要 3紧急)
     */
    private String importance;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 发布时间
     */
    private LocalDateTime publishTime;
    
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 阅读时间
     */
    private LocalDateTime readTime;
    
    /**
     * 是否删除(0未删除 1已删除)
     */
    private String deleted;
} 