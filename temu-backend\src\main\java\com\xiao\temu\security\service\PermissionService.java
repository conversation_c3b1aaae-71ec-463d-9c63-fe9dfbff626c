package com.xiao.temu.security.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 权限服务接口
 */
public interface PermissionService {

    /**
     * 获取用户权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    List<String> getPermissionsByUserId(Long userId);

    /**
     * 获取用户角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<String> getRolesByUserId(Long userId);

    /**
     * 获取用户角色与权限的映射关系
     *
     * @param userId 用户ID
     * @return 角色权限映射，key为角色标识，value为权限列表
     */
    Map<String, List<String>> getRolePermissionsByUserId(Long userId);

    /**
     * 获取用户角色标识与角色名称的映射关系
     *
     * @param userId 用户ID
     * @return 角色名称映射，key为角色标识，value为角色名称
     */
    Map<String, String> getRoleNamesByUserId(Long userId);

    /**
     * 判断用户是否有指定权限
     *
     * @param permission 权限标识
     * @return 是否有权限
     */
    boolean hasPermission(String permission);

    /**
     * 判断用户是否有指定角色
     *
     * @param role 角色标识
     * @return 是否有角色
     */
    boolean hasRole(String role);

    /**
     * 重新加载用户权限
     *
     * @param userId 用户ID
     */
    void reloadPermission(Long userId);

    /**
     * 清除所有权限缓存
     */
    void clearAllPermissionCache();

    /**
     * 更新角色权限关系
     *
     * @param roleId 角色ID
     * @param permissions 权限列表
     * @return 是否成功
     */
    boolean updateRolePermissions(Long roleId, Set<Long> permissions);

    /**
     * 获取具有特定权限的用户ID列表
     *
     * @param permission 权限标识
     * @return 用户ID列表
     */
    List<Long> getUserIdsByPermission(String permission);
} 