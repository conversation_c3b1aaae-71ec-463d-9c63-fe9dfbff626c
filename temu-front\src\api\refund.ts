import request from '@/utils/request'
import type { RefundRequestDTO } from '../types/refund'

/**
 * 获取退货包裹列表
 * @param data 请求参数
 * @returns 退货包裹列表数据
 */
export function getRefundPackages(data: RefundRequestDTO) {
  return request({
    url: '/temu/returnDetails/packages',
    method: 'post',
    data
  })
}

/**
 * 获取用户可操作的店铺列表（旧版API，已废弃）
 * @deprecated 请使用getUserAccessibleShops代替
 * @returns 店铺列表数据
 */
export function getUserShops() {
  return request({
    url: '/temu/returnDetails/shops',
    method: 'get'
  })
}

/**
 * 获取用户可访问的店铺列表（公共API）
 * @returns 店铺列表数据
 */
export function getUserAccessibleShops() {
  return request({
    url: '/operation/shop/accessible/shops',
    method: 'get'
  })
} 