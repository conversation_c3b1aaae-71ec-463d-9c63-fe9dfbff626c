package com.xiao.temu.modules.refund.service;

import com.xiao.temu.modules.refund.dto.LocalRefundRequestDTO;
import com.xiao.temu.modules.refund.vo.LocalRefundPackageVO;

/**
 * 本地退货明细服务接口
 */
public interface LocalRefundPackageService {
    
    /**
     * 获取本地退货包裹列表
     * 
     * @param requestDTO 请求参数
     * @param userId 用户ID
     * @return 退货包裹明细VO
     */
    LocalRefundPackageVO getLocalRefundPackageList(LocalRefundRequestDTO requestDTO, Long userId);
} 