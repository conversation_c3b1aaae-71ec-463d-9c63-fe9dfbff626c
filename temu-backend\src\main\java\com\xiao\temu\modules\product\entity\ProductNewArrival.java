package com.xiao.temu.modules.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商品上新基本信息实体类
 */
@Data
@TableName("product_new_arrival")
public class ProductNewArrival {
    
    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /** 店铺ID */
    @TableField("shop_id")
    private Long shopId;
    
    /** 商品ID */
    @TableField("product_id")
    private Long productId;
    
    /** 商品goods_id */
    @TableField("goods_id")
    private Long goodsId;
    
    /** 商品名称 */
    @TableField("product_name")
    private String productName;
    
    /** 主SKC ID */
    @TableField("skc_id")
    private Long skcId;
    
    /** 同步时间 */
    @TableField("sync_time")
    private LocalDateTime syncTime;
    
    /** 创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /** 更新时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;
} 