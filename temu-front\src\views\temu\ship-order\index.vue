<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="ship-order-tabs">
      <el-tab-pane label="JIT发货单" name="jit">
        <JitShipOrderTab :class="{ 'hidden-tab': activeTab !== 'jit' }" :shops="shops" />
      </el-tab-pane>
      <el-tab-pane label="普通发货单" name="normal">
        <NormalShipOrderTab :class="{ 'hidden-tab': activeTab !== 'normal' }" :shops="shops" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useShipOrderStore } from '@/store'
import JitShipOrderTab from './JitShipOrderTab.vue'
import NormalShipOrderTab from './NormalShipOrderTab.vue'
import type { Shop } from '@/types/shipOrder'
import PackageLabelPrinter from './components/PackageLabelPrinter.vue'

// 标签页激活名称
const activeTab = ref('jit')

// 获取store实例
const shipOrderStore = useShipOrderStore()

// 店铺列表
const shops = ref<Shop[]>([])

// 获取店铺列表
const getShopList = async () => {
  try {
    const shopList = await shipOrderStore.getShopList()
    // 确保shopList不为空且是数组才赋值
    if (shopList && Array.isArray(shopList) && shopList.length > 0) {
      shops.value = shopList
      console.log('获取到店铺列表，共', shopList.length, '个店铺')
    } else {
      console.warn('获取到的店铺列表为空或非数组')
    }
  } catch (error) {
    console.error('获取店铺列表失败', error)
  }
}

// 处理标签页点击
const handleTabClick = () => {
  // 标签页切换时不自动发送请求，需要用户手动点击查询按钮
}

// 组件挂载时只获取店铺列表，不请求数据
onMounted(() => {
  getShopList()
})
</script>

<style scoped>
.app-container {
  padding: 15px; /* 减少内边距 */
  /* 添加底部空间，防止内容被固定的分页栏遮挡 */
  padding-bottom: 60px;
}

/* 标签页样式 */
.ship-order-tabs :deep(.el-tabs__content) {
  overflow: visible;
  padding: 0; /* 移除内边距 */
}

/* 使标签页更加紧凑 */
.ship-order-tabs :deep(.el-tabs__header) {
  margin-bottom: 0;
}

/* 隐藏非活动标签页内容但保持组件状态 */
.hidden-tab {
  display: none;
}
</style> 