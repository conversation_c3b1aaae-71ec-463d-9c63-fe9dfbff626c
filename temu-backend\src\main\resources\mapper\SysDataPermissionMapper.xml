<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.system.mapper.SysDataPermissionMapper">

    <resultMap id="DataPermissionResult" type="com.xiao.temu.modules.system.entity.SysDataPermission">
        <id property="id" column="id"/>
        <result property="roleId" column="role_id"/>
        <result property="permissionType" column="permission_type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>
    
    <resultMap id="DataPermissionDTOResult" type="com.xiao.temu.modules.system.dto.SysDataPermissionDTO">
        <id property="id" column="id"/>
        <result property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="permissionType" column="permission_type"/>
        <result property="remark" column="remark"/>
    </resultMap>
    
    <!-- 查询角色的数据权限 -->
    <select id="selectByRoleId" resultMap="DataPermissionDTOResult">
        SELECT dp.*, r.role_name, r.role_key
        FROM sys_data_permission dp
        LEFT JOIN sys_role r ON dp.role_id = r.role_id
        WHERE dp.role_id = #{roleId}
    </select>
    
    <!-- 查询用户的数据权限 -->
    <select id="selectByUserId" resultMap="DataPermissionDTOResult">
        SELECT dp.*, r.role_name, r.role_key
        FROM sys_data_permission dp
        INNER JOIN sys_role r ON dp.role_id = r.role_id
        INNER JOIN sys_user_role ur ON r.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
    </select>
    
    <!-- 更新角色的数据权限 -->
    <update id="updateByRoleId">
        UPDATE sys_data_permission
        SET permission_type = #{permissionType},
            update_time = NOW()
        WHERE role_id = #{roleId}
    </update>
    
</mapper> 