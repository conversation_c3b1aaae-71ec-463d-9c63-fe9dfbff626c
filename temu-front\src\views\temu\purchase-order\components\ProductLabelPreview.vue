<template>
  <el-dialog
    v-model="dialogVisible"
    title="商品标签数据预览"
    width="1500px"
    height="1000px"
    :top="'3vh'"
    destroy-on-close
    :close-on-click-modal="false"
    :append-to-body="true"
    :modal-append-to-body="true"
  >
    <div v-loading="loading" class="product-label-preview">
      <div v-if="error" class="error-message">
        <el-alert
          type="error"
          :title="error"
          :closable="false"
          show-icon
        />
        <!-- 添加重试按钮 -->
        <div class="error-actions">
          <el-button type="primary" size="small" @click="handleRetry">重试</el-button>
        </div>
      </div>
      
      <div v-else-if="labelData && Object.keys(labelData).length > 0" class="label-data-container">
        <!-- 移除店铺选择器 -->
        
        <!-- 标签数据展示 -->
        <div class="label-content">
          <div v-if="combinedLabelList.length > 0" class="label-list">
            <div class="label-items">
              <el-table :data="combinedLabelList" border style="width: 100%;" size="small" max-height="600px" :header-cell-style="{ background: '#f5f7fa', color: '#606266' }">
                <el-table-column label="店铺信息" width="100" fixed="left">
                  <template #default="scope">
                    <div class="shop-info">
                      {{ scope.row.shopName || `店铺 ${scope.row.shopId}` }}
                    </div>
                  </template>
                </el-table-column>
                
                <el-table-column label="商品图片" width="120">
                  <template #default="scope">
                    <div class="product-image-container">
                      <el-image 
                        v-if="scope.row.productPicUrl" 
                        :src="scope.row.productPicUrl" 
                        fit="contain"
                        :preview-src-list="[scope.row.productPicUrl]"
                        class="product-image"
                      />
                      <div v-else class="no-image">暂无图片</div>
                    </div>
                  </template>
                </el-table-column>
                
                <el-table-column prop="productSkuId" label="商品SKU ID" width="120" />
                
                <el-table-column prop="productName" label="商品名称" min-width="250" show-overflow-tooltip />
                
                <el-table-column prop="specInfo" label="商品规格" width="200" show-overflow-tooltip />
                
                <el-table-column prop="extCode" label="商品编码" width="120" show-overflow-tooltip />
                
                <el-table-column label="条码数据" width="200" fixed="right">
                  <template #default="scope">
                    <div v-if="scope.row.labelCode" class="label-code-container">
                      <div class="label-code">条码: {{ scope.row.labelCode }}</div>
                      <div v-if="scope.row.labelUrl" class="label-image-container">
                        <el-image 
                          :src="scope.row.labelUrl" 
                          fit="contain"
                          :preview-src-list="[scope.row.labelUrl]"
                          class="label-image"
                        />
                      </div>
                    </div>
                    <div v-else class="no-label">暂无条码数据</div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <el-empty v-else description="未找到标签数据" />
        </div>
      </div>
      <el-empty v-else description="未获取到标签数据" />
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handlePrintAll" :disabled="!canPrint">批量打印</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
// 导入pdf-helper工具替代直接导入jsPDF和JSBarcode
import { generateProductLabelPDF } from '@/utils/pdf-helper'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  labelData: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'print-all', 'download-label', 'retry'])

// 对话框可见性，用于双向绑定
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 将所有店铺的标签数据合并到一个列表中
const combinedLabelList = computed(() => {
  if (!props.labelData) return []
  
  try {
    console.log('处理标签数据:', props.labelData)
    const allLabels: any[] = []
    
    // 检查是否有shopResultMap
    if (props.labelData.shopResultMap) {
      // 遍历所有店铺数据
      Object.entries(props.labelData.shopResultMap).forEach(([shopId, shopData]: [string, any]) => {
        if (shopData && shopData.success && shopData.data && Array.isArray(shopData.data)) {
          // 处理每个店铺的标签数据
          const shopLabels = processShopLabels(shopData.data, Number(shopId), shopData.shopName || `店铺 ${shopId}`)
          allLabels.push(...shopLabels)
        }
      })
    } else if (props.labelData.data && Array.isArray(props.labelData.data)) {
      // 如果数据直接在根对象的data字段中
      const shopId = props.labelData.shopId || 0
      const shopName = props.labelData.shopName || `店铺 ${shopId}`
      const shopLabels = processShopLabels(props.labelData.data, shopId, shopName)
      allLabels.push(...shopLabels)
    } else {
      // 尝试直接从数据中获取店铺数据
      const shopIds = Object.keys(props.labelData).filter(key => /^\d+$/.test(key))
      shopIds.forEach(shopId => {
        const shopData = props.labelData[shopId]
        if (shopData && shopData.success && shopData.data && Array.isArray(shopData.data)) {
          const shopLabels = processShopLabels(shopData.data, Number(shopId), shopData.shopName || `店铺 ${shopId}`)
          allLabels.push(...shopLabels)
        }
      })
    }
    
    console.log(`合并后共有${allLabels.length}个商品标签数据`)
    return allLabels
  } catch (error) {
    console.error('处理标签数据出错:', error)
    return []
  }
})

// 处理单个店铺的标签数据
const processShopLabels = (data: any[], shopId: number, shopName: string) => {
  return data.map((item: any, index: number) => {
    if (!item) {
      console.warn(`第${index}项数据为空`)
      return {
        shopId,
        shopName
      }
    }
    
    try {
      // 从商品数据中提取需要的信息
      const productDTO = item.productDTO || {}
      const productSkuDTO = item.productSkuDTO || {}
      const productSkcDTO = item.productSkcDTO || {}
      const productSkuLabelCodeDTO = item.productSkuLabelCodeDTO || {}
      
      // 获取图片URL
      let imageUrl = ''
      
      // 先尝试从productSkcImageList获取图片
      if (item.productSkcImageList && item.productSkcImageList.length > 0) {
        // 查找类型为1的图片（主图）
        const mainImage = item.productSkcImageList.find((img: any) => img.imageType === 1)
        if (mainImage) {
          imageUrl = mainImage.imageUrl
        } else if (item.productSkcImageList[0]) {
          imageUrl = item.productSkcImageList[0].imageUrl
        }
      }
      
      // 如果没有从productSkcImageList找到图片，尝试从productSkuImageList获取
      if (!imageUrl && item.productSkuImageList && item.productSkuImageList.length > 0) {
        const mainImage = item.productSkuImageList.find((img: any) => img.imageType === 1)
        if (mainImage) {
          imageUrl = mainImage.imageUrl
        } else if (item.productSkuImageList[0]) {
          imageUrl = item.productSkuImageList[0].imageUrl
        }
      }
      
      // 如果仍然没有找到图片，使用thumbUrl
      if (!imageUrl && productSkuDTO.thumbUrl) {
        imageUrl = productSkuDTO.thumbUrl
      }
      
      // 生成标签URL，如果有labelCode才生成URL
      let labelUrl = ''
      let labelCode = ''
      
      if (productSkuLabelCodeDTO && productSkuLabelCodeDTO.labelCode) {
        // 确保labelCode是字符串类型
        labelCode = String(productSkuLabelCodeDTO.labelCode)
        labelUrl = `https://img.cdnfe.com/product/fancy/label/${labelCode}.png`
      }
      
      // 提取规格信息
      let specInfo = ''
      if (productSkuDTO.productSkuSpec && productSkuDTO.productSkuSpec.specList) {
        specInfo = productSkuDTO.productSkuSpec.specList.map((spec: any) => 
          `${spec.parentSpecName}: ${spec.specName}`
        ).join(', ')
      }
      
      // 提取规格国际化信息（用于打印标签）
      let colorSpec = ''
      let sizeSpec = ''
      
      if (productSkuDTO.productSkuSpecI18nMap && productSkuDTO.productSkuSpecI18nMap.en) {
        // 尝试提取颜色和尺寸信息
        const specs = productSkuDTO.productSkuSpecI18nMap.en;
        
        // 假设第一个规格是颜色，第二个是尺寸
        // 这里需要根据实际数据结构进行调整
        if (Array.isArray(specs) && specs.length > 0) {
          if (specs[0] && specs[0].specName) colorSpec = specs[0].specName;
          if (specs[1] && specs[1].specName) sizeSpec = specs[1].specName;
        }
      }
      
      // 提取制造国信息
      let madeIn = 'Made In China'; // 默认值
      if (productDTO.productOrigin && productDTO.productOrigin.countryName) {
        madeIn = `Made In ${productDTO.productOrigin.countryName}`;
      }
      
      // 判断是否为定制品
      const isCustom = productDTO.isCustom || false;
      
      // 获取SKU编码和货号
      const extCode = productSkuDTO.extCode || productSkcDTO.extCode || '';
      // 确保字符串类型
      const skuCode = isCustom ? `DZ${String(productSkuDTO.productSkuId || '')}` : String(productSkuDTO.productSkuId || '');
      const productSkcId = productSkcDTO.productSkcId || '';
      
      return {
        shopId,
        shopName,
        productSkuId: productSkuDTO.productSkuId || '',
        productSkcId: productSkcId,
        productId: productDTO.productId || '',
        productName: productDTO.productName || '',
        productPicUrl: imageUrl || '',
        specInfo: specInfo,
        supplierName: productDTO.supplierName || '',
        extCode: extCode,
        labelCode: labelCode,
        labelUrl: labelUrl,
        colorSpec: colorSpec,
        sizeSpec: sizeSpec,
        madeIn: madeIn,
        isCustom: isCustom,
        skuCode: skuCode,
        labelData: item // 保存完整的标签数据
      }
    } catch (itemError) {
      console.error(`解析第${index}个商品数据出错:`, itemError)
      return {
        shopId,
        shopName
      }
    }
  })
}

// 是否可以打印
const canPrint = computed(() => {
  return combinedLabelList.value && combinedLabelList.value.length > 0
})

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}

// 批量打印所有标签
const handlePrintAll = async () => {
  if (!combinedLabelList.value || combinedLabelList.value.length === 0) {
    ElMessage.warning('没有可打印的标签数据')
    return
  }
  
  try {
    // 过滤有条码的标签
    const labels = combinedLabelList.value.filter(label => label.labelCode)
    if (labels.length === 0) {
      ElMessage.warning('没有找到有条码数据的商品')
      return
    }
    
    // 显示正在生成PDF的消息
    ElMessage.info(`正在生成PDF文件，总共${labels.length}张标签，每张标签将独立占用一页...`)
    
    // 调用工具函数生成PDF
    const fileName = await generateProductLabelPDF(labels)
    
    ElMessage.success(`PDF文件已生成，共${labels.length}页，每页一个标签，开始下载`)
    
    // 通知父组件打印已完成
    emit('print-all', {
      labels: labels,
      pdfFile: fileName
    })
  } catch (error) {
    console.error('生成PDF标签失败:', error)
    ElMessage.error('生成PDF标签失败，请重试')
  }
}

// 下载单个标签
const downloadLabel = (label: any) => {
  emit('download-label', label)
}

// 重试按钮处理函数
const handleRetry = () => {
  emit('retry')
}
</script>

<style scoped>
.product-label-preview {
  min-height: 600px;
  max-height: 70vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.error-message {
  margin-bottom: 20px;
}

.error-actions {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.label-data-container {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.label-content {
  flex: 1;
}

.label-list {
  margin-top: 0;
}

.shop-info {
  font-weight: bold;
  color: #606266;
  font-size: 12px;
  text-align: center;
}

.product-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
}

.product-image {
  max-height: 80px;
  max-width: 100%;
  object-fit: contain;
}

.no-image {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 12px;
}

.label-code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.label-code {
  font-weight: bold;
  color: #409EFF;
}

.label-image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.label-image {
  height: 70px;
  max-width: 100%;
  object-fit: contain;
}

.no-label {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 12px;
}

/* 调整el-dialog的样式 */
:deep(.el-dialog__body) {
  padding: 10px 20px;
  min-height: 800px;
  max-height: calc(85vh - 100px);
  overflow-y: auto;
}

:deep(.el-dialog__footer) {
  padding-top: 10px;
  padding-bottom: 15px;
  border-top: 1px solid #dcdfe6;
}

:deep(.el-table .cell) {
  line-height: 1.4;
}
</style> 