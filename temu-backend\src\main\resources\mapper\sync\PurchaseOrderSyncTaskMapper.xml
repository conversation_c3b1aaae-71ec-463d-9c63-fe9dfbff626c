<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.sync.mapper.PurchaseOrderSyncTaskMapper">

    <resultMap id="BaseResultMap" type="com.xiao.temu.modules.sync.entity.PurchaseOrderSyncTask">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="last_sync_time" property="lastSyncTime"/>
        <result column="last_update_time" property="lastUpdateTime"/>
        <result column="sync_status" property="syncStatus"/>
        <result column="error_message" property="errorMessage"/>
        <result column="normal_order_count" property="normalOrderCount"/>
        <result column="jit_order_count" property="jitOrderCount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="selectPurchaseOrderSyncTaskVo">
        SELECT id, shop_id, last_sync_time, last_update_time, sync_status, error_message,
               normal_order_count, jit_order_count, create_time, update_time
        FROM purchase_order_sync_task
    </sql>

    <select id="getByShopId" resultMap="BaseResultMap">
        <include refid="selectPurchaseOrderSyncTaskVo"/>
        WHERE shop_id = #{shopId}
    </select>

    <select id="getAllTasks" resultMap="BaseResultMap">
        <include refid="selectPurchaseOrderSyncTaskVo"/>
        ORDER BY update_time DESC
    </select>

    <select id="getTasksByShopIds" resultMap="BaseResultMap">
        <include refid="selectPurchaseOrderSyncTaskVo"/>
        WHERE shop_id IN
        <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        ORDER BY update_time DESC
    </select>

    <select id="getNotSyncingTasks" resultMap="BaseResultMap">
        <include refid="selectPurchaseOrderSyncTaskVo"/>
        WHERE sync_status != 1
        ORDER BY last_sync_time ASC
    </select>
</mapper> 