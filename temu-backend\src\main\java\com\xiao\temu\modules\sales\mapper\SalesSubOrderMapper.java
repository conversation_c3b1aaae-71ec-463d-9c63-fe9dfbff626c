package com.xiao.temu.modules.sales.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.sales.entity.SalesSubOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 销售子订单Mapper接口
 */
@Mapper
public interface SalesSubOrderMapper extends BaseMapper<SalesSubOrder> {

    /**
     * 获取指定店铺的销售子订单数量
     *
     * @param shopId 店铺ID
     * @return 数量
     */
    @Select("SELECT COUNT(*) FROM sales_sub_order WHERE shop_id = #{shopId}")
    Integer countByShopId(@Param("shopId") Long shopId);
} 