/**
 * PDF生成工具
 */
import jsPDF from 'jspdf'
import JsBarcode from 'jsbarcode'

/**
 * 商品标签数据类型
 */
interface ProductLabelData {
  // 基础信息
  productSkuId: string | number;
  productSkcId: string | number;
  extCode: string | number;
  labelCode: string | number;
  // 商品规格
  colorSpec: string;
  sizeSpec: string;
  // 其他信息
  isCustom: boolean;
  skuCode: string | number;
  madeIn: string;
}

/**
 * PDF标签布局配置
 */
interface LabelLayoutConfig {
  labelWidth: number;
  labelHeight: number;
  fontSize: {
    normal: number;
    small: number;
  };
}

/**
 * 默认标签布局配置
 */
const defaultLabelConfig: LabelLayoutConfig = {
  labelWidth: 70, // 70mm宽
  labelHeight: 20, // 20mm高
  fontSize: {
    normal: 7,
    small: 6
  }
};

/**
 * 生成商品标签PDF文件
 * @param labels 标签数据列表
 * @param config 布局配置
 * @param filename 文件名，不含扩展名
 * @returns PDF文件名
 */
export async function generateProductLabelPDF(
  labels: ProductLabelData[],
  config: LabelLayoutConfig = defaultLabelConfig,
  filename: string = `商品标签打印_${new Date().getTime()}`
): Promise<string> {
  return new Promise(async (resolve, reject) => {
    try {
      // 过滤没有条码的标签
      const validLabels = labels.filter(label => label.labelCode);
      
      if (validLabels.length === 0) {
        throw new Error('没有找到有条码数据的商品');
      }
      
      // 创建PDF实例，单位为mm，页面大小设置为标签大小
      const pdf = new jsPDF({
        orientation: 'landscape', // 横向布局，因为标签是宽大于高
        unit: 'mm',
        format: [config.labelWidth, config.labelHeight] // 设置页面大小为标签尺寸
      });
      
      // 生成和添加标签到PDF，每个标签独占一页
      for (let i = 0; i < validLabels.length; i++) {
        // 如果不是第一个标签，添加新页面
        if (i > 0) {
          pdf.addPage([config.labelWidth, config.labelHeight]);
        }
        
        // 获取当前标签数据
        const label = validLabels[i];
        
        // 在页面上添加标签内容，从(0,0)点开始，占据整个页面
        await addLabelToPDF(pdf, label, 0, 0, config);
      }
      
      // 保存PDF文件
      const pdfName = `${filename}.pdf`;
      pdf.save(pdfName);
      
      resolve(pdfName);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 将单个标签添加到PDF中
 * @param pdf PDF文档对象
 * @param label 标签数据
 * @param x 左上角X坐标
 * @param y 左上角Y坐标
 * @param config 布局配置
 */
async function addLabelToPDF(
  pdf: jsPDF,
  label: ProductLabelData,
  x: number,
  y: number,
  config: LabelLayoutConfig
): Promise<void> {
  const labelWidth = config.labelWidth;
  const labelHeight = config.labelHeight;
  
  // 绘制标签边框（可选，如果需要边框的话）
  pdf.setDrawColor(200, 200, 200); // 淡灰色
  pdf.rect(x, y, labelWidth, labelHeight);
  
  // 设置字体
  pdf.setFont('helvetica', 'normal');
  
  // 计算内容区域的边距，避免内容过于靠近边缘
  const marginX = 2; // 左右边距2mm
  const marginY = 2; // 上下边距2mm
  
  // 1. 左上角：SKU货号/SKC ID
  const skuExtCode = String(label.extCode || label.productSkcId || '');
  pdf.setFontSize(config.fontSize.small);
  pdf.text(skuExtCode, x + marginX, y + marginY + 2);
  
  // 2. 上面中间部分：主属性（颜色）
  const colorSpec = String(label.colorSpec || '');
  pdf.setFontSize(config.fontSize.small);
  pdf.text(colorSpec, x + labelWidth/2, y + marginY + 2, { align: 'center' });
  
  // 3. 右上角：次属性（尺码）
  const sizeSpec = String(label.sizeSpec || '');
  pdf.setFontSize(config.fontSize.small);
  pdf.text(sizeSpec, x + labelWidth - marginX, y + marginY + 2, { align: 'right' });
  
  // 4. 中间部分：条码图片
  // 条码宽度占标签宽度的80%
  const barcodeWidth = labelWidth * 0.8;
  // 条码高度为标签高度的30%
  const barcodeHeight = labelHeight * 0.3;
  // 计算条码居中放置的位置
  const barcodeX = x + (labelWidth - barcodeWidth) / 2;
  const barcodeY = y + labelHeight * 0.35; // 放置在标签中部偏上
  
  // 生成条码图像
  const barcodeDataUrl = await generateBarcodeDataUrl(label.labelCode);
  pdf.addImage(barcodeDataUrl, 'PNG', barcodeX, barcodeY, barcodeWidth, barcodeHeight);
  
  // 添加条码编号
  const labelCodeText = String(label.labelCode);
  pdf.setFontSize(config.fontSize.small);
  pdf.text(labelCodeText, x + labelWidth/2, y + barcodeY + barcodeHeight + 2, { align: 'center' });
  
  // 5. 左下角：货品SKU ID（定制品前加DZ）
  const skuCode = String(label.skuCode || '');
  pdf.setFontSize(config.fontSize.small);
  pdf.text(skuCode, x + marginX, y + labelHeight - marginY);
  
  // 6. 右下角：Made In China/其他
  const madeIn = String(label.madeIn || 'Made In China');
  pdf.setFontSize(config.fontSize.small);
  pdf.text(madeIn, x + labelWidth - marginX, y + labelHeight - marginY, { align: 'right' });
}

/**
 * 生成条形码图像的DataURL
 * @param code 条码内容
 * @returns 条码图像的DataURL
 */
function generateBarcodeDataUrl(code: string | number): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      const canvas = document.createElement('canvas');
      // 确保条码内容始终是字符串类型
      const codeString = String(code);
      console.log('生成条形码，内容:', codeString, '类型:', typeof codeString);
      
      JsBarcode(canvas, codeString, {
        format: 'CODE128',
        width: 2,
        height: 30,
        displayValue: false
      });
      
      const dataUrl = canvas.toDataURL('image/png');
      console.log('条形码生成成功，dataURL长度:', dataUrl.length);
      resolve(dataUrl);
    } catch (error) {
      console.error('条形码生成失败:', error);
      // 发生错误时，返回一个空白图像，避免整个过程失败
      const fallbackCanvas = document.createElement('canvas');
      fallbackCanvas.width = 200;
      fallbackCanvas.height = 60;
      const ctx = fallbackCanvas.getContext('2d');
      if (ctx) {
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, 200, 60);
        ctx.fillStyle = 'black';
        ctx.font = '16px Arial';
        ctx.fillText(`Error: ${String(code)}`, 10, 30);
      }
      const fallbackUrl = fallbackCanvas.toDataURL('image/png');
      resolve(fallbackUrl);
    }
  });
} 