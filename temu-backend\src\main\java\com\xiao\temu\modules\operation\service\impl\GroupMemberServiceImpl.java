package com.xiao.temu.modules.operation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.operation.dto.GroupMemberDTO;
import com.xiao.temu.modules.operation.entity.GroupLeaderShopAssignment;
import com.xiao.temu.modules.operation.entity.GroupMember;
import com.xiao.temu.modules.operation.entity.OperationGroup;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.operation.mapper.GroupLeaderShopAssignmentMapper;
import com.xiao.temu.modules.operation.mapper.GroupMemberMapper;
import com.xiao.temu.modules.operation.mapper.OperationGroupMapper;
import com.xiao.temu.modules.system.mapper.SysUserMapper;
import com.xiao.temu.modules.operation.service.GroupMemberService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 运营组成员服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GroupMemberServiceImpl implements GroupMemberService {

    private final GroupMemberMapper groupMemberMapper;
    private final OperationGroupMapper operationGroupMapper;
    private final SysUserMapper userMapper;
    private final GroupLeaderShopAssignmentMapper groupLeaderShopAssignmentMapper;

    @Override
    public IPage<GroupMemberDTO> getMemberList(Long groupId, Page<GroupMemberDTO> page) {
        // 校验运营组是否存在
        OperationGroup group = operationGroupMapper.selectById(groupId);
        if (group == null) {
            throw new RuntimeException("查询成员列表失败，运营组不存在");
        }
        
        return groupMemberMapper.selectMemberList(page, groupId);
    }

    @Override
    public List<GroupMemberDTO> getAllMembers(Long groupId) {
        // 校验运营组是否存在
        OperationGroup group = operationGroupMapper.selectById(groupId);
        if (group == null) {
            throw new RuntimeException("查询成员列表失败，运营组不存在");
        }
        
        return groupMemberMapper.selectAllMembers(groupId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addMember(Long groupId, Long userId) {
        // 校验运营组是否存在
        OperationGroup group = operationGroupMapper.selectById(groupId);
        if (group == null) {
            throw new RuntimeException("添加成员失败，运营组不存在");
        }
        
        // 校验用户是否存在
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("添加成员失败，用户不存在");
        }
        
        // 判断用户是否已是成员
        if (checkUserInGroup(groupId, userId)) {
            // 用户已是成员，无需重复添加
            return 1;
        }
        
        // 添加新成员
        GroupMember member = new GroupMember();
        member.setGroupId(groupId);
        member.setUserId(userId);
        member.setJoinTime(new Date());
        member.setStatus("0");
        
        return groupMemberMapper.insert(member);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchAddMembers(Long groupId, List<Long> userIds) {
        // 校验运营组是否存在
        OperationGroup group = operationGroupMapper.selectById(groupId);
        if (group == null) {
            throw new RuntimeException("批量添加成员失败，运营组不存在");
        }
        
        // 校验用户是否都存在
        for (Long userId : userIds) {
            SysUser user = userMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("批量添加成员失败，用户ID为" + userId + "的用户不存在");
            }
        }
        
        // 批量添加成员
        Date joinTime = new Date();
        return groupMemberMapper.batchAddMembers(groupId, userIds, joinTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeMember(Long groupId, Long userId) {
        // 校验运营组是否存在
        OperationGroup group = operationGroupMapper.selectById(groupId);
        if (group == null) {
            throw new RuntimeException("移除成员失败，运营组不存在");
        }
        
        // 校验用户是否是成员
        if (!checkUserInGroup(groupId, userId)) {
            throw new RuntimeException("移除成员失败，用户不是该运营组成员");
        }
        
        // 判断是否是负责人
        if (group.getLeaderId().equals(userId)) {
            throw new RuntimeException("移除成员失败，不能移除负责人");
        }
        
        // 删除该用户在该运营组中被分配的店铺记录
        LambdaQueryWrapper<GroupLeaderShopAssignment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GroupLeaderShopAssignment::getGroupId, groupId)
                .eq(GroupLeaderShopAssignment::getUserId, userId);
        groupLeaderShopAssignmentMapper.delete(wrapper);
        
        // 移除成员（物理删除）
        return groupMemberMapper.deleteMember(groupId, userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeAllMembers(Long groupId) {
        // 校验运营组是否存在
        OperationGroup group = operationGroupMapper.selectById(groupId);
        if (group == null) {
            throw new RuntimeException("移除所有成员失败，运营组不存在");
        }
        
        // 注意：删除组长店铺分配记录由调用方处理
        // 因为调用removeAllMembers通常是在删除整个运营组的场景下，此时需要在上层统一处理所有关联数据的删除
        
        // 删除所有成员（物理删除）
        return groupMemberMapper.deleteByGroupId(groupId);
    }

    @Override
    public boolean checkUserInGroup(Long groupId, Long userId) {
        return groupMemberMapper.checkUserInGroup(groupId, userId) > 0;
    }

    @Override
    public int countGroupMembers(Long groupId) {
        return groupMemberMapper.countGroupMembers(groupId);
    }
} 