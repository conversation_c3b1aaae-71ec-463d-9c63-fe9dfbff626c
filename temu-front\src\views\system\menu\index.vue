<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="菜单名称" prop="menuName">
          <el-input
            v-model="queryParams.menuName"
            placeholder="请输入菜单名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="visible" style="width: 150px">
          <el-select v-model="queryParams.visible" placeholder="菜单状态" clearable>
            <el-option label="显示" value="0" />
            <el-option label="隐藏" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>菜单列表</span>
          <div class="right-buttons">
            <el-button type="primary" icon="Plus" @click="handleAdd">新增</el-button>
            <el-button icon="Refresh" @click="getList">刷新</el-button>
          </div>
        </div>
      </template>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="menuList"
        row-key="menuId"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        border
        :default-expand-all="false"
        :indent="10"
        class="menu-tree-table"
        ref="menuTableRef"
      >
        <!-- 隐藏原始展开按钮 -->
        <el-table-column width="24" class="hidden-expand-column">
          <template #default="scope">
            <div class="hidden-expand-icon"></div>
          </template>
        </el-table-column>
        
        <el-table-column prop="menuName" label="菜单名称" min-width="80" max-width="250" :show-overflow-tooltip="true">
          <template #default="scope">
            <div 
              class="menu-item-wrapper"
              @click="handleMenuClick(scope.row)"
              :class="{
                'has-children': scope.row.children && scope.row.children.length > 0,
                'level-0': scope.row._level === 0,
                'level-1': scope.row._level === 1,
                'level-2': scope.row._level === 2
              }"
            >
              <!-- 自定义展开按钮 - 仅对有子节点的项显示，放在左侧 -->
              <span 
                v-if="scope.row.children && scope.row.children.length > 0" 
                class="custom-expand-icon left-expand-icon"
                @click.stop="toggleExpand(scope.row)"
              >
                <el-icon :class="{ 'is-expanded': isExpanded(scope.row) }">
                  <ArrowRight />
                </el-icon>
              </span>
              
              <span v-if="scope.row.menuType === 'M'" class="menu-item directory-item">
                <el-icon><component :is="getIconName(scope.row.icon) || 'Folder'" /></el-icon>
                {{ scope.row.menuName }}
              </span>
              <span v-else-if="scope.row.menuType === 'C'" class="menu-item menu-link-item">
                <el-icon><component :is="getIconName(scope.row.icon) || 'Document'" /></el-icon>
                {{ scope.row.menuName }}
              </span>
              <span v-else class="menu-item button-item">
                <el-icon><CircleCheck /></el-icon>
                {{ scope.row.menuName }}
              </span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="icon" label="图标" align="center" width="60">
          <template #default="scope">
            <span v-if="scope.row.menuType === 'M'" class="icon-display">
              <el-icon><component :is="getIconName(scope.row.icon) || 'Folder'" /></el-icon>
            </span>
            <span v-else-if="scope.row.menuType === 'C'" class="icon-display">
              <el-icon><component :is="getIconName(scope.row.icon) || 'Document'" /></el-icon>
            </span>
            <span v-else class="icon-display">
              <el-icon><CircleCheck /></el-icon>
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="orderNum" label="排序" width="60" align="center" />
        
        <el-table-column prop="perms" label="权限标识" min-width="120" :show-overflow-tooltip="true" />
        
        <el-table-column prop="path" label="路由地址" min-width="100" :show-overflow-tooltip="true" />
        
        <el-table-column prop="visible" label="状态" width="70" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.visible === '0'" type="success">显示</el-tag>
            <el-tag v-else type="info">隐藏</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="menuType" label="类型" width="70" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.menuType === 'M'" type="primary">目录</el-tag>
            <el-tag v-else-if="scope.row.menuType === 'C'" type="success">菜单</el-tag>
            <el-tag v-else type="warning">按钮</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" align="center" width="250">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="Plus"
              v-if="scope.row.menuType !== 'F'"
              @click="handleAdd(scope.row)"
            >新增</el-button>
            <el-button
              type="primary"
              link
              icon="Edit"
              @click="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
              type="danger"
              link
              icon="Delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加或修改菜单对话框 -->
    <MenuForm
      ref="menuFormRef"
      @refreshList="getList"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { getMenuList, deleteMenu } from '@/api/menu'
import { ElMessage, ElMessageBox, ElTable } from 'element-plus'
import { 
  Folder, 
  Document, 
  CircleCheck, 
  User, 
  Lock, 
  Setting, 
  Menu, 
  ArrowRight,
  Plus, 
  Edit, 
  Delete, 
  Search, 
  Refresh 
} from '@element-plus/icons-vue'
import MenuForm from './components/MenuForm.vue'

// 菜单列表
const menuList = ref<any[]>([])
// 加载状态
const loading = ref(false)
// 查询参数
const queryParams = reactive({
  menuName: '',
  visible: ''
})
// 菜单表单引用
const menuFormRef = ref<InstanceType<typeof MenuForm> | null>(null)
// 表格引用
const menuTableRef = ref<InstanceType<typeof ElTable> | null>(null)
// 展开的行
const expandedRows = ref(new Set<number>())

// 在组件挂载时获取菜单列表
onMounted(() => {
  getList()
})

// 系统内置的图标映射表，将后端返回的icon字符串映射到Element Plus的图标组件
const iconMap: Record<string, string> = {
  'system': 'Setting',
  'user': 'User',
  'monitor': 'Monitor',
  'tool': 'Tools',
  'dict': 'List',
  'build': 'House',
  'swagger': 'Document',
  'guide': 'Guide',
  'tree': 'Tree',
  'job': 'Alarm',
  'code': 'Edit',
  'druid': 'DataLine',
  'server': 'Server',
  'edit': 'Edit',
  'log': 'Tickets',
  'post': 'Postcard',
  'chart': 'PieChart',
  'peoples': 'User',
  'cascader': 'CoffeeCup',
  'money': 'Money',
  'shopping': 'ShoppingCart',
  'message': 'ChatDotRound',
  'question': 'QuestionFilled',
  'international': 'Globe',
  'theme': 'Brush',
  'clipboard': 'DocumentCopy',
  'people': 'User',
  'role': 'Lock',
  'menu': 'Menu'
}

// 判断是否为有效的图标名称，并返回有效的图标组件名
const getIconName = (iconName: string): string | null => {
  if (!iconName || iconName === '#' || iconName.length === 0) {
    return null
  }

  // 尝试从映射表中获取图标
  if (iconMap[iconName]) {
    return iconMap[iconName]
  }

  // 处理Element Plus图标的前缀
  if (iconName.startsWith('el-icon-')) {
    // 转换el-icon-xxx格式为Pascal命名格式
    const name = iconName.replace('el-icon-', '')
    return name.charAt(0).toUpperCase() + name.slice(1)
  }

  return iconName
}

// 切换行的展开状态
const toggleExpand = (row: any) => {
  menuTableRef.value?.toggleRowExpansion(row)
  
  if (isExpanded(row)) {
    expandedRows.value.delete(row.menuId)
  } else {
    expandedRows.value.add(row.menuId)
  }
}

// 处理菜单名称点击事件
const handleMenuClick = (row: any) => {
  // 只有有子菜单的项才会触发展开/折叠
  if (row.children && row.children.length > 0) {
    toggleExpand(row)
  }
}

// 检查行是否已展开
const isExpanded = (row: any): boolean => {
  return expandedRows.value.has(row.menuId)
}

// 获取菜单列表
const getList = async () => {
  loading.value = true
  try {
    const res = await getMenuList(queryParams)
    
    // 检查返回的数据
    if (!res.data || !Array.isArray(res.data)) {
      console.error('菜单数据格式错误:', res)
      menuList.value = []
      return
    }
    
    // 对每个菜单项进行处理，确保children属性始终是数组
    const processMenuItems = (items: any[], level = 0) => {
      return items.map(item => {
        // 添加层级标记
        item._level = level;
        
        // 确保children属性是数组
        if (!item.children) {
          item.children = []
        } else if (Array.isArray(item.children)) {
          // 递归处理子菜单，并增加层级
          item.children = processMenuItems(item.children, level + 1)
        }
        return item
      })
    }
    
    // 处理菜单数据
    menuList.value = processMenuItems(res.data)
    
    // 保持菜单树默认折叠状态，不自动展开
    expandedRows.value.clear()
  } catch (error) {
    console.error('获取菜单列表失败', error)
    menuList.value = []
  } finally {
    loading.value = false
  }
}

// 将嵌套菜单扁平化为一维数组
const flattenMenuRows = (menus: any[]): any[] => {
  const result: any[] = []
  
  const flatten = (items: any[]) => {
    items.forEach(item => {
      result.push(item)
      if (item.children && item.children.length > 0) {
        flatten(item.children)
      }
    })
  }
  
  flatten(menus)
  return result
}

// 搜索按钮操作
const handleQuery = () => {
  // 在搜索前先保存搜索条件，确保boolean类型
  const hasSearchCondition = Boolean(queryParams.menuName) || Boolean(queryParams.visible);
  
  getList();
  
  // 如果有搜索条件，确保展开所有节点以显示匹配结果
  if (hasSearchCondition) {
    nextTick(() => {
      // 延迟执行，确保表格已渲染
      setTimeout(() => {
        const tableRef = document.querySelector('.menu-tree-table')
        if (tableRef) {
          const expandButtons = tableRef.querySelectorAll('.el-table__expand-icon')
          expandButtons.forEach((btn: any) => {
            if (!btn.classList.contains('el-table__expand-icon--expanded')) {
              btn.click()
            }
          })
        }
      }, 300)
    })
  }
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.menuName = ''
  queryParams.visible = ''
  handleQuery()
}

// 新增菜单
const handleAdd = (row?: any) => {
  if (row && row.menuId) {
    // 如果传入了父菜单，先打开对话框再设置父菜单
    menuFormRef.value?.openDialog()
    // 设置延时确保openDialog完成初始化
    setTimeout(() => {
      menuFormRef.value?.setParentMenu(row.menuId)
    }, 100)
  } else {
    // 新建顶级菜单
    menuFormRef.value?.openDialog()
  }
}

// 修改菜单
const handleUpdate = (row: any) => {
  menuFormRef.value?.openDialog(row.menuId)
}

// 删除菜单
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确认删除菜单 ${row.menuName} 吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteMenu(row.menuId)
      ElMessage.success('删除成功')
      getList()
    } catch (error: any) {
      ElMessage.error(error.message || '删除失败')
    }
  }).catch(() => {
    // 用户取消删除操作
  })
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.right-buttons {
  display: flex;
  gap: 10px;
}

/* 菜单树样式 */
.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.menu-item-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
  padding: 4px 0;
}

.menu-item-wrapper.has-children {
  cursor: pointer;
}

.menu-item-wrapper.has-children:hover {
  background-color: #f5f7fa;
  border-radius: 4px;
}

.custom-expand-icon {
  cursor: pointer;
  width: 20px;
  height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.left-expand-icon {
  margin-right: 4px;
}

.custom-expand-icon .el-icon {
  font-size: 16px;
  transition: transform 0.3s;
}

.custom-expand-icon .is-expanded {
  transform: rotate(90deg);
}

/* 隐藏原生展开按钮 */
.hidden-expand-column :deep(.cell) {
  padding: 0 !important;
  width: 0;
  overflow: hidden;
}

.hidden-expand-icon {
  width: 0;
  height: 0;
  visibility: hidden;
}

.menu-tree-table :deep(.el-table__expand-icon) {
  visibility: hidden;
  width: 0;
  height: 0;
  overflow: hidden;
}

.directory-item {
  font-weight: bold;
  color: #303133;
}

.menu-link-item {
  color: #409eff;
}

.button-item {
  color: #67c23a;
  font-size: 0.95em;
}

/* 图标显示样式 */
.icon-display {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.icon-display .el-icon {
  font-size: 18px;
}

/* 菜单项特定层级的样式 */
.menu-item-wrapper.level-0 {
  padding-left: 0;
}

.menu-item-wrapper.level-1 {
  padding-left: 24px;
}

.menu-item-wrapper.level-2 {
  padding-left: 60px;
}

/* 修改表格的基础缩进，与我们的自定义缩进配合 */
.menu-tree-table :deep(.el-table__indent) {
  padding-left: 0;
  display: inline-block;
  height: 100%;
}

/* 为不同层级添加视觉区分 */
.menu-tree-table :deep(.el-table__row--level-0) {
  background-color: #f5f7fa;
}

/* 清除由 padding-left 造成的菜单项本身的缩进，我们将使用表格的 indent 属性来控制缩进 */
.menu-tree-table :deep(.el-table__row--level-1) {
  background-color: #ffffff;
}

.menu-tree-table :deep(.el-table__row--level-2) {
  background-color: #f8f9fa;
  border-top: 1px dashed #e6e6e6;
  border-bottom: 1px dashed #e6e6e6;
}

/* 为不同层级设置左侧边框颜色和粗细 */
.menu-tree-table :deep(.el-table__row--level-0) td:first-child {
  border-left: 4px solid #409eff;
}

.menu-tree-table :deep(.el-table__row--level-1) td:first-child {
  border-left: 4px solid #67c23a;
}

.menu-tree-table :deep(.el-table__row--level-2) td:first-child {
  border-left: 4px solid #e6a23c;
}

/* 增强菜单名称列层级视觉效果 */
.menu-tree-table :deep(.el-table__row--level-0) .directory-item {
  font-size: 15px;
  font-weight: bold;
}

.menu-tree-table :deep(.el-table__row--level-1) .menu-link-item {
  font-size: 14px;
  color: #1890ff;
}

.menu-tree-table :deep(.el-table__row--level-2) .button-item {
  font-size: 13px;
  color: #67c23a;
  position: relative;
}

/* 为第三层级添加左侧的小标记，增加视觉区分 */
.menu-tree-table :deep(.el-table__row--level-2) .button-item::before {
  content: "";
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #e6a23c;
  position: absolute;
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
}

/* 添加悬停效果 */
.menu-tree-table :deep(.el-table__row:hover) {
  background-color: #f0f9ff !important;
}
</style> 