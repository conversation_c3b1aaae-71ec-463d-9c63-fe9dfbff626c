<view class="header-container">
  <!-- 状态栏高度占位 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
  
  <!-- 标题栏 -->
  <view class="header" style="height: {{menuButtonInfo.height + 24}}px; padding-top: {{(menuButtonInfo.top - statusBarHeight) / 2}}px;">
    <view class="header-left">
      <!-- 返回按钮（条件渲染） -->
      <view wx:if="{{showBack}}" class="back-btn" bindtap="goBack">
        <image class="back-btn-img" src="/static/icons/left.png" mode="aspectFit" />
      </view>
      <!-- 标题文本 -->
      <text class="title">{{title}}</text>
    </view>
  </view>
</view> 