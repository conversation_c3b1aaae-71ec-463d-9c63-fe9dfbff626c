
## 需求分析
您需要我为ProductionProgressController中的接口编写调用文档，包括URL、请求参数、返回参数等详细信息。需要用JSON格式描述请求参数，而不是直接引用DTO对象名称，需要展开说明DTO对象包含的具体参数。

## 生产进度API开发文档

### 1. 获取生产进度详情

**请求方式**：GET

**接口URL**：`/v1/production/progress/detail`

**请求参数**：

| 参数名 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
| shopId | Long | 是 | 店铺ID |
| subPurchaseOrderSn | String | 是 | 备货单号 |

**返回结果**：
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    // 生产进度详情
    // ProductionProgressVO的详细字段
  }
}
```

### 2. 批量获取生产进度详情

**请求方式**：POST

**接口URL**：`/v1/production/progress/batch-detail`

**请求参数**：

| 参数名 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
| shopId | Long | 是 | 店铺ID |

**请求体**：
```json
[
  "子采购单号1",
  "子采购单号2",
  "子采购单号3"
]
```

**返回结果**：
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "子采购单号1": {
      // 生产进度详情1
    },
    "子采购单号2": {
      // 生产进度详情2
    }
  }
}
```

### 3. 获取生产进度操作日志

**请求方式**：GET

**接口URL**：`/v1/production/progress/logs`

**请求参数**：

| 参数名 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
| shopId | Long | 是 | 店铺ID |
| subPurchaseOrderSn | String | 是 | 备货单号 |

**返回结果**：
```json
{
  "code": 200,
  "msg": "success",
  "data": [
    {
      // ProductionProgressLogVO的详细字段
    }
  ]
}
```

### 4. 分页查询生产进度

**请求方式**：POST

**接口URL**：`/v1/production/progress/page`

**请求体**：
```json
{
  // ProductionProgressDTO的查询参数字段
  // 注：原代码中未提供具体字段，实际使用时需要补充
}
```

**返回结果**：
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "records": [
      {
        // 生产进度详情列表
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 5. 扫码更新生产进度

**请求方式**：POST

**接口URL**：`/v1/production/progress/update`

**请求体**：
```json
{
  "shopId": 10001,
  "subPurchaseOrderSn": "SP202403150001",
  "progressType": "burning",
  "operationType": "1",
  "remarks": "烧花完成"
}
```

**参数说明**：

| 参数名 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
| shopId | Long | 是 | 店铺ID |
| subPurchaseOrderSn | String | 是 | 备货单号 |
| progressType | String | 是 | 进度类型(burning:烧花,sewing:车缝,tail:尾部,shipping:发货,delivery:送货) |
| operationType | String | 否 | 操作类型(1:完成 2:撤销) 默认为1完成 |
| remarks | String | 否 | 备注 |

**返回结果**：
```json
{
  "code": 200,
  "msg": "success",
  "data": true
}
```

### 6. 撤销生产进度（管理员功能）

**请求方式**：POST

**接口URL**：`/v1/production/progress/cancel`

**请求体**：
```json
{
  "shopId": 10001,
  "subPurchaseOrderSn": "SP202403150001",
  "progressType": "burning",
  "operationType": "2",
  "remarks": "撤销原因说明"
}
```

**参数说明**：同"扫码更新生产进度"接口

**返回结果**：
```json
{
  "code": 200,
  "msg": "success",
  "data": true
}
```

## 总结
本文档详细描述了生产进度相关的6个接口，包括获取生产进度详情、批量获取生产进度详情、获取生产进度操作日志、分页查询生产进度、扫码更新生产进度以及撤销生产进度等功能。文档提供了每个接口的URL、请求方式、参数说明及返回格式，方便开发人员进行集成调用。
