package com.xiao.temu.modules.product.service;

import com.xiao.temu.modules.product.dto.ProductNewArrivalBatchDTO;
import com.xiao.temu.modules.product.dto.ProductNewArrivalStatsDTO;
import com.xiao.temu.modules.product.dto.ProductNewArrivalStatsQueryDTO;

import java.util.List;

/**
 * 商品上新服务接口
 */
public interface ProductNewArrivalService {
    
    /**
     * 批量保存商品上新数据
     *
     * @param batchDTO 批量数据传输对象
     * @return 保存的记录数
     */
    int batchSave(ProductNewArrivalBatchDTO batchDTO);
    
    /**
     * 查询商品上新统计数据
     *
     * @param queryDTO 查询条件
     * @return 商品上新统计数据列表
     */
    List<ProductNewArrivalStatsDTO> getProductNewArrivalStats(ProductNewArrivalStatsQueryDTO queryDTO);
} 