<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建组员"
    width="500px"
    @close="handleClose"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      size="default"
    >
      <el-form-item label="用户名" prop="username">
        <el-input v-model="form.username" placeholder="请输入用户名" />
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input
          v-model="form.password"
          type="password"
          placeholder="请输入密码"
          show-password
        />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          v-model="form.confirmPassword"
          type="password"
          placeholder="请确认密码"
          show-password
        />
      </el-form-item>
      <el-form-item label="昵称" prop="nickName">
        <el-input v-model="form.nickName" placeholder="请输入昵称" />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="form.email" placeholder="请输入邮箱" />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="form.phone" placeholder="请输入手机号" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useLeaderAssignStore } from '@/store/modules/leaderAssign'
import type { CreateMemberParams } from '@/types/leaderAssign'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  groupId: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['update:visible', 'success'])

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const leaderAssignStore = useLeaderAssignStore()

// 表单引用
const formRef = ref()
// 表单数据
const form = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  nickName: '',
  email: '',
  phone: '',
  groupId: props.groupId || 0
})
// 加载状态
const loading = ref(false)

// 表单规则
const rules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度必须在3到20个字符之间', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度必须在6到20个字符之间', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value !== form.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  nickName: [
    { max: 50, message: '昵称长度不能超过50个字符', trigger: 'blur' }
  ],
  email: [
    { 
      pattern: /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/, 
      message: '请输入有效的邮箱地址', 
      trigger: 'blur' 
    }
  ],
  phone: [
    { 
      pattern: /^1[3-9]\d{9}$/, 
      message: '请输入有效的手机号', 
      trigger: 'blur' 
    }
  ]
})

// 监听groupId变化
watch(() => props.groupId, (newVal) => {
  form.groupId = newVal
})

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 构造创建用户的参数
    const params: CreateMemberParams = {
      username: form.username,
      password: form.password,
      groupId: form.groupId
    }
    
    // 添加可选字段
    if (form.nickName) params.nickName = form.nickName
    if (form.email) params.email = form.email
    if (form.phone) params.phone = form.phone
    
    // 调用API创建用户
    const response = await leaderAssignStore.createMember(params)
    
    ElMessage.success('创建组员成功')
    
    // 关闭对话框
    handleClose()
    
    // 触发成功回调
    emit('success')
  } catch (error: any) {
    console.error('创建组员失败:', error)
    ElMessage.error(error.message || '创建组员失败，请重试')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style> 