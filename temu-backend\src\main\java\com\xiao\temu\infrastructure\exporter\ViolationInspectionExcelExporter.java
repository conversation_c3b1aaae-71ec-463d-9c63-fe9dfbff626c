package com.xiao.temu.infrastructure.exporter;

import com.xiao.temu.modules.violation.dto.ViolationInspectionQueryDTO;
import com.xiao.temu.modules.violation.vo.ViolationInspectionVO;
import com.xiao.temu.modules.system.service.SysDataPermissionService;
import com.xiao.temu.modules.system.service.UserService;
import com.xiao.temu.modules.violation.dto.ViolationInspectionDTO;
import com.xiao.temu.modules.violation.service.ViolationInspectionService;
import com.xiao.temu.infrastructure.excel.ExcelExportService;
import com.xiao.temu.infrastructure.excel.ExcelExportUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 违规检查Excel导出实现类
 */
@Slf4j
@Service
public class ViolationInspectionExcelExporter implements ExcelExportService.ExcelExporter {

    @Autowired
    private ExcelExportService excelExportService;
    
    @Autowired
    private ViolationInspectionService violationInspectionService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private SysDataPermissionService dataPermissionService;
    
    @Override
    public void export(Map<String, Object> exportParams, String taskId) {
        // 定义表头和列宽
        String[] headers = {"序号", "店铺代号", "店铺名称", "违规编号", "备货单号", "商品SKU ID", "货号", 
                "备货件数", "缺货件数", "质量问题件数", "违规类型", "违规时间", "违规金额(元)", "具体原因", "疵点图片"};
        
        int[] columnWidths = {5, 20, 15, 15, 15, 15, 15, 10, 10, 15, 15, 20, 15, 30, 20};
        
        // 获取文件名
        String fileName = (String) exportParams.get("fileName");
        if (fileName == null || fileName.trim().isEmpty()) {
            fileName = "违规检查数据";
        }

        // 执行导出
        excelExportService.exportExcel(
            taskId,
            fileName,
            "违规检查",
            headers,
            columnWidths,
            new ViolationInspectionDataProcessor(exportParams)
        );
    }
    
    /**
     * 违规检查数据处理器
     */
    private class ViolationInspectionDataProcessor implements ExcelExportService.ExcelDataProcessor {
        
        private final Map<String, Object> exportParams;
        
        public ViolationInspectionDataProcessor(Map<String, Object> exportParams) {
            this.exportParams = exportParams;
        }
        
        @Override
        public void prepareData(ExcelExportService.ExcelExportContext context) {
            try {
                // 从导出参数中获取用户ID
                Object userIdObj = exportParams.get("userId");
                if (userIdObj == null) {
                    throw new RuntimeException("导出参数中缺少用户ID");
                }
                Long userId = Long.valueOf(userIdObj.toString());
                
                // 判断用户是否为管理员或拥有全部数据权限
                boolean isAdmin = userService.isAdmin(userId);
                
                // 获取用户的最高数据权限
                String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
                boolean hasFullDataPermission = "2".equals(permissionType);
                
                // 解析导出参数
                Map<String, Object> queryParams = (Map<String, Object>) exportParams.get("queryParams");
                String exportType = (String) exportParams.get("exportType");
                
                // 构建查询参数
                ViolationInspectionQueryDTO requestDTO = new ViolationInspectionQueryDTO();
                
                // 设置查询参数
                if (queryParams != null) {
                    if (queryParams.get("shopIds") != null) {
                        List<?> rawShopIds = (List<?>) queryParams.get("shopIds");
                        List<Long> shopIdList = new ArrayList<>();
                        for (Object id : rawShopIds) {
                            if (id instanceof Integer) {
                                shopIdList.add(((Integer) id).longValue());
                            } else if (id instanceof Long) {
                                shopIdList.add((Long) id);
                            } else if (id instanceof String) {
                                shopIdList.add(Long.valueOf((String) id));
                            } else {
                                shopIdList.add(Long.valueOf(id.toString()));
                            }
                        }
                        requestDTO.setShopIds(shopIdList);
                    }
                    
                    if (queryParams.get("violationTimeStart") != null) {
                        String timeStart = (String) queryParams.get("violationTimeStart");
                        requestDTO.setViolationTimeStart(parseDateTime(timeStart));
                    }
                    
                    if (queryParams.get("violationTimeEnd") != null) {
                        String timeEnd = (String) queryParams.get("violationTimeEnd");
                        requestDTO.setViolationTimeEnd(parseDateTime(timeEnd));
                    }
                    
                    if (queryParams.get("punishSn") != null) {
                        requestDTO.setPunishSn((String) queryParams.get("punishSn"));
                    }
                    
                    if (queryParams.get("subPurchaseOrderSn") != null) {
                        requestDTO.setSubPurchaseOrderSn((String) queryParams.get("subPurchaseOrderSn"));
                    }
                    
                    if (queryParams.get("productSkuId") != null) {
                        Object skuId = queryParams.get("productSkuId");
                        if (skuId instanceof Integer) {
                            requestDTO.setProductSkuId(((Integer) skuId).longValue());
                        } else if (skuId instanceof Long) {
                            requestDTO.setProductSkuId((Long) skuId);
                        } else if (skuId instanceof String) {
                            requestDTO.setProductSkuId(Long.valueOf((String) skuId));
                        }
                    }
                    
                    if (queryParams.get("extCode") != null) {
                        requestDTO.setExtCode((String) queryParams.get("extCode"));
                    }
                    
                    // 设置分页参数
                    if (queryParams.get("pageNum") != null) {
                        requestDTO.setPageNum(Math.toIntExact(Long.valueOf(queryParams.get("pageNum").toString())));
                    } else {
                        requestDTO.setPageNum(1); // 默认第一页
                    }
                    
                    if (queryParams.get("pageSize") != null) {
                        requestDTO.setPageSize(Math.toIntExact(Long.valueOf(queryParams.get("pageSize").toString())));
                    } else {
                        requestDTO.setPageSize(Integer.MAX_VALUE); // 默认导出全部
                    }
                }
                
                // 处理不同导出类型
                List<ViolationInspectionDTO> allItems = new ArrayList<>();
                
                if ("page".equals(exportType) || "current".equals(exportType)) {
                    // 导出当前页数据
                    ViolationInspectionVO result = violationInspectionService.getViolationInspectionData(requestDTO);
                    if (result.getRows() != null) {
                        allItems.addAll(result.getRows());
                    }
                } else if ("custom".equals(exportType)) {
                    // 导出自定义数量数据 - 多页
                    Integer exportPageCount = 1;
                    if (queryParams.get("exportPageCount") != null) {
                        exportPageCount = Math.toIntExact(Long.valueOf(queryParams.get("exportPageCount").toString()));
                    }
                    
                    // 初始页码
                    int startPage = requestDTO.getPageNum();
                    // 设置每页条数
                    int pageSize = requestDTO.getPageSize();
                    
                    // 分页查询数据
                    for (int i = 0; i < exportPageCount; i++) {
                        requestDTO.setPageNum(startPage + i);
                        ViolationInspectionVO result = violationInspectionService.getViolationInspectionData(requestDTO);
                        if (result.getRows() != null && !result.getRows().isEmpty()) {
                            allItems.addAll(result.getRows());
                        }
                        
                        // 如果返回的数据条数少于pageSize，说明已经没有更多数据，跳出循环
                        if (result.getRows() == null || result.getRows().size() < pageSize) {
                            break;
                        }
                    }
                } else {
                    // 导出全部数据
                    requestDTO.setPageSize(Integer.MAX_VALUE);
                    requestDTO.setPageNum(1);
                    ViolationInspectionVO result = violationInspectionService.getViolationInspectionData(requestDTO);
                    if (result.getRows() != null) {
                        allItems.addAll(result.getRows());
                    }
                }
                
                // 设置导出上下文
                context.setData(allItems);
                context.setDataSize(allItems.size());
                
            } catch (Exception e) {
                log.error("准备导出数据失败", e);
                throw new RuntimeException("准备导出数据失败: " + e.getMessage(), e);
            }
        }
        
        @Override
        public void prepareImageTasks(ExcelExportService.ExcelExportContext context, File tempDir, List<ExcelExportUtils.ImageDownloadTask> downloadTasks) {
            @SuppressWarnings("unchecked")
            List<ViolationInspectionDTO> items = (List<ViolationInspectionDTO>) context.getData();
            
            // 计算总图片数量，用于初始化容量
            int totalEstimatedImages = 0;
            for (ViolationInspectionDTO item : items) {
                List<String> attachments = item.getAttachmentList();
                if (attachments != null && !attachments.isEmpty()) {
                    totalEstimatedImages += Math.min(attachments.size(), 1); // 只取第一张图片
                }
            }
            
            // 每批处理的记录数
            final int BATCH_SIZE = 100; // 每次处理100条记录
            int totalRecords = items.size();
            int batchCount = (totalRecords + BATCH_SIZE - 1) / BATCH_SIZE; // 向上取整
            
            log.info("开始批量处理图片任务，总记录数: {}，估计图片数: {}，批次数: {}", 
                    totalRecords, totalEstimatedImages, batchCount);
                    
            // 添加大文件警告阈值和记录集合
            final int MAX_RECOMMENDED_IMAGES = 3000;
            final List<String> suspiciousImageUrls = new ArrayList<>();
            
            // 分批处理图片下载任务
            for (int batchIndex = 0; batchIndex < batchCount; batchIndex++) {
                int startIndex = batchIndex * BATCH_SIZE;
                int endIndex = Math.min(startIndex + BATCH_SIZE, totalRecords);
                
                log.info("处理第 {}/{} 批图片，记录范围: {}-{}", 
                        batchIndex + 1, batchCount, startIndex, endIndex - 1);
                
                List<ExcelExportUtils.ImageDownloadTask> batchTasks = new ArrayList<>();
                
                // 为当前批次创建下载任务
                for (int i = startIndex; i < endIndex; i++) {
                    ViolationInspectionDTO item = items.get(i);
                    final int rowIndex = i;
                    
                    // 处理疵点图片，只取第一张
                    List<String> attachments = item.getAttachmentList();
                    if (attachments != null && !attachments.isEmpty()) {
                        String imageUrl = attachments.get(0);
                        
                        // 跳过空URL
                        if (imageUrl == null || imageUrl.trim().isEmpty()) {
                            continue;
                        }
                        
                        // 检查可疑图片URL模式
                        if (imageUrl.contains("original") || imageUrl.contains("high-resolution")) {
                            suspiciousImageUrls.add(imageUrl);
                            log.warn("检测到可能的高分辨率图片URL: {}", imageUrl);
                        }
                        
                        File attachmentFile = new File(tempDir, "violation_img_" + i + "_" + UUID.randomUUID() + ".jpg");
                        batchTasks.add(new ExcelExportUtils.ImageDownloadTask(imageUrl, attachmentFile, 14, rowIndex));
                    }
                }
                
                // 将当前批次任务添加到总任务列表
                log.info("第 {} 批添加 {} 个图片任务", batchIndex + 1, batchTasks.size());
                downloadTasks.addAll(batchTasks);
                
                // 手动触发GC，释放内存
                if (batchIndex > 0 && batchIndex % 3 == 0) {
                    log.info("处理了 {} 批次后，主动触发GC释放内存", batchIndex);
                    System.gc();
                }
                
                // 监控总任务数，如果超过阈值，提前结束以避免OOM
                if (downloadTasks.size() > MAX_RECOMMENDED_IMAGES) {
                    log.warn("图片数量已超过推荐最大值 ({}张)，为确保稳定性将限制图片数量", MAX_RECOMMENDED_IMAGES);
                    break;
                }
            }
            
            // 添加可疑图片URL警告
            if (!suspiciousImageUrls.isEmpty()) {
                log.warn("发现 {} 个可能是高分辨率的图片URL，这些图片可能导致处理缓慢", suspiciousImageUrls.size());
            }
            
            log.info("图片任务准备完成，总任务数: {}", downloadTasks.size());
        }
        
        @Override
        public void fillExcelData(ExcelExportService.ExcelExportContext context, Sheet sheet, CellStyle contentStyle) {
            @SuppressWarnings("unchecked")
            List<ViolationInspectionDTO> items = (List<ViolationInspectionDTO>) context.getData();
            
            // 填充数据
            for (int i = 0; i < items.size(); i++) {
                ViolationInspectionDTO item = items.get(i);
                Row row = sheet.createRow(i + 1);
                row.setHeight((short) (80 * 20)); // 设置行高为80点，更好地适应图片
                
                // 设置单元格内容
                ExcelExportUtils.setCellValue(row, 0, String.valueOf(i + 1), contentStyle); // 序号
                ExcelExportUtils.setCellValue(row, 1, item.getShopRemark(), contentStyle); // 店铺代号
                ExcelExportUtils.setCellValue(row, 2, item.getShopName(), contentStyle); // 店铺名称
                ExcelExportUtils.setCellValue(row, 3, item.getPunishSn(), contentStyle); // 违规编号
                ExcelExportUtils.setCellValue(row, 4, item.getSubPurchaseOrderSn(), contentStyle); // 备货单号
                ExcelExportUtils.setCellValue(row, 5, item.getProductSkuId() != null ? String.valueOf(item.getProductSkuId()) : "", contentStyle); // 商品SKU ID
                ExcelExportUtils.setCellValue(row, 6, item.getExtCode(), contentStyle); // 货号
                ExcelExportUtils.setCellValue(row, 7, item.getStockQuantity() != null ? String.valueOf(item.getStockQuantity()) : "0", contentStyle); // 备货件数
                ExcelExportUtils.setCellValue(row, 8, item.getLackQuantity() != null ? String.valueOf(item.getLackQuantity()) : "0", contentStyle); // 缺货件数
                ExcelExportUtils.setCellValue(row, 9, item.getUnqualifiedQuantity() != null ? String.valueOf(item.getUnqualifiedQuantity()) : "0", contentStyle); // 质量问题件数
                ExcelExportUtils.setCellValue(row, 10, item.getPunishReasonDesc(), contentStyle); // 违规类型
                ExcelExportUtils.setCellValue(row, 11, formatDateTime(item.getViolationTime()), contentStyle); // 违规时间
                
                // 格式化金额
                String amountStr = "";
                if (item.getPunishAmount() != null) {
                    // 将分转换为元 (除以100)，使用BigDecimal进行除法运算
                    BigDecimal hundred = new BigDecimal("100");
                    BigDecimal amountInYuan = item.getPunishAmount().divide(hundred, 2, java.math.RoundingMode.HALF_UP);
                    amountStr = amountInYuan.toString();
                }
                ExcelExportUtils.setCellValue(row, 12, amountStr, contentStyle); // 违规金额(元)
                
                ExcelExportUtils.setCellValue(row, 13, item.getFlawNameDesc(), contentStyle); // 具体原因
                // 疵点图片列，留空，后面插入图片
                ExcelExportUtils.setCellValue(row, 14, "", contentStyle);
            }
        }
        
        /**
         * 格式化日期时间
         */
        private String formatDateTime(LocalDateTime dateTime) {
            if (dateTime == null) {
                return "";
            }
            return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        
        /**
         * 解析日期时间
         */
        private LocalDateTime parseDateTime(String dateTimeStr) {
            if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
                return null;
            }
            try {
                return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                try {
                    return LocalDateTime.parse(dateTimeStr + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                } catch (Exception ex) {
                    log.error("解析日期时间失败: {}", dateTimeStr);
                    return null;
                }
            }
        }
    }
} 