/**
 * 主页
 */
const worklistApi = require('../../api/worklistApi');
const productionProgressApi = require('../../api/productionProgressApi');
const progressConfig = require('../../utils/progressConfig');
const pageRefreshUtils = require('../../utils/pageRefreshUtils');

// 定义页面路径常量
const PAGE_PATH = 'pages/home/<USER>';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    userOperationLogs: [],
    orderedRoles: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 记录页面访问
    pageRefreshUtils.recordPageVisit(PAGE_PATH);
    
    this.loadUserInfo();
    // 获取待处理数量和用户操作日志
    this.loadData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 记录页面访问
    pageRefreshUtils.recordPageVisit(PAGE_PATH);
    
    // 检查页面是否需要刷新
    const needRefresh = pageRefreshUtils.needRefresh(PAGE_PATH) || 
                        pageRefreshUtils.isMarkedForRefresh(PAGE_PATH);
                        
    if (needRefresh) {
      console.log('首页需要刷新数据');
      this.loadData();
      pageRefreshUtils.clearRefreshMark(PAGE_PATH);
    }
  },
  
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    // 如果从首页前往扫码页，标记扫码页需要刷新
    pageRefreshUtils.markPageForRefresh('pages/scan/scan');
  },

  /**
   * 加载所有数据
   */
  loadData: function() {
    this.loadUserInfo();
    this.loadUserOperationLogs();
  },

  /**
   * 加载用户信息
   */
  loadUserInfo: function () {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      // 获取角色名称映射
      let roleNames = userInfo.roleNames || wx.getStorageSync('roleNames') || {};
      
      // 检查是否有生产组长角色
      const hasLeaderRole = roleNames.hasOwnProperty('productionLeader');
      
      // 创建有序角色数组，按照progressConfig中的VALID_PROGRESS_ROLES顺序排列
      const orderedRoles = [];
      
      // 遍历VALID_PROGRESS_ROLES，按顺序提取roleNames中的角色
      progressConfig.VALID_PROGRESS_ROLES.forEach(roleKey => {
        if (roleNames.hasOwnProperty(roleKey)) {
          orderedRoles.push({
            roleKey: roleKey,
            roleName: roleNames[roleKey]
          });
        }
      });
      
      // 设置用户信息，主要用于显示角色
      this.setData({
        userInfo: {
          ...userInfo,
          roleNames: roleNames,
          hasLeaderRole: hasLeaderRole
        },
        orderedRoles: orderedRoles
      });
      
      console.log('首页加载的用户信息:', this.data.userInfo);
      console.log('有序角色数组:', this.data.orderedRoles);
    } else {
      // 如果没有存储的用户信息，使用模拟数据
      const mockUserInfo = {
        userId: 1,
        roleName: '烧花/剪图工',
        roleNames: {
          cutting: '烧花/剪图',
          productionLeader: '生产组长' // 使用正确的键名
        },
        hasLeaderRole: true
      };
      
      // 创建有序角色数组
      const orderedRoles = [];
      progressConfig.VALID_PROGRESS_ROLES.forEach(roleKey => {
        if (mockUserInfo.roleNames.hasOwnProperty(roleKey)) {
          orderedRoles.push({
            roleKey: roleKey,
            roleName: mockUserInfo.roleNames[roleKey]
          });
        }
      });
      
      this.setData({
        userInfo: mockUserInfo,
        orderedRoles: orderedRoles
      });
    }
  },

  /**
   * 加载待处理数量
   */
  loadPendingCount: function () {
    // 删除整个函数
  },

  /**
   * 加载用户操作日志
   */
  loadUserOperationLogs: function () {
    // 尝试调用获取用户操作日志API
    productionProgressApi.getUserOperationLogs()
      .then(res => {
        console.log('获取到用户操作日志:', res);
        
        // 转换日志格式以适应界面显示
        const logs = (res || []).map(log => {
          return {
            orderNumber: log.subPurchaseOrderSn,
            operationType: this.formatOperationType(log.progressType, log.operationType),
            completedTime: this.formatTime(log.operationTime)
          };
        });
        
        this.setData({
          userOperationLogs: logs
        });
      })
      .catch(err => {
        console.error('获取用户操作日志失败:', err);
        
        // 如果API调用失败，使用模拟数据
        const mockLogs = [
          {
            orderNumber: 'TM20230615001',
            operationType: '完成烧花/剪图工序',
            completedTime: '10:30'
          },
          {
            orderNumber: 'TM20230614002',
            operationType: '完成车间/拣货工序',
            completedTime: '09:15'
          }
        ];
        
        this.setData({
          userOperationLogs: mockLogs
        });
      });
  },

  /**
   * 格式化操作类型
   */
  formatOperationType: function(progressType, operationType) {
    // 使用progressConfig中的工序类型映射
    const progressName = progressConfig.PROGRESS_TYPE_MAP[progressType] || progressType;
    
    const operationTypeMap = {
      '1': '完成',
      '2': '撤销'
    };
    
    const operationName = operationTypeMap[operationType] || operationType;
    
    return `${operationName}${progressName}工序`;
  },

  /**
   * 格式化时间
   */
  formatTime: function(timeStr) {
    if (!timeStr) return '';
    
    // 如果是完整的时间字符串，截取时分
    if (timeStr.length > 10) {
      return timeStr.substring(11, 16);
    }
    
    return timeStr;
  },

  /**
   * 跳转到扫码页
   */
  goToScan: function () {
    wx.switchTab({
      url: '/pages/scan/scan',
    });
  },

  /**
   * 跳转到工作列表页
   */
  goToWorklist: function () {
    wx.switchTab({
      url: '/pages/worklist/worklist',
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: 'TEMU备货单追踪',
      path: '/pages/home/<USER>'
    };
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    // 加载数据
    this.loadData();
    
    // 停止下拉刷新
    wx.stopPullDownRefresh();
  }
}) 