package com.xiao.temu.common.response;

/**
 * 分页请求
 */
public class PageRequest {
    
    /** 当前页码，从1开始 */
    private Integer pageNum = 1;
    
    /** 每页记录数 */
    private Integer pageSize = 10;
    
    /** 排序字段 */
    private String orderByColumn;
    
    /** 排序方向（asc/desc） */
    private String orderDirection;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getOrderByColumn() {
        return orderByColumn;
    }

    public void setOrderByColumn(String orderByColumn) {
        this.orderByColumn = orderByColumn;
    }

    public String getOrderDirection() {
        return orderDirection;
    }

    public void setOrderDirection(String orderDirection) {
        this.orderDirection = orderDirection;
    }
} 