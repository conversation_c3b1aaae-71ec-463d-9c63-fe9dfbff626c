<template>
  <div class="table-section">
    <el-card class="box-card table-card">
      <slot name="tabs"></slot>
      <div class="table-container">
        <slot></slot>
      </div>
    </el-card>
  </div>
</template>

<script setup>
// 表格卡片组件
// 使用方式：
// <TableCard>
//   <template #tabs>
//     <el-tabs v-model="activeTab">...</el-tabs>
//   </template>
//   <el-table :data="dataList">...</el-table>
// </TableCard>
</script>

<style scoped>
.table-section {
  flex: 1;
  overflow: hidden;
  margin: 0;
  padding: 0;
  margin-bottom: 60px;
  min-height: 150px;
  width: 100%;
}

.table-section .box-card {
  border-radius: 0 0 4px 4px;
  border-top: none;
}

.table-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.table-card :deep(.el-card__body) {
  padding: 8px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.table-container {
  flex: 1;
  overflow: auto;
  width: 100%;
}

/* 标签页样式 */
.table-card :deep(.el-tabs) {
  padding: 8px 8px 0 8px;
}

.table-card :deep(.el-tabs__header) {
  margin-bottom: 8px;
}
</style> 