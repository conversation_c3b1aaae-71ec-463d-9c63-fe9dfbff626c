<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>消息模板管理</span>
          <el-button type="primary" @click="handleAdd">新增模板</el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
        <el-form-item label="模板名称" prop="templateName">
          <el-input
            v-model="queryParams.templateName"
            placeholder="请输入模板名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        
        <el-form-item label="模板类型" prop="templateType">
          <el-select v-model="queryParams.templateType" placeholder="请选择模板类型" clearable style="width: 180px">
            <el-option label="系统消息" value="1" />
            <el-option label="任务提醒" value="2" />
            <el-option label="店铺消息" value="3" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="正常" value="0" />
            <el-option label="禁用" value="1" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon> 搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon> 重置
          </el-button>
        </el-form-item>
      </el-form>
      
      <!-- 模板列表 -->
      <el-table
        v-loading="loading"
        :data="templateList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" width="50" label="#" />
        
        <el-table-column prop="templateName" label="模板名称" min-width="120" show-overflow-tooltip />
        
        <el-table-column prop="templateCode" label="模板编码" min-width="120" show-overflow-tooltip />
        
        <el-table-column prop="titleTemplate" label="标题模板" min-width="150" show-overflow-tooltip />
        
        <el-table-column prop="contentTemplate" label="内容模板" min-width="200" show-overflow-tooltip />
        
        <el-table-column label="模板类型" align="center" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.templateType === '1'" type="primary">系统</el-tag>
            <el-tag v-else-if="scope.row.templateType === '2'" type="success">任务</el-tag>
            <el-tag v-else-if="scope.row.templateType === '3'" type="warning">店铺</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" align="center" width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.status === '0'" type="success">正常</el-tag>
            <el-tag v-else type="danger">禁用</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="scope">
            {{ formatDatetime(scope.row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="success" link @click="handlePreview(scope.row)">预览</el-button>
            <el-button 
              type="danger" 
              link 
              @click="handleDelete(scope.row)"
              v-if="scope.row.templateCode !== 'SYSTEM_NOTICE' && scope.row.templateCode !== 'TASK_REMINDER' && scope.row.templateCode !== 'SHOP_NOTICE'"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      
      <!-- 模板编辑对话框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="dialog.title"
        width="700px"
        append-to-body
      >
        <el-form :model="templateForm" :rules="rules" ref="templateFormRef" label-width="100px">
          <el-form-item label="模板名称" prop="templateName">
            <el-input v-model="templateForm.templateName" placeholder="请输入模板名称" />
          </el-form-item>
          
          <el-form-item label="模板编码" prop="templateCode">
            <el-input 
              v-model="templateForm.templateCode" 
              placeholder="请输入模板编码"
              :disabled="templateForm.templateId !== undefined"
            />
          </el-form-item>
          
          <el-form-item label="模板类型" prop="templateType">
            <el-radio-group v-model="templateForm.templateType">
              <el-radio label="1">系统消息</el-radio>
              <el-radio label="2">任务提醒</el-radio>
              <el-radio label="3">店铺消息</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="标题模板" prop="titleTemplate">
            <el-input v-model="templateForm.titleTemplate" placeholder="请输入标题模板" />
            <div class="form-help">支持使用${variable}格式的变量，如：${title}</div>
          </el-form-item>
          
          <el-form-item label="内容模板" prop="contentTemplate">
            <el-input
              v-model="templateForm.contentTemplate"
              type="textarea"
              :rows="5"
              placeholder="请输入内容模板"
            />
            <div class="form-help">支持使用${variable}格式的变量，如：${content}、${username}等</div>
          </el-form-item>
          
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="templateForm.status">
              <el-radio label="0">正常</el-radio>
              <el-radio label="1">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="templateForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-form>
        
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitForm" :loading="submitLoading">确定</el-button>
          </span>
        </template>
      </el-dialog>
      
      <!-- 预览对话框 -->
      <el-dialog
        v-model="previewVisible"
        title="模板预览"
        width="600px"
        append-to-body
      >
        <div class="template-preview">
          <div class="template-code">模板编码: {{ currentTemplate?.templateCode }}</div>
          
          <div class="template-variables">
            <h4>变量设置:</h4>
            <div class="variables-form">
              <el-form :inline="true">
                <el-form-item label="标题" v-if="previewVars.title !== undefined">
                  <el-input v-model="previewVars.title" placeholder="标题变量" />
                </el-form-item>
                <el-form-item label="内容" v-if="previewVars.content !== undefined">
                  <el-input v-model="previewVars.content" placeholder="内容变量" />
                </el-form-item>
                <el-form-item label="用户名" v-if="previewVars.username !== undefined">
                  <el-input v-model="previewVars.username" placeholder="用户名变量" />
                </el-form-item>
                <el-form-item label="任务名称" v-if="previewVars.taskName !== undefined">
                  <el-input v-model="previewVars.taskName" placeholder="任务名称变量" />
                </el-form-item>
                <el-form-item label="店铺名称" v-if="previewVars.shopName !== undefined">
                  <el-input v-model="previewVars.shopName" placeholder="店铺名称变量" />
                </el-form-item>
                <el-form-item label="结束时间" v-if="previewVars.endTime !== undefined">
                  <el-date-picker
                    v-model="previewVars.endTime"
                    type="datetime"
                    placeholder="结束时间变量"
                  />
                </el-form-item>
              </el-form>
            </div>
          </div>
          
          <div class="preview-result">
            <h4>预览结果:</h4>
            <div class="preview-title">{{ previewResult.title }}</div>
            <div class="preview-content">{{ previewResult.content }}</div>
          </div>
        </div>
        
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="previewVisible = false">关闭</el-button>
            <el-button type="primary" @click="applyPreview">应用变量</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { getTemplateList, getTemplateDetail, addTemplate, updateTemplate, deleteTemplate } from '@/api/message'
import type { MessageTemplate } from '@/types/message'
import { MessageType } from '@/types/message'
import { formatTime as formatDatetime } from '@/utils/format'

// 加载状态
const loading = ref(false)
const submitLoading = ref(false)

// 模板列表和总数
const templateList = ref<MessageTemplate[]>([])
const total = ref(0)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  templateName: '',
  templateType: '',
  status: ''
})

// 对话框显示状态
const dialogVisible = ref(false)
const previewVisible = ref(false)

// 对话框配置
const dialog = reactive({
  title: '添加模板',
  isAdd: true
})

// 表单对象
const templateFormRef = ref<FormInstance>()
const templateForm = reactive<any>({
  templateName: '',
  templateCode: '',
  templateType: '1',
  titleTemplate: '',
  contentTemplate: '',
  status: '0',
  remark: ''
})

// 表单校验规则
const rules = reactive<FormRules>({
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  templateCode: [
    { required: true, message: '请输入模板编码', trigger: 'blur' },
    { pattern: /^[A-Z][A-Z0-9_]*$/, message: '编码必须以大写字母开头，只能包含大写字母、数字和下划线', trigger: 'blur' }
  ],
  templateType: [
    { required: true, message: '请选择模板类型', trigger: 'change' }
  ],
  titleTemplate: [
    { required: true, message: '请输入标题模板', trigger: 'blur' },
    { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  contentTemplate: [
    { required: true, message: '请输入内容模板', trigger: 'blur' },
    { min: 2, max: 2000, message: '长度在 2 到 2000 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
})

// 加载模板列表
const loadTemplateList = async () => {
  loading.value = true
  try {
    const response = await getTemplateList(queryParams)
    if (response && response.data) {
      templateList.value = response.data.records || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('加载模板列表失败', error)
  } finally {
    loading.value = false
  }
}

// 处理查询
const handleQuery = () => {
  queryParams.pageNum = 1
  loadTemplateList()
}

// 重置查询条件
const resetQuery = () => {
  queryParams.templateName = ''
  queryParams.templateType = ''
  queryParams.status = ''
  queryParams.pageNum = 1
  loadTemplateList()
}

// 处理分页变化
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  loadTemplateList()
}

const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page
  loadTemplateList()
}

// 添加模板
const handleAdd = () => {
  dialog.title = '添加模板'
  dialog.isAdd = true
  
  // 重置表单
  Object.assign(templateForm, {
    templateName: '',
    templateCode: '',
    templateType: '1',
    titleTemplate: '',
    contentTemplate: '',
    status: '0',
    remark: ''
  })
  
  dialogVisible.value = true
}

// 编辑模板
const handleEdit = async (row: MessageTemplate) => {
  dialog.title = '编辑模板'
  dialog.isAdd = false
  
  try {
    const response = await getTemplateDetail(row.templateId)
    if (response && response.data) {
      Object.assign(templateForm, response.data)
    }
  } catch (error) {
    console.error('获取模板详情失败', error)
  }
  
  dialogVisible.value = true
}

// 提交表单
const submitForm = async () => {
  if (!templateFormRef.value) return
  
  await templateFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        submitLoading.value = true
        
        let response
        if (dialog.isAdd) {
          // 添加模板
          response = await addTemplate(templateForm)
        } else {
          // 更新模板
          response = await updateTemplate(templateForm.templateId, templateForm)
        }
        
        if (response && response.data) {
          const message = dialog.isAdd ? '添加成功' : '更新成功'
          ElMessage.success(message)
          dialogVisible.value = false
          loadTemplateList()
        }
      } catch (error) {
        console.error('提交表单失败', error)
      } finally {
        submitLoading.value = false
      }
    }
  })
}

// 删除模板
const handleDelete = (row: MessageTemplate) => {
  ElMessageBox.confirm(`确定要删除模板"${row.templateName}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await deleteTemplate(row.templateId)
      if (response && response.data) {
        ElMessage.success('删除成功')
        loadTemplateList()
      }
    } catch (error) {
      console.error('删除模板失败', error)
      ElMessage.error('删除模板失败')
    }
  }).catch(() => {})
}

// 预览功能
const currentTemplate = ref<MessageTemplate>()
const previewVars = reactive<Record<string, any>>({})
const previewResult = reactive({
  title: '',
  content: ''
})

// 提取模板中的变量
const extractVariables = (template: string) => {
  const regex = /\${([^}]+)}/g
  const variables: string[] = []
  let match
  
  while ((match = regex.exec(template)) !== null) {
    variables.push(match[1])
  }
  
  return [...new Set(variables)]
}

// 替换模板变量
const replaceVariables = (template: string, variables: Record<string, any>) => {
  return template.replace(/\${([^}]+)}/g, (match, key) => {
    if (key in variables) {
      if (key === 'endTime' && variables[key]) {
        return typeof variables[key] === 'string' 
          ? variables[key] 
          : formatDatetime(variables[key])
      }
      return variables[key] || ''
    }
    return match
  })
}

// 处理预览
const handlePreview = (row: MessageTemplate) => {
  currentTemplate.value = row
  
  // 提取变量
  const titleVars = extractVariables(row.titleTemplate)
  const contentVars = extractVariables(row.contentTemplate)
  const allVars = [...new Set([...titleVars, ...contentVars])]
  
  // 重置预览变量
  Object.keys(previewVars).forEach(key => {
    delete previewVars[key]
  })
  
  // 初始化预览变量
  allVars.forEach(variable => {
    if (variable === 'title') {
      previewVars[variable] = '测试标题'
    } else if (variable === 'content') {
      previewVars[variable] = '这是一条测试内容，用于预览模板效果。'
    } else if (variable === 'username') {
      previewVars[variable] = '测试用户'
    } else if (variable === 'taskName') {
      previewVars[variable] = '测试任务'
    } else if (variable === 'shopName') {
      previewVars[variable] = '测试店铺'
    } else if (variable === 'endTime') {
      previewVars[variable] = new Date(Date.now() + 86400000) // 明天
    } else {
      previewVars[variable] = `[${variable}]`
    }
  })
  
  // 应用预览
  applyPreview()
  
  previewVisible.value = true
}

// 应用预览变量
const applyPreview = () => {
  if (!currentTemplate.value) return
  
  previewResult.title = replaceVariables(currentTemplate.value.titleTemplate, previewVars)
  previewResult.content = replaceVariables(currentTemplate.value.contentTemplate, previewVars)
}

// 监听预览变量变化
watch(previewVars, () => {
  applyPreview()
}, { deep: true })

// 初始化加载
loadTemplateList()
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.template-preview {
  padding: 20px;
}

.template-code {
  margin-bottom: 15px;
  font-weight: bold;
  color: #606266;
}

.template-variables {
  margin-bottom: 20px;
}

.variables-form {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.preview-result {
  margin-top: 20px;
}

.preview-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  padding: 10px;
  background-color: #ecf5ff;
  border-left: 4px solid #409eff;
  border-radius: 4px;
}

.preview-content {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-height: 100px;
  white-space: pre-wrap;
  word-break: break-all;
}
</style> 