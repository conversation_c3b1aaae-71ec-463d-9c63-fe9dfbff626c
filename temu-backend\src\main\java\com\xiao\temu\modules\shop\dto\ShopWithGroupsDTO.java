package com.xiao.temu.modules.shop.dto;

import com.xiao.temu.modules.operation.entity.OperationGroup;
import com.xiao.temu.modules.shop.entity.Shop;

import java.util.List;

/**
 * 店铺及其所属运营组DTO
 */
public class ShopWithGroupsDTO extends Shop {
    
    /**
     * 店铺所属的运营组列表
     */
    private List<OperationGroup> groups;
    
    /**
     * 运营组名称列表（格式化后的字符串）
     */
    private String groupNames;

    public List<OperationGroup> getGroups() {
        return groups;
    }

    public void setGroups(List<OperationGroup> groups) {
        this.groups = groups;
    }

    public String getGroupNames() {
        return groupNames;
    }

    public void setGroupNames(String groupNames) {
        this.groupNames = groupNames;
    }
} 