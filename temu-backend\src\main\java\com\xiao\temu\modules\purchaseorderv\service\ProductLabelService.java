package com.xiao.temu.modules.purchaseorderv.service;

import com.xiao.temu.modules.purchaseorderv.dto.ProductLabelRequestDTO;
import com.xiao.temu.modules.purchaseorderv.vo.ProductLabelVO;

/**
 * 商品标签服务接口
 */
public interface ProductLabelService {
    
    /**
     * 获取商品标签数据
     *
     * @param requestDTO 请求参数
     * @param userId 当前用户ID
     * @return 商品标签数据
     */
    ProductLabelVO getProductLabels(ProductLabelRequestDTO requestDTO, Long userId);
} 