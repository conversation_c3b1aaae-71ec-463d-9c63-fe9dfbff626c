package com.xiao.temu.modules.refund.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.refund.entity.RefundPackageDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 退货包裹明细Mapper接口
 */
@Mapper
public interface RefundPackageDetailMapper extends BaseMapper<RefundPackageDetail> {
    
    /**
     * 批量插入退货包裹明细记录
     *
     * @param details 退货包裹明细列表
     * @return 插入成功的记录数
     */
    int batchInsert(@Param("list") List<RefundPackageDetail> details);
    
    /**
     * 根据店铺ID获取最近的出库时间
     *
     * @param shopId 店铺ID
     * @return 最近的出库时间（毫秒时间戳）
     */
    Long getLatestOutboundTime(@Param("shopId") Long shopId);
    
    /**
     * 获取指定店铺的退货包裹记录总数
     *
     * @param shopId 店铺ID
     * @return 退货包裹记录总数
     */
    @Select("SELECT COUNT(*) FROM refund_package_detail WHERE shop_id = #{shopId}")
    Integer getRefundPackageCountByShopId(@Param("shopId") Long shopId);
} 