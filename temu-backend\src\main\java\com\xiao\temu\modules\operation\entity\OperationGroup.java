package com.xiao.temu.modules.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 运营组实体类
 */
@Data
@TableName("operation_group")
public class OperationGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 运营组ID
     */
    @TableId(value = "group_id", type = IdType.AUTO)
    private Long groupId;

    /**
     * 运营组名称
     */
    private String groupName;

    /**
     * 负责人ID
     */
    private Long leaderId;

    /**
     * 状态（0正常 1禁用）
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;
} 