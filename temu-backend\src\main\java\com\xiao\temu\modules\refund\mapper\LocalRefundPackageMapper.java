package com.xiao.temu.modules.refund.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.refund.entity.RefundPackageDetail;
import com.xiao.temu.modules.refund.vo.LocalRefundPackageVO.LocalRefundPackageItemVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 本地退货包裹数据Mapper接口
 */
@Mapper
public interface LocalRefundPackageMapper extends BaseMapper<RefundPackageDetail> {
    
    /**
     * 分页查询退货包裹数据
     *
     * @param page 分页对象
     * @param shopIds 店铺ID列表
     * @param outboundTimeStart 出库开始时间
     * @param outboundTimeEnd 出库结束时间
     * @param productSkuIdList SKU ID列表
     * @param returnSupplierPackageNosList 退货包裹号列表
     * @param purchaseSubOrderSnsList 备货单号列表
     * @return 分页数据
     */
    IPage<LocalRefundPackageItemVO> selectRefundPackagePage(
            Page<LocalRefundPackageItemVO> page,
            @Param("shopIds") List<Long> shopIds,
            @Param("outboundTimeStart") LocalDateTime outboundTimeStart,
            @Param("outboundTimeEnd") LocalDateTime outboundTimeEnd,
            @Param("productSkuIdList") List<Long> productSkuIdList,
            @Param("returnSupplierPackageNosList") List<String> returnSupplierPackageNosList,
            @Param("purchaseSubOrderSnsList") List<String> purchaseSubOrderSnsList
    );
} 