/**
 * 本地退货明细查询请求参数
 */
export interface LocalRefundRequestParams {
  shopIds?: number[]
  outboundTimeStart?: string
  outboundTimeEnd?: string
  pageNo: number
  pageSize: number
  productSkuIdList: number[]
  returnSupplierPackageNos: string[]
  purchaseSubOrderSns: string[]
}

/**
 * 本地退货明细数据项
 */
export interface LocalRefundPackageItem {
  id: number
  shopId: number
  shopName: string
  shopRemark?: string
  packageSn: string
  productSkuId: number
  productSkcId: number
  productSpuId: number
  purchaseSubOrderSn: string
  quantity: number
  mainSaleSpec: string
  secondarySaleSpec: string
  thumbUrl: string
  orderTypeDesc: string
  reasonDesc: string
  remark: string
  outboundTime: string
}

/**
 * 本地退货明细数据
 */
export interface LocalRefundPackageData {
  total: number
  pageNo: number
  pageSize: number
  items: LocalRefundPackageItem[]
  shopName: string
  shopId: number
  shopRemark?: string
}

/**
 * 本地退货明细响应
 */
export interface LocalRefundPackageResponse {
  code: number
  message: string
  data: LocalRefundPackageData
} 