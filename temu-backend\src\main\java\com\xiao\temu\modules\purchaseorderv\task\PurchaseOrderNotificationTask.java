package com.xiao.temu.modules.purchaseorderv.task;

import com.xiao.temu.modules.purchaseorderv.service.PurchaseOrderNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 备货单通知工具类
 * 注意: 定时任务已迁移到Quartz实现 {@link com.xiao.temu.modules.purchaseorderv.job.PurchaseOrderNotificationQuartzJob}
 */
@Slf4j
@Component
public class PurchaseOrderNotificationTask {
    
    @Autowired
    private PurchaseOrderNotificationService purchaseOrderNotificationService;
    
    /**
     * 手动执行JIT即将逾期通知检查
     */
    public void checkJitSoonOverdue() {
        log.info("开始执行JIT即将逾期通知任务");
        try {
            purchaseOrderNotificationService.processJitSoonOverdueNotification();
        } catch (Exception e) {
            log.error("执行JIT即将逾期通知任务出错：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 手动执行JIT已逾期通知检查
     */
    public void checkJitOverdue() {
        log.info("开始执行JIT已逾期通知任务");
        try {
            purchaseOrderNotificationService.processJitOverdueNotification();
        } catch (Exception e) {
            log.error("执行JIT已逾期通知任务出错：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 手动执行普通备货未发货通知检查
     */
    public void checkNormalNotDelivered() {
        log.info("开始执行普通备货未发货通知任务");
        try {
            purchaseOrderNotificationService.processNormalNotDeliveredNotification();
        } catch (Exception e) {
            log.error("执行普通备货未发货通知任务出错：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 手动执行普通备货未到货通知检查
     */
    public void checkNormalNotReceived() {
        log.info("开始执行普通备货未到货通知任务");
        try {
            purchaseOrderNotificationService.processNormalNotReceivedNotification();
        } catch (Exception e) {
            log.error("执行普通备货未到货通知任务出错：{}", e.getMessage(), e);
        }
    }
} 