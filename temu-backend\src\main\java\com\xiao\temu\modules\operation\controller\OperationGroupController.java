package com.xiao.temu.modules.operation.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.infrastructure.exporter.dto.ExportRequestDTO;
import com.xiao.temu.modules.operation.dto.GroupMemberDTO;
import com.xiao.temu.modules.operation.dto.GroupStatisticsDTO;
import com.xiao.temu.modules.operation.dto.OperationGroupDTO;
import com.xiao.temu.modules.operation.dto.QueryGroupDTO;
import com.xiao.temu.modules.operation.entity.OperationGroup;
import com.xiao.temu.modules.shop.dto.ShopWithGroupsDTO;
import com.xiao.temu.security.annotation.RequiresPermission;
import com.xiao.temu.security.model.UserDetailsImpl;
import com.xiao.temu.modules.operation.service.GroupMemberService;
import com.xiao.temu.modules.operation.service.OperationGroupService;
import com.xiao.temu.modules.system.service.SysDataPermissionService;
import com.xiao.temu.infrastructure.excel.ExcelUtils;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;

/**
 * 运营组管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/operation/group")
@RequiredArgsConstructor
public class OperationGroupController {

    private final OperationGroupService operationGroupService;
    private final GroupMemberService groupMemberService;
    private final SysDataPermissionService dataPermissionService;

    /**
     * 获取运营组列表
     */
    @GetMapping("/list")
    @RequiresPermission("operation:group:list")
    public ApiResponse getGroupList(QueryGroupDTO queryDTO) {
        return ApiResponse.success(operationGroupService.getGroupList(queryDTO));
    }

    /**
     * 获取运营组详细信息
     */
    @GetMapping("/{groupId}")
    @RequiresPermission("operation:group:query")
    public ApiResponse getGroup(@PathVariable Long groupId) {
        OperationGroupDTO group = operationGroupService.getGroupById(groupId);
        if (group == null) {
            return ApiResponse.error("运营组不存在");
        }
        return ApiResponse.success(group);
    }

    /**
     * 新增运营组
     */
    @PostMapping
    @RequiresPermission("operation:group:add")
    public ApiResponse addGroup(@Validated @RequestBody OperationGroupDTO groupDTO) {
        // 校验运营组名称是否唯一
        if (!operationGroupService.checkGroupNameUnique(groupDTO.getGroupName(), null)) {
            return ApiResponse.error("新增运营组'" + groupDTO.getGroupName() + "'失败，运营组名称已存在");
        }
        
        // 插入运营组信息
        int rows = operationGroupService.insertGroup(groupDTO);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("新增运营组失败");
    }

    /**
     * 修改运营组
     */
    @PutMapping
    @RequiresPermission("operation:group:edit")
    public ApiResponse updateGroup(@Validated @RequestBody OperationGroupDTO groupDTO) {
        // 校验运营组ID是否存在
        if (groupDTO.getGroupId() == null) {
            return ApiResponse.error("修改运营组失败，运营组ID不能为空");
        }
        
        // 校验运营组名称是否唯一
        if (!operationGroupService.checkGroupNameUnique(groupDTO.getGroupName(), groupDTO.getGroupId())) {
            return ApiResponse.error("修改运营组'" + groupDTO.getGroupName() + "'失败，运营组名称已存在");
        }
        
        // 更新运营组信息
        int rows = operationGroupService.updateGroup(groupDTO);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("修改运营组失败");
    }

    /**
     * 删除运营组
     */
    @DeleteMapping("/{groupIds}")
    @RequiresPermission("operation:group:remove")
    public ApiResponse deleteGroup(@PathVariable Long[] groupIds) {
        try {
            // 删除运营组
            int rows = operationGroupService.deleteGroups(groupIds);
            return rows > 0 ? ApiResponse.success() : ApiResponse.error("删除运营组失败");
        } catch (RuntimeException e) {
            // 捕获业务异常并返回错误信息
            log.error("删除运营组失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 修改运营组状态
     */
    @PutMapping("/changeStatus")
    @RequiresPermission("operation:group:edit")
    public ApiResponse changeStatus(@RequestParam Long groupId, @RequestParam String status) {
        // 修改运营组状态
        int rows = operationGroupService.changeStatus(groupId, status);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("修改运营组状态失败");
    }

    /**
     * 设置运营组负责人
     */
    @PutMapping("/{groupId}/leader/{userId}")
    @RequiresPermission("operation:group:edit")
    public ApiResponse setGroupLeader(@PathVariable Long groupId, @PathVariable Long userId) {
        // 设置运营组负责人
        int rows = operationGroupService.setGroupLeader(groupId, userId);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("设置运营组负责人失败");
    }

    /**
     * 获取运营组成员
     */
    @GetMapping("/{groupId}/members")
    @RequiresPermission("operation:group:query")
    public ApiResponse getGroupMembers(@PathVariable Long groupId,
                                       @RequestParam(defaultValue = "1") Integer pageNum,
                                       @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<GroupMemberDTO> page = new Page<>(pageNum, pageSize);
        IPage<GroupMemberDTO> members = groupMemberService.getMemberList(groupId, page);
        return ApiResponse.success(members);
    }

    /**
     * 添加运营组成员
     */
    @PostMapping("/{groupId}/members")
    @RequiresPermission("operation:group:edit")
    public ApiResponse addGroupMembers(@PathVariable Long groupId, 
                                      @RequestBody List<Long> userIds) {
        // 添加运营组成员
        int rows = groupMemberService.batchAddMembers(groupId, userIds);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("添加运营组成员失败");
    }

    /**
     * 移除运营组成员
     */
    @DeleteMapping("/{groupId}/members/{userId}")
    @RequiresPermission("operation:group:edit")
    public ApiResponse removeGroupMember(@PathVariable Long groupId, 
                                        @PathVariable Long userId) {
        // 移除运营组成员
        int rows = groupMemberService.removeMember(groupId, userId);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("移除运营组成员失败");
    }

    /**
     * 获取运营组统计数据
     */
    @GetMapping("/{groupId}/statistics")
    @RequiresPermission("operation:group:query")
    public ApiResponse getGroupStatistics(@PathVariable Long groupId) {
        // 获取运营组统计数据
        GroupStatisticsDTO statistics = operationGroupService.getGroupStatistics(groupId);
        
        return ApiResponse.success(statistics);
    }

    /**
     * 获取用户所在的运营组列表
     */
    @GetMapping("/user")
    public ApiResponse getUserGroups() {
        // 获取当前登录用户ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !(authentication.getPrincipal() instanceof UserDetailsImpl)) {
            return ApiResponse.error("用户未登录");
        }
        
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        Long userId = userDetails.getUserId();
        
        // 检查是否管理员
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        // 获取用户的最高数据权限
        String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
        
        // 如果是管理员或拥有全部数据权限，则返回所有运营组
        if (isAdmin || "2".equals(permissionType)) {
            QueryGroupDTO queryDTO = new QueryGroupDTO();
            queryDTO.setPageNum(1);
            queryDTO.setPageSize(Integer.MAX_VALUE);
            IPage<OperationGroupDTO> groupPage = operationGroupService.getGroupList(queryDTO);
            return ApiResponse.success(groupPage.getRecords());
        }
        
        // 否则，只返回用户所在的运营组
        List<OperationGroupDTO> groups = operationGroupService.getGroupsByMemberId(userId);
        
        return ApiResponse.success(groups);
    }

    /**
     * 获取指定用户所在的运营组列表
     */
    @GetMapping("/listByMember/{userId}")
    @RequiresPermission("operation:group:query")
    public ApiResponse getGroupsByMemberId(@PathVariable Long userId) {
        // 获取用户所在的运营组列表
        List<OperationGroupDTO> groups = operationGroupService.getGroupsByMemberId(userId);
        
        return ApiResponse.success(groups);
    }

    /**
     * 获取用户负责的运营组列表
     */
    @GetMapping("/leader")
    public ApiResponse getLeaderGroups() {
        // 获取当前登录用户ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !(authentication.getPrincipal() instanceof UserDetailsImpl)) {
            return ApiResponse.error("用户未登录");
        }
        
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        Long userId = userDetails.getUserId();
        
        // 检查是否管理员
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        // 获取用户的最高数据权限
        String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
        
        // 如果是管理员或拥有全部数据权限，则返回所有运营组
        if (isAdmin || "2".equals(permissionType)) {
            QueryGroupDTO queryDTO = new QueryGroupDTO();
            queryDTO.setPageNum(1);
            queryDTO.setPageSize(Integer.MAX_VALUE);
            IPage<OperationGroupDTO> groupPage = operationGroupService.getGroupList(queryDTO);
            return ApiResponse.success(groupPage.getRecords());
        }
        
        // 否则，只返回用户负责的运营组
        List<OperationGroup> groups = operationGroupService.getGroupsByLeaderId(userId);
        
        return ApiResponse.success(groups);
    }

    /**
     * 获取未分配给任何运营组的店铺列表
     */
    @GetMapping("/{groupId}/unassigned-shops")
    @RequiresPermission("operation:group:query")
    public ApiResponse getUnassignedShops(@PathVariable Long groupId) {
        // 获取当前用户ID
        Long userId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        
        // 检查用户是否有权限操作该运营组(是管理员或组长)
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        boolean isGroupLeader = groupId != null && operationGroupService.isUserGroupLeader(userId);
        
        if (!isAdmin && !isGroupLeader) {
            return ApiResponse.error("您没有权限查询未分配的店铺");
        }
        
        try {
            // 获取未分配给运营组的店铺列表
            List<ShopWithGroupsDTO> shops = operationGroupService.getUnassignedShops(groupId);
            return ApiResponse.success(shops);
        } catch (Exception e) {
            log.error("获取未分配店铺列表失败", e);
            return ApiResponse.error("获取未分配店铺列表失败: " + e.getMessage());
        }
    }

    /**
     * 为运营组分配店铺
     */
    @PostMapping("/{groupId}/assign-shops")
    @RequiresPermission("operation:group:edit")
    public ApiResponse assignShopsToGroup(@PathVariable Long groupId, @RequestBody List<Long> shopIds) {
        if (shopIds == null || shopIds.isEmpty()) {
            return ApiResponse.error("店铺ID列表不能为空");
        }
        
        // 为运营组分配店铺
        int rows = operationGroupService.assignShopsToGroup(groupId, shopIds);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("分配店铺失败");
    }

    /**
     * 从运营组中移除店铺
     */
    @DeleteMapping("/{groupId}/shops/{shopId}")
    @RequiresPermission("operation:group:edit")
    public ApiResponse removeShopFromGroup(@PathVariable Long groupId, @PathVariable Long shopId) {
        // 验证运营组和店铺是否存在
        OperationGroupDTO group = operationGroupService.getGroupById(groupId);
        if (group == null) {
            return ApiResponse.error("运营组不存在");
        }
        
        // 从运营组中移除店铺
        int rows = operationGroupService.removeShopFromGroup(groupId, shopId);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("移除店铺失败");
    }

    /**
     * 导出运营组数据
     */
    @PostMapping("/export")
    @RequiresPermission("operation:group:export")
    public void exportGroups(@Validated @RequestBody ExportRequestDTO exportDTO, HttpServletResponse response) throws IOException {
        try {
            // 获取导出数据
            List<OperationGroupDTO> groupList = operationGroupService.getExportData(exportDTO);
            
            // 设置默认文件名和Sheet名
            String fileName = exportDTO.getFileName() != null ? exportDTO.getFileName() : "运营组数据";
            String sheetName = exportDTO.getSheetName() != null ? exportDTO.getSheetName() : "运营组列表";
            
            // 执行导出
            ExcelUtils.export(response, groupList, fileName, sheetName, OperationGroupDTO.class);
        } catch (Exception e) {
            log.error("导出运营组数据失败", e);
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().write("{\"code\":500,\"msg\":\"导出失败，" + e.getMessage() + "\"}");
        }
    }
} 