package com.xiao.temu.modules.purchaseorderv.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xiao.temu.common.constant.PurchaseOrderNotificationConstants;
import com.xiao.temu.modules.purchaseorderv.dto.NotificationMatchDTO;
import com.xiao.temu.modules.purchaseorderv.dto.NotificationTriggerDTO;
import com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderNotificationConfig;
import com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderNotification;
import com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderV;
import com.xiao.temu.modules.purchaseorderv.mapper.PurchaseOrderNotificationMapper;
import com.xiao.temu.modules.purchaseorderv.mapper.PurchaseOrderVMapper;
import com.xiao.temu.modules.purchaseorderv.service.PurchaseOrderNotificationConfigService;
import com.xiao.temu.modules.purchaseorderv.vo.NotificationConfigVO;
import com.xiao.temu.modules.purchaseorderv.vo.NotificationMatchOrderVO;
import com.xiao.temu.modules.sync.vo.ApiResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 备货单通知管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/purchase-order/notification-manage")
public class PurchaseOrderNotificationManageController {
    
    @Autowired
    private PurchaseOrderNotificationConfigService notificationConfigService;
    
    @Autowired
    private PurchaseOrderVMapper purchaseOrderVMapper;
    
    @Autowired
    private PurchaseOrderNotificationMapper notificationMapper;
    
    // 用于存储测试设置的Map
    private static final Map<String, Object> testSettings = new HashMap<>();
    
    /**
     * 获取通知类型列表
     */
    @GetMapping("/notification-types")
    public ApiResponse getNotificationTypes() {
        try {
            List<Map<String, Object>> types = new ArrayList<>();
            
            Map<String, Object> type1 = new HashMap<>();
            type1.put("value", PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_SOON_OVERDUE);
            type1.put("label", "JIT备货单到货即将逾期通知");
            types.add(type1);
            
            Map<String, Object> type2 = new HashMap<>();
            type2.put("value", PurchaseOrderNotificationConstants.NOTIFY_TYPE_JIT_OVERDUE);
            type2.put("label", "JIT备货单到货已逾期通知");
            types.add(type2);
            
            Map<String, Object> type3 = new HashMap<>();
            type3.put("value", PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_DELIVERED);
            type3.put("label", "普通备货未发货通知");
            types.add(type3);
            
            Map<String, Object> type4 = new HashMap<>();
            type4.put("value", PurchaseOrderNotificationConstants.NOTIFY_TYPE_NORMAL_NOT_RECEIVED);
            type4.put("label", "普通备货未到货通知");
            types.add(type4);
            
            return ApiResponse.success(types);
        } catch (Exception e) {
            log.error("获取通知类型列表失败：", e);
            return ApiResponse.error("获取通知类型列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取通知配置信息
     */
    @GetMapping("/notification-config/{notificationType}")
    public ApiResponse getNotificationConfig(@PathVariable Integer notificationType) {
        try {
            PurchaseOrderNotificationConfig config = notificationConfigService.getConfigByType(notificationType);
            if (config == null) {
                return ApiResponse.error("通知配置不存在");
            }
            
            NotificationConfigVO vo = new NotificationConfigVO();
            org.springframework.beans.BeanUtils.copyProperties(config, vo);
            
            return ApiResponse.success(vo);
        } catch (Exception e) {
            log.error("获取通知配置失败：", e);
            return ApiResponse.error("获取通知配置失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取可以发送通知的备货单列表
     */
    @PostMapping("/match-orders")
    public ApiResponse getMatchOrders(@RequestBody @Valid NotificationMatchDTO matchDTO) {
        try {
            IPage<NotificationMatchOrderVO> page = notificationConfigService.previewMatchOrders(matchDTO);
            return ApiResponse.success(page);
        } catch (Exception e) {
            log.error("获取匹配订单失败：", e);
            return ApiResponse.error("获取匹配订单失败：" + e.getMessage());
        }
    }
    
    /**
     * 手动发送通知
     */
    @PostMapping("/send-notification")
    public ApiResponse sendNotification(@RequestBody Map<String, Object> params) {
        try {
            // 构建触发DTO
            NotificationTriggerDTO triggerDTO = new NotificationTriggerDTO();
            
            // 设置通知类型
            if (params.containsKey("notificationType") && params.get("notificationType") != null) {
                triggerDTO.setNotificationType(Integer.valueOf(params.get("notificationType").toString()));
            } else {
                return ApiResponse.error("通知类型不能为空");
            }
            
            // 设置店铺ID
            if (params.containsKey("shopId") && params.get("shopId") != null) {
                triggerDTO.setShopId(Long.valueOf(params.get("shopId").toString()));
            } else {
                return ApiResponse.error("店铺ID不能为空");
            }
            
            // 设置备货单号列表
            if (params.containsKey("orderSnList") && params.get("orderSnList") != null) {
                List<String> orderSnList = (List<String>) params.get("orderSnList");
                triggerDTO.setOrderSnList(orderSnList);
            } else {
                return ApiResponse.error("备货单号列表不能为空");
            }
            
            // 设置测试参数
            if (params.containsKey("skipHoursCheck")) {
                triggerDTO.setSkipHoursCheck(Boolean.valueOf(params.get("skipHoursCheck").toString()));
            }
            
            if (params.containsKey("skipDaysCheck")) {
                triggerDTO.setSkipDaysCheck(Boolean.valueOf(params.get("skipDaysCheck").toString()));
            }
            
            if (params.containsKey("skipNotifyCountCheck")) {
                triggerDTO.setSkipNotifyCountCheck(Boolean.valueOf(params.get("skipNotifyCountCheck").toString()));
            }
            
            if (params.containsKey("mockCurrentTime")) {
                triggerDTO.setMockCurrentTime(params.get("mockCurrentTime").toString());
            }
            
            // 新增：设置是否批量发送
            if (params.containsKey("batchSend")) {
                triggerDTO.setBatchSend(Boolean.valueOf(params.get("batchSend").toString()));
            } else {
                // 默认使用批量发送
                triggerDTO.setBatchSend(true);
            }
            
            // 检查配置是否存在且启用
            PurchaseOrderNotificationConfig config = notificationConfigService.getConfigByType(triggerDTO.getNotificationType());
            if (config == null) {
                return ApiResponse.error("通知配置不存在");
            }
            
            if (!config.getEnabled()) {
                return ApiResponse.error("当前通知类型已被禁用，无法发送通知");
            }
            
            // 不再检查订单是否存在或有效，由服务层处理
            
            // 执行通知发送
            int count = notificationConfigService.manualTriggerNotification(triggerDTO);
            if (count > 0) {
                return ApiResponse.success("成功发送" + count + "条通知");
            } else {
                return ApiResponse.error("没有符合条件的备货单需要发送通知，请检查订单状态是否符合通知条件");
            }
        } catch (Exception e) {
            log.error("手动发送通知失败：", e);
            return ApiResponse.error("手动发送通知失败：" + e.getMessage());
        }
    }
    
    /**
     * 测试特定通知类型的匹配逻辑
     */
    @PostMapping("/test-match/{notificationType}")
    public ApiResponse testNotificationMatch(@PathVariable Integer notificationType, @RequestBody Map<String, Object> params) {
        try {
            NotificationMatchDTO matchDTO = new NotificationMatchDTO();
            matchDTO.setNotificationType(notificationType);
            matchDTO.setPageNum(1);
            matchDTO.setPageSize(10);
            
            if (params.containsKey("shopId") && params.get("shopId") != null && !params.get("shopId").toString().isEmpty()) {
                matchDTO.setShopId(Long.valueOf(params.get("shopId").toString()));
            }
            
            if (params.containsKey("orderSnLike")) {
                matchDTO.setOrderSnLike(params.get("orderSnLike").toString());
            }
            
            if (params.containsKey("productNameLike")) {
                matchDTO.setProductNameLike(params.get("productNameLike").toString());
            }
            
            if (params.containsKey("supplierId") && params.get("supplierId") != null && !params.get("supplierId").toString().isEmpty()) {
                matchDTO.setSupplierId(Long.valueOf(params.get("supplierId").toString()));
            }
            
            // 处理前端传来的页码和每页条数
            if (params.containsKey("pageNum") && params.get("pageNum") != null && !params.get("pageNum").toString().isEmpty()) {
                matchDTO.setPageNum(Integer.valueOf(params.get("pageNum").toString()));
            }
            
            if (params.containsKey("pageSize") && params.get("pageSize") != null && !params.get("pageSize").toString().isEmpty()) {
                matchDTO.setPageSize(Integer.valueOf(params.get("pageSize").toString()));
            }
            
            IPage<NotificationMatchOrderVO> page = notificationConfigService.previewMatchOrders(matchDTO);
            
            Map<String, Object> result = new HashMap<>();
            result.put("total", page.getTotal());
            result.put("rows", page.getRecords());
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("测试通知匹配失败：", e);
            return ApiResponse.error("测试通知匹配失败：" + e.getMessage());
        }
    }
    
    /**
     * 保存测试设置
     */
    @PostMapping("/test-setting")
    public ApiResponse saveTestSetting(@RequestBody Map<String, Object> settings) {
        try {
            // 清空并重新设置
            testSettings.clear();
            testSettings.putAll(settings);
            log.info("保存测试设置成功：{}", settings);
            return ApiResponse.success("保存测试设置成功");
        } catch (Exception e) {
            log.error("保存测试设置失败：", e);
            return ApiResponse.error("保存测试设置失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取测试设置
     */
    @GetMapping("/test-setting")
    public ApiResponse getTestSetting() {
        try {
            log.info("获取测试设置：{}", testSettings);
            return ApiResponse.success(testSettings);
        } catch (Exception e) {
            log.error("获取测试设置失败：", e);
            return ApiResponse.error("获取测试设置失败：" + e.getMessage());
        }
    }
} 