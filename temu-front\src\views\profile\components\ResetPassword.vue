<template>
  <div class="reset-password-container">
    <el-form 
      ref="formRef" 
      :model="passwordForm" 
      :rules="formRules" 
      label-width="120px" 
      v-loading="loading"
    >
      <el-form-item label="当前密码" prop="oldPassword">
        <el-input 
          v-model="passwordForm.oldPassword" 
          type="password" 
          placeholder="请输入当前密码"
          show-password
        />
      </el-form-item>
      
      <el-form-item label="新密码" prop="newPassword">
        <el-input 
          v-model="passwordForm.newPassword" 
          type="password" 
          placeholder="请输入新密码"
          show-password
        />
      </el-form-item>
      
      <el-form-item label="确认新密码" prop="confirmPassword">
        <el-input 
          v-model="passwordForm.confirmPassword" 
          type="password" 
          placeholder="请再次输入新密码"
          show-password
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitForm">修改密码</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
    
    <div class="password-tips">
      <h4>密码修改须知：</h4>
      <ul>
        <li>密码长度应在6个字符以上</li>
        <li>密码修改成功后，系统将自动退出，请使用新密码重新登录</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import request from '@/utils/request'

const router = useRouter()
const userStore = useUserStore()
const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 校验新密码与确认密码是否一致
const validateConfirmPassword = (rule: any, value: string, callback: Function) => {
  if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 校验密码强度
const validatePasswordStrength = (rule: any, value: string, callback: Function) => {
  if (value.length < 6) {
    callback(new Error('密码长度不能小于6个字符'))
  } else {
    callback()
  }
}

// 表单校验规则
const formRules = reactive<FormRules>({
  oldPassword: [{ required: true, message: '请输入当前密码', trigger: 'blur' }],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { validator: validatePasswordStrength, trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
})

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (!valid) return
    
    loading.value = true
    try {
      // 调用修改密码API
      await request({
        url: '/system/user/updatePassword',
        method: 'put',
        params: {
          oldPassword: passwordForm.oldPassword,
          newPassword: passwordForm.newPassword
        }
      })
      
      ElMessage.success('密码修改成功，请重新登录')
      
      // 清除登录状态，重定向到登录页
      setTimeout(() => {
        userStore.logout().then(() => {
          router.push('/login')
        })
      }, 1500)
    } catch (error) {
      console.error('修改密码失败', error)
      ElMessage.error('修改密码失败，请确认当前密码是否正确')
    } finally {
      loading.value = false
    }
  })
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
}
</script>

<style scoped>
.reset-password-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px 0;
}

.password-tips {
  margin-top: 30px;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.password-tips h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #606266;
}

.password-tips ul {
  padding-left: 20px;
  margin: 0;
}

.password-tips li {
  line-height: 1.8;
  color: #606266;
}
</style> 