package com.xiao.temu.modules.purchaseorderv.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiao.temu.modules.purchaseorderv.constant.OperationTypeEnum;
import com.xiao.temu.modules.purchaseorderv.constant.ProgressTypeEnum;
import com.xiao.temu.modules.purchaseorderv.dto.ProductionProgressDTO;
import com.xiao.temu.modules.purchaseorderv.dto.ScanUpdateProgressDTO;
import com.xiao.temu.modules.purchaseorderv.entity.ProductionProgress;
import com.xiao.temu.modules.purchaseorderv.mapper.ProductionProgressMapper;
import com.xiao.temu.modules.purchaseorderv.service.ProductionProgressLogService;
import com.xiao.temu.modules.purchaseorderv.service.ProductionProgressService;
import com.xiao.temu.modules.purchaseorderv.vo.ProductionProgressLogVO;
import com.xiao.temu.modules.purchaseorderv.vo.ProductionProgressVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.HashMap;
import java.util.ArrayList;

// 添加日志支持
import lombok.extern.slf4j.Slf4j;

/**
 * 生产进度服务实现类
 */
@Service
@Slf4j // 添加Slf4j注解
public class ProductionProgressServiceImpl extends ServiceImpl<ProductionProgressMapper, ProductionProgress> implements ProductionProgressService {

    @Autowired
    private ProductionProgressLogService progressLogService;

    /**
     * 初始化备货单的生产进度
     *
     * @param shopId            店铺ID
     * @param subPurchaseOrderSn 备货单号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initProgress(Long shopId, String subPurchaseOrderSn) {
        // 查询是否已存在该备货单的生产进度
        LambdaQueryWrapper<ProductionProgress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductionProgress::getShopId, shopId)
                .eq(ProductionProgress::getSubPurchaseOrderSn, subPurchaseOrderSn);
        
        // 如果不存在，则创建初始进度记录
        // 修改：使用 baseMapper.exists 判断，可能更高效
        boolean exists = baseMapper.exists(queryWrapper);
        if (!exists) {
            log.info("为备货单 [shopId={}, sn={}] 初始化生产进度记录", shopId, subPurchaseOrderSn); // 添加日志
            ProductionProgress progress = new ProductionProgress();
            progress.setShopId(shopId);
            progress.setSubPurchaseOrderSn(subPurchaseOrderSn);
            // 设置默认状态为0（未完成）
            progress.setCuttingStatus(0);
            progress.setWorkshopStatus(0);
            progress.setTrimmingStatus(0);
            progress.setInspectionStatus(0);
            progress.setPackagingStatus(0);
            progress.setShippingStatus(0);
            // 设置创建时间和更新时间
            Date now = new Date();
            progress.setCreateTime(now);
            progress.setUpdateTime(now);
            
            // 保存进度记录
            this.save(progress);
        }
    }

    /**
     * 扫码更新生产进度
     *
     * @param dto          更新参数
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProgress(ScanUpdateProgressDTO dto, Long operatorId, String operatorName) {
        // 获取进度类型枚举
        ProgressTypeEnum progressType = ProgressTypeEnum.getByCode(dto.getProgressType());
        if (progressType == null) {
            return false;
        }

        // 首先确保生产进度记录存在，不存在则初始化
        initProgress(dto.getShopId(), dto.getSubPurchaseOrderSn());

        // 查询当前进度记录
        LambdaQueryWrapper<ProductionProgress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductionProgress::getShopId, dto.getShopId())
                .eq(ProductionProgress::getSubPurchaseOrderSn, dto.getSubPurchaseOrderSn());
        ProductionProgress progress = this.getOne(queryWrapper);

        if (progress == null) {
            return false;
        }

        // 创建更新构造器
        LambdaUpdateWrapper<ProductionProgress> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProductionProgress::getShopId, dto.getShopId())
                .eq(ProductionProgress::getSubPurchaseOrderSn, dto.getSubPurchaseOrderSn());

        // 根据进度类型更新对应字段
        Date now = new Date();
        switch (progressType) {
            case CUTTING:
                // 判断当前状态，如果已经完成则不用再次更新
                if (Objects.equals(progress.getCuttingStatus(), 1)) {
                    return true;
                }
                updateWrapper.set(ProductionProgress::getCuttingStatus, 1)
                        .set(ProductionProgress::getCuttingTime, now)
                        .set(ProductionProgress::getCuttingOperatorId, operatorId);
                break;
            case WORKSHOP:
                if (Objects.equals(progress.getWorkshopStatus(), 1)) {
                    return true;
                }
                updateWrapper.set(ProductionProgress::getWorkshopStatus, 1)
                        .set(ProductionProgress::getWorkshopTime, now)
                        .set(ProductionProgress::getWorkshopOperatorId, operatorId);
                break;
            case TRIMMING:
                if (Objects.equals(progress.getTrimmingStatus(), 1)) {
                    return true;
                }
                updateWrapper.set(ProductionProgress::getTrimmingStatus, 1)
                        .set(ProductionProgress::getTrimmingTime, now)
                        .set(ProductionProgress::getTrimmingOperatorId, operatorId);
                break;
            case INSPECTION:
                if (Objects.equals(progress.getInspectionStatus(), 1)) {
                    return true;
                }
                updateWrapper.set(ProductionProgress::getInspectionStatus, 1)
                        .set(ProductionProgress::getInspectionTime, now)
                        .set(ProductionProgress::getInspectionOperatorId, operatorId);
                break;
            case PACKAGING:
                if (Objects.equals(progress.getPackagingStatus(), 1)) {
                    return true;
                }
                updateWrapper.set(ProductionProgress::getPackagingStatus, 1)
                        .set(ProductionProgress::getPackagingTime, now)
                        .set(ProductionProgress::getPackagingOperatorId, operatorId);
                break;
            case SHIPPING:
                if (Objects.equals(progress.getShippingStatus(), 1)) {
                    return true;
                }
                updateWrapper.set(ProductionProgress::getShippingStatus, 1)
                        .set(ProductionProgress::getShippingTime, now)
                        .set(ProductionProgress::getShippingOperatorId, operatorId);
                break;
            default:
                return false;
        }

        // 更新修改时间
        updateWrapper.set(ProductionProgress::getUpdateTime, now);

        // 执行更新操作
        boolean updated = this.update(updateWrapper);

        // 如果更新成功，记录日志
        if (updated) {
            progressLogService.recordLog(
                    dto.getShopId(),
                    dto.getSubPurchaseOrderSn(),
                    dto.getProgressType(),
                    OperationTypeEnum.COMPLETE.getCode(),
                    operatorId,
                    operatorName,
                    dto.getRemarks()
            );
        }

        return updated;
    }

    /**
     * 撤销生产进度
     *
     * @param dto          更新参数
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否撤销成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelProgress(ScanUpdateProgressDTO dto, Long operatorId, String operatorName) {
        // 获取进度类型枚举
        ProgressTypeEnum progressType = ProgressTypeEnum.getByCode(dto.getProgressType());
        if (progressType == null) {
            return false;
        }

        // 查询当前进度记录
        LambdaQueryWrapper<ProductionProgress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductionProgress::getShopId, dto.getShopId())
                .eq(ProductionProgress::getSubPurchaseOrderSn, dto.getSubPurchaseOrderSn());
        ProductionProgress progress = this.getOne(queryWrapper);

        if (progress == null) {
            return false;
        }

        // 创建更新构造器
        LambdaUpdateWrapper<ProductionProgress> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProductionProgress::getShopId, dto.getShopId())
                .eq(ProductionProgress::getSubPurchaseOrderSn, dto.getSubPurchaseOrderSn());

        // 根据进度类型撤销对应字段
        Date now = new Date();
        switch (progressType) {
            case CUTTING:
                // 判断当前状态，如果未完成则不用撤销
                if (Objects.equals(progress.getCuttingStatus(), 0)) {
                    return true;
                }
                updateWrapper.set(ProductionProgress::getCuttingStatus, 0)
                        .set(ProductionProgress::getCuttingTime, null)
                        .set(ProductionProgress::getCuttingOperatorId, null);
                break;
            case WORKSHOP:
                if (Objects.equals(progress.getWorkshopStatus(), 0)) {
                    return true;
                }
                updateWrapper.set(ProductionProgress::getWorkshopStatus, 0)
                        .set(ProductionProgress::getWorkshopTime, null)
                        .set(ProductionProgress::getWorkshopOperatorId, null);
                break;
            case TRIMMING:
                if (Objects.equals(progress.getTrimmingStatus(), 0)) {
                    return true;
                }
                updateWrapper.set(ProductionProgress::getTrimmingStatus, 0)
                        .set(ProductionProgress::getTrimmingTime, null)
                        .set(ProductionProgress::getTrimmingOperatorId, null);
                break;
            case INSPECTION:
                if (Objects.equals(progress.getInspectionStatus(), 0)) {
                    return true;
                }
                updateWrapper.set(ProductionProgress::getInspectionStatus, 0)
                        .set(ProductionProgress::getInspectionTime, null)
                        .set(ProductionProgress::getInspectionOperatorId, null);
                break;
            case PACKAGING:
                if (Objects.equals(progress.getPackagingStatus(), 0)) {
                    return true;
                }
                updateWrapper.set(ProductionProgress::getPackagingStatus, 0)
                        .set(ProductionProgress::getPackagingTime, null)
                        .set(ProductionProgress::getPackagingOperatorId, null);
                break;
            case SHIPPING:
                if (Objects.equals(progress.getShippingStatus(), 0)) {
                    return true;
                }
                updateWrapper.set(ProductionProgress::getShippingStatus, 0)
                        .set(ProductionProgress::getShippingTime, null)
                        .set(ProductionProgress::getShippingOperatorId, null);
                break;
            default:
                return false;
        }

        // 更新修改时间
        updateWrapper.set(ProductionProgress::getUpdateTime, now);

        // 执行更新操作
        boolean updated = this.update(updateWrapper);

        // 如果更新成功，记录日志
        if (updated) {
            progressLogService.recordLog(
                    dto.getShopId(),
                    dto.getSubPurchaseOrderSn(),
                    dto.getProgressType(),
                    OperationTypeEnum.CANCEL.getCode(),
                    operatorId,
                    operatorName,
                    dto.getRemarks()
            );
        }

        return updated;
    }

    /**
     * 获取生产进度详情
     *
     * @param shopId            店铺ID
     * @param subPurchaseOrderSn 备货单号
     * @return 生产进度详情
     */
    @Override
    public ProductionProgressVO getProgressDetail(Long shopId, String subPurchaseOrderSn) {
        // 首先确保生产进度记录存在，不存在则初始化
        initProgress(shopId, subPurchaseOrderSn);
        
        // 使用Mapper查询详情，包含操作人信息
        return baseMapper.getProgressDetail(shopId, subPurchaseOrderSn);
    }

    /**
     * 获取生产进度日志
     *
     * @param shopId            店铺ID
     * @param subPurchaseOrderSn 备货单号
     * @return 生产进度日志列表
     */
    @Override
    public List<ProductionProgressLogVO> getProgressLogs(Long shopId, String subPurchaseOrderSn) {
        // 调用日志服务获取进度日志
        return progressLogService.getLogsByOrderSn(shopId, subPurchaseOrderSn);
    }

    /**
     * 分页查询生产进度
     *
     * @param dto 查询条件
     * @return 分页结果
     */
    @Override
    public IPage<ProductionProgressVO> getProgressPage(ProductionProgressDTO dto) {
        // 创建分页对象
        Page<ProductionProgressVO> page = new Page<>(
                dto.getCurrent() != null ? dto.getCurrent() : 1,
                dto.getSize() != null ? dto.getSize() : 10
        );
        
        // 使用Mapper查询分页数据
        return baseMapper.selectProgressPage(page, dto);
    }
    
    /**
     * 获取用户操作日志
     *
     * @param userId 用户ID
     * @param limit 限制返回记录数
     * @return 用户操作日志列表
     */
    @Override
    public List<ProductionProgressLogVO> getUserOperationLogs(Long userId, Integer limit) {
        return progressLogService.getLogsByOperator(userId, limit);
    }

    /**
     * 批量获取生产进度详情
     *
     * @param orders 订单数据列表，每个元素包含shopId和subPurchaseOrderSn
     * @return 生产进度详情Map，key为"shopId:subPurchaseOrderSn"，value为进度详情
     */
    @Override
    public Map<String, ProductionProgressVO> batchGetProgressDetails(List<Map<String, Object>> orders) {
        if (orders == null || orders.isEmpty()) {
            return new HashMap<>();
        }

        try {
            // 提取必要的订单信息，只包含shopId和subPurchaseOrderSn
            List<Map<String, Object>> orderInfoList = new ArrayList<>();
            for (Map<String, Object> order : orders) {
                Long shopId = null;
                String subPurchaseOrderSn = null;
                
                Object shopIdObj = order.get("shopId");
                if (shopIdObj instanceof Number) {
                    shopId = ((Number) shopIdObj).longValue();
                } else if (shopIdObj instanceof String) {
                    shopId = Long.parseLong((String) shopIdObj);
                }
                
                Object snObj = order.get("subPurchaseOrderSn");
                if (snObj instanceof String) {
                    subPurchaseOrderSn = (String) snObj;
                }
                
                if (shopId != null && subPurchaseOrderSn != null && !subPurchaseOrderSn.isEmpty()) {
                    Map<String, Object> orderInfo = new HashMap<>();
                    orderInfo.put("shopId", shopId);
                    orderInfo.put("subPurchaseOrderSn", subPurchaseOrderSn);
                    orderInfoList.add(orderInfo);
                }
            }
            
            if (orderInfoList.isEmpty()) {
                return new HashMap<>();
            }
            
            // 批量查询生产进度
            List<ProductionProgressVO> progressList = baseMapper.batchGetProgressDetails(orderInfoList);
            
            // 将结果转换为Map，便于快速查找
            Map<String, ProductionProgressVO> resultMap = new HashMap<>();
            if (progressList != null && !progressList.isEmpty()) {
                for (ProductionProgressVO progress : progressList) {
                    String key = progress.getShopId() + ":" + progress.getSubPurchaseOrderSn();
                    resultMap.put(key, progress);
                }
            }
            
            return resultMap;
        } catch (Exception e) {
            log.error("批量获取生产进度详情异常", e);
            return new HashMap<>();
        }
    }
} 