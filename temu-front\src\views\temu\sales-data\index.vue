<template>
  <div class="app-container sales-data-container">
    <el-card class="box-card search-area-card">
      <div slot="header" class="clearfix title-row">
        <span>销售数据查询</span>
      </div>
      
      <div class="search-container">
        <!-- 第一行 -->
        <div class="search-row">
          <!-- 选择店铺 -->
          <div class="search-item">
            <div class="search-label">选择店铺</div>
            <el-select
              v-model="queryParams.shopIds"
              multiple
              collapse-tags
              collapse-tags-tooltip
              placeholder="请选择店铺"
              clearable
              size="small"
              class="search-input"
              @change="handleShopSelectChange"
              @clear="isAllSelected = false"
            >
              <!-- 增加全选选项 -->
              <el-option
                key="all"
                label="全选"
                :value="'all'"
              />
              <el-option
                v-for="item in shopOptions"
                :key="item.shopId"
                :label="item.shopName"
                :value="item.shopId"
              />
            </el-select>
          </div>
          
          <!-- SPU -->
          <div class="search-item">
            <div class="search-label">SPU</div>
            <el-input 
              v-model="queryParams.productCodeList" 
              placeholder="多个用逗号分隔" 
              clearable 
              size="small"
              class="search-input" 
            />
          </div>
          
          <!-- SKC -->
          <div class="search-item">
            <div class="search-label">SKC</div>
            <el-input 
              v-model="queryParams.skcCodeList" 
              placeholder="多个用逗号分隔" 
              clearable 
              size="small"
              class="search-input" 
            />
          </div>
          
          <!-- 是否VMI -->
          <div class="search-item">
            <div class="search-label">是否VMI</div>
            <el-select 
              v-model="queryParams.isVMI" 
              placeholder="请选择" 
              clearable 
              size="small"
              class="search-input"
            >
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </div>
        </div>

        <!-- 第二行 -->
        <div class="search-row">
          <!-- 是否JIT -->
          <div class="search-item">
            <div class="search-label">是否JIT</div>
            <el-select 
              v-model="queryParams.isSuggestJit" 
              placeholder="请选择" 
              clearable 
              size="small"
              class="search-input"
            >
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </div>
          
          <!-- 时间范围 -->
          <div class="search-item">
            <div class="search-label">时间范围</div>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="timestamp"
              @change="handleDateRangeChange"
              size="small"
              class="search-input"
            />
          </div>
          
          <!-- 商品名称 -->
          <div class="search-item">
            <div class="search-label">商品名称</div>
            <el-input 
              v-model="advancedQuery.productName" 
              placeholder="请输入商品名称" 
              clearable 
              size="small"
              class="search-input" 
            />
          </div>
          
          <!-- SKU ID列表 -->
          <div class="search-item">
            <div class="search-label">SKU ID列表</div>
            <el-input
              v-model="skuIdInput"
              placeholder="请输入SKU ID，多个用逗号分隔"
              clearable
              size="small"
              class="search-input"
            />
          </div>
        </div>

        <!-- 折叠状态下的操作按钮区域 -->
        <div v-if="!showAdvanced" class="buttons-container-bottom">
          <div class="buttons-right">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery" size="small" class="action-button">查询</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery" size="small" class="action-button">重置</el-button>
            <el-button type="warning" icon="el-icon-download" @click="handleExport" size="small" class="action-button">导出</el-button>
            <el-button type="text" size="small" @click="showAdvanced = !showAdvanced" class="expand-button-text">
              展开
              <i class="el-icon-arrow-down"></i>
            </el-button>
          </div>
        </div>

        <!-- 以下行在展开状态时显示 -->
        <template v-if="showAdvanced">
          <!-- 第三行 -->
          <div class="search-row">
            <!-- 商品状态 -->
            <div class="search-item">
              <div class="search-label">商品状态</div>
              <el-select
                v-model="advancedQuery.selectStatusList"
                multiple
                collapse-tags
                collapse-tags-tooltip
                placeholder="请选择商品状态"
                clearable
                size="small"
                class="search-input"
              >
                <el-option :key="10" label="仓库中" :value="10" />
                <el-option :key="20" label="出售中" :value="20" />
                <el-option :key="30" label="在售编辑中/品控中/等待上架" :value="30" />
                <el-option :key="40" label="下架" :value="40" />
                <el-option :key="50" label="删除" :value="50" />
              </el-select>
            </div>
            
            <!-- SKC货号 -->
            <div class="search-item">
              <div class="search-label">SKC货号</div>
              <el-input 
                v-model="advancedQuery.skcExtCodeList" 
                placeholder="多个用逗号分隔" 
                clearable 
                size="small"
                class="search-input" 
              />
            </div>
            
            <!-- SKU货号 -->
            <div class="search-item">
              <div class="search-label">SKU货号</div>
              <el-input 
                v-model="advancedQuery.skuExtCodeList" 
                placeholder="多个用逗号分隔" 
                clearable 
                size="small"
                class="search-input" 
              />
            </div>
            
            <!-- 结算类型 -->
            <div class="search-item">
              <div class="search-label">结算类型</div>
              <el-select 
                v-model="advancedQuery.settlementType" 
                placeholder="请选择" 
                clearable 
                size="small"
                class="search-input"
              >
                <el-option label="非VMI" :value="0" />
                <el-option label="VMI" :value="1" />
              </el-select>
            </div>
          </div>

          <!-- 第四行 -->
          <div class="search-row">
            <!-- 备货区域 -->
            <div class="search-item">
              <div class="search-label">备货区域</div>
              <el-select
                v-model="advancedQuery.inventoryRegionList"
                multiple
                collapse-tags
                collapse-tags-tooltip
                placeholder="请选择备货区域"
                clearable
                size="small"
                class="search-input"
              >
                <el-option label="国内备货" :value="1" />
                <el-option label="海外备货" :value="2" />
                <el-option label="保税仓备货" :value="3" />
              </el-select>
            </div>
            
            <!-- 备货类型 -->
            <div class="search-item">
              <div class="search-label">备货类型</div>
              <el-select 
                v-model="advancedQuery.purchaseStockType" 
                placeholder="请选择" 
                clearable 
                size="small"
                class="search-input"
              >
                <el-option label="普通" value="0" />
                <el-option label="JIT备货" value="1" />
              </el-select>
            </div>
            
            <!-- 加入站点时长(天) -->
            <div class="search-item">
              <div class="search-label">加入站点时长</div>
              <div class="range-container">
                <el-input-number 
                  v-model="advancedQuery.onSalesDurationOfflineGte" 
                  controls-position="right" 
                  :min="0" 
                  placeholder="最小天数" 
                  size="small"
                  class="range-input"
                />
                <span class="range-separator">-</span>
                <el-input-number 
                  v-model="advancedQuery.onSalesDurationOfflineLte" 
                  controls-position="right" 
                  :min="0" 
                  placeholder="最大天数" 
                  size="small"
                  class="range-input"
                />
              </div>
            </div>
            
            <!-- SKU剩余库存 -->
            <div class="search-item">
              <div class="search-label">SKU剩余库存</div>
              <div class="range-container">
                <el-input-number 
                  v-model="advancedQuery.minRemanentInventoryNum" 
                  controls-position="right" 
                  :min="0" 
                  placeholder="最小值" 
                  size="small"
                  class="range-input"
                />
                <span class="range-separator">-</span>
                <el-input-number 
                  v-model="advancedQuery.maxRemanentInventoryNum" 
                  controls-position="right" 
                  :min="0" 
                  placeholder="最大值" 
                  size="small"
                  class="range-input"
                />
              </div>
            </div>
          </div>

          <!-- 第五行 -->
          <div class="search-row">
            <!-- SKU可售天数 -->
            <div class="search-item">
              <div class="search-label">SKU可售天数</div>
              <div class="range-container">
                <el-input-number 
                  v-model="advancedQuery.minAvailableSaleDays" 
                  controls-position="right" 
                  :min="0" 
                  placeholder="最小天数" 
                  size="small"
                  class="range-input"
                />
                <span class="range-separator">-</span>
                <el-input-number 
                  v-model="advancedQuery.maxAvailableSaleDays" 
                  controls-position="right" 
                  :min="0" 
                  placeholder="最大天数" 
                  size="small"
                  class="range-input"
                />
              </div>
            </div>
            
            <!-- 今日销量 -->
            <div class="search-item">
              <div class="search-label">今日销量</div>
              <div class="range-container">
                <el-input-number 
                  v-model="advancedQuery.todaySaleVolumMin" 
                  controls-position="right" 
                  :min="0" 
                  placeholder="最小值" 
                  size="small"
                  class="range-input"
                />
                <span class="range-separator">-</span>
                <el-input-number 
                  v-model="advancedQuery.todaySaleVolumMax" 
                  controls-position="right" 
                  :min="0" 
                  placeholder="最大值" 
                  size="small"
                  class="range-input"
                />
              </div>
            </div>
            
            <!-- 图片审核状态 -->
            <div class="search-item">
              <div class="search-label">图片审核状态</div>
              <el-select
                v-model="advancedQuery.pictureAuditStatusList"
                multiple
                collapse-tags
                collapse-tags-tooltip
                placeholder="请选择状态"
                clearable
                size="small"
                class="search-input"
              >
                <el-option label="未完成" :value="1" />
                <el-option label="已完成" :value="2" />
              </el-select>
            </div>
            
            <!-- 选品状态 -->
            <div class="search-item">
              <div class="search-label">选品状态</div>
              <el-select
                v-model="advancedQuery.supplyStatusList"
                multiple
                collapse-tags
                collapse-tags-tooltip
                placeholder="请选择状态"
                clearable
                size="small"
                class="search-input"
              >
                <el-option label="正常供货" :value="0" />
                <el-option label="暂时无货" :value="1" />
                <el-option label="停产" :value="2" />
              </el-select>
            </div>
          </div>
          
          <!-- 第六行 -->
          <div class="search-row">
            <!-- 备货状态 -->
            <div class="search-item">
              <div class="search-label">备货状态</div>
              <el-select
                v-model="advancedQuery.stockStatusList"
                multiple
                collapse-tags
                collapse-tags-tooltip
                placeholder="请选择状态"
                clearable
                size="small"
                class="search-input"
              >
                <el-option label="库存充足" :value="0" />
                <el-option label="即将售罄" :value="1" />
                <el-option label="已断码" :value="2" />
                <el-option label="全部售罄(已断货)" :value="3" />
              </el-select>
            </div>
            
            <!-- 关停JIT状态 -->
            <div class="search-item">
              <div class="search-label">关停JIT状态</div>
              <el-select
                v-model="advancedQuery.closeJitStatus"
                multiple
                collapse-tags
                collapse-tags-tooltip
                placeholder="请选择状态"
                clearable
                size="small"
                class="search-input"
              >
                <el-option label="未关闭" :value="1" />
                <el-option label="待关闭" :value="2" />
                <el-option label="特殊关闭" :value="3" />
                <el-option label="特殊关闭审核失败" :value="4" />
                <el-option label="关闭失败" :value="5" />
                <el-option label="关闭生效" :value="6" />
              </el-select>
            </div>
            
            <!-- 是否缺货 -->
            <div class="search-item">
              <div class="search-label">是否缺货</div>
              <el-select 
                v-model="advancedQuery.isLack" 
                placeholder="请选择" 
                clearable 
                size="small"
                class="search-input"
              >
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </div>
            
            <!-- 是否定制商品 -->
            <div class="search-item">
              <div class="search-label">是否定制商品</div>
              <el-select 
                v-model="advancedQuery.isCustomGoods" 
                placeholder="请选择" 
                clearable 
                size="small"
                class="search-input"
              >
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </div>
          </div>
          
          <!-- 第七行 -->
          <div class="search-row">
            <!-- 是否热销款 -->
            <div class="search-item">
              <div class="search-label">是否热销款</div>
              <el-select 
                v-model="advancedQuery.hotTag" 
                placeholder="请选择" 
                clearable 
                size="small"
                class="search-input"
              >
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </div>
            
            <!-- 剩余生产数>0 -->
            <div class="search-item">
              <div class="search-label">剩余生产数>0</div>
              <el-select 
                v-model="advancedQuery.availableProduceNumGreaterThanzero" 
                placeholder="请选择" 
                clearable 
                size="small"
                class="search-input"
              >
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </div>
            
            <!-- 建议自动关JIT -->
            <div class="search-item">
              <div class="search-label">建议自动关JIT</div>
              <el-select 
                v-model="advancedQuery.suggestCloseJit" 
                placeholder="请选择" 
                clearable 
                size="small"
                class="search-input"
              >
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </div>
            
            <!-- 其他剩余空白项(占位) -->
            <div class="search-item"></div>
          </div>
          
          <!-- 展开状态下的操作按钮区域 -->
          <div class="buttons-container-bottom">
            <div class="buttons-right">
              <el-button type="primary" icon="el-icon-search" @click="handleQuery" size="small" class="action-button">查询</el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery" size="small" class="action-button">重置</el-button>
              <el-button type="warning" icon="el-icon-download" @click="handleExport" size="small" class="action-button">导出</el-button>
              <el-button type="text" size="small" @click="showAdvanced = !showAdvanced" class="expand-button-text">
                收起
                <i class="el-icon-arrow-up"></i>
              </el-button>
            </div>
          </div>
        </template>
      </div>

      <!-- Wrapper for table to handle overflow -->
      <div class="table-wrapper">
        <el-table
          v-loading="loading"
          :data="salesList"
          border
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          row-key="skuId"
          :span-method="objectSpanMethod"
          max-height="calc(100vh - 350px)"
          @sort-change="handleSortChange"
        >
          <el-table-column type="selection" width="55" align="center" />

          <!-- 新增：店铺信息列 -->
          <el-table-column label="店铺信息" width="150">
            <template #default="scope">
              <div>{{ scope.row.shopName }}</div>
              <div v-if="scope.row.shopRemark" style="font-size: 12px; color: #909399;">{{ scope.row.shopRemark }}</div>
            </template>
          </el-table-column>

          <el-table-column label="商品信息" width="300">
            <template #default="scope">
              <div class="product-info">
                <el-image
                  v-if="scope.row.firstPictureUrl"
                  :src="scope.row.firstPictureUrl"
                  style="width: 70px; height: 70px; margin-right: 10px; flex-shrink: 0;"
                  fit="contain"
                ></el-image>
                <div class="info-content">
                  <div v-if="scope.row.productName" class="product-name" style="white-space: normal;">{{ scope.row.productName }}</div>
                  <div v-if="scope.row.productSkcId">SKC ID: {{ scope.row.productSkcId }}</div>
                  <div v-if="scope.row.spu">SPU: {{ scope.row.spu }}</div>
                  <div v-if="scope.row.skcCode">SKC货号: {{ scope.row.skcCode }}</div>
                  <div v-if="scope.row.onSaleDurationDesc">加入站点时长: {{ scope.row.onSaleDurationDesc }}</div>
                  <div class="tags" style="margin-top: 5px;">
                    <el-tag v-if="scope.row.inventoryRegion === 1" size="mini" type="info" style="margin-right: 5px;">国内备货</el-tag>
                    <el-tag v-if="scope.row.inventoryRegion === 2" size="mini" type="warning" style="margin-right: 5px;">海外备货</el-tag>
                    <el-tag v-if="scope.row.inventoryRegion === 3" size="mini" type="success" style="margin-right: 5px;">保税仓备货</el-tag>
                    <el-tag v-if="scope.row.hotTag" size="mini" type="danger" style="margin-right: 5px;">热销款</el-tag>
                    <el-tag v-if="scope.row.closeJitStatus === 0" size="mini" style="margin-right: 5px;">未申请</el-tag>
                    <el-tag v-if="scope.row.closeJitStatus === 1" size="mini" type="warning" style="margin-right: 5px;">待调价</el-tag>
                    <el-tag v-if="scope.row.closeJitStatus === 2" size="mini" type="primary" style="margin-right: 5px;">待备货</el-tag>
                    <el-tag v-if="scope.row.closeJitStatus === 3" size="mini" type="info" style="margin-right: 5px;">待关JIT</el-tag>
                    <el-tag v-if="scope.row.closeJitStatus === 4" size="mini" type="success" style="margin-right: 5px;">JIT已关</el-tag>
                    <el-tag v-if="scope.row.closeJitStatus === 5" size="mini" type="danger" style="margin-right: 5px;">调价失败</el-tag>
                    <el-tag v-if="scope.row.closeJitStatus === 6" size="mini" type="danger" style="margin-right: 5px;">备货失败</el-tag>
                    <el-tag v-if="scope.row.closeJitStatus === 7" size="mini" type="danger" style="margin-right: 5px;">涨价结束</el-tag>
                    <el-tag v-if="scope.row.supplyStatus === 0" size="mini" type="success" style="margin-right: 5px;">正常供货</el-tag>
                    <el-tag v-if="scope.row.supplyStatus === 1" size="mini" type="warning" style="margin-right: 5px;">暂时无货</el-tag>
                    <el-tag v-if="scope.row.supplyStatus === 2" size="mini" type="danger">停产</el-tag>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="SKU信息" width="220">
            <template #default="scope">
              <div v-if="scope.row.skuPropertyValueDesc">{{ scope.row.skuPropertyValueDesc }}</div>
              <div v-if="scope.row.skuId">SKU ID: {{ scope.row.skuId }}</div>
              <div v-if="scope.row.skuCode !== undefined">SKU货号: {{ scope.row.skuCode }}</div>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="warehouseGroupDesc" label="备货仓组" width="120" /> -->
          <el-table-column label="申报价格(CNY)" width="120" align="right">
            <template #default="scope">
              <span v-if="typeof scope.row.applyPrice === 'number'">¥{{ scope.row.applyPrice.toFixed(2) }}</span>
              <span v-else>{{ scope.row.applyPrice }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="priceStatus" label="开款价格状态" width="120" />
          <el-table-column prop="adjust" label="调价状态" width="100" />
          <el-table-column prop="lackNum" label="缺货数量" width="100" align="center"/>
          <!-- <el-table-column prop="sevenDaysAddCartNum" label="近7日用户加购数量" width="150" align="center"/> -->
          <!-- <el-table-column prop="wishAddCartNum" label="用户累计加购数量" width="150" align="center"/> -->
          <!-- <el-table-column prop="subscribedRestock" label="已订阅待提醒到货" width="150" align="center"/> -->
          <el-table-column label="销售数据" align="center">
            <el-table-column 
              prop="salesToday" 
              label="今日" 
              width="80" 
              align="center"
              sortable="custom"
              :sort-orders="['ascending', 'descending', null]"
              :sort-by="'todaySaleVolume'"
            />
            <el-table-column 
              prop="sales7Days" 
              label="近7天" 
              width="80" 
              align="center"
              sortable="custom"
              :sort-orders="['ascending', 'descending', null]"
              :sort-by="'lastSevenDaysSaleVolume'"
            />
            <el-table-column 
              prop="sales30Days" 
              label="近30天" 
              width="80" 
              align="center"
              sortable="custom"
              :sort-orders="['ascending', 'descending', null]"
              :sort-by="'lastThirtyDaysSaleVolume'"
            />
          </el-table-column>
          <!-- <el-table-column label="生产建议信息" align="center"> -->
            <!-- <el-table-column label="生产信息" width="100" align="center"><template #default>-</template></el-table-column> -->
            <!-- <el-table-column label="剩余未生产数" width="120" align="center"><template #default>-</template></el-table-column> -->
            <!-- <el-table-column prop="warehouseAvailableInventory" label="仓内可用库存" width="120" align="center"/> -->
            <!-- <el-table-column prop="warehouseReservedInventory" label="仓内预占库存" width="120" align="center"/> -->
          <!-- </el-table-column> -->
          <el-table-column label="库存数据" align="center">
            <el-table-column prop="warehouseAvailableInventory" label="仓内可用库存" width="120" align="center"/>
            <el-table-column prop="warehouseUnavailableInventory" label="仓内暂不可用库存" width="150" align="center"/>
            <el-table-column prop="shippedInventory" label="已发货库存" width="120" align="center"/>
            <el-table-column prop="createdOrderInventory" label="已创建备货单待发货库存" width="200" align="center"/>
            <el-table-column prop="pendingApprovalInventory" label="待审核备货单库存" width="150" align="center"/>
          </el-table-column>
          <el-table-column label="备货计划" align="center">
            <el-table-column prop="purchaseConfig" label="备货逻辑" width="100" align="center"/>
            <el-table-column prop="adviceQuantity" label="建议备货量" width="120" align="center"/>
            <el-table-column prop="availableSaleDaysInventory" label="库存可售天数" width="120" align="center"/>
            <el-table-column prop="warehouseAvailableSaleDays" label="仓内库存可售天数" width="150" align="center"/>
            <el-table-column prop="availableSaleDays" label="可售天数" width="100" align="center"/>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- Pagination fixed at the bottom -->
    <div class="bottom-pagination-bar">
      <el-pagination
        v-show="total > 0"
        :total="total"
        :current-page.sync="queryParams.pageNo"
        :page-size.sync="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        background
      />
    </div>
  </div>
</template>

<script>
import { getSalesData, getCombinedSalesData, exportSalesData } from "@/api/temu/sales";
import { fetchUserAccessibleShops } from "@/utils/shop-helper";

export default {
  name: "TemuSalesData",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 销售数据列表
      salesList: [],
      // 店铺选项
      shopOptions: [],
      // 是否全选店铺
      isAllSelected: false,
      // 日期范围
      dateRange: [],
      // SKU ID输入
      skuIdInput: "",
      // 是否显示高级查询
      showAdvanced: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        shopIds: [],
        productCodeList: undefined,
        skcCodeList: undefined,
        isVMI: undefined,
        isSuggestJit: undefined,
        skuIdList: [],
        // 新增排序参数
        orderByParam: undefined,
        orderByDesc: undefined
      },
      // 高级查询参数
      advancedQuery: {
        startTime: undefined,
        endTime: undefined,
        selectStatusList: [],
        productName: undefined,
        skcExtCodeList: undefined,
        skuExtCodeList: undefined,
        onSalesDurationOfflineGte: undefined,
        onSalesDurationOfflineLte: undefined,
        inventoryRegionList: [],
        minRemanentInventoryNum: undefined,
        maxRemanentInventoryNum: undefined,
        minAvailableSaleDays: undefined,
        maxAvailableSaleDays: undefined,
        pictureAuditStatusList: [],
        supplyStatusList: [],
        closeJitStatus: [],
        isLack: undefined,
        isCustomGoods: undefined,
        todaySaleVolumMin: undefined,
        todaySaleVolumMax: undefined,
        hotTag: undefined,
        purchaseStockType: undefined,
        suggestCloseJit: undefined,
        stockStatusList: [],
        availableProduceNumGreaterThanzero: undefined,
        warehouseGroupIdList: undefined,
        settlementType: undefined
      },
      // 店铺数据映射（用于显示店铺名称）
      shopMap: {}
    };
  },
  created() {
    this.getShopOptions();
  },
  methods: {
    // 获取店铺选项
    async getShopOptions() {
      try {
        const shops = await fetchUserAccessibleShops();
        this.shopOptions = shops;
        // 构建店铺映射
        this.shopOptions.forEach(shop => {
          this.shopMap[shop.shopId] = shop.shopName;
        });
      } catch (error) {
        console.error('获取店铺列表失败:', error);
        this.$message.error('获取店铺列表失败');
      }
    },
    // 查询数据列表
    getList() {
      // 检查是否选择了店铺
      if (!this.queryParams.shopIds || this.queryParams.shopIds.length === 0) {
        this.$message.warning('请至少选择一个店铺');
        return;
      }
      
      this.loading = true;
      
      // 处理SKU ID列表
      if (this.skuIdInput) {
        this.queryParams.skuIdList = this.skuIdInput
          .split(",")
          .map(id => id.trim())
          .filter(id => id)
          .map(id => Number(id));
      } else {
        this.queryParams.skuIdList = [];
      }
      
      // 合并基础查询参数和高级查询参数
      const finalQueryParams = {
        // 基础分页和店铺
        pageNo: this.queryParams.pageNo,
        pageSize: this.queryParams.pageSize,
        shopIds: this.queryParams.shopIds,
        skuIdList: this.queryParams.skuIdList, // 已处理
        // 排序参数
        orderByParam: this.queryParams.orderByParam,
        orderByDesc: this.queryParams.orderByDesc,

        // 基础查询条件 (来自 queryParams)
        productIdList: this.queryParams.productCodeList ? this.queryParams.productCodeList.split(',').map(s => parseInt(s.trim())).filter(n => !isNaN(n)) : [], // 修正：转为数字列表
        productSkcIdList: this.queryParams.skcCodeList ? this.queryParams.skcCodeList.split(',').map(s => parseInt(s.trim())).filter(n => !isNaN(n)) : [], // 修正：转为数字列表
        isVMI: this.queryParams.isVMI,
        isSuggestJit: this.queryParams.isSuggestJit,

        // 高级查询条件 (来自 advancedQuery)
        startTime: this.advancedQuery.startTime,
        endTime: this.advancedQuery.endTime,
        selectStatusList: this.advancedQuery.selectStatusList,
        productName: this.advancedQuery.productName,
        skcExtCodeList: this.advancedQuery.skcExtCodeList ? this.advancedQuery.skcExtCodeList.split(',').map(s => s.trim()).filter(Boolean) : [],
        skuExtCodeList: this.advancedQuery.skuExtCodeList ? this.advancedQuery.skuExtCodeList.split(',').map(s => s.trim()).filter(Boolean) : [],
        onSalesDurationOfflineGte: this.advancedQuery.onSalesDurationOfflineGte,
        onSalesDurationOfflineLte: this.advancedQuery.onSalesDurationOfflineLte,
        inventoryRegionList: this.advancedQuery.inventoryRegionList,
        minRemanentInventoryNum: this.advancedQuery.minRemanentInventoryNum,
        maxRemanentInventoryNum: this.advancedQuery.maxRemanentInventoryNum,
        minAvailableSaleDays: this.advancedQuery.minAvailableSaleDays,
        maxAvailableSaleDays: this.advancedQuery.maxAvailableSaleDays,
        pictureAuditStatusList: this.advancedQuery.pictureAuditStatusList,
        supplyStatusList: this.advancedQuery.supplyStatusList,
        closeJitStatus: this.advancedQuery.closeJitStatus,
        isLack: this.advancedQuery.isLack,
        isCustomGoods: this.advancedQuery.isCustomGoods,
        todaySaleVolumMin: this.advancedQuery.todaySaleVolumMin,
        todaySaleVolumMax: this.advancedQuery.todaySaleVolumMax,
        hotTag: this.advancedQuery.hotTag,
        purchaseStockType: this.advancedQuery.purchaseStockType,
        suggestCloseJit: this.advancedQuery.suggestCloseJit,
        stockStatusList: this.advancedQuery.stockStatusList,
        availableProduceNumGreaterThanZero: this.advancedQuery.availableProduceNumGreaterThanzero,
        settlementType: this.advancedQuery.settlementType
      };

      // 使用综合数据接口
      getCombinedSalesData(finalQueryParams)
        .then(response => {
          console.log('获取到的原始销售数据:', response);
          const { data } = response;
          if (data) {
            const { list, totalCount } = this.processApiResponse(data);
            this.salesList = list;
            this.total = totalCount;
            
            // 如果有错误信息，显示通知
            if (data.errors && data.errors.length > 0) {
              this.$notify.warning({
                title: '部分店铺数据获取失败',
                message: data.errors.join('\n'),
                duration: 5000
              });
            }
          } else {
            this.salesList = [];
            this.total = 0;
          }
          this.loading = false;
        })
        .catch(error => {
          console.error("获取销售数据失败", error);
          this.salesList = [];
          this.total = 0;
          this.loading = false;
          this.$message.error('获取销售数据失败: ' + (error.message || '未知错误'));
        });
    },
    // 查询按钮
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    // 重置按钮
    resetQuery() {
      // 重置基础查询表单的字段
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        shopIds: [],
        productCodeList: undefined,
        skcCodeList: undefined,
        isVMI: undefined,
        isSuggestJit: undefined,
        skuIdList: [],
        // 重置排序参数
        orderByParam: undefined,
        orderByDesc: undefined
      };
      // 重置全选状态
      this.isAllSelected = false;
      // 重置高级查询表单的字段
      this.advancedQuery = {
        startTime: undefined,
        endTime: undefined,
        selectStatusList: [],
        productName: undefined,
        skcExtCodeList: undefined,
        skuExtCodeList: undefined,
        onSalesDurationOfflineGte: undefined,
        onSalesDurationOfflineLte: undefined,
        inventoryRegionList: [],
        minRemanentInventoryNum: undefined,
        maxRemanentInventoryNum: undefined,
        minAvailableSaleDays: undefined,
        maxAvailableSaleDays: undefined,
        pictureAuditStatusList: [],
        supplyStatusList: [],
        closeJitStatus: [],
        isLack: undefined,
        isCustomGoods: undefined,
        todaySaleVolumMin: undefined,
        todaySaleVolumMax: undefined,
        hotTag: undefined,
        purchaseStockType: undefined,
        suggestCloseJit: undefined,
        stockStatusList: [],
        availableProduceNumGreaterThanZero: undefined,
        warehouseGroupIdList: undefined,
        settlementType: undefined
      };
      // 重置日期范围选择器
      this.dateRange = [];
      // 重置 SKU ID 输入框
      this.skuIdInput = "";
      // 清空表格数据和总数
      this.salesList = [];
      this.total = 0;
      // 重置 Element UI 表单校验状态（如果需要）
      // 注意：如果高级查询表单有 ref，也需要调用 resetFields
      this.$refs.queryForm.resetFields();
      // 如果高级查询表单也有ref="advancedQueryForm"，则调用:
      // if (this.$refs.advancedQueryForm) {
      //   this.$refs.advancedQueryForm.resetFields();
      // }
      // 不需要重新调用 getList
      // this.getList();
    },
    // 日期范围改变
    handleDateRangeChange(val) {
      if (val) {
        this.advancedQuery.startTime = val[0];
        this.advancedQuery.endTime = val[1];
      } else {
        this.advancedQuery.startTime = undefined;
        this.advancedQuery.endTime = undefined;
      }
    },
    // 查看详情
    handleDetail(row) {
      this.$router.push({
        path: "/temu/sales-data/detail",
        query: { id: row.skuId, shopId: row.shopId }
      });
    },
    // 导出数据
    handleExport() {
      // 检查是否选择了店铺
      if (!this.queryParams.shopIds || this.queryParams.shopIds.length === 0) {
        this.$message.warning('请至少选择一个店铺');
        return;
      }
      
      // 处理SKU ID列表
      if (this.skuIdInput) {
        this.queryParams.skuIdList = this.skuIdInput
          .split(",")
          .map(id => id.trim())
          .filter(id => id)
          .map(id => Number(id));
      } else {
        this.queryParams.skuIdList = [];
      }
      
      // Use finalQueryParams for export as well
      const finalQueryParams = {
        ...this.queryParams,
        // 处理逗号分隔的字符串为数组
        productCodeList: this.advancedQuery.productCodeList ? this.advancedQuery.productCodeList.split(',').map(s => s.trim()).filter(Boolean) : [],
        skcCodeList: this.advancedQuery.skcCodeList ? this.advancedQuery.skcCodeList.split(',').map(s => s.trim()).filter(Boolean) : [],
        skcExtCodeList: this.advancedQuery.skcExtCodeList ? this.advancedQuery.skcExtCodeList.split(',').map(s => s.trim()).filter(Boolean) : [],
        skuExtCodeList: this.advancedQuery.skuExtCodeList ? this.advancedQuery.skuExtCodeList.split(',').map(s => s.trim()).filter(Boolean) : [],
        warehouseGroupIdList: this.advancedQuery.warehouseGroupIdList ? this.advancedQuery.warehouseGroupIdList.split(',').map(s => parseInt(s.trim())).filter(n => !isNaN(n)) : [],
        // 包含所有其他高级查询参数
        productName: this.advancedQuery.productName,
        isVMI: this.advancedQuery.isVMI,
        isSuggestJit: this.advancedQuery.isSuggestJit,
        onSalesDurationOfflineGte: this.advancedQuery.onSalesDurationOfflineGte,
        onSalesDurationOfflineLte: this.advancedQuery.onSalesDurationOfflineLte,
        inventoryRegionList: this.advancedQuery.inventoryRegionList,
        minRemanentInventoryNum: this.advancedQuery.minRemanentInventoryNum,
        maxRemanentInventoryNum: this.advancedQuery.maxRemanentInventoryNum,
        minAvailableSaleDays: this.advancedQuery.minAvailableSaleDays,
        maxAvailableSaleDays: this.advancedQuery.maxAvailableSaleDays,
        pictureAuditStatusList: this.advancedQuery.pictureAuditStatusList,
        supplyStatusList: this.advancedQuery.supplyStatusList,
        closeJitStatus: this.advancedQuery.closeJitStatus,
        isLack: this.advancedQuery.isLack,
        isCustomGoods: this.advancedQuery.isCustomGoods,
        todaySaleVolumMin: this.advancedQuery.todaySaleVolumMin,
        todaySaleVolumMax: this.advancedQuery.todaySaleVolumMax,
        hotTag: this.advancedQuery.hotTag,
        purchaseStockType: this.advancedQuery.purchaseStockType,
        suggestCloseJit: this.advancedQuery.suggestCloseJit,
        stockStatusList: this.advancedQuery.stockStatusList,
        availableProduceNumGreaterThanZero: this.advancedQuery.availableProduceNumGreaterThanzero,
        settlementType: this.advancedQuery.settlementType
      };

      this.$confirm('是否确认导出当前查询结果的销售数据?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$modal.loading("正在导出数据，请稍候...");
        exportSalesData(finalQueryParams).then(response => {
          this.$modal.closeLoading();
          const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          const timestamp = new Date().toISOString().slice(0, 10);
          link.download = `销售数据_${timestamp}.xlsx`;
          link.click();
          URL.revokeObjectURL(link.href);
        }).catch(error => {
          this.$modal.closeLoading();
          console.error("导出失败", error);
          let errorMsg = "导出失败";
          if (error.response && error.response.data && typeof error.response.data === 'string') {
            errorMsg = error.response.data;
          } else if (error.message) {
            errorMsg = error.message;
          }
          this.$modal.msgError(errorMsg);
        });
      });
    },
    // 分页相关方法
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.queryParams.pageNo = val;
      this.getList();
    },
    processApiResponse(responseData) {
      if (!responseData || !responseData.items) {
        return { list: [], totalCount: 0 };
      }
      let flatList = [];

      responseData.items.forEach(product => {
        const productInfo = {
          productName: product.productName,
          spu: product.productId,
          skcCode: product.skcExtCode || '-',
          firstPictureUrl: product.productSkcPicture,
          onSaleDurationDesc: product.onSalesDurationOffline !== undefined ? `${product.onSalesDurationOffline} 天` : '-',
          shopId: product.shopId,
          shopName: product.shopName,
          shopRemark: product.shopRemark,
          productSkcId: product.productSkcId,
          inventoryRegion: product.inventoryRegion,
          hotTag: product.hotTag,
          closeJitStatus: product.closeJitStatus,
          supplyStatus: product.supplyStatus
        };

        const skus = product.skuQuantityDetailList || [];
        const numSkus = skus.length;

        if (numSkus > 0) {
          skus.forEach((sku, index) => {
            const warehouseInfo = sku.warehouseInfoList && sku.warehouseInfoList.length > 0 ? sku.warehouseInfoList[0] : {};
            const inventoryInfo = warehouseInfo.inventoryNumInfo || sku.inventoryNumInfo || {};
            const purchaseConfig = warehouseInfo.purchaseConfig || sku.purchaseConfig || '-';

            flatList.push({
              ...productInfo,
              skuPropertyValueDesc: sku.className || '-',
              skuId: sku.productSkuId,
              skuCode: sku.skuExtCode || '-',
              warehouseGroupDesc: sku.warehouseGroupName || '-',
              applyPrice: sku.supplierPrice !== null && !isNaN(sku.supplierPrice) ? parseFloat(sku.supplierPrice) : '-',
              priceStatus: sku.priceReviewStatus === 2 ? '已生效' : (sku.priceReviewStatus !== undefined ? `状态 ${sku.priceReviewStatus}` : '-'),
              adjust: '-',
              lackNum: sku.lackQuantity !== undefined ? sku.lackQuantity : 0,
              sevenDaysAddCartNum: sku.inCartNumber7d !== undefined ? sku.inCartNumber7d : 0,
              wishAddCartNum: sku.inCardNumber !== undefined ? sku.inCardNumber : 0,
              subscribedRestock: 0,
              salesToday: sku.todaySaleVolume !== undefined ? sku.todaySaleVolume : 0,
              sales7Days: sku.lastSevenDaysSaleVolume !== undefined ? sku.lastSevenDaysSaleVolume : 0,
              sales30Days: sku.lastThirtyDaysSaleVolume !== undefined ? sku.lastThirtyDaysSaleVolume : 0,
              warehouseReservedInventory: inventoryInfo.expectedOccupiedInventoryNum !== undefined ? inventoryInfo.expectedOccupiedInventoryNum : 0,
              warehouseAvailableInventory: inventoryInfo.warehouseInventoryNum !== undefined ? inventoryInfo.warehouseInventoryNum : 0,
              warehouseUnavailableInventory: inventoryInfo.unavailableWarehouseInventoryNum !== undefined ? inventoryInfo.unavailableWarehouseInventoryNum : 0,
              shippedInventory: inventoryInfo.waitReceiveNum !== undefined ? inventoryInfo.waitReceiveNum : 0,
              createdOrderInventory: inventoryInfo.waitDeliveryInventoryNum !== undefined ? inventoryInfo.waitDeliveryInventoryNum : 0,
              pendingApprovalInventory: inventoryInfo.waitApproveInventoryNum !== undefined ? inventoryInfo.waitApproveInventoryNum : 0,
              purchaseConfig: purchaseConfig,
              adviceQuantity: sku.adviceQuantity !== null ? sku.adviceQuantity : '-',
              availableSaleDaysInventory: sku.availableSaleDaysFromInventory !== null ? sku.availableSaleDaysFromInventory : '-',
              warehouseAvailableSaleDays: warehouseInfo.warehouseAvailableSaleDays !== null ? warehouseInfo.warehouseAvailableSaleDays : '-',
              availableSaleDays: sku.availableSaleDays !== null ? sku.availableSaleDays : '-',
              _rowspan: numSkus,
              _isFirstRow: index === 0
            });
          });
        }
      });

      const finalTotal = responseData.total !== undefined ? responseData.total : 0;

      return { list: flatList, totalCount: finalTotal };
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1 || columnIndex === 2) {
        if (row._isFirstRow) {
          return {
            rowspan: row._rowspan,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
      // Added check for selection column (index 0)
      if (columnIndex === 0) {
         if (row._isFirstRow) {
          return {
            rowspan: row._rowspan,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
      return {
        rowspan: 1,
        colspan: 1
      };
    },
    handleShopSelectChange(value) {
      // 检查是否包含全选选项
      const allOptionIndex = value.indexOf('all');
      
      if (allOptionIndex > -1) {
        // 移除"all"选项，只保留真实的店铺ID
        value.splice(allOptionIndex, 1);
        
        // 判断当前是否处于全选状态
        if (this.isAllSelected) {
          // 如果已经是全选状态，此次操作视为取消全选
          this.queryParams.shopIds = [];
          this.isAllSelected = false;
        } else {
          // 否则执行全选
          this.queryParams.shopIds = this.shopOptions.map(item => item.shopId);
          this.isAllSelected = true;
        }
      } else {
        // 正常选择，直接使用传入的值
        this.queryParams.shopIds = value;
        
        // 检查是否等于全选状态
        this.isAllSelected = this.shopOptions.length > 0 && 
                            this.queryParams.shopIds.length === this.shopOptions.length;
      }
    },
    // 新增：处理表格排序变化
    handleSortChange({ column, prop, order }) {
      // 如果column存在排序字段(sort-by属性)，使用该字段作为排序参数
      const sortBy = column && column.sortBy ? column.sortBy : null;
      
      if (sortBy) {
        this.queryParams.orderByParam = sortBy;
        // 根据排序方向设置orderByDesc参数
        if (order === 'descending') {
          this.queryParams.orderByDesc = 1;
        } else if (order === 'ascending') {
          this.queryParams.orderByDesc = 0;
        } else {
          // 没有排序时清空排序参数
          this.queryParams.orderByParam = undefined;
          this.queryParams.orderByDesc = undefined;
        }
        
        // 重新加载数据
        this.queryParams.pageNo = 1; // 重置到第一页
        this.getList();
      }
    }
  }
};
</script>

<style scoped>
/* Make the main container fill the height and use flex column */
.sales-data-container {
  min-height: calc(100vh - 84px); /* Adjust 84px based on actual header/navbar height */
  display: flex;
  flex-direction: column;
  background-color: #f0f2f5; /* Add a background to the main container if needed */
  overflow: hidden; /* 防止整体页面滚动 */
}

/* 搜索区域卡片样式 */
.search-area-card {
  margin-bottom: 15px;
  flex-shrink: 0;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

/* 确保 el-card__body 可以正常伸展 */
.search-area-card >>> .el-card__body {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 15px 20px;
}

/* 搜索容器样式 */
.search-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
  flex-shrink: 0; /* 防止搜索区域被压缩 */
}

/* 搜索行样式 */
.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

/* 搜索项样式 */
.search-item {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 250px;
  max-width: calc(25% - 12px);
  margin-bottom: 5px;
}

/* 搜索标签样式 */
.search-label {
  width: 90px;
  text-align: right;
  margin-right: 10px;
  white-space: nowrap;
  color: #606266;
}

/* 搜索输入框样式 */
.search-input {
  flex: 1;
}

/* 展开按钮文本样式 */
.expand-button-text {
  display: flex;
  align-items: center;
}

/* 按钮样式 */
.action-button {
  display: flex;
  align-items: center;
}

/* 范围输入容器样式 */
.range-container {
  display: flex;
  align-items: center;
  flex: 1;
}

/* 范围输入框样式 */
.range-input {
  flex: 1;
  width: 0; /* 让flex控制宽度 */
}

/* 范围分隔符样式 */
.range-separator {
  margin: 0 5px;
  color: #909399;
}

/* Main card for form and table, allow it to grow and scroll internally */
.form-table-card {
  flex-grow: 1; /* Allow card to take up available space */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent card itself from scrolling */
  margin-bottom: 0; /* Remove margin to connect with pagination bar */
  border-bottom: none; /* Remove card bottom border if it exists */
  box-shadow: 0 1px 4px rgba(0,21,41,.08); /* Keep card shadow or remove if desired */
}

/* Target el-card's body for flex layout */
.form-table-card >>> .el-card__body {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Wrapper for the table to make it scrollable */
.table-wrapper {
  margin-top: 15px;
  width: 100%;
}

/* Bottom bar for pagination, minimal style with top border */
.bottom-pagination-bar {
  flex-shrink: 0; /* Prevents pagination bar from shrinking */
  padding: 5px 20px; /* Reduced vertical padding */
  background-color: #fff; /* Keep white background for pagination itself */
  border-top: 1px solid #ebeef5; /* Separator line */
  text-align: right; /* Align pagination controls to the right */
  position: sticky; /* 使分页栏固定在底部 */
  bottom: 0;
  z-index: 10;
}

/* Original styles */
.product-info {
  display: flex;
  align-items: flex-start;
}
.info-content {
  display: flex;
  flex-direction: column;
  font-size: 12px;
  color: #606266;
  overflow: hidden;
}
.product-name {
  font-weight: bold;
  margin-bottom: 4px;
  font-size: 13px;
  white-space: normal;
  word-break: break-all;
}
.el-table th > .cell {
  text-align: center;
}
.el-table .cell {
  padding-left: 8px;
  padding-right: 8px;
}

/* 标题行样式 */
.title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 底部按钮容器样式 */
.buttons-container-bottom {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  padding-top: 5px;
}

/* 右侧按钮区域 */
.buttons-right {
  display: flex;
  gap: 10px;
  align-items: center;
}
</style> 