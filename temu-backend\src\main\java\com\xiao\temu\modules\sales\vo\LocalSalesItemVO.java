package com.xiao.temu.modules.sales.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 本地商品销售信息查询结果项VO
 */
@Data
public class LocalSalesItemVO {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺名称
     */
    private String shopName;
    
    /**
     * 店铺备注
     */
    private String shopRemark;
    
    /**
     * 商品ID
     */
    private Long productId;
    
    /**
     * 商品SKC ID
     */
    private Long productSkcId;
    
    /**
     * 商品名称
     */
    private String productName;
    
    /**
     * SKC外部编码
     */
    private String skcExtCode;
    
    /**
     * 商品类目
     */
    private String category;
    
    /**
     * 商品SKC图片
     */
    private String productSkcPicture;
    
    /**
     * 是否定制商品
     */
    private Boolean isCustomGoods;
    
    /**
     * 库存区域
     */
    private Integer inventoryRegion;
    
    /**
     * 库存区域名称
     */
    private String inventoryRegionName;
    
    /**
     * 销售时长(线下)
     */
    private Integer onSalesDurationOffline;
    
    /**
     * 自动关闭JIT
     */
    private Boolean autoCloseJit;
    
    /**
     * JIT关闭状态
     */
    private Integer closeJitStatus;
    
    /**
     * JIT关闭状态名称
     */
    private String closeJitStatusName;
    
    /**
     * 是否有热销SKU
     */
    private Boolean hasHotSku;
    
    /**
     * 热销标签 0-普通 1-热销款
     */
    private Integer hotTag;
    
    /**
     * 是否是JIT备货， 0-普通，1-JIT备货
     */
    private Integer purchaseStockType;
    
    /**
     * 结算类型 0-非vmi 1-vmi
     */
    private Integer settlementType;
    
    /**
     * 供应状态 0-正常供货 1-暂时无货 2-停产
     */
    private Integer supplyStatus;
    
    /**
     * 库存是否充足
     */
    private Boolean isEnoughStock;
    
    /**
     * 图片审核状态 1-未完成；2-已完成
     */
    private Integer pictureAuditStatus;
    
    /**
     * 同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;
    
    /**
     * SKU列表
     */
    private List<SkuInfo> skuList;
    
    /**
     * 库存列表
     */
    private List<InventoryInfo> inventoryList;
    
    /**
     * 仓库列表
     */
    private List<WarehouseInfo> warehouseList;
} 