package com.xiao.temu.modules.operation.dto;

import lombok.Data;

/**
 * 运营组统计数据传输对象
 */
@Data
public class GroupStatisticsDTO {

    /**
     * 运营组ID
     */
    private Long groupId;

    /**
     * 运营组名称
     */
    private String groupName;

    /**
     * 成员总数
     */
    private Integer memberCount;

    /**
     * 关联店铺总数
     */
    private Integer shopCount;

    /**
     * 进行中任务数量
     */
    private Integer ongoingTaskCount;

    /**
     * 已完成任务数量
     */
    private Integer completedTaskCount;
} 