<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="角色名称" prop="roleName">
          <el-input
            v-model="queryParams.roleName"
            placeholder="请输入角色名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="权限标识" prop="roleKey">
          <el-input
            v-model="queryParams.roleKey"
            placeholder="请输入权限标识"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status" style="width: 150px">
          <el-select v-model="queryParams.status" placeholder="角色状态" clearable>
            <el-option label="正常" value="0" />
            <el-option label="停用" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>角色列表</span>
          <div class="right-buttons">
            <el-button type="primary" icon="Plus" @click="handleAdd">新增</el-button>
          </div>
        </div>
      </template>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="roleList"
        row-key="roleId"
        border
        class="menu-tree-table"
      >
        <el-table-column label="角色编号" prop="roleId" width="100" />
        <el-table-column label="角色名称" prop="roleName" :show-overflow-tooltip="true" />
        <el-table-column label="权限标识" prop="roleKey" :show-overflow-tooltip="true" />
        <el-table-column label="排序" prop="roleSort" width="100" />
        <el-table-column label="状态" align="center" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="'0'"
              :inactive-value="'1'"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ formatTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="380">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="Edit"
              @click="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
              type="primary"
              link
              icon="Setting"
              @click="handlePermission(scope.row)"
            >菜单权限</el-button>
            <el-button
              type="success"
              link
              icon="DataLine"
              @click="handleDataPermission(scope.row)"
            >数据权限</el-button>
            <el-button
              type="danger"
              link
              icon="Delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <pagination
        v-if="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改角色对话框 -->
    <RoleDialog
      ref="roleDialogRef"
      @refreshList="getList"
    />
    
    <!-- 分配权限对话框 -->
    <PermissionDialog
      ref="permissionDialogRef"
      @refreshList="getList"
    />

    <!-- 数据权限对话框 -->
    <DataPermissionDialog
      ref="dataPermissionDialogRef"
      @refreshList="getList"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { getRoleList, changeRoleStatus, deleteRole } from '@/api/role'
import { ElMessage, ElMessageBox } from 'element-plus'
import Pagination from '@/components/Pagination/index.vue'
import RoleDialog from './components/RoleDialog.vue'
import PermissionDialog from './components/PermissionDialog.vue'
import DataPermissionDialog from './components/DataPermissionDialog.vue'
import { formatTime } from '@/utils/format'

// 列表加载状态
const loading = ref(false)
// 角色列表数据
const roleList = ref<any[]>([])
// 总条数
const total = ref(0)
// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  roleName: '',
  roleKey: '',
  status: ''
})

// 角色表单对话框实例
const roleDialogRef = ref<InstanceType<typeof RoleDialog> | null>(null)
// 权限分配对话框实例
const permissionDialogRef = ref<InstanceType<typeof PermissionDialog> | null>(null)
// 数据权限对话框实例
const dataPermissionDialogRef = ref<InstanceType<typeof DataPermissionDialog> | null>(null)

// 获取角色列表
const getList = async () => {
  loading.value = true
  try {
    const res = await getRoleList(queryParams)
    roleList.value = res.data.records || []
    total.value = res.data.total || 0
  } catch (error) {
    console.error('获取角色列表失败', error)
  } finally {
    loading.value = false
  }
}

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.roleName = ''
  queryParams.roleKey = ''
  queryParams.status = ''
  handleQuery()
}

// 新增角色
const handleAdd = () => {
  roleDialogRef.value?.openDialog()
}

// 修改角色
const handleUpdate = (row: any) => {
  roleDialogRef.value?.openDialog(row.roleId)
}

// 分配权限
const handlePermission = (row: any) => {
  permissionDialogRef.value?.openDialog(row)
}

// 设置数据权限
const handleDataPermission = (row: any) => {
  dataPermissionDialogRef.value?.openDialog(row)
}

// 修改角色状态
const handleStatusChange = async (row: any) => {
  try {
    await changeRoleStatus(row.roleId, row.status)
    ElMessage.success('修改角色状态成功')
  } catch (error) {
    console.error('修改角色状态失败', error)
    row.status = row.status === '0' ? '1' : '0' // 如果失败，回滚状态
    ElMessage.error('修改角色状态失败')
  }
}

// 删除角色
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确认删除角色"${row.roleName}"吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteRole(row.roleId)
      ElMessage.success('删除成功')
      getList()
    } catch (error) {
      console.error('删除角色失败', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 页面初始化时获取数据
onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.right-buttons {
  display: flex;
  gap: 10px;
}

/* 表格样式 */
.menu-tree-table :deep(.el-table__row) {
  transition: background-color 0.2s;
}

/* 添加悬停效果 */
.menu-tree-table :deep(.el-table__row:hover) {
  background-color: #f0f9ff !important;
}

/* 为不同类型的项添加不同的左侧边框样式 */
.menu-tree-table :deep(.el-table__row) td:first-child {
  border-left: 4px solid #409eff;
}
</style> 