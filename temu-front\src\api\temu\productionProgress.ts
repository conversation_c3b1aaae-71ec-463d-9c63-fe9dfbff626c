import request from '@/utils/request'

/**
 * 获取备货单的生产进度
 * @param params 
 * @returns 
 */
export function getProductionProgress(params: {
  subPurchaseOrderSn: string;
  shopId?: number;
}) {
  return request({
    url: '/v1/production/progress/detail',
    method: 'get',
    params
  })
}

/**
 * 更新生产进度
 * @param data 
 * @returns 
 */
export function updateProductionProgress(data: {
  subPurchaseOrderSn: string;
  shopId?: number;
  progressType: string;
  operationType: string;
  remarks?: string;
}) {
  return request({
    url: '/v1/production/progress/update',
    method: 'post',
    data
  })
}

/**
 * 撤销生产进度
 * @param data 
 * @returns 
 */
export function cancelProductionProgress(data: {
  subPurchaseOrderSn: string;
  shopId?: number;
  progressType: string;
  remarks?: string;
}) {
  return request({
    url: '/v1/production/progress/cancel',
    method: 'post',
    data
  })
}

/**
 * 获取生产进度操作日志
 * @param params 
 * @returns 
 */
export function getProgressLogs(params: {
  subPurchaseOrderSn: string;
  shopId?: number;
}) {
  return request({
    url: '/v1/production/progress/logs',
    method: 'get',
    params
  })
}

/**
 * 获取备货单二维码
 * @param params 
 * @returns 
 */
export function getQrCode(params: {
  subPurchaseOrderSn: string;
  shopId?: number;
}) {
  return request({
    url: '/v1/qrcode/generate/image',
    method: 'get',
    params,
    responseType: 'blob' // 返回二进制数据
  })
}

/**
 * 批量获取二维码
 * @param data 
 * @returns 
 */
export function batchGetQrCodes(data: {
  subPurchaseOrderSnList: string[];
  shopId?: number;
}) {
  return request({
    url: '/v1/qrcode/generate/batch',
    method: 'post',
    data,
    responseType: 'blob' // 返回二进制压缩文件
  })
}

/**
 * 导出生产进度数据
 * @param params 
 * @returns 
 */
export function exportProductionProgress(params: {
  shopIds: number[];
  beginTime?: number;
  endTime?: number;
  statusList?: number[];
}) {
  return request({
    url: '/v1/production/progress/export',
    method: 'get',
    params,
    responseType: 'blob' // 返回Excel文件
  })
}

/**
 * 批量获取备货单的生产进度
 * @param data 包含shopId和subPurchaseOrderSnList的对象
 * @returns 返回API响应，data字段为对象，键是备货单号，值是进度数据
 * 
 * 注意：此接口的返回格式可能有两种：
 * 1. 标准格式：{code: 200, message: "操作成功", data: {WB2505064: {...}, ...}}
 * 2. 直接数据格式：{WB2505064: {...}, ...}
 */
export function batchGetProductionProgress(data: {
  shopId: number;
  subPurchaseOrderSnList: string[];
}) {
  // 确保shopId为数字类型 
  const shopId = typeof data.shopId === 'number' ? data.shopId : Number(data.shopId) || 0;
  
  // 确保订单列表是非空数组
  const orderList = Array.isArray(data.subPurchaseOrderSnList) ? 
    data.subPurchaseOrderSnList.filter(Boolean) : [];
  
  if (orderList.length === 0) {
    console.warn('批量获取生产进度时，订单列表为空');
  }
  
  // 注意：API预期接收备货单号列表作为POST请求体，shopId作为查询参数
  return request({
    url: '/v1/production/progress/batch-detail',
    method: 'post',
    data: orderList,          // 直接发送数组作为请求体
    params: {
      shopId: shopId          // shopId作为URL查询参数
    }
  })
} 