import Layout from '@/layout'

const productionRouter = {
  path: '/production',
  component: Layout,
  name: 'Production',
  meta: { title: '生产组管理', icon: 'Document' },
  children: [
    {
      path: 'group',
      component: () => import('@/views/production/group/index'),
      name: 'ProductionGroup',
      meta: { title: '生产组列表', icon: 'list', activeMenu: '/production/group' }
    },
    {
      path: 'member',
      component: () => import('@/views/production/member/index'),
      name: 'ProductionMember',
      meta: { title: '成员管理', icon: 'user', activeMenu: '/production/member' }
    }
  ]
}

export default productionRouter 