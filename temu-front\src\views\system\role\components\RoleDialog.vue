<template>
  <div>
    <el-dialog
      :title="title"
      v-model="visible"
      width="600px"
      append-to-body
      @close="cancel"
    >
      <el-form
        ref="roleFormRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="form.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="权限字符" prop="roleKey">
          <el-input v-model="form.roleKey" placeholder="请输入权限字符" />
        </el-form-item>
        <el-form-item label="显示顺序" prop="roleSort">
          <el-input-number v-model="form.roleSort" :min="0" controls-position="right" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { getRole, addRole, updateRole } from '@/api/role'

// 定义组件触发的事件
const emit = defineEmits(['refreshList'])

// 弹窗可见状态
const visible = ref(false)
// 表单引用
const roleFormRef = ref<FormInstance | null>(null)
// 是否为新增
const isAdd = ref(true)
// 当前编辑的角色ID
const roleId = ref<number | null>(null)

// 表单对象
const form = reactive({
  roleId: undefined,
  roleName: '',
  roleKey: '',
  roleSort: 0,
  status: '0',
  remark: ''
})

// 校验规则
const rules = reactive({
  roleName: [
    { required: true, message: '角色名称不能为空', trigger: 'blur' },
    { min: 2, max: 20, message: '角色名称长度必须在2到20个字符之间', trigger: 'blur' }
  ],
  roleKey: [
    { required: true, message: '权限字符不能为空', trigger: 'blur' },
    { min: 2, max: 100, message: '权限字符长度必须在2到100个字符之间', trigger: 'blur' }
  ],
  roleSort: [
    { required: true, message: '显示顺序不能为空', trigger: 'blur' }
  ]
})

// 计算弹窗标题
const title = computed(() => {
  return isAdd.value ? '添加角色' : '修改角色'
})

// 重置表单数据
const resetForm = () => {
  form.roleId = undefined
  form.roleName = ''
  form.roleKey = ''
  form.roleSort = 0
  form.status = '0'
  form.remark = ''
  
  if (roleFormRef.value) {
    roleFormRef.value.resetFields()
  }
}

// 取消按钮
const cancel = () => {
  visible.value = false
  resetForm()
}

// 打开对话框
const openDialog = async (id?: number) => {
  resetForm()
  
  if (id) {
    // 修改
    isAdd.value = false
    roleId.value = id
    try {
      const res = await getRole(id)
      Object.assign(form, res.data.role)
    } catch (error) {
      console.error('获取角色详情失败', error)
    }
  } else {
    // 新增
    isAdd.value = true
    roleId.value = null
  }
  
  visible.value = true
}

// 提交表单
const submitForm = async () => {
  if (!roleFormRef.value) return
  
  await roleFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    try {
      if (isAdd.value) {
        // 新增
        await addRole(form)
        ElMessage.success('新增成功')
      } else {
        // 修改
        await updateRole(form)
        ElMessage.success('修改成功')
      }
      
      visible.value = false
      emit('refreshList')
    } catch (error) {
      console.error('保存角色失败', error)
    }
  })
}

// 对外暴露方法
defineExpose({
  openDialog
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style> 