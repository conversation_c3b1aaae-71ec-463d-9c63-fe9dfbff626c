package com.xiao.temu.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.system.entity.SysDataPermission;
import com.xiao.temu.modules.system.dto.SysDataPermissionDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据权限Mapper接口
 */
@Mapper
public interface SysDataPermissionMapper extends BaseMapper<SysDataPermission> {
    
    /**
     * 查询角色的数据权限
     *
     * @param roleId 角色ID
     * @return 数据权限
     */
    SysDataPermissionDTO selectByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 查询用户的数据权限
     *
     * @param userId 用户ID
     * @return 数据权限列表
     */
    List<SysDataPermissionDTO> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 更新角色的数据权限
     *
     * @param roleId 角色ID
     * @param permissionType 权限类型
     * @return 影响行数
     */
    int updateByRoleId(@Param("roleId") Long roleId, @Param("permissionType") String permissionType);
} 