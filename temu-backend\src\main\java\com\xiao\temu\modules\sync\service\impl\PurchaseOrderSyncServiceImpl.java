package com.xiao.temu.modules.sync.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.infrastructure.api.ApiRateLimiter;
import com.xiao.temu.infrastructure.api.TemuApiClient;
import com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderV;
import com.xiao.temu.modules.purchaseorderv.mapper.PurchaseOrderVMapper;
import com.xiao.temu.modules.purchaseorderv.service.ProductionProgressService;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.sync.entity.PurchaseOrderSyncTask;
import com.xiao.temu.modules.sync.mapper.PurchaseOrderSyncTaskMapper;
import com.xiao.temu.modules.sync.service.PurchaseOrderSyncService;
import com.xiao.temu.modules.sync.vo.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 备货单数据同步服务实现类
 */
@Service
@Slf4j
public class PurchaseOrderSyncServiceImpl extends ServiceImpl<PurchaseOrderSyncTaskMapper, PurchaseOrderSyncTask> implements PurchaseOrderSyncService {

    @Autowired
    private PurchaseOrderSyncTaskMapper purchaseOrderSyncTaskMapper;

    @Autowired
    private PurchaseOrderVMapper purchaseOrderVMapper;

    @Autowired
    private ShopService shopService;
    
    @Autowired
    private ApiRateLimiter apiRateLimiter;
    
    @Autowired
    private ProductionProgressService productionProgressService;
    
    @Value("${temu.sync.purchase-order-page-size:100}")
    private int defaultPageSize;
    
    @Value("${temu.sync.purchase-order-max-pages:20}")
    private int defaultMaxPages;
    
    private static final String API_TYPE = "bg.purchaseorderv2.get";
    
    /**
     * 同步指定店铺的备货单数据
     *
     * @param shopId 店铺ID
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse.PurchaseOrderSyncVO syncPurchaseOrderData(Long shopId) {
        // 获取同步任务
        PurchaseOrderSyncTask task = purchaseOrderSyncTaskMapper.getByShopId(shopId);
        if (task == null) {
            // 如果不存在任务，则初始化
            boolean init = initSyncTask(shopId);
            if (!init) {
                ApiResponse.PurchaseOrderSyncVO errorResponse = new ApiResponse.PurchaseOrderSyncVO();
                errorResponse.setSuccess(false);
                errorResponse.setErrorCode(500);
                errorResponse.setErrorMsg("初始化同步任务失败");
                errorResponse.setShopId(shopId);
                return errorResponse;
            }
            task = purchaseOrderSyncTaskMapper.getByShopId(shopId);
        }

        // 获取店铺信息
        Shop shop = shopService.getShopById(shopId).convertToShop();
        if (shop == null) {
            ApiResponse.PurchaseOrderSyncVO errorResponse = new ApiResponse.PurchaseOrderSyncVO();
            errorResponse.setSuccess(false);
            errorResponse.setErrorCode(404);
            errorResponse.setErrorMsg("店铺不存在或已被删除");
            errorResponse.setShopId(shopId);
            return errorResponse;
        }

        // 更新任务状态为同步中
        task.setSyncStatus(1);
        task.setErrorMessage(null);
        purchaseOrderSyncTaskMapper.updateById(task);

        ApiResponse.PurchaseOrderSyncVO response = new ApiResponse.PurchaseOrderSyncVO();
        response.setShopId(shopId);
        response.setShopName(shop.getShopName());
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 同步JIT备货单
            int jitCount = syncJitPurchaseOrders(shopId, defaultPageSize, defaultMaxPages);
            
            // 同步普通备货单
            int normalCount = syncNormalPurchaseOrders(shopId, defaultPageSize, defaultMaxPages);
            
            long endTime = System.currentTimeMillis();
            Date now = new Date();
            
            // 更新任务状态为同步成功
            task.setSyncStatus(2);
            task.setLastSyncTime(now);
            task.setLastUpdateTime(now);  // 同时更新lastUpdateTime
            task.setJitOrderCount(jitCount);
            task.setNormalOrderCount(normalCount);
            purchaseOrderSyncTaskMapper.updateById(task);
            
            // 设置响应结果
            response.setSuccess(true);
            response.setSyncStatus(2);
            response.setNormalOrderCount(normalCount);
            response.setJitOrderCount(jitCount);
            response.setLastSyncTime(task.getLastSyncTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
            response.setLastUpdateTime(task.getLastUpdateTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
            response.setMessage("同步完成，JIT备货单：" + jitCount + "条，普通备货单：" + normalCount + "条，耗时：" + (endTime - startTime) + "ms");
            
            return response;
        } catch (Exception e) {
            log.error("同步备货单数据失败，店铺ID：{}，错误：{}", shopId, e.getMessage(), e);
            
            // 更新任务状态为同步失败
            task.setSyncStatus(3);
            task.setErrorMessage(e.getMessage());
            task.setLastUpdateTime(new Date());  // 同步失败时也更新lastUpdateTime
            purchaseOrderSyncTaskMapper.updateById(task);
            
            // 设置响应结果
            response.setSuccess(false);
            response.setErrorCode(500);
            response.setErrorMsg("同步备货单数据失败: " + e.getMessage());
            response.setSyncStatus(3);
            response.setLastUpdateTime(task.getLastUpdateTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
            
            return response;
        }
    }
    
    /**
     * 同步指定店铺的JIT备货单数据
     *
     * @param shopId 店铺ID
     * @param pageSize 每页数量
     * @param maxPages 最大页数
     * @return 同步的JIT备货单数量
     */
    @Override
    public int syncJitPurchaseOrders(Long shopId, int pageSize, int maxPages) {
        return syncPurchaseOrdersByType(shopId, 1, pageSize, maxPages);
    }
    
    /**
     * 同步指定店铺的普通备货单数据
     *
     * @param shopId 店铺ID
     * @param pageSize 每页数量
     * @param maxPages 最大页数
     * @return 同步的普通备货单数量
     */
    @Override
    public int syncNormalPurchaseOrders(Long shopId, int pageSize, int maxPages) {
        return syncPurchaseOrdersByType(shopId, 0, pageSize, maxPages);
    }
    
    /**
     * 根据类型同步备货单数据
     *
     * @param shopId 店铺ID
     * @param urgencyType 紧急类型(0:普通备货 1:JIT备货)
     * @param pageSize 每页数量
     * @param maxPages 最大页数
     * @return 同步的备货单数量
     */
    private int syncPurchaseOrdersByType(Long shopId, int urgencyType, int pageSize, int maxPages) {
        Shop shop = shopService.getShopById(shopId).convertToShop();
        if (shop == null) {
            throw new RuntimeException("店铺不存在或已被删除");
        }
        
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken(shop.getAccessToken());
        commonParams.setType(API_TYPE);
        commonParams.setAppKey(shop.getApiKey());
        commonParams.setAppSecret(shop.getApiSecret());
        
        int totalCount = 0;
        int currentPage = 1;
        int failedAttempts = 0; // 连续失败次数
        final int MAX_RETRIES = 3; // 最大重试次数
        String errorMessage = null; // 记录错误信息
        
        while (currentPage <= maxPages && failedAttempts < MAX_RETRIES) {
            // 限流控制
            apiRateLimiter.acquire();
            
            Map<String, Object> businessParams = new HashMap<>(8);
            businessParams.put("pageNo", String.valueOf(currentPage));
            businessParams.put("pageSize", String.valueOf(pageSize));
            businessParams.put("urgencyType", String.valueOf(urgencyType)); // 0是普通备货，1是JIT备货
            
            try {
                JSONObject response = TemuApiClient.sendRequest(commonParams, businessParams);
                
                if (!response.containsKey("result") || !response.containsKey("success") || !response.getBooleanValue("success")) {
                    errorMessage = response.getString("errorMsg");
                    log.error("获取{}备货单失败，店铺ID：{}，页码：{}，错误：{}", 
                            urgencyType == 1 ? "JIT" : "普通", shopId, currentPage, errorMessage);
                    
                    failedAttempts++;
                    if (failedAttempts < MAX_RETRIES) {
                        log.info("重试获取{}备货单，店铺ID：{}，页码：{}，第{}次尝试", 
                                urgencyType == 1 ? "JIT" : "普通", shopId, currentPage, failedAttempts + 1);
                        continue; // 重试当前页
                    } else {
                        // 达到最大重试次数,抛出异常而不是仅退出循环
                        throw new RuntimeException(String.format("获取%s备货单失败, 错误: %s", 
                            urgencyType == 1 ? "JIT" : "普通", errorMessage));
                    }
                }
                
                JSONObject result = response.getJSONObject("result");
                int total = result.getIntValue("total");
                
                if (total == 0) {
                    log.info("店铺{}没有{}备货单数据", shopId, urgencyType == 1 ? "JIT" : "普通");
                    break;
                }
                
                JSONArray orderList = result.getJSONArray("subOrderForSupplierList");
                if (orderList == null || orderList.isEmpty()) {
                    log.info("店铺{}第{}页{}备货单数据为空", shopId, currentPage, urgencyType == 1 ? "JIT" : "普通");
                    break;
                }
                
                try {
                    List<PurchaseOrderV> orders = parsePurchaseOrders(orderList, shopId);
                    savePurchaseOrders(orders);
                    
                    totalCount += orders.size();
                    log.info("同步店铺{}的{}备货单，第{}页，数量：{}", shopId, urgencyType == 1 ? "JIT" : "普通", currentPage, orders.size());
                    
                    // 重置失败计数
                    failedAttempts = 0;
                } catch (Exception e) {
                    log.error("处理{}备货单异常，店铺ID：{}，页码：{}，错误：{}", 
                            urgencyType == 1 ? "JIT" : "普通", shopId, currentPage, e.getMessage(), e);
                    
                    failedAttempts++;
                    if (failedAttempts < MAX_RETRIES) {
                        log.info("重试处理{}备货单，店铺ID：{}，页码：{}，第{}次尝试", 
                                urgencyType == 1 ? "JIT" : "普通", shopId, currentPage, failedAttempts + 1);
                        continue; // 重试当前页
                    } else {
                        // 达到最大重试次数,抛出异常
                        throw new RuntimeException(String.format("处理%s备货单异常, 错误: %s", 
                            urgencyType == 1 ? "JIT" : "普通", e.getMessage()), e);
                    }
                }
                
                // 如果当前页数据量小于页大小，说明已经是最后一页
                if (orderList.size() < pageSize) {
                    break;
                }
                
                currentPage++;
                
            } catch (RuntimeException e) {
                // 直接重新抛出RuntimeException
                throw e;
            } catch (Exception e) {
                log.error("获取{}备货单异常，店铺ID：{}，页码：{}，错误：{}", 
                        urgencyType == 1 ? "JIT" : "普通", shopId, currentPage, e.getMessage(), e);
                
                failedAttempts++;
                if (failedAttempts < MAX_RETRIES) {
                    log.info("重试获取{}备货单，店铺ID：{}，页码：{}，第{}次尝试", 
                            urgencyType == 1 ? "JIT" : "普通", shopId, currentPage, failedAttempts + 1);
                    continue; // 重试当前页
                } else {
                    // 达到最大重试次数,抛出异常而不是仅退出循环
                    throw new RuntimeException(String.format("获取%s备货单异常, 错误: %s", 
                        urgencyType == 1 ? "JIT" : "普通", e.getMessage()), e);
                }
            }
        }
        
        return totalCount;
    }
    
    /**
     * 解析备货单数据
     *
     * @param orderList 备货单JSON数组
     * @param shopId 店铺ID
     * @return 备货单列表
     */
    private List<PurchaseOrderV> parsePurchaseOrders(JSONArray orderList, Long shopId) {
        List<PurchaseOrderV> orders = new ArrayList<>(orderList.size());
        
        for (int i = 0; i < orderList.size(); i++) {
            JSONObject orderJson = orderList.getJSONObject(i);
            PurchaseOrderV order = new PurchaseOrderV();
            
            order.setShopId(shopId);
            order.setSubPurchaseOrderSn(orderJson.getString("subPurchaseOrderSn"));
            order.setOriginalPurchaseOrderSn(orderJson.getString("originalPurchaseOrderSn"));
            order.setProductName(orderJson.getString("productName"));
            order.setProductId(orderJson.getLong("productId"));
            order.setProductSkcId(orderJson.getLong("productSkcId"));
            order.setSupplierId(orderJson.getLong("supplierId"));
            order.setSupplierName(orderJson.getString("supplierName"));
            
            // 解析时间
            if (orderJson.containsKey("purchaseTime")) {
                long timestamp = orderJson.getLongValue("purchaseTime");
                // 只有当时间戳大于0时才设置购买时间，避免设置1970-01-01的默认时间
                if (timestamp > 0) {
                    order.setPurchaseTime(new Date(timestamp));
                }
            }
            
            // 解析发货信息
            if (orderJson.containsKey("deliverInfo")) {
                JSONObject deliverInfo = orderJson.getJSONObject("deliverInfo");
                if (deliverInfo.containsKey("deliverTime")) {
                    long timestamp = deliverInfo.getLongValue("deliverTime");
                    // 只有当时间戳大于0时才设置发货时间，避免设置1970-01-01的默认时间
                    if (timestamp > 0) {
                        order.setDeliverTime(new Date(timestamp));
                    }
                }
                order.setDeliveryOrderSn(deliverInfo.getString("deliveryOrderSn"));
                
                if (deliverInfo.containsKey("expectLatestDeliverTimeOrDefault")) {
                    long timestamp = deliverInfo.getLongValue("expectLatestDeliverTimeOrDefault");
                    // 只有当时间戳大于0时才设置期望发货时间，避免设置1970-01-01的默认时间
                    if (timestamp > 0) {
                        order.setExpectLatestDeliverTime(new Date(timestamp));
                    }
                }
                
                if (deliverInfo.containsKey("expectLatestArrivalTimeOrDefault")) {
                    long timestamp = deliverInfo.getLongValue("expectLatestArrivalTimeOrDefault");
                    // 只有当时间戳大于0时才设置期望到货时间，避免设置1970-01-01的默认时间
                    if (timestamp > 0) {
                        order.setExpectLatestArrivalTime(new Date(timestamp));
                    }
                }
                
                order.setReceiveWarehouseId(deliverInfo.getLong("receiveWarehouseId"));
                order.setReceiveWarehouseName(deliverInfo.getString("receiveWarehouseName"));
            }
            
            // 订单状态
            order.setStatus(orderJson.getIntValue("status"));
            
            // 备货类型
            order.setPurchaseStockType(orderJson.getIntValue("purchaseStockType"));
            
            // 数量信息
            if (orderJson.containsKey("skuQuantityTotalInfo")) {
                JSONObject quantityInfo = orderJson.getJSONObject("skuQuantityTotalInfo");
                order.setPurchaseQuantity(quantityInfo.getIntValue("purchaseQuantity"));
                order.setDeliverQuantity(quantityInfo.getIntValue("deliverQuantity"));
                order.setReceiveQuantity(quantityInfo.getIntValue("realReceiveAuthenticQuantity"));
            }
            
            order.setIsDeleted(false);
            order.setSyncTime(new Date());
            order.setCreateTime(new Date());
            order.setUpdateTime(new Date());
            
            orders.add(order);
        }
        
        return orders;
    }
    
    /**
     * 保存备货单数据
     *
     * @param orders 备货单列表
     */
    private void savePurchaseOrders(List<PurchaseOrderV> orders) {
        if (orders == null || orders.isEmpty()) {
            return;
        }
        
        for (PurchaseOrderV order : orders) {
            try {
                PurchaseOrderV existingOrder = purchaseOrderVMapper.getByShopIdAndOrderSn(order.getShopId(), order.getSubPurchaseOrderSn());
                if (existingOrder == null) {
                    try {
                        purchaseOrderVMapper.insert(order);
                    } catch (org.springframework.dao.DuplicateKeyException e) {
                        // 捕获到主键冲突异常，说明数据可能已存在但被标记为删除
                        log.warn("插入备货单时发生主键冲突，转为更新操作, shopId={}, sn={}", 
                                order.getShopId(), order.getSubPurchaseOrderSn());
                        
                        // 查询包括已删除的记录
                        PurchaseOrderV existingDeletedOrder = purchaseOrderVMapper.getByShopIdAndOrderSnIncludeDeleted(
                                order.getShopId(), order.getSubPurchaseOrderSn());
                        
                        if (existingDeletedOrder != null) {
                            // 如果找到记录（包括已删除的），则设置ID并更新
                            order.setId(existingDeletedOrder.getId());
                            order.setIsDeleted(false); // 确保记录不被标记为已删除
                            purchaseOrderVMapper.updateById(order);
                            log.info("成功恢复并更新之前标记为删除的备货单, shopId={}, sn={}", 
                                    order.getShopId(), order.getSubPurchaseOrderSn());
                        } else {
                            // 如果依然查询不到，使用强制更新方法
                            int updated = purchaseOrderVMapper.updateByShopIdAndOrderSnForce(
                                order.getShopId(), 
                                order.getSubPurchaseOrderSn(),
                                order
                            );
                            
                            if (updated > 0) {
                                log.info("成功强制更新备货单数据, shopId={}, sn={}", 
                                        order.getShopId(), order.getSubPurchaseOrderSn());
                            } else {
                                log.error("备货单主键冲突且无法查询到记录或更新失败, shopId={}, sn={}", 
                                        order.getShopId(), order.getSubPurchaseOrderSn());
                            }
                        }
                    }
                } else {
                    order.setId(existingOrder.getId());
                    purchaseOrderVMapper.updateById(order);
                }
                
                // 初始化备货单生产进度
                try {
                    productionProgressService.initProgress(order.getShopId(), order.getSubPurchaseOrderSn());
                    log.debug("同步数据时初始化备货单生产进度成功, shopId={}, sn={}", order.getShopId(), order.getSubPurchaseOrderSn());
                } catch (Exception e) {
                    log.error("同步数据时初始化备货单生产进度失败, shopId={}, sn={}, 错误: {}", 
                            order.getShopId(), order.getSubPurchaseOrderSn(), e.getMessage(), e);
                    // 不影响主流程，继续执行
                }
            } catch (Exception e) {
                log.error("保存备货单数据异常, shopId={}, sn={}, 错误: {}", 
                        order.getShopId(), order.getSubPurchaseOrderSn(), e.getMessage(), e);
                // 单条数据异常不影响其他数据的处理，继续执行
            }
        }
    }
    
    /**
     * 初始化店铺的同步任务
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    @Override
    public boolean initSyncTask(Long shopId) {
        PurchaseOrderSyncTask task = purchaseOrderSyncTaskMapper.getByShopId(shopId);
        if (task == null) {
            Date now = new Date();
            task = new PurchaseOrderSyncTask();
            task.setShopId(shopId);
            task.setSyncStatus(0);
            task.setNormalOrderCount(0);
            task.setJitOrderCount(0);
            task.setLastUpdateTime(now);
            task.setCreateTime(now);
            task.setUpdateTime(now);
            return save(task);
        }
        return true;
    }
    
    /**
     * 获取同步任务列表
     *
     * @param shopIds 店铺ID列表，为空则查询所有
     * @return 同步任务列表
     */
    @Override
    public List<PurchaseOrderSyncTask> getSyncTasks(List<Long> shopIds) {
        if (shopIds == null || shopIds.isEmpty()) {
            return purchaseOrderSyncTaskMapper.getAllTasks();
        } else {
            return purchaseOrderSyncTaskMapper.getTasksByShopIds(shopIds);
        }
    }
    
    /**
     * 根据店铺ID获取同步任务
     *
     * @param shopId 店铺ID
     * @return 同步任务
     */
    @Override
    public PurchaseOrderSyncTask getSyncTaskByShopId(Long shopId) {
        return purchaseOrderSyncTaskMapper.getByShopId(shopId);
    }
    
    /**
     * 执行定时同步
     * 
     * @return 同步结果
     */
    @Override
    public String executeScheduledSync() {
        List<PurchaseOrderSyncTask> tasks = purchaseOrderSyncTaskMapper.getNotSyncingTasks();
        if (tasks == null || tasks.isEmpty()) {
            return "没有需要同步的备货单任务";
        }
        
        int successCount = 0;
        int failCount = 0;
        
        for (PurchaseOrderSyncTask task : tasks) {
            try {
                ApiResponse.PurchaseOrderSyncVO result = syncPurchaseOrderData(task.getShopId());
                if (result.getSuccess()) {
                    successCount++;
                } else {
                    failCount++;
                    log.error("定时同步备货单失败，店铺ID：{}，错误：{}", task.getShopId(), result.getErrorMsg());
                }
            } catch (Exception e) {
                failCount++;
                log.error("定时同步备货单异常，店铺ID：{}，错误：{}", task.getShopId(), e.getMessage(), e);
            }
        }
        
        return String.format("定时同步备货单完成，总任务数：%d，成功：%d，失败：%d", tasks.size(), successCount, failCount);
    }
    
    /**
     * 获取指定店铺的备货单总数
     *
     * @param shopId 店铺ID
     * @param purchaseStockType 备货类型，null表示全部
     * @return 备货单总数
     */
    @Override
    public Integer getPurchaseOrderCountByShopId(Long shopId, Integer purchaseStockType) {
        return purchaseOrderVMapper.countByShopId(shopId, purchaseStockType);
    }
    
    /**
     * 清空店铺的同步数据
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearSyncData(Long shopId) {
        // 物理删除备货单数据，而不是软删除
        purchaseOrderVMapper.physicalDeleteByShopId(shopId, null);
        
        // 重置同步任务状态
        PurchaseOrderSyncTask task = purchaseOrderSyncTaskMapper.getByShopId(shopId);
        if (task != null) {
            Date now = new Date();
            task.setLastSyncTime(null);
            task.setLastUpdateTime(now);  // 更新为当前时间,而不是置空
            task.setSyncStatus(0);
            task.setNormalOrderCount(0);
            task.setJitOrderCount(0);
            task.setErrorMessage(null);
            task.setUpdateTime(now);
            return updateById(task);
        }
        
        return true;
    }
} 