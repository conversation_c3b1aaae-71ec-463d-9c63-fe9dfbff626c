package com.xiao.temu.modules.sales.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 销售库存信息实体类
 * 对应数据库表：sales_inventory_info
 * 存储每个SKU在仓库的具体库存信息
 */
@Data
@TableName("sales_inventory_info")
public class SalesInventoryInfo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 商品SKU ID
     */
    private Long productSkuId;

    /**
     * 仓库组ID
     */
    private Long warehouseGroupId;

    /**
     * 仓内库存
     */
    private Integer warehouseInventoryNum;

    /**
     * 待上架库存
     */
    private Integer waitOnShelfNum;

    /**
     * 待发货库存
     */
    private Integer waitDeliveryInventoryNum;

    /**
     * 预计占用库存
     */
    private Integer expectedOccupiedInventoryNum;

    /**
     * 待审核备货库存
     */
    private Integer waitApproveInventoryNum;

    /**
     * 已上架待质检库存
     */
    private Integer waitQcNum;

    /**
     * 仓内暂不可用库存
     */
    private Integer unavailableWarehouseInventoryNum;

    /**
     * 待入库库存
     */
    private Integer waitInStock;

    /**
     * 待收货库存
     */
    private Integer waitReceiveNum;

    /**
     * 同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}