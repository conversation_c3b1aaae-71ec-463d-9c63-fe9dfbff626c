/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : temu_api

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 04/06/2025 01:04:14
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_message_template
-- ----------------------------
DROP TABLE IF EXISTS `sys_message_template`;
CREATE TABLE `sys_message_template`  (
  `template_id` bigint NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模板编码',
  `template_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模板名称',
  `title_template` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题模板',
  `content_template` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '内容模板',
  `template_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '模板类型(1系统消息 2任务提醒 3店铺消息)',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '状态(0正常 1禁用)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`template_id`) USING BTREE,
  UNIQUE INDEX `idx_template_code`(`template_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '消息模板表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_message_template
-- ----------------------------
INSERT INTO `sys_message_template` VALUES (1, 'SYSTEM_NOTICE', '系统通知', '系统通知：${title}', '尊敬的用户，${content}', '1', '0', '2025-03-24 08:48:20', NULL, '系统通知默认模板');
INSERT INTO `sys_message_template` VALUES (2, 'TASK_REMINDER', '任务提醒', '任务提醒：${taskName}', '您有一项任务\"${taskName}\"需要处理，截止时间为${endTime}。', '2', '0', '2025-03-24 08:48:20', NULL, '任务提醒默认模板');
INSERT INTO `sys_message_template` VALUES (3, 'SHOP_NOTICE', '店铺通知', '店铺通知：${shopName}', '您关注的店铺\"${shopName}\"有新的消息：${content}', '3', '0', '2025-03-24 08:48:20', NULL, '店铺通知默认模板');
INSERT INTO `sys_message_template` VALUES (4, 'JIT_SOON_OVERDUE', 'JIT备货单到货即将逾期通知', '【JIT备货单到货即将逾期提醒】- ${shopName}', '以下店铺的JIT备货单将于24小时内到达预计最晚到货时间，请及时处理：\n{{#orderItems}}\n备货单号：{{subPurchaseOrderSn}}\n商品名称：{{productName}}\n预计最晚到货时间：{{expectLatestArrivalTime?string(\"yyyy-MM-dd HH:mm:ss\")}}\n{{/orderItems}}', '2', '0', '2025-06-03 10:06:19', '2025-06-03 16:25:25', 'JIT备货单到货即将逾期通知模板');
INSERT INTO `sys_message_template` VALUES (5, 'JIT_OVERDUE', 'JIT备货单到货已逾期通知', '【JIT备货单到货已逾期提醒】- ${shopName}', '以下店铺的JIT备货单已超过预计最晚到货时间，请立即处理：\n{{#orderItems}}\n备货单号：{{subPurchaseOrderSn}}\n商品名称：{{productName}}\n预计最晚到货时间：{{expectLatestArrivalTime?string(\"yyyy-MM-dd HH:mm:ss\")}}\n{{/orderItems}}', '2', '0', '2025-06-03 10:06:19', '2025-06-03 16:25:25', 'JIT备货单到货已逾期通知模板');
INSERT INTO `sys_message_template` VALUES (6, 'NORMAL_NOT_DELIVERED', '普通备货单未发货提醒', '【普通备货单未发货提醒】- ${shopName}', '以下店铺的普通备货单已创建5天仍未发货，请检查处理：\n{{#orderItems}}\n备货单号：{{subPurchaseOrderSn}}\n商品名称：{{productName}}\n备货时间：{{purchaseTime?string(\"yyyy-MM-dd HH:mm:ss\")}}\n{{/orderItems}}', '2', '0', '2025-06-03 10:06:19', '2025-06-03 16:25:25', '普通备货单未发货提醒模板');
INSERT INTO `sys_message_template` VALUES (7, 'NORMAL_NOT_RECEIVED', '普通备货单未到货提醒', '【普通备货单未到货提醒】- ${shopName}', '以下店铺的普通备货单已发货5天仍未收货，请跟进物流状态：\n{{#orderItems}}\n备货单号：{{subPurchaseOrderSn}}\n商品名称：{{productName}}\n发货时间：{{shippingTime?string(\"yyyy-MM-dd HH:mm:ss\")}}\n{{/orderItems}}', '2', '0', '2025-06-03 10:06:19', '2025-06-03 16:25:25', '普通备货单未到货提醒模板');
INSERT INTO `sys_message_template` VALUES (8, 'JIT_SOON_OVERDUE_BATCH', 'JIT备货单即将逾期批量通知', '【${notifyTypeName}】系统共有${totalOrderCount}个备货单需要处理', '<p><strong>${notifyTypeName}通知</strong>：系统中共有<strong>${totalOrderCount}</strong>个备货单需要处理</p>\r\n#foreach($shopGroup in $shopGroups)\r\n<div style=\"margin-top: 10px; border-top: 1px solid #eee; padding-top: 5px;\">\r\n  <h3>店铺【${shopGroup.shopName}】${shopGroup.shopRemark} - 共${shopGroup.items.size()}个备货单</h3>\r\n  <table border=\"1\" cellpadding=\"5\" cellspacing=\"0\" style=\"border-collapse: collapse; width: 100%;\">\r\n    <tr style=\"background-color: #f2f2f2;\">\r\n      <th>备货单号</th>\r\n      <th>商品名称</th>\r\n      <th>预计最晚到货时间</th>\r\n    </tr>\r\n    #foreach($item in $shopGroup.items)\r\n    <tr>\r\n      <td>${item.subPurchaseOrderSn}</td>\r\n      <td>${item.productName}</td>\r\n      <td>#if($item.expectLatestArrivalTime)${formatDate.format(\"yyyy-MM-dd HH:mm:ss\", $item.expectLatestArrivalTime)}#end</td>\r\n    </tr>\r\n    #end\r\n  </table>\r\n</div>\r\n#end\r\n<p style=\"margin-top: 20px; color: #d14836; font-weight: bold;\">请各相关运营人员及时处理！</p>', '2', '0', '2025-06-03 21:05:36', '2025-06-03 21:05:36', 'JIT备货单即将逾期批量通知模板');
INSERT INTO `sys_message_template` VALUES (9, 'JIT_OVERDUE_BATCH', 'JIT备货单已逾期批量通知', '【${notifyTypeName}】系统共有${totalOrderCount}个备货单需要处理', '<p><strong>${notifyTypeName}通知</strong>：系统中共有<strong>${totalOrderCount}</strong>个备货单需要处理</p>\r\n#foreach($shopGroup in $shopGroups)\r\n<div style=\"margin-top: 10px; border-top: 1px solid #eee; padding-top: 5px;\">\r\n  <h3>店铺【${shopGroup.shopName}】${shopGroup.shopRemark} - 共${shopGroup.items.size()}个备货单</h3>\r\n  <table border=\"1\" cellpadding=\"5\" cellspacing=\"0\" style=\"border-collapse: collapse; width: 100%;\">\r\n    <tr style=\"background-color: #f2f2f2;\">\r\n      <th>备货单号</th>\r\n      <th>商品名称</th>\r\n      <th>预计最晚到货时间</th>\r\n    </tr>\r\n    #foreach($item in $shopGroup.items)\r\n    <tr>\r\n      <td>${item.subPurchaseOrderSn}</td>\r\n      <td>${item.productName}</td>\r\n      <td>#if($item.expectLatestArrivalTime)${formatDate.format(\"yyyy-MM-dd HH:mm:ss\", $item.expectLatestArrivalTime)}#end</td>\r\n    </tr>\r\n    #end\r\n  </table>\r\n</div>\r\n#end\r\n<p style=\"margin-top: 20px; color: #d14836; font-weight: bold;\">请各相关运营人员立即处理！</p>', '2', '0', '2025-06-03 21:05:36', '2025-06-03 21:05:36', 'JIT备货单已逾期批量通知模板');
INSERT INTO `sys_message_template` VALUES (10, 'NORMAL_NOT_DELIVERED_BATCH', '普通备货单未发货批量通知', '【${notifyTypeName}】系统共有${totalOrderCount}个备货单需要处理', '<p><strong>${notifyTypeName}通知</strong>：系统中共有<strong>${totalOrderCount}</strong>个备货单需要处理</p>\r\n#foreach($shopGroup in $shopGroups)\r\n<div style=\"margin-top: 10px; border-top: 1px solid #eee; padding-top: 5px;\">\r\n  <h3>店铺【${shopGroup.shopName}】${shopGroup.shopRemark} - 共${shopGroup.items.size()}个备货单</h3>\r\n  <table border=\"1\" cellpadding=\"5\" cellspacing=\"0\" style=\"border-collapse: collapse; width: 100%;\">\r\n    <tr style=\"background-color: #f2f2f2;\">\r\n      <th>备货单号</th>\r\n      <th>商品名称</th>\r\n      <th>创建时间</th>\r\n    </tr>\r\n    #foreach($item in $shopGroup.items)\r\n    <tr>\r\n      <td>${item.subPurchaseOrderSn}</td>\r\n      <td>${item.productName}</td>\r\n      <td>#if($item.purchaseTime)${formatDate.format(\"yyyy-MM-dd HH:mm:ss\", $item.purchaseTime)}#end</td>\r\n    </tr>\r\n    #end\r\n  </table>\r\n</div>\r\n#end\r\n<p style=\"margin-top: 20px; color: #d14836; font-weight: bold;\">请各相关运营人员及时联系供应商发货！</p>', '2', '0', '2025-06-03 21:05:36', '2025-06-03 21:05:36', '普通备货单未发货批量通知模板');
INSERT INTO `sys_message_template` VALUES (11, 'NORMAL_NOT_RECEIVED_BATCH', '普通备货单未到货批量通知', '【${notifyTypeName}】系统共有${totalOrderCount}个备货单需要处理', '<p><strong>${notifyTypeName}通知</strong>：系统中共有<strong>${totalOrderCount}</strong>个备货单需要处理</p>\r\n#foreach($shopGroup in $shopGroups)\r\n<div style=\"margin-top: 10px; border-top: 1px solid #eee; padding-top: 5px;\">\r\n  <h3>店铺【${shopGroup.shopName}】${shopGroup.shopRemark} - 共${shopGroup.items.size()}个备货单</h3>\r\n  <table border=\"1\" cellpadding=\"5\" cellspacing=\"0\" style=\"border-collapse: collapse; width: 100%;\">\r\n    <tr style=\"background-color: #f2f2f2;\">\r\n      <th>备货单号</th>\r\n      <th>商品名称</th>\r\n      <th>发货时间</th>\r\n    </tr>\r\n    #foreach($item in $shopGroup.items)\r\n    <tr>\r\n      <td>${item.subPurchaseOrderSn}</td>\r\n      <td>${item.productName}</td>\r\n      <td>#if($item.shippingTime)${formatDate.format(\"yyyy-MM-dd HH:mm:ss\", $item.shippingTime)}#end</td>\r\n    </tr>\r\n    #end\r\n  </table>\r\n</div>\r\n#end\r\n<p style=\"margin-top: 20px; color: #d14836; font-weight: bold;\">请各相关运营人员及时跟进物流状态！</p>', '2', '0', '2025-06-03 21:05:36', '2025-06-03 21:05:36', '普通备货单未到货批量通知模板');

SET FOREIGN_KEY_CHECKS = 1;
