package com.xiao.temu.modules.purchaseorderv.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 备货单通知记录实体类
 */
@Data
@TableName("purchase_order_notification")
public class PurchaseOrderNotification {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 备货单号
     */
    private String subPurchaseOrderSn;
    
    /**
     * 通知类型(1:JIT即将逾期 2:JIT已逾期 3:通备货未发货 4:普通备货未到货)
     */
    private Integer notificationType;
    
    /**
     * 通知次数
     */
    private Integer notifyCount;
    
    /**
     * 最后通知时间
     */
    private Date lastNotifyTime;
    
    /**
     * 通知状态(0:待通知 1:通知中 2:已通知完成)
     */
    private Integer notifyStatus;
    
    /**
     * 通知的运营组ID
     */
    private Long groupId;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 