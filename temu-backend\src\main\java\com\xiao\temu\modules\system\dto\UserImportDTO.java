package com.xiao.temu.modules.system.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 用户导入DTO
 */
@Data
public class UserImportDTO {

    /**
     * 用户名
     */
    @ExcelProperty(value = "用户名", index = 0)
    private String username;

    /**
     * 昵称
     */
    @ExcelProperty(value = "昵称", index = 1)
    private String nickName;

    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱", index = 2)
    private String email;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号码", index = 3)
    private String phone;

    /**
     * 角色
     */
    @ExcelProperty(value = "角色", index = 4)
    private String roleNames;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 5)
    private String remark;
} 