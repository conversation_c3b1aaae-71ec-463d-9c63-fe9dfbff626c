package com.xiao.temu.modules.quality.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.quality.entity.QualityInspection;
import com.xiao.temu.modules.quality.vo.LocalQualityInspectionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 本地质检结果数据Mapper接口
 */
@Mapper
public interface LocalQualityInspectionMapper extends BaseMapper<QualityInspection> {
    
    /**
     * 分页查询质检数据
     *
     * @param page 分页对象
     * @param shopId 店铺ID
     * @param qcResult 质检结果 1-合格 2-不合格
     * @param purchaseNoList 采购单号列表
     * @param skuIdList SKU ID列表
     * @param skcIdList SKC ID列表
     * @param qcResultUpdateTimeBegin 质检结果更新时间开始
     * @param qcResultUpdateTimeEnd 质检结果更新时间结束
     * @param skuNameKeyword SKU名称关键词
     * @return 分页数据
     */
    IPage<LocalQualityInspectionVO.LocalQualityInspectionItemVO> selectQualityInspectionPage(
            Page<LocalQualityInspectionVO.LocalQualityInspectionItemVO> page,
            @Param("shopId") Long shopId,
            @Param("qcResult") String qcResult,
            @Param("purchaseNoList") List<String> purchaseNoList,
            @Param("skuIdList") List<Long> skuIdList,
            @Param("skcIdList") List<Long> skcIdList,
            @Param("qcResultUpdateTimeBegin") LocalDateTime qcResultUpdateTimeBegin,
            @Param("qcResultUpdateTimeEnd") LocalDateTime qcResultUpdateTimeEnd,
            @Param("skuNameKeyword") String skuNameKeyword
    );
    
    /**
     * 多店铺分页查询质检数据
     *
     * @param page 分页对象
     * @param shopIds 店铺ID列表
     * @param qcResult 质检结果 1-合格 2-不合格
     * @param purchaseNoList 采购单号列表
     * @param skuIdList SKU ID列表
     * @param skcIdList SKC ID列表
     * @param qcResultUpdateTimeBegin 质检结果更新时间开始
     * @param qcResultUpdateTimeEnd 质检结果更新时间结束
     * @param skuNameKeyword SKU名称关键词
     * @return 分页数据
     */
    IPage<LocalQualityInspectionVO.LocalQualityInspectionItemVO> selectMultiShopQualityInspectionPage(
            Page<LocalQualityInspectionVO.LocalQualityInspectionItemVO> page,
            @Param("shopIds") List<Long> shopIds,
            @Param("qcResult") String qcResult,
            @Param("purchaseNoList") List<String> purchaseNoList,
            @Param("skuIdList") List<Long> skuIdList,
            @Param("skcIdList") List<Long> skcIdList,
            @Param("qcResultUpdateTimeBegin") LocalDateTime qcResultUpdateTimeBegin,
            @Param("qcResultUpdateTimeEnd") LocalDateTime qcResultUpdateTimeEnd,
            @Param("skuNameKeyword") String skuNameKeyword
    );
} 