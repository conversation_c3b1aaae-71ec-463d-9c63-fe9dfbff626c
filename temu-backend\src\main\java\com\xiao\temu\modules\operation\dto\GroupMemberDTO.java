package com.xiao.temu.modules.operation.dto;

import com.xiao.temu.modules.system.dto.RoleDTO;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 运营组成员数据传输对象
 */
@Data
public class GroupMemberDTO {

    /**
     * ID
     */
    private Long id;

    /**
     * 运营组ID
     */
    @NotNull(message = "运营组ID不能为空")
    private Long groupId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 用户名（查询结果显示）
     */
    private String username;

    /**
     * 用户昵称（查询结果显示）
     */
    private String nickName;

    /**
     * 手机号码
     */
    private String phonenumber;

    /**
     * 用户角色列表
     */
    private List<RoleDTO> roles;

    /**
     * 加入时间
     */
    private Date joinTime;

    /**
     * 状态（0正常 1禁用）
     */
    private String status;

    /**
     * 是否为负责人（查询结果显示）
     */
    private Boolean isLeader;
} 