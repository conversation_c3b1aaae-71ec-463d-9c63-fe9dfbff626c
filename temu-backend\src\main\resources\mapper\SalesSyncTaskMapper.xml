<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.sync.mapper.SalesSyncTaskMapper">

    <!-- 根据店铺ID列表查询同步任务 -->
    <select id="findByShopIds" resultType="com.xiao.temu.modules.sync.entity.SalesSyncTask">
        SELECT * FROM sales_sync_task
        <where>
            <if test="shopIds != null and shopIds.size() > 0">
                shop_id IN
                <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
        </where>
        ORDER BY id ASC
    </select>

</mapper> 