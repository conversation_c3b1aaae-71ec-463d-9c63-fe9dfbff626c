package com.xiao.temu.modules.purchaseorderv.service;

import com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderV;

/**
 * 备货单通知服务接口
 */
public interface PurchaseOrderNotificationService {
    
    /**
     * 处理JIT即将逾期通知
     * 到货前24小时
     */
    void processJitSoonOverdueNotification();
    
    /**
     * 处理JIT已逾期通知
     * 逾期后连续3天
     */
    void processJitOverdueNotification();
    
    /**
     * 处理普通备货未发货通知
     * 创建5天后未发货，连续提醒3天
     */
    void processNormalNotDeliveredNotification();
    
    /**
     * 处理普通备货未到货通知
     * 发货5天后未收货，连续提醒3天
     */
    void processNormalNotReceivedNotification();
    
    /**
     * 设置跳过小时检查（测试用）
     */
    void setSkipHoursCheck(boolean skipHoursCheck);
    
    /**
     * 设置跳过天数检查（测试用）
     */
    void setSkipDaysCheck(boolean skipDaysCheck);
    
    /**
     * 设置跳过通知次数限制（测试用）
     */
    void setSkipNotifyCountCheck(boolean skipNotifyCountCheck);
    
    /**
     * 设置模拟当前时间（测试用）
     */
    void setMockCurrentTime(String mockCurrentTime);
    
    /**
     * 重置测试标志
     */
    void resetTestFlags();
} 