package com.xiao;

import com.alibaba.fastjson2.JSONObject;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.infrastructure.api.TemuApiClient;

import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 用于测试bg.shiporderv2.get接口在高并发情况下的稳定性
 * 模拟多个并发请求，观察接口限流和异常情况
 */
public class ShipOrderApiStressTest {

    private static final String LOG_FILE = "ship_order_api_stress_test.log";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
    
    // 并发线程数
    private static final int THREAD_COUNT = 5;
    // 每个线程调用次数
    private static final int CALLS_PER_THREAD = 3;
    
    // 统计变量
    private static final AtomicInteger successCount = new AtomicInteger(0);
    private static final AtomicInteger failCount = new AtomicInteger(0);
    
    public static void main(String[] args) {
        System.out.println("开始进行bg.shiporderv2.get接口压力测试...");
        System.out.println("并发线程数: " + THREAD_COUNT);
        System.out.println("每个线程调用次数: " + CALLS_PER_THREAD);
        System.out.println("总调用次数: " + (THREAD_COUNT * CALLS_PER_THREAD));
        System.out.println("测试结果将保存到" + LOG_FILE + "文件中");
        
        // 清空日志文件
        try (PrintWriter writer = new PrintWriter(new FileWriter(LOG_FILE))) {
            writer.println("======== TEMU API压力测试日志 ========");
            writer.println("测试接口: bg.shiporderv2.get");
            writer.println("测试时间: " + DATE_FORMAT.format(new Date()));
            writer.println("并发线程数: " + THREAD_COUNT);
            writer.println("每个线程调用次数: " + CALLS_PER_THREAD);
            writer.println("总调用次数: " + (THREAD_COUNT * CALLS_PER_THREAD));
            writer.println("==============================");
        } catch (IOException e) {
            System.err.println("无法创建日志文件: " + e.getMessage());
            return;
        }
        
        // 创建线程池
        ExecutorService executorService = Executors.newFixedThreadPool(THREAD_COUNT);
        // 使用CountDownLatch等待所有线程完成
        CountDownLatch latch = new CountDownLatch(THREAD_COUNT);
        
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        
        // 提交任务给线程池
        for (int i = 0; i < THREAD_COUNT; i++) {
            final int threadId = i + 1;
            executorService.submit(() -> {
                try {
                    // 每个线程执行多次调用
                    for (int j = 0; j < CALLS_PER_THREAD; j++) {
                        try {
                            // 调用API并记录结果
                            JSONObject result = callShipOrderV2Api();
                            boolean isSuccess = result != null && result.getBooleanValue("success");
                            
                            if (isSuccess) {
                                successCount.incrementAndGet();
                                logResult(threadId, j + 1, true, result);
                                System.out.println("线程" + threadId + "的第" + (j + 1) + "次调用成功");
                            } else {
                                failCount.incrementAndGet();
                                logResult(threadId, j + 1, false, result);
                                System.out.println("线程" + threadId + "的第" + (j + 1) + "次调用失败: " + 
                                        (result != null ? result.getString("errorMsg") : "无响应"));
                            }
                            
                            // 每次调用后等待一小段时间，避免过快请求导致限流
                            if (j < CALLS_PER_THREAD - 1) {
                                Thread.sleep(1000);
                            }
                            
                        } catch (Exception e) {
                            failCount.incrementAndGet();
                            logError(threadId, j + 1, e);
                            System.out.println("线程" + threadId + "的第" + (j + 1) + "次调用异常: " + e.getMessage());
                        }
                    }
                } finally {
                    // 计数器减一
                    latch.countDown();
                }
            });
        }
        
        try {
            // 等待所有线程完成
            latch.await();
            // 关闭线程池
            executorService.shutdown();
            
            // 记录结束时间
            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;
            
            // 输出统计结果
            System.out.println("\n测试完成，统计结果:");
            System.out.println("总调用次数: " + (THREAD_COUNT * CALLS_PER_THREAD));
            System.out.println("成功次数: " + successCount.get());
            System.out.println("失败次数: " + failCount.get());
            System.out.println("成功率: " + (successCount.get() * 100.0 / (THREAD_COUNT * CALLS_PER_THREAD)) + "%");
            System.out.println("总耗时: " + totalTime + "ms");
            System.out.println("平均每次调用耗时: " + (totalTime * 1.0 / (THREAD_COUNT * CALLS_PER_THREAD)) + "ms");
            System.out.println("详细日志请查看: " + LOG_FILE);
            
            // 记录统计结果到日志
            try (PrintWriter writer = new PrintWriter(new FileWriter(LOG_FILE, true))) {
                writer.println("\n======== 测试统计 ========");
                writer.println("总调用次数: " + (THREAD_COUNT * CALLS_PER_THREAD));
                writer.println("成功次数: " + successCount.get());
                writer.println("失败次数: " + failCount.get());
                writer.println("成功率: " + (successCount.get() * 100.0 / (THREAD_COUNT * CALLS_PER_THREAD)) + "%");
                writer.println("总耗时: " + totalTime + "ms");
                writer.println("平均每次调用耗时: " + (totalTime * 1.0 / (THREAD_COUNT * CALLS_PER_THREAD)) + "ms");
                writer.println("测试结束时间: " + DATE_FORMAT.format(new Date()));
                writer.println("=======================");
            } catch (IOException e) {
                System.err.println("无法写入日志文件: " + e.getMessage());
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            System.err.println("测试被中断: " + e.getMessage());
        }
    }
    
    /**
     * 调用bg.shiporderv2.get接口
     * @return 接口返回的JSON对象
     */
    private static JSONObject callShipOrderV2Api() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken("juekxhoepdyctc7lgex2nvvo8gbhzg9rxtklha6vgvxipe46njx7gz20");
        commonParams.setType("bg.shiporderv2.get");
        commonParams.setAppKey("9c65725c36feec08d6b7848cdff20e2a");
        commonParams.setAppSecret("f6cc806fb70ee21fb06ea176db6d3b9bb10b0f78");

        HashMap<String, Object> businessParams = new HashMap<>();
        businessParams.put("pageNo", 1);
        businessParams.put("pageSize", 10);
        // 添加timestamp参数，避免缓存问题
        businessParams.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        return TemuApiClient.sendRequest(commonParams, businessParams);
    }
    
    /**
     * 记录API调用结果
     * @param threadId 线程ID
     * @param callCount 当前线程中的调用次数
     * @param isSuccess 是否调用成功
     * @param result API返回的结果
     */
    private static synchronized void logResult(int threadId, int callCount, boolean isSuccess, JSONObject result) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(LOG_FILE, true))) {
            writer.println("\n----- 线程" + threadId + "的第" + callCount + "次调用 -----");
            writer.println("调用时间: " + DATE_FORMAT.format(new Date()));
            writer.println("调用状态: " + (isSuccess ? "成功" : "失败"));
            
            if (result != null) {
                writer.println("返回结果:");
                writer.println(result.toJSONString());
                
                if (!isSuccess) {
                    writer.println("错误码: " + result.getString("errorCode"));
                    writer.println("错误信息: " + result.getString("errorMsg"));
                    writer.println("请求ID: " + result.getString("requestId"));
                } else {
                    writer.println("请求ID: " + result.getString("requestId"));
                    writer.println("数据条数: " + (result.getJSONObject("result") != null ? result.getJSONObject("result").getIntValue("total") : 0));
                }
            } else {
                writer.println("返回结果: 空");
            }
        } catch (IOException e) {
            System.err.println("无法写入日志文件: " + e.getMessage());
        }
    }
    
    /**
     * 记录API调用异常
     * @param threadId 线程ID
     * @param callCount 当前线程中的调用次数
     * @param e 异常对象
     */
    private static synchronized void logError(int threadId, int callCount, Exception e) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(LOG_FILE, true))) {
            writer.println("\n----- 线程" + threadId + "的第" + callCount + "次调用 -----");
            writer.println("调用时间: " + DATE_FORMAT.format(new Date()));
            writer.println("调用状态: 异常");
            writer.println("异常信息: " + e.getMessage());
            writer.println("异常堆栈:");
            e.printStackTrace(writer);
        } catch (IOException ex) {
            System.err.println("无法写入日志文件: " + ex.getMessage());
        }
    }
} 