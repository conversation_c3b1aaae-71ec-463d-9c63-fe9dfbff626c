<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.production.mapper.ProductionGroupMapper">

    <resultMap id="productionGroupMap" type="com.xiao.temu.modules.production.entity.ProductionGroup">
        <id property="groupId" column="group_id"/>
        <result property="groupName" column="group_name"/>
        <result property="leaderId" column="leader_id"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>
    
    <resultMap id="productionGroupDTOMap" type="com.xiao.temu.modules.production.dto.ProductionGroupDTO">
        <id property="groupId" column="group_id"/>
        <result property="groupName" column="group_name"/>
        <result property="leaderId" column="leader_id"/>
        <result property="leaderName" column="leader_name"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="memberCount" column="member_count"/>
    </resultMap>
    
    <!-- 查询生产组列表 -->
    <select id="selectGroupList" resultMap="productionGroupDTOMap">
        SELECT 
            g.group_id, 
            g.group_name, 
            g.leader_id, 
            u.nick_name as leader_name, 
            g.status, 
            g.create_time, 
            g.update_time, 
            g.remark,
            (SELECT COUNT(1) FROM production_group_member m 
             JOIN sys_user su ON m.user_id = su.user_id 
             WHERE m.group_id = g.group_id AND m.status = '0') as member_count
        FROM production_group g
        LEFT JOIN sys_user u ON g.leader_id = u.user_id
        <where>
            <if test="query.groupName != null and query.groupName != ''">
                AND g.group_name LIKE CONCAT('%', #{query.groupName}, '%')
            </if>
            <if test="query.leaderId != null">
                AND g.leader_id = #{query.leaderId}
            </if>
            <if test="query.leaderName != null and query.leaderName != ''">
                AND u.nick_name LIKE CONCAT('%', #{query.leaderName}, '%')
            </if>
            <if test="query.status != null and query.status != ''">
                AND g.status = #{query.status}
            </if>
            <if test="query.memberId != null">
                AND EXISTS (
                    SELECT 1 FROM production_group_member m 
                    WHERE m.group_id = g.group_id 
                    AND m.user_id = #{query.memberId} 
                    AND m.status = '0'
                )
            </if>
        </where>
        ORDER BY g.create_time DESC
    </select>
    
    <!-- 查询生产组详情 -->
    <select id="selectGroupById" resultMap="productionGroupDTOMap">
        SELECT 
            g.group_id, 
            g.group_name, 
            g.leader_id, 
            u.nick_name as leader_name, 
            g.status, 
            g.create_time, 
            g.update_time, 
            g.remark,
            (SELECT COUNT(1) FROM production_group_member m 
             JOIN sys_user su ON m.user_id = su.user_id 
             WHERE m.group_id = g.group_id AND m.status = '0') as member_count
        FROM production_group g
        LEFT JOIN sys_user u ON g.leader_id = u.user_id
        WHERE g.group_id = #{groupId}
    </select>
    
    <!-- 检查生产组名称是否唯一 -->
    <select id="checkGroupNameUnique" resultType="int">
        SELECT COUNT(1)
        FROM production_group
        WHERE group_name = #{groupName}
        <if test="groupId != null">
            AND group_id != #{groupId}
        </if>
    </select>
    
    <!-- 更新生产组状态 -->
    <update id="updateStatus">
        UPDATE production_group
        SET status = #{status}, update_time = NOW()
        WHERE group_id = #{groupId}
    </update>
    
    <!-- 根据负责人ID查询生产组列表 -->
    <select id="selectGroupsByLeaderId" resultMap="productionGroupMap">
        SELECT * FROM production_group
        WHERE leader_id = #{leaderId}
        AND status = '0'
    </select>
</mapper> 