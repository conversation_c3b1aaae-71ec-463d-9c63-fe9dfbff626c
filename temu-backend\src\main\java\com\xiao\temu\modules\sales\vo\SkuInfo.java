package com.xiao.temu.modules.sales.vo;

import lombok.Data;

/**
 * SKU信息VO
 */
@Data
public class SkuInfo {
    /**
     * 商品SKU ID
     */
    private Long productSkuId;
    
    /**
     * SKU货号
     */
    private String skuExtCode;
    
    /**
     * 尺码名称
     */
    private String className;
    
    /**
     * 颜色
     */
    private String color;
    
    /**
     * 尺码
     */
    private String size;
    
    /**
     * 备货天数
     */
    private Integer stockDays;
    
    /**
     * 今日销量
     */
    private Integer todaySaleVolume;
    
    /**
     * 总销量
     */
    private Integer totalSaleVolume;
    
    /**
     * 近7天销量
     */
    private Integer lastSevenDaysSaleVolume;
    
    /**
     * 近30天销量
     */
    private Integer lastThirtyDaysSaleVolume;
    
    /**
     * 用户加购数量
     */
    private Integer inCartNumber;
    
    /**
     * 近7天用户加购数量
     */
    private Integer inCartNumber7d;
    
    /**
     * 缺货数量
     */
    private Integer lackQuantity;
} 