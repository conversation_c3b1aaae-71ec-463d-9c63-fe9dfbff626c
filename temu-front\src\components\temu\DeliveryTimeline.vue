<template>
  <div class="custom-timeline-wrapper">
    <!-- 发货节点 -->
    <div class="timeline-node delivery-node" :class="`node-type-${deliveryTimelineType}`">
      <div class="node-circle" :class="{ hollow: !isDeliveryCompleted }"></div>
      <div class="node-content">
        <div>需 {{ formatDate(expectLatestDeliverTime) + '前发货' }}</div>
        <div class="status-tag" v-if="isDeliveryCompleted">
          <el-tag :type="isDeliveryOnTime ? 'success' : 'info'" size="small">{{ isDeliveryOnTime ? '已准时发货' : '已逾期' }}</el-tag>
        </div>
        <div class="status-tag" v-else>
          <el-tag :type="isDeliveryOverdue ? 'danger' : 'warning'" size="small">{{ getDeliveryStatus }}</el-tag>
        </div>
      </div>
      <div v-if="showTimeInterval" class="interval-text">{{ timeInterval }}</div>
    </div>

    <!-- 连接线通过 ::before 伪元素绘制 -->

    <!-- 到货节点 - 显示到货信息，不管expectLatestArrivalTime是否存在 -->
    <div class="timeline-node arrival-node" :class="`node-type-${arrivalTimelineType}`">
      <div class="node-circle" :class="{ hollow: !isArrivalCompleted }"></div>
      <div class="node-content">
        <div>需 {{ expectLatestArrivalTime ? formatDate(expectLatestArrivalTime) + '前到货' : '-前到货' }}</div>
        <div class="status-tag" v-if="isArrivalCompleted">
          <el-tag type="success" size="small">已完成</el-tag>
        </div>
        <div class="status-tag" v-else-if="expectLatestArrivalTime">
          <el-tag :type="isArrivalOverdue ? 'danger' : 'warning'" size="small">{{ getArrivalStatus }}</el-tag>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElTag } from 'element-plus' // 确保导入ElTag

const props = defineProps({
  // 最晚发货时间
  expectLatestDeliverTime: {
    type: Number,
    default: null
  },
  // 最晚到货时间
  expectLatestArrivalTime: {
    type: Number,
    default: null
  },
  // 实际发货时间
  deliverTime: {
    type: Number,
    default: null
  },
  // 实际收货时间（可用于判断到货是否完成）
  realReceiveTime: {
    type: Number,
    default: null
  },
  // 订单状态（用于判断到货是否完成）
  status: {
    type: Number,
    default: 0
  }
})

// 格式化日期为MM-DD格式
const formatDate = (timestamp: number | null) => {
  if (!timestamp) return '-'
  // 增加HH:mm显示
  const date = new Date(timestamp)
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${month}-${day} ${hours}:${minutes}`
}

// 判断发货是否完成
const isDeliveryCompleted = computed(() => {
  return !!props.deliverTime
})

// 判断发货是否逾期
const isDeliveryOverdue = computed(() => {
  if (!props.expectLatestDeliverTime) return false
  const now = Date.now()
  return !isDeliveryCompleted.value && now > props.expectLatestDeliverTime
})

// 判断发货是否准时
const isDeliveryOnTime = computed(() => {
  if (!props.expectLatestDeliverTime || !props.deliverTime) return false
  return props.deliverTime <= props.expectLatestDeliverTime
})

// 获取发货状态文本
const getDeliveryStatus = computed(() => {
  if (!props.expectLatestDeliverTime) return ''
  
  if (isDeliveryOverdue.value) {
    // 修改为只显示"已逾期"三个字
    return '已逾期'
  } else {
    const { hours, minutes } = getRemainingTime(props.expectLatestDeliverTime)
    let remainingText = ''
    if (hours > 0 || minutes > 0) {
        remainingText = `${hours > 0 ? hours + '小时' : ''}${minutes > 0 ? minutes + '分钟' : ''}后逾期`
    } else {
        remainingText = '即将逾期'
    }
    return remainingText
  }
})

// 判断到货是否已完成
const isArrivalCompleted = computed(() => {
  // 根据实际业务调整，这里用 realReceiveTime 或 status 判断
  return !!props.realReceiveTime || props.status === 3 || props.status === 6 || props.status === 7
})

// 判断到货是否已逾期
const isArrivalOverdue = computed(() => {
  if (!props.expectLatestArrivalTime) return false
  const now = Date.now()
  // 确保在未完成的情况下才判断是否逾期
  return !isArrivalCompleted.value && now > props.expectLatestArrivalTime
})

// 获取到货状态文本
const getArrivalStatus = computed(() => {
  if (!props.expectLatestArrivalTime) return ''
  
  if (isArrivalOverdue.value) {
    // 修改为只显示"已逾期"三个字
    return '已逾期'
  } else {
    const { hours, minutes } = getRemainingTime(props.expectLatestArrivalTime)
     let remainingText = ''
    if (hours > 0 || minutes > 0) {
        remainingText = `${hours > 0 ? hours + '小时' : ''}${minutes > 0 ? minutes + '分钟' : ''}后逾期`
    } else {
        remainingText = '即将到期' // 或者 '即将逾期'
    }
    return remainingText
  }
})

// 计算剩余时间（小时和分钟）
const getRemainingTime = (timestamp: number) => {
  if (!timestamp) return { hours: 0, minutes: 0 }
  const now = Date.now()
  const diff = timestamp - now
  if (diff <= 0) return { hours: 0, minutes: 0 }

  const totalMinutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(totalMinutes / 60)
  const minutes = totalMinutes % 60
  return { hours, minutes }
}

// 计算超出时间（小时和分钟）
const getOverdueTime = (timestamp: number) => {
  if (!timestamp) return { hours: 0, minutes: 0 }
  const now = Date.now()
  const diff = now - timestamp
  if (diff <= 0) return { hours: 0, minutes: 0 }

  const totalMinutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(totalMinutes / 60)
  const minutes = totalMinutes % 60
  return { hours, minutes }
}

// 获取发货时间线类型
const deliveryTimelineType = computed(() => {
  if (isDeliveryCompleted.value) {
    return isDeliveryOnTime.value ? 'success' : 'info' // info 代表已完成但逾期
  }
  return isDeliveryOverdue.value ? 'danger' : 'warning' // danger 代表未完成且逾期, warning 代表未完成且即将逾期
})

// 获取到货时间线类型
const arrivalTimelineType = computed(() => {
  if (isArrivalCompleted.value) {
    return 'success'
  }
  // danger 代表未完成且逾期, warning 代表未完成且即将逾期
  return isArrivalOverdue.value ? 'danger' : 'warning' 
})

// 计算是否显示时间间隔
const showTimeInterval = computed(() => {
  return !!props.expectLatestDeliverTime && 
         !!props.expectLatestArrivalTime && 
         props.expectLatestArrivalTime > props.expectLatestDeliverTime
})

// 计算发货到到货的时间间隔
const timeInterval = computed(() => {
  if (!props.expectLatestDeliverTime || !props.expectLatestArrivalTime) {
    return ''
  }
  
  const deliverTime = props.expectLatestDeliverTime
  const arrivalTime = props.expectLatestArrivalTime
  
  if (arrivalTime <= deliverTime) {
    return ''
  }
  
  const diffTime = arrivalTime - deliverTime
  const diffHours = Math.ceil(diffTime / (1000 * 60 * 60))
  
  return diffHours > 0 ? `${diffHours}h` : ''
})

</script>

<style scoped>
.custom-timeline-wrapper {
  position: relative;
  padding-left: 20px; /* 为节点圆圈和线条留出空间 */
  margin-top: 8px;
}

.timeline-node {
  position: relative;
  padding-bottom: 25px; /* 增加节点之间的基础间距，为间隔文本留空间 */
}

.delivery-node { 
  /* 特殊处理第一个节点，确保伪元素连接线有足够长度 */
  min-height: 40px; /* 根据实际内容调整 */
}

.timeline-node:last-child {
  padding-bottom: 0;
}

/* 节点圆圈 */
.node-circle {
  position: absolute;
  left: -15px; /* 调整位置以对齐 */
  top: 4px; /* 微调垂直对齐 */
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid;
  background-color: #fff; /* 实心节点内部颜色 */
  z-index: 1;
  box-sizing: border-box;
}

.node-circle.hollow {
  background-color: transparent; /* 空心节点 */
}

/* 根据类型设置节点边框和背景色 */
.node-type-success .node-circle { 
  border-color: var(--el-color-success); 
  background-color: var(--el-color-success); 
}
.node-type-warning .node-circle { 
  border-color: var(--el-color-warning); 
  background-color: var(--el-color-warning); 
}
.node-type-danger .node-circle { 
  border-color: var(--el-color-danger); 
  background-color: var(--el-color-danger); 
}
.node-type-info .node-circle { 
  border-color: var(--el-color-info); 
  background-color: var(--el-color-info); /* 逾期完成用 info 色 */
}
.node-type-primary .node-circle { 
  border-color: var(--el-color-primary); 
  background-color: var(--el-color-primary); /* 如果需要 primary 状态 */
}

/* 使用 ::after 伪元素绘制对勾 (仅对 success 和 info 状态) */
.node-type-success .node-circle::after,
.node-type-info .node-circle::after {
    content: '';
    position: absolute;
    top: 1px; /* 微调位置 */
    left: 3px; /* 微调位置 */
    width: 3px; /* 对勾线条宽度 */
    height: 6px; /* 对勾线条高度 */
    border: solid white; /* 对勾颜色 */
    border-width: 0 2px 2px 0; /* 对勾形状 */
    transform: rotate(45deg);
    box-sizing: border-box;
}

/* 节点内容 */
.node-content {
  font-size: 12px;
  line-height: 1.5;
  color: #606266;
  padding-left: 5px; /* 给内容留一点左边距 */
}
.status-tag {
  margin-top: 4px;
}

/* 连接线 - 使用伪元素 */
.timeline-node:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -9px; /* 圆圈中心 */
    top: 16px; /* 圆圈下方开始 */
    bottom: -15px; /* 连接到下一个节点的上方 (相对父节点.timeline-node) - 这个值可能需要基于下一个节点的位置动态调整，或者足够长 */
    /* 或者使用 height 计算 */
    height: calc(100% - 0px); /* 尝试填满父节点高度，减去不需要的部分 */
    width: 2px;
    background-color: #e4e7ed;
    z-index: 0;
}

/* 特殊调整第一个节点伪元素的高度，确保连接到第二个节点 */
.delivery-node::before {
   /* height: 100%; */ /* 或者根据第二个节点的 top 动态计算 */
   /* 这里的 bottom / height 比较难精确控制，取决于后续节点内容高度 */
   /* 暂时保持足够长 */
   bottom: -20px; /* 增加负值确保连接 */
}

/* 间隔时间文本 - 相对于第一个节点定位 */
.interval-text {
  position: absolute;
  left: -9px; /* 与线条左侧对齐 */
  /* top: 50%; */ /* 尝试更精确的定位 */
  top: calc(16px + (100% - 16px - 15px) / 2); /* 尝试在连接线可视部分的中间 */
  transform: translateX(-50%); /* 水平居中 */
  background-color: #fff; /* 改为白色背景更干净 */
  padding: 1px 4px;
  font-size: 10px;
  color: #909399;
  border-radius: 3px;
  z-index: 1;
  white-space: nowrap;
  border: 1px solid #e4e7ed; /* 添加边框 */
  line-height: 1.2; /* 调整行高 */
}

/* 针对最后一个节点，移除其下方的padding */
.custom-timeline-wrapper > .timeline-node:last-of-type {
    padding-bottom: 0;
}

</style> 