// 商品销售信息请求参数
export interface LocalSalesRequestParams {
  // 页码
  pageNo: number
  // 每页大小
  pageSize: number
  // 店铺ID列表
  shopIds?: number[]
  // 商品类目
  category?: string
  // SPU ID列表
  spuIdList?: number[]
  // SPU ID列表（字符串格式）
  spuIdListStr?: string
  // SKC ID列表
  skcIdList?: number[]
  // SKC ID列表（字符串格式）
  skcIdListStr?: string
  // 是否VMI
  isVmi?: boolean
  // 备货区域
  inventoryRegion?: number
  // 加入站点时长（天）
  onSitesDuration?: number
  // SKC型号/编码
  skcExtCode?: string
  // SKU规格号
  skuExtCode?: string
  // 商品名称关键词
  productNameKeyword?: string
  // 是否定制商品
  isCustomGoods?: boolean
  // 是否自动关闭JIT
  autoCloseJit?: boolean
  // JIT状态
  closeJitStatus?: number[]
  // 库存状态（1-库存充足，2-库存紧张，3-库存不足，4-缺货）
  stockStatus?: number
  // 是否热销款
  hasHotSku?: boolean
  // 库存天数最小值
  stockDaysMin?: number
  // 库存天数最大值
  stockDaysMax?: number
  // 备货仓组ID
  warehouseGroupId?: number
  // 商品评分
  productScore?: number
  // 近7天销量最小值
  lastSevenDaysSaleVolumeMin?: number
  // 近7天销量最大值
  lastSevenDaysSaleVolumeMax?: number
  // 近30天销量最小值
  lastThirtyDaysSaleVolumeMin?: number
  // 近30天销量最大值
  lastThirtyDaysSaleVolumeMax?: number
  // 销量查询的时间范围类型: 1-今日, 2-近7日, 3-近30天
  saleVolumeTimeRange?: number
  // 快速筛选类型
  quickFilter?: string
  
  // 高级查询参数
  // 查询开始时间
  startTime?: number
  // 查询结束时间
  endTime?: number
  // 商品状态列表
  selectStatusList?: number[]
  // 商品名称
  productName?: string
  // SKC货号列表
  skcExtCodeList?: string
  // SKU货号列表
  skuExtCodeList?: string
  // 最小加入站点时长
  onSalesDurationOfflineGte?: number
  // 最大加入站点时长
  onSalesDurationOfflineLte?: number
  // 库存区域列表
  inventoryRegionList?: number[]
  // 最小剩余库存数量
  minRemanentInventoryNum?: number
  // 最大剩余库存数量
  maxRemanentInventoryNum?: number
  // 最小可售天数
  minAvailableSaleDays?: number
  // 最大可售天数
  maxAvailableSaleDays?: number
  // 图片审核状态列表
  pictureAuditStatusList?: number[]
  // 供货状态列表
  supplyStatusList?: number[]
  // 是否缺货
  isLack?: boolean
  // 今日销量最小值
  todaySaleVolumMin?: number
  // 今日销量最大值
  todaySaleVolumMax?: number
  // 热销标签
  hotTag?: number
  // 备货类型
  purchaseStockType?: number
  // 建议关闭JIT
  suggestCloseJit?: boolean
  // 库存状态列表
  stockStatusList?: number[]
  // 可生产数量大于零
  availableProduceNumGreaterThanzero?: boolean
  // 结算类型
  settlementType?: number
  // SKU ID列表
  skuIdList?: string[]
  // 销量排序字段: 1-今日销量, 2-近7天销量, 3-近30天销量
  sortField?: number
  // 排序方向: asc-升序, desc-降序
  sortDirection?: string
}

// 商品销售信息响应
export interface LocalSalesResponse {
  // 结果列表
  list: SalesItemInfo[]
  // 总记录数
  total: number
  // 当前页码
  pageNo: number
  // 每页大小
  pageSize: number
}

// SKU信息
export interface SkuInfo {
  // 商品SKU ID
  productSkuId: number
  // SKU货号
  skuExtCode?: string
  // 尺码名称
  className?: string
  // 颜色
  color?: string
  // 尺码
  size?: string
  // 备货天数
  stockDays?: number
  // 今日销量
  todaySaleVolume?: number
  // 总销量
  totalSaleVolume?: number
  // 近7天销量
  lastSevenDaysSaleVolume?: number
  // 近30天销量
  lastThirtyDaysSaleVolume?: number
  // 用户加购数量
  inCartNumber?: number
  // 近7天用户加购数量
  inCartNumber7d?: number
  // 缺货数量
  lackQuantity?: number
}

// 库存信息
export interface InventoryInfo {
  // 商品SKU ID
  productSkuId: number
  // 仓库组ID
  warehouseGroupId?: number
  // 仓内库存
  warehouseInventoryNum?: number
  // 待上架库存
  waitOnShelfNum?: number
  // 待发货库存
  waitDeliveryInventoryNum?: number
  // 预期占用库存数量
  expectedOccupiedInventoryNum?: number
  // 待审核备货库存
  waitApproveInventoryNum?: number
  // 已上架待质检库存
  waitQcNum?: number
  // 仓内暂不可用库存
  unavailableWarehouseInventoryNum?: number
  // 待入库库存
  waitInStock?: number
  // 待收货库存
  waitReceiveNum?: number
}

// 仓库信息
export interface WarehouseInfo {
  // 商品SKU ID
  productSkuId: number
  // 仓库组ID
  warehouseGroupId?: number
  // 仓库组名称
  warehouseGroupName?: string
  // 备货天数
  stockDays?: number
  // 安全库存天数
  safeInventoryDays?: number
  // 采购配置
  purchaseConfig?: string
  // 今日销量
  todaySaleVolume?: number
  // 总销量
  totalSaleVolume?: number
  // 近7天销量
  lastSevenDaysSaleVolume?: number
  // 近30天销量
  lastThirtyDaysSaleVolume?: number
  // 缺货数量
  lackQuantity?: number
  // 建议下单量
  adviceQuantity?: number
  // 可售天数
  availableSaleDays?: string
  // 库存可售天数
  availableSaleDaysFromInventory?: string
  // 仓内库存可售天数
  warehouseAvailableSaleDays?: string
  // 7日销量参考
  sevenDaysSaleReference?: string
  // 七日销量参考类型 1.7日最大销量 2.7日日均销量
  sevenDaysReferenceSaleType?: number
}

// 商品销售信息项
export interface SalesItemInfo {
  // 主键ID
  id: number
  // 店铺ID
  shopId: number
  // 店铺名称
  shopName: string
  // 商品ID
  productId: number
  // 商品SKC ID
  productSkcId: number
  // 商品SKU ID
  productSkuId: number
  // 商品名称
  productName: string
  // SKC外部编码
  skcExtCode: string
  // SKU货号
  skuExtCode: string
  // 商品类目
  category: string
  // 商品SKC图片
  productSkcPicture: string
  // 是否定制商品
  isCustomGoods: boolean
  // 库存区域
  inventoryRegion: number
  // 库存区域名称
  inventoryRegionName: string
  // 销售时长(线下)
  onSalesDurationOffline: number
  // 自动关闭JIT
  autoCloseJit: boolean
  // JIT关闭状态
  closeJitStatus: number
  // JIT关闭状态名称
  closeJitStatusName: string
  // 是否有热销SKU
  hasHotSku: boolean
  // 库存是否充足
  isEnoughStock: boolean
  // 图片审核状态 1-未完成；2-已完成
  pictureAuditStatus?: number
  // 尺码名称
  className: string
  // 仓库组名称
  warehouseGroupName: string
  // 备货天数
  stockDays: number
  // 今日销量
  todaySaleVolume: number
  // 总销量
  totalSaleVolume: number
  // 近7天销量
  lastSevenDaysSaleVolume: number
  // 近30天销量
  lastThirtyDaysSaleVolume: number
  // 用户加购数量
  inCartNumber: number
  // 近7天用户加购数量
  inCartNumber7d: number
  // 缺货数量
  lackQuantity: number
  // 仓内库存
  warehouseInventoryNum: number
  // 待上架库存
  waitOnShelfNum: number
  // 待发货库存
  waitDeliveryInventoryNum: number
  // 同步时间
  syncTime: string
  // SKU信息列表
  skuList?: SkuInfo[]
  // 库存信息列表
  inventoryList?: InventoryInfo[]
  // 仓库信息列表
  warehouseList?: WarehouseInfo[]
} 