import router from './router'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { useUserStore, usePermissionStore } from '@/store'
import { ElMessage } from 'element-plus'

NProgress.configure({ showSpinner: false }) // NProgress配置

const whiteList = ['/login'] // 白名单路由，可以不用登录访问
// 定义不需要动态权限的路由页面
const staticPaths = ['/dashboard', '/', '/profile', '/redirect', '/404'] 

// 设置重试加载用户信息的最大次数和重试间隔
const MAX_USER_INFO_RETRY = 3
const RETRY_DELAY = 1000
let userInfoRetryCount = 0

// 添加重试延迟函数
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 标记当前是否有正在进行的路由加载
let isRouteLoading = false
let pendingNavigation: any = null

// 路由跳转前处理
router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start()

  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - TEMU管理系统` : 'TEMU管理系统'

  // 获取token
  const hasToken = getToken()
  
  // 检查登录和菜单加载状态
  const userStore = useUserStore()
  const permissionStore = usePermissionStore()
  const hasLoadedUserInfo = userStore.name !== ''
  const hasLoadedMenus = permissionStore.routes && permissionStore.routes.length > 0

  // 如果当前已有路由加载过程，则放入队列
  if (isRouteLoading && !whiteList.includes(to.path)) {
    console.log('路由加载中，暂存导航请求:', to.path)
    pendingNavigation = { to, from, next }
    return
  }

  try {
    // 路由切换时的逻辑流程
    if (to.path === '/login') {
      // 已登录访问登录页，直接跳转到首页
      if (hasToken) {
        next({ path: '/' })
        NProgress.done()
      } else {
        next()
      }
    } else if (hasToken) {
      // 对于其他路由，如果已经登录（有token）
      const isStaticPath = staticPaths.some(path => to.path.startsWith(path))
      
        if (hasLoadedUserInfo) {
        if (hasLoadedMenus || isStaticPath) {
          // 用户信息和菜单都已加载，或访问静态路由，直接放行
          if (to.matched.length === 0) {
            // 如果没有匹配到路由，跳转到404
            next('/404')
          } else {
            next()
          }
        } else {
          // 需要加载菜单
          try {
            isRouteLoading = true
            
            // 生成路由并添加到router
            const roles = userStore.roles || []
            const accessRoutes = await permissionStore.generateRoutes(roles)
            
            // 添加动态路由
            for (const route of accessRoutes) {
              router.addRoute(route)
            }
            
            isRouteLoading = false
            
            // 重新push当前路由，确保匹配到新添加的路由
            next({ ...to, replace: true })
          } catch (error) {
            console.error('加载菜单失败:', error)
            isRouteLoading = false
            next('/login')
          }
        }
      } else {
        // 用户信息未加载，先加载用户信息
        try {
          isRouteLoading = true
          
          // 请求获取用户信息
          const { roles } = await userStore.getInfo()
          
          // 根据用户角色，动态生成可访问路由
          const accessRoutes = await permissionStore.generateRoutes(roles)
          
          // 动态添加可访问路由
          for (const route of accessRoutes) {
            router.addRoute(route)
          }
          
          // 重置重试计数
          userInfoRetryCount = 0
          
          isRouteLoading = false
          
          // 检查是否有挂起的导航
          if (pendingNavigation) {
            const { to: pendingTo, next: pendingNext } = pendingNavigation
            pendingNavigation = null
            console.log('处理挂起的导航请求:', pendingTo.path)
            pendingNext({ ...pendingTo, replace: true })
            return
          }
          
          // 重新导航到目标地址，确保路由表已更新
            next({ ...to, replace: true })
        } catch (error) {
          console.error('加载用户信息或路由失败:', error)
          
          // 如果未超过最大重试次数，则进行重试
          if (userInfoRetryCount < MAX_USER_INFO_RETRY) {
            userInfoRetryCount++
            console.log(`重试加载用户信息 (${userInfoRetryCount}/${MAX_USER_INFO_RETRY})`)
            
            // 等待一段时间后重试
            await delay(RETRY_DELAY * userInfoRetryCount)
            
            // 标记路由加载完成，以便下次重试
            isRouteLoading = false
            
            // 重新触发当前路由的导航
            next(to.fullPath)
          } else {
            // 超过重试次数，重置状态
            console.error('重试加载用户信息失败，跳转到登录页')
            userStore.resetToken()
            isRouteLoading = false
            next('/login')
            NProgress.done()
          }
        }
      }
    } else {
      // 没有登录，检查是否是白名单
      if (whiteList.includes(to.path)) {
        // 白名单路由，直接访问
        next()
      } else {
        // 非白名单页面，跳转到登录页
        next(`/login?redirect=${to.path}`)
        NProgress.done()
      }
    }
  } catch (error) {
    // 全局错误捕获
    console.error('路由导航发生未处理的错误:', error)
    isRouteLoading = false
    next('/login')
    NProgress.done()
  }
})

// 路由跳转后处理
router.afterEach(() => {
  // 结束进度条
  NProgress.done()
})