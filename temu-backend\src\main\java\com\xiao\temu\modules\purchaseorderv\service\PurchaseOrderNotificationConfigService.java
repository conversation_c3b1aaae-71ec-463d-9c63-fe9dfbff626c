package com.xiao.temu.modules.purchaseorderv.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xiao.temu.modules.purchaseorderv.dto.NotificationMatchDTO;
import com.xiao.temu.modules.purchaseorderv.dto.NotificationQueryDTO;
import com.xiao.temu.modules.purchaseorderv.dto.NotificationTriggerDTO;
import com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderNotificationConfig;
import com.xiao.temu.modules.purchaseorderv.vo.NotificationConfigVO;
import com.xiao.temu.modules.purchaseorderv.vo.NotificationMatchOrderVO;
import com.xiao.temu.modules.purchaseorderv.vo.NotificationRecordVO;

import java.util.List;

/**
 * 备货单通知配置服务接口
 */
public interface PurchaseOrderNotificationConfigService {
    
    /**
     * 获取所有通知配置
     * 
     * @return 通知配置列表
     */
    List<NotificationConfigVO> getAllNotificationConfigs();
    
    /**
     * 根据ID获取通知配置
     * 
     * @param id 配置ID
     * @return 通知配置
     */
    NotificationConfigVO getNotificationConfigById(Long id);
    
    /**
     * 根据通知类型获取配置
     * 
     * @param notificationType 通知类型
     * @return 通知配置
     */
    PurchaseOrderNotificationConfig getConfigByType(Integer notificationType);
    
    /**
     * 更新通知配置
     * 
     * @param config 通知配置
     * @return 是否成功
     */
    boolean updateNotificationConfig(PurchaseOrderNotificationConfig config);
    
    /**
     * 启用/禁用通知配置
     * 
     * @param id 配置ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    boolean toggleNotificationConfig(Long id, Boolean enabled);
    
    /**
     * 获取通知记录列表
     * 
     * @param queryDTO 查询条件
     * @return 通知记录分页数据
     */
    IPage<NotificationRecordVO> getNotificationRecords(NotificationQueryDTO queryDTO);
    
    /**
     * 预览符合条件的备货单
     * 
     * @param matchDTO 匹配条件
     * @return 备货单列表
     */
    IPage<NotificationMatchOrderVO> previewMatchOrders(NotificationMatchDTO matchDTO);
    
    /**
     * 手动触发通知
     * 
     * @param triggerDTO 触发参数
     * @return 成功触发的订单数量
     */
    int manualTriggerNotification(NotificationTriggerDTO triggerDTO);
}