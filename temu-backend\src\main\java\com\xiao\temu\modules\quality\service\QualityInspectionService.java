package com.xiao.temu.modules.quality.service;

import com.xiao.temu.modules.quality.dto.QualityInspectionRequestDTO;
import com.xiao.temu.modules.quality.vo.QualityInspectionVO;

/**
 * 抽检结果明细服务接口
 */
public interface QualityInspectionService {
    
    /**
     * 获取抽检结果明细列表
     * 
     * @param requestDTO 请求参数
     * @param userId 用户ID
     * @return 抽检结果明细VO
     */
    QualityInspectionVO getQualityInspectionList(QualityInspectionRequestDTO requestDTO, Long userId);
} 