package com.xiao.temu.security.utils;

import com.xiao.temu.modules.system.mapper.SysRoleMapper;
import com.xiao.temu.modules.system.mapper.SysUserRoleMapper;
import com.xiao.temu.security.model.UserDetailsImpl;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 安全工具类
 */
@Slf4j
@Component
public class SecurityUtils {

    private static SysRoleMapper staticRoleMapper;
    private static SysUserRoleMapper staticUserRoleMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @PostConstruct
    public void init() {
        staticRoleMapper = roleMapper;
        staticUserRoleMapper = userRoleMapper;
    }

    /**
     * 获取当前登录用户ID
     *
     * @return 用户ID
     */
    public static Long getCurrentUserId() {
        UserDetails userDetails = getCurrentUserDetails();
        if (userDetails == null) {
            return null;
        }
        
        if (userDetails instanceof UserDetailsImpl) {
            return ((UserDetailsImpl) userDetails).getUserId();
        } else {
            log.warn("无法识别的UserDetails实现类: {}", userDetails.getClass().getName());
            return null;
        }
    }
    
    /**
     * 获取当前登录的UserDetails对象
     *
     * @return UserDetails对象
     */
    public static UserDetails getCurrentUserDetails() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserDetails) {
            return (UserDetails) authentication.getPrincipal();
        }
        return null;
    }
    
    /**
     * 获取当前登录用户名
     *
     * @return 用户名
     */
    public static String getCurrentUsername() {
        UserDetails userDetails = getCurrentUserDetails();
        return userDetails != null ? userDetails.getUsername() : "";
    }
    
    /**
     * 获取当前用户的角色列表
     *
     * @return 角色列表
     */
    public static List<String> getCurrentUserRoles() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getAuthorities() != null) {
            List<String> authorities = authentication.getAuthorities().stream()
                    .map(authority -> authority.getAuthority())
                    .collect(Collectors.toList());
            
            log.debug("当前用户 '{}' 的权限列表: {}", 
                     authentication.getName(), authorities);
            
            return authorities;
        }
        return null;
    }

    /**
     * 判断当前用户是否为超级管理员
     *
     * @return 是否为超级管理员
     */
    public static boolean isAdmin() {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        

        
        // 从认证信息中获取权限列表
        List<String> authorities = getCurrentUserRoles();
        if (authorities == null || authorities.isEmpty()) {
            log.debug("未找到当前用户的权限列表");
            return false;
        }
        

        // 从数据库查询用户角色
        boolean hasAdminRole = hasAdminRoleByUserId(userId);
        
        log.debug("当前用户是否为管理员(通过角色key判断): {}", hasAdminRole);
        return hasAdminRole;
    }
    
    /**
     * 根据用户ID判断是否拥有管理员角色
     * 
     * @param userId 用户ID
     * @return 是否拥有管理员角色
     */
    private static boolean hasAdminRoleByUserId(Long userId) {
        if (userId == null) {
            return false;
        }
        
        try {
            // 检查静态变量是否已初始化
            if (staticUserRoleMapper == null || staticRoleMapper == null) {
                log.error("静态Mapper尚未初始化，无法检查用户角色");
                // 临时回退到用户ID判断
                return userId == 1;
            }
            
            // 获取用户的角色ID列表
            List<Long> roleIds = staticUserRoleMapper.selectRoleIdsByUserId(userId);
            if (roleIds == null || roleIds.isEmpty()) {
                return false;
            }
            
            // 检查是否包含role_key为admin的角色
            for (Long roleId : roleIds) {
                com.xiao.temu.modules.system.entity.SysRole role = staticRoleMapper.selectById(roleId);
                if (role != null && "admin".equals(role.getRoleKey())) {
                    return true;
                }
            }
            
            return false;
        } catch (Exception e) {
            log.error("检查用户是否为管理员时发生异常", e);
            // 发生异常时，回退到用户ID判断
            return userId == 1;
        }
    }
    
    /**
     * 判断当前用户是否拥有指定角色
     *
     * @param role 角色标识
     * @return 是否拥有角色
     */
    public static boolean hasRole(String role) {
        // 检查角色列表中是否包含指定角色
        List<String> roles = getCurrentUserRoles();
        return roles != null && roles.contains(role);
    }
} 