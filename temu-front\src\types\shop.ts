export interface Shop {
  shopId: number | null;
  shopName: string;
  shopTemuId: string;
  groupId?: number | null;
  groupName?: string;
  groups?: OperationGroup[];
  apiKey: string;
  apiSecret: string;
  accessToken: string | null;
  status: string;
  createTime: string | null;
  updateTime: string | null;
  remark: string | null;
}

export interface OperationGroup {
  groupId: number;
  groupName: string;
  status: string;
}

export interface ShopQuery {
  pageNum: number;
  pageSize: number;
  shopName?: string;
  shopTemuId?: string;
  groupId?: number | null;
  status?: string;
  orderByColumn?: string;
  orderDirection?: string;
}

export interface ShopPageResult {
  total: number;
  pageNum: number;
  pageSize: number;
  list: Shop[];
} 