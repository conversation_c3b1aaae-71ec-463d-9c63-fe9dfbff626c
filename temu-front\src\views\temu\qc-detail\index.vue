<template>
  <AppLayout>
    <!-- 搜索区域 -->
    <template #search>
      <SearchCard>
        <div class="search-container">
          <!-- 第一行 -->
          <div class="search-row">
            <!-- 店铺 -->
            <div class="search-item">
              <div class="search-label">店铺</div>
              <el-select 
                v-model="queryParams.shopId" 
                placeholder="请选择店铺" 
                clearable 
                @change="handleShopChange" 
                
                class="search-input"
              >
                <el-option 
                  v-for="shop in shops" 
                  :key="shop.shopId" 
                  :label="shop.shopName" 
                  :value="shop.shopId"
                />
              </el-select>
            </div>
            
            <!-- 商品ID查询 -->
            <div class="search-item">
              <div class="search-label">商品ID查询</div>
              <TagInput
                :model-value="idType === 'sku' ? queryParams.skuIdList : queryParams.skcIdList"
                @update:model-value="val => idType === 'sku' ? queryParams.skuIdList = val : queryParams.skcIdList = val"
                :is-numeric="true"
                :show-type-select="true"
                v-model:type-value="idType"
                :type-options="[
                  { label: 'SKU', value: 'sku' },
                  { label: 'SKC', value: 'skc' }
                ]"
                placeholder="多个查询请空格或逗号等依次输入"
                size="small"
                class="search-input"
              />
            </div>
            
            <!-- 备货单号 -->
            <div class="search-item">
              <div class="search-label">备货单号</div>
              <TagInput
                v-model="queryParams.purchaseNo"
                placeholder="多个以空格，逗号等分隔"
                :is-numeric="false"
                size="small"
                class="search-input"
              />
            </div>
            
            <!-- 最新抽检时间 -->
            <div class="search-item">
              <div class="search-label">最新抽检时间</div>
              <el-date-picker
                v-model="timeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD"
                @change="handleTimeRangeChange"
                size="small"
                class="search-input date-picker-input"
              />
            </div>
          </div>
          
          <!-- 第二行 - 只有按钮 -->
          <div class="search-row">
            <!-- 空白项 -->
            <div class="search-item"></div>
            <div class="search-item"></div>
            <div class="search-item"></div>
            
            <!-- 按钮 -->
            <div class="search-item buttons-container">
              <div class="search-buttons">
                <el-button type="primary" @click="handleQuery" size="small" class="action-button">
                  查询
                </el-button>
                <el-button @click="resetQuery" size="small" class="action-button">
                  重置
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </SearchCard>
    </template>

    <!-- 表格内容区域 -->
    <template #table>
      <TableCard>
        <!-- 添加标签页 -->
        <template #tabs>
          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane label="抽检不合格" name="failed"></el-tab-pane>
            <el-tab-pane label="抽检完成" name="completed"></el-tab-pane>
          </el-tabs>
        </template>
        
        <!-- 表格容器 -->
        <el-table
          v-loading="loading"
          :data="qualityInspectionList"
          border
          style="width: 100%;"
          size="small"
          class="compact-table"
        >
          <template #empty>
            <EmptyTips message="请选择店铺后点击搜索按钮查询数据" />
          </template>
          <!-- 商品信息列 -->
          <el-table-column prop="skuName" label="商品信息" min-width="300">
            <template #default="scope">
              <div class="product-info">
                <img 
                  :src="scope.row.thumbUrl" 
                  class="product-image" 
                  alt="商品图片"
                  @click="handlePreviewImage(scope.row.thumbUrl)"
                >
                <div class="product-details">
                  <div class="product-name">{{ scope.row.skuName }}</div>
                  <div class="product-category">类目：{{ scope.row.catName }}</div>
                  <div class="product-id">
                    <div>SPU ID：{{ scope.row.spuId }}</div>
                    <div>SKC ID：{{ scope.row.productSkcId }}</div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <!-- SKU信息列 -->
          <el-table-column label="SKU信息" min-width="180">
            <template #default="scope">
              <div class="sku-info">
                <div class="sku-attribute">属性：{{ scope.row.spec }}</div>
                <div class="sku-id">SKU ID：{{ scope.row.productSkuId }}</div>
              </div>
            </template>
          </el-table-column>
          
          <!-- 备货单号列 -->
          <el-table-column prop="purchaseNo" label="备货单号" min-width="150" />
          
          <!-- 最新抽检时间列 -->
          <el-table-column label="最新抽检时间" min-width="180">
            <template #default="scope">
              {{ formatTime(scope.row.qcResultUpdateTime, 'YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          
          <!-- 操作列 -->
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button 
                type="primary" 
                size="small" 
                link
                @click="viewQcDetail(scope.row.qcBillId)"
              >
                查看抽检记录
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </TableCard>
    </template>
    
    <!-- 固定在底部的分页区域 -->
    <template #pagination>
      <PaginationBar
        v-model:current-page="queryParams.pageNo"
        v-model:page-size="queryParams.pageSize"
        :total="total"
        :show-limit-tip="true"
        :max-limit="10000"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        size="small"
      />
    </template>
  
    <!-- 弹窗区域 -->
    <template #dialogs>
      <ImagePreview
        v-model:visible="imagePreviewVisible"
        :image-url="currentPreviewImage"
      />
    </template>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, onUnmounted, nextTick } from 'vue'
import type { Ref } from 'vue'
import { ElMessage } from 'element-plus'
import type { Shop } from '@/types/refund'
import { formatDateTime, formatTime } from '@/utils/format'
import { useQualityInspectionStore } from '@/store/modules/qcInspection'
import { fetchUserAccessibleShops } from '@/utils/shop-helper'
import type { 
  QualityInspectionRequestParams, 
  QualityInspectionDetail,
  QualityInspectionResponse 
} from '@/types/qcInspection'
import { 
  AppLayout, 
  SearchCard, 
  TableCard, 
  PaginationBar, 
  ImagePreview, 
  EmptyTips,
  TagInput
} from '@/components/temu'

// 抽检结果store
const qcStore = useQualityInspectionStore()

// 加载状态
const loading = computed(() => qcStore.loading)

// 店铺列表
const shops = ref<Shop[]>([])

// 图片预览相关
const imagePreviewVisible = ref(false)
const currentPreviewImage = ref('')

// 多值输入相关
const idType = ref('sku')

// 抽检结果列表
const qualityInspectionList = ref<QualityInspectionDetail[]>([])

// 总记录数
const total = ref(0)

// 时间范围，默认为最近7天
const timeRange = ref<[string, string] | null>(null)

// 计算时间戳
const getTimestamp = (dateStr: string): number => {
  return new Date(dateStr).getTime()
}

// 查询参数
const queryParams = reactive<QualityInspectionRequestParams>({
  shopId: undefined,
  qcResultUpdateTimeBegin: undefined,
  qcResultUpdateTimeEnd: undefined,
  pageNo: 1,
  pageSize: 10,
  skuIdList: [],
  skcIdList: [],
  purchaseNo: [],
  skuQcResult: 2 // 默认查询抽检不合格的记录
})

// 标签页相关
const activeTab = ref('failed')

// 初始化
onMounted(() => {
  // 只加载店铺列表，不自动加载抽检结果列表
  loadShops()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  // 组件销毁时移除监听器
  window.removeEventListener('resize', handleResize)
})

// 处理窗口大小变化
const handleResize = () => {
  // 窗口大小变化时的处理逻辑
}

// 加载店铺列表
const loadShops = async () => {
  shops.value = await fetchUserAccessibleShops()
}

// 加载抽检结果列表
const loadQualityInspectionList = async () => {
  // 验证分页参数，不允许查询超过10000条数据
  const totalRequested = queryParams.pageNo * queryParams.pageSize
  if (totalRequested > 10000) {
    ElMessage.warning('只能查询前10000条数据')
    return
  }
  
  const result = await qcStore.getQualityInspectionList(queryParams)
  
  if (result && result.success) {
    // 解析后端返回的数据结构
    if (result.result) {
      // 使用类型断言来解决类型问题
      const resultData = result.result as any;
      if (resultData.skuList) {
        qualityInspectionList.value = resultData.skuList
        total.value = resultData.total || 0
      } else if (Array.isArray(result.result)) {
        // 兼容直接返回数组的情况
        qualityInspectionList.value = result.result
        total.value = result.result.length || 0
      } else {
        qualityInspectionList.value = []
        total.value = 0
      }
    } else {
      qualityInspectionList.value = []
      total.value = 0
    }
  } else {
    qualityInspectionList.value = []
    total.value = 0
    
    // 显示错误消息
    if (result && result.errorMsg) {
      ElMessage.error(result.errorMsg)
    } else {
      ElMessage.error('获取抽检结果明细失败')
    }
  }
}

// 处理查询
const handleQuery = () => {
  // 验证必须选择店铺
  if (!queryParams.shopId) {
    ElMessage.warning('请选择店铺')
    return
  }

  // 更新时间范围的时间戳
  if (timeRange.value) {
    queryParams.qcResultUpdateTimeBegin = getTimestamp(timeRange.value[0])
    queryParams.qcResultUpdateTimeEnd = getTimestamp(timeRange.value[1])
  } else {
    // 如果没有选择时间范围，则不设置时间过滤条件
    queryParams.qcResultUpdateTimeBegin = undefined
    queryParams.qcResultUpdateTimeEnd = undefined
  }

  queryParams.pageNo = 1
  qcStore.setShopId(queryParams.shopId)
  loadQualityInspectionList()
}

// 重置查询
const resetQuery = () => {
  queryParams.shopId = undefined
  
  // 清空时间范围
  timeRange.value = null
  queryParams.qcResultUpdateTimeBegin = undefined
  queryParams.qcResultUpdateTimeEnd = undefined
  
  // 重置多值输入
  queryParams.skuIdList = []
  queryParams.skcIdList = []
  queryParams.purchaseNo = []
  idType.value = 'sku'
  
  // 保留当前标签页对应的查询条件
  if (activeTab.value === 'completed') {
    queryParams.skuQcResult = 1
  } else if (activeTab.value === 'failed') {
    queryParams.skuQcResult = 2
  }
  
  queryParams.pageNo = 1
  
  // 重置store中的查询条件
  qcStore.resetQuery()
  
  // 清空当前数据，不重新加载
  qualityInspectionList.value = []
  total.value = 0
}

// 处理时间范围变化
const handleTimeRangeChange = (val: [string, string] | null) => {
  if (val) {
    timeRange.value = val
    queryParams.qcResultUpdateTimeBegin = getTimestamp(val[0])
    queryParams.qcResultUpdateTimeEnd = getTimestamp(val[1])
    qcStore.setTimeRange(val)
  } else {
    // 如果手动清除，清空时间范围
    timeRange.value = null
    queryParams.qcResultUpdateTimeBegin = undefined
    queryParams.qcResultUpdateTimeEnd = undefined
    qcStore.setTimeRange(null)
  }
}

// 处理每页显示数量变化
const handleSizeChange = (size: number) => {
  // 检查是否会超出10000条数据限制
  if (queryParams.pageNo * size > 10000) {
    // 如果会超出限制，调整pageNo使其不超过限制
    const maxPageNo = Math.floor(10000 / size)
    queryParams.pageNo = maxPageNo
    ElMessage.warning(`每页${size}条时，最多只能查看到第${maxPageNo}页（共10000条数据）`)
  }
  
  queryParams.pageSize = size
  loadQualityInspectionList()
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  // 检查是否会超出10000条数据限制
  if (page * queryParams.pageSize > 10000) {
    ElMessage.warning('只能查询前10000条数据')
    // 不更新页码，保持在当前页
    return
  }
  
  queryParams.pageNo = page
  loadQualityInspectionList()
}

// 处理店铺选择
const handleShopChange = (shopId: number | undefined) => {
  qcStore.setShopId(shopId || null)
  console.log('选择店铺:', shopId)
}

// 获取质检结果类型
const getQcResultType = (result: number | undefined) => {
  if (result === undefined) return 'info'
  
  // 根据不同状态返回不同的标签类型
  switch (result) {
    case 1: return 'success'
    case 2: return 'danger'
    default: return 'info'
  }
}

// 获取质检结果文本
const getQcResultText = (result: number | undefined) => {
  if (result === undefined) return '未知'
  
  // 根据不同状态返回不同的文本
  switch (result) {
    case 1: return '合格'
    case 2: return '不合格'
    default: return '未知'
  }
}

// 处理图片预览
const handlePreviewImage = (imageUrl: string) => {
  currentPreviewImage.value = imageUrl
  imagePreviewVisible.value = true
}

// 查看抽检记录
const viewQcDetail = (qcBillId: number) => {
  // 实现查看抽检记录的逻辑
  ElMessage.info(`正在查看抽检记录，抽检单号：${qcBillId}`)
  // 这里可以添加跳转到抽检记录详情页的逻辑
  // 例如：router.push(`/temu/qc-detail/record/${qcBillId}`)
}

// 处理标签页切换
const handleTabClick = (tab: any) => {
  if (tab.props.name === 'completed') {
    queryParams.skuQcResult = 1 // 合格
  } else if (tab.props.name === 'failed') {
    queryParams.skuQcResult = 2 // 不合格
  }
  // 如果有选中店铺，自动刷新数据
  if (queryParams.shopId) {
    loadQualityInspectionList()
  }
}
</script>

<style scoped>
/* 改为行布局样式 */
.search-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 0 5px;
}

.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.search-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 250px;
  max-width: calc(25% - 12px);
  margin-bottom: 5px;
}

.search-label {
  width: 90px;
  text-align: left;
  margin-bottom: 8px;
  color: #606266;
  font-size: 14px;
}

.search-input {
  width: 100%;
}

/* 按钮容器样式 */
.buttons-container {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

.search-buttons {
  display: flex;
  gap: 10px;
}

.action-button {
  min-width: 80px; 
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 确保日期选择器宽度保持一致 */
.date-picker-input {
  width: 100% !important;
}

.date-picker-input :deep(.el-input__wrapper) {
  width: 100%;
}

/* 优化TagInput样式，确保它在SearchCard中正确显示 */
:deep(.tag-input-wrapper) {
  width: 100%;
  height: auto; /* 允许自动高度 */
}

:deep(.tag-input-wrapper .input-container) {
  min-height: 32px; /* 最小高度与其他输入框一致 */
  height: auto; /* 允许高度自动增长 */
  box-sizing: border-box;
  overflow: visible; /* 允许内容溢出 */
}

:deep(.tag-input-wrapper .tags-container) {
  margin-bottom: 5px;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

:deep(.tag-input-wrapper .tag-main-input) {
  width: 100%;
  min-height: 22px; /* 确保输入框有足够的高度 */
}

/* 表格相关样式 */
.product-info {
  display: flex;
  align-items: flex-start;
  padding: 5px 0;
}

.product-image {
  width: 60px;
  height: 60px;
  margin-right: 15px;
  object-fit: contain;
  cursor: pointer;
  transition: opacity 0.3s;
  border: 1px solid #eee;
}

.product-image:hover {
  opacity: 0.8;
}

.product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.product-name {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-category {
  font-size: 12px;
  color: #909399;
}

.product-id {
  font-size: 12px;
  color: #909399;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.sku-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding: 5px 0;
}

.sku-attribute {
  font-size: 14px;
  font-weight: 500;
}

.sku-id {
  font-size: 12px;
  color: #909399;
}

/* 添加紧凑表格样式 */
.compact-table {
  font-size: 12px;
}

.compact-table :deep(.el-table__cell) {
  padding: 6px 0;
}

.compact-table :deep(.cell) {
  line-height: 1.3;
}
</style> 