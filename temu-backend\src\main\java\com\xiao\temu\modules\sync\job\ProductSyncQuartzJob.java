package com.xiao.temu.modules.sync.job;

import com.xiao.temu.modules.sync.service.ProductSyncService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

/**
 * 商品数据同步Quartz任务
 */
@Component
@Slf4j
public class ProductSyncQuartzJob extends QuartzJobBean {

    @Autowired
    private ProductSyncService productSyncService;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        log.info("开始执行商品数据同步Quartz任务... 触发时间: {}", context.getFireTime());
        try {
            String result = productSyncService.executeScheduledSync();
            log.info("商品数据同步Quartz任务执行结果: {}", result);
        } catch (Exception e) {
            log.error("商品数据同步Quartz任务执行异常", e);
            throw new JobExecutionException(e);
        }
    }
} 