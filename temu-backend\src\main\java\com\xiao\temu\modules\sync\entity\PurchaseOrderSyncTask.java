package com.xiao.temu.modules.sync.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 备货单数据同步任务实体类
 */
@Data
@TableName("purchase_order_sync_task")
public class PurchaseOrderSyncTask {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 上次同步时间
     */
    private Date lastSyncTime;
    
    /**
     * 数据最新更新时间
     */
    private Date lastUpdateTime;
    
    /**
     * 同步状态：0-未同步，1-同步中，2-同步成功，3-同步失败
     */
    private Integer syncStatus;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 普通备货单总记录数
     */
    private Integer normalOrderCount;
    
    /**
     * JIT备货单总记录数
     */
    private Integer jitOrderCount;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 