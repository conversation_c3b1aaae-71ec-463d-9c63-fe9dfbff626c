/**
 * 路由组件导入工具
 * 解决Vite不支持多层级动态导入问题
 */

// 导入所有视图组件
const modules = import.meta.glob('../views/**/*.vue')

// 调试用 - 打印所有可用的模块路径
console.log('可用的视图组件路径:', Object.keys(modules))

/**
 * 规范化组件路径
 * @param componentPath 原始组件路径
 * @returns 规范化后的路径
 */
function normalizePath(componentPath: string): string {
  if (!componentPath) {
    return ''
  }
  
  // 移除开头的斜杠
  return componentPath.startsWith('/') 
    ? componentPath.substring(1) 
    : componentPath
}

/**
 * 获取组件导入函数
 * @param componentPath 组件路径，如 "system/user/index"
 * @returns 组件导入函数
 */
export function getViewComponent(componentPath: string) {
  // 特殊处理Layout组件
  if (componentPath === 'Layout' || !componentPath) {
    console.log('加载Layout组件')
    return () => import('@/layout/index.vue')
  }
  
  // 规范化路径
  const normalizedPath = normalizePath(componentPath)
  console.log('规范化后的组件路径:', normalizedPath)
  
  // 构建可能的视图路径
  const possiblePaths = [
    `/src/views/${normalizedPath}.vue`,  // 绝对路径
    `../views/${normalizedPath}.vue`,    // 相对路径
  ]
  
  // 检查路径是否存在于预加载的模块中
  for (const path of possiblePaths) {
    if (modules[path]) {
      console.log('在预加载模块中找到组件:', path)
      return modules[path]
    }
  }
  
  // 如果找不到预加载路径，使用硬编码方式导入组件
  console.log('在预加载模块中未找到，尝试硬编码导入:', normalizedPath)
  
  // 针对常见组件的硬编码映射
  const componentMap: Record<string, any> = {
    // 系统管理
    'system/user/index': () => import('@/views/system/user/index.vue'),
    'system/role/index': () => import('@/views/system/role/index.vue'),
    'system/menu/index': () => import('@/views/system/menu/index.vue'),
    
    // 运营管理
    'operation/group/index': () => import('@/views/operation/group/index.vue'),
    'operation/shop/index': () => import('@/views/operation/shop/index.vue'),
    'operation/task/index': () => import('@/views/operation/task/index.vue'),
    'operation/leader-assign/index': () => import('@/views/operation/leader-assign/index.vue'),
    
    // 监控模块
    'monitor/operlog/index': () => import('@/views/monitor/operlog/index.vue'),
    'monitor/apilog/index': () => import('@/views/monitor/apilog/index.vue'),
    
    // 消息中心
    'message/my-message/index': () => import('@/views/message/my-message/index.vue'),
    'message/send-message/index': () => import('@/views/message/send-message/index.vue'),
    'message/message-template/index': () => import('@/views/message/message-template/index.vue'),
    
    // TEMU平台接口
    'temu/returnDetails/index': () => import('@/views/temu/returnDetails/index.vue'),
    'temu/qc-detail/index': () => import('@/views/temu/qc-detail/index.vue'),
    'temu/sales-data/index': () => import('@/views/temu/sales-data/index.vue'),
    
    // 本地数据
    'local/qc-detail/index': () => import('@/views/local/qc-detail/index.vue'),
    'local/refund-detail/index': () => import('@/views/local/refund-detail/index.vue'),
    'local/violation/index': () => import('@/views/local/violation/index.vue'),
    'local/violation-inspection/index': () => import('@/views/local/violation-inspection/index.vue'),
    'local/sales-data/index': () => import('@/views/local/sales-data/index.vue'),
    
    // 数据同步
    'synchronous/quality-inspection-sync/index': () => import('@/views/synchronous/quality-inspection-sync/index.vue'),
    'synchronous/product-sync/index': () => import('@/views/synchronous/product-sync/index.vue'),
    'synchronous/refund-package-sync/index': () => import('@/views/synchronous/refund-package-sync/index.vue'),
    'synchronous/sales-sync/index': () => import('@/views/synchronous/sales-sync/index.vue'),
    'synchronous/purchase-order-sync/index': () => import('@/views/synchronous/purchase-order-sync/index.vue'),
  }
  
  // 尝试从映射表获取
  if (componentMap[normalizedPath]) {
    console.log('在硬编码映射中找到组件:', normalizedPath)
    return componentMap[normalizedPath]
  }
  
  // 如果找不到匹配的组件，返回404页面
  console.error('找不到匹配的组件:', normalizedPath)
  return () => import('@/views/error/404.vue')
}

export default { getViewComponent } 