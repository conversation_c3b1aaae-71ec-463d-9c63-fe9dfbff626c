import request from '@/utils/request'
import { getToken } from '@/utils/auth'

/**
 * 获取本地退货明细列表
 */
export function getLocalRefundPackageList(params: any) {
  return request({
    url: '/local/returnDetails/packages',
    method: 'post',
    data: params
  })
}

/**
 * 创建导出任务
 * @param data 导出参数
 * @returns 任务ID
 */
export function createRefundExportTask(data: any) {
  return request({
    url: '/local/returnDetails/createExportTask',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

/**
 * 获取导出任务进度
 * @param taskId 任务ID
 * @returns 任务进度
 */
export function getRefundExportProgress(taskId: string) {
  return request({
    url: `/local/returnDetails/exportProgress/${taskId}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

/**
 * 下载Excel文件 - 改为提供直接下载链接
 * @param taskId 任务ID
 */
export function downloadExcelFile(taskId: string) {
  const token = getToken() || ''
  const downloadUrl = `/api/local/returnDetails/download/${taskId}?downloadToken=${encodeURIComponent(token)}`
  
  // 创建隐藏的a标签并触发下载
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = downloadUrl
  link.target = '_blank' // 在新标签页中打开，避免导航问题
  
  // 附加到文档并点击
  document.body.appendChild(link)
  link.click()
  
  // 清理DOM
  setTimeout(() => {
    document.body.removeChild(link)
  }, 100)
}

/**
 * 获取用户店铺列表
 */
export function getUserShops() {
  return request({
    url: '/local/returnDetails/shops',
    method: 'get'
  })
} 