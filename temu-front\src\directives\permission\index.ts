import type { ObjectDirective, App } from 'vue'
import { hasPermission } from '@/utils/permission'

export const permission: ObjectDirective = {
  mounted(el, binding) {
    const { value } = binding
    
    if (value) {
      const hasAuth = hasPermission(value)
      
      if (!hasAuth) {
        el.parentNode?.removeChild(el)
      }
    } else {
      throw new Error('需要指定权限值')
    }
  }
}

export function setupPermission(app: App<Element>) {
  app.directive('permission', permission)
  app.directive('hasPermi', permission)
} 