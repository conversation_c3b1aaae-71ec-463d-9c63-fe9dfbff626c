package com.xiao.temu.modules.quality.vo;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

/**
 * 抽检结果明细返回VO
 */
@Data
public class QualityInspectionVO {
    
    /**
     * 请求是否成功
     */
    private Boolean success;
    
    /**
     * 错误码
     */
    private Integer errorCode;
    
    /**
     * 错误信息
     */
    private String errorMsg;
    
    /**
     * 返回结果
     */
    private JSONObject result;
    
    /**
     * 店铺名称（补充字段，方便前端展示）
     */
    private String shopName;
    
    /**
     * 店铺ID（补充字段，方便前端展示）
     */
    private Long shopId;

} 