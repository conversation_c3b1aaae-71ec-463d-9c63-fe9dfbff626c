package com.xiao.temu.modules.sync.vo;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xiao.temu.modules.sync.entity.ProductSyncTask;
import com.xiao.temu.modules.sync.entity.PurchaseOrderSyncTask;
import com.xiao.temu.modules.sync.entity.QualityInspectionSyncTask;
import com.xiao.temu.modules.sync.entity.SalesSyncTask;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * API统一返回结果对象
 */
@Data
public class ApiResponse {
    /**
     * 状态码
     */
    private Integer code;
    
    /**
     * 返回消息
     */
    private String message;
    
    /**
     * 返回数据
     */
    private Object data;
    
    /**
     * 成功状态码
     */
    private static final Integer SUCCESS_CODE = 200;
    
    /**
     * 错误状态码
     */
    private static final Integer ERROR_CODE = 500;
    
    /**
     * 私有构造方法
     */
    private ApiResponse(Integer code, String message, Object data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    /**
     * 成功返回（无数据）
     */
    public static ApiResponse success() {
        return new ApiResponse(SUCCESS_CODE, "操作成功", null);
    }
    
    /**
     * 成功返回（有数据）
     */
    public static ApiResponse success(Object data) {
        return new ApiResponse(SUCCESS_CODE, "操作成功", data);
    }
    
    /**
     * 成功返回（自定义消息和数据）
     */
    public static ApiResponse success(String message, Object data) {
        return new ApiResponse(SUCCESS_CODE, message, data);
    }
    
    /**
     * 失败返回（无数据）
     */
    public static ApiResponse error(String string, List<Map<String, String>> failedRecords) {
        return new ApiResponse(ERROR_CODE, "操作失败", null);
    }
    
    /**
     * 失败返回（自定义消息）
     */
    public static ApiResponse error(String message) {
        return new ApiResponse(ERROR_CODE, message, null);
    }
    
    /**
     * 失败返回（自定义状态码和消息）
     */
    public static ApiResponse error(Integer code, String message) {
        return new ApiResponse(code, message, null);
    }
    
    /**
     * 失败返回（自定义状态码、消息和数据）
     */
    public static ApiResponse error(Integer code, String message, Object data) {
        return new ApiResponse(code, message, data);
    }

    /**
     * 商品同步结果VO
     */
    @Data
    public static class ProductSyncVO {

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 错误码
         */
        private Integer errorCode;

        /**
         * 错误信息
         */
        private String errorMsg;

        /**
         * 店铺ID
         */
        private Long shopId;

        /**
         * 店铺名称
         */
        private String shopName;

        /**
         * 同步状态：0-未同步，1-同步中，2-同步成功，3-同步失败
         */
        private Integer syncStatus;

        /**
         * 上次同步时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastSyncTime;

        /**
         * 数据最新更新时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastUpdateTime;

        /**
         * 总记录数
         */
        private Integer totalRecords;
        
        /**
         * 消息内容
         */
        private String message;

        /**
         * 从同步任务对象创建VO
         *
         * @param task 同步任务
         * @param shopName 店铺名称
         * @return 同步结果VO
         */
        public static ProductSyncVO fromTask(ProductSyncTask task, String shopName) {
            ProductSyncVO vo = new ProductSyncVO();
            vo.setSuccess(task.getSyncStatus() == 2);
            vo.setShopId(task.getShopId());
            vo.setShopName(shopName);
            vo.setSyncStatus(task.getSyncStatus());
            vo.setLastSyncTime(task.getLastSyncTime());
            vo.setLastUpdateTime(task.getLastUpdateTime());
            vo.setTotalRecords(task.getTotalRecords());

            if (task.getSyncStatus() == 3 && task.getErrorMessage() != null) {
                vo.setErrorCode(500);
                vo.setErrorMsg(task.getErrorMessage());
            }

            return vo;
        }
    }

    /**
     * 质检数据同步结果VO
     */
    @Data
    public static class QualityInspectionSyncVO {

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 错误码
         */
        private Integer errorCode;

        /**
         * 错误信息
         */
        private String errorMsg;

        /**
         * 店铺ID
         */
        private Long shopId;

        /**
         * 店铺名称
         */
        private String shopName;

        /**
         * 同步状态：0-未同步，1-同步中，2-同步成功，3-同步失败
         */
        private Integer syncStatus;

        /**
         * 同步时间
         */
        private LocalDateTime syncTime;

        /**
         * 同步记录数
         */
        private Integer totalRecords;

        /**
         * 最新数据更新时间
         */
        private LocalDateTime lastUpdateTime;

        /**
         * 消息内容
         */
        private String message;

        /**
         * 构建同步结果
         *
         * @param task 同步任务
         * @param shopName 店铺名称
         * @return 同步结果VO
         */
        public static QualityInspectionSyncVO fromTask(QualityInspectionSyncTask task, String shopName) {
            QualityInspectionSyncVO vo = new QualityInspectionSyncVO();
            vo.setSuccess(task.getSyncStatus() == 2);
            vo.setErrorCode(task.getSyncStatus() == 3 ? 500 : 0);
            vo.setErrorMsg(task.getErrorMessage());
            vo.setShopId(task.getShopId());
            vo.setShopName(shopName);
            vo.setSyncStatus(task.getSyncStatus());
            vo.setSyncTime(task.getLastSyncTime());
            vo.setTotalRecords(task.getTotalRecords());
            vo.setLastUpdateTime(task.getLastUpdateTime());

            // 如果有lastMessage就使用，否则使用errorMessage
            String message = task.getLastMessage();
            if (message == null || message.isEmpty()) {
                message = task.getErrorMessage();
            }
            vo.setMessage(message);

            return vo;
        }
    }

    /**
     * 退货包裹同步返回VO
     */
    @Data
    public static class RefundPackageSyncVO {

        /**
         * 店铺ID
         */
        private Long shopId;

        /**
         * 店铺名称
         */
        private String shopName;

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 错误码
         */
        private Integer errorCode;

        /**
         * 错误信息
         */
        private String errorMsg;

        /**
         * 同步状态：0-未同步，1-同步中，2-同步成功，3-同步失败
         */
        private Integer syncStatus;

        /**
         * 任务ID
         */
        private Long taskId;

        /**
         * 总记录数
         */
        private Integer totalRecords;

        /**
         * 成功记录数
         */
        private Integer successRecords;

        /**
         * 第一次同步时的成功数量（用于两次同步时累加结果）
         */
        private Integer syncedCount = 0;

        /**
         * 结果数据
         */
        private JSONObject result;
        
        /**
         * 消息内容
         */
        private String message;

        /**
         * 设置同步失败状态
         *
         * @param errorMsg 错误信息
         * @param errorCode 错误码
         */
        public void setFailed(String errorMsg, Integer errorCode) {
            this.success = false;
            this.syncStatus = 3; // 同步失败
            this.errorMsg = errorMsg;
            this.errorCode = errorCode;
        }

        /**
         * 设置同步进行中状态
         */
        public void setProcessing() {
            this.success = null; // 处理中
        }

        /**
         * 设置同步成功状态
         *
         * @param totalRecords 总记录数
         * @param successRecords 成功记录数
         */
        public void setSuccess(Integer totalRecords, Integer successRecords) {
            this.success = true;
            this.syncStatus = 2; // 同步成功
            this.totalRecords = totalRecords;
            this.successRecords = successRecords;
        }
    }

    /**
     * 销售数据同步结果VO
     */
    @Data
    public static class SalesSyncVO {

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 错误码
         */
        private Integer errorCode;

        /**
         * 错误信息
         */
        private String errorMsg;

        /**
         * 店铺ID
         */
        private Long shopId;

        /**
         * 店铺名称
         */
        private String shopName;

        /**
         * 同步状态：0-未同步，1-同步中，2-同步成功，3-同步失败
         */
        private Integer syncStatus;

        /**
         * 上次同步时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastSyncTime;

        /**
         * 数据最新更新时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastUpdateTime;

        /**
         * 总记录数
         */
        private Integer totalRecords;
        
        /**
         * SKU总记录数
         */
        private Integer skuTotalRecords;
        
        /**
         * 仓库总记录数
         */
        private Integer warehouseTotalRecords;
        
        /**
         * 消息内容
         */
        private String message;

        /**
         * 从同步任务对象创建VO
         *
         * @param task 同步任务
         * @param shopName 店铺名称
         * @return 同步结果VO
         */
        public static SalesSyncVO fromTask(SalesSyncTask task, String shopName) {
            SalesSyncVO vo = new SalesSyncVO();
            vo.setSuccess(task.getSyncStatus() == 2);
            vo.setShopId(task.getShopId());
            vo.setShopName(shopName);
            vo.setSyncStatus(task.getSyncStatus());
            vo.setLastSyncTime(task.getLastSyncTime());
            vo.setLastUpdateTime(task.getLastUpdateTime());
            vo.setTotalRecords(task.getTotalRecords());
            vo.setSkuTotalRecords(task.getSkuTotalRecords());
            vo.setWarehouseTotalRecords(task.getWarehouseTotalRecords());

            if (task.getSyncStatus() == 3 && task.getErrorMessage() != null) {
                vo.setErrorCode(500);
                vo.setErrorMsg(task.getErrorMessage());
            }

            return vo;
        }
    }

    /**
     * 备货单同步结果VO
     */
    @Data
    public static class PurchaseOrderSyncVO {

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 错误码
         */
        private Integer errorCode;

        /**
         * 错误信息
         */
        private String errorMsg;

        /**
         * 店铺ID
         */
        private Long shopId;

        /**
         * 店铺名称
         */
        private String shopName;
        
        /**
         * 店铺备注
         */
        private String shopRemark;

        /**
         * 同步状态：0-未同步，1-同步中，2-同步成功，3-同步失败
         */
        private Integer syncStatus;

        /**
         * 上次同步时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastSyncTime;

        /**
         * 数据最新更新时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastUpdateTime;

        /**
         * 普通备货单记录数
         */
        private Integer normalOrderCount;
        
        /**
         * JIT备货单记录数
         */
        private Integer jitOrderCount;
        
        /**
         * 备货单总数
         */
        private Integer totalRecords;
        
        /**
         * 普通备货单数量 (兼容前端字段命名)
         */
        private Integer normalPurchaseOrderCount;
        
        /**
         * JIT备货单数量 (兼容前端字段命名)
         */
        private Integer jitPurchaseOrderCount;
        
        /**
         * 消息内容
         */
        private String message;

        /**
         * 从同步任务对象创建VO
         *
         * @param task 同步任务
         * @param shopName 店铺名称
         * @return 同步结果VO
         */
        public static PurchaseOrderSyncVO fromTask(PurchaseOrderSyncTask task, String shopName) {
            PurchaseOrderSyncVO vo = new PurchaseOrderSyncVO();
            vo.setSuccess(task.getSyncStatus() == 2);
            vo.setShopId(task.getShopId());
            vo.setShopName(shopName);
            vo.setSyncStatus(task.getSyncStatus());
            if (task.getLastSyncTime() != null) {
                vo.setLastSyncTime(task.getLastSyncTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
            }
            if (task.getLastUpdateTime() != null) {
                vo.setLastUpdateTime(task.getLastUpdateTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
            }
            
            // 设置普通备货单数量和JIT备货单数量
            vo.setNormalOrderCount(task.getNormalOrderCount());
            vo.setJitOrderCount(task.getJitOrderCount());
            
            // 设置前端兼容字段
            vo.setNormalPurchaseOrderCount(task.getNormalOrderCount());
            vo.setJitPurchaseOrderCount(task.getJitOrderCount());
            
            // 计算备货单总数
            int totalRecords = 0;
            if (task.getNormalOrderCount() != null) {
                totalRecords += task.getNormalOrderCount();
            }
            if (task.getJitOrderCount() != null) {
                totalRecords += task.getJitOrderCount();
            }
            vo.setTotalRecords(totalRecords);

            if (task.getSyncStatus() == 3 && task.getErrorMessage() != null) {
                vo.setErrorCode(500);
                vo.setErrorMsg(task.getErrorMessage());
            }

            return vo;
        }
    }
}