package com.xiao.temu.modules.refund.dto;

import lombok.Data;

import java.util.List;

/**
 * Temu退货明细请求参数DTO
 */
@Data
public class RefundRequestDTO {
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 出库开始时间（毫秒时间戳）
     */
    private String outboundTimeStart;
    
    /**
     * 出库结束时间（毫秒时间戳）
     */
    private String outboundTimeEnd;
    
    /**
     * 页码
     */
    private String pageNo;
    
    /**
     * 每页条数
     */
    private String pageSize;
    
    /**
     * SKU列表
     */
    private List<Long> productSkuIdList;
    
    /**
     * 退货包裹号列表
     */
    private List<String> returnSupplierPackageNos;
    
    /**
     * 备货单号列表
     */
    private List<String> purchaseSubOrderSns;
    
    /**
     * 是否忽略权限检查
     * 为true时,超级管理员或拥有全部数据权限的用户可以查看所有数据
     */
    private Boolean ignorePermissionCheck = false;
} 