package com.xiao.temu.modules.system.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 系统用户视图对象
 */
@Data
@NoArgsConstructor
public class SysUserVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 昵称
     */
    private String nickName;
    
    /**
     * 手机号码
     */
    private String phonenumber;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 用户状态（0正常 1停用）
     */
    private String status;
    
    /**
     * 角色列表
     */
    private List<RoleVO> roles;
    
    /**
     * 角色内部类
     */
    @Data
    @NoArgsConstructor
    public static class RoleVO implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 角色ID
         */
        private Long roleId;
        
        /**
         * 角色名称
         */
        private String roleName;
        
        /**
         * 角色键值
         */
        private String roleKey;
    }
} 