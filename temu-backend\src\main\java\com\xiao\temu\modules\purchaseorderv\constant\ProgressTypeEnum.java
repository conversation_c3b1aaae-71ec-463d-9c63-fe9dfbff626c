package com.xiao.temu.modules.purchaseorderv.constant;

/**
 * 生产进度类型枚举
 */
public enum ProgressTypeEnum {

    /**
     * 烧花/剪图
     */
    CUTTING("cutting", "烧花/剪图"),

    /**
     * 车间/拣货
     */
    WORKSHOP("workshop", "车间/拣货"),

    /**
     * 剪线/压图
     */
    TRIMMING("trimming", "剪线/压图"),

    /**
     * 查货
     */
    INSPECTION("inspection", "查货"),

    /**
     * 包装
     */
    PACKAGING("packaging", "包装"),

    /**
     * 发货
     */
    SHIPPING("shipping", "发货");

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型标题
     */
    private final String title;

    ProgressTypeEnum(String code, String title) {
        this.code = code;
        this.title = title;
    }

    public String getCode() {
        return code;
    }

    public String getTitle() {
        return title;
    }

    /**
     * 根据编码获取枚举
     */
    public static ProgressTypeEnum getByCode(String code) {
        for (ProgressTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 