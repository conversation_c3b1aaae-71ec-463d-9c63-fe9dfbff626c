<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.quality.mapper.LocalQualityInspectionMapper">

    <!-- 查询结果映射 -->
    <resultMap id="QualityInspectionItemResultMap" type="com.xiao.temu.modules.quality.vo.LocalQualityInspectionVO$LocalQualityInspectionItemVO">
        <id column="id" property="id" />
        <result column="qc_bill_id" property="qcBillId" />
        <result column="product_sku_id" property="productSkuId" />
        <result column="product_skc_id" property="productSkcId" />
        <result column="spu_id" property="spuId" />
        <result column="sku_name" property="skuName" />
        <result column="cat_name" property="catName" />
        <result column="purchase_no" property="purchaseNo" />
        <result column="spec" property="spec" />
        <result column="thumb_url" property="thumbUrl" />
        <result column="qc_result" property="qcResult" />
        <result column="qc_result_update_time" property="qcResultUpdateTime" />
        <result column="flaw_name_desc" property="flawNameDesc" />
        <result column="remark" property="remark" />
        <result column="attachments" property="attachments" typeHandler="com.xiao.temu.infrastructure.persistence.handler.JsonListTypeHandler" />
        <result column="ext_code" property="extCode" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="shop_remark" property="shopRemark" />
    </resultMap>

    <!-- 分页查询质检数据 -->
    <select id="selectQualityInspectionPage" resultMap="QualityInspectionItemResultMap">
        SELECT
            q.id,
            q.qc_bill_id,
            q.product_sku_id,
            q.product_skc_id,
            q.spu_id,
            q.sku_name,
            q.cat_name,
            q.purchase_no,
            q.spec,
            q.thumb_url,
            q.qc_result,
            DATE_FORMAT(q.qc_result_update_time, '%Y-%m-%d %H:%i:%s') as qc_result_update_time,
            d.flaw_name_desc,
            d.remark,
            d.attachments,
            p.ext_code AS ext_code,
            q.shop_id,
            s.shop_name,
            s.remark as shop_remark
        FROM
            quality_inspection q
        LEFT JOIN
            quality_inspection_defect_detail d ON q.shop_id = d.shop_id AND q.qc_bill_id = d.qc_bill_id AND q.product_sku_id = d.product_sku_id
        LEFT JOIN
            product p ON q.shop_id = p.shop_id AND q.product_skc_id = p.product_skc_id
        LEFT JOIN 
            shop s ON q.shop_id = s.shop_id
        <where>
            <if test="shopId != null">
                AND q.shop_id = #{shopId}
            </if>
            <if test="qcResult != null and qcResult != ''">
                AND q.qc_result = #{qcResult}
            </if>
            <if test="purchaseNoList != null and purchaseNoList.size() > 0">
                AND q.purchase_no IN
                <foreach collection="purchaseNoList" item="pNo" open="(" separator="," close=")">
                    #{pNo}
                </foreach>
            </if>
            <if test="skuIdList != null and skuIdList.size() > 0">
                AND q.product_sku_id IN
                <foreach collection="skuIdList" item="skuId" open="(" separator="," close=")">
                    #{skuId}
                </foreach>
            </if>
            <if test="skcIdList != null and skcIdList.size() > 0">
                AND q.product_skc_id IN
                <foreach collection="skcIdList" item="skcId" open="(" separator="," close=")">
                    #{skcId}
                </foreach>
            </if>
            <if test="qcResultUpdateTimeBegin != null">
                AND q.qc_result_update_time &gt;= #{qcResultUpdateTimeBegin}
            </if>
            <if test="qcResultUpdateTimeEnd != null">
                AND q.qc_result_update_time &lt;= #{qcResultUpdateTimeEnd}
            </if>
            <if test="skuNameKeyword != null and skuNameKeyword != ''">
                AND q.sku_name LIKE CONCAT('%', #{skuNameKeyword}, '%')
            </if>
        </where>
        ORDER BY q.qc_result_update_time DESC
    </select>
    
    <!-- 多店铺分页查询质检数据 -->
    <select id="selectMultiShopQualityInspectionPage" resultMap="QualityInspectionItemResultMap">
        SELECT
            q.id,
            q.qc_bill_id,
            q.product_sku_id,
            q.product_skc_id,
            q.spu_id,
            q.sku_name,
            q.cat_name,
            q.purchase_no,
            q.spec,
            q.thumb_url,
            q.qc_result,
            DATE_FORMAT(q.qc_result_update_time, '%Y-%m-%d %H:%i:%s') as qc_result_update_time,
            d.flaw_name_desc,
            d.remark,
            d.attachments,
            p.ext_code AS ext_code,
            q.shop_id,
            s.shop_name,
            s.remark as shop_remark
        FROM
            quality_inspection q
        LEFT JOIN
            quality_inspection_defect_detail d ON q.shop_id = d.shop_id AND q.qc_bill_id = d.qc_bill_id AND q.product_sku_id = d.product_sku_id
        LEFT JOIN
            product p ON q.shop_id = p.shop_id AND q.product_skc_id = p.product_skc_id
        LEFT JOIN 
            shop s ON q.shop_id = s.shop_id
        <where>
            <if test="shopIds != null and shopIds.size() > 0">
                AND q.shop_id IN
                <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test="qcResult != null and qcResult != ''">
                AND q.qc_result = #{qcResult}
            </if>
            <if test="purchaseNoList != null and purchaseNoList.size() > 0">
                AND q.purchase_no IN
                <foreach collection="purchaseNoList" item="pNo" open="(" separator="," close=")">
                    #{pNo}
                </foreach>
            </if>
            <if test="skuIdList != null and skuIdList.size() > 0">
                AND q.product_sku_id IN
                <foreach collection="skuIdList" item="skuId" open="(" separator="," close=")">
                    #{skuId}
                </foreach>
            </if>
            <if test="skcIdList != null and skcIdList.size() > 0">
                AND q.product_skc_id IN
                <foreach collection="skcIdList" item="skcId" open="(" separator="," close=")">
                    #{skcId}
                </foreach>
            </if>
            <if test="qcResultUpdateTimeBegin != null">
                AND q.qc_result_update_time &gt;= #{qcResultUpdateTimeBegin}
            </if>
            <if test="qcResultUpdateTimeEnd != null">
                AND q.qc_result_update_time &lt;= #{qcResultUpdateTimeEnd}
            </if>
            <if test="skuNameKeyword != null and skuNameKeyword != ''">
                AND q.sku_name LIKE CONCAT('%', #{skuNameKeyword}, '%')
            </if>
        </where>
        ORDER BY q.qc_result_update_time DESC
    </select>

</mapper> 