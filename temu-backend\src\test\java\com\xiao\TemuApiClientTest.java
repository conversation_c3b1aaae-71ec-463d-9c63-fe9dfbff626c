package com.xiao;

import com.alibaba.fastjson2.JSONObject;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.infrastructure.api.TemuApiClient;
import org.ehcache.shadow.org.terracotta.statistics.Time;

import java.util.HashMap;

public class TemuApiClientTest {
    public static void main(String[] args) {
//        qualityInspectionList();
//        getGoodsSalesv2();
//        qualityInspectionDetail();
//        getGoodsList();
//        refundPackageList();
//        getGoodsPrice();
//        purchaseorderv2();
//        customLable();
        shiporderv2();
//        address();
//        boxmarkinfo();
//        packageInfo();
//        productSearch();
//        labelv2();
    }

    private static void refundPackageList() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken("d37jp01jhfkkkidjqrzdocg9yyl7g7rwkqtnu48qxjomgwe2ntzl81ii");
        commonParams.setType("bg.refund.returnpackagelist.get");
        commonParams.setAppKey("2715ae152d7baa483dab21ead39ac222");
        commonParams.setAppSecret("9d5c08e4f6e66f538c5c8555cab3d60e5d0fef72");

        HashMap<String, Object> businessParams = new HashMap<>();
        businessParams.put("outboundTimeEnd", "1740326399999");
        businessParams.put("outboundTimeStart", "1739030400000");
        businessParams.put("pageNo", "1");
        businessParams.put("pageSize", "10");
        businessParams.put("timestamp", String.valueOf(Time.time()));
        JSONObject jsonObject = TemuApiClient.sendRequest(commonParams, businessParams);
        System.out.println(jsonObject);
    }

    private static void qualityInspectionList() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken("juekxhoepdyctc7lgex2nvvo8gbhzg9rxtklha6vgvxipe46njx7gz20");
        commonParams.setType("bg.goods.qualityinspection.get");
        commonParams.setAppKey("9c65725c36feec08d6b7848cdff20e2a");
        commonParams.setAppSecret("f6cc806fb70ee21fb06ea176db6d3b9bb10b0f78");

        HashMap<String, Object> businessParams = new HashMap<>();
        businessParams.put("outboundTimeEnd", "1740326399999");
        businessParams.put("outboundTimeStart", "1739030400000");
        HashMap<String, Integer> pageInfo = new HashMap<>();
        pageInfo.put("pageNo", 1);
        pageInfo.put("pageSize", 100);
        businessParams.put("pageInfo", pageInfo);

        // 支持传入多个sku ID，最多10个
        businessParams.put("skuIdList", new Long[]{
                3314079535L,
                9376926828L,
                // 以下是示例，可以根据实际需要添加更多的sku ID，最多支持10个
                // 9832287239L,
                // 9832287240L,
                // 9832287241L
        });

        // 可以选择只查询合格或不合格质检记录
        // 1 - 合格，2 - 不合格
        businessParams.put("skuQcResult", 2);

        businessParams.put("timestamp", String.valueOf(Time.time()));
        JSONObject jsonObject = TemuApiClient.sendRequest(commonParams, businessParams);
        System.out.println(jsonObject);
    }

    private static void qualityInspectionDetail() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken("tt7gi5iktlnhavynsmbqgndgf6padmmhzogxszxswfhsl2dtc5zkx58x");
        commonParams.setType("bg.goods.qualityinspectiondetail.get");
        commonParams.setAppKey("9c65725c36feec08d6b7848cdff20e2a");
        commonParams.setAppSecret("f6cc806fb70ee21fb06ea176db6d3b9bb10b0f78");

        HashMap<String, Object> businessParams = new HashMap<>();
        businessParams.put("qcBillId", "1553477275031");
        businessParams.put("timestamp", String.valueOf(Time.time()));
        JSONObject jsonObject = TemuApiClient.sendRequest(commonParams, businessParams);
        System.out.println(jsonObject);
    }

    public static void getGoodsList() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken("juekxhoepdyctc7lgex2nvvo8gbhzg9rxtklha6vgvxipe46njx7gz20");
        commonParams.setType("bg.goods.list.get");
        commonParams.setAppKey("9c65725c36feec08d6b7848cdff20e2a");
        commonParams.setAppSecret("f6cc806fb70ee21fb06ea176db6d3b9bb10b0f78");

        HashMap<String, Object> businessParams = new HashMap<>();
        businessParams.put("timestamp", String.valueOf(Time.time()));
        businessParams.put("page", "1");
        businessParams.put("pageSize", "10");
//       businessParams.put("productSkuIds", new Long[]{7463158335L});
        JSONObject jsonObject = TemuApiClient.sendRequest(commonParams, businessParams);
        System.out.println(jsonObject);
    }

    public static void getGoodsSalesv2() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken("4zlu3hwtooucpeylhzmrkjznu6squmy2ibl9pl0lpettvtrpf4fjpgpq");
        commonParams.setType("bg.goods.salesv2.get");
        commonParams.setAppKey("befc7d1eb185707d512c0ea3e3fc924f");
        commonParams.setAppSecret("cf29a9bdba34d6451944fce5dddcceb5c9beda32");
        HashMap<String, Object> businessParams = new HashMap<>();
        businessParams.put("timestamp", String.valueOf(Time.time()));
        businessParams.put("pageNo", "1");
        businessParams.put("isLack", 0);
        businessParams.put("priceAdjustRecentDays", 7);
        //最近一个月有销量的数据
        businessParams.put("thirtyDaysSaleVolumMax", 9999);
        businessParams.put("thirtyDaysSaleVolumMin", 1);
        businessParams.put("pageSize", "100");
        JSONObject jsonObject = TemuApiClient.sendRequest(commonParams, businessParams);
        System.out.println(jsonObject);
    }

    public static void getGoodsPrice() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken("qw6ayyvzcbpi8luhwwhiaolmgi4rrogm2kvnuizxlrplbdcutdacxgwc");
        commonParams.setType("bg.goods.price.list.get");
        commonParams.setAppKey("7f64a7e22499d93789a73c0a1a904d8f");
        commonParams.setAppSecret("436b9abbc22aae9302e4f62b919b1e88fa5693ad");

        HashMap<String, Object> businessParams = new HashMap<>();
        businessParams.put("timestamp", String.valueOf(Time.time()));
        businessParams.put("pageNo","1");
        businessParams.put("pageSize","10");
//        businessParams.put("productSkuIds", new long[]{8101452141L});
        JSONObject jsonObject = TemuApiClient.sendRequest(commonParams, businessParams);
        System.out.println(jsonObject);
    }

    public static void purchaseorderv2() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken("qw6ayyvzcbpi8luhwwhiaolmgi4rrogm2kvnuizxlrplbdcutdacxgwc");
        commonParams.setType("bg.purchaseorderv2.get");
        commonParams.setAppKey("7f64a7e22499d93789a73c0a1a904d8f");
        commonParams.setAppSecret("436b9abbc22aae9302e4f62b919b1e88fa5693ad");

//        commonParams.setAccessToken("juekxhoepdyctc7lgex2nvvo8gbhzg9rxtklha6vgvxipe46njx7gz20");
//        commonParams.setType("bg.purchaseorderv2.get");
//        commonParams.setAppKey("9c65725c36feec08d6b7848cdff20e2a");
//        commonParams.setAppSecret("f6cc806fb70ee21fb06ea176db6d3b9bb10b0f78");
        HashMap<String, Object> businessParams = new HashMap<>();
        businessParams.put("timestamp", String.valueOf(Time.time()));
        businessParams.put("pageNo", "1");
        businessParams.put("pageSize", "10");//最大100
        businessParams.put("urgencyType","1");//1是jit备货,0是普通备货
//        businessParams.put("subPurchaseOrderSnList", new String[] {"WB2505201341174"});
//        businessParams.put("productSkuIds",new long[]{8101452141L});
//        businessParams.put("inventoryRegionList",new int[]{3});
//        businessParams.put("qcNotPassCreate", false);
        JSONObject jsonObject = TemuApiClient.sendRequest(commonParams, businessParams);
        System.out.println(jsonObject);
    }

    public static void customLable() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken("juekxhoepdyctc7lgex2nvvo8gbhzg9rxtklha6vgvxipe46njx7gz20");
        commonParams.setType("bg.goods.labelv2.get");
        commonParams.setAppKey("9c65725c36feec08d6b7848cdff20e2a");
        commonParams.setAppSecret("f6cc806fb70ee21fb06ea176db6d3b9bb10b0f78");

        HashMap<String, Object> businessParams = new HashMap<>();
        businessParams.put("timestamp", String.valueOf(Time.time()));
        businessParams.put("productSkuIdList", new long[]{3760383695L});
        businessParams.put("return_data_key", "true");
        JSONObject jsonObject = TemuApiClient.sendRequest(commonParams, businessParams);
        System.out.println(jsonObject);
    }

    public static void shiporderv2() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken("juekxhoepdyctc7lgex2nvvo8gbhzg9rxtklha6vgvxipe46njx7gz20");
        commonParams.setType("bg.shiporderv2.get");
        commonParams.setAppKey("9c65725c36feec08d6b7848cdff20e2a");
        commonParams.setAppSecret("f6cc806fb70ee21fb06ea176db6d3b9bb10b0f78");


        HashMap<String, Object> businessParams = new HashMap<>();
        businessParams.put("timestamp", String.valueOf(Time.time()));
        businessParams.put("pageNo", 1);
        businessParams.put("pageSize", 10);
        JSONObject jsonObject = TemuApiClient.sendRequest(commonParams, businessParams);
        System.out.println(jsonObject);
    }

    public static void address() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken("juekxhoepdyctc7lgex2nvvo8gbhzg9rxtklha6vgvxipe46njx7gz20");
        commonParams.setType("bg.goods.warehouse.list.get");
        commonParams.setAppKey("9c65725c36feec08d6b7848cdff20e2a");
        commonParams.setAppSecret("f6cc806fb70ee21fb06ea176db6d3b9bb10b0f78");

        HashMap<String, Object> businessParams = new HashMap<>();
        businessParams.put("openApiUser", String.valueOf(Time.time()));
//        businessParams.put("pageNo", 1);
//        businessParams.put("pageSize", 10);


//        businessParams.put("subPurchaseOrderSnList", new String[]{"WB2505151634213"});
//        businessParams.put("productLabelCodeStyle", 0);
        JSONObject jsonObject = TemuApiClient.sendRequest(commonParams, businessParams);
        System.out.println(jsonObject);
    }
    public static void boxmarkinfo() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken("juekxhoepdyctc7lgex2nvvo8gbhzg9rxtklha6vgvxipe46njx7gz20");
        commonParams.setType("bg.logistics.boxmarkinfo.get");
        commonParams.setAppKey("9c65725c36feec08d6b7848cdff20e2a");
        commonParams.setAppSecret("f6cc806fb70ee21fb06ea176db6d3b9bb10b0f78");

        HashMap<String, Object> businessParams = new HashMap<>();
        businessParams.put("deliveryOrderSnList", new String[]{"FH2505183098655","FH2505183082651"});


        JSONObject jsonObject = TemuApiClient.sendRequest(commonParams, businessParams);
        System.out.println(jsonObject);
    }

    public static void packageInfo() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken("juekxhoepdyctc7lgex2nvvo8gbhzg9rxtklha6vgvxipe46njx7gz20");
        commonParams.setType("bg.shiporder.package.get");
        commonParams.setAppKey("9c65725c36feec08d6b7848cdff20e2a");
        commonParams.setAppSecret("f6cc806fb70ee21fb06ea176db6d3b9bb10b0f78");

        HashMap<String, Object> businessParams = new HashMap<>();
        businessParams.put("deliveryOrderSn","FH2505182553244");


        JSONObject jsonObject = TemuApiClient.sendRequest(commonParams, businessParams);
        System.out.println(jsonObject);
    }

    public static void topsellingGet() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken("juekxhoepdyctc7lgex2nvvo8gbhzg9rxtklha6vgvxipe46njx7gz20");
        commonParams.setType("bg.goods.topselling.soldout.get");
        commonParams.setAppKey("9c65725c36feec08d6b7848cdff20e2a");
        commonParams.setAppSecret("f6cc806fb70ee21fb06ea176db6d3b9bb10b0f78");

        HashMap<String, Object> businessParams = new HashMap<>();
//        businessParams.put("deliveryOrderSn","FH2505182553244");


        JSONObject jsonObject = TemuApiClient.sendRequest(commonParams, businessParams);
        System.out.println(jsonObject);
    }

    public static void productSearch() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken("juekxhoepdyctc7lgex2nvvo8gbhzg9rxtklha6vgvxipe46njx7gz20");
        commonParams.setType("bg.product.search");
        commonParams.setAppKey("9c65725c36feec08d6b7848cdff20e2a");
        commonParams.setAppSecret("f6cc806fb70ee21fb06ea176db6d3b9bb10b0f78");

        HashMap<String, Object> businessParams = new HashMap<>();
        businessParams.put("mallId","634418214622876");
        businessParams.put("pageSize","100");
        businessParams.put("pageNum","1");


        JSONObject jsonObject = TemuApiClient.sendRequest(commonParams, businessParams);
        System.out.println(jsonObject);
    }

    public static void labelv2() {
        CommonParams commonParams = new CommonParams();
        commonParams.setAccessToken("juekxhoepdyctc7lgex2nvvo8gbhzg9rxtklha6vgvxipe46njx7gz20");
        commonParams.setType("bg.goods.labelv2.get");
        commonParams.setAppKey("9c65725c36feec08d6b7848cdff20e2a");
        commonParams.setAppSecret("f6cc806fb70ee21fb06ea176db6d3b9bb10b0f78");

        HashMap<String, Object> businessParams = new HashMap<>();
        businessParams.put("productSkuIdList",new String[]{"7256878594"});
//        businessParams.put("productSkcIdList",new String[]{"4635815697"});//最大20,超过数量需要分批处理


        JSONObject jsonObject = TemuApiClient.sendRequest(commonParams, businessParams);
        System.out.println(jsonObject);
    }
}
