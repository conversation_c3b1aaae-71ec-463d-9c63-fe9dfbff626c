package com.xiao.temu.modules.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.production.entity.ProductionGroupShopAssignment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产组店铺分配Mapper接口
 */
@Mapper
public interface ProductionGroupShopAssignmentMapper extends BaseMapper<ProductionGroupShopAssignment> {
    
    /**
     * 根据店铺ID查询所属的所有生产组ID
     *
     * @param shopId 店铺ID
     * @return 生产组ID列表
     */
    List<Long> selectGroupIdsByShopId(@Param("shopId") Long shopId);
    
    /**
     * 根据生产组ID查询所属的所有店铺ID
     *
     * @param groupId 生产组ID
     * @return 店铺ID列表
     */
    List<Long> selectShopIdsByGroupId(@Param("groupId") Long groupId);
    
    /**
     * 根据店铺ID查询一个生产组关联
     *
     * @param shopId 店铺ID
     * @return 生产组关联
     */
    ProductionGroupShopAssignment selectOneByShopId(@Param("shopId") Long shopId);
} 