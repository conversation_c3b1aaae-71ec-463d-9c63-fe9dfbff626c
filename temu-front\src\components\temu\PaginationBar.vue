<template>
  <div class="pagination-section">
    <div class="pagination-container">
      <!-- 修改：使用 el-tooltip 来显示数据分布 -->
      <el-tooltip
        v-if="shopRanges && shopRanges.length > 0"
        placement="top"
        effect="light"
        popper-class="shop-ranges-tooltip"
      >
        <template #content>
          <div class="shop-ranges-tooltip-content">
            <div class="tooltip-title">数据分布详情</div>
            <table class="tooltip-table">
              <thead>
                <tr>
                  <th>店铺名称</th>
                  <th>数据范围</th>
                  <th>可查询总条数</th>
                  <th>页码范围</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="range in shopRanges" :key="range.shopId">
                  <td>{{ range.shopName }}</td>
                  <td>{{ range.startIndex }}-{{ range.endIndex }}</td>
                  <td>{{ range.endIndex - range.startIndex + 1 }}</td>
                  <td>{{ Math.ceil(range.startIndex / pageSize) }} - {{ Math.ceil(range.endIndex / pageSize) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </template>
        <span class="shop-ranges-summary">
          数据分布 (共 {{ shopRanges.length }} 个店铺)
        </span>
      </el-tooltip>
      <!-- 保留原始的 el-pagination 组件 -->
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        :layout="layout"
        :total="displayTotal"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @update:current-page="(val) => emit('update:currentPage', val)"
        @update:page-size="(val) => emit('update:pageSize', val)"
      />
      <div v-if="showLimitTip && total > maxLimit" class="pagination-limit-tip">
        <el-icon><Warning /></el-icon>
        <span>{{ limitTipText }}</span>
      </div>
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, computed } from 'vue';
import { Warning } from '@element-plus/icons-vue';
// 新增：导入 ElTooltip
import { ElTooltip } from 'element-plus';
// import { ShopDataRange } from '@/types/purchaseOrder' // 导入类型 (JS 文件中不能用 import type)

const props = defineProps({
  currentPage: {
    type: Number,
    required: true
  },
  pageSize: {
    type: Number,
    required: true
  },
  total: {
    type: Number,
    required: true
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100]
  },
  layout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  },
  maxLimit: {
    type: Number,
    default: 10000
  },
  showLimitTip: {
    type: Boolean,
    default: false
  },
  limitTipText: {
    type: String,
    default: '系统限制只能查询前10000条数据'
  },
  shopRanges: {
    type: Array,
    default: () => []
    /**
     * @type {() => import('@/types/purchaseOrder').ShopDataRange[]}
     */
  }
});

const emit = defineEmits(['update:currentPage', 'update:pageSize', 'size-change', 'current-change']);

// 显示的总数，如果超过最大限制，则显示最大限制数
const displayTotal = computed(() => {
  if (props.showLimitTip && props.total > props.maxLimit) {
    return props.maxLimit;
  }
  return props.total;
});

const handleSizeChange = (size) => {
  emit('update:pageSize', size);
  emit('size-change', size);
};

const handleCurrentChange = (page) => {
  emit('update:currentPage', page);
  emit('current-change', page);
};
</script>

<style scoped>
.pagination-section {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  background-color: #fff;
  padding: 8px 16px 8px 280px; /* 调整左边距以适应侧边栏 */
  border-top: 1px solid #EBEEF5;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 15px;
}

/* 新增：数据分布摘要样式 */
.shop-ranges-summary {
  font-size: 13px;
  color: #606266;
  cursor: pointer; /* 添加手型光标 */
  border-bottom: 1px dashed #dcdfe6; /* 添加虚线 */
  user-select: none; /* 防止文本被选中 */
  margin-right: 10px; /* 与分页控件保持间距 */
}

/* 分页限制提示样式 */
.pagination-limit-tip {
  font-size: 12px;
  color: #e6a23c;
  display: flex;
  align-items: center;
  /* margin-left: 15px; */ /* 由 gap 控制间距 */
  white-space: nowrap;
}

.pagination-limit-tip .el-icon {
  margin-right: 4px;
  font-size: 14px;
}

/* 响应式布局调整 */
@media (max-width: 1200px) {
  .pagination-section {
    padding-left: 220px; /* 适应较窄的侧边栏 */
  }
}

@media (max-width: 768px) {
  .pagination-section {
    padding-left: 80px; /* 适应更窄的侧边栏或移动设备 */
    flex-direction: column; /* 在小屏幕上可能需要垂直排列 */
    align-items: flex-end;
  }
  .pagination-container {
     flex-wrap: wrap; /* 允许换行 */
     justify-content: center;
  }
   .shop-ranges-summary {
    margin-bottom: 5px; /* 垂直排列时增加底部间距 */
    margin-right: 0;
  }
}
</style>

<style>
/* 新增 Tooltip 样式 (全局，因为 popper-class 不支持 scoped) */
.shop-ranges-tooltip .el-tooltip__popper {
  max-width: 400px; /* 调整最大宽度 */
  padding: 12px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.shop-ranges-tooltip-content .tooltip-title {
  font-size: 14px;
  margin-bottom: 10px;
  color: #303133;
  font-weight: 600;
  text-align: center;
}

.shop-ranges-tooltip-content .tooltip-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.shop-ranges-tooltip-content .tooltip-table th,
.shop-ranges-tooltip-content .tooltip-table td {
  border: 1px solid #ebeef5;
  padding: 8px 10px;
  text-align: left;
  vertical-align: middle;
}

.shop-ranges-tooltip-content .tooltip-table th {
  background-color: #f5f7fa;
  color: #909399;
  font-weight: 500;
}

.shop-ranges-tooltip-content .tooltip-table td {
  color: #606266;
}
</style>