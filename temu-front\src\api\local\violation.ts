// 店铺违规信息API服务
import request from '@/utils/request'
import type { ViolationQueryParams, ViolationQueryResult, ViolationInfoItem, CompleteViolationInfo } from '@/types/local/violation'

// 获取违规信息列表
export function getViolationList(params: ViolationQueryParams) {
  return request<ViolationQueryResult>({
    url: '/local/violation/list',
    method: 'post',
    data: params
  })
}

// 获取违规信息详情
export function getViolationDetail(id: number) {
  return request<CompleteViolationInfo>({
    url: `/local/violation/${id}`,
    method: 'get'
  })
}

// 新增违规信息
export function addViolation(data: ViolationInfoItem) {
  return request<boolean>({
    url: '/local/violation',
    method: 'post',
    data
  })
}

// 修改违规信息
export function updateViolation(data: ViolationInfoItem) {
  return request<boolean>({
    url: '/local/violation',
    method: 'put',
    data
  })
}

// 删除违规信息
export function deleteViolation(id: number) {
  return request({
    url: `/local/violation/${id}`,
    method: 'delete'
  })
}

// 批量删除违规信息
export function batchDeleteViolations(ids: number[]) {
  return request({
    url: `/local/violation/batch/${ids.join(',')}`,
    method: 'delete'
  })
}

// 提交申诉(示例，实际接口待接入)
export function submitAppeal(data: any) {
  return request({
    url: '/local/violation/appeal',
    method: 'post',
    data
  })
}

// 创建导出任务
export function createExportTask(params: any) {
  return request<{ taskId: string }>({
    url: '/local/violation/createExportTask',
    method: 'post',
    data: params
  })
}

// 获取导出任务进度
export function getExportProgress(taskId: string) {
  return request<{
    taskId: string;
    progress: number;
    status: string;
    message: string;
    fileName: string;
  }>({
    url: `/local/violation/exportProgress/${taskId}`,
    method: 'get'
  })
}

// 获取导出文件下载URL
export function getExportDownloadUrl(taskId: string) {
  return `/api/local/violation/downloadExcel/${taskId}`
} 