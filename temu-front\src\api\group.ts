import request from '@/utils/request'

// 获取运营组列表
export function getGroupList(query: any) {
  return request({
    url: 'operation/group/list',
    method: 'get',
    params: query
  })
}

// 获取运营组详情
export function getGroupDetail(groupId: number) {
  return request({
    url: `operation/group/${groupId}`,
    method: 'get'
  })
}

// 新增运营组
export function addGroup(data: any) {
  return request({
    url: 'operation/group',
    method: 'post',
    data: data
  })
}

// 修改运营组
export function updateGroup(data: any) {
  return request({
    url: 'operation/group',
    method: 'put',
    data: data
  })
}

// 删除运营组
export function deleteGroup(groupIds: number[]) {
  return request({
    url: `operation/group/${groupIds}`,
    method: 'delete'
  })
}

// 修改运营组状态
export function changeGroupStatus(groupId: number, status: string) {
  return request({
    url: 'operation/group/changeStatus',
    method: 'put',
    params: {
      groupId,
      status
    }
  })
}

// 设置运营组负责人
export function setGroupLeader(groupId: number, userId: number) {
  return request({
    url: `operation/group/${groupId}/leader/${userId}`,
    method: 'put'
  })
}

// 获取运营组成员
export function getGroupMembers(groupId: number, pageNum: number = 1, pageSize: number = 10) {
  return request({
    url: `operation/group/${groupId}/members`,
    method: 'get',
    params: {
      pageNum,
      pageSize
    }
  })
}

// 添加运营组成员
export function addGroupMembers(groupId: number, userIds: number[]) {
  return request({
    url: `operation/group/${groupId}/members`,
    method: 'post',
    data: userIds
  })
}

// 移除运营组成员
export function removeGroupMember(groupId: number, userId: number) {
  return request({
    url: `operation/group/${groupId}/members/${userId}`,
    method: 'delete'
  })
}

// 获取运营组统计数据
export function getGroupStatistics(groupId: number) {
  return request({
    url: `operation/group/${groupId}/statistics`,
    method: 'get'
  })
}

// 获取用户所在的运营组列表
export function getUserGroups() {
  return request({
    url: 'operation/group/user',
    method: 'get'
  })
}

// 获取用户负责的运营组列表
export function getLeaderGroups() {
  return request({
    url: 'operation/group/leader',
    method: 'get'
  })
}

// 获取未分配给运营组的店铺列表
export function getUnassignedShops(groupId: number) {
  return request({
    url: `operation/group/${groupId}/unassigned-shops`,
    method: 'get'
  })
}

// 为运营组分配店铺
export function assignShopsToGroup(groupId: number, shopIds: number[]) {
  return request({
    url: `operation/group/${groupId}/assign-shops`,
    method: 'post',
    data: shopIds
  })
}

// 从运营组中移除店铺
export function removeShopFromGroup(groupId: number, shopId: number) {
  return request({
    url: `operation/group/${groupId}/shops/${shopId}`,
    method: 'delete'
  })
}

// 获取指定用户所在的运营组列表
export function getUserGroupsByUserId(userId: number) {
  return request({
    url: `operation/group/listByMember/${userId}`,
    method: 'get'
  })
}

// 导出运营组数据
export function exportGroups(data: any) {
  console.log('API调用 - 导出运营组数据:', data);
  return request({
    url: 'operation/group/export',
    method: 'post',
    data: data,
    responseType: 'blob' // 设置响应类型为二进制流
  })
} 