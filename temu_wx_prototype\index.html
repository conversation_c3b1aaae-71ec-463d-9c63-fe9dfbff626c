<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TEMU备货单生产进度追踪</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background-color: #f7f7f7;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }
        .wx-container {
            max-width: 414px;
            min-height: 100vh;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 0;
            box-shadow: 0 0 10px rgba(0,0,0,0.05);
            overflow: hidden;
            position: relative;
        }
        @media (min-width: 500px) {
            .wx-container {
                min-height: 90vh;
                margin: 20px auto;
                border-radius: 30px;
            }
        }
        .wx-btn-primary {
            background-color: #07c160;
            color: white;
        }
        .wx-btn-primary:hover {
            background-color: #06ad56;
        }
    </style>
</head>
<body>
    <div class="wx-container">
        <div class="flex flex-col h-screen">
            <div class="text-center pt-20 pb-10">
                <h1 class="text-2xl font-bold">TEMU备货单生产进度追踪</h1>
                <p class="text-gray-500 mt-2">原型演示</p>
            </div>
            <div class="flex-grow flex flex-col justify-center px-8">
                <a href="login.html" class="block w-full py-3 bg-[#07c160] text-white text-center rounded-md font-medium mb-4">
                    开始演示
                </a>
            </div>
            <div class="p-4 text-center text-xs text-gray-400">
                这是一个原型演示应用
            </div>
        </div>
    </div>
</body>
</html> 