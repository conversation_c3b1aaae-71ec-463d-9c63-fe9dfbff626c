/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : temu_api

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 03/06/2025 15:41:37
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for operation_group
-- ----------------------------
DROP TABLE IF EXISTS `operation_group`;
CREATE TABLE `operation_group`  (
  `group_id` bigint NOT NULL AUTO_INCREMENT COMMENT '运营组ID',
  `group_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '运营组名称',
  `leader_id` bigint NOT NULL COMMENT '负责人ID',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`group_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '运营组表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of operation_group
-- ----------------------------
INSERT INTO `operation_group` VALUES (1, '运营组2', 7, '0', '2025-03-20 11:23:07', '2025-03-24 18:24:53', '运营组2');
INSERT INTO `operation_group` VALUES (4, '运营组1', 5, '0', '2025-03-22 13:28:16', '2025-03-22 20:15:46', '123');

SET FOREIGN_KEY_CHECKS = 1;
