package com.xiao.temu.utils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;

/**
 * 日期时间工具类
 * 用于统一处理系统中的日期时间转换
 */
public class DateUtils {

    /**
     * 标准日期格式 yyyy-MM-dd
     */
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    
    /**
     * 标准日期时间格式 yyyy-MM-dd HH:mm:ss
     */
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * ISO 8601标准日期时间格式
     */
    public static final DateTimeFormatter ISO_DATE_TIME_FORMATTER = DateTimeFormatter.ISO_DATE_TIME;

    /**
     * 字符串转LocalDateTime
     * 
     * @param text 日期字符串
     * @return LocalDateTime对象
     */
    public static LocalDateTime parseLocalDateTime(String text) {
        if (text == null || text.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 尝试使用标准日期时间格式解析
            return LocalDateTime.parse(text, DATE_TIME_FORMATTER);
        } catch (DateTimeParseException e) {
            try {
                // 尝试使用ISO标准格式解析
                return LocalDateTime.parse(text, ISO_DATE_TIME_FORMATTER);
            } catch (DateTimeParseException e1) {
                try {
                    // 尝试解析纯日期格式
                    return LocalDate.parse(text, DATE_FORMATTER).atStartOfDay();
                } catch (DateTimeParseException e2) {
                    throw new IllegalArgumentException("无法解析日期时间: " + text, e2);
                }
            }
        }
    }
    
    /**
     * LocalDateTime转字符串，使用标准格式
     * 
     * @param dateTime LocalDateTime对象
     * @return 格式化后的字符串
     */
    public static String formatLocalDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DATE_TIME_FORMATTER);
    }
    
    /**
     * Date转LocalDateTime
     * 
     * @param date Date对象
     * @return LocalDateTime对象
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }
    
    /**
     * LocalDateTime转Date
     * 
     * @param dateTime LocalDateTime对象
     * @return Date对象
     */
    public static Date toDate(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
    }
    
    /**
     * 时间戳(毫秒)转LocalDateTime
     * 
     * @param timestamp 时间戳，单位毫秒
     * @return LocalDateTime对象
     */
    public static LocalDateTime ofEpochMilli(long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
    }
    
    /**
     * LocalDateTime转时间戳(毫秒)
     * 
     * @param dateTime LocalDateTime对象
     * @return 时间戳，单位毫秒
     */
    public static long toEpochMilli(LocalDateTime dateTime) {
        if (dateTime == null) {
            return 0L;
        }
        return dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
    
    /**
     * 获取当天起始时间 (00:00:00)
     * 
     * @return 当天起始时间
     */
    public static LocalDateTime startOfDay() {
        return LocalDate.now().atStartOfDay();
    }
    
    /**
     * 获取指定日期的起始时间 (00:00:00)
     * 
     * @param date 指定日期
     * @return 起始时间
     */
    public static LocalDateTime startOfDay(LocalDate date) {
        if (date == null) {
            return null;
        }
        return date.atStartOfDay();
    }
} 