<template>
  <AppLayout>
    <!-- 搜索区域 -->
    <template #search>
      <SearchCard>
        <!-- 修改为四列布局 -->
        <div class="search-form">
          <!-- 第一列 -->
          <div class="search-item">
            <div class="search-label">店铺</div>
            <el-select v-model="queryParams.shopIds" placeholder="请选择店铺" clearable @change="handleShopChange"
              @clear="handleClearShops" size="small" class="search-input" multiple collapse-tags filterable
              :filter-method="filterShops">
              <!-- 添加全选选项 -->
              <el-option key="select-all" label="全选" :value="-1" />
              <el-option v-for="shop in filteredShops" :key="shop.shopId" :label="shop.shopName" :value="shop.shopId" />
            </el-select>
          </div>

          <!-- 第二列 -->
          <div class="search-item">
            <div class="search-label">SKU</div>
            <TagInput v-model="queryParams.productSkuIdList" placeholder="输入SKU (空格,逗号或回车分隔)" :is-numeric="true"
              class="search-input" size="small" />
          </div>

          <!-- 第三列 -->
          <div class="search-item">
            <div class="search-label">出库时间</div>
            <el-date-picker v-model="timeRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" value-format="YYYY-MM-DD" @change="handleTimeRangeChange" size="small"
              class="search-input date-picker-input" />
          </div>

          <!-- 第四列 -->
          <div class="search-item">
            <div class="search-label">退货包裹号</div>
            <TagInput v-model="queryParams.returnSupplierPackageNos" placeholder="输入退货包裹号 (空格,逗号或回车分隔)"
              :is-numeric="false" class="search-input" size="small" />
          </div>

          <!-- 第五列 -->
          <div class="search-item">
            <div class="search-label">备货单号</div>
            <TagInput v-model="queryParams.purchaseSubOrderSns" placeholder="输入备货单号 (空格,逗号或回车分隔)" :is-numeric="false"
              class="search-input" size="small" />
          </div>

          <!-- 空元素占位 -->
          <div class="search-item"></div>
          <div class="search-item"></div>
          
          <!-- 按钮区域 - 放在右下角 -->
          <div class="search-item search-buttons-container">
            <div class="search-buttons">
              <el-button type="primary" @click="handleQuery" size="small" class="action-button">
                <el-icon>
                  <Search />
                </el-icon> 搜索
              </el-button>
              <el-button @click="resetQuery" size="small" class="action-button">
                <el-icon>
                  <Refresh />
                </el-icon> 重置
              </el-button>
              <el-button type="primary" @click="handleBackendExport" size="small" class="action-button">
                <el-icon>
                  <Download />
                </el-icon> 导出Excel
              </el-button>
            </div>
          </div>
        </div>
      </SearchCard>
    </template>

    <!-- 排序区域 -->
    <div class="sort-container">
      <span class="sort-label">排序：</span>
      <el-radio-group v-model="sortOption" size="small" @change="handleSortChange">
        <el-radio-button label="outboundTime">按出库时间</el-radio-button>
        <el-radio-button label="shopName">按店铺名称</el-radio-button>
      </el-radio-group>
      <el-radio-group v-model="sortDirection" size="small" @change="handleSortChange" style="margin-left: 10px;">
        <el-radio-button label="ascending">
          <el-icon>
            <Sort />
          </el-icon> 升序
        </el-radio-button>
        <el-radio-button label="descending">
          <el-icon>
            <Sort />
          </el-icon> 降序
        </el-radio-button>
      </el-radio-group>
    </div>

    <!-- 表格内容区域 -->
    <template #table>
      <TableCard>
        <!-- 表格容器 -->
        <el-table :loading="loading" :data="sortedRefundPackages" border style="width: 100%" size="small"
          class="compact-table" @sort-change="handleTableSortChange" fit>
          <template #empty>
            <EmptyTips message="请选择店铺后点击搜索按钮查询数据（已默认设置最近7天）" />
          </template>
          <el-table-column type="index" label="序号" min-width="50" width="50" align="center" />
          <el-table-column prop="shopName" label="店铺信息" min-width="120" sortable="custom">
            <template #default="scope">
              <div class="shop-info">
                <div class="shop-name">{{ scope.row.shopName }}</div>
                <div v-if="scope.row.shopRemark" class="shop-remark">{{ scope.row.shopRemark }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="productSpuId" label="SPU" min-width="80" />
          <el-table-column prop="productSkuId" label="SKU" min-width="80" />
          <el-table-column label="商品信息" min-width="200">
            <template #default="scope">
              <div class="product-info">
                <img :src="scope.row.thumbUrl" class="product-image" alt="商品图片"
                  @click="handlePreviewImage(scope.row.thumbUrl)">
                <div class="product-details">
                  <div class="product-skc">SKC: {{ scope.row.productSkcId }}</div>
                  <div class="product-attr">属性集: {{ scope.row.mainSaleSpec }}-{{ scope.row.secondarySaleSpec }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="货号" min-width="100">
            <template #default="scope">
              {{ scope.row.extCode }}
            </template>
          </el-table-column>
          <el-table-column prop="purchaseSubOrderSn" label="备货单号" min-width="140" show-overflow-tooltip />
          <el-table-column label="退货原因" min-width="120" show-overflow-tooltip>
            <template #default="scope">
              {{ formatReasonDesc(scope.row.reasonDesc) }}
            </template>
          </el-table-column>
          <el-table-column prop="packageSn" label="退货包裹号" min-width="170" show-overflow-tooltip />
          <el-table-column prop="quantity" label="SKU件数" min-width="70" width="70" align="center" />
          <el-table-column prop="outboundTime" label="出库时间" min-width="150" sortable="custom">
            <template #default="scope">
              {{ scope.row.outboundTime }}
            </template>
          </el-table-column>
          <!-- <el-table-column label="操作" width="100" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="handleViewDetail(scope.row)">
                查看抽检记录
              </el-button>
            </template>
          </el-table-column> -->
        </el-table>
      </TableCard>
    </template>

    <!-- 固定在底部的分页区域 -->
    <template #pagination>
      <PaginationBar :current-page="Number(queryParams.pageNo)" :page-size="Number(queryParams.pageSize)" :total="total"
        @update:current-page="(val) => queryParams.pageNo = Number(val)"
        @update:page-size="(val) => queryParams.pageSize = Number(val)" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </template>

    <!-- 弹窗区域 -->
    <template #dialogs>
      <ImagePreview v-model:visible="imagePreviewVisible" :image-url="currentPreviewImage" />

      <!-- 导出对话框 -->
      <ExportDialog
        v-model:visible="exportDialog.visible"
        data-type="refund"
        :default-file-name="exportDialog.fileName"
        :current-page-data="refundPackages"
        :total="total"
        :fetch-data-fn="fetchRefundData"
        :query-params="queryParams"
        :use-backend-export="true"
      />
    </template>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, onUnmounted, watch } from 'vue'
import type { Ref } from 'vue'
import { Search, Refresh, Sort, Download, Warning, Delete, InfoFilled } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useLocalRefundStore } from '@/store'
import { useExportTaskStore } from '@/store'
import type { Shop } from '@/types/refund'
import type { LocalRefundPackageItem } from '@/types/local/refund'
import {
  AppLayout,
  SearchCard,
  TableCard,
  PaginationBar,
  ImagePreview,
  EmptyTips,
  TagInput,
  ExportDialog
} from '@/components/temu'
import { useRouter } from 'vue-router'
import {
  getLocalRefundPackageList,
  getUserShops,
  createRefundExportTask,
  getRefundExportProgress,
  downloadExcelFile
} from '@/api/local/refund'
import axios from 'axios'
import { getToken } from '@/utils/auth'

// 本地退货明细store
const localRefundStore = useLocalRefundStore()
// 导出任务store
const exportTaskStore = useExportTaskStore()
const router = useRouter()

// 加载状态
const loading = computed(() => localRefundStore.loading)

// 店铺列表
const shops: Ref<Shop[]> = ref([])

// 退货包裹列表
const refundPackages = computed<LocalRefundPackageItem[]>(() => {
  return localRefundStore.refundResult?.items || []
})

// 排序选项
const sortOption = ref('outboundTime')
const sortDirection = ref('descending')

// 按排序选项处理后的列表
const sortedRefundPackages = computed<LocalRefundPackageItem[]>(() => {
  if (!refundPackages.value || refundPackages.value.length === 0) {
    return []
  }

  // 创建一个新数组进行排序
  const sortedData = [...refundPackages.value]

  if (sortOption.value === 'outboundTime') {
    // 按出库时间排序
    sortedData.sort((a, b) => {
      const timeA = new Date(a.outboundTime).getTime()
      const timeB = new Date(b.outboundTime).getTime()
      return sortDirection.value === 'ascending' ? timeA - timeB : timeB - timeA
    })
  } else if (sortOption.value === 'shopName') {
    // 按店铺名称排序
    sortedData.sort((a, b) => {
      // 先按店铺名称排序
      const compareShop = a.shopName.localeCompare(b.shopName)

      if (compareShop === 0) {
        // 如果店铺名称相同，再按出库时间排序
        const timeA = new Date(a.outboundTime).getTime()
        const timeB = new Date(b.outboundTime).getTime()
        return sortDirection.value === 'ascending' ? timeA - timeB : timeB - timeA
      }

      // 否则返回店铺名称的排序结果
      return sortDirection.value === 'ascending' ? compareShop : -compareShop
    })
  }

  return sortedData
})

// 总记录数
const total = computed(() => localRefundStore.refundResult?.total || 0)

// 查询参数
const queryParams = reactive({
  shopIds: [] as number[],
  pageNo: 1,
  pageSize: 10,
  outboundTimeStart: undefined as string | undefined,
  outboundTimeEnd: undefined as string | undefined,
  productSkuIdList: [] as number[],
  returnSupplierPackageNos: [] as string[],
  purchaseSubOrderSns: [] as string[]
})

// 日期范围
const timeRange = ref<[string, string] | null>(null)

// 图片预览
const imagePreviewVisible = ref(false)
const currentPreviewImage = ref('')

// 导出相关对话框数据
const exportDialog = reactive({
  visible: false,
  fileName: `本地退货明细-${new Date().getTime()}`
})

// 添加全选状态跟踪变量
const isAllSelected = ref(false)

// 添加过滤后的店铺列表
const filteredShops = ref<Shop[]>([])
// 用于存储过滤搜索关键词
const filterKeyword = ref('')

// 封装获取退货数据的函数供导出组件使用
const fetchRefundData = async (params: any) => {
  try {
    // 构造导出请求参数
    const exportParams = {
      ...params,
      queryParams: {
        ...queryParams,
        ...(params.queryParams || {})
      }
    };
    
    console.log('创建导出任务，参数:', JSON.stringify(exportParams));
    
    // 使用传入的taskId，不要重复创建任务
    try {
      // 创建导出任务
      const response = await createRefundExportTask(exportParams);
      console.log('创建导出任务响应:', JSON.stringify(response));
      
      // 尝试不同方式获取任务ID
      let taskId: string | undefined = undefined;
      
      if (response && response.data) {
        console.log('响应数据结构:', JSON.stringify(response.data));
        const data = response.data as any;
        taskId = data.exportTaskId || data.taskId || data.id;
      } 
      
      console.log('提取到的任务ID:', taskId);
      
      // 检查是否成功获取任务ID
      if (taskId) {
        console.log('成功获取导出任务ID:', taskId);
        ElMessage.success('创建导出任务成功，正在导出...');
        // 开始轮询导出进度
        startPollingExportProgress(taskId, params.taskId);
        return { success: true, data: { taskId } };
      } else {
        console.error('无法获取导出任务ID:', response);
        ElMessage.error('创建导出任务失败：无法获取任务ID');
        exportTaskStore.failTask(params.taskId, '无法获取任务ID');
        return { success: false };
      }
    } catch (error: any) {
      console.error('创建导出任务失败:', error);
      let errorMsg = '创建导出任务失败';
      
      if (error.response && error.response.data) {
        // 尝试从错误响应中获取信息
        errorMsg += ': ' + (error.response.data.message || JSON.stringify(error.response.data));
      } else if (error.message) {
        errorMsg += ': ' + error.message;
      }
      
      ElMessage.error(errorMsg);
      exportTaskStore.failTask(params.taskId, errorMsg);
      return { success: false };
    }
  } catch (e: any) {
    console.error('导出过程发生异常:', e);
    const errorMsg = '导出过程发生异常: ' + (e instanceof Error ? e.message : '未知错误');
    ElMessage.error(errorMsg);
    return { success: false };
  }
};

// 处理表格排序变更
const handleTableSortChange = (params: any) => {
  if (params.prop) {
    sortOption.value = params.prop
    sortDirection.value = params.order || 'descending'
  }
}

// 处理排序变更
const handleSortChange = () => {
  // 当用户切换排序选项时，刷新排序结果
  console.log(`排序方式改变: ${sortOption.value}, 方向: ${sortDirection.value}`)
}

// 格式化退货原因描述
const formatReasonDesc = (desc: string) => {
  if (!desc) return '无'
  // 移除多余的退货原因前缀
  const cleanDesc = desc.replace(/退货原因：/g, '')
  return cleanDesc
}

// 获取店铺列表
const getShops = async () => {
  try {
    const result = await localRefundStore.getUserShops()
    shops.value = result || []
    // 初始化过滤后的店铺列表
    filteredShops.value = [...shops.value]

    // 如果只有一个店铺且没有选择店铺，自动选择该店铺
    if (shops.value.length === 1 && queryParams.shopIds.length === 0) {
      queryParams.shopIds = [shops.value[0].shopId]
      // 重新加载数据
      if (queryParams.shopIds.length > 0) {
        fetchData()
      }
    }
  } catch (error) {
    console.error('获取店铺列表失败:', error)
    ElMessage.error('获取店铺列表失败')
  }
}

// 过滤店铺方法
const filterShops = (query: string) => {
  filterKeyword.value = query
  if (query) {
    filteredShops.value = shops.value.filter(shop => 
      shop.shopName.toLowerCase().includes(query.toLowerCase())
    )
    
    // 当过滤条件发生变化时，重置全选状态
    isAllSelected.value = false
    
    // 如果当前已选中店铺，保留它们（如果它们在过滤结果中）
    if (queryParams.shopIds && queryParams.shopIds.length > 0) {
      // 保留那些在过滤结果中的店铺ID
      queryParams.shopIds = queryParams.shopIds.filter(id => 
        filteredShops.value.some(shop => shop.shopId === id)
      )
    }
  } else {
    filteredShops.value = [...shops.value]
    
    // 检查是否全部店铺都被选中
    if (queryParams.shopIds && shops.value.length > 0) {
      isAllSelected.value = queryParams.shopIds.length === shops.value.length &&
        shops.value.every(shop => queryParams.shopIds.includes(shop.shopId))
    }
  }
}

// 处理清除所有店铺选择
const handleClearShops = () => {
  // 重置全选状态
  isAllSelected.value = false
  queryParams.shopIds = []
  localRefundStore.setShopIds([])
}

// 处理店铺变更
const handleShopChange = (shopIds: number[]) => {
  // 检查是否点击了全选选项
  if (shopIds.includes(-1)) {
    // 如果当前不是全选状态，则进行全选
    if (!isAllSelected.value) {
      // 标记为全选状态
      isAllSelected.value = true
      
      // 全选所有店铺（过滤后的）
      let allShopIds: number[] = []
      
      // 如果有搜索关键词，只选择过滤后的店铺
      if (filterKeyword.value) {
        allShopIds = filteredShops.value.map(shop => shop.shopId)
      } else {
        // 否则选择所有店铺
        allShopIds = shops.value.map(shop => shop.shopId)
      }
      
      // 更新选中的店铺，并移除全选选项值(-1)
      queryParams.shopIds = allShopIds
      
      // 避免-1被包含在选中项中
      setTimeout(() => {
        if (queryParams.shopIds && queryParams.shopIds.includes(-1)) {
          const index = queryParams.shopIds.indexOf(-1)
          if (index > -1) {
            queryParams.shopIds.splice(index, 1)
          }
        }
      }, 0)
    } else {
      // 如果当前已经是全选状态，则取消全选
      isAllSelected.value = false
      queryParams.shopIds = []
    }
  } else {
    // 如果选择了具体的店铺且数量与总店铺数相同，标记为全选状态
    const totalShops = filterKeyword.value ? filteredShops.value.length : shops.value.length
    isAllSelected.value = shopIds.length > 0 && shopIds.length === totalShops
    
    // 更新选中的店铺
    queryParams.shopIds = shopIds
  }
  
  // 调用store方法
  localRefundStore.setShopIds(queryParams.shopIds)
  console.log('选择店铺:', queryParams.shopIds, '全选状态:', isAllSelected.value)
}

// 设置默认时间范围（最近7天）
const setDefaultTimeRange = () => {
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - 7)

  const formatDate = (date: Date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  timeRange.value = [formatDate(startDate), formatDate(endDate)]
  queryParams.outboundTimeStart = timeRange.value[0]
  queryParams.outboundTimeEnd = timeRange.value[1]
}

// 查询数据
const fetchData = async () => {
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请选择至少一个店铺')
    return
  }

  try {
    await localRefundStore.getLocalRefundPackages(queryParams)
  } catch (error) {
    console.error('获取退货明细失败:', error)
    ElMessage.error('获取退货明细失败')
  }
}

// 处理查询
const handleQuery = () => {
  queryParams.pageNo = 1
  fetchData()
}

// 重置查询
const resetQuery = () => {
  queryParams.productSkuIdList = []
  queryParams.returnSupplierPackageNos = []
  queryParams.purchaseSubOrderSns = []
  timeRange.value = null
  queryParams.outboundTimeStart = undefined
  queryParams.outboundTimeEnd = undefined
  queryParams.shopIds = []
  // 重置全选状态
  isAllSelected.value = false
  setDefaultTimeRange()
}

// 处理时间范围变更
const handleTimeRangeChange = (val: [string, string] | null) => {
  if (val) {
    queryParams.outboundTimeStart = val[0]
    queryParams.outboundTimeEnd = val[1]
  } else {
    queryParams.outboundTimeStart = undefined
    queryParams.outboundTimeEnd = undefined
  }
  localRefundStore.setTimeRange(val)
}

// 处理页大小变更
const handleSizeChange = (val: number) => {
  queryParams.pageSize = val
  fetchData()
}

// 处理当前页变更
const handleCurrentChange = (val: number) => {
  queryParams.pageNo = val
  fetchData()
}

// 处理预览图片
const handlePreviewImage = (url: string) => {
  if (!url) return
  currentPreviewImage.value = url
  imagePreviewVisible.value = true
}

// 跳转到抽检明细
const handleViewDetail = (row: LocalRefundPackageItem) => {
  // 构建跳转参数
  const routeParams = {
    skuId: row.productSkuId,
    shopId: row.shopId // 使用行数据中的shopId
  }

  // 导航到抽检明细页面
  router.push({
    path: '/local/qc-detail',
    query: routeParams
  })
}

// 开始轮询导出进度
const startPollingExportProgress = (taskId: string, exportTaskId: string) => {
  console.log('开始轮询导出进度, 任务ID:', taskId)
  
  if (!taskId) {
    console.error('轮询失败：无效的任务ID')
    ElMessage.error('无法获取导出进度：无效的任务ID')
    exportTaskStore.failTask(exportTaskId, '无效的任务ID')
    return
  }
  
  // 定义取消功能
  let cancelled = false
  const cancelFn = () => {
    cancelled = true
  }
  
  // 设置取消函数
  exportTaskStore.setTaskCancelFn(exportTaskId, cancelFn)
  
  // 直接使用window.setInterval，避免类型问题
  const pollingTimer = window.setInterval(() => {
    // 如果任务已被取消，停止轮询
    if (cancelled) {
      clearInterval(pollingTimer)
      return
    }
    
    console.log('发送进度查询请求:', taskId)
    
    getRefundExportProgress(taskId)
      .then(response => {
        // 如果任务已被取消，停止处理
        if (cancelled) {
          clearInterval(pollingTimer)
          return
        }
        
        console.log('获取进度响应:', JSON.stringify(response))
        
        // 根据实际返回的数据结构提取进度信息
        let progressData: any = null
        
        if (response) {
          if (response.data) {
            progressData = response.data
          } else {
            progressData = response
          }
        }
        
        if (!progressData) {
          console.error('无法解析进度数据:', response)
          return
        }
        
        // 更新进度
        const progress = progressData.progress || 0
        const message = progressData.message || `正在导出数据 (${progress}%)...`
        
        // 更新任务进度
        exportTaskStore.updateTaskProgress(exportTaskId, progress)
        
        console.log(`导出进度: ${progress}%, 状态: ${progressData.status}, 消息: ${message || '无'}`)
        
        // 判断任务状态
        const status = (progressData.status || '').toUpperCase()
        
        // 导出完成
        if (status === 'COMPLETED' || status === 'COMPLETE') {
          console.log('导出任务完成，准备下载文件')
          
          clearInterval(pollingTimer)
          
          // 提取可能的任务ID
          const downloadTaskId = progressData.exportTaskId || progressData.taskId || taskId
          
          // 下载文件
          downloadFile(downloadTaskId, exportTaskId)
          exportTaskStore.completeTask(exportTaskId)
          ElMessage.success('导出成功')
        } 
        // 导出失败
        else if (status === 'FAILED' || status === 'FAIL' || status === 'ERROR') {
          console.error('导出任务失败:', progressData.message || progressData.errorMessage || '未知错误')
          
          clearInterval(pollingTimer)
          
          exportTaskStore.failTask(exportTaskId, progressData.message || progressData.errorMessage || '导出失败')
          ElMessage.error(progressData.message || progressData.errorMessage || '导出失败')
        }
      })
      .catch((error) => {
        // 如果任务已被取消，停止处理
        if (cancelled) {
          clearInterval(pollingTimer)
          return
        }
        
        console.error('获取导出进度失败:', error)
        
        // 不要立即清除定时器，尝试多次
        if (error.message && error.message.includes('404')) {
          console.error('任务不存在，停止轮询')
          clearInterval(pollingTimer)
          ElMessage.error('导出任务不存在，请重试')
          exportTaskStore.failTask(exportTaskId, '导出任务不存在')
        }
      })
  }, 2000) // 修改为2秒一次，避免过于频繁
}

// 下载导出的Excel文件
async function downloadFile(taskId: string, exportTaskId: string) {
  try {
    const downloadingMessage = ElMessage({
      message: '正在下载文件，请稍候...',
      type: 'info',
      duration: 0
    })
    
    try {
      const token = getToken();
      
      // 创建下载链接
      const downloadUrl = `/api/local/returnDetails/download/${taskId}`;
      
      // 使用fetch API发起带认证头的请求
      const response = await fetch(downloadUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      // 关闭下载提示
      downloadingMessage.close();
      
      // 检查响应状态
      if (!response.ok) {
        // 尝试解析错误响应
        try {
          const errorData = await response.json();
          ElMessage.error(errorData.message || `下载失败: ${response.status} ${response.statusText}`);
          exportTaskStore.failTask(exportTaskId, errorData.message || '下载失败');
        } catch (parseError) {
          ElMessage.error(`下载失败: ${response.status} ${response.statusText}`);
          exportTaskStore.failTask(exportTaskId, `下载失败: ${response.status}`);
        }
        return;
      }
      
      // 获取文件名 - 改进文件名处理
      let filename = '本地退货明细.xlsx'; // 默认文件名
      
      // 1. 优先使用导出对话框中设置的文件名
      if (exportDialog.fileName && exportDialog.fileName.trim() !== '') {
        filename = `${exportDialog.fileName}.xlsx`;
      }
      
      // 2. 尝试从Content-Disposition获取文件名并正确解码
      const contentDisposition = response.headers.get('Content-Disposition');
      if (contentDisposition) {
        // 匹配filename或filename*=utf-8''格式
        const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
        const filenameUtf8Regex = /filename\*=UTF-8''([^;]*)/i;
        const filenameUtf8Regex2 = /filename\*=utf-8''([^;]*)/i;
        
        // 先尝试获取UTF-8编码的文件名
        const utf8Match = filenameUtf8Regex.exec(contentDisposition) || filenameUtf8Regex2.exec(contentDisposition);
        if (utf8Match && utf8Match[1]) {
          try {
            // 对URL编码的文件名进行解码
            filename = decodeURIComponent(utf8Match[1]);
            console.log('从Content-Disposition获取到UTF-8文件名:', filename);
          } catch (e) {
            console.error('解码文件名失败:', e);
          }
        } else {
          // 尝试获取普通文件名
          const match = filenameRegex.exec(contentDisposition);
          if (match && match[1]) {
            let extractedName = match[1].replace(/['"]/g, '');
            
            // 尝试处理可能的URL编码
            try {
              if (extractedName.includes('%')) {
                extractedName = decodeURIComponent(extractedName);
              }
              filename = extractedName;
              console.log('从Content-Disposition获取到文件名:', filename);
            } catch (e) {
              console.error('处理文件名失败:', e);
            }
          }
        }
      }
      
      // 确保文件名以.xlsx结尾
      if (!filename.toLowerCase().endsWith('.xlsx')) {
        filename += '.xlsx';
      }
      
      console.log('最终使用的文件名:', filename);
      
      // 将响应转换为blob
      const blob = await response.blob();
      
      // 检查blob大小，过小可能是错误
      if (blob.size < 100) {
        console.warn('下载的文件过小，可能不是有效Excel文件:', blob.size, '字节');
        ElMessage.warning('下载的文件可能不完整，请稍后重试');
        return;
      }
      
      // 创建下载链接并触发下载
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      
      // 清理资源
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      console.log('文件下载成功');
      ElMessage.success('文件下载已开始，请查看浏览器下载管理器');
    } catch (error: any) {
      // 关闭下载提示
      downloadingMessage.close();
      
      console.error('下载文件失败:', error);
      ElMessage.error('下载文件失败: ' + (error.message || '未知错误'));
      exportTaskStore.failTask(exportTaskId, '下载失败: ' + (error.message || '未知错误'));
      
      // 尝试使用备用方式下载
      fallbackDownload(taskId, exportTaskId);
    }
  } catch (error) {
    console.error('下载文件时发生异常:', error);
    ElMessage.error('下载文件失败，正在尝试备用方式...');
    
    // 尝试使用备用方式下载
    fallbackDownload(taskId, exportTaskId);
  }
}

// 备用下载方法
const fallbackDownload = (taskId: string, exportTaskId: string) => {
  console.log('使用备用方式下载文件')
  
  // 构建下载URL
  const baseUrl = import.meta.env.VITE_API_BASE_URL || ''
  const downloadUrl = `${baseUrl}/local/returnDetails/download/${taskId}`
  
  // 打开新窗口下载
  const token = getToken()
  if (token) {
    // 使用fetch API下载
    fetch(downloadUrl, {
      headers: {
        'Authorization': 'Bearer ' + token
      }
    })
    .then(response => response.blob())
    .then(blob => {
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${exportDialog.fileName}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // 清理URL对象
      setTimeout(() => {
        URL.revokeObjectURL(url)
      }, 100)
      
      console.log('备用方式下载成功')
    })
    .catch(error => {
      console.error('备用下载失败:', error)
      exportTaskStore.failTask(exportTaskId, '备用下载失败: ' + error.message)
      
      // 最后尝试直接用窗口打开
      window.open(downloadUrl, '_blank')
    })
  } else {
    // 没有token，直接打开
    window.open(downloadUrl, '_blank')
  }
}

// 处理后端导出
const handleBackendExport = () => {
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请选择至少一个店铺')
    return
  }

  if (loading.value || refundPackages.value.length === 0) {
    ElMessage.warning('没有可导出的数据')
    return
  }
  
  // 生成默认文件名：店铺备注 + 日期时间
  let fileName = '本地退货明细';
  
  // 获取选中店铺的备注
  if (shops.value && shops.value.length > 0 && queryParams.shopIds.length > 0) {
    // 查找选中店铺的备注
    const selectedShops = shops.value.filter(shop => queryParams.shopIds.includes(shop.shopId));
    if (selectedShops.length > 0) {
      // 提取店铺备注，最多使用3个店铺备注，避免文件名过长
      const shopRemarks = selectedShops.slice(0, 3).map(shop => shop.remark || shop.shopName);
      fileName = shopRemarks.join('-');
      
      // 如果选择的店铺超过3个，添加省略标记
      if (selectedShops.length > 3) {
        fileName += '等';
      }
    }
  }
  
  // 添加日期时间后缀
  const now = new Date();
  const dateStr = now.getFullYear() + 
    (now.getMonth() + 1).toString().padStart(2, '0') + 
    now.getDate().toString().padStart(2, '0');
  const timeStr = now.getHours().toString().padStart(2, '0') + 
    now.getMinutes().toString().padStart(2, '0');
  
  fileName += '_' + dateStr + timeStr;
  
  console.log('导出文件名:', fileName); // 添加调试输出
  
  exportDialog.fileName = fileName;
  exportDialog.visible = true;
}

// 页面加载时执行
onMounted(() => {
  // 设置默认时间范围（最近7天）
  setDefaultTimeRange()

  // 获取店铺列表
  getShops()

})

// 组件销毁时清理
onUnmounted(() => {
  localRefundStore.resetQuery()
})
</script>

<style scoped>
/* 搜索表单新样式 - 四列布局 */
.search-form {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 15px;
  align-items: start;
  position: relative;
  padding-bottom: 10px;
}

.search-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.search-label {
  font-size: 13px;
  color: #606266;
}

.search-input {
  width: 100%;
}

.date-picker-input {
  width: 100%;
}

/* 按钮容器定位到右下角 */
.search-buttons-container {
  grid-column: 4;
  justify-self: end;
  align-self: end;
  margin-top: 10px;
}

.search-buttons {
  display: flex;
  gap: 8px;
}

/* 排序区域样式 */
.sort-container {
  margin: 10px 0;
  padding: 0 20px;
  display: flex;
  align-items: center;
}

.sort-label {
  margin-right: 10px;
  color: #606266;
  font-size: 14px;
}

/* 商品信息样式 */
.product-info {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.product-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
}

.product-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-skc,
.product-attr,
.product-ext-code {
  font-size: 12px;
  color: #606266;
}

/* 表格自适应样式 */
.compact-table :deep(.el-table__row) {
  height: 70px;
}

.compact-table :deep(.cell) {
  padding-left: 5px;
  padding-right: 5px;
  white-space: normal;
  line-height: 1.3;
}

.compact-table :deep(.el-table__header th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
  text-align: center;
}

.compact-table :deep(.el-table--border th) {
  padding: 8px 0;
}

/* 确保表格能够自适应容器 */
:deep(.el-table) {
  width: 100% !important;
  table-layout: fixed;
}

:deep(.el-table__body-wrapper) {
  overflow-x: auto;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .search-form {
    grid-template-columns: 1fr;
  }
  
  .search-buttons-container {
    grid-column: 1;
    justify-self: center;
  }

  .sort-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

/* 导出提示样式 */
.export-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 导出信息块样式 */
.export-info-block {
  margin-top: 15px;
}

/* 店铺信息样式 */
.shop-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.shop-name {
  font-size: 14px;
  font-weight: bold;
}

.shop-remark {
  font-size: 14px;
  color: #409EFF;
  line-height: 1.3;
  word-break: break-all;
}
</style>