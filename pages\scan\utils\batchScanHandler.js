/**
 * 批量扫码处理功能
 */
const productionProgressApi = require('../../../api/productionProgressApi');
const userRoleManager = require('./userRoleManager');

/**
 * 处理批量扫码结果
 */
function handleBatchScanResult(page, batchData) {
  console.log('处理批量扫码结果:', batchData);
  
  if (!batchData.orders || !Array.isArray(batchData.orders) || batchData.orders.length === 0) {
    console.error('批量扫码数据不完整:', batchData);
    wx.showModal({
      title: '数据不完整',
      content: '批量扫码数据不完整或格式不正确',
      showCancel: false
    });
    return;
  }
  
  // 确保这是批量处理类型
  if (batchData.type !== 'batch') {
    console.error('不是批量处理类型:', batchData);
    wx.showModal({
      title: '类型错误',
      content: '不是有效的批量处理类型',
      showCancel: false
    });
    return;
  }
  
  // 如果启用了快速扫码模式，则直接进入批量处理流程
  if (page.data.fastScanEnabled) {
    // 获取用户选中的角色
    const selectedRoleTypes = userRoleManager.getSelectedUserRoleTypes(page);
    const { validProgressRoles } = page.data;
    
    // 如果没有选择角色，无法继续处理
    if (!selectedRoleTypes || selectedRoleTypes.length === 0) {
      console.error('用户没有选择角色，无法处理批量订单');
      wx.showModal({
        title: '请选择角色',
        content: '请先选择要处理的工序角色',
        showCancel: false,
        success: () => page.backToScan()
      });
      return;
    }
    
    // 筛选出有效的生产进度角色
    const validUserRoleKeys = selectedRoleTypes.filter(roleKey => 
      validProgressRoles.includes(roleKey)
    );
    console.log('用户可用于生产进度的角色键:', validUserRoleKeys);
    
    // 如果没有可用工序，无法继续处理
    if (validUserRoleKeys.length === 0) {
      console.error('用户没有可用于生产进度的工序');
      wx.showModal({
        title: '缺少权限',
        content: '您选择的角色中没有可操作的生产工序，无法处理批量订单',
        showCancel: false,
        success: () => page.backToScan()
      });
      return;
    }
    
    // 使用工序名称映射
    const progressNames = validUserRoleKeys.map(key => page.data.roleProgressMap[key] || key);
    
    // 显示合并后的确认对话框，包含备货单数量和要处理的工序
    const orderCount = batchData.orders.length;
    wx.showModal({
      title: '批量处理确认',
      content: `检测到批量处理二维码，包含${orderCount}个备货单，\n\n将完成以下工序：${progressNames.join('、')}`,
      confirmText: '确认处理',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 直接执行批量处理，不再显示第二个弹窗
          executeBatchProgress(page, batchData.orders, validUserRoleKeys);
        } else {
          console.log('用户取消批量处理');
          page.backToScan();
        }
      }
    });
  } else {
    // 非快速扫码模式下，显示批量备货单信息界面
    console.log('非快速扫码模式，准备显示批量备货单信息');
    loadBatchOrdersData(page, batchData.orders);
  }
}

/**
 * 加载批量备货单数据
 */
function loadBatchOrdersData(page, orders) {
  if (!orders || orders.length === 0) {
    console.error('没有有效的备货单数据');
    wx.showToast({
      title: '没有有效的备货单数据',
      icon: 'none'
    });
    return;
  }
  
  console.log('开始加载批量备货单数据，数量:', orders.length);
  wx.showLoading({ title: '加载中...' });
  
  // 初始化进度列表
  const batchProgressList = orders.map(() => ({}));
  
  // 获取第一个备货单的进度信息
  loadOrderProgressDetail(page, orders[0], 0, orders, batchProgressList);
}

/**
 * 加载指定备货单的进度详情
 */
function loadOrderProgressDetail(page, order, index, allOrders, progressList) {
  const { shopId, subPurchaseOrderSn } = order;
  
  console.log(`加载第${index + 1}个备货单进度信息:`, order);
  
  // 查询备货单生产进度
  productionProgressApi.getProgressDetail(shopId, subPurchaseOrderSn)
    .then(res => {
      console.log(`获取到备货单${subPurchaseOrderSn}的进度信息:`, res);
      
      // 保存进度信息到列表
      progressList[index] = res;
      
      // 检查是否所有备货单都已加载完成
      if (index === allOrders.length - 1) {
        // 所有备货单加载完成，显示批量备货单界面
        displayBatchOrdersUI(page, allOrders, progressList);
      } else {
        // 加载下一个备货单信息
        loadOrderProgressDetail(
          page, 
          allOrders[index + 1], 
          index + 1, 
          allOrders, 
          progressList
        );
      }
    })
    .catch(err => {
      console.error(`获取备货单${subPurchaseOrderSn}进度信息失败:`, err);
      
      // 标记该备货单加载失败
      progressList[index] = { error: true, message: err.message || '加载失败' };
      
      // 继续加载下一个，不中断流程
      if (index < allOrders.length - 1) {
        loadOrderProgressDetail(
          page,
          allOrders[index + 1], 
          index + 1, 
          allOrders, 
          progressList
        );
      } else {
        // 所有备货单处理完毕，显示界面
        displayBatchOrdersUI(page, allOrders, progressList);
      }
    });
}

/**
 * 显示批量备货单界面
 */
function displayBatchOrdersUI(page, orders, progressList) {
  console.log('准备显示批量备货单界面, 备货单数量:', orders.length);
  console.log('备货单列表:', orders);
  console.log('进度信息列表:', progressList);
  
  wx.hideLoading();
  
  // 检查是否有有效进度信息
  const hasValidProgress = progressList.some(progress => progress && !progress.error);
  
  if (!hasValidProgress) {
    console.error('没有获取到有效的进度信息');
    wx.showModal({
      title: '获取失败',
      content: '无法获取备货单进度信息',
      showCancel: false,
      success: () => page.resetScannerState()
    });
    return;
  }
  
  // 构建第一个备货单的结果对象
  const firstOrder = orders[0];
  const firstProgress = progressList[0];
  
  const scanResult = {
    subPurchaseOrderSn: firstOrder.subPurchaseOrderSn,
    shopId: firstOrder.shopId,
    progressDetail: firstProgress,
    batchCount: orders.length, // 添加批量数量标记
    currentIndex: 0 // 当前显示的是第一个
  };
  
  // 设置状态，展示批量扫码结果
  page.setData({
    scanResult: scanResult,
    scanVisible: false,
    batchMode: true,
    batchOrders: orders,
    batchProgressList: progressList,
    currentBatchIndex: 0,
    selectedProgressTypes: [], // 清空选中的工序
    // 重置所有工序的强制更新变量
    forceBurningUpdate: 0,
    forceSewingUpdate: 0,
    forceTailUpdate: 0,
    forceShippingUpdate: 0,
    forceDeliveryUpdate: 0,
    forceInspectionUpdate: 0,
    pageVersion: Date.now() // 强制刷新视图
  });
  
  // 更新工序项目列表
  page.updateProgressItems();
  
  console.log('批量备货单界面设置完成:', page.data.scanResult);
}

/**
 * 切换显示下一个批量备货单
 */
function showNextBatchOrder(page) {
  const { currentBatchIndex, batchOrders, batchProgressList } = page.data;
  
  // 计算下一个索引，如果到达末尾则返回第一个
  const nextIndex = (currentBatchIndex + 1) % batchOrders.length;
  
  // 获取下一个备货单信息
  const nextOrder = batchOrders[nextIndex];
  const nextProgress = batchProgressList[nextIndex];
  
  // 构建结果对象
  const scanResult = {
    subPurchaseOrderSn: nextOrder.subPurchaseOrderSn,
    shopId: nextOrder.shopId,
    progressDetail: nextProgress,
    batchCount: batchOrders.length,
    currentIndex: nextIndex
  };
  
  // 更新状态
  page.setData({
    scanResult: scanResult,
    currentBatchIndex: nextIndex,
    selectedProgressTypes: [], // 切换备货单时清空选中的工序
    // 重置所有工序的强制更新变量
    forceBurningUpdate: 0,
    forceSewingUpdate: 0,
    forceTailUpdate: 0,
    forceShippingUpdate: 0,
    forceDeliveryUpdate: 0,
    forceInspectionUpdate: 0,
    pageVersion: Date.now() // 强制刷新视图
  });
  
  // 更新工序项目列表
  page.updateProgressItems();
  
  // 更新复选框状态，确保视觉上显示为未选中
  page.updateCheckboxStatus();
  
  console.log('切换到下一个批量备货单:', nextIndex, scanResult);
}

/**
 * 切换显示上一个批量备货单
 */
function showPrevBatchOrder(page) {
  const { currentBatchIndex, batchOrders, batchProgressList } = page.data;
  
  // 计算上一个索引，如果到达开头则返回最后一个
  const prevIndex = (currentBatchIndex - 1 + batchOrders.length) % batchOrders.length;
  
  // 获取上一个备货单信息
  const prevOrder = batchOrders[prevIndex];
  const prevProgress = batchProgressList[prevIndex];
  
  // 构建结果对象
  const scanResult = {
    subPurchaseOrderSn: prevOrder.subPurchaseOrderSn,
    shopId: prevOrder.shopId,
    progressDetail: prevProgress,
    batchCount: batchOrders.length,
    currentIndex: prevIndex
  };
  
  // 更新状态
  page.setData({
    scanResult: scanResult,
    currentBatchIndex: prevIndex,
    selectedProgressTypes: [], // 切换备货单时清空选中的工序
    // 重置所有工序的强制更新变量
    forceBurningUpdate: 0,
    forceSewingUpdate: 0,
    forceTailUpdate: 0,
    forceShippingUpdate: 0,
    forceDeliveryUpdate: 0,
    forceInspectionUpdate: 0,
    pageVersion: Date.now() // 强制刷新视图
  });
  
  // 更新工序项目列表
  page.updateProgressItems();
  
  // 更新复选框状态，确保视觉上显示为未选中
  page.updateCheckboxStatus();
  
  console.log('切换到上一个批量备货单:', prevIndex, scanResult);
}

/**
 * 提交当前批量备货单的选中工序
 */
function submitCurrentBatchProgress(page) {
  const { selectedProgressTypes, batchOrders, currentBatchIndex } = page.data;
  
  // 防重复提交
  if (page.data.processing) {
    console.log('正在处理中，防止重复提交');
    return;
  }
  
  // 检查是否选择了工序
  if (!selectedProgressTypes || selectedProgressTypes.length === 0) {
    console.log('未选择任何工序');
    wx.showToast({
      title: '请至少选择一个工序',
      icon: 'none'
    });
    return;
  }
  
  // 获取当前备货单
  const currentOrder = batchOrders[currentBatchIndex];
  
  console.log('提交当前批量备货单的选中工序:', selectedProgressTypes, 
    '备货单号:', currentOrder.subPurchaseOrderSn, 
    '店铺ID:', currentOrder.shopId,
    '批量索引:', currentBatchIndex + 1, '/', batchOrders.length
  );
  
  // 弹窗确认
  wx.showModal({
    title: '确认提交',
    content: `您选择了 ${selectedProgressTypes.length} 个工序，确认要提交完成吗？`,
    confirmText: '确认提交',
    cancelText: '再想想',
    success: (res) => {
      if (res.confirm) {
        console.log('用户确认提交当前批量备货单工序');
        // 更新当前备货单工序状态
        updateBatchOrderProgress(page, currentOrder, selectedProgressTypes);
      } else {
        console.log('用户取消提交');
      }
    }
  });
}

/**
 * 更新批量备货单的进度数据
 */
function updateBatchOrderProgress(page, order, progressTypes) {
  if (!progressTypes || progressTypes.length === 0) {
    console.error('未选择工序:', progressTypes);
    wx.showToast({
      title: '未选择工序',
      icon: 'none'
    });
    return;
  }
  
  console.log('准备更新批量备货单工序, 数量:', progressTypes.length, '工序列表:', progressTypes, {
    shopId: order.shopId,
    subPurchaseOrderSn: order.subPurchaseOrderSn
  });
  
  // 设置处理中状态并显示加载提示
  page.setData({ processing: true });
  wx.showLoading({ title: '提交中...' });
  
  // 跟踪成功和失败的工序
  let successCount = 0;
  let failedTypes = [];
  
  // 创建请求队列
  const requests = progressTypes.map(progressType => {
    // 准备请求参数
    const params = {
      shopId: order.shopId,
      subPurchaseOrderSn: order.subPurchaseOrderSn,
      progressType: progressType,
      operationType: "1", // 扫码确认完成工序，固定为"1"
      remarks: "" // 备注字段，可以为空字符串
    };
    
    console.log('创建批量备货单更新请求, 工序:', progressType, params);
    
    // 返回请求Promise
    return productionProgressApi.updateProgress(params)
      .then(res => {
        console.log('工序更新成功:', progressType, res);
        successCount++;
        return { type: progressType, success: true, response: res };
      })
      .catch(err => {
        console.error('工序更新失败:', progressType, err);
        failedTypes.push(progressType);
        return { type: progressType, success: false, error: err };
      });
  });
  
  // 执行所有请求
  Promise.all(requests)
    .then(results => {
      console.log('当前批量备货单工序更新请求完成, 结果数量:', results.length, '结果:', results);
      
      if (failedTypes.length === 0) {
        // 全部成功
        wx.showModal({
          title: '处理成功',
          content: `已完成工序：${progressTypes.map(type => page.data.roleProgressMap[type] || type).join('、')}`,
          showCancel: false,
          confirmText: '确定',
          confirmColor: '#1890ff',
          success: () => {
            // 重置选中状态
            page.setData({
              selectedProgressTypes: [],
              // 重置所有工序的强制更新变量
              forceBurningUpdate: 0,
              forceSewingUpdate: 0,
              forceTailUpdate: 0,
              forceShippingUpdate: 0,
              forceDeliveryUpdate: 0,
              forceInspectionUpdate: 0
            });
            
            // 更新工序项目列表
            page.updateProgressItems();
            
            // 刷新所有备货单的状态
            refreshAllBatchProgress(page);
          }
        });
      } else if (successCount > 0) {
        // 部分成功
        // 获取成功和失败的工序名称
        const successNames = results.filter(r => r.success).map(r => page.data.roleProgressMap[r.type] || r.type);
        const failedNames = results.filter(r => !r.success).map(r => page.data.roleProgressMap[r.type] || r.type);
        
        wx.showModal({
          title: '部分更新成功',
          content: `成功工序：${successNames.join('、')}\n\n失败工序：${failedNames.join('、')}`,
          showCancel: false,
          confirmText: '确定',
          confirmColor: '#1890ff',
          success: () => {
            // 重置选中状态
            page.setData({
              selectedProgressTypes: [],
              // 重置所有工序的强制更新变量
              forceBurningUpdate: 0,
              forceSewingUpdate: 0,
              forceTailUpdate: 0,
              forceShippingUpdate: 0,
              forceDeliveryUpdate: 0,
              forceInspectionUpdate: 0
            });
            
            // 更新工序项目列表
            page.updateProgressItems();
            
            // 刷新所有备货单的状态
            refreshAllBatchProgress(page);
          }
        });
      } else {
        // 全部失败
        // 获取所有失败的工序名称
        const failedNames = failedTypes.map(type => page.data.roleProgressMap[type] || type);
        
        wx.showModal({
          title: '更新失败',
          content: `更新失败工序：${failedNames.join('、')}`,
          showCancel: false,
          confirmText: '确定',
          confirmColor: '#1890ff',
          success: () => {
            // 设置处理完成状态
            page.setData({ 
              processing: false,
              selectedProgressTypes: [],
              // 重置所有工序的强制更新变量
              forceBurningUpdate: 0,
              forceSewingUpdate: 0,
              forceTailUpdate: 0,
              forceShippingUpdate: 0,
              forceDeliveryUpdate: 0,
              forceInspectionUpdate: 0
            });
          }
        });
      }
      
      wx.hideLoading();
    })
    .catch(err => {
      console.error('工序更新过程中发生未处理错误:', err);
      wx.showModal({
        title: '处理失败',
        content: err.message || '无法更新生产进度，请重试',
        showCancel: false
      });
      
      page.setData({ 
        processing: false,
        selectedProgressTypes: [],
        // 重置所有工序的强制更新变量
        forceBurningUpdate: 0,
        forceSewingUpdate: 0,
        forceTailUpdate: 0,
        forceShippingUpdate: 0,
        forceDeliveryUpdate: 0,
        forceInspectionUpdate: 0
      });
      
      // 更新工序项目列表
      page.updateProgressItems();
      
      wx.hideLoading();
    });
}

/**
 * 刷新当前批量备货单的进度信息
 */
function refreshCurrentBatchProgress(page, order) {
  const { currentBatchIndex } = page.data;
  
  console.log('刷新当前批量备货单进度信息:', order);
  
  productionProgressApi.getProgressDetail(order.shopId, order.subPurchaseOrderSn)
    .then(res => {
      console.log('刷新获取到的进度信息:', res);
      
      // 更新进度信息
      page.setData({
        [`batchProgressList[${currentBatchIndex}]`]: res,
        'scanResult.progressDetail': res
      });
    })
    .catch(err => {
      console.error('刷新进度信息失败:', err);
    });
}

/**
 * 刷新所有批量备货单的进度信息
 */
function refreshAllBatchProgress(page) {
  const { batchOrders, batchProgressList } = page.data;
  
  console.log('开始刷新所有批量备货单进度信息, 数量:', batchOrders.length);
  wx.showLoading({ title: '刷新状态中...' });
  
  // 创建所有备货单的刷新请求
  const refreshPromises = batchOrders.map((order, index) => {
    return productionProgressApi.getProgressDetail(order.shopId, order.subPurchaseOrderSn)
      .then(res => {
        console.log(`刷新备货单${order.subPurchaseOrderSn}进度信息成功:`, res);
        return { index, data: res, success: true };
      })
      .catch(err => {
        console.error(`刷新备货单${order.subPurchaseOrderSn}进度信息失败:`, err);
        return { index, error: err, success: false };
      });
  });
  
  // 执行所有刷新请求
  Promise.all(refreshPromises)
    .then(results => {
      console.log('所有批量备货单进度信息刷新完成, 结果:', results);
      
      // 更新成功的进度信息
      const updatedProgressList = [...batchProgressList];
      let successCount = 0;
      
      results.forEach(result => {
        if (result.success) {
          updatedProgressList[result.index] = result.data;
          successCount++;
        }
      });
      
      // 更新界面数据
      const updateData = {
        batchProgressList: updatedProgressList,
        processing: false,
        pageVersion: Date.now() // 强制刷新视图
      };
      
      // 如果当前显示的是批量备货单中的一个，更新其进度信息
      if (page.data.currentBatchIndex >= 0 && page.data.currentBatchIndex < updatedProgressList.length) {
        updateData['scanResult.progressDetail'] = updatedProgressList[page.data.currentBatchIndex];
      }
      
      page.setData(updateData);
      
      // 显示刷新结果
      wx.showToast({
        title: `已刷新${successCount}/${batchOrders.length}个备货单状态`,
        icon: 'success',
        duration: 2000
      });
    })
    .catch(err => {
      console.error('刷新批量备货单进度信息过程中发生错误:', err);
      
      // 设置处理完成状态
      page.setData({ 
        processing: false,
        pageVersion: Date.now() // 强制刷新视图
      });
      
      wx.showToast({
        title: '刷新状态失败',
        icon: 'none',
        duration: 2000
      });
    })
    .finally(() => {
      wx.hideLoading();
    });
}

/**
 * 完成所有批量备货单的选中工序
 */
function completeAllBatchOrders(page) {
  const { batchOrders, selectedProgressTypes } = page.data;
  
  // 防重复提交
  if (page.data.processing) {
    console.log('正在处理中，防止重复提交');
    return;
  }
  
  // 检查是否选择了工序
  if (!selectedProgressTypes || selectedProgressTypes.length === 0) {
    console.log('未选择任何工序');
    wx.showToast({
      title: '请至少选择一个工序',
      icon: 'none'
    });
    return;
  }
  
  // 弹窗确认
  wx.showModal({
    title: '批量完成确认',
    content: `您选择了 ${selectedProgressTypes.length} 个工序，将对所有 ${batchOrders.length} 个备货单执行这些工序，确认要提交完成吗？`,
    confirmText: '确认提交',
    cancelText: '再想想',
    success: (res) => {
      if (res.confirm) {
        console.log('用户确认批量提交');
        // 执行批量处理
        executeBatchProgress(page, batchOrders, selectedProgressTypes);
      } else {
        console.log('用户取消批量提交');
      }
    }
  });
}

/**
 * 处理批量订单
 */
function processBatchOrders(page, orders) {
  if (!orders || !Array.isArray(orders) || orders.length === 0) {
    console.error('没有有效的批量订单数据');
    wx.showToast({
      title: '没有有效的订单数据',
      icon: 'none'
    });
    return;
  }
  
  console.log('开始处理批量订单，数量:', orders.length);
  wx.showLoading({ title: '批量处理中...' });
  page.setData({ processing: true });
  
  // 获取用户选中的角色
  const selectedRoleTypes = userRoleManager.getSelectedUserRoleTypes(page);
  const { validProgressRoles } = page.data;
  
  // 如果没有选择角色，无法继续处理
  if (!selectedRoleTypes || selectedRoleTypes.length === 0) {
    console.error('用户没有选择角色，无法处理批量订单');
    wx.showModal({
      title: '请选择角色',
      content: '请先选择要处理的工序角色',
      showCancel: false,
      success: () => page.backToScan()
    });
    wx.hideLoading();
    page.setData({ processing: false });
    return;
  }
  
  // 筛选出有效的生产进度角色
  const validUserRoleKeys = selectedRoleTypes.filter(roleKey => 
    validProgressRoles.includes(roleKey)
  );
  console.log('用户可用于生产进度的角色键:', validUserRoleKeys);
  
  // 如果没有可用工序，无法继续处理
  if (validUserRoleKeys.length === 0) {
    console.error('用户没有可用于生产进度的工序');
    wx.showModal({
      title: '缺少权限',
      content: '您选择的角色中没有可操作的生产工序，无法处理批量订单',
      showCancel: false,
      success: () => page.backToScan()
    });
    wx.hideLoading();
    page.setData({ processing: false });
    return;
  }
  
  // 使用工序名称映射
  const progressNames = validUserRoleKeys.map(key => page.data.roleProgressMap[key] || key);
  
  // 检查是否是快速扫码模式
  if (page.data.activeTab === 'fast') {
    // 快速扫码模式下直接执行批量处理，不显示确认弹窗
    executeBatchProgress(page, orders, validUserRoleKeys);
  } else {
    // 非快速扫码模式下，在处理前确认工序选择
    wx.showModal({
      title: '确认操作工序',
      content: `将为${orders.length}个备货单完成以下工序：${progressNames.join('、')}`,
      confirmText: '确认处理',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          executeBatchProgress(page, orders, validUserRoleKeys);
        } else {
          console.log('用户取消批量工序处理');
          page.backToScan();
          wx.hideLoading();
          page.setData({ processing: false });
        }
      }
    });
  }
}

/**
 * 执行批量进度更新
 */
function executeBatchProgress(page, orders, progressTypes) {
  console.log('执行批量进度更新, 订单数:', orders.length, '工序类型:', progressTypes);
  
  // 初始化统计数据
  let totalRequests = orders.length * progressTypes.length;
  let completedRequests = 0;
  let successCount = 0;
  let failedCount = 0;
  let statusInfo = {};
  
  // 为每个备货单创建进度更新请求
  let allPromises = [];
  
  orders.forEach(order => {
    const { subPurchaseOrderSn, shopId } = order;
    statusInfo[subPurchaseOrderSn] = { success: 0, failed: 0 };
    
    // 对每个工序类型创建更新请求
    progressTypes.forEach(progressType => {
      // 准备请求参数
      const params = {
        shopId: shopId,
        subPurchaseOrderSn: subPurchaseOrderSn,
        progressType: progressType,
        operationType: "1", // 扫码确认完成工序，固定为"1"
        remarks: "" // 备注字段，可以为空字符串
      };
      
      console.log('创建批量更新请求, 备货单:', subPurchaseOrderSn, '工序:', progressType);
      
      // 创建并添加到请求列表
      const promise = productionProgressApi.updateProgress(params)
        .then(res => {
          console.log('批量工序更新成功:', subPurchaseOrderSn, progressType, res);
          successCount++;
          statusInfo[subPurchaseOrderSn].success++;
          return { order, type: progressType, success: true };
        })
        .catch(err => {
          console.error('批量工序更新失败:', subPurchaseOrderSn, progressType, err);
          failedCount++;
          statusInfo[subPurchaseOrderSn].failed++;
          return { order, type: progressType, success: false, error: err };
        })
        .finally(() => {
          completedRequests++;
          // 更新进度提示
          wx.showLoading({
            title: `处理中(${completedRequests}/${totalRequests})`,
          });
        });
      
      allPromises.push(promise);
    });
  });
  
  // 执行所有请求
  Promise.all(allPromises)
    .then(results => {
      console.log('所有批量请求已完成, 结果:', results);
      console.log('成功:', successCount, '失败:', failedCount);
      
      // 生成结果摘要
      let summaryContent = '';
      
      if (successCount > 0 && failedCount === 0) {
        // 全部成功
        summaryContent = `已完成工序：${progressTypes.map(type => page.data.roleProgressMap[type] || type).join('、')}`;
      } else if (successCount > 0 && failedCount > 0) {
        // 部分成功
        summaryContent = `成功：${successCount}个工序\n失败：${failedCount}个工序`;
        
        // 添加简化的失败信息
        let failedOrders = [];
        for (const sn in statusInfo) {
          if (statusInfo[sn].failed > 0) {
            failedOrders.push(`${sn}`);
          }
        }
        if (failedOrders.length > 0) {
          summaryContent += `\n\n失败的备货单：${failedOrders.join('、')}`;
        }
      } else {
        // 全部失败
        summaryContent = `所有工序更新均失败`;
      }
      
      // 显示结果摘要
      wx.showModal({
        title: successCount > 0 ? (failedCount > 0 ? '部分成功' : '处理成功') : '处理失败',
        content: summaryContent,
        showCancel: false,
        confirmText: '确定',
        confirmColor: '#1890ff',
        success: () => {
          // 重置选中状态
          page.setData({
            selectedProgressTypes: [],
            // 重置所有工序的强制更新变量
            forceBurningUpdate: 0,
            forceSewingUpdate: 0,
            forceTailUpdate: 0,
            forceShippingUpdate: 0,
            forceDeliveryUpdate: 0,
            forceInspectionUpdate: 0
          });
          
          // 更新工序项目列表
          page.updateProgressItems();
          
          // 检查是否是快速扫码模式
          if (page.data.activeTab === 'fast') {
            // 快速扫码模式下直接返回扫码界面，不刷新状态
            page.backToScan();
          } else {
            // 非快速扫码模式下，如果有成功的更新，刷新所有批量备货单的进度信息
            if (successCount > 0) {
              // 刷新所有备货单的最新状态
              refreshAllBatchProgress(page);
            } else {
              // 如果全部失败，则直接重置处理状态
              page.setData({ processing: false });
            }
          }
        }
      });
    })
    .catch(err => {
      console.error('批量处理过程中发生未捕获错误:', err);
      wx.showModal({
        title: '处理失败',
        content: '批量处理过程中发生错误: ' + (err.message || '未知错误'),
        showCancel: false,
        success: () => {
          // 重置状态
          page.setData({
            selectedProgressTypes: [],
            // 重置所有工序的强制更新变量
            forceBurningUpdate: 0,
            forceSewingUpdate: 0,
            forceTailUpdate: 0,
            forceShippingUpdate: 0,
            forceDeliveryUpdate: 0,
            forceInspectionUpdate: 0,
            processing: false,
            pageVersion: Date.now() // 强制刷新视图
          });
          
          // 更新工序项目列表
          page.updateProgressItems();
          
          // 快速扫码模式下直接返回扫码界面
          if (page.data.activeTab === 'fast') {
            page.backToScan();
          }
        }
      });
    })
    .finally(() => {
      wx.hideLoading();
    });
}

module.exports = {
  handleBatchScanResult,
  loadBatchOrdersData,
  loadOrderProgressDetail,
  displayBatchOrdersUI,
  showNextBatchOrder,
  showPrevBatchOrder,
  submitCurrentBatchProgress,
  updateBatchOrderProgress,
  refreshCurrentBatchProgress,
  refreshAllBatchProgress,
  completeAllBatchOrders,
  processBatchOrders,
  executeBatchProgress
}; 