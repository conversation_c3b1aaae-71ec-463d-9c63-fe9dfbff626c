<template>
  <!-- 紧急备货建议搜索区域 -->
  <SearchCard class="search-card search-area-card">
    <div class="search-container">
      <!-- 快速筛选区域 -->
      <div class="quick-filter-section">
        <div class="quick-filter-title">快速筛选:</div>
        <div class="quick-filter-tags">
          <el-tag v-for="(tag, index) in quickFilterTags" :key="index"
            :class="{ 'active-tag': isTagActive(tag.value) }" @click="toggleQuickFilter(tag.value)"
            class="filter-tag" :effect="isTagActive(tag.value) ? 'dark' : 'plain'">
            {{ tag.label }}
            <span v-if="isTagActive(tag.value)" class="tag-count">{{ getTagCount(tag.value) }}</span>
          </el-tag>
        </div>
      </div>

      <!-- 第一行 -->
      <div class="search-row">
        <!-- 店铺 -->
        <div class="search-item">
          <div class="search-label">店铺</div>
          <el-select 
            v-model="localQueryParams.shopIds" 
            placeholder="请选择店铺" 
            clearable 
            multiple 
            filterable 
            collapse-tags
            collapse-tags-tooltip 
            size="small" 
            class="search-input" 
            @change="handleShopSelectChange" 
            @clear="isAllSelected = false">
            <!-- 添加全选选项 -->
            <el-option 
              key="all" 
              label="全选" 
              :value="'all'" 
            />
            <el-option v-for="shop in props.shops" :key="shop.shopId" :label="shop.shopName" :value="shop.shopId" />
          </el-select>
        </div>

        <!-- 备货母单号 - 改为标签输入框 -->
        <div class="search-item">
          <div class="search-label">备货母单号</div>
          <div class="tag-input-container">
            <div class="tags-container">
              <el-tag v-for="sn in queryParams.originalPurchaseOrderSnList" :key="sn" closable class="skc-tag"
                @close="handleRemoveOriginalPurchaseOrderSn(sn)">
                {{ sn }}
              </el-tag>
            </div>
            <el-input v-model="originalPurchaseOrderSnInput" placeholder="请输入备货母单号，回车、空格或逗号分隔" size="small" class="skc-input"
              @keyup.enter="handleAddOriginalPurchaseOrderSn" @keyup.space="handleAddOriginalPurchaseOrderSn" @keyup.comma="handleAddOriginalPurchaseOrderSn"
              @blur="handleAddOriginalPurchaseOrderSn" />
          </div>
        </div>

        <!-- 备货单号 -->
        <div class="search-item">
          <div class="search-label">备货单号</div>
          <!-- 修改：将 el-select 改为标签输入框 -->
          <div class="tag-input-container">
            <div class="tags-container">
              <el-tag v-for="sn in queryParams.subPurchaseOrderSnList" :key="sn" closable class="skc-tag"
                @close="handleRemoveSubPurchaseOrderSn(sn)">
                {{ sn }}
              </el-tag>
            </div>
            <el-input v-model="subPurchaseOrderSnInput" placeholder="请输入备货单号，回车、空格或逗号分隔" size="small" class="skc-input"
              @keyup.enter="handleAddSubPurchaseOrderSn" @keyup.space="handleAddSubPurchaseOrderSn" @keyup.comma="handleAddSubPurchaseOrderSn"
              @blur="handleAddSubPurchaseOrderSn" />
          </div>
        </div>

        <!-- 货号 - 修改为标签输入框 -->
        <div class="search-item">
          <div class="search-label">货号</div>
          <div class="tag-input-container">
            <div class="tags-container">
              <el-tag v-for="sn in queryParams.productSnList" :key="sn" closable class="skc-tag"
                @close="handleRemoveProductSn(sn)">
                {{ sn }}
              </el-tag>
            </div>
            <el-input v-model="productSnInput" placeholder="请输入货号，回车、空格或逗号分隔" size="small" class="skc-input"
              @keyup.enter="handleAddProductSn" @keyup.space="handleAddProductSn" @keyup.comma="handleAddProductSn"
              @blur="handleAddProductSn" />
          </div>
        </div>
      </div>

      <!-- 以下行在展开状态时显示 -->
      <template v-if="isAdvancedSearchExpanded">
        <!-- 第二行 -->
      <div class="search-row">
        <!-- 是否JIT -->
        <div class="search-item">
          <div class="search-label">是否JIT</div>
          <el-select v-model="queryParams.purchaseStockType" placeholder="请选择" clearable size="small"
            class="search-input">
            <el-option label="普通" :value="0" />
            <el-option label="JIT备货" :value="1" />
          </el-select>
        </div>

        <!-- 备货区域 -->
        <div class="search-item">
          <div class="search-label">备货区域</div>
          <el-select v-model="queryParams.inventoryRegionList" placeholder="请选择" clearable multiple collapse-tags
            collapse-tags-tooltip size="small" class="search-input">
            <el-option label="国内备货" :value="1" />
            <el-option label="海外备货" :value="2" />
            <el-option label="保税仓备货" :value="3" />
          </el-select>
        </div>

        <!-- SKC -->
        <div class="search-item">
          <div class="search-label">SKC</div>
          <div class="tag-input-container">
            <div class="tags-container">
              <el-tag v-for="skc in queryParams.productSkcIdList" :key="skc" closable class="skc-tag"
                @close="handleRemoveSkc(skc)">
                {{ skc }}
              </el-tag>
            </div>
            <el-input v-model="skcInput" placeholder="请输入SKC，回车、空格或逗号分隔" size="small" class="skc-input"
              @keyup.enter="handleAddSkc" @keyup.space="handleAddSkc" @keyup.comma="handleAddSkc"
              @blur="handleAddSkc" />
          </div>
        </div>

        <!-- 是否VMI -->
        <div class="search-item">
          <div class="search-label">是否VMI</div>
          <el-select v-model="queryParams.settlementType" placeholder="请选择" clearable size="small"
            class="search-input">
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </div>
      </div>
        <!-- 第三行 -->
        <div class="search-row">
          <!-- 来源 -->
          <div class="search-item">
            <div class="search-label">来源</div>
            <el-select v-model="queryParams.sourceList" placeholder="请选择" clearable multiple collapse-tags
              collapse-tags-tooltip size="small" class="search-input">
              <el-option label="运营申请" :value="0" />
              <el-option label="卖家申请" :value="1" />
              <el-option label="系统创建" :value="9999" />
            </el-select>
          </div>

          <!-- 是否自动创建返单 -->
          <div class="search-item">
            <div class="search-label">是否自动创建返单</div>
            <el-select v-model="queryParams.isSystemAutoPurchaseSource" placeholder="请选择" clearable size="small"
              class="search-input">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </div>

          <!-- 备货单创建时间 -->
          <div class="search-item">
            <div class="search-label">备货单创建时间</div>
            <el-date-picker v-model="purchaseTimeRange" type="datetimerange" range-separator="至"
              start-placeholder="开始日期时间" end-placeholder="结束日期时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
              size="small" class="search-input" @change="handlePurchaseTimeChange" />
          </div>

          <!-- 发货单号 -->
          <div class="search-item">
            <div class="search-label">发货单号</div>
            <div class="tag-input-container">
              <div class="tags-container">
                <el-tag v-for="orderSn in queryParams.deliverOrderSnList" :key="orderSn" closable class="skc-tag"
                  @close="handleRemoveDeliverOrderSn(orderSn)">
                  {{ orderSn }}
                </el-tag>
              </div>
              <el-input v-model="deliverOrderSnInput" placeholder="请输入发货单号，回车、空格或逗号分隔" size="small"
                class="skc-input" @keyup.enter="handleAddDeliverOrderSn" @keyup.space="handleAddDeliverOrderSn"
                @keyup.comma="handleAddDeliverOrderSn" @blur="handleAddDeliverOrderSn" />
            </div>
          </div>
        </div>

        <!-- 第四行 -->
        <div class="search-row">
          <!-- 发货是否逾期 -->
          <div class="search-item">
            <div class="search-label">发货是否逾期</div>
            <el-select v-model="queryParams.isDelayDeliver" placeholder="请选择" clearable size="small"
              class="search-input">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </div>

          <!-- 到货是否逾期 -->
          <div class="search-item">
            <div class="search-label">到货是否逾期</div>
            <el-select v-model="queryParams.isDelayArrival" placeholder="请选择" clearable size="small"
              class="search-input">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </div>

          <!-- 要求最晚发货时间 -->
          <div class="search-item">
            <div class="search-label">最晚发货时间</div>
            <el-date-picker v-model="expectLatestDeliverTimeRange" type="datetimerange" range-separator="至"
              start-placeholder="开始日期时间" end-placeholder="结束日期时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
              size="small" class="search-input" @change="handleExpectLatestDeliverTimeChange" />
          </div>

          <!-- 要求最晚到货时间 -->
          <div class="search-item">
            <div class="search-label">最晚到货时间</div>
            <el-date-picker v-model="expectLatestArrivalTimeRange" type="datetimerange" range-separator="至"
              start-placeholder="开始日期时间" end-placeholder="结束日期时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
              size="small" class="search-input" @change="handleExpectLatestArrivalTimeChange" />
          </div>
        </div>

        <!-- 第五行 -->
        <div class="search-row">
          <!-- 是否首单 -->
          <div class="search-item">
            <div class="search-label">是否首单</div>
            <el-select v-model="queryParams.isFirst" placeholder="请选择" clearable size="small" class="search-input">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </div>

          <!-- 是否缺货 -->
          <div class="search-item">
            <div class="search-label">是否缺货</div>
            <el-select v-model="queryParams.skuLackSnapshot" placeholder="请选择" clearable size="small"
              class="search-input">
              <el-option label="否" :value="0" />
              <el-option label="是" :value="1" />
            </el-select>
          </div>

          <!-- 是否有质量隐患 -->
          <div class="search-item">
            <div class="search-label">质量隐患</div>
            <el-select v-model="queryParams.qcReject" placeholder="请选择" clearable size="small" class="search-input">
              <el-option label="否" :value="0" />
              <el-option label="是" :value="1" />
            </el-select>
          </div>

          <!-- 抽检不合格 -->
          <div class="search-item">
            <div class="search-label">抽检不合格</div>
            <el-select v-model="queryParams.qcOption" placeholder="请选择" clearable size="small" class="search-input">
              <el-option label="是" :value="10" />
              <el-option label="否" :value="20" />
            </el-select>
          </div>
        </div>

        <!-- 第六行 -->
        <div class="search-row">
          <!-- 因抽检不合格创建 -->
          <div class="search-item">
            <div class="search-label">因抽检不合格<br />创建</div>
            <el-select v-model="queryParams.qcNotPassCreate" placeholder="请选择" clearable size="small"
              class="search-input">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </div>

          <!-- 是否含缺货/售罄SKU -->
          <div class="search-item">
            <div class="search-label">是否含<br />缺货/售罄SKU</div>
            <el-select v-model="queryParams.lackOrSoldOutTagList" placeholder="请选择" clearable multiple size="small"
              class="search-input">
              <el-option label="含缺货SKU" :value="1" />
              <el-option label="含售罄SKU" :value="2" />
            </el-select>
          </div>

          <!-- 是否热销款 -->
          <div class="search-item">
            <div class="search-label">是否热销款</div>
            <el-select v-model="queryParams.hotTag" placeholder="请选择" clearable size="small" class="search-input">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </div>

          <!-- 预计可发货日期 -->
          <div class="search-item">
            <div class="search-label">可发货日期</div>
            <el-date-picker v-model="canDeliverTimeRange" type="datetimerange" range-separator="至"
              start-placeholder="开始日期时间" end-placeholder="结束日期时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
              size="small" class="search-input" @change="handleCanDeliverTimeChange" />
          </div>
        </div>

        <!-- 第七行 -->
        <div class="search-row">
          <!-- 商品条码尺寸 -->
          <div class="search-item">
            <div class="search-label">商品条码尺寸</div>
            <el-select v-model="queryParams.productLabelCodeStyle" placeholder="请选择" clearable size="small"
              class="search-input">
              <el-option label="全选" :value="0" />
              <el-option label="70*20mm" :value="1" />
              <el-option label="100*100mm" :value="2" />
            </el-select>
          </div>

          <!-- 是否入库退供 -->
          <div class="search-item">
            <div class="search-label">是否入库退供</div>
            <el-select v-model="queryParams.inboundReturn" placeholder="请选择" clearable size="small"
              class="search-input">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </div>

          <!-- 是否因入库退供创建 -->
          <div class="search-item">
            <div class="search-label">因入库退供创建</div>
            <el-select v-model="queryParams.inboundReturnCreate" placeholder="请选择" clearable size="small"
              class="search-input">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </div>
        </div>
      </template>

      <!-- 按钮行 -->
      <div class="search-row">
        <div class="search-item search-buttons-container">
          <div class="search-buttons">
            <el-button type="primary" @click="handleSearch" size="small" class="action-button">
              <el-icon>
                <Search />
              </el-icon> 搜索
            </el-button>
            <el-button @click="handleReset" size="small" class="action-button">
              <el-icon>
                <Refresh />
              </el-icon> 重置
            </el-button>
            <el-button type="text" size="small" @click="toggleAdvancedSearch" class="expand-button-text">
              {{ isAdvancedSearchExpanded ? '收起' : '展开' }}
              <el-icon>
                <component :is="isAdvancedSearchExpanded ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </SearchCard>
</template>

<script setup lang="ts">
import { ref, reactive, defineEmits, defineProps, watch } from 'vue'
import { Search, Refresh, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { SearchCard } from '@/components/temu'
import type { PurchaseOrderRequestDTO, Shop } from '@/types/purchaseOrder'

const emit = defineEmits([
  'search', 
  'reset',
  'update:query-params'
])

const props = defineProps({
  shops: {
    type: Array as () => Shop[],
    required: true
  },
  queryParams: {
    type: Object as () => PurchaseOrderRequestDTO,
    required: true
  },
  tagCounts: {
    type: Object as () => Record<string, number>,
    required: true
  }
})

// 双向绑定查询参数的副本
const localQueryParams = reactive<PurchaseOrderRequestDTO>({ ...props.queryParams })

// 监听父组件传入的 queryParams 变化，同步到本地
watch(
  () => props.queryParams,
  (newVal) => {
    Object.assign(localQueryParams, newVal)
  },
  { deep: true }
)

// 监听本地 queryParams 变化，同步回父组件
watch(
  localQueryParams,
  (newVal) => {
    emit('update:query-params', { ...newVal })
  },
  { deep: true }
)

// 输入框的值
const skcInput = ref('')
const deliverOrderSnInput = ref('')
const subPurchaseOrderSnInput = ref('')
const originalPurchaseOrderSnInput = ref('')
const productSnInput = ref('')

// 日期范围控件
const purchaseTimeRange = ref<[string, string] | null>(null)
const expectLatestDeliverTimeRange = ref<[string, string] | null>(null)
const expectLatestArrivalTimeRange = ref<[string, string] | null>(null)
const canDeliverTimeRange = ref<[string, string] | null>(null)

// 添加控制高级搜索展开/收起的状态变量
const isAdvancedSearchExpanded = ref(false)

// 定义选择店铺后的全选状态
const isAllSelected = ref(false)

// 切换高级搜索展开/收起状态
const toggleAdvancedSearch = () => {
  isAdvancedSearchExpanded.value = !isAdvancedSearchExpanded.value
}

// 日期范围变更处理函数
const handlePurchaseTimeChange = (val: [string, string] | null) => {
  if (val) {
    // 直接使用时间字符串转时间戳
    const startDate = new Date(val[0]);
    localQueryParams.purchaseTimeFrom = startDate.getTime();

    // 直接使用时间字符串转时间戳
    const endDate = new Date(val[1]);
    localQueryParams.purchaseTimeTo = endDate.getTime();
  } else {
    localQueryParams.purchaseTimeFrom = undefined;
    localQueryParams.purchaseTimeTo = undefined;
  }
}

const handleExpectLatestDeliverTimeChange = (val: [string, string] | null) => {
  if (val) {
    // 直接使用时间字符串转时间戳
    const startDate = new Date(val[0]);
    localQueryParams.expectLatestDeliverTimeFrom = startDate.getTime();

    // 直接使用时间字符串转时间戳
    const endDate = new Date(val[1]);
    localQueryParams.expectLatestDeliverTimeTo = endDate.getTime();
  } else {
    localQueryParams.expectLatestDeliverTimeFrom = undefined;
    localQueryParams.expectLatestDeliverTimeTo = undefined;
  }
}

const handleExpectLatestArrivalTimeChange = (val: [string, string] | null) => {
  if (val) {
    // 直接使用时间字符串转时间戳
    const startDate = new Date(val[0]);
    localQueryParams.expectLatestArrivalTimeFrom = startDate.getTime();

    // 直接使用时间字符串转时间戳
    const endDate = new Date(val[1]);
    localQueryParams.expectLatestArrivalTimeTo = endDate.getTime();
  } else {
    localQueryParams.expectLatestArrivalTimeFrom = undefined;
    localQueryParams.expectLatestArrivalTimeTo = undefined;
  }
}

const handleCanDeliverTimeChange = (val: [string, string] | null) => {
  if (val) {
    // 直接使用时间字符串转时间戳
    const startDate = new Date(val[0]);
    localQueryParams.canDeliverStartTime = startDate.getTime();

    // 直接使用时间字符串转时间戳
    const endDate = new Date(val[1]);
    localQueryParams.canDeliverEndTime = endDate.getTime();
  } else {
    localQueryParams.canDeliverStartTime = undefined;
    localQueryParams.canDeliverEndTime = undefined;
  }
}

// 查询按钮点击
const handleSearch = () => {
  if (!localQueryParams.shopIds || localQueryParams.shopIds.length === 0) {
    ElMessage.warning('请选择至少一个店铺')
    return
  }

  emit('search')
}

// 重置按钮点击
const handleReset = () => {
  // 重置全选状态
  isAllSelected.value = false;
  emit('reset')
}

// 添加SKC标签
const handleAddSkc = () => {
  if (!skcInput.value) return

  // 将输入内容按空格或逗号分割
  const skcItems = skcInput.value.split(/[\s,，]+/).filter(item => item.trim() !== '')

  // 添加标签
  skcItems.forEach(skc => {
    if (skc && !localQueryParams.productSkcIdList.includes(skc)) {
      localQueryParams.productSkcIdList.push(skc)
    }
  })

  // 清空输入框
  skcInput.value = ''
}

// 移除SKC标签
const handleRemoveSkc = (skc: string) => {
  const index = localQueryParams.productSkcIdList.indexOf(skc)
  if (index !== -1) {
    localQueryParams.productSkcIdList.splice(index, 1)
  }
}

// 添加发货单号标签
const handleAddDeliverOrderSn = () => {
  if (!deliverOrderSnInput.value) return

  // 将输入内容按空格或逗号分割
  const orderSnItems = deliverOrderSnInput.value.split(/[\s,，]+/).filter(item => item.trim() !== '')

  // 添加标签
  orderSnItems.forEach(orderSn => {
    if (orderSn && !localQueryParams.deliverOrderSnList.includes(orderSn)) {
      localQueryParams.deliverOrderSnList.push(orderSn)
    }
  })

  // 清空输入框
  deliverOrderSnInput.value = ''
}

// 移除发货单号标签
const handleRemoveDeliverOrderSn = (orderSn: string) => {
  const index = localQueryParams.deliverOrderSnList.indexOf(orderSn)
  if (index !== -1) {
    localQueryParams.deliverOrderSnList.splice(index, 1)
  }
}

// 添加备货单号标签
const handleAddSubPurchaseOrderSn = () => {
  if (!subPurchaseOrderSnInput.value) return

  // 将输入内容按空格或逗号分割
  const snItems = subPurchaseOrderSnInput.value.split(/[\s,，]+/).filter(item => item.trim() !== '')

  // 添加标签
  snItems.forEach(sn => {
    if (sn && !localQueryParams.subPurchaseOrderSnList.includes(sn)) {
      localQueryParams.subPurchaseOrderSnList.push(sn)
    }
  })

  // 清空输入框
  subPurchaseOrderSnInput.value = ''
}

// 移除备货单号标签
const handleRemoveSubPurchaseOrderSn = (sn: string) => {
  const index = localQueryParams.subPurchaseOrderSnList.indexOf(sn)
  if (index !== -1) {
    localQueryParams.subPurchaseOrderSnList.splice(index, 1)
  }
}

// 添加备货母单号标签
const handleAddOriginalPurchaseOrderSn = () => {
  if (!originalPurchaseOrderSnInput.value) return

  // 将输入内容按空格或逗号分割
  const snItems = originalPurchaseOrderSnInput.value.split(/[\s,，]+/).filter(item => item.trim() !== '')

  // 添加标签
  snItems.forEach(sn => {
    if (sn && !localQueryParams.originalPurchaseOrderSnList.includes(sn)) {
      localQueryParams.originalPurchaseOrderSnList.push(sn)
    }
  })

  // 清空输入框
  originalPurchaseOrderSnInput.value = ''
}

// 移除备货母单号标签
const handleRemoveOriginalPurchaseOrderSn = (sn: string) => {
  const index = localQueryParams.originalPurchaseOrderSnList.indexOf(sn)
  if (index !== -1) {
    localQueryParams.originalPurchaseOrderSnList.splice(index, 1)
  }
}

// 添加货号标签
const handleAddProductSn = () => {
  if (!productSnInput.value) return

  // 将输入内容按空格或逗号分割
  const snItems = productSnInput.value.split(/[\s,，]+/).filter(item => item.trim() !== '')

  // 添加标签
  snItems.forEach(sn => {
    if (sn && !localQueryParams.productSnList.includes(sn)) {
      localQueryParams.productSnList.push(sn)
    }
  })

  // 清空输入框
  productSnInput.value = ''
}

// 移除货号标签
const handleRemoveProductSn = (sn: string) => {
  const index = localQueryParams.productSnList.indexOf(sn)
  if (index !== -1) {
    localQueryParams.productSnList.splice(index, 1)
  }
}

// 快速筛选标签定义
const quickFilterTags = ref([
  { label: '履约考核', value: 'inFulfilmentPunish' },
  { label: '即将发货逾期', value: 'deliverSoonDelay' },
  { label: '发货已逾期', value: 'deliverDelay' },
  { label: '到货即将逾期', value: 'arrivalSoonDelay' },
  { label: '到货已逾期', value: 'arrivalDelay' },
  { label: '今日系统创建', value: 'isTodayPlatformPurchase' }
])

// 检查标签是否激活
const isTagActive = (tagValue: string): boolean => {
  switch (tagValue) {
    case 'inFulfilmentPunish':
      return localQueryParams.inFulfilmentPunish === true
    case 'deliverSoonDelay':
      return localQueryParams.deliverOrArrivalDelayStatusList?.includes(101) || false
    case 'deliverDelay':
      return localQueryParams.deliverOrArrivalDelayStatusList?.includes(102) || false
    case 'arrivalSoonDelay':
      return localQueryParams.deliverOrArrivalDelayStatusList?.includes(201) || false
    case 'arrivalDelay':
      return localQueryParams.deliverOrArrivalDelayStatusList?.includes(202) || false
    case 'isTodayPlatformPurchase':
      return localQueryParams.isTodayPlatformPurchase === true
    default:
      return false
  }
}

// 切换快速筛选
const toggleQuickFilter = (tagValue: string) => {
  switch (tagValue) {
    case 'inFulfilmentPunish':
      localQueryParams.inFulfilmentPunish = localQueryParams.inFulfilmentPunish === true ? undefined : true
      break
    case 'deliverSoonDelay':
      toggleDeliveryStatus(101)
      break
    case 'deliverDelay':
      toggleDeliveryStatus(102)
      break
    case 'arrivalSoonDelay':
      toggleDeliveryStatus(201)
      break
    case 'arrivalDelay':
      toggleDeliveryStatus(202)
      break
    case 'isTodayPlatformPurchase':
      localQueryParams.isTodayPlatformPurchase = localQueryParams.isTodayPlatformPurchase === true ? undefined : true
      break
  }

  // 移除自动执行搜索，交给父组件的 watch 处理
  // handleSearch()
}

// 切换发货/到货状态
const toggleDeliveryStatus = (status: number) => {
  if (!localQueryParams.deliverOrArrivalDelayStatusList) {
    localQueryParams.deliverOrArrivalDelayStatusList = []
  }

  const index = localQueryParams.deliverOrArrivalDelayStatusList.indexOf(status)
  if (index > -1) {
    localQueryParams.deliverOrArrivalDelayStatusList.splice(index, 1)
  } else {
    localQueryParams.deliverOrArrivalDelayStatusList.push(status)
  }

  // 如果列表为空，则设为undefined
  if (localQueryParams.deliverOrArrivalDelayStatusList.length === 0) {
    localQueryParams.deliverOrArrivalDelayStatusList = []
  }
}

// 获取标签数量
const getTagCount = (tagValue: string): number => {
  // 直接返回tagCounts中存储的对应标签数量
  return props.tagCounts[tagValue] || 0;
}

// 添加店铺选择变化处理函数
const handleShopSelectChange = (value) => {
  // 检查是否包含全选选项
  const allOptionIndex = value.indexOf('all');
  
  if (allOptionIndex > -1) {
    // 移除"all"选项，只保留真实的店铺ID
    value.splice(allOptionIndex, 1);
    
    // 判断当前是否处于全选状态
    if (isAllSelected.value) {
      // 如果已经是全选状态，此次操作视为取消全选
      localQueryParams.shopIds = [];
      isAllSelected.value = false;
    } else {
      // 否则执行全选
      localQueryParams.shopIds = props.shops.map(item => item.shopId);
      isAllSelected.value = true;
    }
  } else {
    // 正常选择，直接使用传入的值
    localQueryParams.shopIds = value;
    
    // 检查是否等于全选状态
    isAllSelected.value = props.shops.length > 0 && 
                        Array.isArray(localQueryParams.shopIds) && 
                        localQueryParams.shopIds.length === props.shops.length;
  }
}
</script>

<style scoped>
.search-area-card {
  border-radius: 0 0 4px 4px;
  margin-top: -1px;
  box-shadow: none;
  z-index: 0;
  flex-shrink: 0;
  border-top: none;
  position: relative;
  border: 1px solid #e4e7ed;
  border-top: none;
}

/* 禁用SearchCard的hover效果 */
.search-area-card:deep(.el-card):hover {
  box-shadow: none !important;
}

.search-area-card:hover {
  box-shadow: none !important;
}

/* 快速筛选区域样式 */
.quick-filter-section {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.quick-filter-title {
  font-weight: bold;
  margin-right: 10px;
  white-space: nowrap;
  color: #606266;
}

.quick-filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-tag {
  cursor: pointer;
  user-select: none;
  padding: 5px 10px;
  display: flex;
  align-items: center;
}

.filter-tag:hover {
  opacity: 0.8;
}

.active-tag {
  font-weight: bold;
}

.tag-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
  color: #fff;
  font-size: 12px;
  line-height: 1;
  font-weight: normal;
}

.search-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.search-item {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 250px;
  max-width: calc(25% - 12px);
  margin-bottom: 5px;
}

.search-label {
  width: 90px;
  text-align: right;
  margin-right: 10px;
  white-space: nowrap;
}

.search-input {
  flex: 1;
}

.search-buttons-container {
  justify-content: flex-end;
  display: flex;
  max-width: none;
}

.search-buttons {
  display: flex;
  gap: 10px;
}

.expand-button-text {
  display: flex;
  align-items: center;
}

.action-button {
  display: flex;
  align-items: center;
}

.tag-input-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 2px 5px;
  min-height: 28px;
  background-color: #fff;
  box-sizing: border-box;
}

.tag-input-container:hover {
  border-color: #c0c4cc;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  padding-top: 3px;
}

.skc-tag {
  margin-right: 0;
  margin-bottom: 2px;
  height: 22px;
  line-height: 20px;
  font-size: 12px;
}

.skc-input {
  flex: 1;
}

.skc-input :deep(.el-input__wrapper) {
  box-shadow: none !important;
  padding: 0;
  font-size: 12px;
}

.skc-input :deep(.el-input__inner) {
  height: 24px;
  line-height: 24px;
}

.skc-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: none !important;
}
</style> 