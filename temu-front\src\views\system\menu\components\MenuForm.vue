<template>
  <el-dialog
    :title="title"
    v-model="visible"
    width="600px"
    append-to-body
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="上级菜单" prop="parentId">
        <el-tree-select
          v-model="form.parentId"
          :data="menuOptions"
          :props="{ label: 'menuName', value: 'menuId', children: 'children' }"
          value-key="menuId"
          placeholder="请选择上级菜单"
          check-strictly
          clearable
        />
      </el-form-item>
      <el-form-item label="菜单类型" prop="menuType">
        <el-radio-group v-model="form.menuType" @change="handleMenuTypeChange">
          <el-radio label="M">目录</el-radio>
          <el-radio label="C">菜单</el-radio>
          <el-radio label="F">按钮</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="菜单名称" prop="menuName">
        <el-input v-model="form.menuName" placeholder="请输入菜单名称" />
      </el-form-item>
      <el-form-item label="显示排序" prop="orderNum">
        <el-input-number v-model="form.orderNum" :min="0" :max="999" controls-position="right" />
      </el-form-item>
      <el-form-item v-if="form.menuType !== 'F'" label="菜单图标">
        <el-popover
          placement="bottom-start"
          trigger="click"
          :width="400"
          popper-class="icon-popover"
        >
          <template #reference>
            <el-input v-model="form.icon" placeholder="点击选择图标" readonly>
              <template #prefix>
                <el-icon v-if="form.icon">
                  <component :is="form.icon" />
                </el-icon>
                <el-icon v-else><Picture /></el-icon>
              </template>
            </el-input>
          </template>
          <icon-select v-model="form.icon" />
        </el-popover>
      </el-form-item>
      <el-form-item v-if="form.menuType !== 'F'" label="路由地址" prop="path">
        <el-input v-model="form.path" placeholder="请输入路由地址" />
      </el-form-item>
      <el-form-item v-if="form.menuType === 'C'" label="组件路径" prop="component">
        <el-input v-model="form.component" placeholder="请输入组件路径" />
      </el-form-item>
      <el-form-item v-if="form.menuType !== 'F'" label="是否外链">
        <el-radio-group v-model="form.isFrame">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.menuType !== 'F'" label="显示状态">
        <el-radio-group v-model="form.visible">
          <el-radio label="0">显示</el-radio>
          <el-radio label="1">隐藏</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.menuType !== 'M'" label="权限标识" prop="perms">
        <el-input v-model="form.perms" placeholder="请输入权限标识" maxlength="100" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import IconSelect from '@/components/IconSelect/index.vue'
import { getMenuTree, getMenu, addMenu, updateMenu } from '@/api/menu'

// 表单DOM引用
const formRef = ref<FormInstance>()

// 自定义事件
const emit = defineEmits(['refreshList'])

// 弹窗可见性
const visible = ref(false)

// 弹窗标题
const title = ref('')

// 菜单树选项
const menuOptions = ref<any[]>([])

// 表单数据
const form = reactive({
  menuId: 0,
  parentId: 0,
  menuName: '',
  orderNum: 0,
  path: '',
  component: '',
  isFrame: 0,
  menuType: 'M',
  visible: '0',
  perms: '',
  icon: '',
  remark: ''
})

// 表单校验规则
const rules = reactive({
  menuName: [
    { required: true, message: '菜单名称不能为空', trigger: 'blur' }
  ],
  orderNum: [
    { required: true, message: '显示排序不能为空', trigger: 'blur' }
  ],
  path: [
    { required: true, message: '路由地址不能为空', trigger: 'blur' }
  ],
  component: [
    { required: true, message: '组件路径不能为空', trigger: 'blur' }
  ]
})

// 监听菜单类型变化
const handleMenuTypeChange = (value: string) => {
  // 根据菜单类型调整表单验证规则
  if (value === 'F') {
    // 按钮类型
    form.isFrame = 0
    form.path = ''
    form.component = ''
    form.icon = ''
  } else if (value === 'C') {
    // 菜单类型，需要组件路径
    form.component = ''
  } else {
    // 目录类型
    form.component = ''
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    menuId: 0,
    parentId: 0,
    menuName: '',
    orderNum: 0,
    path: '',
    component: '',
    isFrame: 0,
    menuType: 'M',
    visible: '0',
    perms: '',
    icon: '',
    remark: ''
  })

  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 加载菜单树数据
const loadMenuTree = async () => {
  try {
    const res = await getMenuTree()
    // 添加一个根节点
    menuOptions.value = [{
      menuId: 0,
      menuName: '主目录',
      children: res.data
    }]
  } catch (error) {
    console.error('获取菜单树失败', error)
  }
}

// 加载菜单详情
const loadMenuInfo = async (menuId: number) => {
  try {
    const res = await getMenu(menuId)
    Object.assign(form, res.data)
  } catch (error) {
    console.error('获取菜单详情失败', error)
  }
}

// 打开对话框
const openDialog = async (menuId?: number) => {
  resetForm()
  await loadMenuTree()
  
  if (menuId) {
    // 修改菜单
    title.value = '修改菜单'
    await loadMenuInfo(menuId)
  } else {
    // 新增菜单
    title.value = '添加菜单'
  }
  
  visible.value = true
}

// 取消操作
const cancel = () => {
  visible.value = false
  resetForm()
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (!valid) return
    
    try {
      const isUpdate = form.menuId !== 0
      
      if (isUpdate) {
        await updateMenu(form)
        ElMessage.success('修改成功')
      } else {
        await addMenu(form)
        ElMessage.success('新增成功')
      }
      
      visible.value = false
      emit('refreshList')
    } catch (error: any) {
      console.error('保存菜单失败', error)
      ElMessage.error(error.message || '操作失败')
    }
  })
}

// 设置父菜单ID
const setParentMenu = (parentId: number) => {
  form.parentId = parentId
  
  // 查找父菜单
  const findParentMenu = (menus: any[], id: number): any => {
    for (const menu of menus) {
      if (menu.menuId === id) {
        return menu
      }
      if (menu.children && menu.children.length > 0) {
        const found = findParentMenu(menu.children, id)
        if (found) return found
      }
    }
    return null
  }
  
  // 获取除根菜单外的所有菜单
  const allMenus: any[] = []
  menuOptions.value.forEach(option => {
    if (option.children) {
      allMenus.push(...option.children)
    }
  })
  
  // 找到父菜单并设置合适的默认值
  const parentMenu = findParentMenu(allMenus, parentId)
  if (parentMenu) {
    // 根据父菜单类型设置默认值
    if (parentMenu.menuType === 'M') {
      // 父菜单是目录，默认创建菜单
      form.menuType = 'C'
    } else if (parentMenu.menuType === 'C') {
      // 父菜单是菜单，默认创建按钮
      form.menuType = 'F'
    }
  }
}

// 提供给父组件的方法
defineExpose({
  openDialog,
  setParentMenu
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style> 