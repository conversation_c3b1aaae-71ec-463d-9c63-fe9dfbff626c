package com.xiao.temu.infrastructure.exporter;

import com.xiao.temu.modules.violation.dto.ShopViolationInfoDTO;
import com.xiao.temu.modules.violation.dto.ShopViolationInfoQueryDTO;
import com.xiao.temu.modules.violation.vo.ShopViolationInfoVO;
import com.xiao.temu.modules.system.service.SysDataPermissionService;
import com.xiao.temu.modules.system.service.UserService;
import com.xiao.temu.modules.violation.service.ShopViolationInfoService;
import com.xiao.temu.infrastructure.excel.ExcelExportService;
import com.xiao.temu.infrastructure.excel.ExcelExportUtils;
import com.xiao.temu.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 店铺违规信息Excel导出实现类
 */
@Slf4j
@Service
public class ShopViolationExcelExporter implements ExcelExportService.ExcelExporter {

    @Autowired
    private ExcelExportService excelExportService;
    
    @Autowired
    private ShopViolationInfoService shopViolationInfoService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private SysDataPermissionService dataPermissionService;
    
    @Override
    public void export(Map<String, Object> exportParams, String taskId) {
        // 定义表头和列宽
        String[] headers = {
            "序号", "店铺代号", "店铺名称", "违规编号", "备货单号", "商品SKU ID", "库存数量", 
            "缺货数量", "不合格数量", "违规一级类型", "违规二级类型", "违规时间", "罚款金额", 
            "罚款币种", "状态", "抽检状态"
        };
        
        int[] columnWidths = {5, 20, 15, 20, 20, 15, 10, 10, 10, 20, 20, 20, 15, 10, 10, 10};
        
        // 获取文件名
        String fileName = (String) exportParams.get("fileName");
        if (fileName == null || fileName.trim().isEmpty()) {
            fileName = "店铺违规信息";
        }

        // 执行导出
        excelExportService.exportExcel(
            taskId,
            fileName,
            "违规信息",
            headers,
            columnWidths,
            new ShopViolationDataProcessor(exportParams)
        );
    }
    
    /**
     * 店铺违规信息数据处理器
     */
    private class ShopViolationDataProcessor implements ExcelExportService.ExcelDataProcessor {
        private final Map<String, Object> exportParams;
        
        public ShopViolationDataProcessor(Map<String, Object> exportParams) {
            this.exportParams = exportParams;
        }
        
        @Override
        public void prepareData(ExcelExportService.ExcelExportContext context) {
            try {
                Map<String, Object> queryParamsMap = (Map<String, Object>) exportParams.get("queryParams");
                Object userId = exportParams.get("userId");
                ShopViolationInfoQueryDTO queryDTO = buildQueryDTO(queryParamsMap);
                
                // 判断导出类型
                String exportType = (String) exportParams.get("exportType");
                if ("page".equals(exportType)) {
                    // 当前页数据，使用查询参数中的页码和每页条数
                } else if ("custom".equals(exportType)) {
                    // 自定义页数导出
                    Object exportPageCount = queryParamsMap.get("exportPageCount");
                    int pageCount = 1;
                    if (exportPageCount instanceof Number) {
                        pageCount = ((Number) exportPageCount).intValue();
                    } else if (exportPageCount != null) {
                        try {
                            pageCount = Integer.parseInt(exportPageCount.toString());
                        } catch (NumberFormatException e) {
                            log.error("解析exportPageCount失败: {}", exportPageCount, e);
                        }
                    }
                    
                    // 如果是多页导出，调整pageSize以包含所有页的数据
                    if (pageCount > 1) {
                        queryDTO.setPageSize(queryDTO.getPageSize() * pageCount);
                    }
                } else if ("all".equals(exportType)) {
                    // 设置为大值以获取所有数据
                    queryDTO.setPageNum(1);
                    queryDTO.setPageSize(10000);
                }
                
                // 判断用户是否为管理员或拥有全部数据权限
                boolean ignorePermissionCheck = false;
                if (userId != null) {
                    Long uid = null;
                    if (userId instanceof Number) {
                        uid = ((Number) userId).longValue();
                    } else {
                        try {
                            uid = Long.parseLong(userId.toString());
                        } catch (NumberFormatException e) {
                            log.error("解析userId失败: {}", userId, e);
                        }
                    }
                    
                    if (uid != null) {
                        boolean isAdmin = userService.isAdmin(uid);
                        String permissionType = dataPermissionService.getUserMaxDataPermission(uid);
                        boolean hasFullDataPermission = "2".equals(permissionType);
                        
                        if (isAdmin || hasFullDataPermission) {
                            ignorePermissionCheck = true;
                        }
                    }
                }
                
                // 查询数据
                ShopViolationInfoVO violationInfoVO = shopViolationInfoService.getViolationInfoListWithDetail(queryDTO);
                List<ShopViolationInfoDTO> items = violationInfoVO.getItems();
                
                // 设置到上下文
                context.setData(items);
                context.setDataSize(items.size());
                
                log.info("店铺违规信息导出数据准备完成，共 {} 条记录", items.size());
            } catch (Exception e) {
                log.error("准备导出数据时发生错误", e);
                throw e;
            }
        }
        
        /**
         * 构建查询参数对象
         */
        private ShopViolationInfoQueryDTO buildQueryDTO(Map<String, Object> queryParamsMap) {
            ShopViolationInfoQueryDTO queryDTO = new ShopViolationInfoQueryDTO();
            
            if (queryParamsMap != null) {
                // 处理页码和每页条数
                if (queryParamsMap.get("pageNum") != null) {
                    Object pageObj = queryParamsMap.get("pageNum");
                    if (pageObj instanceof Number) {
                        queryDTO.setPageNum(((Number) pageObj).intValue());
                    } else if (pageObj != null) {
                        try {
                            queryDTO.setPageNum(Integer.valueOf(pageObj.toString()));
                        } catch (Exception e) {
                            log.error("解析pageNum失败: {}", pageObj, e);
                        }
                    }
                }
                
                if (queryParamsMap.get("pageSize") != null) {
                    Object sizeObj = queryParamsMap.get("pageSize");
                    if (sizeObj instanceof Number) {
                        queryDTO.setPageSize(((Number) sizeObj).intValue());
                    } else if (sizeObj != null) {
                        try {
                            queryDTO.setPageSize(Integer.valueOf(sizeObj.toString()));
                        } catch (Exception e) {
                            log.error("解析pageSize失败: {}", sizeObj, e);
                        }
                    }
                }
                
                // 处理店铺ID
                if (queryParamsMap.get("shopIds") != null) {
                    Object shopIdsObj = queryParamsMap.get("shopIds");
                    if (shopIdsObj instanceof List) {
                        List<?> shopIdList = (List<?>) shopIdsObj;
                        List<Long> longShopIds = new ArrayList<>();
                        for (Object shopIdObj : shopIdList) {
                            if (shopIdObj instanceof Integer) {
                                longShopIds.add(((Integer) shopIdObj).longValue());
                            } else if (shopIdObj instanceof Long) {
                                longShopIds.add((Long) shopIdObj);
                            } else if (shopIdObj != null) {
                                try {
                                    longShopIds.add(Long.valueOf(shopIdObj.toString()));
                                } catch (NumberFormatException e) {
                                    log.error("转换shopId失败: {}", shopIdObj, e);
                                }
                            }
                        }
                        queryDTO.setShopIds(longShopIds);
                    }
                }
                
                // 处理其他查询条件
                if (queryParamsMap.get("punishSn") != null) {
                    queryDTO.setPunishSn((String) queryParamsMap.get("punishSn"));
                }
                
                if (queryParamsMap.get("subPurchaseOrderSn") != null) {
                    queryDTO.setSubPurchaseOrderSn((String) queryParamsMap.get("subPurchaseOrderSn"));
                }
                
                if (queryParamsMap.get("punishStatus") != null) {
                    Object statusObj = queryParamsMap.get("punishStatus");
                    if (statusObj instanceof Number) {
                        queryDTO.setPunishStatus(((Number) statusObj).intValue());
                    } else if (statusObj != null) {
                        try {
                            queryDTO.setPunishStatus(Integer.valueOf(statusObj.toString()));
                        } catch (Exception e) {
                            log.error("解析punishStatus失败: {}", statusObj, e);
                        }
                    }
                }
                
                // 处理时间范围
                if (queryParamsMap.get("violationTimeBegin") != null) {
                    Object timeBeginObj = queryParamsMap.get("violationTimeBegin");
                    if (timeBeginObj instanceof String) {
                        try {
                            queryDTO.setViolationTimeBegin(DateUtils.parseLocalDateTime((String) timeBeginObj));
                        } catch (Exception e) {
                            log.error("解析violationTimeBegin失败: {}", timeBeginObj, e);
                        }
                    }
                }
                
                if (queryParamsMap.get("violationTimeEnd") != null) {
                    Object timeEndObj = queryParamsMap.get("violationTimeEnd");
                    if (timeEndObj instanceof String) {
                        try {
                            queryDTO.setViolationTimeEnd(DateUtils.parseLocalDateTime((String) timeEndObj));
                        } catch (Exception e) {
                            log.error("解析violationTimeEnd失败: {}", timeEndObj, e);
                        }
                    }
                }
            }
            
            return queryDTO;
        }
        
        @Override
        public void prepareImageTasks(ExcelExportService.ExcelExportContext context, File tempDir, List<ExcelExportUtils.ImageDownloadTask> downloadTasks) {
            // 店铺违规信息导出不需要下载图片
        }
        
        @Override
        public void fillExcelData(ExcelExportService.ExcelExportContext context, Sheet sheet, CellStyle contentStyle) {
            @SuppressWarnings("unchecked")
            List<ShopViolationInfoDTO> items = (List<ShopViolationInfoDTO>) context.getData();
            
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            
            // 填充数据
            for (int i = 0; i < items.size(); i++) {
                ShopViolationInfoDTO item = items.get(i);
                Row row = sheet.createRow(i + 1);
                
                // 设置单元格内容
                int colIndex = 0;
                ExcelExportUtils.setCellValue(row, colIndex++, String.valueOf(i + 1), contentStyle);
                ExcelExportUtils.setCellValue(row, colIndex++, item.getShopRemark() != null ? item.getShopRemark() : "", contentStyle);
                ExcelExportUtils.setCellValue(row, colIndex++, item.getShopName() != null ? item.getShopName() : "", contentStyle);
                ExcelExportUtils.setCellValue(row, colIndex++, item.getPunishSn() != null ? item.getPunishSn() : "", contentStyle);
                ExcelExportUtils.setCellValue(row, colIndex++, item.getSubPurchaseOrderSn() != null ? item.getSubPurchaseOrderSn() : "", contentStyle);
                ExcelExportUtils.setCellValue(row, colIndex++, item.getProductSkuId() != null ? String.valueOf(item.getProductSkuId()) : "", contentStyle);
                ExcelExportUtils.setCellValue(row, colIndex++, item.getStockQuantity() != null ? String.valueOf(item.getStockQuantity()) : "0", contentStyle);
                ExcelExportUtils.setCellValue(row, colIndex++, item.getLackQuantity() != null ? String.valueOf(item.getLackQuantity()) : "0", contentStyle);
                ExcelExportUtils.setCellValue(row, colIndex++, item.getUnqualifiedQuantity() != null ? String.valueOf(item.getUnqualifiedQuantity()) : "0", contentStyle);
                ExcelExportUtils.setCellValue(row, colIndex++, item.getPunishFirstTypeDesc() != null ? item.getPunishFirstTypeDesc() : "", contentStyle);
                ExcelExportUtils.setCellValue(row, colIndex++, item.getPunishSecondTypeDesc() != null ? item.getPunishSecondTypeDesc() : "", contentStyle);
                ExcelExportUtils.setCellValue(row, colIndex++, item.getViolationTime() != null ? item.getViolationTime().format(formatter) : "", contentStyle);
                ExcelExportUtils.setCellValue(row, colIndex++, item.getPunishAmount() != null ? item.getPunishAmount().toString() : "0", contentStyle);
                ExcelExportUtils.setCellValue(row, colIndex++, item.getPunishAmountCurrency() != null ? item.getPunishAmountCurrency() : "", contentStyle);
                ExcelExportUtils.setCellValue(row, colIndex++, formatPunishStatus(item.getPunishStatus()), contentStyle);
                ExcelExportUtils.setCellValue(row, colIndex++, formatViewDetailsStatus(item.getViewDetailsStatus()), contentStyle);
            }
        }
        
        /**
         * 格式化违规状态
         */
        private String formatPunishStatus(Integer status) {
            if (status == null) return "未知";
            
            switch (status) {
                case 0: return "处罚中";
                case 1: return "已完成";
                case 2: return "已废弃";
                default: return "未知";
            }
        }
        
        /**
         * 格式化查看状态
         */
        private String formatViewDetailsStatus(Integer status) {
            if (status == null) return "未知";
            
            switch (status) {
                case 0: return "未查看";
                case 1: return "已查看";
                default: return "未知";
            }
        }
    }
} 