package com.xiao.temu.modules.violation.vo;

import com.xiao.temu.modules.violation.dto.ShopViolationInfoDTO;
import lombok.Data;

import java.util.List;

/**
 * 店铺违规信息VO
 */
@Data
public class ShopViolationInfoVO {
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 当前页码
     */
    private Integer pageNum;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
    
    /**
     * 违规信息列表
     */
    private List<ShopViolationInfoDTO> items;
    
    /**
     * 构造方法
     * 
     * @param records 记录列表
     * @param total 总记录数
     * @param pageNum 当前页码
     * @param pageSize 每页大小
     */
    public ShopViolationInfoVO(List<ShopViolationInfoDTO> records, Long total, Integer pageNum, Integer pageSize) {
        this.items = records;
        this.total = total;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }
} 