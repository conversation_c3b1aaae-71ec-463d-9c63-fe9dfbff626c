<template>
  <AppLayout>
    <!-- 搜索区域 -->
    <template #search>
      <SearchCard>
        <div class="search-form">
          <!-- 店铺 -->
          <div class="search-item">
            <div class="search-label">店铺</div>
            <el-select v-model="queryParams.shopIds" placeholder="请选择店铺" clearable @change="handleShopChange"
              @clear="handleClearShops" size="small" class="search-input" multiple collapse-tags filterable
              :filter-method="filterShops">
              <!-- 添加全选选项 -->
              <el-option key="select-all" label="全选" :value="-1" />
              <el-option v-for="shop in filteredShops" :key="shop.shopId" :label="shop.shopName" :value="shop.shopId" />
            </el-select>
          </div>

          <!-- 违规单号 -->
          <div class="search-item">
            <div class="search-label">违规单号</div>
            <el-input v-model="queryParams.punishSn" placeholder="请输入违规单号" clearable size="small" class="search-input" />
          </div>

          <!-- 违规时间 -->
          <div class="search-item">
            <div class="search-label">违规时间</div>
            <el-date-picker v-model="timeRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" value-format="YYYY-MM-DD" @change="handleTimeRangeChange" size="small"
              class="search-input date-picker-input" />
          </div>

          <!-- 备货单号 -->
          <div class="search-item">
            <div class="search-label">备货单号</div>
            <el-input v-model="queryParams.subPurchaseOrderSn" placeholder="请输入备货单号" clearable size="small" class="search-input" />
          </div>
        </div>

        <!-- 第二行：违规状态 + 按钮 -->
        <div class="bottom-row">
          <!-- 违规状态 -->
          <div class="left-side">
            <div class="search-label">违规状态</div>
            <el-select v-model="queryParams.punishStatus" placeholder="请选择违规状态" clearable size="small" class="status-select">
              <el-option label="公示中(待申诉)" :value="0" />
              <el-option label="已申诉(处理中)" :value="5" />
              <el-option label="全部" :value="null" />
            </el-select>
          </div>

          <!-- 按钮组 -->
          <div class="right-side">
            <div class="search-buttons">
              <el-button type="primary" @click="handleQuery" size="small" class="action-button">
                <el-icon>
                  <Search />
                </el-icon> 搜索
              </el-button>
              <el-button @click="resetQuery" size="small" class="action-button">
                <el-icon>
                  <Refresh />
                </el-icon> 重置
              </el-button>
              <el-button type="success" @click="handleExport" size="small" class="action-button">
                <el-icon>
                  <Download />
                </el-icon> 导出Excel
              </el-button>
            </div>
          </div>
        </div>
      </SearchCard>
    </template>

    <!-- 排序区域 -->
    <div class="sort-container">
      <span class="sort-label">排序：</span>
      <el-radio-group v-model="sortOption" size="small" @change="handleSortChange">
        <el-radio-button label="violationTime">按违规时间</el-radio-button>
        <el-radio-button label="shopName">按店铺名称</el-radio-button>
        <el-radio-button label="punishAmount">按罚款金额</el-radio-button>
        <el-radio-button label="stockQuantity">按库存数量</el-radio-button>
        <el-radio-button label="lackQuantity">按缺货数量</el-radio-button>
        <el-radio-button label="unqualifiedQuantity">按不合格数量</el-radio-button>
      </el-radio-group>
      <el-radio-group v-model="sortDirection" size="small" @change="handleSortChange" style="margin-left: 10px;">
        <el-radio-button label="ascending">
          <el-icon><Sort /></el-icon> 升序
        </el-radio-button>
        <el-radio-button label="descending">
          <el-icon><Sort /></el-icon> 降序
        </el-radio-button>
      </el-radio-group>
    </div>

    <!-- 表格内容区域 -->
    <template #table>
      <TableCard>
        <!-- 表格容器 -->
        <el-table v-loading="loading" :data="sortedViolationInfos" border style="width: 100%" size="small"
          class="compact-table" @sort-change="handleTableSortChange" fit>
          <template #empty>
            <EmptyTips message="请选择店铺后点击搜索按钮查询数据（已默认设置最近7天）" />
          </template>
          <el-table-column type="index" label="序号" width="50" min-width="50" align="center" />
          <el-table-column prop="shopName" label="店铺信息" min-width="120" show-overflow-tooltip>
            <template #default="scope">
              <div class="shop-info">
                <div class="shop-name">{{ scope.row.shopName }}</div>
                <div v-if="scope.row.shopRemark" class="shop-remark">{{ scope.row.shopRemark }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="punishSn" label="违规编号" min-width="140" show-overflow-tooltip />
          <el-table-column prop="subPurchaseOrderSn" label="备货单号" min-width="140" show-overflow-tooltip />
          <el-table-column prop="productSkuId" label="商品SKU ID" min-width="120" show-overflow-tooltip />
          <el-table-column prop="stockQuantity" label="备货件数" min-width="80" width="80" align="center" />
          <el-table-column prop="lackQuantity" label="缺货件数" min-width="80" width="80" align="center" />
          <el-table-column prop="unqualifiedQuantity" label="质量问题件数" min-width="80" width="80" align="center" />
          <el-table-column prop="punishFirstTypeDesc" label="违规类型" min-width="120" show-overflow-tooltip />
          <el-table-column prop="punishSecondTypeDesc" label="具体原因" min-width="120" show-overflow-tooltip />
          <el-table-column prop="violationStartTime" label="违规时间" min-width="140" sortable="custom">
            <template #default="scope">
              {{ formatTime(scope.row.violationStartTime) }}
            </template>
          </el-table-column>
          <el-table-column label="违规金额(CNY)" min-width="120" sortable="custom" prop="punishAmount" align="right">
            <template #default="scope">
              <span class="amount">¥{{ formatAmount(scope.row.punishAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" min-width="100">
            <template #default="scope">
              <div class="status-wrapper">
                <span class="status-dot" :class="getStatusDotClass(scope.row.punishStatus)"></span>
                <span class="status-text">{{ getStatusText(scope.row.punishStatus) }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </TableCard>
    </template>

    <!-- 固定在底部的分页区域 -->
    <template #pagination>
      <PaginationBar :current-page="Number(queryParams.pageNum)" :page-size="Number(queryParams.pageSize)" :total="total"
        @update:current-page="(val) => queryParams.pageNum = Number(val)"
        @update:page-size="(val) => queryParams.pageSize = Number(val)" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </template>

    <!-- 弹窗区域 -->
    <template #dialogs>
      <!-- 详情弹窗 -->
      <el-dialog v-model="detailDialog.visible" title="违规详情" width="700px" destroy-on-close>
        <div v-loading="detailDialog.loading" class="violation-detail">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="违规单号" :span="2">{{ detailData.punishSn }}</el-descriptions-item>
            <el-descriptions-item label="店铺信息">
              <div class="shop-info">
                <div class="shop-name">{{ detailData.shopName }}</div>
                <div v-if="detailData.shopRemark" class="shop-remark">{{ detailData.shopRemark }}</div>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="备货单号">{{ detailData.subPurchaseOrderSn }}</el-descriptions-item>
            <el-descriptions-item label="商品SKU ID">{{ detailData.productSkuId || '无' }}</el-descriptions-item>
            <el-descriptions-item label="库存/缺货/不合格数量">
              {{ detailData.stockQuantity || 0 }} / {{ detailData.lackQuantity || 0 }} / {{ detailData.unqualifiedQuantity || 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="违规类型">{{ detailData.punishFirstTypeDesc }}</el-descriptions-item>
            <el-descriptions-item label="具体原因">{{ detailData.punishSecondTypeDesc }}</el-descriptions-item>
            <el-descriptions-item label="违规时间">{{ formatTime(detailData.violationStartTime) }}</el-descriptions-item>
            <el-descriptions-item label="罚款金额">
              {{ formatAmount(detailData.punishAmount) }} {{ detailData.punishAmountCurrency }}
            </el-descriptions-item>
            <el-descriptions-item label="违规状态">
              <div class="status-wrapper">
                <span class="status-dot" :class="getStatusDotClass(detailData.punishStatus)"></span>
                <span class="status-text">{{ getStatusText(detailData.punishStatus) }}</span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="申诉状态" v-if="detailData.detail?.appealStatus !== undefined">
              <el-tag :type="getAppealStatusType(detailData.detail?.appealStatus)">
                {{ getAppealStatusText(detailData.detail?.appealStatus) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 申诉详情 -->
          <template v-if="detailData.detail?.appealStatus !== undefined && detailData.detail?.appealStatus > 0">
            <div class="detail-section-title">申诉信息</div>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="申诉单号" :span="2">{{ detailData.detail?.appealSn || '-' }}</el-descriptions-item>
              <el-descriptions-item label="申诉时间" :span="2">
                {{ detailData.detail?.appealTime ? formatTime(detailData.detail.appealTime) : '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="申诉理由" :span="2">{{ detailData.detail?.appealReason || '-' }}</el-descriptions-item>
              <el-descriptions-item label="申诉材料" :span="2">
                <div class="appeal-images" v-if="detailData.detail?.appealMaterial && detailData.detail?.appealMaterial.length > 0">
                  <div v-for="(url, index) in parseAppealMaterial(detailData.detail?.appealMaterial)" :key="index" class="appeal-image-item">
                    <img :src="url" alt="申诉材料" @click="handlePreviewImage(url)" class="appeal-image" />
                  </div>
                </div>
                <span v-else>无申诉材料</span>
              </el-descriptions-item>
            </el-descriptions>
          </template>

          <!-- 处理结果 -->
          <template v-if="detailData.detail?.operateResult">
            <div class="detail-section-title">处理结果</div>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="处理结果" :span="2">{{ detailData.detail?.operateResult }}</el-descriptions-item>
              <el-descriptions-item label="处理时间" :span="2">
                {{ detailData.detail?.operateTime ? formatTime(detailData.detail.operateTime) : '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="处理备注" :span="2">{{ detailData.detail?.operateRemark || '-' }}</el-descriptions-item>
            </el-descriptions>
          </template>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="detailDialog.visible = false">关闭</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 申诉弹窗 -->
      <el-dialog v-model="appealDialog.visible" title="提交申诉" width="700px" destroy-on-close>
        <div class="appeal-form">
          <el-form ref="appealFormRef" :model="appealForm" :rules="appealRules" label-width="100px">
            <el-form-item label="违规单号">
              <span>{{ appealForm.punishSn }}</span>
            </el-form-item>
            <el-form-item label="备货单号">
              <span>{{ appealForm.subPurchaseOrderSn }}</span>
            </el-form-item>
            <el-form-item label="违规类型">
              <span>{{ appealForm.punishTypeDesc }}</span>
            </el-form-item>
            <el-form-item label="申诉理由" prop="appealReason">
              <el-input v-model="appealForm.appealReason" type="textarea" :rows="4" placeholder="请输入申诉理由" />
            </el-form-item>
            <el-form-item label="申诉材料">
              <el-upload
                action="#"
                list-type="picture-card"
                :auto-upload="false"
                :on-change="handleUploadChange"
                :limit="5"
                multiple
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">
                支持jpg、png格式，单张图片不超过2MB，最多上传5张
              </div>
            </el-form-item>
          </el-form>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="appealDialog.visible = false">取消</el-button>
            <el-button type="primary" @click="submitAppeal" :loading="appealDialog.submitting">
              提交申诉
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 图片预览 -->
      <ImagePreview v-model:visible="imagePreviewVisible" :image-url="currentPreviewImage" />
      
      <!-- 导出对话框 -->
      <ExportDialog
        v-model:visible="exportDialog.visible"
        data-type="violation"
        :default-file-name="exportFileName"
        :current-page-data="sortedViolationInfos"
        :total="total"
        :fetch-data-fn="fetchViolationData"
        :query-params="queryParams"
        :use-backend-export="true"
      />
    </template>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, onUnmounted } from 'vue'
import type { Ref } from 'vue'
import { Search, Refresh, Sort, Download, Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, type FormInstance, type UploadFile } from 'element-plus'
import { useViolationStore } from '@/store'
import type { Shop } from '@/types/shop'
import type { ViolationInfoItem, ViolationQueryParams, CompleteViolationInfo } from '@/types/local/violation'
import {
  AppLayout,
  SearchCard,
  TableCard,
  PaginationBar,
  ImagePreview,
  EmptyTips,
  ExportDialog
} from '@/components/temu'
import { useRouter } from 'vue-router'
import { useExportTaskStore } from '@/store'
import { formatTime } from '@/utils/format'
import { createExportTask, getExportProgress, getExportDownloadUrl } from '@/api/local/violation'
import axios from 'axios'
import { getToken } from '@/utils/auth'

// 获取store实例
const violationStore = useViolationStore()
const router = useRouter()
const exportTaskStore = useExportTaskStore()

// 加载状态
const loading = computed(() => violationStore.loading)

// 店铺列表
const shops: Ref<Shop[]> = ref([])

// 违规信息列表
const violationInfos = computed<ViolationInfoItem[]>(() => {
  return violationStore.violationResult?.items || []
})

// 总记录数
const total = computed(() => violationStore.violationResult?.total || 0)

// 排序选项
const sortOption = ref('violationStartTime')
const sortDirection = ref('descending')

// 按排序选项处理后的列表
const sortedViolationInfos = computed<ViolationInfoItem[]>(() => {
  if (!violationInfos.value || violationInfos.value.length === 0) {
    return []
  }

  // 创建一个新数组进行排序
  const sortedData = [...violationInfos.value]

  if (sortOption.value === 'violationStartTime') {
    // 按违规时间排序
    sortedData.sort((a, b) => {
      const timeA = a.violationStartTime || 0
      const timeB = b.violationStartTime || 0
      return sortDirection.value === 'ascending' ? timeA - timeB : timeB - timeA
    })
  } else if (sortOption.value === 'shopName') {
    // 按店铺名称排序
    sortedData.sort((a, b) => {
      const compareShop = a.shopName.localeCompare(b.shopName)
      
      if (compareShop === 0) {
        // 如果店铺名称相同，再按时间排序
        const timeA = a.violationStartTime || 0
        const timeB = b.violationStartTime || 0
        return sortDirection.value === 'ascending' ? timeA - timeB : timeB - timeA
      }
      
      return sortDirection.value === 'ascending' ? compareShop : -compareShop
    })
  } else if (sortOption.value === 'punishAmount') {
    // 按罚款金额排序
    sortedData.sort((a, b) => {
      const amountA = a.punishAmount || 0
      const amountB = b.punishAmount || 0
      
      if (amountA === amountB) {
        // 如果金额相同，再按时间排序
        const timeA = a.violationStartTime || 0
        const timeB = b.violationStartTime || 0
        return sortDirection.value === 'ascending' ? timeA - timeB : timeB - timeA
      }
      
      return sortDirection.value === 'ascending' ? amountA - amountB : amountB - amountA
    })
  } else if (sortOption.value === 'stockQuantity') {
    // 按库存数量排序
    sortedData.sort((a, b) => {
      const quantityA = a.stockQuantity || 0
      const quantityB = b.stockQuantity || 0
      
      if (quantityA === quantityB) {
        // 如果数量相同，再按时间排序
        const timeA = a.violationStartTime || 0
        const timeB = b.violationStartTime || 0
        return sortDirection.value === 'ascending' ? timeA - timeB : timeB - timeA
      }
      
      return sortDirection.value === 'ascending' ? quantityA - quantityB : quantityB - quantityA
    })
  } else if (sortOption.value === 'lackQuantity') {
    // 按缺货数量排序
    sortedData.sort((a, b) => {
      const quantityA = a.lackQuantity || 0
      const quantityB = b.lackQuantity || 0
      
      if (quantityA === quantityB) {
        // 如果数量相同，再按时间排序
        const timeA = a.violationStartTime || 0
        const timeB = b.violationStartTime || 0
        return sortDirection.value === 'ascending' ? timeA - timeB : timeB - timeA
      }
      
      return sortDirection.value === 'ascending' ? quantityA - quantityB : quantityB - quantityA
    })
  } else if (sortOption.value === 'unqualifiedQuantity') {
    // 按不合格数量排序
    sortedData.sort((a, b) => {
      const quantityA = a.unqualifiedQuantity || 0
      const quantityB = b.unqualifiedQuantity || 0
      
      if (quantityA === quantityB) {
        // 如果数量相同，再按时间排序
        const timeA = a.violationStartTime || 0
        const timeB = b.violationStartTime || 0
        return sortDirection.value === 'ascending' ? timeA - timeB : timeB - timeA
      }
      
      return sortDirection.value === 'ascending' ? quantityA - quantityB : quantityB - quantityA
    })
  }

  return sortedData
})

// 查询参数
const queryParams = reactive<ViolationQueryParams>({
  shopIds: [],
  pageNum: 1,
  pageSize: 10,
  punishSn: '',
  subPurchaseOrderSn: '',
  punishStatus: null,
  violationTimeBegin: undefined,
  violationTimeEnd: undefined
})

// 日期范围
const timeRange = ref<[string, string] | null>(null)

// 详情弹窗
const detailDialog = reactive({
  visible: false,
  loading: false
})

// 申诉弹窗
const appealDialog = reactive({
  visible: false,
  submitting: false
})

// 申诉表单
const appealFormRef = ref<FormInstance>()
const appealForm = reactive({
  id: 0,
  shopId: 0,
  punishSn: '',
  subPurchaseOrderSn: '',
  punishTypeDesc: '',
  appealReason: '',
  appealMaterial: [] as UploadFile[]
})

// 申诉表单校验规则
const appealRules = {
  appealReason: [
    { required: true, message: '请输入申诉理由', trigger: 'blur' },
    { min: 5, max: 500, message: '申诉理由长度在 5 到 500 个字符', trigger: 'blur' }
  ]
}

// 详情数据
const detailData = reactive<CompleteViolationInfo>({} as CompleteViolationInfo)

// 图片预览
const imagePreviewVisible = ref(false)
const currentPreviewImage = ref('')

// 导出相关
const exportDialog = reactive({
  visible: false
})
const exportFileName = ref('店铺违规信息')

// 是否有可申诉的条目
const hasAppealableItems = computed(() => {
  return violationInfos.value.some(item => item.punishStatus === 0);
});

// 添加全选状态跟踪变量
const isAllSelected = ref(false)

// 添加过滤后的店铺列表
const filteredShops = ref<Shop[]>([])
// 用于存储过滤搜索关键词
const filterKeyword = ref('')

// 处理表格排序变更
const handleTableSortChange = (params: any) => {
  if (params.prop) {
    if (params.prop === 'violationTime') {
      sortOption.value = 'violationStartTime'
    } else {
      sortOption.value = params.prop
    }
    sortDirection.value = params.order || 'descending'
  }
}

// 处理排序变更
const handleSortChange = () => {
  // 当用户切换排序选项时，刷新排序结果
  console.log(`排序方式改变: ${sortOption.value}, 方向: ${sortDirection.value}`)
}

// 获取违规状态对应的类型（用于标签颜色）
const getStatusType = (status: number | undefined) => {
  if (status === undefined) return 'info'
  
  switch (status) {
    case 0: return 'warning' // 公示中(待申诉)
    case 5: return 'primary' // 已申诉(平台处理中)
    default: return 'info'
  }
}

// 获取状态圆点的CSS类
const getStatusDotClass = (status: number | undefined) => {
  if (status === undefined) return 'status-dot-info'
  
  switch (status) {
    case 0: return 'status-dot-error' // 公示中(待申诉)
    case 1: return 'status-dot-success' // 已取消违规处理
    case 3: return 'status-dot-warning' // 已按违规处理
    case 4: return 'status-dot-error' // 逾期未申诉
    case 5: return 'status-dot-primary' // 已申诉(平台处理中)
    case 6: return 'status-dot-error' // 申诉驳回
    default: return 'status-dot-info'
  }
}

// 获取申诉状态对应的类型（用于标签颜色）
const getAppealStatusType = (status: number | undefined) => {
  if (status === undefined) return 'info'
  
  switch (status) {
    case 0: return 'info' // 未申诉
    case 1: return 'primary' // 申诉中
    case 2: return 'success' // 申诉成功
    case 3: return 'danger' // 申诉失败
    default: return 'info'
  }
}

// 获取申诉状态文本
const getAppealStatusText = (status: number | undefined) => {
  if (status === undefined) return '未知'
  
  switch (status) {
    case 0: return '未申诉'
    case 1: return '申诉中'
    case 2: return '申诉成功'
    case 3: return '申诉失败'
    default: return '未知'
  }
}

// 格式化金额，保留两位小数
const formatAmount = (amount: number | undefined) => {
  if (amount === undefined || amount === null) return '0.00'
  // 将角转换为元（除以100）
  const amountInYuan = amount / 100
  return amountInYuan.toFixed(2)
}

// 获取状态文本
const getStatusText = (status: number | undefined) => {
  if (status === undefined) return '未知'
  
  switch (status) {
    case 0: return '公示中(待申诉)'
    case 1: return '已取消违规处理'
    case 3: return '已按违规处理'
    case 4: return '逾期未申诉'
    case 5: return '已申诉(处理中)'
    case 6: return '申诉驳回'
    default: return '未知'
  }
}

// 解析申诉材料
const parseAppealMaterial = (material: string | string[] | undefined) => {
  if (!material) return []
  
  if (Array.isArray(material)) {
    return material
  }
  
  try {
    return JSON.parse(material)
  } catch (e) {
    return []
  }
}

// 获取店铺列表
const getShops = async () => {
  try {
    const result = await violationStore.loadShops()
    shops.value = result || []
    // 初始化过滤后的店铺列表
    filteredShops.value = [...shops.value]

    // 如果只有一个店铺且没有选择店铺，自动选择该店铺
    if (shops.value.length === 1 && queryParams.shopIds && queryParams.shopIds.length === 0) {
      const shopId = shops.value[0].shopId
      if (shopId !== null) {
        queryParams.shopIds = [shopId]
        // 重新加载数据
        if (queryParams.shopIds && queryParams.shopIds.length > 0) {
          fetchData()
        }
      }
    }
  } catch (error) {
    console.error('获取店铺列表失败:', error)
    ElMessage.error('获取店铺列表失败')
  }
}

// 过滤店铺方法
const filterShops = (query: string) => {
  filterKeyword.value = query
  if (query) {
    filteredShops.value = shops.value.filter(shop => 
      shop.shopName.toLowerCase().includes(query.toLowerCase())
    )
    
    // 当过滤条件发生变化时，重置全选状态
    isAllSelected.value = false
    
    // 如果当前已选中店铺，保留它们（如果它们在过滤结果中）
    if (queryParams.shopIds && queryParams.shopIds.length > 0) {
      // 保留那些在过滤结果中的店铺ID
      queryParams.shopIds = queryParams.shopIds.filter(id => 
        filteredShops.value.some(shop => shop.shopId === id)
      )
    }
  } else {
    filteredShops.value = [...shops.value]
    
    // 检查是否全部店铺都被选中
    if (queryParams.shopIds && shops.value.length > 0) {
      const validShopIds = queryParams.shopIds.filter(id => id !== null) as number[]
      isAllSelected.value = validShopIds.length === shops.value.length &&
        shops.value.every(shop => shop.shopId !== null && validShopIds.includes(shop.shopId))
    }
  }
}

// 处理清除所有店铺选择
const handleClearShops = () => {
  // 重置全选状态
  isAllSelected.value = false
  queryParams.shopIds = []
  violationStore.setShopIds(queryParams.shopIds || [])
}

// 处理店铺变更
const handleShopChange = (shopIds: number[]) => {
  // 检查是否点击了全选选项
  if (shopIds.includes(-1)) {
    // 如果当前不是全选状态，则进行全选
    if (!isAllSelected.value) {
      // 标记为全选状态
      isAllSelected.value = true
      
      // 全选所有店铺（过滤后的）
      let allShopIds: number[] = []
      
      // 如果有搜索关键词，只选择过滤后的店铺
      if (filterKeyword.value) {
        // 过滤掉可能的null值
        allShopIds = filteredShops.value
          .filter(shop => shop.shopId !== null)
          .map(shop => shop.shopId as number)
      } else {
        // 否则选择所有店铺
        allShopIds = shops.value
          .filter(shop => shop.shopId !== null)
          .map(shop => shop.shopId as number)
      }
      
      // 更新选中的店铺，并移除全选选项值(-1)
      queryParams.shopIds = allShopIds
      
      // 避免-1被包含在选中项中
      setTimeout(() => {
        if (queryParams.shopIds && queryParams.shopIds.includes(-1)) {
          const index = queryParams.shopIds.indexOf(-1)
          if (index > -1) {
            queryParams.shopIds.splice(index, 1)
          }
        }
      }, 0)
    } else {
      // 如果当前已经是全选状态，则取消全选
      isAllSelected.value = false
      queryParams.shopIds = []
    }
  } else {
    // 如果选择了具体的店铺且数量与总店铺数相同，标记为全选状态
    const totalShops = filterKeyword.value ? 
      filteredShops.value.filter(shop => shop.shopId !== null).length : 
      shops.value.filter(shop => shop.shopId !== null).length
    isAllSelected.value = shopIds.length > 0 && shopIds.length === totalShops
  }
  
  // 更新store中的shopIds
  violationStore.setShopIds(queryParams.shopIds || [])
}

// 重置查询
const resetQuery = () => {
  queryParams.punishSn = ''
  queryParams.subPurchaseOrderSn = ''
  queryParams.punishStatus = null
  timeRange.value = null
  queryParams.violationTimeBegin = undefined
  queryParams.violationTimeEnd = undefined
  queryParams.shopIds = []
  // 重置全选状态
  isAllSelected.value = false
  setDefaultTimeRange()
}

// 设置默认时间范围（最近7天）
const setDefaultTimeRange = () => {
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - 7)

  const formatDate = (date: Date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  timeRange.value = [formatDate(startDate), formatDate(endDate)]
  queryParams.violationTimeBegin = timeRange.value[0] + 'T00:00:00'
  queryParams.violationTimeEnd = timeRange.value[1] + 'T23:59:59'
}

// 查询数据
const fetchData = async () => {
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请选择至少一个店铺')
    return
  }

  try {
    await violationStore.getViolationList(queryParams)
  } catch (error) {
    console.error('获取违规信息失败:', error)
    ElMessage.error('获取违规信息失败')
  }
}

// 处理查询
const handleQuery = () => {
  queryParams.pageNum = 1
  fetchData()
}

// 处理时间范围变更
const handleTimeRangeChange = (val: [string, string] | null) => {
  if (val) {
    // 添加时间部分，起始日期使用00:00:00，结束日期使用23:59:59
    queryParams.violationTimeBegin = val[0] + 'T00:00:00';
    queryParams.violationTimeEnd = val[1] + 'T23:59:59';
  } else {
    queryParams.violationTimeBegin = undefined;
    queryParams.violationTimeEnd = undefined;
  }
  violationStore.setTimeRange(val);
}

// 处理页大小变更
const handleSizeChange = (val: number) => {
  queryParams.pageSize = val
  fetchData()
}

// 处理当前页变更
const handleCurrentChange = (val: number) => {
  queryParams.pageNum = val
  fetchData()
}

// 查看详情
const handleViewDetail = async (row: ViolationInfoItem) => {
  detailDialog.visible = true
  detailDialog.loading = true
  
  try {
    const violationDetail = await violationStore.getViolationDetail(row.id)
    Object.assign(detailData, violationDetail)
  } catch (error) {
    console.error('获取违规详情失败:', error)
    ElMessage.error('获取违规详情失败')
  } finally {
    detailDialog.loading = false
  }
}

// 处理申诉
const handleAppeal = (row: ViolationInfoItem) => {
  // 重置申诉表单
  appealForm.id = row.id
  appealForm.shopId = row.shopId
  appealForm.punishSn = row.punishSn
  appealForm.subPurchaseOrderSn = row.subPurchaseOrderSn
  appealForm.punishTypeDesc = `${row.punishFirstTypeDesc} - ${row.punishSecondTypeDesc}`
  appealForm.appealReason = ''
  appealForm.appealMaterial = []
  
  // 显示申诉弹窗
  appealDialog.visible = true
  detailDialog.visible = false
}

// 处理上传图片变更
const handleUploadChange = (file: UploadFile) => {
  // 验证文件类型
  const isImage = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png'
  if (!isImage) {
    ElMessage.error('只能上传jpg/png格式的图片！')
    const fileList = appealForm.appealMaterial
    appealForm.appealMaterial = fileList.filter(item => item.uid !== file.uid)
    return
  }
  
  // 验证文件大小
  const isLt2M = (file.raw?.size || 0) / 1024 / 1024 < 2
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过2MB！')
    const fileList = appealForm.appealMaterial
    appealForm.appealMaterial = fileList.filter(item => item.uid !== file.uid)
    return
  }
}

// 提交申诉
const submitAppeal = async () => {
  if (!appealFormRef.value) return
  
  await appealFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    // 这里应该调用接口提交申诉信息
    // 由于目前没有实现这部分接口，这里仅模拟提交成功
    appealDialog.submitting = true
    
    try {
      // 模拟接口调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      ElMessage.success('申诉提交成功')
      appealDialog.visible = false
      
      // 重新加载列表
      fetchData()
    } catch (error) {
      console.error('提交申诉失败:', error)
      ElMessage.error('提交申诉失败')
    } finally {
      appealDialog.submitting = false
    }
  })
}

// 处理预览图片
const handlePreviewImage = (url: string) => {
  if (!url) return
  currentPreviewImage.value = url
  imagePreviewVisible.value = true
}

// 开始轮询导出进度
const startPollingExportProgress = (taskId: string, exportTaskId: string) => {
  console.log('开始轮询导出进度, 任务ID:', taskId);
  
  if (!taskId) {
    console.error('轮询失败：无效的任务ID');
    ElMessage.error('无法获取导出进度：无效的任务ID');
    exportTaskStore.failTask(exportTaskId, '无效的任务ID');
    return;
  }
  
  // 定义取消功能
  let cancelled = false;
  const cancelFn = () => {
    cancelled = true;
  };
  
  // 设置取消函数
  exportTaskStore.setTaskCancelFn(exportTaskId, cancelFn);
  
  // 轮询定时器
  const pollingTimer = window.setInterval(() => {
    // 如果任务已被取消，停止轮询
    if (cancelled) {
      clearInterval(pollingTimer);
      return;
    }
    
    console.log('发送进度查询请求:', taskId);
    
    // 使用API查询进度
    getExportProgress(taskId)
      .then(response => {
        // 如果任务已被取消，停止处理
        if (cancelled) {
          clearInterval(pollingTimer);
          return;
        }
        
        console.log('获取进度响应:', JSON.stringify(response));
        
        // 提取进度信息
        let progressData: any = null;
        
        if (response) {
          if (response.data) {
            progressData = response.data;
          } else {
            progressData = response;
          }
        }
        
        if (!progressData) {
          console.error('无法解析进度数据:', response);
          return;
        }
        
        // 更新进度
        const progress = progressData.progress || 0;
        const message = progressData.message || `正在导出数据 (${progress}%)...`;
        
        // 更新任务进度
        exportTaskStore.updateTaskProgress(exportTaskId, progress);
        
        console.log(`导出进度: ${progress}%, 状态: ${progressData.status}, 消息: ${message || '无'}`);
        
        // 判断任务状态
        const status = (progressData.status || '').toUpperCase();
        
        // 导出完成
        if (status === 'COMPLETED' || status === 'COMPLETE') {
          console.log('导出任务完成，准备下载文件');
          
          clearInterval(pollingTimer);
          
          // 提取任务ID
          const downloadTaskId = progressData.exportTaskId || progressData.taskId || taskId;
          
          // 找到对应的任务信息
          const task = exportTaskStore.tasks.find(t => t.id === exportTaskId);
          const fileName = task ? task.fileName : '店铺违规信息';
          
          // 下载文件 - 使用axios替代链接点击方式
          downloadExportFile(downloadTaskId, fileName, exportTaskId);
        } 
        // 导出失败
        else if (status === 'FAILED' || status === 'FAIL' || status === 'ERROR') {
          console.error('导出任务失败:', progressData.message || progressData.errorMessage || '未知错误');
          
          clearInterval(pollingTimer);
          
          exportTaskStore.failTask(exportTaskId, progressData.message || progressData.errorMessage || '导出失败');
          ElMessage.error(progressData.message || progressData.errorMessage || '导出失败');
        }
      })
      .catch((error) => {
        // 如果任务已被取消，停止处理
        if (cancelled) {
          clearInterval(pollingTimer);
          return;
        }
        
        console.error('获取导出进度失败:', error);
        
        // 不要立即清除定时器，尝试多次
        if (error.message && error.message.includes('404')) {
          console.error('任务不存在，停止轮询');
          clearInterval(pollingTimer);
          ElMessage.error('导出任务不存在，请重试');
          exportTaskStore.failTask(exportTaskId, '导出任务不存在');
        }
      });
  }, 2000); // 2秒一次轮询
}

// 下载导出文件
const downloadExportFile = async (taskId: string, fileName: string, exportTaskId: string) => {
  try {
    // 获取下载链接
    const downloadUrl = getExportDownloadUrl(taskId);
    const loadingMessage = ElMessage({
      message: '正在下载文件...',
      type: 'info',
      duration: 0
    });
    
    // 使用axios下载文件（确保携带token）
    const token = getToken();
    const response = await axios({
      method: 'GET',
      url: downloadUrl,
      responseType: 'blob',
      timeout: 60000, // 60秒超时
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    });
    
    loadingMessage.close();
    
    // 处理下载的文件
    const contentType = response.headers['content-type'];
    const blob = new Blob([response.data], { type: contentType });
    
    // 创建下载链接并触发下载
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${fileName}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    exportTaskStore.completeTask(exportTaskId);
    ElMessage.success('文件下载成功');
  } catch (error) {
    console.error('下载文件失败:', error);
    ElMessage.error('下载文件失败，请重试');
    exportTaskStore.failTask(exportTaskId, '下载文件失败');
  }
}

// 用于防止重复创建任务的标志
const exportTaskCreating = ref(false);

// 修复重复创建任务的问题：确保ExportDialog组件只会调用一次fetchViolationData
const fetchViolationData = async (params: any) => {
  try {
    // 防止重复创建任务
    if (params.isPolling) {
      return { success: true, data: { taskId: params.taskId } };
    }
    
    // 添加重复提交保护
    if (exportTaskCreating.value) {
      console.log('导出任务正在创建中，忽略重复请求');
      return { success: false, data: { items: [], total: 0 } };
    }
    
    exportTaskCreating.value = true;
    
    try {
      // 构造导出请求参数
      const exportParams = {
        queryParams: {
          ...queryParams, // 基础查询参数
          pageNum: params.pageNum || 1,
          pageSize: params.pageSize || 10,
          shopIds: queryParams.shopIds,
          violationTimeBegin: queryParams.violationTimeBegin,
          violationTimeEnd: queryParams.violationTimeEnd,
          punishSn: queryParams.punishSn,
          subPurchaseOrderSn: queryParams.subPurchaseOrderSn,
          punishStatus: queryParams.punishStatus
        },
        fileName: params.fileName,
        exportType: params.exportType
      };
      
      console.log('使用ExportDialog提供的任务ID:', params.taskId);
      
      // 调用创建导出任务接口，返回任务ID
      const res = await createExportTask(exportParams);
      if (res && res.data?.taskId) {
        // 开始轮询任务进度，使用ExportDialog提供的taskId
        if (params.taskId) {
          startPollingExportProgress(res.data.taskId, params.taskId);
        }
        
        // 返回任务信息
        return {
          data: {
            taskId: res.data.taskId
          },
          success: true
        };
      } else {
        if (params.taskId) {
          exportTaskStore.failTask(params.taskId, '创建导出任务失败');
        }
        return { data: { items: [], total: 0 }, success: false };
      }
    } finally {
      // 无论成功失败，最终都重置防重复标志
      setTimeout(() => {
        exportTaskCreating.value = false;
      }, 1000);  // 1秒后重置，防止快速多次点击
    }
  } catch (error) {
    console.error('获取导出数据失败:', error);
    ElMessage.error('获取导出数据失败');
    exportTaskCreating.value = false;
    throw error;
  }
}

// 处理导出
const handleExport = () => {
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请选择至少一个店铺')
    return
  }
  
  if (violationInfos.value.length === 0) {
    ElMessage.warning('没有可导出的数据')
    return
  }
  
  // 防止重复触发
  if (exportTaskCreating.value) {
    ElMessage.warning('导出任务正在处理中，请稍候')
    return
  }
  
  // 生成默认文件名：店铺备注 + 日期时间
  let fileName = '店铺违规信息';
  
  // 获取选中店铺的备注
  if (shops.value && shops.value.length > 0 && queryParams.shopIds && queryParams.shopIds.length > 0) {
    // 查找选中店铺的备注
    const selectedShops = shops.value.filter(shop => 
      shop.shopId !== null && queryParams.shopIds!.includes(shop.shopId)
    );
    if (selectedShops.length > 0) {
      // 提取店铺备注，最多使用3个店铺备注，避免文件名过长
      const shopRemarks = selectedShops.slice(0, 3).map(shop => shop.remark || shop.shopName);
      fileName = shopRemarks.join('-');
      
      // 如果选择的店铺超过3个，添加省略标记
      if (selectedShops.length > 3) {
        fileName += '等';
      }
    }
  }
  
  // 添加日期时间后缀
  const now = new Date();
  const dateStr = now.getFullYear() + 
    (now.getMonth() + 1).toString().padStart(2, '0') + 
    now.getDate().toString().padStart(2, '0');
  const timeStr = now.getHours().toString().padStart(2, '0') + 
    now.getMinutes().toString().padStart(2, '0');
  
  fileName += '_' + dateStr + timeStr;
  
  console.log('导出文件名:', fileName); // 添加调试输出
  
  exportFileName.value = fileName;
  exportDialog.visible = true;
}

// 页面加载时执行
onMounted(() => {
  // 设置默认时间范围（最近7天）
  setDefaultTimeRange()

  // 获取店铺列表
  getShops()
})

// 组件销毁时清理
onUnmounted(() => {
  violationStore.resetQuery()
})
</script>

<style scoped>
/* 搜索表单新样式 - 四列布局 */
.search-form {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 15px;
  align-items: start;
  position: relative;
  padding-bottom: 15px;
}

.search-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.search-label {
  font-size: 13px;
  color: #606266;
}

.search-input {
  width: 100%;
}

.date-picker-input {
  width: 100%;
}

/* 底部行样式 */
.bottom-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: 15px;
  padding-top: 5px;
}

/* 左侧违规状态 */
.left-side {
  display: flex;
  flex-direction: column;
  gap: 6px;
  width: 100%;
  max-width: 400px; /* 与店铺下拉框宽度一致 */
}

/* 右侧按钮组 */
.right-side {
  display: flex;
  justify-content: flex-end;
}

/* 违规状态选择器 */
.status-select {
  width: 100%;
}

/* 按钮组 */
.search-buttons {
  display: flex;
  gap: 8px;
}

/* 排序区域样式 */
.sort-container {
  margin: 10px 0;
  padding: 0 20px;
  display: flex;
  align-items: center;
}

.sort-label {
  margin-right: 10px;
  color: #606266;
  font-size: 14px;
}

/* 金额样式 */
.amount {
  color: #f56c6c;
  font-weight: bold;
}

/* 违规详情样式 */
.violation-detail {
  padding: 10px 0;
}

.detail-section-title {
  margin: 20px 0 10px;
  font-weight: bold;
  color: #409eff;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 5px;
}

/* 申诉材料样式 */
.appeal-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.appeal-image-item {
  width: 100px;
  height: 100px;
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.appeal-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

/* 上传提示 */
.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

/* 紧凑表格样式 */
.compact-table :deep(.el-table__row) {
  height: 60px;
}

.compact-table :deep(.cell) {
  line-height: 1.3;
  padding-left: 5px;
  padding-right: 5px;
  white-space: normal;
}

.compact-table :deep(.el-table__header th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
  text-align: center;
}

.compact-table :deep(.el-table--border th) {
  padding: 8px 0;
}

/* 确保表格能够自适应容器 */
:deep(.el-table) {
  width: 100% !important;
  table-layout: fixed;
}

:deep(.el-table__body-wrapper) {
  overflow-x: auto;
}

/* 状态圆点样式 */
.status-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot-error {
  background-color: #f56c6c;
}

.status-dot-warning {
  background-color: #e6a23c;
}

.status-dot-success {
  background-color: #67c23a;
}

.status-dot-info {
  background-color: #909399;
}

.status-dot-primary {
  background-color: #409eff;
}

.status-text {
  font-size: 13px;
}

/* 店铺信息样式 */
.shop-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.shop-name {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.shop-remark {
  font-size: 14px;
  color: #409EFF;
  line-height: 1.3;
  word-break: break-all;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .search-form {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .bottom-row {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .right-side {
    width: 100%;
    justify-content: flex-start;
    margin-top: 10px;
  }
  
  .search-buttons {
    flex-wrap: wrap;
  }
  
  .sort-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>