<template>
  <el-dialog
    title="店铺详情"
    v-model="visible"
    width="700px"
    append-to-body
  >
    <div class="sensitive-control">
      <el-button type="primary" size="small" plain @click="toggleAllSensitive">
        {{ isAnySensitiveVisible ? '全部隐藏' : '全部显示' }}
      </el-button>
    </div>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="店铺名称">{{ shopInfo.shopName }}</el-descriptions-item>
      <el-descriptions-item label="Temu店铺ID">{{ shopInfo.shopTemuId }}</el-descriptions-item>
      <el-descriptions-item label="所属运营组">
        <div v-if="shopInfo.groups && shopInfo.groups.length > 0" class="group-tags">
          <el-tag 
            v-for="(group, index) in shopInfo.groups" 
            :key="group.groupId" 
            class="group-tag" 
            size="small" 
            :type="getTagTypeByIndex(index)"
            effect="plain"
            style="border-radius: 4px; padding: 0 8px;"
          >
            {{ group.groupName }}
          </el-tag>
        </div>
        <span v-else>-</span>
      </el-descriptions-item>
      <el-descriptions-item label="API密钥" class="api-item">
        <div class="api-content">
          <span v-if="!showApiKey">******</span>
          <span v-else class="api-value">{{ shopInfo.apiKey }}</span>
          <el-button link type="primary" size="small" @click="showApiKey = !showApiKey" class="toggle-btn">
            {{ showApiKey ? '隐藏' : '显示' }}
          </el-button>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="API密钥Secret" class="api-item">
        <div class="api-content">
          <span v-if="!showSecret">******</span>
          <span v-else class="api-value">{{ shopInfo.apiSecret }}</span>
          <el-button link type="primary" size="small" @click="showSecret = !showSecret" class="toggle-btn">
            {{ showSecret ? '隐藏' : '显示' }}
          </el-button>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="Access Token" class="api-item">
        <div class="api-content">
          <span v-if="!showToken">******</span>
          <span v-else class="api-value">{{ shopInfo.accessToken || '-' }}</span>
          <el-button link type="primary" size="small" @click="showToken = !showToken" class="toggle-btn">
            {{ showToken ? '隐藏' : '显示' }}
          </el-button>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag :type="shopInfo.status === '0' ? 'success' : 'danger'">
          {{ shopInfo.status === '0' ? '正常' : '禁用' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ shopInfo.createTime ? formatTime(shopInfo.createTime) : '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="更新时间">
        {{ shopInfo.updateTime ? formatTime(shopInfo.updateTime) : '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="店铺代号">
        {{ shopInfo.remark || '-' }}
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关 闭</el-button>
        <el-button type="primary" @click="handleEdit" v-hasPermi="['operation:shop:edit']">编辑</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { Shop } from '@/types/shop'
import { formatTime } from '@/utils/format'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  shopInfo: {
    type: Object as () => Shop,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'edit'])

// 对话框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 控制敏感信息的显示/隐藏
const showApiKey = ref(false)
const showSecret = ref(false)
const showToken = ref(false)

// 编辑店铺
const handleEdit = () => {
  emit('edit')
  visible.value = false
}

// 添加一个全部显示/隐藏的按钮，位于对话框顶部
const toggleAllSensitive = () => {
  if (isAnySensitiveVisible.value) {
    // 如果有任何敏感信息可见，则全部隐藏
    showApiKey.value = false
    showSecret.value = false
    showToken.value = false
  } else {
    // 否则全部显示
    showApiKey.value = true
    showSecret.value = true
    showToken.value = true
  }
}

// 检查是否有敏感信息可见
const isAnySensitiveVisible = computed(() => {
  return showApiKey.value || showSecret.value || showToken.value
})

// 根据索引获取标签类型
const getTagTypeByIndex = (index: number): string => {
  const types = ['primary', 'success', 'warning', 'danger', 'info', '']
  return types[index % types.length]
}

// 根据索引获取标签颜色
const getTagColorByIndex = (index: number): string => {
  const colors = [
    '#409eff', // 蓝色
    '#67c23a', // 绿色
    '#e6a23c', // 橙色
    '#f56c6c', // 红色
    '#909399', // 灰色
    '#9370db'  // 紫色
  ]
  return colors[index % colors.length]
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.toggle-btn {
  margin-left: 8px;
  padding: 2px 8px;
  border-radius: 4px;
  transition: all 0.3s;
  flex-shrink: 0;
}

.toggle-btn:hover {
  background-color: #f0f9ff;
}

.sensitive-control {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 12px;
}

.group-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.group-tag {
  margin-right: 0;
  margin-bottom: 0;
  font-weight: 500;
  transition: all 0.3s;
}

.group-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 添加新样式 */
:deep(.el-descriptions__label) {
  width: 120px;
  min-width: 120px;
  white-space: nowrap;
  padding: 12px 10px;
}

:deep(.el-descriptions__content) {
  padding: 10px;
  word-break: break-all;
}

.api-item {
  position: relative;
}

.api-content {
  display: flex;
  align-items: center;
  overflow: hidden;
}

.api-value {
  word-break: break-all;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style> 