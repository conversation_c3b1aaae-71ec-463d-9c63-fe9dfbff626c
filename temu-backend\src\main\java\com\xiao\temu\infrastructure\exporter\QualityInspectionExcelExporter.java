package com.xiao.temu.infrastructure.exporter;

import com.xiao.temu.modules.quality.dto.LocalQualityInspectionRequestDTO;
import com.xiao.temu.modules.quality.vo.LocalQualityInspectionVO;
import com.xiao.temu.modules.system.service.SysDataPermissionService;
import com.xiao.temu.modules.system.service.UserService;
import com.xiao.temu.modules.quality.service.LocalQualityInspectionService;
import com.xiao.temu.infrastructure.excel.ExcelExportService;
import com.xiao.temu.infrastructure.excel.ExcelExportUtils;
import com.xiao.temu.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 质量检验Excel导出实现类
 */
@Slf4j
@Service
public class QualityInspectionExcelExporter implements ExcelExportService.ExcelExporter {

    @Autowired
    private ExcelExportService excelExportService;
    
    @Autowired
    private LocalQualityInspectionService localQualityInspectionService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private SysDataPermissionService dataPermissionService;
    
    @Override
    public void export(Map<String, Object> exportParams, String taskId) {
        // 定义表头和列宽
        String[] headers = {"序号", "店铺代号", "店铺名称", "SPU ID", "SKU ID", "SKC ID", "商品缩略图", "商品名称", "商品类目", "备货单号", "规格", "货号", "质检结果", "质检时间", "疵点描述", "问题备注", "疵点图片1", "疵点图片2", "疵点图片3", "疵点图片4", "疵点图片5", "疵点图片6"};
        
        int[] columnWidths = {5, 20, 15, 15, 15, 15, 10, 40, 15, 15, 15, 15, 10, 20, 30, 30, 20, 20, 20, 20, 20, 20};
        
        // 获取文件名
        String fileName = (String) exportParams.get("fileName");
        if (fileName == null || fileName.trim().isEmpty()) {
            fileName = "抽检明细数据";
        }

        // 执行导出
        excelExportService.exportExcel(
            taskId,
            fileName,
            "抽检明细",
            headers,
            columnWidths,
            new QualityInspectionDataProcessor(exportParams)
        );
    }
    
    /**
     * 质量检验数据处理器
     */
    private class QualityInspectionDataProcessor implements ExcelExportService.ExcelDataProcessor {
        
        private final Map<String, Object> exportParams;
        
        public QualityInspectionDataProcessor(Map<String, Object> exportParams) {
            this.exportParams = exportParams;
        }
        
        @Override
        public void prepareData(ExcelExportService.ExcelExportContext context) {
            try {
                // 从导出参数中获取用户ID
                Object userIdObj = exportParams.get("userId");
                if (userIdObj == null) {
                    throw new RuntimeException("导出参数中缺少用户ID");
                }
                Long userId = Long.valueOf(userIdObj.toString());
                
                // 判断用户是否为管理员或拥有全部数据权限
                boolean isAdmin = userService.isAdmin(userId);
                
                // 获取用户的最高数据权限
                String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
                boolean hasFullDataPermission = "2".equals(permissionType);
                
                // 解析导出参数
                Map<String, Object> queryParams = (Map<String, Object>) exportParams.get("queryParams");
                String exportType = (String) exportParams.get("exportType");
                
                // 构建查询参数
                LocalQualityInspectionRequestDTO requestDTO = new LocalQualityInspectionRequestDTO();
                
                // 设置查询参数
                if (queryParams != null) {
                    if (queryParams.get("shopIds") != null) {
                        List<?> rawShopIds = (List<?>) queryParams.get("shopIds");
                        List<Long> shopIdList = new ArrayList<>();
                        for (Object id : rawShopIds) {
                            if (id instanceof Integer) {
                                shopIdList.add(((Integer) id).longValue());
                            } else if (id instanceof Long) {
                                shopIdList.add((Long) id);
                            } else if (id instanceof String) {
                                shopIdList.add(Long.valueOf((String) id));
                            } else {
                                shopIdList.add(Long.valueOf(id.toString()));
                            }
                        }
                        requestDTO.setShopIds(shopIdList);
                    }
                    
                    if (queryParams.get("qcResultUpdateTimeBegin") != null) {
                        requestDTO.setQcResultUpdateTimeBegin(DateUtils.parseLocalDateTime((String) queryParams.get("qcResultUpdateTimeBegin")));
                    }
                    
                    if (queryParams.get("qcResultUpdateTimeEnd") != null) {
                        requestDTO.setQcResultUpdateTimeEnd(DateUtils.parseLocalDateTime((String) queryParams.get("qcResultUpdateTimeEnd")));
                    }
                    
                    if (queryParams.get("skuIdList") != null) {
                        List<?> rawSkuIds = (List<?>) queryParams.get("skuIdList");
                        List<Long> skuIdList = new ArrayList<>();
                        for (Object id : rawSkuIds) {
                            if (id instanceof Integer) {
                                skuIdList.add(((Integer) id).longValue());
                            } else if (id instanceof Long) {
                                skuIdList.add((Long) id);
                            } else if (id instanceof String) {
                                skuIdList.add(Long.valueOf((String) id));
                            } else {
                                skuIdList.add(Long.valueOf(id.toString()));
                            }
                        }
                        requestDTO.setSkuIdList(skuIdList);
                    }
                    
                    if (queryParams.get("skcIdList") != null) {
                        List<?> rawSkcIds = (List<?>) queryParams.get("skcIdList");
                        List<Long> skcIdList = new ArrayList<>();
                        for (Object id : rawSkcIds) {
                            if (id instanceof Integer) {
                                skcIdList.add(((Integer) id).longValue());
                            } else if (id instanceof Long) {
                                skcIdList.add((Long) id);
                            } else if (id instanceof String) {
                                skcIdList.add(Long.valueOf((String) id));
                            } else {
                                skcIdList.add(Long.valueOf(id.toString()));
                            }
                        }
                        requestDTO.setSkcIdList(skcIdList);
                    }
                    
                    if (queryParams.get("purchaseNo") != null) {
                        requestDTO.setPurchaseNo((List<String>) queryParams.get("purchaseNo"));
                    }
                    
                    if (queryParams.get("qcResult") != null) {
                        requestDTO.setQcResult((String) queryParams.get("qcResult"));
                    }
                    
                    if (queryParams.get("skuNameKeyword") != null) {
                        requestDTO.setSkuNameKeyword((String) queryParams.get("skuNameKeyword"));
                    }
                    
                    // 设置分页参数
                    if (queryParams.get("pageNo") != null) {
                        requestDTO.setPageNo(Math.toIntExact(Long.valueOf(queryParams.get("pageNo").toString())));
                    } else {
                        requestDTO.setPageNo(1); // 默认第一页
                    }
                    
                    if (queryParams.get("pageSize") != null) {
                        requestDTO.setPageSize(Math.toIntExact(Long.valueOf(queryParams.get("pageSize").toString())));
                    } else {
                        requestDTO.setPageSize(Integer.MAX_VALUE); // 默认导出全部
                    }
                }
                
                // 如果是管理员或拥有全部数据权限，设置忽略权限检查标志
                if (isAdmin || hasFullDataPermission) {
                    requestDTO.setIgnorePermissionCheck(true);
                }
                
                // 处理不同导出类型
                List<LocalQualityInspectionVO.LocalQualityInspectionItemVO> allItems = new ArrayList<>();
                
                if ("page".equals(exportType)) {
                    // 导出当前页数据
                    LocalQualityInspectionVO result = localQualityInspectionService.getLocalQualityInspectionList(requestDTO, userId);
                    allItems.addAll(result.getItems());
                } else if ("custom".equals(exportType)) {
                    // 导出自定义数量数据 - 多页
                    Integer exportPageCount = 1;
                    if (queryParams.get("exportPageCount") != null) {
                        exportPageCount = Math.toIntExact(Long.valueOf(queryParams.get("exportPageCount").toString()));
                    }
                    
                    // 初始页码
                    int startPage = requestDTO.getPageNo();
                    // 设置每页条数
                    int pageSize = requestDTO.getPageSize();
                    
                    // 分页查询数据
                    for (int i = 0; i < exportPageCount; i++) {
                        requestDTO.setPageNo(startPage + i);
                        LocalQualityInspectionVO result = localQualityInspectionService.getLocalQualityInspectionList(requestDTO, userId);
                        if (result.getItems() != null && !result.getItems().isEmpty()) {
                            allItems.addAll(result.getItems());
                        }
                        
                        // 如果返回的数据条数少于pageSize，说明已经没有更多数据，跳出循环
                        if (result.getItems() == null || result.getItems().size() < pageSize) {
                            break;
                        }
                    }
                } else {
                    // 导出全部数据 - 单次大查询
                    LocalQualityInspectionVO result = localQualityInspectionService.getLocalQualityInspectionList(requestDTO, userId);
                    allItems.addAll(result.getItems());
                }
                
                // 设置导出上下文
                context.setData(allItems);
                context.setDataSize(allItems.size());
                
            } catch (Exception e) {
                log.error("准备导出数据失败", e);
                throw new RuntimeException("准备导出数据失败: " + e.getMessage(), e);
            }
        }
        
        @Override
        public void prepareImageTasks(ExcelExportService.ExcelExportContext context, File tempDir, List<ExcelExportUtils.ImageDownloadTask> downloadTasks) {
            @SuppressWarnings("unchecked")
            List<LocalQualityInspectionVO.LocalQualityInspectionItemVO> items = (List<LocalQualityInspectionVO.LocalQualityInspectionItemVO>) context.getData();
            
            // 计算总图片数量，用于初始化容量
            int totalEstimatedImages = 0;
            for (int i = 0; i < items.size(); i++) {
                LocalQualityInspectionVO.LocalQualityInspectionItemVO item = items.get(i);
                totalEstimatedImages++; // 缩略图
                List<String> attachments = item.getAttachments();
                if (attachments != null) {
                    totalEstimatedImages += Math.min(attachments.size(), 6); // 最多6张疵点图片
                }
            }
            
            // 每批处理的记录数
            final int BATCH_SIZE = 100; // 每次处理100条记录
            int totalRecords = items.size();
            int batchCount = (totalRecords + BATCH_SIZE - 1) / BATCH_SIZE; // 向上取整
            
            log.info("开始批量处理图片任务，总记录数: {}，总图片数: {}，批次数: {}", 
                    totalRecords, totalEstimatedImages, batchCount);
                    
            // 检查记录数量，如果太多可能导致内存问题，发出警告
            if (totalRecords > 1000) {
                log.warn("导出记录数量较大 ({}条)，可能需要较长时间处理", totalRecords);
            }
            
            // 添加大文件警告阈值和记录集合
            final int MAX_RECOMMENDED_IMAGES = 3000;
            final List<String> suspiciousImageUrls = new ArrayList<>();
            
            // 分批处理图片下载任务
            for (int batchIndex = 0; batchIndex < batchCount; batchIndex++) {
                int startIndex = batchIndex * BATCH_SIZE;
                int endIndex = Math.min(startIndex + BATCH_SIZE, totalRecords);
                
                log.info("处理第 {}/{} 批图片，记录范围: {}-{}", 
                        batchIndex + 1, batchCount, startIndex, endIndex - 1);
                
                List<ExcelExportUtils.ImageDownloadTask> batchTasks = new ArrayList<>();
                
                // 为当前批次创建下载任务
                for (int i = startIndex; i < endIndex; i++) {
                    LocalQualityInspectionVO.LocalQualityInspectionItemVO item = items.get(i);
                    final int rowIndex = i;
                    
                    // 处理商品缩略图
                    if (item.getThumbUrl() != null && !item.getThumbUrl().isEmpty()) {
                        // 检查可疑图片URL模式（可能是特别大的图片）
                        if (item.getThumbUrl().contains("original") || item.getThumbUrl().contains("high-resolution")) {
                            suspiciousImageUrls.add(item.getThumbUrl());
                            log.warn("检测到可能的高分辨率图片URL: {}", item.getThumbUrl());
                        }
                        
                        File thumbFile = new File(tempDir, "thumb_" + i + "_" + UUID.randomUUID() + ".jpg");
                        batchTasks.add(new ExcelExportUtils.ImageDownloadTask(item.getThumbUrl(), thumbFile, 6, rowIndex));
                    }
                    
                    // 处理疵点图片列表
                    List<String> attachments = item.getAttachments();
                    if (attachments != null && !attachments.isEmpty()) {
                        // 限制每条记录的最大图片数，防止某条记录有过多图片
                        int maxAttachments = Math.min(attachments.size(), 6); // 最多6张疵点图片
                        for (int j = 0; j < maxAttachments; j++) {
                            String imageUrl = attachments.get(j);
                            
                            // 跳过空URL
                            if (imageUrl == null || imageUrl.trim().isEmpty()) {
                                continue;
                            }
                            
                            // 检查可疑图片URL模式
                            if (imageUrl.contains("original") || imageUrl.contains("high-resolution")) {
                                suspiciousImageUrls.add(imageUrl);
                                log.warn("检测到可能的高分辨率图片URL: {}", imageUrl);
                            }
                            
                            File attachmentFile = new File(tempDir, "attachment_" + i + "_" + j + "_" + UUID.randomUUID() + ".jpg");
                            batchTasks.add(new ExcelExportUtils.ImageDownloadTask(imageUrl, attachmentFile, 16 + j, rowIndex));
                        }
                    }
                }
                
                // 将当前批次任务添加到总任务列表
                log.info("第 {} 批添加 {} 个图片任务", batchIndex + 1, batchTasks.size());
                downloadTasks.addAll(batchTasks);
                
                // 手动触发GC，释放内存
                if (batchIndex > 0 && batchIndex % 3 == 0) {
                    log.info("处理了 {} 批次后，主动触发GC释放内存", batchIndex);
                    System.gc();
                }
                
                // 监控总任务数，如果超过阈值，提前结束以避免OOM
                if (downloadTasks.size() > MAX_RECOMMENDED_IMAGES) {
                    log.warn("图片数量已超过推荐最大值 ({}张)，为确保稳定性将限制图片数量", MAX_RECOMMENDED_IMAGES);
                    break;
                }
            }
            
            // 添加可疑图片URL警告
            if (!suspiciousImageUrls.isEmpty()) {
                log.warn("发现 {} 个可能是高分辨率的图片URL，这些图片可能导致处理缓慢", suspiciousImageUrls.size());
            }
            
            log.info("图片任务准备完成，总任务数: {}", downloadTasks.size());
        }
        
        @Override
        public void fillExcelData(ExcelExportService.ExcelExportContext context, Sheet sheet, CellStyle contentStyle) {
            @SuppressWarnings("unchecked")
            List<LocalQualityInspectionVO.LocalQualityInspectionItemVO> items = (List<LocalQualityInspectionVO.LocalQualityInspectionItemVO>) context.getData();
            
            // 填充数据
            for (int i = 0; i < items.size(); i++) {
                LocalQualityInspectionVO.LocalQualityInspectionItemVO item = items.get(i);
                Row row = sheet.createRow(i + 1);
                row.setHeight((short) (80 * 20)); // 设置行高为80点，更好地适应图片
                
                // 设置单元格内容
                ExcelExportUtils.setCellValue(row, 0, String.valueOf(i + 1), contentStyle);
                ExcelExportUtils.setCellValue(row, 1, item.getShopRemark(), contentStyle);
                ExcelExportUtils.setCellValue(row, 2, item.getShopName(), contentStyle);
                ExcelExportUtils.setCellValue(row, 3, item.getSpuId() != null ? String.valueOf(item.getSpuId()) : "", contentStyle);
                ExcelExportUtils.setCellValue(row, 4, item.getProductSkuId() != null ? String.valueOf(item.getProductSkuId()) : "", contentStyle);
                ExcelExportUtils.setCellValue(row, 5, item.getProductSkcId() != null ? String.valueOf(item.getProductSkcId()) : "", contentStyle);
                // 商品缩略图列，留空，后面插入图片
                ExcelExportUtils.setCellValue(row, 6, "", contentStyle);
                ExcelExportUtils.setCellValue(row, 7, item.getSkuName(), contentStyle);
                ExcelExportUtils.setCellValue(row, 8, item.getCatName(), contentStyle);
                ExcelExportUtils.setCellValue(row, 9, item.getPurchaseNo(), contentStyle);
                ExcelExportUtils.setCellValue(row, 10, item.getSpec(), contentStyle);
                ExcelExportUtils.setCellValue(row, 11, item.getExtCode(), contentStyle);
                ExcelExportUtils.setCellValue(row, 12, "1".equals(item.getQcResult()) ? "合格" : "不合格", contentStyle);
                ExcelExportUtils.setCellValue(row, 13, item.getQcResultUpdateTime(), contentStyle);
                ExcelExportUtils.setCellValue(row, 14, item.getFlawNameDesc(), contentStyle);
                ExcelExportUtils.setCellValue(row, 15, item.getRemark(), contentStyle);
                // 疵点图片列，留空，后面插入图片
                ExcelExportUtils.setCellValue(row, 16, "", contentStyle);
                ExcelExportUtils.setCellValue(row, 17, "", contentStyle);
                ExcelExportUtils.setCellValue(row, 18, "", contentStyle);
                ExcelExportUtils.setCellValue(row, 19, "", contentStyle);
                ExcelExportUtils.setCellValue(row, 20, "", contentStyle);
                ExcelExportUtils.setCellValue(row, 21, "", contentStyle);
            }
        }
    }
} 