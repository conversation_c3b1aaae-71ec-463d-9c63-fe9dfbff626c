<template>
  <el-tag :type="tagType" size="small" effect="plain">{{ typeName }}</el-tag>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { NotificationType, NOTIFICATION_TYPE_MAP } from '@/types/notification'

const props = defineProps<{
  type: NotificationType
}>()

// 根据通知类型获取标签类型
const tagType = computed(() => {
  switch (props.type) {
    case NotificationType.JIT_SOON_EXPIRE:
      return 'warning'
    case NotificationType.JIT_EXPIRED:
      return 'danger'
    case NotificationType.NORMAL_NOT_DELIVERED:
      return 'primary'
    case NotificationType.NORMAL_NOT_RECEIVED:
      return 'success'
    default:
      return 'info'
  }
})

// 根据通知类型获取通知名称
const typeName = computed(() => {
  return NOTIFICATION_TYPE_MAP[props.type] || '未知通知类型'
})
</script> 