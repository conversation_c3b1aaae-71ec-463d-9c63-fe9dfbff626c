package com.xiao.temu.common.params;


public class CommonParams {

    /**
     * 请求类型，通常用来标识请求的具体类型或操作。
     */
    private String type;

    /**
     * 应用密钥，用于标识调用 API 的应用。
     * 应用密钥由 Temu API 提供，用于验证请求来源。
     */
    private String appKey;

    /**
     * 应用密钥密文，通常用于签名生成，确保请求的安全性。
     * 该值在调用 API 时通常是私密的，不应公开。
     */
    private String appSecret;

    /**
     * 访问令牌，用于授权访问 Temu API。
     * 访问令牌通常是在用户授权后获得，用于表明当前请求属于哪个用户。
     */
    private String accessToken;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
}
