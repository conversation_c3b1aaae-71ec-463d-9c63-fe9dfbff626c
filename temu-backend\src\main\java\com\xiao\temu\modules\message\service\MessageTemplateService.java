package com.xiao.temu.modules.message.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xiao.temu.modules.message.entity.SysMessageTemplate;

/**
 * 消息模板服务接口
 */
public interface MessageTemplateService extends IService<SysMessageTemplate> {

    /**
     * 根据模板编码获取模板
     *
     * @param templateCode 模板编码
     * @return 消息模板
     */
    SysMessageTemplate getByTemplateCode(String templateCode);
} 