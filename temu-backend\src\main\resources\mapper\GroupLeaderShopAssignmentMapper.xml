<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.operation.mapper.GroupLeaderShopAssignmentMapper">

    <!-- 定义结果映射 -->
    <resultMap id="AssignmentResult" type="com.xiao.temu.modules.operation.dto.GroupLeaderShopAssignmentDTO">
        <id property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="groupName" column="group_name"/>
        <result property="shopId" column="shop_id"/>
        <result property="shopName" column="shop_name"/>
        <result property="shopTemuId" column="shop_temu_id"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="permissionType" column="permission_type"/>
        <result property="permissionTypeName" column="permission_type_name"/>
        <result property="assignTime" column="assign_time"/>
        <result property="assignBy" column="assign_by"/>
        <result property="assignByName" column="assign_by_name"/>
        <result property="status" column="status"/>
    </resultMap>

    <!-- 通用查询列 -->
    <sql id="selectColumns">
        a.id, a.group_id, g.group_name, a.shop_id, s.shop_name, s.shop_temu_id,
        a.user_id, u.username as user_name, u.nick_name,
        a.permission_type, 
        (CASE a.permission_type WHEN '0' THEN '只读' WHEN '1' THEN '读写' ELSE '未知' END) AS permission_type_name,
        a.assign_time, a.assign_by, l.username as assign_by_name,
        a.status
    </sql>

    <!-- 查询条件 -->
    <sql id="whereClause">
        <where>
            <if test="query.groupId != null">
                AND a.group_id = #{query.groupId}
            </if>
            <if test="query.shopId != null">
                AND a.shop_id = #{query.shopId}
            </if>
            <if test="query.shopName != null and query.shopName != ''">
                AND s.shop_name LIKE CONCAT('%', #{query.shopName}, '%')
            </if>
            <if test="query.userId != null">
                AND a.user_id = #{query.userId}
            </if>
            <if test="query.userName != null and query.userName != ''">
                AND u.username LIKE CONCAT('%', #{query.userName}, '%')
            </if>
            <if test="query.permissionType != null and query.permissionType != ''">
                AND a.permission_type = #{query.permissionType}
            </if>
            <if test="query.status != null and query.status != ''">
                AND a.status = #{query.status}
            </if>
        </where>
    </sql>

    <!-- 分页查询分配列表 -->
    <select id="selectAssignmentList" resultMap="AssignmentResult">
        SELECT <include refid="selectColumns"/>
        FROM group_leader_shop_assignment a
        LEFT JOIN operation_group g ON a.group_id = g.group_id
        LEFT JOIN shop s ON a.shop_id = s.shop_id
        LEFT JOIN sys_user u ON a.user_id = u.user_id
        LEFT JOIN sys_user l ON a.assign_by = l.user_id
        <include refid="whereClause"/>
        ORDER BY a.assign_time DESC
    </select>

    <!-- 根据ID查询分配详情 -->
    <select id="selectAssignmentById" resultMap="AssignmentResult">
        SELECT <include refid="selectColumns"/>
        FROM group_leader_shop_assignment a
        LEFT JOIN operation_group g ON a.group_id = g.group_id
        LEFT JOIN shop s ON a.shop_id = s.shop_id
        LEFT JOIN sys_user u ON a.user_id = u.user_id
        LEFT JOIN sys_user l ON a.assign_by = l.user_id
        WHERE a.id = #{id}
    </select>

    <!-- 查询用户在指定店铺的分配信息 -->
    <select id="selectByUserAndShop" resultMap="AssignmentResult">
        SELECT <include refid="selectColumns"/>
        FROM group_leader_shop_assignment a
        LEFT JOIN operation_group g ON a.group_id = g.group_id
        LEFT JOIN shop s ON a.shop_id = s.shop_id
        LEFT JOIN sys_user u ON a.user_id = u.user_id
        LEFT JOIN sys_user l ON a.assign_by = l.user_id
        WHERE a.user_id = #{userId} AND a.shop_id = #{shopId} AND a.status = '0'
    </select>

    <!-- 查询用户的所有分配信息 -->
    <select id="selectByUserId" resultMap="AssignmentResult">
        SELECT <include refid="selectColumns"/>
        FROM group_leader_shop_assignment a
        LEFT JOIN operation_group g ON a.group_id = g.group_id
        LEFT JOIN shop s ON a.shop_id = s.shop_id
        LEFT JOIN sys_user u ON a.user_id = u.user_id
        LEFT JOIN sys_user l ON a.assign_by = l.user_id
        WHERE a.user_id = #{userId} AND a.status = '0'
    </select>

    <!-- 查询店铺的所有分配信息 -->
    <select id="selectByShopId" resultMap="AssignmentResult">
        SELECT <include refid="selectColumns"/>
        FROM group_leader_shop_assignment a
        LEFT JOIN operation_group g ON a.group_id = g.group_id
        LEFT JOIN shop s ON a.shop_id = s.shop_id
        LEFT JOIN sys_user u ON a.user_id = u.user_id
        LEFT JOIN sys_user l ON a.assign_by = l.user_id
        WHERE a.shop_id = #{shopId} AND a.status = '0'
    </select>

    <!-- 查询运营组的所有分配信息 -->
    <select id="selectByGroupId" resultMap="AssignmentResult">
        SELECT <include refid="selectColumns"/>
        FROM group_leader_shop_assignment a
        LEFT JOIN operation_group g ON a.group_id = g.group_id
        LEFT JOIN shop s ON a.shop_id = s.shop_id
        LEFT JOIN sys_user u ON a.user_id = u.user_id
        LEFT JOIN sys_user l ON a.assign_by = l.user_id
        WHERE a.group_id = #{groupId} AND a.status = '0'
    </select>

    <!-- 获取指定运营组的所有店铺分配数据（不分页） -->
    <select id="selectAllAssignmentsByGroupId" resultMap="AssignmentResult">
        SELECT <include refid="selectColumns"/>
        FROM group_leader_shop_assignment a
        LEFT JOIN operation_group g ON a.group_id = g.group_id
        LEFT JOIN shop s ON a.shop_id = s.shop_id
        LEFT JOIN sys_user u ON a.user_id = u.user_id
        LEFT JOIN sys_user l ON a.assign_by = l.user_id
        WHERE a.group_id = #{groupId}
        ORDER BY a.assign_time DESC
    </select>
</mapper> 