import request from '@/utils/request'

// 用于防止重复请求的变量
let routersPromise: Promise<any> | null = null
let routersPromiseTimeout: number | null = null

// 获取用户菜单路由 - 添加防抖机制
export function getRouters() {
  // 如果已经有一个请求在进行中，直接返回该请求的Promise
  if (routersPromise) {
    return routersPromise
  }
  
  // 创建新的请求
  routersPromise = request({
    url: '/system/menu/userMenuTree',
    method: 'get'
  }).finally(() => {
    // 请求完成后设置一个短暂的锁定期，防止高频重复请求
    if (routersPromiseTimeout) {
      clearTimeout(routersPromiseTimeout)
    }
    
    routersPromiseTimeout = window.setTimeout(() => {
      routersPromise = null
      routersPromiseTimeout = null
    }, 1000) // 1秒内不允许重复请求
  })
  
  return routersPromise
}

// 重置路由请求缓存
export function resetRoutersCache() {
  routersPromise = null
  if (routersPromiseTimeout) {
    clearTimeout(routersPromiseTimeout)
    routersPromiseTimeout = null
  }
}

// 获取菜单列表
export function getMenuList(query?: any) {
  return request({
    url: '/system/menu/list',
    method: 'get',
    params: query
  })
}

// 查询菜单详细
export function getMenu(menuId: number) {
  return request({
    url: `/system/menu/${menuId}`,
    method: 'get'
  })
}

// 新增菜单
export function addMenu(data: any) {
  // 清除路由缓存，确保下次获取是最新数据
  resetRoutersCache()
  return request({
    url: '/system/menu',
    method: 'post',
    data
  })
}

// 修改菜单
export function updateMenu(data: any) {
  // 清除路由缓存，确保下次获取是最新数据
  resetRoutersCache()
  return request({
    url: '/system/menu',
    method: 'put',
    data
  })
}

// 删除菜单
export function deleteMenu(menuId: number) {
  // 清除路由缓存，确保下次获取是最新数据
  resetRoutersCache()
  return request({
    url: `/system/menu/${menuId}`,
    method: 'delete'
  })
}

// 获取菜单树
export function getMenuTree() {
  return request({
    url: '/system/menu/tree',
    method: 'get'
  })
}

// 根据角色ID获取菜单树
export function getRoleMenuTree(roleId: number) {
  return request({
    url: `/system/menu/roleMenuTree/${roleId}`,
    method: 'get'
  })
} 