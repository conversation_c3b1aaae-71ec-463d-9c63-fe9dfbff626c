package com.xiao.temu.modules.refund.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.refund.dto.LocalRefundRequestDTO;
import com.xiao.temu.modules.refund.vo.LocalRefundPackageVO;
import com.xiao.temu.modules.refund.vo.LocalRefundPackageVO.LocalRefundPackageItemVO;
import com.xiao.temu.modules.refund.mapper.LocalRefundPackageMapper;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.system.service.UserService;
import com.xiao.temu.modules.refund.service.LocalRefundPackageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 本地退货明细服务实现类
 */
@Slf4j
@Service
public class LocalRefundPackageServiceImpl implements LocalRefundPackageService {

    @Autowired
    private LocalRefundPackageMapper localRefundPackageMapper;
    
    @Autowired
    private ShopService shopService;
    
    @Autowired
    private UserService userService;

    @Override
    public LocalRefundPackageVO getLocalRefundPackageList(LocalRefundRequestDTO requestDTO, Long userId) {
        log.info("查询本地退货包裹明细, 请求参数: {}, 用户ID: {}", requestDTO, userId);
        
        LocalRefundPackageVO response = new LocalRefundPackageVO();
        
        try {
            // 验证店铺权限
            List<Long> shopIds = requestDTO.getShopIds();
            if (shopIds == null || shopIds.isEmpty()) {
                throw new RuntimeException("店铺ID不能为空");
            }
            
            // 检查用户是否是管理员，如果是管理员则跳过权限检查
            boolean isAdmin = userService.isAdmin(userId);
            // 检查是否忽略权限检查
            boolean ignorePermissionCheck = Boolean.TRUE.equals(requestDTO.getIgnorePermissionCheck());
            
            // 获取店铺信息
            Map<Long, String> shopNameMap = new HashMap<>();
            Map<Long, String> shopRemarkMap = new HashMap<>();
            
            // 如果不是管理员且不忽略权限检查，则需要验证每个店铺的权限
            List<Shop> validShops = new ArrayList<>();
            if (!isAdmin && !ignorePermissionCheck) {
                // 验证每个店铺的权限
                for (Long shopId : shopIds) {
                    // 检查用户是否有权限访问此店铺
                    boolean hasPermission = shopService.checkShopPermission(userId, shopId, false);
                    if (!hasPermission) {
                        log.warn("用户 {} 没有权限访问店铺 {}", userId, shopId);
                        continue; // 跳过无权限的店铺
                    }
                    
                    // 查询店铺信息
                    Shop shop = shopService.getShopById(shopId).convertToShop();
                    if (shop != null) {
                        validShops.add(shop);
                        shopNameMap.put(shopId, shop.getShopName());
                        shopRemarkMap.put(shopId, shop.getRemark());
                    } else {
                        log.warn("店铺不存在: {}", shopId);
                    }
                }
                
                // 如果没有有效的店铺，则返回空结果
                if (validShops.isEmpty()) {
                    throw new RuntimeException("没有权限访问所选店铺或店铺不存在");
                }
                
                // 更新请求中的shopIds为有效的店铺ID列表
                shopIds = validShops.stream().map(Shop::getShopId).collect(Collectors.toList());
                requestDTO.setShopIds(shopIds);
            } else {
                // 管理员或忽略权限检查，直接获取所有店铺信息
                for (Long shopId : shopIds) {
                    Shop shop = shopService.getShopById(shopId).convertToShop();
                    if (shop != null) {
                        validShops.add(shop);
                        shopNameMap.put(shopId, shop.getShopName());
                        shopRemarkMap.put(shopId, shop.getRemark());
                    }
                }
            }
            
            // 设置分页参数
            Integer pageNo = requestDTO.getPageNo() != null ? requestDTO.getPageNo() : 1;
            Integer pageSize = requestDTO.getPageSize() != null ? requestDTO.getPageSize() : 10;
            Page<LocalRefundPackageItemVO> page = new Page<>(pageNo, pageSize);
            
            // 处理日期参数
            LocalDateTime startDateTime = requestDTO.getOutboundTimeStart();
            LocalDateTime endDateTime = requestDTO.getOutboundTimeEnd();
            
            // 记录日志
            if (startDateTime != null) {
                log.info("出库开始时间: {}", startDateTime);
            }
            
            if (endDateTime != null) {
                log.info("出库结束时间: {}", endDateTime);
            }
            
            // 查询数据
            IPage<LocalRefundPackageItemVO> resultPage = localRefundPackageMapper.selectRefundPackagePage(
                    page,
                    shopIds,
                    startDateTime,
                    endDateTime,
                    requestDTO.getProductSkuIdList(),
                    requestDTO.getReturnSupplierPackageNos(),
                    requestDTO.getPurchaseSubOrderSns()
            );
            
            // 设置店铺名称和备注
            if (resultPage.getRecords() != null && !resultPage.getRecords().isEmpty()) {
                for (LocalRefundPackageItemVO item : resultPage.getRecords()) {
                    if (item.getShopId() != null && shopNameMap.containsKey(item.getShopId())) {
                        item.setShopName(shopNameMap.get(item.getShopId()));
                        item.setShopRemark(shopRemarkMap.get(item.getShopId()));
                    }
                }
            }
            
            // 构建返回结果
            response.setTotal(resultPage.getTotal());
            response.setPageNo(pageNo);
            response.setPageSize(pageSize);
            response.setItems(resultPage.getRecords());
            
            // 多店铺查询时，不设置shopId、shopName和shopRemark
            if (shopIds.size() == 1) {
                Long shopId = shopIds.get(0);
                response.setShopId(shopId);
                response.setShopName(shopNameMap.get(shopId));
                response.setShopRemark(shopRemarkMap.get(shopId));
            }
            
            log.info("查询本地退货包裹明细成功, 共查询到 {} 条记录", response.getTotal());
            
        } catch (Exception e) {
            log.error("查询本地退货包裹明细失败", e);
            throw new RuntimeException("查询退货包裹明细失败: " + e.getMessage());
        }
        
        return response;
    }
} 