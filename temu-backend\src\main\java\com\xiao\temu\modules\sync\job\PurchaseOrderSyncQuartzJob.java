package com.xiao.temu.modules.sync.job;

import com.xiao.temu.modules.sync.service.PurchaseOrderSyncService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

/**
 * 备货单数据同步定时任务
 */
@Slf4j
@Component
public class PurchaseOrderSyncQuartzJob extends QuartzJobBean {

    @Autowired
    private PurchaseOrderSyncService purchaseOrderSyncService;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        String triggerName = context.getTrigger().getKey().getName();
        log.info("备货单数据同步任务开始执行, 触发器: {}", triggerName);
        
        try {
            String result = purchaseOrderSyncService.executeScheduledSync();
            log.info("备货单数据同步任务执行成功, 触发器: {}, 结果: {}", triggerName, result);
        } catch (Exception e) {
            log.error("备货单数据同步任务执行失败, 触发器: {}, 错误: {}", triggerName, e.getMessage(), e);
            throw new JobExecutionException(e);
        }
    }
} 