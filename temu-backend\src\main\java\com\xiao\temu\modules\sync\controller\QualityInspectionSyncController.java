package com.xiao.temu.modules.sync.controller;

import com.xiao.temu.modules.sync.entity.QualityInspectionSyncTask;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.sync.dto.QualityInspectionSyncDTO;
import com.xiao.temu.modules.sync.vo.ApiResponse;
import com.xiao.temu.modules.sync.service.QualityInspectionSyncService;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 质检数据同步控制器
 */
@RestController
@RequestMapping("/temu/qualityInspection/sync")
public class QualityInspectionSyncController {

    @Autowired
    private QualityInspectionSyncService qualityInspectionSyncService;

    @Autowired
    private ShopService shopService;

    @Autowired
    private UserService userService;

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        // 通过用户名查询用户信息获取用户ID
        SysUser user = userService.getUserByUsername(username);
        if (user != null) {
            return user.getUserId();
        }
        throw new RuntimeException("用户未找到: " + username);
    }

    /**
     * 获取同步任务列表
     *
     * @param syncDTO 同步请求参数
     * @return API响应
     */
    @PostMapping("/tasks")
    public ApiResponse getSyncTasks(@RequestBody(required = false) QualityInspectionSyncDTO syncDTO) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        List<Long> shopIds;
        if (isAdmin(userId)) {
            // 管理员可以获取指定店铺或所有店铺的任务
            if (syncDTO != null && syncDTO.getShopIds() != null && !syncDTO.getShopIds().isEmpty()) {
                shopIds = new ArrayList<>(syncDTO.getShopIds());
            } else {
                // 获取所有店铺ID
                shopIds = shopService.listAllShops().stream()
                        .map(shop -> shop.getShopId())
                        .collect(Collectors.toList());
            }
        } else {
            // 非管理员用户
            if (syncDTO != null && syncDTO.getShopIds() != null && !syncDTO.getShopIds().isEmpty()) {
                // 过滤出用户有权限的店铺
                shopIds = syncDTO.getShopIds().stream()
                        .filter(shopId -> shopService.checkShopPermission(userId, shopId, false))
                        .collect(Collectors.toList());
            } else {
                // 获取用户有权限的所有店铺
                shopIds = shopService.listUserShops(userId).stream()
                        .map(shop -> shop.getShopId())
                        .collect(Collectors.toList());
            }
        }
        
        List<QualityInspectionSyncTask> tasks = qualityInspectionSyncService.getSyncTasks(shopIds);
        
        // 获取所有相关店铺信息，并创建映射
        Map<Long, String> shopNameMap = new HashMap<>();
        Map<Long, String> shopRemarkMap = new HashMap<>();
        List<Shop> shops = shopService.listAllShops();
        for (Shop shop : shops) {
            shopNameMap.put(shop.getShopId(), shop.getShopName());
            shopRemarkMap.put(shop.getShopId(), shop.getRemark());
        }
        
        // 为每个任务设置店铺名称和备注
        for (QualityInspectionSyncTask task : tasks) {
            task.setShopName(shopNameMap.getOrDefault(task.getShopId(), "未知店铺"));
            task.setShopRemark(shopRemarkMap.getOrDefault(task.getShopId(), ""));
            // 更新质检记录总数
            Integer inspectionCount = qualityInspectionSyncService.getQualityInspectionCountByShopId(task.getShopId());
            if (inspectionCount != null) {
                task.setTotalRecords(inspectionCount);
            }
        }
        
        // 处理排序
        if (syncDTO != null && syncDTO.getSortField() != null && !syncDTO.getSortField().isEmpty()) {
            String sortField = syncDTO.getSortField();
            boolean isAsc = "asc".equalsIgnoreCase(syncDTO.getSortOrder());
            
            // 根据排序字段对任务列表进行排序
            if ("shopId".equals(sortField)) {
                if (isAsc) {
                    tasks.sort(Comparator.comparing(QualityInspectionSyncTask::getShopId));
                } else {
                    tasks.sort(Comparator.comparing(QualityInspectionSyncTask::getShopId).reversed());
                }
            }
            // 可以根据需要添加更多字段的排序支持
        }
        
        // 处理分页
        Integer pageNum = (syncDTO != null && syncDTO.getPageNum() != null) ? syncDTO.getPageNum() : 1;
        Integer pageSize = (syncDTO != null && syncDTO.getPageSize() != null) ? syncDTO.getPageSize() : 10;
        
        // 计算分页数据
        int total = tasks.size();
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);
        
        // 防止索引越界
        if (startIndex >= total) {
            startIndex = 0;
            endIndex = 0;
        }
        
        // 获取当前页数据
        List<QualityInspectionSyncTask> pageData = startIndex < endIndex ? 
            tasks.subList(startIndex, endIndex) : 
            List.of();
        
        // 构建分页结果
        Map<String, Object> result = new HashMap<>();
        result.put("total", total);
        result.put("list", pageData);
        result.put("pageNum", pageNum);
        result.put("pageSize", pageSize);
        
        return ApiResponse.success(result);
    }

    /**
     * 手动触发同步
     *
     * @param shopId 店铺ID
     * @return API响应
     */
    @PostMapping("/trigger/{shopId}")
    public ApiResponse triggerSync(@PathVariable Long shopId) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        // 如果是管理员，直接允许操作；否则检查权限
        if (!isAdmin(userId) && !shopService.checkShopPermission(userId, shopId, false)) {
            return ApiResponse.error(403, "您没有访问该店铺的权限");
        }
        
        // 检查当前同步状态
        QualityInspectionSyncTask currentTask = qualityInspectionSyncService.getSyncTaskByShopId(shopId);
        if (currentTask != null && currentTask.getSyncStatus() == 1) {
            // 已经在同步中，直接返回成功状态
            ApiResponse.QualityInspectionSyncVO result = new ApiResponse.QualityInspectionSyncVO();
            result.setSuccess(true);
            result.setSyncStatus(1);
            result.setMessage("同步任务正在进行中");
            return ApiResponse.success(result);
        }
        
        // 异步触发同步
        CompletableFuture.runAsync(() -> {
            try {
                qualityInspectionSyncService.syncQualityInspectionData(shopId);
            } catch (Exception e) {
                // 记录异常日志，但不影响响应
                e.printStackTrace();
            }
        });
        
        // 立即返回已启动的状态
        ApiResponse.QualityInspectionSyncVO result = new ApiResponse.QualityInspectionSyncVO();
        result.setSuccess(true);
        result.setSyncStatus(1);
        result.setMessage("同步任务已启动");
        return ApiResponse.success(result);
    }

    /**
     * 检查用户是否为管理员
     */
    private boolean isAdmin(Long userId) {
        return userService.isAdmin(userId);
    }

    /**
     * 初始化同步任务
     *
     * @param shopId 店铺ID
     * @return API响应
     */
    @PostMapping("/init/{shopId}")
    public ApiResponse initSyncTask(@PathVariable Long shopId) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        // 如果是管理员，直接允许操作；否则检查权限
        if (!isAdmin(userId) && !shopService.checkShopPermission(userId, shopId, true)) {
            return ApiResponse.error(403, "您没有操作该店铺的权限");
        }
        
        // 初始化同步任务
        boolean result = qualityInspectionSyncService.initSyncTask(shopId);
        
        if (result) {
            return ApiResponse.success("初始化同步任务成功");
        } else {
            return ApiResponse.error(500, "初始化同步任务失败");
        }
    }

    /**
     * 获取同步任务详情
     *
     * @param shopId 店铺ID
     * @return API响应
     */
    @GetMapping("/task/{shopId}")
    public ApiResponse getSyncTask(@PathVariable Long shopId) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        // 如果是管理员，直接允许操作；否则检查权限
        if (!isAdmin(userId) && !shopService.checkShopPermission(userId, shopId, false)) {
            return ApiResponse.error(403, "您没有访问该店铺的权限");
        }
        
        // 获取同步任务
        QualityInspectionSyncTask task = qualityInspectionSyncService.getSyncTaskByShopId(shopId);
        
        if (task != null) {
            // 设置店铺名称和备注
            Shop shop = shopService.getShopById(shopId).convertToShop();
            if (shop != null) {
                task.setShopName(shop.getShopName());
                task.setShopRemark(shop.getRemark());
            } else {
                task.setShopName("未知店铺");
                task.setShopRemark("");
            }
            
            // 更新质检记录总数
            Integer inspectionCount = qualityInspectionSyncService.getQualityInspectionCountByShopId(shopId);
            if (inspectionCount != null) {
                task.setTotalRecords(inspectionCount);
            }
            
            return ApiResponse.success(task);
        } else {
            return ApiResponse.error(404, "同步任务不存在");
        }
    }

    /**
     * 批量初始化同步任务
     *
     * @param syncDTO 包含店铺ID列表的数据传输对象
     * @return API响应
     */
    @PostMapping("/batch/init")
    public ApiResponse batchInitSyncTasks(@RequestBody QualityInspectionSyncDTO syncDTO) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        if (syncDTO == null || syncDTO.getShopIds() == null || syncDTO.getShopIds().isEmpty()) {
            return ApiResponse.error(400, "请选择要初始化的店铺");
        }
        
        // 过滤出用户有权限的店铺
        List<Long> shopIds;
        if (isAdmin(userId)) {
            // 管理员可以访问所有店铺
            shopIds = new ArrayList<>(syncDTO.getShopIds());
        } else {
            // 非管理员需要过滤权限
            shopIds = syncDTO.getShopIds().stream()
                    .filter(shopId -> shopService.checkShopPermission(userId, shopId, true))
                    .collect(Collectors.toList());
        }
        
        if (shopIds.isEmpty()) {
            return ApiResponse.error(403, "您没有操作所选店铺的权限");
        }
        
        // 批量初始化结果
        List<Map<String, Object>> results = new ArrayList<>();
        int successCount = 0;
        
        for (Long shopId : shopIds) {
            Map<String, Object> result = new HashMap<>();
            result.put("shopId", shopId);
            
            try {
                boolean success = qualityInspectionSyncService.initSyncTask(shopId);
                result.put("success", success);
                if (success) {
                    successCount++;
                    result.put("message", "初始化成功");
                } else {
                    result.put("message", "初始化失败");
                }
            } catch (Exception e) {
                result.put("success", false);
                result.put("message", "初始化异常: " + e.getMessage());
            }
            
            results.add(result);
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("total", shopIds.size());
        response.put("success", successCount);
        response.put("results", results);
        
        return ApiResponse.success(response);
    }
    
    /**
     * 批量触发同步
     *
     * @param syncDTO 包含店铺ID列表的数据传输对象
     * @return API响应
     */
    @PostMapping("/batch/trigger")
    public ApiResponse batchTriggerSync(@RequestBody QualityInspectionSyncDTO syncDTO) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        if (syncDTO == null || syncDTO.getShopIds() == null || syncDTO.getShopIds().isEmpty()) {
            return ApiResponse.error(400, "请选择要同步的店铺");
        }
        
        // 过滤出用户有权限的店铺
        List<Long> shopIds;
        if (isAdmin(userId)) {
            // 管理员可以访问所有店铺
            shopIds = new ArrayList<>(syncDTO.getShopIds());
        } else {
            // 非管理员需要过滤权限
            shopIds = syncDTO.getShopIds().stream()
                    .filter(shopId -> shopService.checkShopPermission(userId, shopId, false))
                    .collect(Collectors.toList());
        }
        
        if (shopIds.isEmpty()) {
            return ApiResponse.error(403, "您没有访问所选店铺的权限");
        }
        
        // 批量同步结果
        List<Map<String, Object>> results = new ArrayList<>();
        int successCount = 0;
        
        for (Long shopId : shopIds) {
            Map<String, Object> result = new HashMap<>();
            result.put("shopId", shopId);
            
            try {
                ApiResponse.QualityInspectionSyncVO syncResult = qualityInspectionSyncService.syncQualityInspectionData(shopId);
                result.put("success", syncResult.getSuccess());
                result.put("syncStatus", syncResult.getSyncStatus());
                
                if (syncResult.getSuccess()) {
                    successCount++;
                    result.put("message", "同步成功触发");
                } else {
                    result.put("message", syncResult.getErrorMsg());
                }
            } catch (Exception e) {
                result.put("success", false);
                result.put("message", "同步异常: " + e.getMessage());
            }
            
            results.add(result);
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("total", shopIds.size());
        response.put("success", successCount);
        response.put("results", results);
        
        return ApiResponse.success(response);
    }

    /**
     * 清空同步数据
     *
     * @param shopId 店铺ID
     * @return API响应
     */
    @PostMapping("/clear/{shopId}")
    public ApiResponse clearSyncData(@PathVariable Long shopId) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        // 如果是管理员，直接允许操作；否则检查权限
        if (!isAdmin(userId) && !shopService.checkShopPermission(userId, shopId, true)) {
            return ApiResponse.error(403, "您没有操作该店铺的权限");
        }
        
        // 清空同步数据
        boolean result = qualityInspectionSyncService.clearSyncData(shopId);
        
        if (result) {
            return ApiResponse.success("清空同步数据成功");
        } else {
            return ApiResponse.error(500, "清空同步数据失败");
        }
    }
    
    /**
     * 批量清空同步数据
     *
     * @param syncDTO 包含店铺ID列表的数据传输对象
     * @return API响应
     */
    @PostMapping("/batch/clear")
    public ApiResponse batchClearSyncData(@RequestBody QualityInspectionSyncDTO syncDTO) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        if (syncDTO == null || syncDTO.getShopIds() == null || syncDTO.getShopIds().isEmpty()) {
            return ApiResponse.error(400, "请选择要清空数据的店铺");
        }
        
        // 过滤出用户有权限的店铺
        List<Long> shopIds;
        if (isAdmin(userId)) {
            // 管理员可以访问所有店铺
            shopIds = new ArrayList<>(syncDTO.getShopIds());
        } else {
            // 非管理员需要过滤权限
            shopIds = syncDTO.getShopIds().stream()
                    .filter(shopId -> shopService.checkShopPermission(userId, shopId, true))
                    .collect(Collectors.toList());
        }
        
        if (shopIds.isEmpty()) {
            return ApiResponse.error(403, "您没有操作所选店铺的权限");
        }
        
        // 批量清空结果
        List<Map<String, Object>> results = new ArrayList<>();
        int successCount = 0;
        
        for (Long shopId : shopIds) {
            Map<String, Object> result = new HashMap<>();
            result.put("shopId", shopId);
            
            try {
                boolean success = qualityInspectionSyncService.clearSyncData(shopId);
                result.put("success", success);
                if (success) {
                    successCount++;
                    result.put("message", "清空成功");
                } else {
                    result.put("message", "清空失败");
                }
            } catch (Exception e) {
                result.put("success", false);
                result.put("message", "清空异常: " + e.getMessage());
            }
            
            results.add(result);
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("total", shopIds.size());
        response.put("success", successCount);
        response.put("results", results);
        
        return ApiResponse.success(response);
    }
} 