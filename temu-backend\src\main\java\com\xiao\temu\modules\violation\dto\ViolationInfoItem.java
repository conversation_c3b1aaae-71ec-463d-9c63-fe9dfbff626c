package com.xiao.temu.modules.violation.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * 违规信息项
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ViolationInfoItem {
    /**
     * 违规编号
     */
    private String punishSn;
    
    /**
     * 违规类型描述
     */
    private String punishTypeDesc;
    
    /**
     * 违规类型代码
     */
    private Integer punishTypeCode;
    
    /**
     * 违规发起时间戳
     */
    private Long violationStartTime;
    
    /**
     * 倒计时时间戳
     */
    private Long countdownTime;
    
    /**
     * 违规金额
     */
    private String punishAmount;
    
    /**
     * 违规金额币种
     */
    private String punishAmountCurrency;
    
    /**
     * 违规状态
     */
    private Integer punishStatus;
    
    /**
     * 详情查看状态
     */
    private Integer viewDetailsStatus;
    
    /**
     * 备货单号
     */
    private String subPurchaseOrderSn;
    
    /**
     * 处罚金额差异
     */
    private String punishAmountDiff;
    
    /**
     * 处罚第三类型代码
     */
    private Integer punishThirdTypeCode;
    
    /**
     * 处罚第三类型描述
     */
    private String punishThirdTypeDesc;
    
    /**
     * 凭证
     */
    private String voucher;
    
    /**
     * 质检缺陷图片URL列表
     */
    private String qcFlawPictureUrlList;
    
    /**
     * 显示凭证
     */
    private String showVoucher;
    
    /**
     * 违规二级类型描述
     */
    private String punishSecondTypeDesc;
    
    /**
     * 违规一级类型描述
     */
    private String punishFirstTypeDesc;
    
    /**
     * 违规一级类型代码
     */
    private Integer punishFirstTypeCode;
    
    /**
     * 质量事件处罚维度
     */
    private String qualityEventPunishDimension;
    
    /**
     * 质量事件处罚维度描述
     */
    private String qualityEventPunishDimensionDesc;
    
    /**
     * 产品SKCID
     */
    private String productSkcId;
    
    /**
     * 店铺Temu平台ID
     */
    private String mallId;
    
    /**
     * 店铺名称
     */
    private String mallName;
    
    /**
     * 详细信息
     */
    private ViolationDetail detail;
} 