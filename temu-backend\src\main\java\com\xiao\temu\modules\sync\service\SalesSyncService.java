package com.xiao.temu.modules.sync.service;

import com.xiao.temu.modules.sales.entity.SalesSubOrder;
import com.xiao.temu.modules.sync.entity.SalesSyncTask;
import com.xiao.temu.modules.sync.vo.ApiResponse;

import java.util.List;
import java.util.Set;

/**
 * 销售数据同步服务接口
 */
public interface SalesSyncService {

    /**
     * 同步指定店铺的销售数据
     *
     * @param shopId 店铺ID
     * @return 同步结果
     */
    ApiResponse.SalesSyncVO syncSalesData(Long shopId);
    
    /**
     * 同步指定SKC ID列表的销售数据
     *
     * @param shopId 店铺ID
     * @param skcIds 需要同步的商品SKC ID集合
     * @return 同步的销售子订单列表
     */
    List<SalesSubOrder> syncSalesBySkcIds(Long shopId, Set<Long> skcIds);

    /**
     * 初始化店铺的同步任务
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    boolean initSyncTask(Long shopId);

    /**
     * 获取同步任务列表
     *
     * @param shopIds 店铺ID列表，为空则查询所有
     * @return 同步任务列表
     */
    List<SalesSyncTask> getSyncTasks(List<Long> shopIds);

    /**
     * 根据店铺ID获取同步任务
     *
     * @param shopId 店铺ID
     * @return 同步任务
     */
    SalesSyncTask getSyncTaskByShopId(Long shopId);

    /**
     * 执行定时同步
     * 
     * @return 同步结果
     */
    String executeScheduledSync();
    
    /**
     * 获取指定店铺的销售子订单总数
     *
     * @param shopId 店铺ID
     * @return 销售子订单总数
     */
    Integer getSalesSubOrderCountByShopId(Long shopId);
    
    /**
     * 获取指定店铺的销售SKU总数
     *
     * @param shopId 店铺ID
     * @return 销售SKU总数
     */
    Integer getSalesSkuCountByShopId(Long shopId);
    
    /**
     * 获取指定店铺的仓库信息总数
     *
     * @param shopId 店铺ID
     * @return 仓库信息总数
     */
    Integer getWarehouseInfoCountByShopId(Long shopId);
    
    /**
     * 清空店铺的销售同步数据
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    boolean clearSyncData(Long shopId);

    /**
     * 执行销售数据全量同步
     *
     * @param shopId 店铺ID
     * @param limit 最大同步数量限制
     * @return 是否同步成功
     */
    boolean syncSalesDataFull(Long shopId, int limit);
    
    /**
     * 执行销售数据增量同步
     *
     * @param shopId 店铺ID
     * @return 是否同步成功
     */
    boolean syncSalesDataIncremental(Long shopId);

    /**
     * 使用多线程执行销售数据全量同步
     *
     * @param shopId 店铺ID
     * @param limit 最大同步数量限制
     * @param threadCount 线程数量,默认为配置文件中的设置
     * @return 同步结果
     */
    ApiResponse.SalesSyncVO syncSalesDataFullAsync(Long shopId, int limit, Integer threadCount);
    
    /**
     * 使用多线程执行销售数据增量同步
     *
     * @param shopId 店铺ID
     * @param threadCount 线程数量,默认为配置文件中的设置
     * @return 同步结果
     */
    ApiResponse.SalesSyncVO syncSalesDataIncrementalAsync(Long shopId, Integer threadCount);
    
    /**
     * 批量多线程同步多个店铺的销售数据
     *
     * @param shopIds 店铺ID列表
     * @param isFullSync 是否全量同步,true为全量,false为增量
     * @param limit 全量同步时的最大同步数量限制,增量同步时忽略
     * @param threadCount 线程数量,默认为配置文件中的设置
     * @return 同步结果
     */
    String batchSyncSalesDataAsync(List<Long> shopIds, boolean isFullSync, int limit, Integer threadCount);
    
    /**
     * 根据ID更新同步任务对象
     *
     * @param entity 同步任务对象
     * @return 是否更新成功
     */
    boolean updateById(SalesSyncTask entity);
} 