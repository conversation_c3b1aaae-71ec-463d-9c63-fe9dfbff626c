package com.xiao.temu.modules.purchaseorderv.controller;

import com.xiao.temu.modules.purchaseorderv.dto.ProductLabelRequestDTO;
import com.xiao.temu.modules.purchaseorderv.service.ProductLabelService;
import com.xiao.temu.modules.purchaseorderv.vo.ProductLabelVO;
import com.xiao.temu.modules.sync.vo.ApiResponse;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

/**
 * 商品标签控制器
 */
@Slf4j
@RestController
@RequestMapping("/temu/productLabel")
public class ProductLabelController {

    @Autowired
    private ProductLabelService productLabelService;
    
    @Autowired
    private UserService userService;

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        // 通过用户名查询用户信息获取用户ID
        SysUser user = userService.getUserByUsername(username);
        if (user != null) {
            return user.getUserId();
        }
        throw new RuntimeException("用户未找到: " + username);
    }

    /**
     * 批量获取商品标签数据
     *
     * @param requestDTO 请求参数
     * @return API响应
     */
    @PostMapping("/batchGet")
    public ApiResponse batchGetProductLabels(@RequestBody ProductLabelRequestDTO requestDTO) {
        try {
            // 获取当前用户ID
            Long userId = getCurrentUserId();
            
            // 调用服务获取结果
            ProductLabelVO response = productLabelService.getProductLabels(requestDTO, userId);
            if (response.getSuccess()) {
                return ApiResponse.success(response);
            } else {
                return ApiResponse.error(response.getErrorCode(), response.getErrorMsg(), response);
            }
        } catch (Exception e) {
            log.error("批量获取商品标签数据失败", e);
            return ApiResponse.error("批量获取商品标签数据失败: " + e.getMessage());
        }
    }
} 