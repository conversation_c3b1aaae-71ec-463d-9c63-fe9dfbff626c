<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.sync.mapper.QualityInspectionSyncTaskMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.xiao.temu.modules.sync.entity.QualityInspectionSyncTask">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="last_sync_time" property="lastSyncTime" />
        <result column="last_update_time" property="lastUpdateTime" />
        <result column="sync_status" property="syncStatus" />
        <result column="error_message" property="errorMessage" />
        <result column="total_records" property="totalRecords" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 查询需要同步的任务列表 -->
    <select id="getTasksToSync" resultMap="BaseResultMap">
        SELECT * FROM quality_inspection_sync_task
        WHERE sync_status IN (0, 3) OR  
            (sync_status = 2 AND 
             (last_sync_time &lt; DATE_SUB(NOW(), INTERVAL 30 MINUTE) OR 
              YEAR(last_sync_time) > YEAR(NOW())))
        ORDER BY last_sync_time
        LIMIT 10
    </select>

    <!-- 根据店铺ID查询同步任务 -->
    <select id="getByShopId" resultMap="BaseResultMap">
        SELECT * FROM quality_inspection_sync_task
        WHERE shop_id = #{shopId}
        LIMIT 1
    </select>

    <!-- 初始化店铺的同步任务 -->
    <insert id="initSyncTask">
        INSERT INTO quality_inspection_sync_task (
            shop_id, sync_status, total_records, create_time, update_time
        ) VALUES (
            #{shopId}, 0, 0, NOW(), NOW()
        )
        ON DUPLICATE KEY UPDATE
            sync_status = 0,
            error_message = NULL,
            update_time = NOW()
    </insert>
    
    <!-- 清空时间字段 -->
    <update id="clearTimeFields" parameterType="java.util.Map">
        UPDATE quality_inspection_sync_task
        SET last_sync_time = NULL,
            last_update_time = NULL,
            sync_status = 0,
            error_message = NULL,
            total_records = 0,
            update_time = NOW()
        WHERE shop_id = #{shopId}
    </update>

</mapper> 