{"root": ["./src/env.d.ts", "./src/main.ts", "./src/permission.ts", "./src/vite-env.d.ts", "./src/api/datapermission.ts", "./src/api/group.ts", "./src/api/leaderassign.ts", "./src/api/menu.ts", "./src/api/message.ts", "./src/api/notification.ts", "./src/api/productsync.ts", "./src/api/purchaseordersync.ts", "./src/api/qualityinspectionsync.ts", "./src/api/refund.ts", "./src/api/refundpackagesync.ts", "./src/api/role.ts", "./src/api/salessync.ts", "./src/api/shop.ts", "./src/api/user.ts", "./src/api/local/qualityinspection.ts", "./src/api/local/refund.ts", "./src/api/local/sales.ts", "./src/api/local/violation.ts", "./src/api/local/violationinspection.ts", "./src/api/product/new-arrival.ts", "./src/api/production/group.ts", "./src/api/production/member.ts", "./src/api/production/role.ts", "./src/api/temu/productlabel.ts", "./src/api/temu/productionprogress.ts", "./src/api/temu/purchaseorder.ts", "./src/api/temu/shiporder.ts", "./src/api/temu/warehouse.ts", "./src/components/temu/index.ts", "./src/directives/permission/index.ts", "./src/router/index.ts", "./src/store/index.ts", "./src/store/modules/app.ts", "./src/store/modules/exporttask.ts", "./src/store/modules/leaderassign.ts", "./src/store/modules/localqcinspection.ts", "./src/store/modules/localrefund.ts", "./src/store/modules/message.ts", "./src/store/modules/permission.ts", "./src/store/modules/purchaseorder.ts", "./src/store/modules/qcinspection.ts", "./src/store/modules/refund.ts", "./src/store/modules/shiporder.ts", "./src/store/modules/user.ts", "./src/store/modules/violation.ts", "./src/types/api.ts", "./src/types/datapermission.d.ts", "./src/types/group.ts", "./src/types/index.d.ts", "./src/types/leaderassign.ts", "./src/types/message.ts", "./src/types/notification.ts", "./src/types/purchaseorder.ts", "./src/types/qcinspection.ts", "./src/types/refund.ts", "./src/types/shiporder.ts", "./src/types/shop.ts", "./src/types/warehouse.ts", "./src/types/local/qcinspection.ts", "./src/types/local/refund.ts", "./src/types/local/sales.ts", "./src/types/local/violation.ts", "./src/types/local/violationinspection.ts", "./src/utils/auth.ts", "./src/utils/date.ts", "./src/utils/encrypt.ts", "./src/utils/excel.ts", "./src/utils/exceljs-helper.ts", "./src/utils/format.ts", "./src/utils/importrouter.ts", "./src/utils/permission.ts", "./src/utils/remember.ts", "./src/utils/request.ts", "./src/utils/shop-helper.ts", "./src/utils/validate.ts", "./src/app.vue", "./src/components/helloworld.vue", "./src/components/permissionbutton.vue", "./src/components/breadcrumb/index.vue", "./src/components/hamburger/index.vue", "./src/components/iconselect/index.vue", "./src/components/messagelist/index.vue", "./src/components/messagenotification/index.vue", "./src/components/notificationtypetag/index.vue", "./src/components/pagination/index.vue", "./src/components/table/index.vue", "./src/components/tagsview/index.vue", "./src/components/temu/applayout.vue", "./src/components/temu/deliverytimeline.vue", "./src/components/temu/emptytips.vue", "./src/components/temu/exportdialog.vue", "./src/components/temu/exporttaskpanel.vue", "./src/components/temu/imagepreview.vue", "./src/components/temu/paginationbar.vue", "./src/components/temu/productionprogressviewer.vue", "./src/components/temu/qrcodetest.vue", "./src/components/temu/qrcodeviewer.vue", "./src/components/temu/searchcard.vue", "./src/components/temu/tablecard.vue", "./src/components/temu/taginput.vue", "./src/layout/index.vue", "./src/layout/components/appmain.vue", "./src/layout/components/navbar.vue", "./src/layout/components/sidebar/link.vue", "./src/layout/components/sidebar/logo.vue", "./src/layout/components/sidebar/sidebaritem.vue", "./src/layout/components/sidebar/index.vue", "./src/views/dashboard/index.vue", "./src/views/error/404.vue", "./src/views/local/qc-detail/index.vue", "./src/views/local/refund-detail/index.vue", "./src/views/local/sales-data/index.vue", "./src/views/local/violation/index.vue", "./src/views/local/violation-inspection/index.vue", "./src/views/login/index.vue", "./src/views/message/message-template/index.vue", "./src/views/message/my-message/index.vue", "./src/views/message/notification-config/index.vue", "./src/views/message/notification-record/index.vue", "./src/views/message/notification-test/index.vue", "./src/views/message/send-message/index.vue", "./src/views/monitor/apilog/index.vue", "./src/views/monitor/operlog/index.vue", "./src/views/operation/group/detail.vue", "./src/views/operation/group/index.vue", "./src/views/operation/group/members.vue", "./src/views/operation/group/components/groupdetail.vue", "./src/views/operation/leader-assign/index.vue", "./src/views/operation/leader-assign/components/assigndialog.vue", "./src/views/operation/leader-assign/components/creatememberdialog.vue", "./src/views/operation/leader-assign/components/permissiondialog.vue", "./src/views/operation/shop/index.vue", "./src/views/operation/shop/components/shopdetail.vue", "./src/views/operation/shop/components/shopform.vue", "./src/views/operation/task/index.vue", "./src/views/production/group/index.vue", "./src/views/production/member/index.vue", "./src/views/profile/index.vue", "./src/views/profile/components/resetpassword.vue", "./src/views/profile/components/userinfo.vue", "./src/views/redirect/index.vue", "./src/views/synchronous/product-sync/index.vue", "./src/views/synchronous/purchase-order-sync/index.vue", "./src/views/synchronous/quality-inspection-sync/index.vue", "./src/views/synchronous/refund-package-sync/index.vue", "./src/views/synchronous/sales-sync/index.vue", "./src/views/system/menu/index.vue", "./src/views/system/menu/components/menuform.vue", "./src/views/system/role/index.vue", "./src/views/system/role/components/datapermissiondialog.vue", "./src/views/system/role/components/permissiondialog.vue", "./src/views/system/role/components/roledialog.vue", "./src/views/system/user/index.vue", "./src/views/system/user/components/resetpwddialog.vue", "./src/views/system/user/components/roledialog.vue", "./src/views/system/user/components/userform.vue", "./src/views/temu/purchase-order/normalpurchasetab.vue", "./src/views/temu/purchase-order/urgentpurchasetab.vue", "./src/views/temu/purchase-order/index.vue", "./src/views/temu/purchase-order/normalpurchasetab/actionbar.vue", "./src/views/temu/purchase-order/normalpurchasetab/purchaseordertable.vue", "./src/views/temu/purchase-order/normalpurchasetab/searcharea.vue", "./src/views/temu/purchase-order/normalpurchasetab/statustabs.vue", "./src/views/temu/purchase-order/urgentpurchasetab/actionbar.vue", "./src/views/temu/purchase-order/urgentpurchasetab/pickingorderpreview.vue", "./src/views/temu/purchase-order/urgentpurchasetab/purchaseordertable.vue", "./src/views/temu/purchase-order/urgentpurchasetab/searcharea.vue", "./src/views/temu/purchase-order/urgentpurchasetab/statustabs.vue", "./src/views/temu/purchase-order/components/productlabelpreview.vue", "./src/views/temu/qc-detail/index.vue", "./src/views/temu/returndetails/index.vue", "./src/views/temu/sales-data/index.vue", "./src/views/temu/ship-order/jitshipordertab.vue", "./src/views/temu/ship-order/normalshipordertab.vue", "./src/views/temu/ship-order/index.vue", "./src/views/temu/ship-order/components/exportbutton.vue", "./src/views/temu/ship-order/components/packageinfoprinter.vue", "./src/views/temu/ship-order/components/packagelabelprinter.vue", "./src/views/temu/ship-order/components/searchform.vue", "./src/views/temu/ship-order/components/statustabs.vue"], "version": "5.7.3"}