package com.xiao.temu.security.model;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 登录请求实体类
 */
@Data
public class LoginRequest {
    
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    @NotBlank(message = "密码不能为空")
    private String password;
    
    private Boolean rememberMe = false;
    
    private String captcha;
    
    private String captchaKey;
} 