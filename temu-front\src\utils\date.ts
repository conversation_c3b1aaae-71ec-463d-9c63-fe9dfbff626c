/**
 * 格式化日期
 * @param date 日期对象、时间戳或日期字符串
 * @param pattern 格式化模式 默认：YYYY-MM-DD HH:mm:ss
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: Date | number | string, pattern = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!date) {
    return '-'
  }
  
  let dateObj: Date
  if (typeof date === 'string') {
    // 处理只有日期部分的字符串
    if (date.indexOf('T') === -1 && date.indexOf(' ') === -1) {
      date = date + ' 00:00:00'
    }
    dateObj = new Date(date)
  } else if (typeof date === 'number') {
    dateObj = new Date(date)
  } else {
    dateObj = date
  }
  
  // 如果日期无效，返回原始字符串或'-'
  if (isNaN(dateObj.getTime())) {
    return typeof date === 'string' ? date : '-'
  }
  
  const o: Record<string, number | string> = {
    'M+': dateObj.getMonth() + 1, // 月份
    'D+': dateObj.getDate(), // 日
    'H+': dateObj.getHours(), // 小时
    'm+': dateObj.getMinutes(), // 分
    's+': dateObj.getSeconds(), // 秒
    'q+': Math.floor((dateObj.getMonth() + 3) / 3), // 季度
    'S': dateObj.getMilliseconds() // 毫秒
  }
  
  // 替换年份
  if (/(Y+)/.test(pattern)) {
    pattern = pattern.replace(RegExp.$1, 
      (dateObj.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  
  // 替换其他时间部分
  for (const k in o) {
    if (new RegExp('(' + k + ')').test(pattern)) {
      pattern = pattern.replace(RegExp.$1, 
        RegExp.$1.length === 1 ? o[k] as string : 
        ('00' + o[k]).substr(('' + o[k]).length))
    }
  }
  
  return pattern
} 