<!-- 角色分配弹窗 -->
<template>
  <el-dialog 
    :title="`分配角色: ${userName}`" 
    v-model="dialogVisible" 
    width="600px" 
    append-to-body
    destroy-on-close
  >
    <el-form label-width="80px">
      <el-form-item label="角色列表">
        <el-checkbox-group v-model="selectedRoleIds">
          <el-checkbox 
            v-for="role in roleOptions" 
            :key="role.roleId" 
            :label="role.roleId"
            :disabled="role.status === '1'"
          >
            {{ role.roleName }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getAllRoles } from '@/api/role'
import { getUserRoles, assignRoles } from '@/api/user'

const props = defineProps({
  // 是否显示
  visible: {
    type: Boolean,
    default: false
  },
  // 用户ID
  userId: {
    type: Number,
    default: 0
  },
  // 用户名
  userName: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 角色选项列表
const roleOptions = ref<any[]>([])
// 已选择的角色ID
const selectedRoleIds = ref<number[]>([])

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 监听props.userId变化
watch(() => props.userId, (newVal) => {
  if (newVal > 0 && dialogVisible.value) {
    // 获取角色选项和已分配角色
    getRoleOptions()
    getUserRoleIds(newVal)
  }
}, { immediate: true })

// 监听对话框可见性
watch(() => dialogVisible.value, (newVal) => {
  if (newVal && props.userId > 0) {
    // 获取角色选项和已分配角色
    getRoleOptions()
    getUserRoleIds(props.userId)
  } else {
    // 重置数据
    selectedRoleIds.value = []
  }
})

// 获取所有角色列表
const getRoleOptions = async () => {
  try {
    const res = await getAllRoles()
    roleOptions.value = res.data
  } catch (error) {
    console.error('获取角色列表失败', error)
    ElMessage.error('获取角色列表失败')
  }
}

// 获取用户已分配的角色ID
const getUserRoleIds = async (userId: number) => {
  try {
    const res = await getUserRoles(userId)
    // 将返回的角色列表转换为角色ID数组
    selectedRoleIds.value = res.data.map((role: any) => role.roleId)
  } catch (error) {
    console.error('获取用户角色失败', error)
    ElMessage.error('获取用户角色失败')
  }
}

// 取消操作
const cancel = () => {
  dialogVisible.value = false
  selectedRoleIds.value = []
}

// 提交表单
const submitForm = async () => {
  try {
    await assignRoles(props.userId, selectedRoleIds.value)
    ElMessage.success('分配角色成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('分配角色失败', error)
    ElMessage.error('分配角色失败')
  }
}

// 导入watch方法
import { watch } from 'vue'
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.el-checkbox {
  margin-right: 15px;
  margin-bottom: 10px;
}

.el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
}
</style> 