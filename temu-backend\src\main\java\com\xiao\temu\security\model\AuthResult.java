package com.xiao.temu.security.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 认证结果实体类，用于返回登录结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthResult {
    
    private String token;            // JWT令牌
    private String tokenType;        // 令牌类型
    private Long expiration;         // 过期时间（秒）
    private Long userId;             // 用户ID
    private String username;         // 用户名
    private String nickName;         // 昵称
    private String[] roles;          // 角色列表
    private String[] permissions;    // 权限列表
    private Map<String, String[]> rolePermissions;  // 每个角色对应的权限字符
    private Map<String, String> roleNames;          // 每个角色标识对应的角色名称
} 