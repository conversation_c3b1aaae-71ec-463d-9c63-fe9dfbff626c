package com.xiao.temu.modules.sales.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 销售仓库库存详情实体类
 * 对应数据库表：sales_warehouse_inventory
 * 存储每个SKU在特定仓库的详细库存信息
 */
@Data
@TableName("sales_warehouse_inventory")
public class SalesWarehouseInventory {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 商品SKU ID
     */
    private Long productSkuId;

    /**
     * 仓库组ID
     */
    private Long warehouseGroupId;

    /**
     * 待上架库存
     */
    private Integer waitOnShelfNum;

    /**
     * 仓内库存
     */
    private Integer warehouseInventoryNum;

    /**
     * 预计占用库存
     */
    private Integer expectedOccupiedInventoryNum;

    /**
     * 待审核备货库存
     */
    private Integer waitApproveInventoryNum;

    /**
     * 已上架待质检库存
     */
    private Integer waitQcNum;

    /**
     * 仓内暂不可用库存
     */
    private Integer unavailableWarehouseInventoryNum;

    /**
     * 待入库库存
     */
    private Integer waitInStock;

    /**
     * 待收货库存
     */
    private Integer waitReceiveNum;

    /**
     * 待发货库存
     */
    private Integer waitDeliveryInventoryNum;

    /**
     * 同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 