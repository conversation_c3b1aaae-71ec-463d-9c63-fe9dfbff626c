// 店铺信息类型
export interface Shop {
  shopId: number
  shopName: string
  shopRemark?: string
  shopLogo?: string
  [key: string]: any
}

// 备货单SKU详情
export interface PurchaseOrderSkuDetail {
  className: string
  currencyType: string
  productSkuId: number
  fulfilmentProductSkuId: number
  thumbUrlList: string[]
  adviceQuantity: number
  purchaseQuantity: number
  deliverQuantity: number
  purchaseUpLimit: number
  realReceiveAuthenticQuantity: number
  supportIncreaseNum: boolean
  customizationType: number
  extCode: string
  [key: string]: any
}

// SKU数量总计信息
export interface SkuQuantityTotalInfo {
  purchaseQuantity: number
  deliverQuantity: number
  realReceiveAuthenticQuantity: number
  [key: string]: any
}

// 备货单信息
export interface PurchaseOrder {
  originalPurchaseOrderSn: string
  subPurchaseOrderSn: string
  productName: string
  productSn: string
  productId: number
  productSkcId: number
  productSkcPicture: string
  supplierId: number
  supplierName: string
  status: number
  purchaseTime: number
  skuQuantityDetailList: PurchaseOrderSkuDetail[]
  skuQuantityTotalInfo: SkuQuantityTotalInfo
  category: string
  deliverInfo?: {
    expectLatestDeliverTimeOrDefault: number
    expectLatestArrivalTimeOrDefault?: number
  }
  todayCanDeliver: boolean
  urgencyType: number
  [key: string]: any
}

// 备货单请求参数
export interface PurchaseOrderRequestDTO {
  shopIds?: number[] // 店铺ID列表，支持多选
  startTime?: string // 开始时间
  endTime?: string // 结束时间
  pageNo: number // 页码
  pageSize: number // 每页条数
  productSkuIds?: number[] // 商品SKU IDs
  supplierIds?: number[] // 供应商IDs
  purchaseSubOrderSns?: string[] // 备货单号列表
  [key: string]: any
}

// 新增：店铺数据范围信息类型
export interface ShopDataRange {
  shopId: number
  shopName: string
  startIndex: number
  endIndex: number
  accessibleCount: number
  actualCount: number
} 