package com.xiao.temu.config.redis;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.stereotype.Component;

/**
 * Redis健康检查指示器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedisHealthIndicator implements HealthIndicator {

    private final RedisConnectionFactory redisConnectionFactory;

    @Override
    public Health health() {
        try (RedisConnection connection = redisConnectionFactory.getConnection()) {
            if (connection.ping() != null) {
                return Health.up()
                        .withDetail("redis", "Redis")
                        .withDetail("status", "Available")
                        .build();
            } else {
                return Health.down()
                        .withDetail("redis", "Redis")
                        .withDetail("status", "Unavailable")
                        .withDetail("error", "Redis ping返回null")
                        .build();
            }
        } catch (Exception e) {
            log.error("Redis连接异常", e);
            return Health.down()
                    .withDetail("redis", "Redis")
                    .withDetail("status", "Unavailable")
                    .withDetail("error", e.getMessage())
                    .build();
        }
    }
} 