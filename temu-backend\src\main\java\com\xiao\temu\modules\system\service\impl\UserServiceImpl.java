package com.xiao.temu.modules.system.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.system.entity.SysUserRole;
import com.xiao.temu.modules.system.entity.SysRole;
import com.xiao.temu.infrastructure.exporter.dto.ExportRequestDTO;
import com.xiao.temu.modules.system.dto.UserExportDTO;
import com.xiao.temu.modules.system.dto.UserImportDTO;
import com.xiao.temu.infrastructure.listener.UserImportListener;
import com.xiao.temu.modules.system.mapper.SysUserMapper;
import com.xiao.temu.modules.system.mapper.SysUserRoleMapper;
import com.xiao.temu.modules.system.mapper.SysRoleMapper;
import com.xiao.temu.security.service.PermissionService;
import com.xiao.temu.security.utils.SecurityUtils;
import com.xiao.temu.modules.system.service.UserService;
import com.xiao.temu.modules.system.service.RoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final SysUserMapper userMapper;
    private final SysUserRoleMapper userRoleMapper;
    private final PasswordEncoder passwordEncoder;
    private final PermissionService permissionService;
    private final SysRoleMapper roleMapper;
    private final RoleService roleService;

    @Override
    public SysUser getUserById(Long userId) {
        SysUser user = userMapper.selectById(userId);
        return removeSensitiveInfo(user);
    }

    @Override
    public SysUser getUserByUsername(String username) {
        return userMapper.selectUserByUsername(username);
    }

    @Override
    public IPage<SysUser> getUserList(SysUser user, Page<SysUser> page, String orderBy, String orderDir) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (user != null) {
            // 按用户名模糊查询
            if (StringUtils.isNotBlank(user.getUsername())) {
                queryWrapper.like(SysUser::getUsername, user.getUsername());
            }
            
            // 按昵称模糊查询
            if (StringUtils.isNotBlank(user.getNickName())) {
                queryWrapper.like(SysUser::getNickName, user.getNickName());
            }
            
            // 按状态查询
            if (StringUtils.isNotBlank(user.getStatus())) {
                queryWrapper.eq(SysUser::getStatus, user.getStatus());
            }
            
            // 按邮箱查询
            if (StringUtils.isNotBlank(user.getEmail())) {
                queryWrapper.like(SysUser::getEmail, user.getEmail());
            }
            
            // 按手机号查询
            if (StringUtils.isNotBlank(user.getPhone())) {
                queryWrapper.like(SysUser::getPhone, user.getPhone());
            }
        }
        
        // 处理排序
        if (StringUtils.isNotBlank(orderBy) && StringUtils.isNotBlank(orderDir)) {
            // 根据不同字段进行排序
            switch (orderBy) {
                case "userId":
                    if ("asc".equalsIgnoreCase(orderDir)) {
                        queryWrapper.orderByAsc(SysUser::getUserId);
                    } else {
                        queryWrapper.orderByDesc(SysUser::getUserId);
                    }
                    break;
                case "username":
                    if ("asc".equalsIgnoreCase(orderDir)) {
                        queryWrapper.orderByAsc(SysUser::getUsername);
                    } else {
                        queryWrapper.orderByDesc(SysUser::getUsername);
                    }
                    break;
                case "nickName":
                    if ("asc".equalsIgnoreCase(orderDir)) {
                        queryWrapper.orderByAsc(SysUser::getNickName);
                    } else {
                        queryWrapper.orderByDesc(SysUser::getNickName);
                    }
                    break;
                case "email":
                    if ("asc".equalsIgnoreCase(orderDir)) {
                        queryWrapper.orderByAsc(SysUser::getEmail);
                    } else {
                        queryWrapper.orderByDesc(SysUser::getEmail);
                    }
                    break;
                case "phone":
                    if ("asc".equalsIgnoreCase(orderDir)) {
                        queryWrapper.orderByAsc(SysUser::getPhone);
                    } else {
                        queryWrapper.orderByDesc(SysUser::getPhone);
                    }
                    break;
                case "status":
                    if ("asc".equalsIgnoreCase(orderDir)) {
                        queryWrapper.orderByAsc(SysUser::getStatus);
                    } else {
                        queryWrapper.orderByDesc(SysUser::getStatus);
                    }
                    break;
                case "createTime":
                default:
                    if ("asc".equalsIgnoreCase(orderDir)) {
                        queryWrapper.orderByAsc(SysUser::getCreateTime);
                    } else {
                        queryWrapper.orderByDesc(SysUser::getCreateTime);
                    }
                    break;
            }
        } else {
            // 默认按创建时间倒序排序
            queryWrapper.orderByDesc(SysUser::getCreateTime);
        }
        
        // 执行分页查询
        IPage<SysUser> result = userMapper.selectPage(page, queryWrapper);
        
        // 清除敏感信息
        if (result.getRecords() != null) {
            result.getRecords().forEach(this::removeSensitiveInfo);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUser(SysUser user) {
        // 校验用户名是否唯一
        if (!checkUsernameUnique(user.getUsername())) {
            throw new RuntimeException("新增用户'" + user.getUsername() + "'失败，登录账号已存在");
        }
        
        // 设置默认值
        user.setCreateTime(new Date());
        
        // 密码加密
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        // 插入用户
        return userMapper.insert(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUser(SysUser user) {
        // 校验用户是否存在
        SysUser existingUser = userMapper.selectById(user.getUserId());
        if (existingUser == null) {
            throw new RuntimeException("修改用户失败，用户不存在");
        }
        
        // 用户名不允许修改
        user.setUsername(null);
        
        // 密码不允许通过这个接口修改
        user.setPassword(null);
        
        // 设置更新时间
        user.setUpdateTime(new Date());
        
        // 执行更新
        return userMapper.updateById(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUser(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        
        // 删除用户
        return userMapper.deleteById(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUsers(Long[] userIds) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserIds(Arrays.asList(userIds));
        
        // 批量删除用户
        return userMapper.deleteBatchIds(Arrays.asList(userIds));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int resetPassword(Long userId, String password) {
        // 校验用户是否存在
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("重置密码失败，用户不存在");
        }
        
        // 更新密码
        SysUser updateUser = new SysUser();
        updateUser.setUserId(userId);
        updateUser.setPassword(passwordEncoder.encode(password));
        updateUser.setUpdateTime(new Date());
        
        return userMapper.updateById(updateUser);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int changeStatus(Long userId, String status) {
        // 校验用户是否存在
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("修改用户状态失败，用户不存在");
        }
        
        // 更新状态
        SysUser updateUser = new SysUser();
        updateUser.setUserId(userId);
        updateUser.setStatus(status);
        updateUser.setUpdateTime(new Date());
        
        return userMapper.updateById(updateUser);
    }

    @Override
    public boolean checkUsernameUnique(String username) {
        return userMapper.checkUsernameUnique(username) == 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int assignRoles(Long userId, Long[] roleIds) {
        // 校验用户是否存在
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("分配角色失败，用户不存在");
        }
        
        // 删除原有角色关系
        userRoleMapper.deleteUserRoleByUserId(userId);
        
        // 如果没有分配角色，直接返回
        if (roleIds == null || roleIds.length == 0) {
            return 1;
        }
        
        // 构建用户角色关系
        List<SysUserRole> userRoleList = Arrays.stream(roleIds)
                .map(roleId -> {
                    SysUserRole userRole = new SysUserRole();
                    userRole.setUserId(userId);
                    userRole.setRoleId(roleId);
                    return userRole;
                })
                .collect(Collectors.toList());
        
        // 批量插入用户角色关系
        userRoleMapper.batchInsertUserRole(userRoleList);
        
        // 刷新用户权限缓存
        permissionService.reloadPermission(userId);
        
        return 1;
    }

    @Override
    public List<Long> getUserRoleIds(Long userId) {
        return userRoleMapper.selectRoleIdsByUserId(userId);
    }

    @Override
    public boolean checkPassword(Long userId, String password) {
        // 获取用户信息
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }
        
        // 验证密码
        return passwordEncoder.matches(password, user.getPassword());
    }

    @Override
    public List<SysUser> getUsersByPermission(String permission) {
        // 获取当前登录用户ID
        Long currentUserId = SecurityUtils.getCurrentUserId();
        
        // 获取有特定权限的用户ID列表
        List<Long> userIds = permissionService.getUserIdsByPermission(permission);
        if (userIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 查询用户列表
        List<SysUser> users;
        
        // 判断当前用户权限
        if (SecurityUtils.isAdmin()) {
            // 管理员可以查看所有符合条件的用户
            users = userMapper.selectBatchIds(userIds);
        } else {
            // 非管理员只能查看自己
            if (userIds.contains(currentUserId)) {
                users = Collections.singletonList(userMapper.selectById(currentUserId));
            } else {
                // 当前用户没有指定权限，返回空列表
                return new ArrayList<>();
            }
        }
        
        // 处理返回结果，移除敏感信息
        return users.stream()
            .map(this::removeSensitiveInfo)
            .collect(Collectors.toList());
    }

    /**
     * 移除用户敏感信息
     * @param user 用户对象
     * @return 移除敏感信息后的用户对象
     */
    private SysUser removeSensitiveInfo(SysUser user) {
        if (user != null) {
            // 清除密码等敏感信息
            user.setPassword(null);
            // 可以根据需要清除其他敏感字段
        }
        return user;
    }

    @Override
    public List<SysUser> getUsersByRoleKey(String roleKey) {
        // 查询具有特定角色的用户ID列表
        List<Long> roleIds = roleMapper.selectRoleIdsByRoleKey(roleKey);
        if (roleIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 获取用户ID列表
        List<Long> userIds = userRoleMapper.selectUserIdsByRoleIds(roleIds);
        if (userIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 查询用户列表
        List<SysUser> users = userMapper.selectBatchIds(userIds);
        
        // 处理返回结果，移除敏感信息
        return users.stream()
            .map(this::removeSensitiveInfo)
            .collect(Collectors.toList());
    }

    @Override
    public List<UserExportDTO> getExportData(ExportRequestDTO exportDTO) {
        // 构建查询条件
        SysUser queryParams = new SysUser();
        if (exportDTO.getQueryParams() != null) {
            if (exportDTO.getQueryParams().containsKey("username")) {
                queryParams.setUsername((String) exportDTO.getQueryParams().get("username"));
            }
            if (exportDTO.getQueryParams().containsKey("phone")) {
                queryParams.setPhone((String) exportDTO.getQueryParams().get("phone"));
            }
            if (exportDTO.getQueryParams().containsKey("status")) {
                queryParams.setStatus((String) exportDTO.getQueryParams().get("status"));
            }
        }

        // 根据导出类型获取数据
        List<SysUser> userList;
        String exportType = exportDTO.getExportType();
        
        if ("all".equals(exportType)) {
            // 导出全部数据
            LambdaQueryWrapper<SysUser> wrapper = createQueryWrapper(queryParams);
            userList = userMapper.selectList(wrapper);
        } else if ("page".equals(exportType)) {
            // 导出当前页数据
            LambdaQueryWrapper<SysUser> wrapper = createQueryWrapper(queryParams);
            Page<SysUser> page = new Page<>(exportDTO.getPageNum(), exportDTO.getPageSize());
            IPage<SysUser> pageResult = userMapper.selectPage(page, wrapper);
            userList = pageResult.getRecords();
        } else if ("selected".equals(exportType)) {
            // 导出选中数据
            List<Long> selectedIds = exportDTO.getSelectedIds();
            if (selectedIds == null || selectedIds.isEmpty()) {
                return new ArrayList<>();
            }
            userList = userMapper.selectBatchIds(selectedIds);
        } else {
            throw new IllegalArgumentException("未知的导出类型: " + exportType);
        }

        // 转换为导出DTO
        return userList.stream().map(this::convertToExportDTO).collect(Collectors.toList());
    }
    
    /**
     * 创建查询条件包装器
     */
    private LambdaQueryWrapper<SysUser> createQueryWrapper(SysUser user) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据条件构建查询
        if (user != null) {
            // 按用户名模糊查询
            if (StringUtils.isNotBlank(user.getUsername())) {
                queryWrapper.like(SysUser::getUsername, user.getUsername());
            }
            
            // 按状态精确查询
            if (StringUtils.isNotBlank(user.getStatus())) {
                queryWrapper.eq(SysUser::getStatus, user.getStatus());
            }
            
            // 按手机号模糊查询
            if (StringUtils.isNotBlank(user.getPhone())) {
                queryWrapper.like(SysUser::getPhone, user.getPhone());
            }
        }
        
        // 默认按用户ID排序
        queryWrapper.orderByAsc(SysUser::getUserId);
        
        return queryWrapper;
    }
    
    /**
     * 将用户实体转换为导出DTO
     */
    private UserExportDTO convertToExportDTO(SysUser user) {
        if (user == null) {
            return null;
        }
        
        UserExportDTO dto = new UserExportDTO();
        dto.setUserId(user.getUserId());
        dto.setUsername(user.getUsername());
        dto.setNickName(user.getNickName());
        dto.setEmail(user.getEmail());
        dto.setPhone(user.getPhone());
        dto.setStatus(user.getStatus());
        // 设置状态标签
        dto.setStatusLabel("0".equals(user.getStatus()) ? "正常" : "禁用");
        
        // 设置用户角色信息
        List<SysRole> roles = roleService.getRolesByUserId(user.getUserId());
        if (roles != null && !roles.isEmpty()) {
            String roleNameStr = roles.stream()
                .map(SysRole::getRoleName)
                .collect(Collectors.joining(", "));
            dto.setRoleNames(roleNameStr);
        } else {
            dto.setRoleNames("无角色");
        }
        
        dto.setCreateTime(user.getCreateTime());
        dto.setUpdateTime(user.getUpdateTime());
        dto.setRemark(user.getRemark());
        
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchImportUsers(List<SysUser> userList, Map<String, List<Long>> userRoleMap) {
        if (userList == null || userList.isEmpty()) {
            return 0;
        }
        
        // 批量插入用户
        int successCount = 0;
        for (SysUser user : userList) {
            try {
                // 插入用户
                userMapper.insert(user);
                successCount++;
                
                // 如果有角色信息，则分配角色
                List<Long> roleIds = userRoleMap.get(user.getUsername());
                if (roleIds != null && !roleIds.isEmpty()) {
                    // 构建用户角色关系
                    List<SysUserRole> userRoleList = roleIds.stream()
                            .map(roleId -> {
                                SysUserRole userRole = new SysUserRole();
                                userRole.setUserId(user.getUserId());
                                userRole.setRoleId(roleId);
                                return userRole;
                            })
                            .collect(Collectors.toList());
                    
                    // 批量插入用户角色关系
                    userRoleMapper.batchInsertUserRole(userRoleList);
                }
            } catch (Exception e) {
                log.error("插入用户出错: {}", user.getUsername(), e);
                throw new RuntimeException("插入用户出错: " + user.getUsername(), e);
            }
        }
        
        return successCount;
    }
    
    @Override
    public Map<String, Object> importUserFromExcel(InputStream inputStream) {
        try {
            // 创建Excel读取监听器
            UserImportListener listener = new UserImportListener(this, roleService, passwordEncoder);
            
            // 读取Excel
            ExcelReaderBuilder readerBuilder = EasyExcel.read(inputStream, UserImportDTO.class, listener);
            readerBuilder.sheet().doRead();
            
            // 构建导入结果
            Map<String, Object> result = new HashMap<>();
            result.put("successCount", listener.getSuccessCount());
            result.put("failCount", listener.getFailCount());
            result.put("errorMsgs", listener.getErrorMsgs());
            
            return result;
        } catch (Exception e) {
            log.error("导入Excel出错", e);
            Map<String, Object> result = new HashMap<>();
            result.put("successCount", 0);
            result.put("failCount", 0);
            result.put("errorMsgs", Collections.singletonList("导入Excel出错: " + e.getMessage()));
            return result;
        }
    }
    
    @Override
    public void getImportTemplate(HttpServletResponse response) throws IOException {
        // 创建示例数据
        List<UserImportDTO> list = new ArrayList<>();
        UserImportDTO example = new UserImportDTO();
        example.setUsername("zhangsan");
        example.setNickName("张三");
        example.setEmail("<EMAIL>");
        example.setPhone("13800000000");
        example.setRoleNames("系统管理员");
        example.setRemark("示例数据");
        list.add(example);
        
        // 设置响应头
        String fileName = "用户导入模板";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = java.net.URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");
        
        // 写入模板数据
        EasyExcel.write(response.getOutputStream(), UserImportDTO.class)
                .sheet("用户数据")
                .doWrite(list);
    }

    /**
     * 判断用户是否为管理员
     *
     * @param userId 用户ID
     * @return 是否为管理员
     */
    @Override
    public boolean isAdmin(Long userId) {
        // 获取用户角色列表
        List<String> roleList = permissionService.getRolesByUserId(userId);
        
        // 检查是否包含admin角色
        if (roleList.contains("admin")) {
            return true;
        }
        
        // 检查是否拥有系统管理权限
        List<String> permissionList = permissionService.getPermissionsByUserId(userId);
        return permissionList.stream().anyMatch(permission -> 
                permission.startsWith("system:") || 
                permission.contains(":all:"));
    }
} 