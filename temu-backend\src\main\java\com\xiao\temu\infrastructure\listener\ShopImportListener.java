package com.xiao.temu.infrastructure.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.xiao.temu.modules.operation.entity.OperationGroup;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.shop.dto.ShopImportDTO;
import com.xiao.temu.modules.operation.service.OperationGroupService;
import com.xiao.temu.modules.shop.service.ShopService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 店铺导入Excel监听器
 */
@Slf4j
public class ShopImportListener extends AnalysisEventListener<ShopImportDTO> {

    /**
     * 数据处理批次大小
     */
    private static final int BATCH_SIZE = 100;

    /**
     * 店铺服务
     */
    private final ShopService shopService;

    /**
     * 运营组服务
     */
    private final OperationGroupService groupService;

    /**
     * 存储待导入的店铺数据
     */
    @Getter
    private final List<Shop> shopList = new ArrayList<>();

    /**
     * 存储店铺与运营组的映射关系 (店铺名称 -> 运营组ID列表)
     */
    @Getter
    private final Map<String, List<Long>> shopGroupMap = new HashMap<>();

    /**
     * 所有系统运营组的映射表 - 运营组名称 -> 运营组ID
     */
    private Map<String, Long> groupNameMap;

    /**
     * 成功导入计数
     */
    @Getter
    private int successCount = 0;

    /**
     * 失败导入计数
     */
    @Getter
    private int failCount = 0;

    /**
     * 失败信息列表
     */
    @Getter
    private final List<String> errorMsgs = new ArrayList<>();

    /**
     * 构造函数
     */
    public ShopImportListener(ShopService shopService, OperationGroupService groupService) {
        this.shopService = shopService;
        this.groupService = groupService;
        initGroupNameMap();
    }

    /**
     * 初始化运营组名称映射表
     */
    private void initGroupNameMap() {
        // 获取所有运营组
        List<OperationGroup> groups = groupService.getAllGroups();
        // 构建运营组名称到运营组ID的映射
        this.groupNameMap = groups.stream()
                .collect(Collectors.toMap(OperationGroup::getGroupName, OperationGroup::getGroupId, (v1, v2) -> v1));
    }

    /**
     * 每解析一行数据，调用该方法
     */
    @Override
    public void invoke(ShopImportDTO data, AnalysisContext context) {
        try {
            // 表格行号
            int rowIndex = context.readRowHolder().getRowIndex() + 1;
            
            // 校验数据
            String errorMsg = validateData(data, rowIndex);
            if (errorMsg != null) {
                failCount++;
                errorMsgs.add(errorMsg);
                return;
            }
            
            // 检查店铺名称是否唯一
            if (!shopService.checkShopNameUnique(data.getShopName())) {
                failCount++;
                errorMsgs.add("第" + rowIndex + "行: 店铺名称 '" + data.getShopName() + "' 已存在");
                return;
            }
            
            // 转换为Shop对象
            Shop shop = convertToShop(data);
            shopList.add(shop);
            
            // 处理运营组信息
            if (StringUtils.isNotBlank(data.getGroupNames())) {
                String[] groupNames = data.getGroupNames().split(",|，"); // 支持中英文逗号分隔
                List<Long> groupIds = new ArrayList<>();
                
                for (String groupName : groupNames) {
                    String trimmedGroupName = groupName.trim();
                    Long groupId = groupNameMap.get(trimmedGroupName);
                    if (groupId != null) {
                        groupIds.add(groupId);
                    } else {
                        log.warn("导入的运营组 '{}' 不存在", trimmedGroupName);
                    }
                }
                
                if (!groupIds.isEmpty()) {
                    shopGroupMap.put(data.getShopName(), groupIds);
                }
            }
            
            // 如果达到批处理大小，则批量保存
            if (shopList.size() >= BATCH_SIZE) {
                saveShopBatch();
            }
            
            successCount++;
        } catch (Exception e) {
            log.error("解析Excel第{}行数据出错", context.readRowHolder().getRowIndex() + 1, e);
            failCount++;
            errorMsgs.add("第" + (context.readRowHolder().getRowIndex() + 1) + "行: 数据解析出错 - " + e.getMessage());
        }
    }

    /**
     * 所有数据解析完成后调用
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 保存剩余店铺
        saveShopBatch();
        log.info("Excel解析完成，成功导入{}条，失败{}条", successCount, failCount);
    }

    /**
     * 批量保存店铺
     */
    private void saveShopBatch() {
        if (shopList.isEmpty()) {
            return;
        }
        
        try {
            int importCount = shopService.batchImportShops(shopList, shopGroupMap);
            if (importCount > 0) {
                shopList.clear();
                shopGroupMap.clear();
            } else {
                failCount += shopList.size();
                successCount -= shopList.size();
                errorMsgs.add("批量保存店铺出错");
                shopList.clear();
                shopGroupMap.clear();
            }
        } catch (Exception e) {
            log.error("批量保存店铺出错", e);
            failCount += shopList.size();
            successCount -= shopList.size();
            errorMsgs.add("批量保存店铺出错: " + e.getMessage());
            shopList.clear();
            shopGroupMap.clear();
        }
    }

    /**
     * 校验数据
     * @return 错误信息，如果没有错误则返回null
     */
    private String validateData(ShopImportDTO data, int rowIndex) {
        // 校验店铺名称
        if (StringUtils.isBlank(data.getShopName())) {
            return "第" + rowIndex + "行: 店铺名称不能为空";
        }
        
        // 校验Temu店铺ID
        if (StringUtils.isBlank(data.getShopTemuId())) {
            return "第" + rowIndex + "行: Temu店铺ID不能为空";
        }
        
        // 校验API密钥
        if (StringUtils.isBlank(data.getApiKey())) {
            return "第" + rowIndex + "行: API密钥不能为空";
        }
        
        // 校验API密钥Secret
        if (StringUtils.isBlank(data.getApiSecret())) {
            return "第" + rowIndex + "行: API密钥Secret不能为空";
        }
        
        return null;
    }

    /**
     * 将DTO转换为Shop对象
     */
    private Shop convertToShop(ShopImportDTO data) {
        Shop shop = new Shop();
        shop.setShopName(data.getShopName());
        shop.setShopTemuId(data.getShopTemuId());
        shop.setApiKey(data.getApiKey());
        shop.setApiSecret(data.getApiSecret());
        shop.setAccessToken(data.getAccessToken());
        shop.setRemark(data.getRemark());
        
        // 处理状态
        if (StringUtils.isBlank(data.getStatus()) || "正常".equals(data.getStatus())) {
            shop.setStatus("0");
        } else if ("禁用".equals(data.getStatus())) {
            shop.setStatus("1");
        } else {
            shop.setStatus("0"); // 默认正常状态
        }
        
        shop.setCreateTime(LocalDateTime.now());
        
        return shop;
    }
} 