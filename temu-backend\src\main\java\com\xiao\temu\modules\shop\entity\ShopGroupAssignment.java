package com.xiao.temu.modules.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

/**
 * 店铺运营组关联实体类
 */
@TableName("shop_group_assignment")
public class ShopGroupAssignment {
    
    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /** 店铺ID */
    @TableField("shop_id")
    private Long shopId;
    
    /** 运营组ID */
    @TableField("group_id")
    private Long groupId;
    
    /** 分配时间 */
    @TableField("assign_time")
    private LocalDateTime assignTime;
    
    /** 分配人ID */
    @TableField("assign_by")
    private Long assignBy;
    
    /** 状态（0正常 1禁用） */
    @TableField("status")
    private String status;
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public LocalDateTime getAssignTime() {
        return assignTime;
    }

    public void setAssignTime(LocalDateTime assignTime) {
        this.assignTime = assignTime;
    }

    public Long getAssignBy() {
        return assignBy;
    }

    public void setAssignBy(Long assignBy) {
        this.assignBy = assignBy;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
} 