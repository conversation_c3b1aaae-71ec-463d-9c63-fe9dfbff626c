<template>
  <el-button
    v-if="hasPermission"
    :type="type"
    :size="size"
    :loading="loading"
    :disabled="disabled"
    @click="handleClick"
  >
    <slot></slot>
  </el-button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { PropType } from 'vue'
import { hasPermission as checkPermission } from '@/utils/permission'

type PermissionType = string | string[];

const props = defineProps({
  permission: {
    type: [String, Array] as PropType<PermissionType>,
    required: true
  },
  type: {
    type: String,
    default: 'default'
  },
  size: {
    type: String,
    default: 'default'
  },
  loading: {
    type: <PERSON>olean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

// 判断是否有权限
const hasPermission = computed(() => {
  if (props.permission) {
    return checkPermission(props.permission as PermissionType)
  }
  return true
})

// 处理点击事件
function handleClick(e: MouseEvent) {
  emit('click', e)
}
</script> 