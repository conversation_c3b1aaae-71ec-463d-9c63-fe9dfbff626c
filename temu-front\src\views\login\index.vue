<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-form-container">
        <div class="title-container">
          <h3 class="title">TEMU 管理系统</h3>
          <p class="subtitle">欢迎使用，请登录</p>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          auto-complete="on"
          label-position="left"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="用户名"
              type="text"
              tabindex="1"
              auto-complete="on"
            >
              <template #prefix>
                <el-icon><User /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              :type="passwordType"
              placeholder="密码"
              tabindex="2"
              auto-complete="on"
              @keyup.enter="handleLogin"
              @input="handlePasswordInput"
            >
              <template #prefix>
                <el-icon><Lock /></el-icon>
              </template>
              <template #suffix>
                <el-icon
                  class="show-pwd"
                  @click="showPwd"
                >
                  <component :is="passwordType === 'password' ? 'Hide' : 'View'" />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <div class="form-option">
            <el-checkbox v-model="loginForm.rememberMe" class="remember-me">记住密码</el-checkbox>
          </div>

          <el-button
            :loading="loading"
            type="primary"
            class="login-button"
            @click.prevent="handleLogin"
          >
            登录
          </el-button>
        </el-form>
      </div>
      
      <div class="copyright">
        © {{ new Date().getFullYear() }} TEMU管理系统 版权所有
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, unref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { User, Lock, View, Hide } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'
import { getLoginInfo, saveLoginInfo, removeLoginInfo } from '@/utils/remember'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 表单和验证规则
const loginFormRef = ref()
const loading = ref(false)
const passwordType = ref('password')
// 标记密码是否是自动填充的
const isAutoFilled = ref(false)

const redirect = computed(() => {
  return route.query.redirect as string || '/'
})

const loginForm = reactive({
  username: '',
  password: '',
  rememberMe: false
})

const loginRules = {
  username: [{ required: true, trigger: 'blur', message: '请输入用户名' }],
  password: [{ required: true, min: 6, max: 20, trigger: 'blur', message: '密码长度必须在6-20个字符之间' }]
}

// 监听密码输入，如果用户手动输入了密码，则不再视为自动填充
function handlePasswordInput() {
  if (isAutoFilled.value) {
    isAutoFilled.value = false
  }
}

// 显示/隐藏密码
function showPwd() {
  // 如果当前是密码模式，并且密码是通过"记住我"功能自动填充的
  if (passwordType.value === 'password' && isAutoFilled.value) {
    // 清空密码字段
    loginForm.password = ''
    // 切换为明文模式，允许用户重新输入
    passwordType.value = ''
    // 重置自动填充标记
    isAutoFilled.value = false
  } else {
    // 正常切换密码显示模式
    passwordType.value = passwordType.value === 'password' ? '' : 'password'
  }
}

// 页面加载时，尝试从本地存储获取登录信息
onMounted(() => {
  const savedInfo = getLoginInfo()
  if (savedInfo) {
    loginForm.username = savedInfo.username
    loginForm.password = savedInfo.password
    loginForm.rememberMe = savedInfo.rememberMe
    // 标记密码是自动填充的
    isAutoFilled.value = true
  }
})

// 登录处理
async function handleLogin() {
  const formEl = unref(loginFormRef)
  if (!formEl) return
  
  await formEl.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true
      try {
        // 1. 登录获取token
        await userStore.login(loginForm)
        
        // 2. 获取用户信息
        const { roles } = await userStore.getInfo()
        
        // 3. 生成路由
        const permissionStore = usePermissionStore()
        const accessRoutes = await permissionStore.generateRoutes(roles)
        
        // 4. 添加路由
        accessRoutes.forEach(route => {
          router.addRoute(route)
        })
        
        // 5. 如果勾选了"记住我"，则保存登录信息
        if (loginForm.rememberMe) {
          saveLoginInfo(loginForm.username, loginForm.password, loginForm.rememberMe)
        } else {
          // 如果未勾选，则清除之前可能保存的信息
          removeLoginInfo()
        }
        
        ElMessage.success('登录成功')
        router.push({ path: redirect.value })
      } catch (error: any) {
        ElMessage.error(error.response?.data?.message || error.message || '登录失败，请检查用户名和密码')
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style lang="scss" scoped>
$primary-color: #409EFF;
$bg: #F0F2F5;
$text-color: #333;
$light-text: #909399;

.login-container {
  min-height: 100vh;
  width: 100%;
  background-color: $bg;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .login-box {
    width: 100%;
    max-width: 520px;
    padding: 20px;
    
    .login-form-container {
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      padding: 35px 35px 15px;
      margin-bottom: 20px;
    }
  }

  .login-form {
    width: 100%;
    
    .form-option {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .login-button {
      width: 100%;
      margin-bottom: 30px;
    }
  }

  .title-container {
    position: relative;
    text-align: center;
    margin-bottom: 30px;

    .title {
      font-size: 26px;
      color: $text-color;
      font-weight: bold;
      margin-bottom: 10px;
    }
    
    .subtitle {
      font-size: 14px;
      color: $light-text;
      margin: 0;
    }
  }
  
  .copyright {
    text-align: center;
    color: $light-text;
    font-size: 14px;
  }
}
</style> 