<template>
  <div class="search-section">
    <el-card class="box-card">
      <slot></slot>
    </el-card>
  </div>
</template>

<script setup>
// 搜索条件卡片组件
// 使用方式：<SearchCard><您的搜索表单></SearchCard>
</script>

<style scoped>
.search-section {
  flex: 0 0 auto;
  margin: 0;
  padding: 0;
  width: 100%;
}

.search-section .box-card {
  margin-bottom: 0;
  border-radius: 4px 4px 0 0;
  border-bottom: none;
  height: auto !important;
  overflow: visible !important;
}

.box-card {
  margin-bottom: 0;
  height: auto !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.search-section :deep(.el-card__body) {
  padding: 15px;
  min-height: 80px;
  height: auto !important;
  overflow: visible !important;
}
</style> 