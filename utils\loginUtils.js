/**
 * 登录相关工具函数
 */

/**
 * 保存用户登录信息
 * @param {string} username - 用户名
 * @param {string} password - 密码
 */
const saveLoginInfo = (username, password) => {
  try {
    const savedUserInfo = {
      username: username,
      password: password,
      saveTime: Date.now() // 记录保存时间
    };
    
    // 保存记住密码状态和用户信息
    wx.setStorageSync('rememberMe', true);
    wx.setStorageSync('savedUserInfo', savedUserInfo);
  } catch (error) {
    console.error('保存登录信息失败:', error);
  }
};

/**
 * 获取保存的登录信息
 * @returns {Object|null} 保存的登录信息，如果没有则返回null
 */
const getSavedLoginInfo = () => {
  try {
    const rememberMe = wx.getStorageSync('rememberMe');
    
    if (rememberMe) {
      return wx.getStorageSync('savedUserInfo') || null;
    }
    
    return null;
  } catch (error) {
    console.error('获取登录信息失败:', error);
    return null;
  }
};

/**
 * 清除保存的登录信息
 */
const clearSavedLoginInfo = () => {
  try {
    wx.removeStorageSync('rememberMe');
    wx.removeStorageSync('savedUserInfo');
  } catch (error) {
    console.error('清除保存的登录信息失败:', error);
  }
};

/**
 * 检查是否已登录
 * @returns {boolean} 是否已登录
 */
const isLoggedIn = () => {
  try {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    return !!(token && userInfo);
  } catch (error) {
    console.error('检查登录状态失败:', error);
    return false;
  }
};

/**
 * 清除所有登录状态（登出）
 */
const clearLoginState = () => {
  try {
    // 清除token和用户信息
    wx.removeStorageSync('token');
    wx.removeStorageSync('userId');
    wx.removeStorageSync('username');
    wx.removeStorageSync('nickName');
    wx.removeStorageSync('roles');
    wx.removeStorageSync('permissions');
    wx.removeStorageSync('rolePermissions');
    wx.removeStorageSync('roleNames');
    wx.removeStorageSync('userInfo');
    
    // 记住密码相关信息不清除，除非明确取消"记住密码"
  } catch (error) {
    console.error('清除登录状态失败:', error);
  }
};

module.exports = {
  saveLoginInfo,
  getSavedLoginInfo,
  clearSavedLoginInfo,
  isLoggedIn,
  clearLoginState
}; 