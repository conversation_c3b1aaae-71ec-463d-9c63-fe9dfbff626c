package com.xiao.temu.modules.production.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 生产组店铺分配实体类
 */
@Data
@TableName("production_group_shop_assignment")
public class ProductionGroupShopAssignment {
    
    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /** 店铺ID */
    @TableField("shop_id")
    private Long shopId;
    
    /** 生产组ID */
    @TableField("group_id")
    private Long groupId;
    
    /** 分配时间 */
    @TableField("assign_time")
    private LocalDateTime assignTime;
    
    /** 分配人ID */
    @TableField("assign_by")
    private Long assignBy;
    
    /** 状态（0正常 1禁用） */
    @TableField("status")
    private String status;
} 