.custom-checkbox {
  display: flex;
  align-items: center;
  padding: 12rpx 15rpx;
  background-color: transparent;
  border-radius: 8rpx;
  border: none;
  transition: all 0.3s;
  margin-bottom: 0;
}

.custom-checkbox.checked {
  background-color: transparent;
  border-color: transparent;
  transform: none;
  box-shadow: none;
}

.custom-checkbox.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.checkbox-inner {
  width: 32rpx;
  height: 32rpx;
  border-radius: 4rpx;
  border: 2rpx solid #c8c8c8;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  box-shadow: 0 0 1rpx rgba(0, 0, 0, 0.1) inset;
  transition: all 0.3s ease;
}

.custom-checkbox.checked .checkbox-inner {
  background-color: #1890ff;
  border-color: #1890ff;
  box-shadow: none;
}

.checkbox-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  animation: fadeIn 0.2s ease;
}

.checkbox-icon-empty {
  height: 100%;
  width: 100%;
  border-radius: 2rpx;
  background-color: #fff;
  transition: all 0.3s ease;
}

.checkbox-check {
  width: 16rpx;
  height: 22rpx;
  border-right: 3rpx solid #fff;
  border-bottom: 3rpx solid #fff;
  transform: rotate(45deg) translate(-1rpx, -3rpx);
  animation: checkmark 0.2s ease-in-out;
}

.checkbox-label {
  margin-left: 12rpx;
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.custom-checkbox.checked .checkbox-label {
  color: #1890ff;
  font-weight: 500;
}

@keyframes checkmark {
  0% {
    opacity: 0;
    transform: rotate(45deg) scale(0.5) translate(-1rpx, -3rpx);
  }
  100% {
    opacity: 1;
    transform: rotate(45deg) scale(1) translate(-1rpx, -3rpx);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
} 