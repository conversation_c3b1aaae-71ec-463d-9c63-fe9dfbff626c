package com.xiao.temu.modules.purchaseorderv.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 通知匹配订单VO
 */
@Data
public class NotificationMatchOrderVO {
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 备货单号
     */
    private String subPurchaseOrderSn;
    
    /**
     * 商品名称
     */
    private String productName;
    
    /**
     * 商品ID
     */
    private Long productId;
    
    /**
     * 商品SKC ID
     */
    private Long productSkcId;
    
    /**
     * 供应商ID
     */
    private Long supplierId;
    
    /**
     * 供应商名称
     */
    private String supplierName;
    
    /**
     * 备货单创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date purchaseTime;
    
    /**
     * 发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliverTime;
    
    /**
     * 预计最晚发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectLatestDeliverTime;
    
    /**
     * 预计最晚到货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectLatestArrivalTime;
    
    /**
     * 备货单状态
     */
    private Integer status;
    
    /**
     * 备货类型(0:普通备货 1:JIT备货)
     */
    private Integer purchaseStockType;
    
    /**
     * 匹配原因
     */
    private String matchReason;
} 