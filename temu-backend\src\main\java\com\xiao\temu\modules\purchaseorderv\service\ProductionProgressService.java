package com.xiao.temu.modules.purchaseorderv.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xiao.temu.modules.purchaseorderv.entity.ProductionProgress;
import com.xiao.temu.modules.purchaseorderv.dto.ProductionProgressDTO;
import com.xiao.temu.modules.purchaseorderv.dto.ScanUpdateProgressDTO;
import com.xiao.temu.modules.purchaseorderv.vo.ProductionProgressVO;
import com.xiao.temu.modules.purchaseorderv.vo.ProductionProgressLogVO;

import java.util.List;
import java.util.Map;

/**
 * 生产进度服务接口
 */
public interface ProductionProgressService extends IService<ProductionProgress> {

    /**
     * 初始化备货单的生产进度
     *
     * @param shopId 店铺ID
     * @param subPurchaseOrderSn 备货单号
     */
    void initProgress(Long shopId, String subPurchaseOrderSn);

    /**
     * 扫码更新生产进度
     *
     * @param dto 更新参数
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 是否更新成功
     */
    boolean updateProgress(ScanUpdateProgressDTO dto, Long operatorId, String operatorName);

    /**
     * 撤销生产进度
     *
     * @param dto 更新参数
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 是否撤销成功
     */
    boolean cancelProgress(ScanUpdateProgressDTO dto, Long operatorId, String operatorName);

    /**
     * 获取生产进度详情
     *
     * @param shopId 店铺ID
     * @param subPurchaseOrderSn 备货单号
     * @return 生产进度详情
     */
    ProductionProgressVO getProgressDetail(Long shopId, String subPurchaseOrderSn);

    /**
     * 分页查询生产进度
     *
     * @param dto 查询条件
     * @return 分页结果
     */
    IPage<ProductionProgressVO> getProgressPage(ProductionProgressDTO dto);

    /**
     * 获取生产进度日志
     *
     * @param shopId 店铺ID
     * @param subPurchaseOrderSn 备货单号
     * @return 生产进度日志列表
     */
    List<ProductionProgressLogVO> getProgressLogs(Long shopId, String subPurchaseOrderSn);
    
    /**
     * 获取用户操作日志
     *
     * @param userId 用户ID
     * @param limit 限制返回记录数
     * @return 用户操作日志列表
     */
    List<ProductionProgressLogVO> getUserOperationLogs(Long userId, Integer limit);

    /**
     * 批量获取生产进度详情
     *
     * @param orders 订单数据列表，每个元素包含shopId和subPurchaseOrderSn
     * @return 生产进度详情Map，key为"shopId:subPurchaseOrderSn"，value为进度详情
     */
    Map<String, ProductionProgressVO> batchGetProgressDetails(List<Map<String, Object>> orders);
} 