package com.xiao.temu.infrastructure.storage;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.region.Region;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.BufferedSink;
import okio.Okio;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.Date;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 腾讯云对象存储服务工具类
 */
@Slf4j
@Component
public class CosStorageService {

    @Value("${tencent.cos.secret-id}")
    private String secretId;

    @Value("${tencent.cos.secret-key}")
    private String secretKey;

    @Value("${tencent.cos.region}")
    private String region;

    @Value("${tencent.cos.bucket-name}")
    private String bucketName;

    // 存储路径前缀
    private static final String DEFECT_IMAGE_PREFIX = "defect-images/";
    // 临时目录
    private static final String TEMP_DIR = System.getProperty("java.io.tmpdir");
    
    // OkHttp客户端实例，使用单例模式提高性能
    private final OkHttpClient okHttpClient = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .build();

    /**
     * 创建COS客户端
     */
    private COSClient createCOSClient() {
        // 初始化用户身份信息
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        
        // 设置bucket的区域
        ClientConfig clientConfig = new ClientConfig(new Region(region));
        
        // 设置使用https协议
        clientConfig.setHttpProtocol(HttpProtocol.https);
        
        // 生成cos客户端
        return new COSClient(cred, clientConfig);
    }

    /**
     * 下载图片到本地临时目录 - 使用OkHttp3客户端提高性能
     * 
     * @param imageUrl 图片URL或包含URL的JSON字符串
     * @return 下载后的临时文件
     */
    public File downloadImage(String imageUrl) {
        ResponseBody responseBody = null;
        BufferedSink sink = null;
        File tmpFile = null;
        
        try {
            // 处理可能是JSON格式的URL
            String actualUrl = extractActualUrl(imageUrl);
            if (actualUrl == null || actualUrl.isEmpty()) {
                log.error("无效的图片URL: {}", imageUrl);
                return null;
            }
            
            // 创建临时文件
            String tmpFileName = UUID.randomUUID().toString() + ".jpg";
            tmpFile = new File(TEMP_DIR, tmpFileName);
            
            // 创建OkHttp请求
            Request request = new Request.Builder()
                    .url(actualUrl)
                    .get()
                    .build();
            
            // 执行请求并获取响应
            Response response = okHttpClient.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.error("下载图片失败，HTTP状态码: {}, URL: {}", response.code(), actualUrl);
                return null;
            }
            
            // 获取响应体
            responseBody = response.body();
            if (responseBody == null) {
                log.error("下载图片失败，响应体为空, URL: {}", actualUrl);
                return null;
            }
            
            // 直接使用Okio将响应内容写入文件，比传统IO更高效
            sink = Okio.buffer(Okio.sink(tmpFile));
            sink.writeAll(responseBody.source());
            sink.flush();
            
            log.info("图片下载成功: {}", tmpFile.getAbsolutePath());
            return tmpFile;
        } catch (Exception e) {
            log.error("下载图片失败: {}, error: {}", imageUrl, e.getMessage(), e);
            if (tmpFile != null && tmpFile.exists()) {
                tmpFile.delete();
            }
            return null;
        } finally {
            try {
                if (sink != null) sink.close();
                if (responseBody != null) responseBody.close();
            } catch (IOException e) {
                log.error("关闭资源失败", e);
            }
        }
    }

    /**
     * 从可能是JSON格式的字符串中提取实际URL
     * 
     * @param possibleJsonUrl 可能是JSON格式的URL字符串
     * @return 实际的URL
     */
    public String extractActualUrl(String possibleJsonUrl) {
        if (possibleJsonUrl == null || possibleJsonUrl.isEmpty()) {
            return null;
        }
        
        // 如果是普通URL（以http开头），直接返回
        if (possibleJsonUrl.startsWith("http")) {
            return possibleJsonUrl;
        }
        
        // 尝试解析为JSON
        try {
            if (possibleJsonUrl.startsWith("{") && possibleJsonUrl.endsWith("}")) {
                // 可能是一个JSON对象
                com.alibaba.fastjson2.JSONObject jsonObject = com.alibaba.fastjson2.JSON.parseObject(possibleJsonUrl);
                if (jsonObject.containsKey("url")) {
                    String url = jsonObject.getString("url");
                    // 递归处理，因为提取出的url可能也是JSON格式
                    return extractActualUrl(url);
                }
            }
        } catch (Exception e) {
            log.warn("解析JSON URL失败: {}, 尝试直接使用", possibleJsonUrl);
        }
        
        // 如果无法解析，返回原始值
        return possibleJsonUrl;
    }

    /**
     * 上传图片到腾讯云COS
     * 
     * @param localFile 本地文件
     * @param key 对象键（存储路径）
     * @return 上传结果（成功返回true，失败返回false）
     */
    public boolean uploadImage(File localFile, String key) {
        COSClient cosClient = null;
        try {
            cosClient = createCOSClient();
            
            // 指定文件将要存放的存储桶和对象键
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, localFile);
            PutObjectResult result = cosClient.putObject(putObjectRequest);
            
            log.info("图片上传成功: {}, ETag: {}", key, result.getETag());
            return true;
        } catch (Exception e) {
            log.error("上传图片到COS失败: {}, error: {}", key, e.getMessage(), e);
            return false;
        } finally {
            if (cosClient != null) {
                cosClient.shutdown();
            }
        }
    }

    /**
     * 生成图片访问URL
     * 
     * @param key 对象键（存储路径）
     * @param expiredHours URL有效时长（小时）
     * @return 图片访问URL
     */
    public String generateImageUrl(String key, int expiredHours) {
        COSClient cosClient = null;
        try {
            cosClient = createCOSClient();
            
            // 设置签名过期时间
            Date expirationDate = new Date(System.currentTimeMillis() + expiredHours * 3600 * 1000);
            
            // 生成预签名URL
            URL url = cosClient.generatePresignedUrl(bucketName, key, expirationDate, HttpMethodName.GET);
            return url.toString();
        } catch (CosClientException e) {
            log.error("生成图片访问URL失败: {}, error: {}", key, e.getMessage(), e);
            return null;
        } finally {
            if (cosClient != null) {
                cosClient.shutdown();
            }
        }
    }

    /**
     * 生成永久访问URL（不带签名）
     * 
     * @param key 对象键（存储路径）
     * @return 永久访问URL
     */
    public String generatePermanentUrl(String key) {
        return String.format("https://%s.cos.%s.myqcloud.com/%s", 
                bucketName, region, key);
    }

    /**
     * 生成疵点图片的存储路径
     * 
     * @param shopId 店铺ID
     * @param qcBillId 质检单ID
     * @param productSkuId 商品SKUID
     * @param fileName 文件名称（可选）
     * @return 存储路径
     */
    public String generateDefectImageKey(Long shopId, Long qcBillId, Long productSkuId, String fileName) {
        if (fileName == null) {
            fileName = UUID.randomUUID().toString() + ".jpg";
        }
        return DEFECT_IMAGE_PREFIX + shopId + "/" + qcBillId + "/" + productSkuId + "/" + fileName;
    }
} 