package com.xiao.temu.modules.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.system.entity.SysRole;

import java.util.List;
import java.util.Set;

/**
 * 角色服务接口
 */
public interface RoleService {

    /**
     * 根据角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色信息
     */
    SysRole getRoleById(Long roleId);

    /**
     * 查询角色列表
     *
     * @param role 角色查询条件
     * @param page 分页信息
     * @return 角色分页列表
     */
    IPage<SysRole> getRoleList(SysRole role, Page<SysRole> page);

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    List<SysRole> getAllRoles();

    /**
     * 查询用户角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRole> getRolesByUserId(Long userId);

    /**
     * 新增角色
     *
     * @param role 角色信息
     * @return 结果
     */
    int insertRole(SysRole role);

    /**
     * 修改角色
     *
     * @param role 角色信息
     * @return 结果
     */
    int updateRole(SysRole role);

    /**
     * 删除角色
     *
     * @param roleId 角色ID
     * @return 结果
     */
    int deleteRole(Long roleId);

    /**
     * 批量删除角色
     *
     * @param roleIds 角色ID数组
     * @return 结果
     */
    int deleteRoles(Long[] roleIds);

    /**
     * 修改角色状态
     *
     * @param roleId 角色ID
     * @param status 状态
     * @return 结果
     */
    int changeStatus(Long roleId, String status);

    /**
     * 检查角色名称是否唯一
     *
     * @param roleName 角色名称
     * @return 结果 true-唯一 false-不唯一
     */
    boolean checkRoleNameUnique(String roleName);

    /**
     * 检查角色权限标识是否唯一
     *
     * @param roleKey 角色权限标识
     * @return 结果 true-唯一 false-不唯一
     */
    boolean checkRoleKeyUnique(String roleKey);

    /**
     * 分配权限给角色
     *
     * @param roleId 角色ID
     * @param menuIds 菜单ID集合
     * @return 结果
     */
    int assignPermissions(Long roleId, Set<Long> menuIds);

    /**
     * 获取角色已分配的权限ID列表
     *
     * @param roleId 角色ID
     * @return 菜单ID集合
     */
    Set<Long> getRoleMenuIds(Long roleId);
} 