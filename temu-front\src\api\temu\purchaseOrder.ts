import request from '@/utils/request'

// 获取备货单列表数据
export function getPurchaseOrders(query) {
  // 过滤掉空值和空数组
  const filteredQuery = filterEmptyValues(query);
  
  return request({
    url: '/temu/purchaseOrder/list',
    method: 'post',
    data: filteredQuery
  })
}

// 获取用户可访问的店铺列表
export function getUserShops() {
  return request({
    url: '/temu/purchaseOrder/shops',
    method: 'get'
  }).catch(error => {
    console.error('获取店铺列表API错误')
    throw error
  })
}

// 导出备货单数据
export function exportPurchaseOrders(query) {
  // 过滤掉空值和空数组
  const filteredQuery = filterEmptyValues(query);
  
  return request({
    url: '/temu/purchaseOrder/export',
    method: 'post',
    data: filteredQuery,
    responseType: 'blob'
  })
}

// 获取导出任务进度
export function getExportProgress(taskId) {
  return request({
    url: `/temu/purchaseOrder/exportProgress/${taskId}`,
    method: 'get'
  })
}

// 下载导出的Excel文件
export function downloadExportFile(taskId) {
  return request({
    url: `/temu/purchaseOrder/downloadExcel/${taskId}`,
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 过滤掉对象中的空值、空数组和未定义的值
 * @param {Object} obj - 需要过滤的对象
 * @returns {Object} - 过滤后的对象
 */
function filterEmptyValues(obj) {
  if (!obj || typeof obj !== 'object') return obj;
  
  return Object.entries(obj).reduce((result, [key, value]) => {
    // 跳过undefined和null值
    if (value === undefined || value === null) return result;
    
    // 检查数组是否为空
    if (Array.isArray(value) && value.length === 0) return result;
    
    // 如果值是对象，递归过滤
    if (typeof value === 'object' && !Array.isArray(value)) {
      const filteredValue = filterEmptyValues(value);
      if (Object.keys(filteredValue).length > 0) {
        result[key] = filteredValue;
      }
      return result;
    }
    
    // 添加有效值
    result[key] = value;
    return result;
  }, {});
} 