package com.xiao.temu.modules.sync.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 销售数据同步任务实体类
 * 对应数据库表：sales_sync_task
 */
@Data
@TableName("sales_sync_task")
public class SalesSyncTask {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺名称（非数据库字段，需要查询关联）
     */
    @TableField(exist = false)
    private String shopName;

    /**
     * 店铺备注（非数据库字段，需要查询关联）
     */
    @TableField(exist = false)
    private String shopRemark;

    /**
     * 上次同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastSyncTime;

    /**
     * 数据最新更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;

    /**
     * 同步状态：0-未同步，1-同步中，2-同步成功，3-同步失败
     */
    private Integer syncStatus;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 总记录数（销售子订单总数）
     */
    private Integer totalRecords;
    
    /**
     * SKU总记录数
     */
    private Integer skuTotalRecords;
    
    /**
     * 仓库总记录数
     */
    private Integer warehouseTotalRecords;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 备注信息（非数据库字段，用于临时存储附加信息）
     */
    @TableField(exist = false)
    private String remark;
    
    /**
     * 上次同步开始时间（非数据库字段，用于临时存储）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private transient LocalDateTime lastSyncStartTime;
    
    /**
     * 上次同步结束时间（非数据库字段，用于临时存储）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private transient LocalDateTime lastSyncEndTime;
    
    /**
     * 最后一次消息（非数据库字段，用于临时存储）
     */
    private transient String lastMessage;
} 