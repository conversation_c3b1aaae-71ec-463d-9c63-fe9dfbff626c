<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>发送系统通知</span>
          <el-button type="primary" @click="showTemplateSelect">使用模板</el-button>
        </div>
      </template>
      
      <el-form :model="messageForm" :rules="rules" ref="messageFormRef" label-width="100px">
        <el-form-item label="消息标题" prop="title">
          <el-input v-model="messageForm.title" placeholder="请输入消息标题" maxlength="100" show-word-limit />
        </el-form-item>
        
        <el-form-item label="消息类型" prop="messageType">
          <el-radio-group v-model="messageForm.messageType">
            <el-radio :label="'1'">系统消息</el-radio>
            <el-radio :label="'2'">任务提醒</el-radio>
            <el-radio :label="'3'">店铺消息</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="重要程度" prop="importance">
          <el-radio-group v-model="messageForm.importance">
            <el-radio :label="'1'">普通</el-radio>
            <el-radio :label="'2'">重要</el-radio>
            <el-radio :label="'3'">紧急</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="接收对象" prop="targetType">
          <el-radio-group v-model="messageForm.targetType" @change="handleTargetTypeChange">
            <el-radio :label="'0'">全部用户</el-radio>
            <el-radio :label="'1'">指定用户</el-radio>
            <el-radio :label="'2'">指定角色</el-radio>
            <el-radio :label="'3'">运营组</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <!-- 根据接收对象类型显示不同的选择器 -->
        <el-form-item label="选择用户" prop="targetIds" v-if="messageForm.targetType === '1'">
          <el-select
            v-model="selectedUsers"
            multiple
            filterable
            placeholder="请选择用户"
            style="width: 100%"
            @change="handleSelectionChange"
          >
            <el-option
              v-for="item in userOptions"
              :key="item.userId"
              :label="item.nickName || item.username"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="选择角色" prop="targetIds" v-if="messageForm.targetType === '2'">
          <el-select
            v-model="selectedRoles"
            multiple
            filterable
            placeholder="请选择角色"
            style="width: 100%"
            @change="handleSelectionChange"
          >
            <el-option
              v-for="item in roleOptions"
              :key="item.roleId"
              :label="item.roleName"
              :value="item.roleId"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="选择运营组" prop="targetIds" v-if="messageForm.targetType === '3'">
          <el-select
            v-model="selectedGroups"
            multiple
            filterable
            placeholder="请选择运营组"
            style="width: 100%"
            @change="handleSelectionChange"
          >
            <el-option
              v-for="item in groupOptions"
              :key="item.groupId"
              :label="item.groupName"
              :value="item.groupId"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="相关店铺" prop="shopId" v-if="messageForm.messageType === '3'">
          <el-select
            v-model="messageForm.shopId"
            filterable
            placeholder="请选择店铺"
            style="width: 100%"
          >
            <el-option
              v-for="item in shopOptions"
              :key="item.shopId"
              :label="item.shopName"
              :value="item.shopId"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="消息内容" prop="content">
          <el-input
            v-model="messageForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入消息内容"
            maxlength="2000"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="定时发送" prop="publishTime">
          <el-switch v-model="enableSchedule" />
          <el-date-picker
            v-if="enableSchedule"
            v-model="messageForm.publishTime"
            type="datetime"
            placeholder="选择发布时间"
            style="width: 240px; margin-left: 10px;"
            :disabled-date="disabledDate"
            :disabled-time="disabledTime"
          />
        </el-form-item>
        
        <el-form-item label="消息过期" prop="expireTime">
          <el-switch v-model="enableExpire" />
          <el-date-picker
            v-if="enableExpire"
            v-model="messageForm.expireTime"
            type="datetime"
            placeholder="选择过期时间"
            style="width: 240px; margin-left: 10px;"
            :disabled-date="disabledDate"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm(messageFormRef)" :loading="loading">发送消息</el-button>
          <el-button @click="resetForm(messageFormRef)">重置</el-button>
          <el-button @click="previewMessage">预览</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 消息预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="消息预览"
      width="500px"
      append-to-body
    >
      <div class="message-preview">
        <div class="message-preview-header">
          <h3>{{ messageForm.title }}</h3>
          <el-tag 
            v-if="messageForm.importance === '3'" 
            size="small" 
            type="danger"
          >紧急</el-tag>
          <el-tag 
            v-else-if="messageForm.importance === '2'" 
            size="small" 
            type="warning"
          >重要</el-tag>
        </div>
        
        <div class="message-preview-content">
          {{ messageForm.content }}
        </div>
        
        <div class="message-preview-footer">
          <div>
            发送时间: {{ enableSchedule ? formatDatetime(messageForm.publishTime) : '立即发送' }}
          </div>
          <div v-if="enableExpire">
            过期时间: {{ formatDatetime(messageForm.expireTime) }}
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="previewVisible = false">关闭</el-button>
          <el-button type="primary" @click="submitForm(messageFormRef)">
            确认发送
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 模板选择对话框 -->
    <el-dialog
      v-model="templateDialogVisible"
      title="选择消息模板"
      width="600px"
      append-to-body
    >
      <el-form :inline="true" class="template-search-form">
        <el-form-item label="模板类型">
          <el-select v-model="templateQuery.templateType" placeholder="选择模板类型" clearable>
            <el-option label="系统消息" value="1" />
            <el-option label="任务提醒" value="2" />
            <el-option label="店铺消息" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchTemplates">查询</el-button>
        </el-form-item>
      </el-form>
      
      <el-table
        v-loading="templateLoading"
        :data="templateList"
        border
        style="width: 100%"
        @row-click="handleTemplateSelect"
      >
        <el-table-column type="index" width="50" label="#" />
        <el-table-column prop="templateName" label="模板名称" min-width="120" show-overflow-tooltip />
        <el-table-column prop="templateCode" label="模板编码" min-width="120" show-overflow-tooltip />
        <el-table-column label="模板类型" align="center" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.templateType === '1'" type="primary">系统</el-tag>
            <el-tag v-else-if="scope.row.templateType === '2'" type="success">任务</el-tag>
            <el-tag v-else-if="scope.row.templateType === '3'" type="warning">店铺</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button type="primary" link @click.stop="useTemplate(scope.row)">使用</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="templateQuery.pageNum"
          v-model:page-size="templateQuery.pageSize"
          :page-sizes="[5, 10, 20]"
          layout="total, sizes, prev, pager, next"
          :total="templateTotal"
          @size-change="handleTemplatePageSizeChange"
          @current-change="handleTemplatePageChange"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { sendMessage, getTemplateList, getTemplateByCode } from '@/api/message'
import { formatTime as formatDatetime } from '@/utils/format'
import { MessageType, TargetType, ImportanceLevel } from '@/types/message'
import type { MessageTemplate } from '@/types/message'

// 表单引用
const messageFormRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)

// 预览对话框
const previewVisible = ref(false)

// 启用定时和过期
const enableSchedule = ref(false)
const enableExpire = ref(false)

// 选择的用户、角色、组
const selectedUsers = ref<number[]>([])
const selectedRoles = ref<number[]>([])
const selectedGroups = ref<number[]>([])

// 数据选项
const userOptions = ref<any[]>([])
const roleOptions = ref<any[]>([])
const groupOptions = ref<any[]>([])
const shopOptions = ref<any[]>([])

// 表单数据
const messageForm = reactive({
  title: '',
  content: '',
  messageType: '1' as MessageType | string, // 默认系统消息
  targetType: '0' as TargetType | string,   // 默认全部用户
  targetIds: '',
  shopId: undefined as number | undefined,
  importance: '1' as ImportanceLevel | string, // 默认普通
  publishTime: '' as string,
  expireTime: '' as string
})

// 表单验证规则
const rules = reactive<FormRules>({
  title: [
    { required: true, message: '请输入消息标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入消息内容', trigger: 'blur' },
    { min: 2, max: 2000, message: '内容长度在 2 到 2000 个字符', trigger: 'blur' }
  ],
  messageType: [
    { required: true, message: '请选择消息类型', trigger: 'change' }
  ],
  targetType: [
    { required: true, message: '请选择接收对象类型', trigger: 'change' }
  ],
  targetIds: [
    { 
      required: true, 
      message: '请选择接收对象', 
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (messageForm.targetType !== '0' && (!messageForm.targetIds || messageForm.targetIds === '')) {
          callback(new Error('请选择接收对象'))
        } else {
          callback()
        }
      }
    }
  ],
  shopId: [
    {
      required: true,
      message: '请选择相关店铺',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (messageForm.messageType === '3' && !messageForm.shopId) {
          callback(new Error('请选择相关店铺'))
        } else {
          callback()
        }
      }
    }
  ]
})

// 模板相关
const templateDialogVisible = ref(false)
const templateLoading = ref(false)
const templateList = ref<MessageTemplate[]>([])
const templateTotal = ref(0)
const templateQuery = reactive({
  pageNum: 1,
  pageSize: 10,
  templateType: ''
})

// 加载用户列表
const loadUsers = async () => {
  try {
    // 实际项目中应该调用API获取用户列表
    // 这里为演示使用模拟数据
    userOptions.value = [
      { userId: 1, username: 'admin', nickName: '系统管理员' },
      { userId: 2, username: 'test', nickName: '测试用户' },
      { userId: 5, username: '组长1', nickName: '组长1' },
      { userId: 6, username: '运营2', nickName: '运营2' },
      { userId: 7, username: '组长2', nickName: '组长2' },
      { userId: 8, username: '运营1', nickName: '运营1' }
    ]
  } catch (error) {
    console.error('加载用户列表失败', error)
  }
}

// 加载角色列表
const loadRoles = async () => {
  try {
    // 实际项目中应该调用API获取角色列表
    // 这里为演示使用模拟数据
    roleOptions.value = [
      { roleId: 1, roleName: '超级管理员' },
      { roleId: 2, roleName: '普通用户' },
      { roleId: 3, roleName: '运营人员' },
      { roleId: 6, roleName: '运营组长' }
    ]
  } catch (error) {
    console.error('加载角色列表失败', error)
  }
}

// 加载运营组列表
const loadGroups = async () => {
  try {
    // 实际项目中应该调用API获取运营组列表
    // 这里为演示使用模拟数据
    groupOptions.value = [
      { groupId: 1, groupName: '运营组2' },
      { groupId: 4, groupName: '运营组1' }
    ]
  } catch (error) {
    console.error('加载运营组列表失败', error)
  }
}

// 加载店铺列表
const loadShops = async () => {
  try {
    // 实际项目中应该调用API获取店铺列表
    // 这里为演示使用模拟数据
    shopOptions.value = [
      { shopId: 1, shopName: '测试店铺1' },
      { shopId: 2, shopName: '测试店铺2' },
      { shopId: 3, shopName: '测试店铺3' },
      { shopId: 4, shopName: '测试店铺4' }
    ]
  } catch (error) {
    console.error('加载店铺列表失败', error)
  }
}

// 处理目标类型变更
const handleTargetTypeChange = () => {
  // 清空已选对象
  selectedUsers.value = []
  selectedRoles.value = []
  selectedGroups.value = []
  messageForm.targetIds = ''
}

// 处理选择变更
const handleSelectionChange = () => {
  switch (messageForm.targetType) {
    case '1': // 用户
      messageForm.targetIds = selectedUsers.value.join(',')
      break
    case '2': // 角色
      messageForm.targetIds = selectedRoles.value.join(',')
      break
    case '3': // 运营组
      messageForm.targetIds = selectedGroups.value.join(',')
      break
    default:
      messageForm.targetIds = ''
  }
}

// 禁用的日期（今天之前的日期）
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7 // 禁用今天之前的日期
}

// 禁用的时间（当天的已过时间）
const disabledTime = (date: Date) => {
  const now = new Date()
  
  if (date.getDate() === now.getDate() &&
      date.getMonth() === now.getMonth() &&
      date.getFullYear() === now.getFullYear()) {
    return {
      hours: Array.from({ length: now.getHours() }, (_, i) => i),
      minutes: date.getHours() === now.getHours() 
        ? Array.from({ length: now.getMinutes() }, (_, i) => i) 
        : []
    }
  }
  
  return {
    hours: [],
    minutes: []
  }
}

// 预览消息
const previewMessage = async () => {
  // 验证表单
  if (!messageFormRef.value) return
  
  await messageFormRef.value.validate((valid) => {
    if (valid) {
      previewVisible.value = true
    }
  })
}

// 提交表单
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  
  await formEl.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true
        
        // 处理时间
        const formData = { ...messageForm }
        
        if (!enableSchedule.value) {
          formData.publishTime = '' // 不启用定时发送，清空时间
        } else {
          // 确保时间是字符串格式
          formData.publishTime = typeof formData.publishTime === 'string' 
            ? formData.publishTime 
            : formatDatetime(formData.publishTime as any)
        }
        
        if (!enableExpire.value) {
          formData.expireTime = '' // 不启用过期时间，清空时间
        } else {
          // 确保时间是字符串格式
          formData.expireTime = typeof formData.expireTime === 'string'
            ? formData.expireTime
            : formatDatetime(formData.expireTime as any)
        }
        
        // 发送消息
        const response = await sendMessage(formData)
        
        if (response && response.data) {
          ElMessage.success('消息发送成功')
          // 重置表单
          resetForm(formEl)
          // 关闭预览
          previewVisible.value = false
        } else {
          ElMessage.error('发送失败')
        }
      } catch (error: any) {
        ElMessage.error(error.message || '发送失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 重置表单
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  
  // 重置其他状态
  enableSchedule.value = false
  enableExpire.value = false
  selectedUsers.value = []
  selectedRoles.value = []
  selectedGroups.value = []
  messageForm.targetIds = ''
  messageForm.shopId = undefined
}

// 显示模板选择对话框
const showTemplateSelect = () => {
  templateDialogVisible.value = true
  searchTemplates()
}

// 查询模板列表
const searchTemplates = async () => {
  templateLoading.value = true
  try {
    const response = await getTemplateList(templateQuery)
    if (response && response.data) {
      templateList.value = response.data.rows || []
      templateTotal.value = response.data.total || 0
    }
  } catch (error) {
    console.error('获取模板列表失败', error)
  } finally {
    templateLoading.value = false
  }
}

// 处理模板页码变化
const handleTemplatePageSizeChange = (size: number) => {
  templateQuery.pageSize = size
  searchTemplates()
}

const handleTemplatePageChange = (page: number) => {
  templateQuery.pageNum = page
  searchTemplates()
}

// 选择模板行
const handleTemplateSelect = (row: MessageTemplate) => {
  // 点击行选中模板
  useTemplate(row)
}

// 使用模板
const useTemplate = async (template: MessageTemplate) => {
  try {
    // 获取完整模板详情
    const response = await getTemplateByCode(template.templateCode)
    if (response && response.data) {
      const templateData = response.data
      
      // 更新消息表单
      messageForm.messageType = templateData.templateType
      messageForm.title = templateData.titleTemplate.replace(/\${([^}]+)}/g, '')
      messageForm.content = templateData.contentTemplate.replace(/\${([^}]+)}/g, '')
      
      // 提示用户进行变量替换
      ElMessage.success('模板已应用，请填写相应变量内容')
      templateDialogVisible.value = false
    }
  } catch (error) {
    console.error('获取模板详情失败', error)
    ElMessage.error('获取模板详情失败')
  }
}

// 组件挂载时加载选项数据
onMounted(() => {
  loadUsers()
  loadRoles()
  loadGroups()
  loadShops()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-preview {
  padding: 16px;
  border-radius: 4px;
  background: #f5f7fa;
}

.message-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.message-preview-header h3 {
  margin: 0;
  font-size: 18px;
}

.message-preview-content {
  padding: 10px 0;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
}

.message-preview-footer {
  margin-top: 12px;
  font-size: 12px;
  color: #909399;
  display: flex;
  justify-content: space-between;
}

.template-search-form {
  margin-bottom: 15px;
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
}
</style> 