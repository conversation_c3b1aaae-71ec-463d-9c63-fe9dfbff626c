/**
 * 退货明细请求参数
 */
export interface RefundRequestDTO {
  /**
   * 店铺ID
   */
  shopId?: number;
  
  /**
   * 出库开始时间
   * 前端显示为YYYY-MM-DD格式，传给后端转为毫秒时间戳字符串
   */
  outboundTimeStart?: string;
  
  /**
   * 出库结束时间
   * 前端显示为YYYY-MM-DD格式，传给后端转为毫秒时间戳字符串
   */
  outboundTimeEnd?: string;
  
  /**
   * 页码
   */
  pageNo?: string | number;
  
  /**
   * 每页条数
   */
  pageSize?: string | number;

  /**
   * SKU列表
   */
  productSkuIdList?: number[];

  /**
   * 退货包裹号列表
   */
  returnSupplierPackageNos?: string[];

  /**
   * 备货单号列表
   */
  purchaseSubOrderSns?: string[];
}

/**
 * 店铺信息
 */
export interface Shop {
  /**
   * 店铺ID
   */
  shopId: number;
  
  /**
   * 店铺名称
   */
  shopName: string;
  
  /**
   * 店铺Temu ID
   */
  shopTemuId?: string;
  
  /**
   * 店铺状态
   */
  status?: string;
  
  /**
   * 备注
   */
  remark?: string;
}

/**
 * 退货包裹信息
 */
export interface RefundPackage {
  [key: string]: any;
}

/**
 * 退货明细返回结果
 */
export interface RefundPackageResult {
  /**
   * 请求是否成功
   */
  success: boolean;
  
  /**
   * 错误码
   */
  errorCode?: number;
  
  /**
   * 错误信息
   */
  errorMsg?: string;
  
  /**
   * 返回结果
   */
  result?: any;
  
  /**
   * 店铺名称
   */
  shopName?: string;
  
  /**
   * 店铺ID
   */
  shopId?: number;
} 