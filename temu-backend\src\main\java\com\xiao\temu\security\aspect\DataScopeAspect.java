package com.xiao.temu.security.aspect;

import com.xiao.temu.modules.operation.entity.OperationGroup;
import com.xiao.temu.security.annotation.DataScope;
import com.xiao.temu.security.utils.SecurityUtils;
import com.xiao.temu.modules.operation.service.OperationGroupService;
import com.xiao.temu.modules.system.service.SysDataPermissionService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.List;

/**
 * 数据权限切面处理
 */
@Slf4j
@Aspect
@Component
public class DataScopeAspect {

    @Autowired
    private OperationGroupService groupService;
    
    @Autowired
    private SysDataPermissionService dataPermissionService;

    /**
     * 数据权限过滤关键字
     */
    public static final String DATA_SCOPE = "data_scope";

    /**
     * 切入点处理
     */
    @Before("@annotation(com.xiao.temu.security.annotation.DataScope)")
    public void doBefore(JoinPoint point) {
        handleDataScope(point);
    }

    /**
     * 处理数据权限
     */
    protected void handleDataScope(JoinPoint joinPoint) {
        // 获取当前的用户ID
        Long userId = SecurityUtils.getCurrentUserId();
        if (userId == null) {
            log.warn("未获取到用户ID，跳过数据权限过滤");
            return;
        }
        
        // 如果是管理员，不进行数据过滤
        boolean isAdmin = SecurityUtils.isAdmin();
        if (isAdmin) {
            log.info("用户 [ID:{}] 是管理员，跳过数据权限过滤", userId);
            return;
        }

        // 获取方法上的注解
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        DataScope dataScope = method.getAnnotation(DataScope.class);
        if (dataScope == null) {
            log.debug("方法 [{}] 未标记@DataScope注解，跳过数据权限过滤", method.getName());
            return;
        }
        
        // 获取用户的最高数据权限
        String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
        log.info("用户 [ID:{}] 的最高数据权限类型: {}", userId, permissionType);
        
        // 如果有全部数据权限，不进行过滤
        if ("2".equals(permissionType)) {
            log.info("用户 [ID:{}] 拥有全部数据权限，跳过数据权限过滤", userId);
            return;
        }

        // 生成数据权限SQL
        String sqlFilter = generateDataScopeSql(dataScope, userId, permissionType);
        log.info("用户 [ID:{}] 生成的数据权限SQL: {}", userId, sqlFilter);
        
        // 获取参数并设置数据权限SQL
        Object[] args = joinPoint.getArgs();
        boolean sqlApplied = false;
        for (Object arg : args) {
            if (arg != null && arg instanceof Object) {
                try {
                    // 通过反射获取并设置sql过滤条件
                    Class<?> argClass = arg.getClass();
                    java.lang.reflect.Field field = argClass.getDeclaredField(DATA_SCOPE);
                    field.setAccessible(true);
                    field.set(arg, sqlFilter);
                    log.info("已将数据权限SQL设置到参数: {}", arg.getClass().getSimpleName());
                    sqlApplied = true;
                } catch (Exception e) {
                    // 找不到数据权限字段，忽略处理
                    log.debug("参数 {} 不支持数据权限过滤: {}", arg.getClass().getSimpleName(), e.getMessage());
                }
            }
        }
        
        if (!sqlApplied) {
            log.warn("未找到支持数据权限的参数，SQL过滤未应用");
        }
    }

    /**
     * 生成数据权限SQL
     */
    private String generateDataScopeSql(DataScope dataScope, Long userId, String permissionType) {
        StringBuilder sqlBuilder = new StringBuilder();
        
        // 初始条件，避免以OR开头导致语法错误
        sqlBuilder.append("1=0");
        
        // 获取表别名
        String tableAlias = dataScope.tableAlias();
        String columnPrefix;
        if (tableAlias != null && !tableAlias.isEmpty()) {
            columnPrefix = tableAlias + ".";
        } else {
            columnPrefix = "";
        }
        
        // 根据权限类型生成SQL
        if ("0".equals(permissionType)) {
            // 本人数据权限 - 只能查看自己直接分配的数据
            sqlBuilder.append(" OR EXISTS (SELECT 1 FROM group_leader_shop_assignment a WHERE a.user_id = ")
                    .append(userId)
                    .append(" AND a.status = '0'");
            
            // 关联店铺ID
            sqlBuilder.append(" AND a.shop_id = ").append(columnPrefix).append(dataScope.userIdColumn());
            
            sqlBuilder.append(") ");
        } else if ("1".equals(permissionType)) {
            // 本组数据权限
            
            // 用户是组长的情况
            List<OperationGroup> groups = groupService.getGroupsByLeaderId(userId);
            if (!groups.isEmpty() && dataScope.includeGroupData()) {
                for (OperationGroup group : groups) {
                    // 组长只能查看与自己运营组关联的店铺
                    sqlBuilder.append(" OR EXISTS (SELECT 1 FROM shop_group_assignment sga WHERE sga.group_id = ")
                            .append(group.getGroupId())
                            .append(" AND sga.status = '0'");
                    
                    // 关联店铺ID
                    sqlBuilder.append(" AND sga.shop_id = ").append(columnPrefix).append(dataScope.userIdColumn());
                    
                    sqlBuilder.append(") ");
                }
            }
            
            // 用户是组员的情况
            List<Long> memberGroupIds = groupService.getGroupIdsByMemberId(userId);
            if (!memberGroupIds.isEmpty()) {
                sqlBuilder.append(" OR EXISTS (SELECT 1 FROM shop_group_assignment sga WHERE sga.group_id IN (");
                
                for (int i = 0; i < memberGroupIds.size(); i++) {
                    if (i > 0) {
                        sqlBuilder.append(",");
                    }
                    sqlBuilder.append(memberGroupIds.get(i));
                }
                
                sqlBuilder.append(") AND sga.status = '0'");
                
                // 关联店铺ID
                sqlBuilder.append(" AND sga.shop_id = ").append(columnPrefix).append(dataScope.userIdColumn());
                
                sqlBuilder.append(") ");
            }
            
            // 用户被分配的店铺数据
            sqlBuilder.append(" OR EXISTS (SELECT 1 FROM group_leader_shop_assignment a WHERE a.user_id = ")
                    .append(userId)
                    .append(" AND a.status = '0'");
            
            // 关联店铺ID
            sqlBuilder.append(" AND a.shop_id = ").append(columnPrefix).append(dataScope.userIdColumn());
            
            sqlBuilder.append(") ");
        }
        
        return "(" + sqlBuilder.toString() + ")";
    }
} 