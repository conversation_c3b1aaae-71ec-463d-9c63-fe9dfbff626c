package com.xiao.temu.modules.operation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.infrastructure.exporter.dto.ExportRequestDTO;
import com.xiao.temu.modules.operation.dto.GroupMemberDTO;
import com.xiao.temu.modules.operation.dto.GroupStatisticsDTO;
import com.xiao.temu.modules.operation.dto.OperationGroupDTO;
import com.xiao.temu.modules.operation.dto.QueryGroupDTO;
import com.xiao.temu.modules.operation.entity.GroupLeaderShopAssignment;
import com.xiao.temu.modules.operation.entity.GroupMember;
import com.xiao.temu.modules.operation.entity.OperationGroup;
import com.xiao.temu.modules.shop.dto.ShopWithGroupsDTO;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.shop.entity.ShopGroupAssignment;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.operation.mapper.GroupMemberMapper;
import com.xiao.temu.modules.operation.mapper.GroupLeaderShopAssignmentMapper;
import com.xiao.temu.modules.operation.mapper.OperationGroupMapper;
import com.xiao.temu.modules.shop.mapper.ShopGroupAssignmentMapper;
import com.xiao.temu.modules.shop.mapper.ShopMapper;
import com.xiao.temu.modules.system.mapper.SysUserMapper;
import com.xiao.temu.modules.system.mapper.SysRoleMapper;
import com.xiao.temu.modules.system.service.SysUserService;
import com.xiao.temu.modules.operation.service.GroupMemberService;
import com.xiao.temu.modules.operation.service.OperationGroupService;
import com.xiao.temu.security.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 运营组服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class    OperationGroupServiceImpl implements OperationGroupService {

    private final OperationGroupMapper operationGroupMapper;
    private final GroupMemberMapper groupMemberMapper;
    private final SysUserMapper userMapper;
    private final GroupMemberService groupMemberService;
    private final ShopMapper shopMapper;
    private final ShopGroupAssignmentMapper shopGroupAssignmentMapper;
    private final GroupLeaderShopAssignmentMapper groupLeaderShopAssignmentMapper;
    private final SysRoleMapper roleMapper;
    private final SysUserService userService;

    @Override
    public IPage<OperationGroupDTO> getGroupList(QueryGroupDTO queryDTO) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getCurrentUserId();
        
        // 判断当前用户是否为管理员
        boolean isAdmin = SecurityUtils.isAdmin();
        
        // 如果不是管理员，则只能查看自己负责的运营组
        if (!isAdmin) {
            // 设置查询条件为当前用户ID，这样SQL查询时会添加 g.leader_id = #{query.leaderId} 条件
            // 确保非管理员用户只能看到自己作为负责人的运营组数据
            queryDTO.setLeaderId(currentUserId);
            log.debug("非管理员用户 [{}] 只能查看自己负责的运营组", currentUserId);
        } else {
            log.debug("管理员用户 [{}] 可以查看所有运营组", currentUserId);
        }
        
        Page<OperationGroupDTO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        return operationGroupMapper.selectGroupList(page, queryDTO);
    }

    @Override
    public OperationGroupDTO getGroupById(Long groupId) {
        return operationGroupMapper.selectGroupById(groupId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertGroup(OperationGroupDTO groupDTO) {
        // 校验运营组名称是否唯一
        if (!checkGroupNameUnique(groupDTO.getGroupName(), null)) {
            throw new RuntimeException("新增运营组'" + groupDTO.getGroupName() + "'失败，运营组名称已存在");
        }
        
        // 校验负责人是否存在
        SysUser leader = userMapper.selectById(groupDTO.getLeaderId());
        if (leader == null) {
            throw new RuntimeException("新增运营组失败，负责人不存在");
        }
        
        // 构建运营组对象
        OperationGroup group = new OperationGroup();
        BeanUtils.copyProperties(groupDTO, group);
        group.setCreateTime(new Date());
        group.setStatus("0"); // 默认正常状态
        
        // 插入运营组
        int rows = operationGroupMapper.insert(group);
        
        // 将负责人添加为运营组成员
        if (rows > 0) {
            GroupMember member = new GroupMember();
            member.setGroupId(group.getGroupId());
            member.setUserId(group.getLeaderId());
            member.setJoinTime(new Date());
            member.setStatus("0");
            groupMemberMapper.insert(member);
        }
        
        return rows;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateGroup(OperationGroupDTO groupDTO) {
        // 校验运营组是否存在
        OperationGroup existingGroup = operationGroupMapper.selectById(groupDTO.getGroupId());
        if (existingGroup == null) {
            throw new RuntimeException("修改运营组失败，运营组不存在");
        }
        
        // 校验运营组名称是否唯一
        if (!checkGroupNameUnique(groupDTO.getGroupName(), groupDTO.getGroupId())) {
            throw new RuntimeException("修改运营组'" + groupDTO.getGroupName() + "'失败，运营组名称已存在");
        }
        
        // 校验负责人是否存在
        Long newLeaderId = groupDTO.getLeaderId();
        if (!existingGroup.getLeaderId().equals(newLeaderId)) {
            SysUser leader = userMapper.selectById(newLeaderId);
            if (leader == null) {
                throw new RuntimeException("修改运营组失败，新负责人不存在");
            }
            
            // 如果负责人变更，确保新负责人是运营组成员
            if (!groupMemberService.checkUserInGroup(groupDTO.getGroupId(), newLeaderId)) {
                // 将新负责人添加为成员
                groupMemberService.addMember(groupDTO.getGroupId(), newLeaderId);
            }
        }
        
        // 构建运营组对象
        OperationGroup group = new OperationGroup();
        BeanUtils.copyProperties(groupDTO, group);
        group.setUpdateTime(new Date());
        
        // 更新运营组
        return operationGroupMapper.updateById(group);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteGroup(Long groupId) {
        // 获取运营组信息
        OperationGroup group = operationGroupMapper.selectById(groupId);
        if (group == null) {
            throw new RuntimeException("删除失败，运营组不存在");
        }
        
        // 获取运营组成员列表
        List<GroupMemberDTO> members = groupMemberService.getAllMembers(groupId);
        
        // 排除组长，只检查普通运营人员
        long normalOperatorCount = members.stream()
                .filter(member -> !member.getUserId().equals(group.getLeaderId()) && "0".equals(member.getStatus()))
                .count();
            
        if (normalOperatorCount > 0) {
            throw new RuntimeException("删除失败，运营组下还有普通运营人员，请先移除所有非负责人成员");
        }
        
        // 删除组长店铺分配关系
        LambdaQueryWrapper<GroupLeaderShopAssignment> leaderAssignmentWrapper = new LambdaQueryWrapper<>();
        leaderAssignmentWrapper.eq(GroupLeaderShopAssignment::getGroupId, groupId);
        groupLeaderShopAssignmentMapper.delete(leaderAssignmentWrapper);
        
        // 删除店铺与运营组的关联
        LambdaQueryWrapper<ShopGroupAssignment> assignmentWrapper = new LambdaQueryWrapper<>();
        assignmentWrapper.eq(ShopGroupAssignment::getGroupId, groupId);
        shopGroupAssignmentMapper.delete(assignmentWrapper);
        
        // 删除所有成员（包括组长）
        groupMemberService.removeAllMembers(groupId);
        
        // 删除运营组
        return operationGroupMapper.deleteById(groupId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteGroups(Long[] groupIds) {
        int successCount = 0;
        
        for (Long groupId : groupIds) {
            // 获取运营组信息
            OperationGroup group = operationGroupMapper.selectById(groupId);
            if (group == null) {
                throw new RuntimeException("删除失败，运营组不存在");
            }
            
            // 获取运营组成员列表
            List<GroupMemberDTO> members = groupMemberService.getAllMembers(groupId);
            
            // 排除组长，只检查普通运营人员
            long normalOperatorCount = members.stream()
                    .filter(member -> !member.getUserId().equals(group.getLeaderId()) && "0".equals(member.getStatus()))
                    .count();
                    
            if (normalOperatorCount > 0) {
                throw new RuntimeException("删除失败，运营组下还有普通运营人员，请先移除所有非负责人成员");
            }
            
            // 删除组长店铺分配关系
            LambdaQueryWrapper<GroupLeaderShopAssignment> leaderAssignmentWrapper = new LambdaQueryWrapper<>();
            leaderAssignmentWrapper.eq(GroupLeaderShopAssignment::getGroupId, groupId);
            groupLeaderShopAssignmentMapper.delete(leaderAssignmentWrapper);
            
            // 删除店铺与运营组的关联
            LambdaQueryWrapper<ShopGroupAssignment> assignmentWrapper = new LambdaQueryWrapper<>();
            assignmentWrapper.eq(ShopGroupAssignment::getGroupId, groupId);
            shopGroupAssignmentMapper.delete(assignmentWrapper);
            
            // 删除所有成员（包括组长）
            groupMemberService.removeAllMembers(groupId);
            
            successCount++;
        }
        
        // 批量删除运营组
        return operationGroupMapper.deleteBatchIds(Arrays.asList(groupIds));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int changeStatus(Long groupId, String status) {
        // 校验运营组是否存在
        OperationGroup group = operationGroupMapper.selectById(groupId);
        if (group == null) {
            throw new RuntimeException("修改运营组状态失败，运营组不存在");
        }
        
        // 更新状态
        return operationGroupMapper.updateStatus(groupId, status);
    }

    @Override
    public boolean checkGroupNameUnique(String groupName, Long groupId) {
        return operationGroupMapper.checkGroupNameUnique(groupName, groupId) == 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int setGroupLeader(Long groupId, Long leaderId) {
        // 校验运营组是否存在
        OperationGroup group = operationGroupMapper.selectById(groupId);
        if (group == null) {
            throw new RuntimeException("设置负责人失败，运营组不存在");
        }
        
        // 校验用户是否存在
        SysUser user = userMapper.selectById(leaderId);
        if (user == null) {
            throw new RuntimeException("设置负责人失败，用户不存在");
        }
        
        // 如果负责人未变更，直接返回成功
        if (group.getLeaderId().equals(leaderId)) {
            return 1;
        }
        
        // 确保新负责人是运营组成员
        if (!groupMemberService.checkUserInGroup(groupId, leaderId)) {
            // 将新负责人添加为成员
            groupMemberService.addMember(groupId, leaderId);
        }
        
        // 更新运营组负责人
        OperationGroup updateGroup = new OperationGroup();
        updateGroup.setGroupId(groupId);
        updateGroup.setLeaderId(leaderId);
        updateGroup.setUpdateTime(new Date());
        
        return operationGroupMapper.updateById(updateGroup);
    }

    @Override
    public List<OperationGroup> getGroupsByLeaderId(Long userId) {
        return operationGroupMapper.selectGroupsByLeaderId(userId);
    }

    @Override
    public List<OperationGroupDTO> getGroupsByMemberId(Long userId) {
        return operationGroupMapper.selectGroupsByMemberId(userId);
    }

    @Override
    public GroupStatisticsDTO getGroupStatistics(Long groupId) {
        // 校验运营组是否存在
        OperationGroup group = operationGroupMapper.selectById(groupId);
        if (group == null) {
            throw new RuntimeException("获取统计数据失败，运营组不存在");
        }
        
        // 构建统计数据对象
        GroupStatisticsDTO statistics = new GroupStatisticsDTO();
        statistics.setGroupId(groupId);
        statistics.setGroupName(group.getGroupName());
        
        // 统计成员数量
        statistics.setMemberCount(groupMemberService.countGroupMembers(groupId));
        
        // 统计关联店铺数量 - 修改为使用实际分配的店铺列表
        List<Shop> groupShops = shopMapper.selectShopsByGroupId(groupId);
        statistics.setShopCount(groupShops != null ? groupShops.size() : 0);
        
        // TODO: 统计任务数量，这部分需要依赖其他模块
        statistics.setOngoingTaskCount(0);
        statistics.setCompletedTaskCount(0);
        
        return statistics;
    }

    @Override
    public List<GroupStatisticsDTO> getGroupsStatistics(List<Long> groupIds) {
        List<GroupStatisticsDTO> statistics = new ArrayList<>();
        for (Long groupId : groupIds) {
            statistics.add(getGroupStatistics(groupId));
        }
        return statistics;
    }

    @Override
    public List<Long> getGroupIdsByLeaderId(Long userId) {
        LambdaQueryWrapper<OperationGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OperationGroup::getLeaderId, userId)
               .eq(OperationGroup::getStatus, "0")
               .select(OperationGroup::getGroupId);
        
        List<OperationGroup> groups = operationGroupMapper.selectList(wrapper);
        List<Long> groupIds = new ArrayList<>();
        
        for (OperationGroup group : groups) {
            groupIds.add(group.getGroupId());
        }
        
        return groupIds;
    }
    
    @Override
    public boolean isUserGroupLeader(Long userId) {
        LambdaQueryWrapper<OperationGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OperationGroup::getLeaderId, userId)
               .eq(OperationGroup::getStatus, "0");
        
        return operationGroupMapper.selectCount(wrapper) > 0;
    }
    
    @Override
    public boolean checkUserInGroup(Long userId, Long groupId) {
        // 先检查是否为组长
        LambdaQueryWrapper<OperationGroup> leaderWrapper = new LambdaQueryWrapper<>();
        leaderWrapper.eq(OperationGroup::getGroupId, groupId)
                    .eq(OperationGroup::getLeaderId, userId)
                    .eq(OperationGroup::getStatus, "0");
        
        if (operationGroupMapper.selectCount(leaderWrapper) > 0) {
            return true;
        }
        
        // 检查是否为组成员
        return groupMemberService.checkUserInGroup(groupId, userId);
    }
    
    @Override
    public boolean existsById(Long groupId) {
        return operationGroupMapper.selectById(groupId) != null;
    }
    
    @Override
    public List<ShopWithGroupsDTO> getUnassignedShops(Long groupId) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getCurrentUserId();
        
        // 获取运营组信息，验证当前用户是否为该组组长
        OperationGroup group = operationGroupMapper.selectById(groupId);
        if (group == null) {
            throw new RuntimeException("运营组不存在");
        }
        
        // 校验权限：只有管理员或该组的组长可以获取未分配店铺列表
        boolean isAdmin = SecurityUtils.isAdmin();
        boolean isGroupLeader = group.getLeaderId().equals(currentUserId);
        
        if (!isAdmin && !isGroupLeader) {
            throw new RuntimeException("您没有权限查看未分配的店铺");
        }
        
        // 如果是管理员，可以查看所有未分配给当前组的店铺
        // 如果是组长，只能查看自己创建的和管理员创建的店铺
        
        // 获取管理员用户ID列表
        final List<Long> adminUserIds = userService.getAdminUserIds();
        
        // 构建查询条件：组长自己创建的店铺和管理员创建的店铺
        List<Shop> filteredShops;
        if (isAdmin) {
            // 管理员可以看到所有店铺
            filteredShops = shopMapper.selectList(new LambdaQueryWrapper<>());
        } else {
            // 组长只能看到自己创建的和管理员创建的店铺
            LambdaQueryWrapper<Shop> shopQueryWrapper = new LambdaQueryWrapper<>();
            shopQueryWrapper.and(wrapper -> 
                wrapper.eq(Shop::getCreateBy, currentUserId)
                      .or()
                      .in(!adminUserIds.isEmpty(), Shop::getCreateBy, adminUserIds)
            );
            filteredShops = shopMapper.selectList(shopQueryWrapper);
        }
        
        List<ShopWithGroupsDTO> resultShops = new ArrayList<>();
        
        // 获取当前运营组已分配的店铺ID列表
        LambdaQueryWrapper<ShopGroupAssignment> currentGroupWrapper = new LambdaQueryWrapper<>();
        currentGroupWrapper.eq(ShopGroupAssignment::getGroupId, groupId)
                          .eq(ShopGroupAssignment::getStatus, "0");
        List<ShopGroupAssignment> currentGroupAssignments = shopGroupAssignmentMapper.selectList(currentGroupWrapper);
        Set<Long> currentGroupShopIds = currentGroupAssignments.stream()
                                                           .map(ShopGroupAssignment::getShopId)
                                                           .collect(Collectors.toSet());
        
        // 查询所有店铺的运营组分配信息
        for (Shop shop : filteredShops) {
            // 跳过已分配给当前运营组的店铺
            if (currentGroupShopIds.contains(shop.getShopId())) {
                continue;
            }
            
            ShopWithGroupsDTO shopWithGroups = new ShopWithGroupsDTO();
            BeanUtils.copyProperties(shop, shopWithGroups);
            
            // 查询该店铺所属的所有运营组
            List<OperationGroup> belongGroups = getGroupsByShopId(shop.getShopId());
            shopWithGroups.setGroups(belongGroups);
            
            // 构建运营组名称字符串
            if (belongGroups != null && !belongGroups.isEmpty()) {
                String groupNames = belongGroups.stream()
                                              .map(OperationGroup::getGroupName)
                                              .collect(Collectors.joining(", "));
                shopWithGroups.setGroupNames(groupNames);
            } else {
                shopWithGroups.setGroupNames("");
            }
            
            // 屏蔽敏感信息
            shopWithGroups.setApiKey(null);
            shopWithGroups.setApiSecret(null);
            shopWithGroups.setAccessToken(null);
            
            resultShops.add(shopWithGroups);
        }
        
        return resultShops;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int assignShopsToGroup(Long groupId, List<Long> shopIds) {
        if (shopIds == null || shopIds.isEmpty()) {
            return 0;
        }
        
        // 校验运营组是否存在
        if (!existsById(groupId)) {
            throw new RuntimeException("运营组不存在");
        }
        
        int successCount = 0;
        
        for (Long shopId : shopIds) {
            // 查询店铺是否存在
            Shop shop = shopMapper.selectById(shopId);
            if (shop == null) {
                log.warn("店铺ID {} 不存在，跳过分配", shopId);
                continue;
            }
            
            // 检查是否已经分配给该运营组
            LambdaQueryWrapper<ShopGroupAssignment> existWrapper = new LambdaQueryWrapper<>();
            existWrapper.eq(ShopGroupAssignment::getShopId, shopId)
                       .eq(ShopGroupAssignment::getGroupId, groupId);
            if (shopGroupAssignmentMapper.selectCount(existWrapper) > 0) {
                log.info("店铺 {} 已经分配给运营组 {}, 跳过分配", shopId, groupId);
                continue;
            }
            
            // 为店铺分配运营组
            ShopGroupAssignment assignment = new ShopGroupAssignment();
            assignment.setShopId(shopId);
            assignment.setGroupId(groupId);
            assignment.setAssignTime(LocalDateTime.now());
            assignment.setStatus("0");
            
            if (shopGroupAssignmentMapper.insert(assignment) > 0) {
                successCount++;
            }
        }
        
        return successCount;
    }
    
    @Override
    public List<OperationGroup> getGroupsByShopId(Long shopId) {
        // 查询店铺关联的所有运营组ID
        List<Long> groupIds = shopGroupAssignmentMapper.selectGroupIdsByShopId(shopId);
        
        if (groupIds == null || groupIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 查询运营组详情
        LambdaQueryWrapper<OperationGroup> groupWrapper = new LambdaQueryWrapper<>();
        groupWrapper.in(OperationGroup::getGroupId, groupIds)
                   .eq(OperationGroup::getStatus, "0");
        
        return operationGroupMapper.selectList(groupWrapper);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeShopFromGroup(Long groupId, Long shopId) {
        // 首先检查运营组是否存在
        if (!existsById(groupId)) {
            throw new RuntimeException("移除店铺失败，运营组不存在");
        }
        
        // 检查店铺是否存在
        Shop shop = shopMapper.selectById(shopId);
        if (shop == null) {
            throw new RuntimeException("移除店铺失败，店铺不存在");
        }
        
        // 首先删除所有与该店铺和运营组关联的组长分配记录
        LambdaQueryWrapper<GroupLeaderShopAssignment> leaderAssignWrapper = new LambdaQueryWrapper<>();
        leaderAssignWrapper.eq(GroupLeaderShopAssignment::getShopId, shopId)
                           .eq(GroupLeaderShopAssignment::getGroupId, groupId);
        groupLeaderShopAssignmentMapper.delete(leaderAssignWrapper);
        
        // 然后删除店铺与运营组的关联记录
        LambdaQueryWrapper<ShopGroupAssignment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShopGroupAssignment::getShopId, shopId)
              .eq(ShopGroupAssignment::getGroupId, groupId);
        
        return shopGroupAssignmentMapper.delete(wrapper);
    }

    @Override
    public List<OperationGroupDTO> getExportData(ExportRequestDTO exportDTO) {
        String exportType = exportDTO.getExportType();
        QueryGroupDTO queryDTO = new QueryGroupDTO();
        
        // 如果有查询参数，设置查询条件
        if (exportDTO.getQueryParams() != null) {
            // 从查询参数中获取分组名称和状态等条件
            if (exportDTO.getQueryParams().containsKey("groupName")) {
                queryDTO.setGroupName((String) exportDTO.getQueryParams().get("groupName"));
            }
            
            if (exportDTO.getQueryParams().containsKey("status")) {
                queryDTO.setStatus((String) exportDTO.getQueryParams().get("status"));
            }
            
            if (exportDTO.getQueryParams().containsKey("leaderId")) {
                queryDTO.setLeaderId(Long.valueOf(exportDTO.getQueryParams().get("leaderId").toString()));
            }
        }

        // 根据导出类型获取数据
        if ("all".equals(exportType)) {
            // 导出全部
            queryDTO.setPageNum(1);
            queryDTO.setPageSize(Integer.MAX_VALUE);
            return getGroupList(queryDTO).getRecords();
        } else if ("page".equals(exportType)) {
            // 导出当前页
            queryDTO.setPageNum(exportDTO.getPageNum());
            queryDTO.setPageSize(exportDTO.getPageSize());
            return getGroupList(queryDTO).getRecords();
        } else if ("selected".equals(exportType)) {
            // 导出选定项
            List<Long> selectedIds = exportDTO.getSelectedIds();
            if (selectedIds == null || selectedIds.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 根据ID列表查询
            List<OperationGroupDTO> selectedGroups = new ArrayList<>();
            for (Long groupId : selectedIds) {
                OperationGroupDTO group = getGroupById(groupId);
                if (group != null) {
                    selectedGroups.add(group);
                }
            }
            return selectedGroups;
        } else {
            // 未知导出类型
            throw new IllegalArgumentException("未知的导出类型: " + exportType);
        }
    }
    
    @Override
    public List<OperationGroup> getAllGroups() {
        LambdaQueryWrapper<OperationGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OperationGroup::getStatus, "0"); // 只查询状态正常的运营组
        return operationGroupMapper.selectList(wrapper);
    }

    @Override
    public List<Long> getGroupIdsByMemberId(Long userId) {
        // 查询用户所在的所有运营组
        LambdaQueryWrapper<GroupMember> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GroupMember::getUserId, userId)
               .eq(GroupMember::getStatus, "0"); // 只查询状态正常的记录
        
        List<GroupMember> members = groupMemberMapper.selectList(wrapper);
        
        // 提取运营组ID列表
        return members.stream()
                .map(GroupMember::getGroupId)
                .distinct()
                .collect(Collectors.toList());
    }
} 