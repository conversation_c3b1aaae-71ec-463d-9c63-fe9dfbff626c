package com.xiao.temu.modules.quality.vo;

import lombok.Data;

import java.util.List;

/**
 * 本地抽检结果明细返回VO
 */
@Data
public class LocalQualityInspectionVO {
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 当前页码
     */
    private Integer pageNo;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
    
    /**
     * 抽检数据列表
     */
    private List<LocalQualityInspectionItemVO> items;
    
    /**
     * 店铺名称
     */
    private String shopName;
    
    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 单个抽检数据展示项
     */
    @Data
    public static class LocalQualityInspectionItemVO {
        /**
         * 抽检数据ID
         */
        private Long id;
        
        /**
         * 质检单ID
         */
        private Long qcBillId;
        
        /**
         * 商品SKU ID
         */
        private Long productSkuId;
        
        /**
         * 商品SKC ID
         */
        private Long productSkcId;
        
        /**
         * SPU ID
         */
        private Long spuId;
        
        /**
         * SKU名称
         */
        private String skuName;
        
        /**
         * 类目名称
         */
        private String catName;
        
        /**
         * 备货单号
         */
        private String purchaseNo;
        
        /**
         * 规格
         */
        private String spec;
        
        /**
         * 商品缩略图
         */
        private String thumbUrl;
        
        /**
         * 质检结果 1-合格 2-不合格
         */
        private String qcResult;
        
        /**
         * 质检结果更新时间
         */
        private String qcResultUpdateTime;
        
        /**
         * 疵点描述（不合格才有）
         */
        private String flawNameDesc;
        
        /**
         * 问题备注（不合格才有）
         */
        private String remark;
        
        /**
         * 疵点证明图片链接列表（不合格才有）
         */
        private List<String> attachments;
        
        /**
         * 外部编码
         */
        private String extCode;
        
        /**
         * 店铺ID
         */
        private Long shopId;
        
        /**
         * 店铺名称
         */
        private String shopName;
        
        /**
         * 店铺备注
         */
        private String shopRemark;
    }
} 