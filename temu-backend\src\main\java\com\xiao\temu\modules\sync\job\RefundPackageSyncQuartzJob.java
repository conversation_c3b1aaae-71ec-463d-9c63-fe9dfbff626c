package com.xiao.temu.modules.sync.job;

import com.xiao.temu.modules.sync.service.RefundPackageSyncService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

/**
 * 退货包裹数据同步Quartz任务
 * 当前配置为每30分钟执行一次
 */
@Component
@Slf4j
public class RefundPackageSyncQuartzJob extends QuartzJobBean {

    @Autowired
    private RefundPackageSyncService refundPackageSyncService;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        log.info("开始执行退货包裹数据同步Quartz任务... 触发时间: {}", context.getFireTime());
        try {
            String result = refundPackageSyncService.executeScheduledSync();
            log.info("退货包裹数据同步Quartz任务执行结果: {}", result);
        } catch (Exception e) {
            log.error("退货包裹数据同步Quartz任务执行异常", e);
            throw new JobExecutionException(e);
        }
    }
} 