<view class="container">
  <view class="header">
    <view class="header-left">

      <text class="title">备货单进度</text>
    </view>
    <view class="role-tag">{{userInfo.roleName || '员工'}}</view>
  </view>

  <view class="content">
    <block wx:if="{{progressDetail}}">
      <view class="order-info">
        <view class="info-title">备货单信息</view>
        <view class="info-item">备货单号：<text class="info-value">{{subPurchaseOrderSn}}</text></view>
        <view class="info-item">店铺ID：<text class="info-value">{{shopId}}</text></view>
      </view>
      
      <view class="progress-title">生产进度</view>
      <view class="progress-list">
        <block wx:for="{{progressItems}}" wx:key="type">
          <view class="progress-item">
            <view class="progress-dot {{progressDetail[item.status] === 1 ? 'done' : 'todo'}}"></view>
            <view class="progress-content">
              <view class="progress-name {{progressDetail[item.status] === 1 ? 'done' : 'todo'}}">{{progressTypeMap[item.type]}}</view>
              <view class="progress-time">{{progressDetail[item.status] === 1 ? progressDetail[item.time] : '未完成'}}</view>
              <block wx:if="{{userInfo.roleName === item.role && progressDetail[item.status] !== 1}}">
                <button class="confirm-btn" bindtap="confirmProcess">确认完成{{progressTypeMap[item.type]}}</button>
              </block>
            </view>
          </view>
        </block>
      </view>
      
      <!-- 操作日志 -->
      <view class="logs-section" wx:if="{{progressLogs && progressLogs.length > 0}}">
        <view class="info-title">操作日志</view>
        <view class="logs-list">
          <view class="log-item" wx:for="{{progressLogs}}" wx:key="id">
            <view class="log-time">{{item.operationTime}}</view>
            <view class="log-content">
              【{{item.operatorName}}】{{item.operationType === '1' ? '完成' : '撤销'}}了【{{item.progressTypeName}}】工序
            </view>
          </view>
        </view>
      </view>
    </block>
    <view class="empty-tip" wx:else>{{loading ? '加载中...' : '暂无备货单信息'}}</view>
  </view>
</view> 