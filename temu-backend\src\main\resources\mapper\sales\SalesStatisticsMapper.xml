<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.sales.mapper.SalesStatisticsMapper">

    <resultMap id="BaseResultMap" type="com.xiao.temu.modules.sales.entity.SalesStatistics">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="statistics_date" property="statisticsDate" />
        <result column="today_sales" property="todaySales" />
        <result column="last_week_sales" property="lastWeekSales" />
        <result column="last_month_sales" property="lastMonthSales" />
        <result column="total_products" property="totalProducts" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, shop_id, shop_name, statistics_date, today_sales, last_week_sales, last_month_sales, 
        total_products, create_time, update_time
    </sql>
    
    <select id="findByShopIdsAndDateBetween" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM t_sales_statistics
        WHERE shop_id IN 
        <foreach item="shopId" collection="shopIds" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test="startDate != null">
            AND statistics_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND statistics_date &lt;= #{endDate}
        </if>
        ORDER BY statistics_date DESC, shop_id
    </select>
    
    <select id="findByShopIdAndDate" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM t_sales_statistics
        WHERE shop_id = #{shopId}
        AND statistics_date = #{date}
        LIMIT 1
    </select>

</mapper> 