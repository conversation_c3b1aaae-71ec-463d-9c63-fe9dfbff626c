<template>
  <!-- 状态筛选选项卡 -->
  <SearchCard class="search-card status-tabs-card">
    <div class="tab-container">
      <div class="status-filter-tabs">
        <div v-for="(tab, index) in statusTabs" :key="index"
          :class="['status-tab', { active: activeStatusTab === tab.value }]" @click="handleStatusTabClick(tab.value)">
          {{ tab.label }}
          <span v-if="tab.count > 0" class="status-count">({{ tab.count }})</span>
        </div>
      </div>
    </div>
    
    <!-- 排序选择下拉框 -->
    <div class="sort-options-wrapper">
      <div class="sort-options">
        <!-- 添加批量打印按钮 -->
        <div class="action-buttons">
          <el-button 
            size="small" 
            class="batch-button" 
            :disabled="!hasSelectedRows"
            @click="handleBatchPrintPicking"
          >批量打印拣货单</el-button>
          <!-- 新增导出按钮 -->
          <el-button size="small" class="batch-button" :disabled="!hasSelectedRows" @click="handleExportSelected">导出选中数据</el-button>
          <el-button size="small" class="batch-button" @click="showExportDialog">按条件导出数据</el-button>
        </div>
        <el-select 
          v-model="sortValue" 
          placeholder="请选择排序方式" 
          size="small" 
          @change="handleSortChange" 
          class="sort-select"
          popper-class="sort-select-dropdown">
          <el-option
            v-for="item in sortOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </div>
    </div>
  </SearchCard>

  <!-- 新增导出对话框 -->
  <el-dialog v-model="exportDialogVisible" title="数据导出" width="400px" destroy-on-close>
    <div class="export-dialog-content">
      <p>当前查询共 {{ total }} 条数据</p>
      <el-form label-width="120px">
        <el-form-item label="导出页数：">
          <el-select v-model="exportPages" placeholder="请选择导出页数" style="width: 100%">
            <el-option
              v-for="option in exportPagesOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="exportPages === 'custom'" label="自定义页数：">
          <el-input-number v-model="customExportPages" :min="1" :max="maxExportPages" style="width: 100%"></el-input-number>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="exportDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmExport" :loading="exporting">确认导出</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, computed } from 'vue'
import { SearchCard } from '@/components/temu'
import type { PurchaseOrderRequestDTO } from '@/types/purchaseOrder'

interface StatusTab {
  label: string
  value: string | number
  count: number
}

interface SortOption {
  label: string
  value: string
}

const props = defineProps({
  // 状态选项卡数据
  statusTabs: {
    type: Array as () => StatusTab[],
    required: true
  },
  // 当前激活的选项卡
  activeStatusTab: {
    type: [String, Number],
    required: true
  },
  // 查询参数
  queryParams: {
    type: Object as () => PurchaseOrderRequestDTO,
    required: true
  },
  // 排序选项
  sortOptions: {
    type: Array as () => SortOption[],
    required: true
  },
  // 添加是否有选中行属性（用于批量操作按钮的禁用状态）
  hasSelectedRows: {
    type: Boolean,
    default: false
  },
  // 新增：总数据条数
  total: {
    type: Number,
    default: 0
  },
  // 新增：排序显示值，便于双向绑定
  modelValue: {
    type: String,
    default: ''
  }
})

const emit = defineEmits([
  'status-tab-click',   // 状态选项卡点击
  'sort-change',        // 排序变更
  'batch-print-picking', // 批量打印拣货单
  'export-selected',    // 新增：导出选中数据
  'export-by-condition', // 新增：按条件导出数据
  'update:modelValue'   // 新增：更新排序显示值
])

// 处理状态选项卡点击
const handleStatusTabClick = (value: string | number) => {
  // 如果当前已经是激活状态，则不做任何操作
  if (props.activeStatusTab === value) return
  
  // 发送状态选项卡点击事件
  emit('status-tab-click', value)
}

// 处理排序选项变化，使用计算属性实现双向绑定
const sortValue = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

// 处理排序选项变化
const handleSortChange = (value: string) => {
  // 发送排序变更事件，传递选中的值
  emit('sort-change', value)
}

// 添加处理批量打印拣货单方法
const handleBatchPrintPicking = () => {
  emit('batch-print-picking')
}

// 新增：导出对话框相关变量
const exportDialogVisible = ref(false)
const exportPages = ref('current')
const customExportPages = ref(1)
const exporting = ref(false)

// 新增：导出页数选项
const exportPagesOptions = [
  { label: '全部数据', value: 'all' },
  { label: '当前页', value: 'current' },
  { label: '自定义页数', value: 'custom' }
]

// 新增：计算最大可导出页数
const maxExportPages = computed(() => {
  const pageSize = props.queryParams.pageSize || 10
  return Math.ceil(props.total / pageSize)
})

// 新增：处理导出选中数据
const handleExportSelected = () => {
  // 导出选中的数据
  emit('export-selected')
}

// 新增：显示导出对话框
const showExportDialog = () => {
  // 显示导出对话框
  exportDialogVisible.value = true
}

// 新增：确认导出
const handleConfirmExport = () => {
  exporting.value = true
  
  // 计算要导出的页数
  let pagesToExport = 0
  let exportType = exportPages.value;
  
  if (exportPages.value === 'all') {
    pagesToExport = maxExportPages.value
  } else if (exportPages.value === 'current') {
    pagesToExport = 1
  } else if (exportPages.value === 'custom') {
    pagesToExport = customExportPages.value
  }
  
  // 触发导出事件
  emit('export-by-condition', {
    exportType: exportType,
    pages: pagesToExport
  })
  
  // 重置状态
  exportDialogVisible.value = false
  
  // 延迟重置loading状态，以便用户看到执行反馈
  setTimeout(() => {
    exporting.value = false
  }, 1000)
}
</script>

<style scoped>
.status-tabs-card {
  border-radius: 4px 4px 0 0;
  margin-bottom: 0;
  box-shadow: none;
  z-index: 1;
  flex-shrink: 0;
  border-bottom: none;
  border: 1px solid #e4e7ed;
  border-bottom: none;
}

/* 禁用SearchCard的hover效果 */
.status-tabs-card:deep(.el-card):hover {
  box-shadow: none !important;
}

.status-tabs-card:hover {
  box-shadow: none !important;
}

/* 添加外层容器确保状态栏有足够空间 */
.tab-container {
  width: 100%;
  overflow: hidden;
  padding: 0 0 1px 0;
}

/* 状态筛选选项卡样式 */
.status-filter-tabs {
  display: flex;
  flex-wrap: nowrap; /* 防止内容换行 */
  gap: 5px;
  margin-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 0;
  overflow-x: auto;
  /* 隐藏滚动条但保留滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  min-height: 42px;
  padding-bottom: 2px;
  /* 添加平滑滚动 */
  scroll-behavior: smooth;
  /* 增加水平间距 */
  padding-left: 2px;
  padding-right: 2px;
}

/* 针对Webkit浏览器隐藏滚动条 */
.status-filter-tabs::-webkit-scrollbar {
  display: none;
}

.status-tab {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
  white-space: nowrap;
  margin-bottom: -1px;
  flex-shrink: 0; /* 防止标签被压缩 */
}

.status-tab:hover {
  color: #409eff;
}

.status-tab.active {
  color: #409eff;
  border-bottom: 2px solid #409eff;
  font-weight: 500;
}

.status-count {
  margin-left: 4px;
  font-size: 12px;
  color: #909399;
}

.status-tab.active .status-count {
  color: #409eff;
}

/* 排序选项样式 */
.sort-options-wrapper {
  padding: 0 12px;
  margin-bottom: 10px;
}

.sort-options {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

/* 添加批量操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 10px;
}

.batch-button {
  font-size: 12px;
  height: 32px;
  padding: 0 12px;
}

.sort-select {
  width: 200px;
}

/* 新增：导出对话框样式 */
.export-dialog-content {
  padding: 0 10px;
}
</style>

<!-- 添加全局样式来覆盖排序下拉选项样式 -->
<style>
/* 覆盖排序下拉框选项的默认样式 */
.sort-select-dropdown .el-select-dropdown__item {
  color: #606266 !important;
}

.sort-select-dropdown .el-select-dropdown__item.selected {
  color: #409EFF !important;
  font-weight: bold;
}

.sort-select-dropdown .el-select-dropdown__item.hover, 
.sort-select-dropdown .el-select-dropdown__item:hover {
  background-color: #f5f7fa !important;
}
</style> 