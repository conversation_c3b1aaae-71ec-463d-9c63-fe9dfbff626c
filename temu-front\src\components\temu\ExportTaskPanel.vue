<template>
  <div class="export-task-dropdown">
    <!-- 任务图标 - 带数字提示 -->
    <el-badge :value="activeTasks.length" :hidden="activeTasks.length === 0" class="task-badge">
      <el-dropdown trigger="click" @visible-change="handleDropdownChange">
        <div class="task-icon">
          <el-tooltip content="导出任务" placement="bottom" :show-after="300">
            <el-icon :size="18"><Download /></el-icon>
          </el-tooltip>
        </div>
        
        <template #dropdown>
          <el-dropdown-menu class="task-dropdown-menu">
            <div class="task-dropdown-header">
              <span class="title">导出任务</span>
              <div class="actions" v-if="completedOrFailedTasks.length > 0">
                <el-button type="primary" link size="small" @click.stop="clearCompletedTasks">
                  清除已完成任务
                </el-button>
              </div>
            </div>
            
            <div class="task-dropdown-body">
              <div v-if="allTasks.length === 0" class="empty-tip">
                暂无导出任务
              </div>
              
              <div v-else class="task-list">
                <div v-for="task in allTasks" :key="task.id" class="task-item">
                  <div class="task-info">
                    <div class="task-name">{{ task.name }}</div>
                    <div class="task-meta">
                      <span class="task-time">{{ formatTime(task.createdAt) }}</span>
                      <span :class="['task-status', `status-${task.status}`]">{{ getStatusText(task.status) }}</span>
                    </div>
                  </div>
                  
                  <div class="task-progress">
                    <template v-if="task.status === 'processing' || task.status === 'pending'">
                      <el-progress 
                        :percentage="task.progress" 
                        :status="task.progress === 100 ? 'success' : ''"
                        :stroke-width="8"
                      />
                      <el-button 
                        type="danger" 
                        size="small" 
                        link 
                        class="cancel-btn"
                        @click.stop="cancelTask(task.id)"
                      >
                        取消
                      </el-button>
                    </template>
                    
                    <template v-else-if="task.status === 'completed'">
                      <div class="task-completed">
                        <el-icon><Check /></el-icon>
                        <span>已完成</span>
                      </div>
                    </template>
                    
                    <template v-else-if="task.status === 'failed'">
                      <div class="task-failed">
                        <el-icon><Close /></el-icon>
                        <span>{{ task.error || '失败' }}</span>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-badge>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Download, Check, Close } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import { useExportTaskStore } from '@/store'
import type { ExportTask } from '@/store/modules/exportTask'

const exportTaskStore = useExportTaskStore()
const expanded = ref(false)

// 获取所有任务
const allTasks = computed(() => {
  return exportTaskStore.recentTasks
})

// 获取活动中的任务
const activeTasks = computed(() => {
  return exportTaskStore.activeTasks
})

// 已完成或失败的任务
const completedOrFailedTasks = computed(() => {
  return allTasks.value.filter(task => 
    task.status === 'completed' || task.status === 'failed'
  )
})

// 处理下拉菜单状态变化
const handleDropdownChange = (visible: boolean) => {
  expanded.value = visible
}

// 格式化时间
const formatTime = (date: Date) => {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  
  if (diffMins < 1) {
    return '刚刚'
  } else if (diffMins < 60) {
    return `${diffMins} 分钟前`
  } else if (diffMins < 24 * 60) {
    const hours = Math.floor(diffMins / 60)
    return `${hours} 小时前`
  } else {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    
    return `${year}-${month}-${day} ${hour}:${minute}`
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return '准备中'
    case 'processing':
      return '进行中'
    case 'completed':
      return '已完成'
    case 'failed':
      return '失败'
    default:
      return status
  }
}

// 取消任务
const cancelTask = (taskId: string) => {
  ElMessageBox.confirm('确定要取消此导出任务吗？', '取消任务', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    exportTaskStore.cancelTask(taskId)
  }).catch(() => {
    // 用户取消操作，不做任何处理
  })
}

// 清除已完成任务
const clearCompletedTasks = () => {
  exportTaskStore.clearCompletedTasks()
}
</script>

<style scoped>
.export-task-dropdown {
  height: 100%;
  display: flex;
  align-items: center;
  margin: 0 10px;
}

.task-icon {
  cursor: pointer;
  color: #606266;
  display: flex;
  align-items: center;
  padding: 0 5px;
  height: 100%;
}

.task-icon:hover {
  color: #409EFF;
}

.task-dropdown-menu {
  min-width: 350px;
  padding: 0;
}

.task-dropdown-header {
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-dropdown-header .title {
  font-size: 16px;
  font-weight: 500;
}

.task-dropdown-body {
  max-height: 320px;
  overflow-y: auto;
  padding: 12px;
}

.empty-tip {
  text-align: center;
  color: #909399;
  padding: 16px 0;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-item {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 12px;
}

.task-info {
  margin-bottom: 8px;
}

.task-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.task-status {
  font-weight: 500;
}

.status-completed {
  color: #67c23a;
}

.status-processing {
  color: #409eff;
}

.status-pending {
  color: #e6a23c;
}

.status-failed {
  color: #f56c6c;
}

.task-progress {
  position: relative;
}

.cancel-btn {
  position: absolute;
  right: 0;
  top: -5px;
}

.task-completed, .task-failed {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 13px;
}

.task-completed {
  color: #67c23a;
}

.task-failed {
  color: #f56c6c;
}

/* 自定义下拉菜单样式 */
:deep(.el-popper.is-dark) {
  background-color: #fff;
  border: 1px solid #e4e7ed;
  color: #303133;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-popper.is-dark .el-popper__arrow::before) {
  background-color: #fff;
  border: 1px solid #e4e7ed;
}
</style> 