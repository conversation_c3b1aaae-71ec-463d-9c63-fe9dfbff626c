package com.xiao.temu.modules.violation.controller;

import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.violation.entity.ShopViolationInfo;
import com.xiao.temu.modules.violation.dto.BatchViolationAddRequest;
import com.xiao.temu.modules.violation.vo.MmsPurchaseOrderViolationInfoVO;
import com.xiao.temu.modules.violation.dto.ShopViolationInfoDTO;
import com.xiao.temu.modules.violation.dto.ShopViolationInfoQueryDTO;
import com.xiao.temu.modules.violation.dto.ViolationDetail;
import com.xiao.temu.modules.violation.dto.ViolationInfoItem;
import com.xiao.temu.infrastructure.task.vo.ExportTaskProgressVO;
import com.xiao.temu.modules.violation.vo.ShopViolationInfoVO;
import com.xiao.temu.security.annotation.DataScope;
import com.xiao.temu.security.annotation.RequiresPermission;
import com.xiao.temu.security.utils.SecurityUtils;
import com.xiao.temu.modules.operation.service.GroupLeaderShopAssignmentService;
import com.xiao.temu.modules.operation.service.OperationGroupService;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.violation.service.ShopViolationDetailService;
import com.xiao.temu.modules.violation.service.ShopViolationInfoService;
import com.xiao.temu.modules.system.service.SysDataPermissionService;
import com.xiao.temu.infrastructure.exporter.ShopViolationExcelExporter;
import com.xiao.temu.modules.sync.service.QualityInspectionSyncService;
import com.xiao.temu.infrastructure.excel.ExcelExportService;
import com.xiao.temu.infrastructure.task.ExportTaskManager;
import com.xiao.temu.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 店铺违规信息控制器
 */
@Slf4j
@RestController
@RequestMapping("/local/violation")
public class LocalShopViolationInfoController {

    @Autowired
    private ShopViolationInfoService violationInfoService;
    
    @Autowired
    private ShopService shopService;
    
    @Autowired
    private OperationGroupService groupService;
    
    @Autowired
    private GroupLeaderShopAssignmentService assignmentService;
    
    @Autowired
    private ShopViolationDetailService violationDetailService;
    
    @Autowired
    private SysDataPermissionService dataPermissionService;
    
    @Autowired
    private ExportTaskManager exportTaskManager;
    
    @Autowired
    private ExcelExportService excelExportService;
    
    @Autowired
    private ShopViolationExcelExporter shopViolationExcelExporter;
    
    @Autowired
    private QualityInspectionSyncService qualityInspectionSyncService;

    /**
     * 分页查询违规信息列表
     */
    @PostMapping("/list")
    @RequiresPermission("operation:violation:list")
    @DataScope(tableAlias = "sga", userIdColumn = "shop_id", groupIdColumn = "group_id")
    public ApiResponse<ShopViolationInfoVO> list(@RequestBody ShopViolationInfoQueryDTO queryDTO) {
        // 超级管理员可查看所有店铺的违规信息
        if (!SecurityUtils.isAdmin()) {
            Long userId = SecurityUtils.getCurrentUserId();
            
            // 获取用户的最高数据权限
            String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
            
            // 如果用户不具有全部数据权限，则需要进行额外的权限控制
            if (!"2".equals(permissionType)) {
                // 判断是否有特定店铺查询
                if (queryDTO.getShopId() != null || (queryDTO.getShopIds() != null && !queryDTO.getShopIds().isEmpty())) {
                    // 检查是否有权限访问这些店铺
                    if (queryDTO.getShopId() != null) {
                        boolean hasPermission = shopService.checkShopPermission(userId, queryDTO.getShopId(), false);
                        if (!hasPermission) {
                            return ApiResponse.error("无权查看该店铺的违规信息");
                        }
                    }
                    
                    if (queryDTO.getShopIds() != null) {
                        for (Long shopId : queryDTO.getShopIds()) {
                            boolean hasPermission = shopService.checkShopPermission(userId, shopId, false);
                            if (!hasPermission) {
                                return ApiResponse.error("无权查看店铺ID为" + shopId + "的违规信息");
                            }
                        }
                    }
                }
                
                // 判断是否按运营组查询
                if (queryDTO.getGroupId() != null) {
                    // 验证是否有权限查看该组
                    boolean isGroupLeader = assignmentService.isGroupLeader(userId, queryDTO.getGroupId());
                    boolean isGroupMember = groupService.checkUserInGroup(userId, queryDTO.getGroupId());
                    
                    if (!isGroupLeader && !isGroupMember) {
                        return ApiResponse.error("无权查看该运营组的违规信息");
                    }
                } else {
                    // 如果未指定运营组，尝试设置为用户所在的组长组
                    List<Long> leaderGroupIds = groupService.getGroupIdsByLeaderId(userId);
                    if (!leaderGroupIds.isEmpty()) {
                        queryDTO.setGroupId(leaderGroupIds.get(0));
                    }
                }
            }
        }
        
        // 使用带详情的查询方法，返回包含违规详情的结果
        ShopViolationInfoVO result = violationInfoService.getViolationInfoListWithDetail(queryDTO);
        return ApiResponse.success(result);
    }

    /**
     * 获取违规信息详情
     */
    @GetMapping("/{id}")
    @RequiresPermission("operation:violation:query")
    public ApiResponse<ShopViolationInfoDTO> getInfo(@PathVariable Long id) {
        ShopViolationInfoDTO violationInfo = violationInfoService.getViolationInfoById(id);
        
        if (violationInfo == null) {
            return ApiResponse.error("违规信息不存在");
        }
        
        // 检查访问权限
        if (!SecurityUtils.isAdmin()) {
            Long userId = SecurityUtils.getCurrentUserId();
            boolean hasPermission = shopService.checkShopPermission(userId, violationInfo.getShopId(), false);
            if (!hasPermission) {
                return ApiResponse.error("无权访问该违规信息");
            }
        }
        
        return ApiResponse.success(violationInfo);
    }

    /**
     * 新增违规信息   
     */
    @PostMapping
    @RequiresPermission("operation:violation:add")
    public ApiResponse<Boolean> add(@RequestBody ShopViolationInfo violationInfo) {
        // 检查权限
        if (!SecurityUtils.isAdmin()) {
            Long userId = SecurityUtils.getCurrentUserId();
            boolean hasPermission = shopService.checkShopPermission(userId, violationInfo.getShopId(), true);
            if (!hasPermission) {
                return ApiResponse.error("无权操作该店铺的违规信息");
            }
        }
        
        // 检查是否存在
        if (violationInfoService.checkViolationInfoExists(violationInfo.getShopId(), violationInfo.getPunishSn())) {
            return ApiResponse.error("该违规信息已存在");
        }
        
        boolean result = violationInfoService.addViolationInfo(violationInfo);
        return result ? ApiResponse.success() : ApiResponse.error("添加违规信息失败");
    }

    /**
     * 修改违规信息
     */
    @PutMapping
    @RequiresPermission("operation:violation:edit")
    public ApiResponse<Boolean> update(@RequestBody ShopViolationInfo violationInfo) {
        // 获取原始记录，以便检查权限
        ShopViolationInfoDTO original = violationInfoService.getViolationInfoById(violationInfo.getId());
        
        if (original == null) {
            return ApiResponse.error("违规信息不存在");
        }
        
        // 检查权限
        if (!SecurityUtils.isAdmin()) {
            Long userId = SecurityUtils.getCurrentUserId();
            boolean hasPermission = shopService.checkShopPermission(userId, original.getShopId(), true);
            if (!hasPermission) {
                return ApiResponse.error("无权操作该店铺的违规信息");
            }
        }
        
        boolean result = violationInfoService.updateViolationInfo(violationInfo);
        return result ? ApiResponse.success() : ApiResponse.error("修改违规信息失败");
    }

    /**
     * 删除违规信息
     */
    @DeleteMapping("/{id}")
    @RequiresPermission("operation:violation:remove")
    public ApiResponse<Boolean> remove(@PathVariable Long id) {
        // 获取原始记录，以便检查权限
        ShopViolationInfoDTO violationInfo = violationInfoService.getViolationInfoById(id);
        
        if (violationInfo == null) {
            return ApiResponse.error("违规信息不存在");
        }
        
        // 检查权限
        if (!SecurityUtils.isAdmin()) {
            Long userId = SecurityUtils.getCurrentUserId();
            boolean hasPermission = shopService.checkShopPermission(userId, violationInfo.getShopId(), true);
            if (!hasPermission) {
                return ApiResponse.error("无权操作该店铺的违规信息");
            }
        }
        
        boolean result = violationInfoService.deleteViolationInfo(id);
        return result ? ApiResponse.success() : ApiResponse.error("删除违规信息失败");
    }

    /**
     * 批量删除违规信息
     */
    @DeleteMapping("/batch/{ids}")
    @RequiresPermission("operation:violation:remove")
    public ApiResponse<Boolean> batchRemove(@PathVariable Long[] ids) {
        // 权限校验在ServiceImpl中处理
        if (!SecurityUtils.isAdmin()) {
            // 仅管理员可批量删除
            return ApiResponse.error("无权进行批量删除操作");
        }
        
        boolean result = violationInfoService.batchDeleteViolationInfo(ids);
        return result ? ApiResponse.success() : ApiResponse.error("批量删除违规信息失败");
    }
    
    /**
     * 创建导出任务并返回任务ID
     * 
     * @param exportParams 导出参数
     * @return API响应，包含任务ID
     */
    @PostMapping("/createExportTask")
    @RequiresPermission("operation:violation:export")
    public ApiResponse createExportTask(@RequestBody Map<String, Object> exportParams) {
        try {
            // 获取当前用户ID
            Long userId = SecurityUtils.getCurrentUserId();
            
            // 获取文件名
            String fileName = (String) exportParams.get("fileName");
            if (fileName == null || fileName.trim().isEmpty()) {
                fileName = "店铺违规信息";
            }
            
            // 创建导出任务
            String taskId = exportTaskManager.createTask(fileName);
            
            // 将导出参数与任务ID关联起来
            exportParams.put("taskId", taskId);
            exportParams.put("userId", userId); // 保存当前用户ID到导出参数
            
            // 异步执行导出操作
            excelExportService.asyncExportExcel(exportParams, shopViolationExcelExporter);
            
            // 返回任务ID
            Map<String, String> result = new HashMap<>();
            result.put("taskId", taskId);
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("创建导出任务失败", e);
            return ApiResponse.error("创建导出任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取导出任务进度
     * @param taskId 任务ID
     * @return 导出任务进度
     */
    @GetMapping("/exportProgress/{taskId}")
    public ApiResponse getExportProgress(@PathVariable String taskId) {
        ExportTaskManager.TaskInfo taskInfo = exportTaskManager.getTaskInfo(taskId);
        if (taskInfo == null) {
            return ApiResponse.error("任务不存在");
        }
        
        ExportTaskProgressVO progressVO = new ExportTaskProgressVO(
            taskInfo.getTaskId(),
            taskInfo.getProgress(),
            taskInfo.getStatus(),
            taskInfo.getMessage(),
            taskInfo.getFileName()
        );
        
        return ApiResponse.success(progressVO);
    }
    
    /**
     * 下载已生成的Excel文件
     * 
     * @param taskId 任务ID
     * @param response HTTP响应对象
     */
    @GetMapping("/downloadExcel/{taskId}")
    public void downloadExcelFile(@PathVariable String taskId, HttpServletResponse response) {
        try {
            // 尝试获取当前用户信息，如果未认证会抛出异常
            Long userId = null;
            try {
                userId = SecurityUtils.getCurrentUserId();
                log.info("用户 [{}] 正在下载文件，任务ID: {}", userId, taskId);
            } catch (Exception e) {
                log.warn("获取用户ID失败，可能是未认证请求: {}", e.getMessage());
                response.setContentType("application/json");
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("{\"code\":401,\"message\":\"未授权，请先登录\"}");
                return;
            }
            
            // 获取任务信息
            ExportTaskManager.TaskInfo taskInfo = exportTaskManager.getTaskInfo(taskId);
            if (taskInfo == null) {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"任务不存在\"}");
                return;
            }
            
            if (!"completed".equals(taskInfo.getStatus())) {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"任务未完成，无法下载\"}");
                return;
            }
            
            // 查找导出文件
            File outputDir = new File(System.getProperty("java.io.tmpdir"), "temu_export_output");
            File[] files = outputDir.listFiles((dir, name) -> name.startsWith(taskInfo.getFileName() + "_") && name.endsWith(".xlsx"));
            
            if (files == null || files.length == 0) {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"找不到导出文件\"}");
                return;
            }
            
            // 找到最新的文件
            File latestFile = files[0];
            for (File file : files) {
                if (file.lastModified() > latestFile.lastModified()) {
                    latestFile = file;
                }
            }
            
            // 记录文件路径到任务信息中，以便定时任务清理
            if (taskInfo.getFilePath() == null) {
                exportTaskManager.completeTaskWithFilePath(taskId, "导出完成，正在下载", latestFile.getAbsolutePath());
            }
            
            // 设置响应头
            String encodedFileName = URLEncoder.encode(taskInfo.getFileName(), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");
            
            // 将文件写入响应
            try (FileInputStream fis = new FileInputStream(latestFile)) {
                byte[] buffer = new byte[1024];
                int len;
                while ((len = fis.read(buffer)) != -1) {
                    response.getOutputStream().write(buffer, 0, len);
                }
                response.getOutputStream().flush();
                log.info("文件下载成功: {}, 用户: {}, 任务ID: {}", latestFile.getName(), userId, taskId);
                // 不再尝试立即删除文件，依靠定时任务清理
            } catch (IOException e) {
                log.error("写入响应失败", e);
                response.reset();
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"下载文件失败: " + e.getMessage() + "\"}");
            }
        } catch (Exception e) {
            log.error("下载处理失败", e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"处理下载请求失败: " + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("设置错误响应失败", ex);
            }
        }
    }

    /**
     * 批量新增违规信息
     */
    @PostMapping("/batch")
    @RequiresPermission("operation:violation:add")
    public ApiResponse<Boolean> batchAdd(@RequestBody BatchViolationAddRequest request) {
        // 检查请求参数
        if (request == null) {
            return ApiResponse.error("请求参数不能为空");
        }
        
        // 获取违规信息列表（兼容新旧两种数据结构）
        List<ViolationInfoItem> violationItems = request.getViolationList();
        if (violationItems == null || violationItems.isEmpty()) {
            return ApiResponse.error("违规信息列表不能为空");
        }
        
        // 获取mallId（兼容首字母大小写不同的情况）
        String mallId = request.getMallId();
        if (mallId == null) {
            // 尝试获取data.json中首字母大写的MallId字段
            try {
                java.lang.reflect.Field field = request.getClass().getDeclaredField("MallId");
                field.setAccessible(true);
                Object value = field.get(request);
                if (value != null) {
                    mallId = value.toString();
                }
            } catch (Exception e) {
                log.debug("获取MallId字段失败", e);
            }
        }
        Long shopId = request.getShopId();
        
        // 如果没有shopId但有mallId，尝试查找对应的店铺
        if (shopId == null && mallId != null && !mallId.isEmpty()) {
            Shop shop = shopService.getShopByMallId(mallId);
            if (shop != null) {
                shopId = shop.getShopId();
                log.info("通过mallId: {} 找到对应的shopId: {}", mallId, shopId);
            } else {
                return ApiResponse.error("未找到匹配的店铺信息，请确认MallId是否正确: " + mallId);
            }
        }
        
        // 最终仍未获取到shopId则报错
        if (shopId == null) {
            return ApiResponse.error("缺少店铺ID参数");
        }
        
        // 检查权限
        if (!SecurityUtils.isAdmin()) {
            Long userId = SecurityUtils.getCurrentUserId();
            boolean hasPermission = shopService.checkShopPermission(userId, shopId, true);
            if (!hasPermission) {
                return ApiResponse.error("无权操作该店铺的违规信息");
            }
        }
        
        // 批量处理违规信息
        List<ShopViolationInfo> violationInfoList = new java.util.ArrayList<>();
        for (ViolationInfoItem item : violationItems) {
            ShopViolationInfo violationInfo = new ShopViolationInfo();
            
            // 设置店铺ID
            violationInfo.setShopId(shopId);
            
            // 设置MallId
            if (mallId != null && !mallId.isEmpty()) {
                violationInfo.setMallId(mallId);
            }
            
            // 设置其他属性
            violationInfo.setPunishSn(item.getPunishSn());
            violationInfo.setPunishTypeDesc(item.getPunishTypeDesc());
            violationInfo.setPunishTypeCode(item.getPunishTypeCode());
            violationInfo.setViolationStartTime(new java.util.Date(item.getViolationStartTime()));
            // 处理金额转换
            if (item.getPunishAmount() != null && !item.getPunishAmount().isEmpty()) {
                violationInfo.setPunishAmount(new BigDecimal(item.getPunishAmount()));
            }
            violationInfo.setPunishAmountCurrency(item.getPunishAmountCurrency());
            violationInfo.setPunishStatus(item.getPunishStatus());
            violationInfo.setViewDetailsStatus(item.getViewDetailsStatus());
            violationInfo.setSubPurchaseOrderSn(item.getSubPurchaseOrderSn());
            violationInfo.setPunishSecondTypeDesc(item.getPunishSecondTypeDesc());
            violationInfo.setPunishFirstTypeDesc(item.getPunishFirstTypeDesc());
            violationInfo.setPunishFirstTypeCode(item.getPunishFirstTypeCode());
            
            // 可选属性，需要判空
            if (item.getPunishAmountDiff() != null) {
                violationInfo.setPunishAmountDiff(item.getPunishAmountDiff());
            }
            if (item.getPunishThirdTypeCode() != null) {
                violationInfo.setPunishThirdTypeCode(item.getPunishThirdTypeCode());
            }
            if (item.getPunishThirdTypeDesc() != null) {
                violationInfo.setPunishThirdTypeDesc(item.getPunishThirdTypeDesc());
            }
            if (item.getVoucher() != null) {
                violationInfo.setVoucher(item.getVoucher());
            }
            if (item.getQcFlawPictureUrlList() != null) {
                violationInfo.setQcFlawPictureUrlList(item.getQcFlawPictureUrlList());
            }
            // 设置showVoucher(已更改为String类型)
            if (item.getShowVoucher() != null) {
                violationInfo.setShowVoucher(item.getShowVoucher());
            }
            if (item.getQualityEventPunishDimension() != null) {
                violationInfo.setQualityEventPunishDimension(item.getQualityEventPunishDimension());
            }
            if (item.getQualityEventPunishDimensionDesc() != null) {
                violationInfo.setQualityEventPunishDimensionDesc(item.getQualityEventPunishDimensionDesc());
            }
            if (item.getProductSkcId() != null) {
                violationInfo.setProductSkcId(item.getProductSkcId());
            }
            
            // 设置倒计时
            if (item.getCountdownTime() != null) {
                violationInfo.setCountdownTime(item.getCountdownTime());
            }
            
            violationInfoList.add(violationInfo);
        }
        
        boolean result = violationInfoService.batchAddViolationInfo(violationInfoList);
        return result ? ApiResponse.success() : ApiResponse.error("批量添加违规信息失败");
    }

    /**
     * 批量导入违规详情信息
     */
    @PostMapping("/batch/detail/direct")
    @RequiresPermission("operation:violation:add")
    public ApiResponse<Boolean> batchAddViolationDetailDirect(@RequestBody List<ViolationInfoItem> violationItems, 
                                                             @RequestParam(required = false) Long shopId,
                                                             @RequestParam(required = false) String mallId) {
        // 检查请求参数
        if (violationItems == null || violationItems.isEmpty()) {
            return ApiResponse.error("违规信息列表不能为空");
        }
        
        // 预处理JSON字段，确保所有Detail对象正确构建
        for (ViolationInfoItem item : violationItems) {
            // 如果detail字段为null或是Map类型，需要转换成ViolationDetail对象
            if (item.getDetail() == null) {
                continue;
            }
            
            // 处理可能的Map转换
            if (item.getDetail() instanceof Map) {
                try {
                    Map<String, Object> detailMap = (Map<String, Object>) item.getDetail();
                    ViolationDetail violationDetail = new ViolationDetail();
                    
                    // 处理mmspurchaseOrderViolationInfoVO
                    if (detailMap.containsKey("mmspurchaseOrderViolationInfoVO")) {
                        Object mmsObj = detailMap.get("mmspurchaseOrderViolationInfoVO");
                        if (mmsObj instanceof Map) {
                            Map<String, Object> mmsMap = (Map<String, Object>) mmsObj;
                            MmsPurchaseOrderViolationInfoVO mmsVO = new MmsPurchaseOrderViolationInfoVO();
                            
                            if (mmsMap.containsKey("subPurchaseOrderSn")) {
                                mmsVO.setSubPurchaseOrderSn(String.valueOf(mmsMap.get("subPurchaseOrderSn")));
                            }
                            
                            if (mmsMap.containsKey("productSkuId")) {
                                Object skuIdObj = mmsMap.get("productSkuId");
                                if (skuIdObj instanceof Number) {
                                    mmsVO.setProductSkuId(((Number) skuIdObj).longValue());
                                } else if (skuIdObj != null) {
                                    try {
                                        mmsVO.setProductSkuId(Long.valueOf(skuIdObj.toString()));
                                    } catch (Exception e) {
                                        log.error("解析productSkuId失败: {}", skuIdObj, e);
                                    }
                                }
                            }
                            
                            if (mmsMap.containsKey("stockQuantity")) {
                                Object qtyObj = mmsMap.get("stockQuantity");
                                if (qtyObj instanceof Number) {
                                    mmsVO.setStockQuantity(((Number) qtyObj).intValue());
                                } else if (qtyObj != null) {
                                    try {
                                        mmsVO.setStockQuantity(Integer.valueOf(qtyObj.toString()));
                                    } catch (Exception e) {
                                        log.error("解析stockQuantity失败: {}", qtyObj, e);
                                    }
                                }
                            }
                            
                            if (mmsMap.containsKey("lackQuantity")) {
                                Object qtyObj = mmsMap.get("lackQuantity");
                                if (qtyObj instanceof Number) {
                                    mmsVO.setLackQuantity(((Number) qtyObj).intValue());
                                } else if (qtyObj != null) {
                                    try {
                                        mmsVO.setLackQuantity(Integer.valueOf(qtyObj.toString()));
                                    } catch (Exception e) {
                                        log.error("解析lackQuantity失败: {}", qtyObj, e);
                                    }
                                }
                            }
                            
                            if (mmsMap.containsKey("unqualifiedQuantity")) {
                                Object qtyObj = mmsMap.get("unqualifiedQuantity");
                                if (qtyObj instanceof Number) {
                                    mmsVO.setUnqualifiedQuantity(((Number) qtyObj).intValue());
                                } else if (qtyObj != null) {
                                    try {
                                        mmsVO.setUnqualifiedQuantity(Integer.valueOf(qtyObj.toString()));
                                    } catch (Exception e) {
                                        log.error("解析unqualifiedQuantity失败: {}", qtyObj, e);
                                    }
                                }
                            }
                            
                            violationDetail.setMmspurchaseOrderViolationInfoVO(mmsVO);
                        }
                    }
                    
                    item.setDetail(violationDetail);
                } catch (Exception e) {
                    log.error("处理detail对象失败: {}", item.getPunishSn(), e);
                }
            }
        }
        
        // 记录成功和失败的条数
        int successCount = 0;
        int failCount = 0;
        List<String> failReasons = new ArrayList<>();
        
        // 按店铺ID分组存储SKU ID
        Map<Long, Set<Long>> shopSkuIdsMap = new HashMap<>();
        
        // 记录有效的店铺ID和mallId对应关系
        Map<String, Long> validShopIdMap = new HashMap<>();
        
        // 对每条记录单独处理
        for (ViolationInfoItem item : violationItems) {
            Long currentShopId = null;
            String itemMallId = item.getMallId();
            
            // 尝试获取当前记录的shopId
            // 1. 优先使用传入的特定shopId参数
            if (shopId != null) {
                currentShopId = shopId;
            }
            // 2. 如果没有传入shopId但有itemMallId，尝试查找对应的店铺
            else if (itemMallId != null && !itemMallId.isEmpty()) {
                Shop shop = shopService.getShopByMallId(itemMallId);
                if (shop != null) {
                    currentShopId = shop.getShopId();
                    // 保存有效的mallId与shopId对应关系
                    validShopIdMap.put(itemMallId, currentShopId);
                    log.info("通过mallId: {} 找到对应的shopId: {}", itemMallId, currentShopId);
                }
            }
            // 3. 如果没有itemMallId但有通用mallId参数，尝试查找对应的店铺
            else if (mallId != null && !mallId.isEmpty()) {
                Shop shop = shopService.getShopByMallId(mallId);
                if (shop != null) {
                    currentShopId = shop.getShopId();
                    // 保存有效的mallId与shopId对应关系
                    validShopIdMap.put(mallId, currentShopId);
                    log.info("通过传入参数mallId: {} 找到对应的shopId: {}", mallId, currentShopId);
                }
            }
            
            // 如果无法获取shopId，则跳过该记录并记录失败原因
            if (currentShopId == null) {
                failCount++;
                failReasons.add("无法为违规编号 " + item.getPunishSn() + " 找到对应的店铺ID");
                log.warn("无法为违规编号 {} 找到对应的店铺ID", item.getPunishSn());
                continue;
            }
            
            // 检查权限
            if (!SecurityUtils.isAdmin()) {
                Long userId = SecurityUtils.getCurrentUserId();
                boolean hasPermission = shopService.checkShopPermission(userId, currentShopId, true);
                if (!hasPermission) {
                    failCount++;
                    failReasons.add("无权操作店铺ID: " + currentShopId + " 的违规信息: " + item.getPunishSn());
                    log.warn("用户 {} 无权操作店铺ID: {} 的违规信息: {}", userId, currentShopId, item.getPunishSn());
                    continue;
                }
            }
            
            // 保存单条违规信息
            boolean infoSaved = saveViolationInfo(item, currentShopId, itemMallId);
            
            // 保存违规详情
            boolean detailSaved = saveViolationDetail(item, currentShopId);
            
            // 记录结果
            if (infoSaved && detailSaved) {
                successCount++;
                
                // 从违规详情中提取sku_id并按shopId分组
                if (item.getDetail() != null && item.getDetail().getMmspurchaseOrderViolationInfoVO() != null) {
                    MmsPurchaseOrderViolationInfoVO mmsVO = item.getDetail().getMmspurchaseOrderViolationInfoVO();
                    if (mmsVO.getProductSkuId() != null) {
                        // 获取或创建该店铺的SKU ID集合
                        Set<Long> shopSkuIds = shopSkuIdsMap.computeIfAbsent(currentShopId, k -> new HashSet<>());
                        shopSkuIds.add(mmsVO.getProductSkuId());
                        log.info("从违规详情中提取SKU ID: {}, 违规编号: {}, 属于店铺ID: {}", 
                                mmsVO.getProductSkuId(), item.getPunishSn(), currentShopId);
                    }
                }
            } else {
                failCount++;
                failReasons.add("保存违规信息或详情失败: " + item.getPunishSn());
            }
        }
        
        // 统计所有需要同步的SKU ID总数
        int totalSkuCount = shopSkuIdsMap.values().stream()
                .mapToInt(Set::size)
                .sum();
        
        // 如果有提取到SKU ID，按店铺分别触发质检数据同步
        if (!shopSkuIdsMap.isEmpty()) {
            log.info("需要同步的店铺数: {}, SKU ID总数: {}", shopSkuIdsMap.size(), totalSkuCount);
            
            // 对每个店铺执行同步
            for (Map.Entry<Long, Set<Long>> entry : shopSkuIdsMap.entrySet()) {
                Long syncShopId = entry.getKey();
                Set<Long> shopSkuIds = entry.getValue();
                
                if (shopSkuIds.isEmpty()) {
                    log.info("店铺ID: {} 没有需要同步的SKU ID", syncShopId);
                    continue;
                }
                
                log.info("开始为店铺ID: {} 同步质检数据, SKU ID数量: {}", syncShopId, shopSkuIds.size());
                try {
                    // 异步执行同步，避免阻塞当前请求
                    final Long finalShopId = syncShopId;
                    final Set<Long> finalSkuIds = new HashSet<>(shopSkuIds);
                    CompletableFuture.runAsync(() -> {
                        try {
                            qualityInspectionSyncService.syncQualityInspectionDataBySkuIds(finalShopId, finalSkuIds);
                            log.info("根据SKU ID同步质检数据完成 - 店铺ID: {}, SKU ID数量: {}", finalShopId, finalSkuIds.size());
                        } catch (Exception e) {
                            log.error("根据SKU ID同步质检数据异常 - 店铺ID: {}", finalShopId, e);
                        }
                    });
                } catch (Exception e) {
                    log.error("触发质检数据同步异常 - 店铺ID: {}", syncShopId, e);
                }
            }
        } else {
            log.info("未从违规详情中提取到任何SKU ID，无需同步质检数据");
        }
        
        // 返回处理结果
        if (failCount == 0) {
            return ApiResponse.success("成功处理 " + successCount + " 条违规信息" + 
                (totalSkuCount > 0 ? "，并为 " + shopSkuIdsMap.size() + " 个店铺触发了 " + totalSkuCount + " 个SKU的质检数据同步" : ""));
        } else if (successCount > 0) {
            return ApiResponse.success("部分成功: 成功 " + successCount + " 条，失败 " + failCount + " 条。" + 
                (totalSkuCount > 0 ? "为 " + shopSkuIdsMap.size() + " 个店铺触发了 " + totalSkuCount + " 个SKU的质检数据同步。" : "") + 
                "失败原因: " + String.join("; ", failReasons));
        } else {
            return ApiResponse.error("全部处理失败。失败原因: " + String.join("; ", failReasons));
        }
    }

    /**
     * 保存单条违规信息
     */
    private boolean saveViolationInfo(ViolationInfoItem item, Long shopId, String mallId) {
        // 创建违规信息对象
        ShopViolationInfo violationInfo = new ShopViolationInfo();
        
        // 设置店铺ID
        violationInfo.setShopId(shopId);
        
        // 设置MallId
        if (mallId != null && !mallId.isEmpty()) {
            violationInfo.setMallId(mallId);
        }
        
        // 设置其他属性
        violationInfo.setPunishSn(item.getPunishSn());
        violationInfo.setPunishTypeDesc(item.getPunishTypeDesc());
        violationInfo.setPunishTypeCode(item.getPunishTypeCode());
        violationInfo.setViolationStartTime(new java.util.Date(item.getViolationStartTime()));
        // 处理金额转换
        if (item.getPunishAmount() != null && !item.getPunishAmount().isEmpty()) {
            violationInfo.setPunishAmount(new BigDecimal(item.getPunishAmount()));
        }
        violationInfo.setPunishAmountCurrency(item.getPunishAmountCurrency());
        violationInfo.setPunishStatus(item.getPunishStatus());
        violationInfo.setViewDetailsStatus(item.getViewDetailsStatus());
        violationInfo.setSubPurchaseOrderSn(item.getSubPurchaseOrderSn());
        violationInfo.setPunishSecondTypeDesc(item.getPunishSecondTypeDesc());
        violationInfo.setPunishFirstTypeDesc(item.getPunishFirstTypeDesc());
        violationInfo.setPunishFirstTypeCode(item.getPunishFirstTypeCode());
        
        // 可选属性，需要判空
        if (item.getPunishAmountDiff() != null) {
            violationInfo.setPunishAmountDiff(item.getPunishAmountDiff());
        }
        if (item.getPunishThirdTypeCode() != null) {
            violationInfo.setPunishThirdTypeCode(item.getPunishThirdTypeCode());
        }
        if (item.getPunishThirdTypeDesc() != null) {
            violationInfo.setPunishThirdTypeDesc(item.getPunishThirdTypeDesc());
        }
        if (item.getVoucher() != null) {
            violationInfo.setVoucher(item.getVoucher());
        }
        if (item.getQcFlawPictureUrlList() != null) {
            violationInfo.setQcFlawPictureUrlList(item.getQcFlawPictureUrlList());
        }
        // 设置showVoucher
        if (item.getShowVoucher() != null) {
            violationInfo.setShowVoucher(item.getShowVoucher());
        }
        if (item.getQualityEventPunishDimension() != null) {
            violationInfo.setQualityEventPunishDimension(item.getQualityEventPunishDimension());
        }
        if (item.getQualityEventPunishDimensionDesc() != null) {
            violationInfo.setQualityEventPunishDimensionDesc(item.getQualityEventPunishDimensionDesc());
        }
        if (item.getProductSkcId() != null) {
            violationInfo.setProductSkcId(item.getProductSkcId());
        }
        
        // 设置倒计时
        if (item.getCountdownTime() != null) {
            violationInfo.setCountdownTime(item.getCountdownTime());
        }
        
        // 检查基础违规信息是否已存在，存在则更新，不存在则新增
        if (violationInfoService.checkViolationInfoExists(shopId, item.getPunishSn())) {
            // 获取现有记录
            ShopViolationInfo existingInfo = violationInfoService.getViolationInfoByShopIdAndPunishSn(shopId, item.getPunishSn());
            if (existingInfo != null) {
                // 设置ID以便更新
                violationInfo.setId(existingInfo.getId());
                log.info("违规信息已存在，更新数据: shopId={}, punishSn={}", shopId, item.getPunishSn());
                return violationInfoService.updateViolationInfo(violationInfo);
            }
        }
        
        // 不存在则新增
        return violationInfoService.addViolationInfo(violationInfo);
    }

    /**
     * 保存单条违规详情
     */
    private boolean saveViolationDetail(ViolationInfoItem item, Long shopId) {
        // 检查是否有详情信息
        if (item.getDetail() == null || item.getDetail().getMmspurchaseOrderViolationInfoVO() == null) {
            log.info("无违规详情信息，跳过保存详情: shopId={}, punishSn={}", shopId, item.getPunishSn());
            return true; // 没有详情视为保存成功
        }
        
        // 不论是否存在，都保存最新数据（service层会处理删除旧数据逻辑）
        List<ViolationInfoItem> items = new ArrayList<>();
        items.add(item);
        boolean result = violationDetailService.batchSaveViolationDetail(shopId, items);
        if (result) {
            log.info("保存违规详情成功: shopId={}, punishSn={}", shopId, item.getPunishSn());
        } else {
            log.error("保存违规详情失败: shopId={}, punishSn={}", shopId, item.getPunishSn());
        }
        return result;
    }

    /**
     * 保留下面的导出方法用于兼容，但将用新的后端导出方法替代
     */
    @PostMapping("/export")
    @RequiresPermission("operation:violation:export")
    public ApiResponse<ShopViolationInfoVO> exportViolationData(@RequestBody Map<String, Object> exportParams) {
        // 获取当前用户ID
        Long userId = SecurityUtils.getCurrentUserId();
        
        try {
            // 解析导出参数
            Map<String, Object> queryParams = (Map<String, Object>) exportParams.get("queryParams");
            
            // 构建查询参数
            ShopViolationInfoQueryDTO queryDTO = new ShopViolationInfoQueryDTO();
            
            // 设置查询参数
            if (queryParams != null) {
                if (queryParams.get("shopIds") != null) {
                    List<?> shopIdList = (List<?>) queryParams.get("shopIds");
                    List<Long> longShopIds = new ArrayList<>();
                    for (Object shopIdObj : shopIdList) {
                        if (shopIdObj instanceof Integer) {
                            longShopIds.add(((Integer) shopIdObj).longValue());
                        } else if (shopIdObj instanceof Long) {
                            longShopIds.add((Long) shopIdObj);
                        } else if (shopIdObj != null) {
                            try {
                                longShopIds.add(Long.valueOf(shopIdObj.toString()));
                            } catch (NumberFormatException e) {
                                log.error("转换shopId失败: {}", shopIdObj, e);
                            }
                        }
                    }
                    queryDTO.setShopIds(longShopIds);
                }
                
                if (queryParams.get("punishSn") != null) {
                    queryDTO.setPunishSn((String) queryParams.get("punishSn"));
                }
                
                if (queryParams.get("subPurchaseOrderSn") != null) {
                    queryDTO.setSubPurchaseOrderSn((String) queryParams.get("subPurchaseOrderSn"));
                }
                
                if (queryParams.get("punishStatus") != null) {
                    Object statusObj = queryParams.get("punishStatus");
                    if (statusObj instanceof Number) {
                        queryDTO.setPunishStatus(((Number) statusObj).intValue());
                    } else if (statusObj != null) {
                        try {
                            queryDTO.setPunishStatus(Integer.valueOf(statusObj.toString()));
                        } catch (Exception e) {
                            log.error("解析punishStatus失败: {}", statusObj, e);
                        }
                    }
                }
                
                if (queryParams.get("violationTimeBegin") != null) {
                    queryDTO.setViolationTimeBegin(DateUtils.parseLocalDateTime((String) queryParams.get("violationTimeBegin")));
                }
                
                if (queryParams.get("violationTimeEnd") != null) {
                    queryDTO.setViolationTimeEnd(DateUtils.parseLocalDateTime((String) queryParams.get("violationTimeEnd")));
                }
                
                // 设置分页参数
                if (queryParams.get("pageNum") != null) {
                    Object pageObj = queryParams.get("pageNum");
                    if (pageObj instanceof Number) {
                        queryDTO.setPageNum(((Number) pageObj).intValue());
                    } else if (pageObj != null) {
                        try {
                            queryDTO.setPageNum(Integer.valueOf(pageObj.toString()));
                        } catch (Exception e) {
                            log.error("解析pageNum失败: {}", pageObj, e);
                        }
                    }
                } else {
                    queryDTO.setPageNum(1); // 默认第一页
                }
                
                if (queryParams.get("pageSize") != null) {
                    Object sizeObj = queryParams.get("pageSize");
                    if (sizeObj instanceof Number) {
                        queryDTO.setPageSize(((Number) sizeObj).intValue());
                    } else if (sizeObj != null) {
                        try {
                            queryDTO.setPageSize(Integer.valueOf(sizeObj.toString()));
                        } catch (Exception e) {
                            log.error("解析pageSize失败: {}", sizeObj, e);
                        }
                    }
                } else {
                    queryDTO.setPageSize(500); // 默认批次大小
                }
            }
            
            // 检查权限
            if (queryDTO.getShopIds() != null && !queryDTO.getShopIds().isEmpty() && !SecurityUtils.isAdmin()) {
                for (Long shopId : queryDTO.getShopIds()) {
                    boolean hasPermission = shopService.checkShopPermission(userId, shopId, false);
                    if (!hasPermission) {
                        return ApiResponse.error("无权访问店铺ID为" + shopId + "的违规信息");
                    }
                }
            }

            // 调用服务获取结果
            ShopViolationInfoVO result = violationInfoService.getViolationInfoListWithDetail(queryDTO);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("导出违规信息数据失败", e);
            return ApiResponse.error("导出数据失败: " + e.getMessage());
        }
    }
}