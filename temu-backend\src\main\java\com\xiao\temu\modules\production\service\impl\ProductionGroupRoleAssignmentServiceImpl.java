package com.xiao.temu.modules.production.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiao.temu.modules.production.dto.ProductionGroupRoleAssignmentDTO;
import com.xiao.temu.modules.production.entity.ProductionGroupRoleAssignment;
import com.xiao.temu.modules.production.mapper.ProductionGroupRoleAssignmentMapper;
import com.xiao.temu.modules.production.service.ProductionGroupRoleAssignmentService;
import com.xiao.temu.modules.system.entity.SysUserRole;
import com.xiao.temu.modules.system.mapper.SysUserRoleMapper;
import com.xiao.temu.security.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * 生产组角色分配服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProductionGroupRoleAssignmentServiceImpl extends ServiceImpl<ProductionGroupRoleAssignmentMapper, ProductionGroupRoleAssignment> implements ProductionGroupRoleAssignmentService {

    private final ProductionGroupRoleAssignmentMapper roleAssignmentMapper;
    private final SysUserRoleMapper userRoleMapper;
    private final PermissionService permissionService;

    @Override
    public IPage<ProductionGroupRoleAssignmentDTO> listAssignments(Long groupId, Long userId, Long roleId, Integer pageNum, Integer pageSize) {
        Page<ProductionGroupRoleAssignmentDTO> page = new Page<>(pageNum, pageSize);
        return roleAssignmentMapper.selectAssignmentList(page, groupId, userId, roleId);
    }

    @Override
    public ProductionGroupRoleAssignmentDTO getAssignmentById(Long id) {
        return roleAssignmentMapper.selectAssignmentById(id);
    }

    @Override
    public boolean addAssignment(ProductionGroupRoleAssignment assignment) {
        // 设置分配时间
        if (assignment.getAssignTime() == null) {
            assignment.setAssignTime(new Date());
        }
        
        // 设置默认状态为正常
        if (assignment.getStatus() == null) {
            assignment.setStatus("0");
        }
        
        return save(assignment);
    }

    @Override
    public boolean updateAssignment(ProductionGroupRoleAssignment assignment) {
        return updateById(assignment);
    }

    @Override
    public boolean changeStatus(Long id, String status) {
        LambdaUpdateWrapper<ProductionGroupRoleAssignment> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProductionGroupRoleAssignment::getId, id)
                    .set(ProductionGroupRoleAssignment::getStatus, status);
        
        return update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAssignment(Long id) {
        // 获取角色分配信息，以便后续从系统角色表中删除
        ProductionGroupRoleAssignmentDTO assignment = roleAssignmentMapper.selectAssignmentById(id);
        boolean result = removeById(id);
        
        // 如果删除成功且角色分配信息存在，尝试从系统角色表中删除
        if (result && assignment != null) {
            try {
                // 从系统角色表中删除该用户的该角色
                // 注意: 根据业务需求，如果您不确定是否要删除系统角色，可以添加额外的条件判断
                // 例如，检查该角色是否只存在于生产组中而不是系统的核心角色
                userRoleMapper.deleteUserRoleByUserIdAndRoleId(assignment.getUserId(), assignment.getRoleId());
                
                // 刷新用户权限缓存
                permissionService.reloadPermission(assignment.getUserId());
            } catch (Exception e) {
                log.error("从系统用户角色表删除角色失败", e);
                // 根据业务需求决定是否抛出异常中断事务
                // throw new RuntimeException("从系统用户角色表删除角色失败", e);
            }
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUserRoles(Long groupId, Long userId) {
        // 在删除生产组角色之前，获取用户在该组的角色ID列表
        List<Long> roleIds = roleAssignmentMapper.selectUserRoleIds(groupId, userId);
        
        // 删除生产组中用户的角色分配
        boolean result = roleAssignmentMapper.deleteUserRoles(groupId, userId) >= 0;
        
        // 如果删除成功且有角色ID，尝试从系统角色表中删除
        if (result && roleIds != null && !roleIds.isEmpty()) {
            try {
                // 对于每个角色ID，从系统角色表中删除
                for (Long roleId : roleIds) {
                    userRoleMapper.deleteUserRoleByUserIdAndRoleId(userId, roleId);
                }
                
                // 刷新用户权限缓存
                permissionService.reloadPermission(userId);
            } catch (Exception e) {
                log.error("从系统用户角色表批量删除角色失败", e);
                // 根据业务需求决定是否抛出异常中断事务
                // throw new RuntimeException("从系统用户角色表批量删除角色失败", e);
            }
        }
        
        return result;
    }

    @Override
    public boolean deleteAllGroupAssignments(Long groupId) {
        return roleAssignmentMapper.deleteAllGroupAssignments(groupId) >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAssignRoles(Long groupId, Long userId, List<Long> roleIds, Long assignBy) {
        // 先删除用户现有的所有角色
        deleteUserRoles(groupId, userId);
        
        // 如果角色ID列表为空，则只需要删除即可
        if (roleIds == null || roleIds.isEmpty()) {
            return true;
        }
        
        // 批量添加新角色
        List<ProductionGroupRoleAssignment> assignments = new ArrayList<>();
        Date now = new Date();
        
        for (Long roleId : roleIds) {
            ProductionGroupRoleAssignment assignment = new ProductionGroupRoleAssignment();
            assignment.setGroupId(groupId);
            assignment.setUserId(userId);
            assignment.setRoleId(roleId);
            assignment.setAssignTime(now);
            assignment.setAssignBy(assignBy);
            assignment.setStatus("0");
            
            assignments.add(assignment);
        }
        
        // 保存到生产组角色分配表
        boolean result = saveBatch(assignments);
        
        // 同步到系统用户角色表(sys_user_role)
        try {
            // 删除用户现有的系统角色关系(可选，取决于您的业务需求)
            // 如果您希望保留用户在系统中的其他角色，可以注释掉这行
            // 如果您希望完全同步生产组角色和系统角色，则保留这行
            // userRoleMapper.deleteUserRoleByUserId(userId);
            
            // 构建用户角色关系
            List<SysUserRole> userRoleList = roleIds.stream()
                    .map(roleId -> {
                        SysUserRole userRole = new SysUserRole();
                        userRole.setUserId(userId);
                        userRole.setRoleId(roleId);
                        return userRole;
                    })
                    .collect(Collectors.toList());
            
            // 批量插入用户角色关系(需要检查是否已存在以避免重复)
            if (!userRoleList.isEmpty()) {
                for (SysUserRole userRole : userRoleList) {
                    // 检查是否已存在该角色关联
                    if (!checkUserRoleExists(userRole.getUserId(), userRole.getRoleId())) {
                        userRoleMapper.insertUserRole(userRole);
                    }
                }
                
                // 刷新用户权限缓存
                permissionService.reloadPermission(userId);
            }
        } catch (Exception e) {
            log.error("同步角色到系统用户角色表失败", e);
            // 根据业务需求决定是否抛出异常中断事务
            // throw new RuntimeException("同步角色到系统用户角色表失败", e);
        }
        
        return result;
    }

    @Override
    public List<Long> getUserRoleIds(Long groupId, Long userId) {
        return roleAssignmentMapper.selectUserRoleIds(groupId, userId);
    }

    @Override
    public boolean checkUserHasRole(Long groupId, Long userId, Long roleId) {
        return roleAssignmentMapper.checkUserHasRole(groupId, userId, roleId) > 0;
    }
    
    @Override
    public Map<Long, List<Long>> batchGetUserRoleIds(Long groupId, List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return new HashMap<>();
        }
        
        Map<Long, List<Long>> result = new HashMap<>();
        
        // 批量查询所有用户的角色ID列表
        List<Map<String, Object>> rolesData = roleAssignmentMapper.batchSelectUserRoleIds(groupId, userIds);
        
        // 将查询结果组织成Map形式
        if (rolesData != null) {
            for (Map<String, Object> data : rolesData) {
                Long userId = Long.valueOf(data.get("user_id").toString());
                Long roleId = Long.valueOf(data.get("role_id").toString());
                
                if (!result.containsKey(userId)) {
                    result.put(userId, new ArrayList<>());
                }
                
                result.get(userId).add(roleId);
            }
        }
        
        // 确保每个请求的userId都有对应的（可能为空）角色列表
        for (Long userId : userIds) {
            if (!result.containsKey(userId)) {
                result.put(userId, new ArrayList<>());
            }
        }
        
        return result;
    }
    
    @Override
    public Map<Long, List<ProductionGroupRoleAssignmentDTO>> batchGetUserRoleAssignments(Long groupId, List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return new HashMap<>();
        }
        
        Map<Long, List<ProductionGroupRoleAssignmentDTO>> result = new HashMap<>();
        
        // 批量查询所有用户的角色分配信息
        List<ProductionGroupRoleAssignmentDTO> assignments = roleAssignmentMapper.batchSelectUserRoleAssignments(groupId, userIds);
        
        // 将查询结果按用户ID分组
        if (assignments != null) {
            for (ProductionGroupRoleAssignmentDTO assignment : assignments) {
                Long userId = assignment.getUserId();
                
                if (!result.containsKey(userId)) {
                    result.put(userId, new ArrayList<>());
                }
                
                result.get(userId).add(assignment);
            }
        }
        
        // 确保每个请求的userId都有对应的（可能为空）角色分配列表
        for (Long userId : userIds) {
            if (!result.containsKey(userId)) {
                result.put(userId, new ArrayList<>());
            }
        }
        
        return result;
    }

    /**
     * 检查用户是否已经拥有指定角色(在系统角色表中)
     * 
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 是否已存在
     */
    private boolean checkUserRoleExists(Long userId, Long roleId) {
        return userRoleMapper.checkUserHasRole(userId, roleId) > 0;
    }
} 