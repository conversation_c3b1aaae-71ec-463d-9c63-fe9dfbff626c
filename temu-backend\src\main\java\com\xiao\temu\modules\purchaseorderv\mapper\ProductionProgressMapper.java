package com.xiao.temu.modules.purchaseorderv.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.purchaseorderv.entity.ProductionProgress;
import com.xiao.temu.modules.purchaseorderv.dto.ProductionProgressDTO;
import com.xiao.temu.modules.purchaseorderv.vo.ProductionProgressVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 生产进度Mapper接口
 */
public interface ProductionProgressMapper extends BaseMapper<ProductionProgress> {

    /**
     * 分页查询生产进度
     *
     * @param page 分页参数
     * @param dto 查询条件
     * @return 分页结果
     */
    IPage<ProductionProgressVO> selectProgressPage(Page<ProductionProgressVO> page, @Param("dto") ProductionProgressDTO dto);

    /**
     * 获取生产进度详情
     *
     * @param shopId 店铺ID
     * @param subPurchaseOrderSn 备货单号
     * @return 生产进度详情
     */
    ProductionProgressVO getProgressDetail(@Param("shopId") Long shopId, @Param("subPurchaseOrderSn") String subPurchaseOrderSn);
    
    /**
     * 批量获取生产进度详情
     *
     * @param orderInfoList 订单信息列表，每个元素包含shopId和subPurchaseOrderSn
     * @return 生产进度详情列表
     */
    List<ProductionProgressVO> batchGetProgressDetails(@Param("orderInfoList") List<Map<String, Object>> orderInfoList);
} 