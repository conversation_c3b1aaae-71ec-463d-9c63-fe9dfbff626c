package com.xiao.temu.modules.product.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiao.temu.modules.product.dto.ProductNewArrivalBatchDTO;
import com.xiao.temu.modules.product.dto.ProductNewArrivalStatsDTO;
import com.xiao.temu.modules.product.dto.ProductNewArrivalStatsQueryDTO;
import com.xiao.temu.modules.product.entity.ProductNewArrival;
import com.xiao.temu.modules.product.entity.ProductNewArrivalSkc;
import com.xiao.temu.modules.product.mapper.ProductNewArrivalMapper;
import com.xiao.temu.modules.product.mapper.ProductNewArrivalSkcMapper;
import com.xiao.temu.modules.product.service.ProductNewArrivalService;
import com.xiao.temu.modules.sales.entity.SalesStatistics;
import com.xiao.temu.modules.sales.service.SalesStatisticsService;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.shop.service.ShopService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商品上新服务实现类
 */
@Service
public class ProductNewArrivalServiceImpl extends ServiceImpl<ProductNewArrivalMapper, ProductNewArrival> implements ProductNewArrivalService {

    private static final Logger logger = LoggerFactory.getLogger(ProductNewArrivalServiceImpl.class);
    
    @Autowired
    private ShopService shopService;
    
    @Autowired
    private ProductNewArrivalSkcMapper productNewArrivalSkcMapper;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private SalesStatisticsService salesStatisticsService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSave(ProductNewArrivalBatchDTO batchDTO) {
        if (batchDTO == null || CollectionUtils.isEmpty(batchDTO.getProducts())) {
            return 0;
        }
        
        // 根据mallId查询shopId
        Shop shop = shopService.getShopByMallId(batchDTO.getMallId());
        if (shop == null) {
            logger.error("店铺不存在, mallId: {}", batchDTO.getMallId());
            return 0;
        }
        
        Long shopId = shop.getShopId();
        int savedCount = 0;
        LocalDateTime now = LocalDateTime.now();
        List<ProductNewArrival> productList = new ArrayList<>();
        List<ProductNewArrivalSkc> skcList = new ArrayList<>();
        
        // 处理商品和SKC数据
        for (ProductNewArrivalBatchDTO.ProductDTO productDTO : batchDTO.getProducts()) {
            if (productDTO == null || productDTO.getProductId() == null) {
                continue;
            }
            
            // 创建商品上新基本信息
            ProductNewArrival product = new ProductNewArrival();
            product.setShopId(shopId);
            product.setProductId(productDTO.getProductId());
            product.setGoodsId(productDTO.getGoodsId());
            product.setProductName(productDTO.getProductName());
            
            // 设置主SKC ID
            product.setSkcId(productDTO.getSkcId());
            
            product.setSyncTime(now);
            product.setCreateTime(now);
            product.setUpdateTime(now);
            productList.add(product);
            
            // 处理SKC数据
            if (!CollectionUtils.isEmpty(productDTO.getSkcs())) {
                for (ProductNewArrivalBatchDTO.SkcDTO skcDTO : productDTO.getSkcs()) {
                    if (skcDTO == null || skcDTO.getSkcId() == null) {
                        continue;
                    }
                    
                    // 如果product没有设置skcId并且这是第一个SKC，则设置为主SKC
                    if (product.getSkcId() == null && productList.size() == 1) {
                        product.setSkcId(skcDTO.getSkcId());
                    }
                    
                    ProductNewArrivalSkc skc = new ProductNewArrivalSkc();
                    skc.setShopId(shopId);
                    skc.setProductId(productDTO.getProductId());
                    skc.setSkcId(skcDTO.getSkcId());
                    skc.setGoodsSkcId(skcDTO.getGoodsSkcId());
                    skc.setSelectId(skcDTO.getSelectId());
                    skc.setColorName(skcDTO.getColorName());
                    skc.setExtCode(skcDTO.getExtCode());
                    
                    // 处理时间状态
                    if (skcDTO.getStatusTime() != null) {
                        skc.setPriceVerificationTime(convertToLocalDateTime(skcDTO.getStatusTime().getPriceVerificationTime()));
                        skc.setAddedToSiteTime(convertToLocalDateTime(skcDTO.getStatusTime().getAddedToSiteTime()));
                        skc.setSamplePostingFinishedTime(convertToLocalDateTime(skcDTO.getStatusTime().getSamplePostingFinishedTime()));
                        skc.setSelectedTime(convertToLocalDateTime(skcDTO.getStatusTime().getSelectedTime()));
                        skc.setFirstPurchaseTime(convertToLocalDateTime(skcDTO.getStatusTime().getFirstPurchaseTime()));
                        skc.setCreatedTime(convertToLocalDateTime(skcDTO.getStatusTime().getCreatedTime()));
                        skc.setQcCompletedTime(convertToLocalDateTime(skcDTO.getStatusTime().getQcCompletedTime()));
                    }
                    
                    skc.setSyncTime(now);
                    skc.setCreateTime(now);
                    skc.setUpdateTime(now);
                    skcList.add(skc);
                }
            }
        }
        
        // 批量保存商品上新基本信息
        if (!productList.isEmpty()) {
            // 使用saveOrUpdateBatch来处理可能存在的记录
            this.saveOrUpdateBatch(productList);
            savedCount += productList.size();
        }
        
        // 批量保存商品上新SKC信息
        if (!skcList.isEmpty()) {
            for (ProductNewArrivalSkc skc : skcList) {
                try {
                    // 使用saveOrUpdate来处理可能存在的记录
                    productNewArrivalSkcMapper.insert(skc);
                } catch (Exception e) {
                    // 记录错误但继续处理其他SKC
                    logger.error("保存SKC信息失败: shopId={}, productId={}, skcId={}, 错误: {}", 
                        skc.getShopId(), skc.getProductId(), skc.getSkcId(), e.getMessage());
                }
            }
            savedCount += skcList.size();
        }
        
        return savedCount;
    }
    
    /**
     * 将时间戳转换为LocalDateTime
     */
    private LocalDateTime convertToLocalDateTime(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
    }
    
    @Override
    public List<ProductNewArrivalStatsDTO> getProductNewArrivalStats(ProductNewArrivalStatsQueryDTO queryDTO) {
        // 计算各个时间段
        LocalDateTime now = LocalDateTime.now();
        
        // 本月开始时间
        LocalDateTime currentMonthStart = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        
        // 本周开始时间（星期一）
        LocalDateTime currentWeekStart = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
                .withHour(0).withMinute(0).withSecond(0).withNano(0);
                
        // 上周开始时间
        LocalDateTime lastWeekStart = currentWeekStart.minusWeeks(1);
        
        // 上周结束时间
        LocalDateTime lastWeekEnd = currentWeekStart.minusSeconds(1);
        
        // 昨天开始时间
        LocalDateTime yesterdayStart = now.minusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        
        // 昨天结束时间
        LocalDateTime yesterdayEnd = now.withHour(0).withMinute(0).withSecond(0).withNano(0).minusSeconds(1);
        
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        // 构建查询条件
        StringBuilder shopIdCondition = new StringBuilder();
        if (!CollectionUtils.isEmpty(queryDTO.getShopIds())) {
            shopIdCondition.append(" AND s.shop_id IN (");
            for (int i = 0; i < queryDTO.getShopIds().size(); i++) {
                if (i > 0) {
                    shopIdCondition.append(",");
                }
                shopIdCondition.append(queryDTO.getShopIds().get(i));
            }
            shopIdCondition.append(")");
        }
        
        StringBuilder mallIdCondition = new StringBuilder();
        if (queryDTO.getMallId() != null) {
            mallIdCondition.append(" AND s.mall_id = '").append(queryDTO.getMallId()).append("'");
        }
        
        Map<Long, ProductNewArrivalStatsDTO> statsMap = new HashMap<>();
        
        // 构建数据权限SQL条件
        StringBuilder dataPermissionCondition = new StringBuilder();
        if (queryDTO.getData_scope() != null && !queryDTO.getData_scope().isEmpty()) {
            dataPermissionCondition.append(" AND (")
                    .append(queryDTO.getData_scope())
                    .append(")");
        }
        
        // 修改查询逻辑，首先查询所有状态正常的店铺（status=0表示正常）
        String shopSql = "SELECT s.shop_id, s.shop_name, s.remark FROM shop s WHERE s.status = '0'" 
                + shopIdCondition + mallIdCondition + dataPermissionCondition;
                
        logger.info("查询店铺的SQL: {}", shopSql);
        List<Map<String, Object>> shopList = jdbcTemplate.queryForList(shopSql);
        
        logger.info("从shop表查询到{}个状态正常的店铺", shopList.size());
        
        // 初始化统计对象，为每个店铺创建初始化的统计DTO
        for (Map<String, Object> shopInfo : shopList) {
            Long shopId = ((Number) shopInfo.get("shop_id")).longValue();
            ProductNewArrivalStatsDTO statsDTO = new ProductNewArrivalStatsDTO();
            statsDTO.setShopId(shopId);
            statsDTO.setShopName((String) shopInfo.get("shop_name"));
            statsDTO.setShopRemark((String) shopInfo.get("remark"));
            
            // 初始化所有计数字段为0
            statsDTO.setCurrentMonthNewCount(0);
            statsDTO.setCurrentWeekNewCount(0);
            statsDTO.setLastWeekNewCount(0);
            statsDTO.setYesterdayNewCount(0);
            statsDTO.setCurrentWeekOnlineCount(0);
            statsDTO.setTodaySales(0);
            statsDTO.setLastWeekSales(0);
            statsDTO.setLastMonthSales(0);
            statsDTO.setTotalProducts(0);
            
            statsMap.put(shopId, statsDTO);
        }
        
        // 获取所有店铺ID列表
        List<Long> shopIds = new ArrayList<>(statsMap.keySet());
        
        if (!shopIds.isEmpty()) {
            // 构建店铺ID的IN子句，用于SQL查询
            String shopIdsInClause = String.join(",", shopIds.stream().map(String::valueOf).toArray(String[]::new));
            
            // 使用一个查询统计所有时间段的上新数量，减少SQL查询次数
            String combinedStatsSql = 
                "SELECT " +
                "   shop_id, " +
                "   COUNT(DISTINCT CASE WHEN created_time >= '" + currentMonthStart.format(formatter) + "' THEN skc_id END) as current_month_new_count, " +
                "   COUNT(DISTINCT CASE WHEN created_time >= '" + currentWeekStart.format(formatter) + "' THEN skc_id END) as current_week_new_count, " +
                "   COUNT(DISTINCT CASE WHEN created_time >= '" + lastWeekStart.format(formatter) + "' AND created_time <= '" + lastWeekEnd.format(formatter) + "' THEN skc_id END) as last_week_new_count, " +
                "   COUNT(DISTINCT CASE WHEN created_time >= '" + yesterdayStart.format(formatter) + "' AND created_time <= '" + yesterdayEnd.format(formatter) + "' THEN skc_id END) as yesterday_new_count, " +
                "   COUNT(DISTINCT CASE WHEN added_to_site_time >= '" + currentWeekStart.format(formatter) + "' THEN skc_id END) as current_week_online_count " +
                "FROM product_new_arrival_skc " +
                "WHERE shop_id IN (" + shopIdsInClause + ") " +
                "GROUP BY shop_id";
            
            logger.info("合并统计查询SQL: {}", combinedStatsSql);
            List<Map<String, Object>> combinedStatsResults = jdbcTemplate.queryForList(combinedStatsSql);
            
            // 填充统计数据
            for (Map<String, Object> row : combinedStatsResults) {
                Long shopId = ((Number) row.get("shop_id")).longValue();
                ProductNewArrivalStatsDTO statsDTO = statsMap.get(shopId);
                
                if (statsDTO != null) {
                    // 更新统计数据
                    if (row.get("current_month_new_count") != null) {
                        statsDTO.setCurrentMonthNewCount(((Number) row.get("current_month_new_count")).intValue());
                    }
                    
                    if (row.get("current_week_new_count") != null) {
                        statsDTO.setCurrentWeekNewCount(((Number) row.get("current_week_new_count")).intValue());
                    }
                    
                    if (row.get("last_week_new_count") != null) {
                        statsDTO.setLastWeekNewCount(((Number) row.get("last_week_new_count")).intValue());
                    }
                    
                    if (row.get("yesterday_new_count") != null) {
                        statsDTO.setYesterdayNewCount(((Number) row.get("yesterday_new_count")).intValue());
                    }
                    
                    if (row.get("current_week_online_count") != null) {
                        statsDTO.setCurrentWeekOnlineCount(((Number) row.get("current_week_online_count")).intValue());
                    }
                }
            }
            
            // 查询店铺所属运营组
            if (!shopIds.isEmpty()) {
                String shopGroupSql = 
                    "SELECT s.shop_id, GROUP_CONCAT(DISTINCT g.group_name SEPARATOR ', ') as group_names " +
                    "FROM shop s " +
                    "LEFT JOIN shop_group_assignment sga ON s.shop_id = sga.shop_id AND sga.status = '0' " +
                    "LEFT JOIN operation_group g ON sga.group_id = g.group_id AND g.status = '0' " +
                    "WHERE s.shop_id IN (" + String.join(",", shopIds.stream().map(String::valueOf).toArray(String[]::new)) + ") " +
                    "GROUP BY s.shop_id";
                    
                logger.info("查询店铺运营组SQL: {}", shopGroupSql);
                
                try {
                    List<Map<String, Object>> groupResults = jdbcTemplate.queryForList(shopGroupSql);
                    for (Map<String, Object> row : groupResults) {
                        Long shopId = ((Number) row.get("shop_id")).longValue();
                        String groupNames = (String) row.get("group_names");
                        
                        ProductNewArrivalStatsDTO statsDTO = statsMap.get(shopId);
                        if (statsDTO != null) {
                            statsDTO.setGroupNames(groupNames);
                            logger.info("店铺[{}]加载运营组数据成功: 所属运营组={}", statsDTO.getShopName(), groupNames);
                        }
                    }
                } catch (Exception e) {
                    logger.error("批量查询店铺运营组数据失败", e);
                }
            }
            
            // 查询每个店铺的销售统计数据并填充 - 恢复使用原来的服务方法
            for (Long shopId : shopIds) {
                try {
                    // 获取当天的销售统计数据
                    SalesStatistics salesStatistics = salesStatisticsService.getSalesStatisticsByShopIdAndDate(shopId, LocalDate.now());
                    ProductNewArrivalStatsDTO statsDTO = statsMap.get(shopId);
                    
                    if (statsDTO != null && salesStatistics != null) {
                        // 填充销售统计数据
                        statsDTO.setTodaySales(salesStatistics.getTodaySales());
                        statsDTO.setLastWeekSales(salesStatistics.getLastWeekSales());
                        statsDTO.setLastMonthSales(salesStatistics.getLastMonthSales());
                        statsDTO.setTotalProducts(salesStatistics.getTotalProducts());
                        logger.info("店铺[{}]加载销售统计数据成功: 今日销量={}, 周销量={}, 月销量={}, 商品总数={}",
                                statsDTO.getShopName(), statsDTO.getTodaySales(), 
                                statsDTO.getLastWeekSales(), statsDTO.getLastMonthSales(), 
                                statsDTO.getTotalProducts());
                    } else if (statsDTO != null) {
                        logger.warn("店铺[{}]无销售统计数据，使用0填充", statsDTO.getShopName());
                        // 已在初始化时设置为0，此处不需要额外操作
                    }
                } catch (Exception e) {
                    logger.error("获取店铺[{}]销售统计数据失败", shopId, e);
                }
            }
        } else {
            logger.warn("没有找到符合条件的店铺");
        }
        
        // 将Map转为List
        List<ProductNewArrivalStatsDTO> resultList = new ArrayList<>(statsMap.values());
        
        // 处理排序
        if (queryDTO.getOrderByColumn() != null && !queryDTO.getOrderByColumn().isEmpty()) {
            String orderByColumn = queryDTO.getOrderByColumn();
            boolean isAsc = "asc".equalsIgnoreCase(queryDTO.getOrderByType());
            
            logger.info("对结果进行排序: 字段={}, 顺序={}", orderByColumn, isAsc ? "升序" : "降序");
            
            resultList.sort((a, b) -> {
                int result = 0;
                try {
                    // 根据排序字段进行排序
                    switch (orderByColumn) {
                        case "shopName":
                            result = compareStringValues(a.getShopName(), b.getShopName());
                            break;
                        case "shopRemark":
                            result = compareStringValues(a.getShopRemark(), b.getShopRemark());
                            break;
                        case "groupNames":
                            result = compareStringValues(a.getGroupNames(), b.getGroupNames());
                            break;
                        case "currentMonthNewCount":
                            result = compareIntValues(a.getCurrentMonthNewCount(), b.getCurrentMonthNewCount());
                            break;
                        case "currentWeekNewCount":
                            result = compareIntValues(a.getCurrentWeekNewCount(), b.getCurrentWeekNewCount());
                            break;
                        case "lastWeekNewCount":
                            result = compareIntValues(a.getLastWeekNewCount(), b.getLastWeekNewCount());
                            break;
                        case "yesterdayNewCount":
                            result = compareIntValues(a.getYesterdayNewCount(), b.getYesterdayNewCount());
                            break;
                        case "currentWeekOnlineCount":
                            result = compareIntValues(a.getCurrentWeekOnlineCount(), b.getCurrentWeekOnlineCount());
                            break;
                        case "todaySales":
                            result = compareIntValues(a.getTodaySales(), b.getTodaySales());
                            break;
                        case "lastWeekSales":
                            result = compareIntValues(a.getLastWeekSales(), b.getLastWeekSales());
                            break;
                        case "lastMonthSales":
                            result = compareIntValues(a.getLastMonthSales(), b.getLastMonthSales());
                            break;
                        case "totalProducts":
                            result = compareIntValues(a.getTotalProducts(), b.getTotalProducts());
                            break;
                        default:
                            logger.warn("未知的排序字段: {}", orderByColumn);
                            break;
                    }
                } catch (Exception e) {
                    logger.error("排序过程中发生错误", e);
                }
                
                // 根据排序方向返回结果
                return isAsc ? result : -result;
            });
        }
        
        return resultList;
    }
    
    /**
     * 比较两个字符串值
     */
    private int compareStringValues(String a, String b) {
        if (a == null && b == null) return 0;
        if (a == null) return -1;
        if (b == null) return 1;
        return a.compareTo(b);
    }
    
    /**
     * 比较两个整数值
     */
    private int compareIntValues(Integer a, Integer b) {
        if (a == null && b == null) return 0;
        if (a == null) return -1;
        if (b == null) return 1;
        return a.compareTo(b);
    }
} 