<template>
  <div class="jit-ship-order-tab">
    <!-- 使用查询条件组件 -->
    <search-form 
      :shops="shops"
      :is-jit="true"
      @query="handleSearchFormQuery"
      @reset="handleSearchFormReset"
    />

    <!-- 添加状态标签页组件 -->
    <status-tabs @statusChange="handleStatusChange" :selected-rows="selectedRows" />

    <!-- 数据表格区域 -->
    <el-table
      v-loading="loading"
      :data="shipOrderList"
      border
      class="order-table"
      row-key="deliveryOrderSn"
      :span-method="objectSpanMethod"
      @selection-change="handleSelectionChange"
    >
      <!-- 新增选择列 -->
      <el-table-column type="selection" width="50" />
      
      <!-- 第一列：店铺名称和备注 -->
      <el-table-column prop="shopName" label="店铺名称" min-width="120">
        <template #default="{ row }">
          <div>
            <div>{{ row.shopName || '-' }}</div>
            <div v-if="row.shopRemark" class="shop-remark">{{ row.shopRemark }}</div>
          </div>
        </template>
      </el-table-column>
      
      <!-- 第二列：发货批次 -->
      <el-table-column prop="expressBatchSn" label="发货批次" min-width="150">
        <template #default="{ row }">
          <div class="batch-info">
            {{ row.expressBatchSn || '-' }}
          </div>
        </template>
      </el-table-column>
      
      <!-- 第三列：物流信息 -->
      <el-table-column label="物流信息" min-width="180">
        <template #default="{ row }">
          <div class="logistics-info">
            <!-- 发货方式 -->
            <div class="logistics-item">
              <span class="logistics-label">发货方式：</span>
              <span>{{ getDeliveryMethodLabel(row.deliveryMethod) }}</span>
            </div>
            <!-- 新增：揽收方式 -->
            <div class="logistics-item">
              <span class="logistics-label">揽收方式：</span>
              <span>-</span>
            </div>
            
            <!-- 物流公司/快递单号 -->
            <template v-if="row.expressCompany || row.expressDeliverySn">
              <div class="logistics-item">
                <span v-if="row.expressCompany" class="logistics-label">物流公司：</span>
                <span v-if="row.expressCompany">{{ row.expressCompany }}</span>
              </div>
              <div v-if="row.expressDeliverySn" class="logistics-item">
                <span class="logistics-label">快递单号：</span>
                <span class="express-sn">{{ row.expressDeliverySn }}</span>
              </div>
            </template>
            
            <!-- 司机信息 -->
            <template v-if="row.deliveryMethod === 1">
              <div v-if="row.driverName" class="logistics-item">
                <span class="logistics-label">司机姓名：</span>
                <span>{{ row.driverName }}</span>
              </div>
              <div v-if="row.plateNumber" class="logistics-item">
                <span class="logistics-label">车牌号：</span>
                <span class="plate-number">{{ row.plateNumber }}</span>
              </div>
              <!-- 新增：联系方式 -->
              <div class="logistics-item">
                <span class="logistics-label">联系方式：</span>
                <span>-</span>
              </div>
            </template>
            
            <!-- 重量回传状态 -->
            <template v-if="row.expressWeightFeedbackStatus === 1">
              <div class="weight-feedback">
                <el-tag size="small" type="success">已回传重量</el-tag>
              </div>
            </template>
            
            <div v-if="!row.expressCompany && !row.expressDeliverySn && !row.driverName && !row.plateNumber" class="no-logistics">
              -
            </div>
          </div>
        </template>
      </el-table-column>
      
      <!-- 第四列：发货单号 -->
      <el-table-column prop="deliveryOrderSn" label="发货单号" min-width="200">
        <template #default="{ row }">
          <div class="delivery-sn-container">
            <div>{{ row.deliveryOrderSn || '-' }}</div>
            
            <!-- 添加标签区域 -->
            <div class="order-tags" v-if="row.purchaseStockType === 1 || row.urgencyType === 1">
              <el-tag size="small" type="warning" v-if="row.purchaseStockType === 1">JIT</el-tag>
              <el-tag size="small" type="danger" v-if="row.urgencyType === 1">加急</el-tag>
            </div>
            
            <!-- 添加时间线组件 -->
            <DeliveryTimeline
              :expect-latest-deliver-time="row.purchaseTime ? (row.purchaseTime + 86401000) : row.expectPickUpGoodsTime"
              :expect-latest-arrival-time="row.purchaseTime ? (row.purchaseTime + 259201000) : row.expectLatestPickTime"
              :deliver-time="row.deliverTime"
              :real-receive-time="row.receiveTime"
              :status="row.status"
            />
          </div>
        </template>
      </el-table-column>
      
      <!-- 第五列：商品信息 -->
      <el-table-column label="商品信息" min-width="280">
        <template #default="{ row }">
          <div class="product-info-container">
            <!-- 商品图片放在左侧 -->
            <div class="product-img-container">
              <img 
                v-if="row.subPurchaseOrderBasicVO && row.subPurchaseOrderBasicVO.productSkcPicture" 
                :src="row.subPurchaseOrderBasicVO.productSkcPicture" 
                alt="商品图片" 
                class="product-img"
                style="cursor:pointer"
                @click="handlePreviewImage(row.subPurchaseOrderBasicVO.productSkcPicture)"
              />
            </div>
            <!-- 右侧商品信息 -->
            <div class="product-detail-container">
              <!-- 第一排：备货单号 -->
              <div class="product-detail-item">
                <span class="product-label">备货单号：</span>
                <span class="product-value">{{ row.subPurchaseOrderBasicVO?.subPurchaseOrderSn || row.subPurchaseOrderSn || '-' }}</span>
              </div>
              <!-- 第二排：名称 -->
              <div class="product-detail-item">
                <span class="product-label">名称：</span>
                <span class="product-value">-</span>
              </div>
              <!-- 第三排：SKC -->
              <div class="product-detail-item">
                <span class="product-label">SKC：</span>
                <span class="product-value">{{ row.subPurchaseOrderBasicVO?.productSkcId || row.productSkcId || '-' }}</span>
              </div>
              <!-- 第四排：货号 -->
              <div class="product-detail-item">
                <span class="product-label">货号：</span>
                <span class="product-value">{{ row.subPurchaseOrderBasicVO?.skcExtCode || row.skcExtCode || '-' }}</span>
              </div>
              <!-- 新增标签区域 -->
              <div class="product-tags" v-if="shouldShowTags(row)">
                <!-- JIT标签：purchaseStockType为1时显示 -->
                <el-tag size="small" type="warning" v-if="row.subPurchaseOrderBasicVO?.purchaseStockType === 1 || row.purchaseStockType === 1">JIT</el-tag>
                <!-- 首单/返单标签：isFirst为true显示首单(绿色)，false显示返单(黄色) -->
                <el-tag size="small" type="success" v-if="row.subPurchaseOrderBasicVO?.isFirst === true || row.isFirst === true">首单</el-tag>
                <el-tag size="small" type="warning" v-if="row.subPurchaseOrderBasicVO?.isFirst === false || row.isFirst === false">返单</el-tag>
                <!-- VMI标签：settlementType为1时显示 -->
                <el-tag size="small" type="info" v-if="row.subPurchaseOrderBasicVO?.settlementType === 1 || row.settlementType === 1">VMI</el-tag>
                <!-- 加急标签：urgencyType为1时显示 -->
                <el-tag size="small" type="danger" v-if="row.subPurchaseOrderBasicVO?.urgencyType === 1 || row.urgencyType === 1">加急</el-tag>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <!-- 第六列：发货信息 -->
      <el-table-column label="发货信息" min-width="220">
        <template #default="{ row }">
          <!-- 发货数量 -->
          <div class="ship-info-item">
            <span class="ship-info-label">发货数量：</span>
            <span class="ship-info-value">{{ row.deliverSkcNum || row.skcPurchaseNum || 0 }}件</span>
          </div>
          
          <!-- 发货仓库 -->
          <div class="ship-info-item">
            <span class="ship-info-label">发货仓库：-</span>
            <!-- <el-tooltip
              effect="light"
              placement="top-start"
              :content="row.shopName + (row.shopRemark ? ' (' + row.shopRemark + ')' : '')"
              :hide-after="0"
            >
              <span class="ship-info-value ellipsis-text">{{ row.shopName || '-' }}</span>
            </el-tooltip> -->
          </div>
          
          <!-- 收货仓库 -->
          <div class="ship-info-item">
            <span class="ship-info-label">收货仓库：</span>
            <el-tooltip
              effect="light"
              placement="top-start"
              :content="row.receiveAddressInfo ? 
                `收货人：${row.receiveAddressInfo.receiverName || '-'}, ${row.receiveAddressInfo.phone || '-'}\n收货地址：${row.receiveAddressInfo.provinceName || ''}${row.receiveAddressInfo.cityName || ''}${row.receiveAddressInfo.districtName || ''}${row.receiveAddressInfo.detailAddress || ''}` : '-'"
              :hide-after="0"
              raw-content
            >
              <span class="ship-info-value ellipsis-text">{{ row.subWarehouseName || (row.receiveAddressInfo ? row.receiveAddressInfo.receiverName : '-') }}</span>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      
      <!-- 第七列：包裹号 -->
      <el-table-column label="包裹号" min-width="180">
        <template #default="{ row }">
          <div v-if="row.packageList && row.packageList.length > 0">
            <div>{{ row.packageList[0].packageSn }} | {{ row.packageList[0].skcNum }}件</div>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      
      <!-- 第八列：已发货/已收包裹数 -->
      <el-table-column label="已发货/已收包裹数" width="150" align="center">
        <template #default="{ row }">
          {{ row.deliverPackageNum || 0 }}/{{ row.receivePackageNum || 0 }}
        </template>
      </el-table-column>
      
      <!-- 第九列：节点时间 -->
      <el-table-column label="节点时间" min-width="250">
        <template #default="{ row }">
          <div class="time-nodes">
            <div class="time-node-item">
              <div class="time-label">发货时间：</div>
              <div class="time-value">{{ row.deliverTime ? formatDate(row.deliverTime) : '-' }}</div>
            </div>
            <div class="time-node-item">
              <div class="time-label">收货时间：</div>
              <div class="time-value">{{ row.receiveTime ? formatDate(row.receiveTime) : '-' }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <!-- 第十列：发货单状态 -->
      <el-table-column label="发货单状态" width="120">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">{{ getStatusLabel(row.status) }}</el-tag>
        </template>
      </el-table-column>
      
      <!-- 操作列 -->
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleDetail(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 替换分页区域为固定在底部的分页组件 -->
    <PaginationBar
      :current-page="queryParams.pageNo" 
      :page-size="queryParams.pageSize"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <ImagePreview 
      :visible="previewImageVisible" 
      :image-url="previewImageUrl" 
      @update:visible="previewImageVisible = $event" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useShipOrderStore } from '@/store'
import type { Shop, ShipOrder } from '@/types/shipOrder'
import { formatTime } from '@/utils/format'
import SearchForm from './components/SearchForm.vue'
import { PaginationBar } from '@/components/temu'  // 导入PaginationBar组件
import StatusTabs from './components/StatusTabs.vue'  // 导入StatusTabs组件
import DeliveryTimeline from '@/components/temu/DeliveryTimeline.vue' // 导入时间线组件
import ImagePreview from '@/components/temu/ImagePreview.vue'

// 定义props
const props = defineProps({
  shops: {
    type: Array as () => Shop[],
    default: () => []
  }
})

// store实例
const shipOrderStore = useShipOrderStore()

// 加载状态
const loading = computed(() => shipOrderStore.loading)

// 查询参数
const queryParams = reactive({
  shopIds: [] as (number | string)[],
  pageNo: 1,
  pageSize: 10,
  isJit: true, // JIT标签页特有参数
  status: undefined as number | undefined, // 添加状态字段
  onlyTaxWarehouseWaitApply: false as boolean | undefined, // 添加新参数并设置可选类型
  productLabelCodeStyle: 0, // 添加商品条码样式参数，0-全选
  // 其他查询参数由SearchForm组件管理
})

// 发货单列表和总数
const shipOrderList = computed(() => {
  // 获取原始数据
  const orders = [...shipOrderStore.shipOrders]
  
  // 按发货时间降序排序（时间越近越靠前）
  return orders.sort((a, b) => {
    // 使用类型断言解决类型检查问题
    const aDeliverTime = (a as any).deliverTime
    const bDeliverTime = (b as any).deliverTime
    
    // 处理可能的空值情况
    if (!aDeliverTime && !bDeliverTime) return 0
    if (!aDeliverTime) return 1  // 没有发货时间的排后面
    if (!bDeliverTime) return -1
    
    // 降序排序，b在前表示降序
    return bDeliverTime - aDeliverTime
  })
})
const total = computed(() => shipOrderStore.total)

// 查询发货单列表
const fetchData = async () => {
  await shipOrderStore.getShipOrderList(queryParams)
}

// 处理查询表单提交
const handleSearchFormQuery = (formData) => {
  // 先保存当前的pageSize
  const currentPageSize = queryParams.pageSize
  
  // 合并表单提交的查询条件和本地的分页参数
  Object.assign(queryParams, formData)
  
  // 确保保留当前的pageSize和设置pageNo为1
  queryParams.pageSize = currentPageSize
  queryParams.pageNo = 1
  
  // 添加店铺选择判断
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请先选择店铺再进行查询')
    return
  }
  
  fetchData()
}

// 处理查询表单重置
const handleSearchFormReset = () => {
  // 保存当前的分页大小
  const currentPageSize = queryParams.pageSize
  
  queryParams.pageNo = 1
  // 保留isJit标志
  queryParams.isJit = true
  // 重置其他参数，但保留分页大小
  queryParams.shopIds = []
  queryParams.status = undefined // 重置状态
  queryParams.pageSize = currentPageSize
  
  // 其他参数将由SearchForm组件自行重置
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  queryParams.pageNo = page
  
  // 添加店铺选择判断
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请先选择店铺再进行查询')
    return
  }
  
  fetchData()
}

// 处理每页数量变化
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  queryParams.pageNo = 1
  
  // 添加店铺选择判断
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请先选择店铺再进行查询')
    return
  }
  
  fetchData()
}

// 查看详情
const handleDetail = (row: ShipOrder) => {
  ElMessage.info('发货单详情功能开发中')
  console.log('查看发货单详情:', row)
}

// 格式化日期
const formatDate = (timestamp: number, format: string = 'YYYY-MM-DD HH:mm:ss') => {
  if (!timestamp) return '-'
  return formatTime(new Date(timestamp), format)
}

// 获取状态标签
const getStatusLabel = (status: number) => {
  const statusMap = {
    0: '待装箱发货',
    1: '待仓库发货',
    2: '已收货',
    5: '已取消',
    6: '部分收货',
    4: '异常'
  }
  return statusMap[status] || '未知'
}

// 获取状态标签类型
const getStatusType = (status: number) => {
  const statusTypeMap = {
    0: 'info',      // 待装箱发货 - 蓝灰色
    1: 'primary',   // 待仓库发货 - 蓝色
    2: 'success',   // 已收货 - 绿色
    5: 'danger',    // 已取消 - 红色
    6: 'warning',   // 部分收货 - 黄色
    4: 'error'      // 异常 - 红色
  }
  return statusTypeMap[status] || ''
}

// 获取发货方式标签
const getDeliveryMethodLabel = (method: number) => {
  const methodMap = {
    0: '快递',
    1: '自送',
    2: '其他'
  }
  return methodMap[method] !== undefined ? methodMap[method] : '未知'
}

// 处理状态变化
const handleStatusChange = (status: number | undefined) => {
  // 更新查询参数中的状态
  queryParams.status = status
  queryParams.pageNo = 1
  
  // 如果状态是待仓库收货，添加参数onlyTaxWarehouseWaitApply: false
  // 注意：这里假设待仓库收货的状态值是1（已发货），根据实际情况可能需要调整
  if (status === 1) {
    queryParams.onlyTaxWarehouseWaitApply = false
  } else {
    // 其他状态不需要传递此参数，将其设为undefined
    queryParams.onlyTaxWarehouseWaitApply = undefined
  }
  
  // 如果已选择店铺，则自动查询
  if (queryParams.shopIds && queryParams.shopIds.length > 0) {
    fetchData()
  } else {
    ElMessage.warning('请先选择店铺再进行查询')
  }
}

// 组件挂载时不再自动加载数据，需要用户手动点击查询按钮
onMounted(() => {
  // 移除自动请求数据的逻辑
})

// 监听店铺列表变化，如果店铺列表为空，则添加提示
watch(() => props.shops, (newVal) => {
  // 只有在组件挂载后且店铺列表为空的情况下才显示警告
  if (newVal && Array.isArray(newVal) && newVal.length === 0) {
    console.warn('JIT标签页收到空的店铺列表')
    ElMessage.warning('没有可用的店铺数据，请确认您是否有权限查看店铺')
  } else if (newVal && Array.isArray(newVal) && newVal.length > 0) {
    console.log('JIT标签页接收到店铺列表，共', newVal.length, '个店铺')
  }
}, { immediate: false }) // 修改为false，仅在props变化时触发

// 存储表格合并信息的数组
const spanArr = ref<number[][]>([])

// 计算合并信息
const getSpanArr = (data: any[]) => {
  spanArr.value = []
  const batchSpans: Record<string, { count: number, firstIndex: number }> = {} // 存储每个批次的跨行信息
  let pos = 0
  
  // 第一步：计算每个批次要合并的行数
  data.forEach((item) => {
    const batchSn = item.expressBatchSn
    if (!batchSpans[batchSn]) {
      batchSpans[batchSn] = {
        count: 1,
        firstIndex: pos
      }
    } else {
      batchSpans[batchSn].count++
    }
    pos++
  })
  
  // 第二步：构建合并数组 - 注意数组中的索引对应调整后的列
  // 我们只关心三列：店铺名称、发货批次、物流信息
  // 由于表格增加了选择列，所以实际列索引为1、2、3，但spanArr中的索引仍为0、1、2
  for (let i = 0; i < data.length; i++) {
    spanArr.value.push([1, 1, 1]) // 对应三列：店铺名称、发货批次、物流信息
  }
  
  // 设置合并信息
  Object.keys(batchSpans).forEach(batchSn => {
    const { count, firstIndex } = batchSpans[batchSn]
    
    // 只有当一个批次有多行时才合并
    if (count > 1) {
      // 首行设置跨行数，其余行不显示
      spanArr.value[firstIndex][0] = count // 店铺名称列跨行数
      spanArr.value[firstIndex][1] = count // 发货批次列跨行数
      spanArr.value[firstIndex][2] = count // 物流信息列跨行数
      
      // 将其余行对应列的跨行数设为0
      for (let i = 1; i < count; i++) {
        spanArr.value[firstIndex + i][0] = 0 // 店铺名称列不显示
        spanArr.value[firstIndex + i][1] = 0 // 发货批次列不显示
        spanArr.value[firstIndex + i][2] = 0 // 物流信息列不显示
      }
    }
  })
}

// 表格单元格合并方法
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  // 只有当表格有数据且合并信息已计算时才应用合并逻辑
  if (shipOrderList.value && shipOrderList.value.length > 0 && spanArr.value.length > 0) {
    // 检查当前的状态标签页，如果是全部、已取消、已收货、待装箱发货，则不进行单元格合并
    // 全部(all)、已取消(5)、已收货(2)、待装箱发货(0)
    if (queryParams.status === undefined || // 全部
        queryParams.status === 5 ||        // 已取消
        queryParams.status === 2 ||        // 已收货
        queryParams.status === 6 ||        // 部分收货
        queryParams.status === 0) {        // 待装箱发货
      return; // 不合并单元格
    }
    
    // 排除选择列（columnIndex === 0），从columnIndex === 1开始应用合并逻辑
    // 对店铺名称(1)、发货批次(2)和物流信息(3)三列应用合并逻辑
    if (columnIndex === 1 || columnIndex === 2 || columnIndex === 3) {
      const colIndex = columnIndex - 1; // 由于Selection列的存在，需要减1来匹配spanArr的索引
      const rowspan = spanArr.value[rowIndex][colIndex]
      const colspan = rowspan > 0 ? 1 : 0
      
      return {
        rowspan,
        colspan
      }
    }
  }
}

// 监听数据变化，重新计算合并信息
watch(() => shipOrderList.value, (newData) => {
  if (newData && newData.length > 0) {
    // 检查当前状态，只有在需要合并的状态下才计算合并信息
    // 如果是全部、已取消、已收货、待装箱发货，则不计算合并信息
    if (queryParams.status !== undefined && // 不是全部
        queryParams.status !== 5 &&        // 不是已取消
        queryParams.status !== 2 &&        // 不是已收货
        queryParams.status !== 0) {        // 不是待装箱发货
      getSpanArr(newData)
    } else {
      // 重置合并信息
      spanArr.value = []
    }
  } else {
    spanArr.value = []
  }
}, { immediate: true })

// 查看包裹详情
const viewPackageDetails = (row: ShipOrder) => {
  ElMessage.info('包裹详情功能开发中')
  console.log('查看包裹详情:', (row as any).packageList)
}

// 判断是否应该显示标签区域
const shouldShowTags = (row) => {
  const basicVO = row.subPurchaseOrderBasicVO || row
  return (
    basicVO.purchaseStockType === 1 || 
    basicVO.isFirst === true || 
    basicVO.isFirst === false || 
    basicVO.settlementType === 1 || 
    basicVO.urgencyType === 1
  )
}

const previewImageVisible = ref(false)
const previewImageUrl = ref('')
function handlePreviewImage(url: string) {
  previewImageUrl.value = url
  previewImageVisible.value = true
}

// 选中行数据
const selectedRows = ref<ShipOrder[]>([])

// 选择变化事件
function handleSelectionChange(val: ShipOrder[]) {
  selectedRows.value = val
  // 后续操作待定
}
</script>

<style scoped lang="scss">
.jit-ship-order-tab {
  padding: 20px;
}

.order-table {
  margin-top: 10px;
}

.shop-remark {
  margin-left: 0;
  color: #409EFF;
  font-size: 12px;
  margin-top: 2px;
}

// 删除旧的时间线样式
// 添加发货单号时间线的样式
.delivery-sn-container {
  margin-top: 5px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  
  .order-tags {
    display: flex;
    gap: 5px;
    margin: 3px 0;
  }
  
  .sub-text {
    color: #909399;
    font-size: 12px;
  }
}

.logistics-info {
  .logistics-item {
    margin-bottom: 5px;
    line-height: 1.5;
    display: flex;
    flex-wrap: wrap;
    
    .logistics-label {
      color: #909399;
      margin-right: 5px;
      min-width: 70px;
    }
  }

  .express-sn {
    display: block;
    word-break: break-all;
    color: #409EFF;
  }

  .plate-number {
    font-weight: 500;
  }

  .weight-feedback {
    margin-top: 8px;
  }

  .no-logistics {
    color: #909399;
  }
}

.address-text {
  word-break: break-all;
}

.time-nodes {
  .time-node-item {
    display: flex;
    margin-bottom: 3px;
    align-items: center;
    
    .time-label {
      color: #909399;
      margin-right: 5px;
      flex-shrink: 0;
    }
    .time-value {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 160px;
      display: inline-block;
    }
  }
}

/* 商品信息样式 */
.product-info-container {
  display: flex;
  align-items: flex-start;
}

.product-img-container {
  flex: 0 0 60px;
  margin-right: 10px;
}

.product-img {
  width: 60px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #eee;
}

.product-detail-container {
  flex: 1;
}

.product-detail-item {
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-label {
  color: #909399;
}

.product-value {
  color: #303133;
  font-weight: 500;
}

/* 发货信息样式 */
.ship-info-item {
  margin-bottom: 6px;
  font-size: 12px;
  line-height: 1.4;
}

.ship-info-label {
  color: #909399;
  display: inline-block;
  width: 70px;
}

.ship-info-value {
  color: #303133;
  font-weight: 500;
}

.ellipsis-text {
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  vertical-align: bottom;
}

/* 商品标签样式 */
.product-tags {
  margin-top: 5px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.product-tags .el-tag--info {
  background-color: #409EFF;
  border-color: #409EFF;
  color: white;
}
</style> 