/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : temu_api

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 04/06/2025 01:23:00
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for production_group_shop_assignment
-- ----------------------------
DROP TABLE IF EXISTS `production_group_shop_assignment`;
CREATE TABLE `production_group_shop_assignment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `group_id` bigint NOT NULL COMMENT '生产组ID',
  `assign_time` datetime NULL DEFAULT NULL COMMENT '分配时间',
  `assign_by` bigint NULL DEFAULT NULL COMMENT '分配人ID',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_group_shop`(`group_id` ASC, `shop_id` ASC) USING BTREE,
  INDEX `idx_shop`(`shop_id` ASC) USING BTREE,
  INDEX `idx_group`(`group_id` ASC) USING BTREE,
  CONSTRAINT `fk_production_shop_assignment_group` FOREIGN KEY (`group_id`) REFERENCES `production_group` (`group_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_production_shop_assignment_shop` FOREIGN KEY (`shop_id`) REFERENCES `shop` (`shop_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '生产组店铺分配表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of production_group_shop_assignment
-- ----------------------------
INSERT INTO `production_group_shop_assignment` VALUES (1, 5, 3, '2025-06-04 00:14:44', 1, '0');
INSERT INTO `production_group_shop_assignment` VALUES (2, 6, 3, '2025-06-04 00:14:44', 1, '0');

SET FOREIGN_KEY_CHECKS = 1;
