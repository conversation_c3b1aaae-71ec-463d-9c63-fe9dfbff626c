package com.xiao.temu.modules.violation.vo;

import lombok.Data;

import java.util.List;

/**
 * 备货单违规详情
 */
@Data
public class MmsPurchaseOrderViolationInfoVO {
    
    /**
     * 备货单号
     */
    private String subPurchaseOrderSn;
    
    /**
     * 商品SKU ID
     */
    private Long productSkuId;
    
    /**
     * 库存数量
     */
    private Integer stockQuantity;
    
    /**
     * 缺货数量
     */
    private Integer lackQuantity;
    
    /**
     * 不合格数量
     */
    private Integer unqualifiedQuantity;
    
    /**
     * 补货备货单列表
     */
    private List<Object> supplementSubPurchaseOrderList;
} 