package com.xiao.temu.modules.message.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiao.temu.common.constant.MessageConstants;
import com.xiao.temu.modules.message.dto.MessageDetailVO;
import com.xiao.temu.modules.message.dto.MessageQueryDTO;
import com.xiao.temu.modules.message.dto.SendMessageDTO;
import com.xiao.temu.modules.message.dto.UnreadCountVO;
import com.xiao.temu.modules.message.entity.SysMessage;
import com.xiao.temu.modules.message.entity.SysMessageTemplate;
import com.xiao.temu.modules.message.entity.SysMessageUser;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.operation.mapper.GroupMemberMapper;
import com.xiao.temu.modules.production.mapper.ProductionGroupMemberMapper;
import com.xiao.temu.modules.message.mapper.SysMessageMapper;
import com.xiao.temu.modules.message.mapper.SysMessageTemplateMapper;
import com.xiao.temu.modules.message.mapper.SysMessageUserMapper;
import com.xiao.temu.modules.system.mapper.SysRoleMapper;
import com.xiao.temu.modules.system.mapper.SysUserMapper;
import com.xiao.temu.modules.message.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;
import org.apache.velocity.tools.generic.DateTool;

import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 消息服务实现类
 */
@Slf4j
@Service
public class MessageServiceImpl extends ServiceImpl<SysMessageMapper, SysMessage> implements MessageService {

    @Autowired
    private SysMessageMapper messageMapper;

    @Autowired
    private SysMessageUserMapper messageUserMapper;

    @Autowired
    private SysMessageTemplateMapper messageTemplateMapper;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private GroupMemberMapper groupMemberMapper;

    @Autowired
    private ProductionGroupMemberMapper productionGroupMemberMapper;

    @Autowired(required = false)
    private SimpMessagingTemplate messagingTemplate;
    
    // 初始化Velocity引擎
    private final VelocityEngine velocityEngine;
    
    public MessageServiceImpl() {
        velocityEngine = new VelocityEngine();
        velocityEngine.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath");
        velocityEngine.setProperty("classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
        velocityEngine.setProperty("input.encoding", "UTF-8");
        velocityEngine.setProperty("output.encoding", "UTF-8");
        velocityEngine.init();
    }

    /**
     * 发送消息
     *
     * @param dto        发送消息DTO
     * @param fromUserId 发送人ID
     * @return 消息ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long sendMessage(SendMessageDTO dto, Long fromUserId) {
        // 1. 处理使用模板的情况
        if (Boolean.TRUE.equals(dto.getUseTemplate()) && StringUtils.isNotBlank(dto.getTemplateCode())) {
            processMessageTemplate(dto);
        }

        // 2. 保存消息
        SysMessage message = new SysMessage();
        BeanUtils.copyProperties(dto, message);
        message.setFromUserId(fromUserId);
        message.setCreateTime(LocalDateTime.now());
        message.setPublishTime(dto.getPublishTime() != null ? dto.getPublishTime() : LocalDateTime.now());
        message.setReadStatus(MessageConstants.READ_STATUS_UNREAD);
        message.setDeleted(MessageConstants.DELETED_NO);
        
        messageMapper.insert(message);
        
        // 3. 根据接收对象类型获取接收用户列表
        List<Long> receiverUserIds = getReceiverUserIds(dto.getTargetType(), dto.getTargetIds());
        
        // 4. 保存消息接收记录
        if (!receiverUserIds.isEmpty()) {
            saveMessageReceivers(message.getMessageId(), receiverUserIds);
        }
        
        // 5. 发送WebSocket通知
        if (messagingTemplate != null) {
            try {
                sendWebSocketNotification(message, receiverUserIds);
            } catch (Exception e) {
                log.error("发送WebSocket消息通知失败", e);
            }
        }
        
        return message.getMessageId();
    }

    /**
     * 查询消息列表
     *
     * @param query  查询条件
     * @param userId 用户ID
     * @return 消息列表分页数据
     */
    @Override
    public IPage<SysMessage> getMessageList(MessageQueryDTO query, Long userId) {
        IPage<SysMessage> page = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<SysMessage> result = messageMapper.selectMessageList(page, query, userId);
        
        // 确保返回结果中的readStatus字段是从sys_message_user表中获取的
        if (result != null && result.getRecords() != null && !result.getRecords().isEmpty()) {
            for (SysMessage message : result.getRecords()) {
                // 查询用户消息关联信息以获取正确的阅读状态
                LambdaQueryWrapper<SysMessageUser> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SysMessageUser::getMessageId, message.getMessageId())
                        .eq(SysMessageUser::getUserId, userId)
                        .eq(SysMessageUser::getDeleted, MessageConstants.DELETED_NO);
                
                SysMessageUser messageUser = messageUserMapper.selectOne(queryWrapper);
                if (messageUser != null) {
                    // 设置正确的阅读状态
                    message.setReadStatus(messageUser.getReadStatus());
                    message.setReadTime(messageUser.getReadTime());
                }
            }
        }
        
        return result;
    }

    /**
     * 获取消息详情
     *
     * @param messageId 消息ID
     * @param userId    用户ID
     * @return 消息详情
     */
    @Override
    public MessageDetailVO getMessageDetail(Long messageId, Long userId) {
        // 查询消息
        SysMessage message = messageMapper.selectById(messageId);
        if (message == null) {
            return null;
        }
        
        // 查询用户消息关联是否存在
        LambdaQueryWrapper<SysMessageUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysMessageUser::getMessageId, messageId)
                .eq(SysMessageUser::getUserId, userId)
                .eq(SysMessageUser::getDeleted, MessageConstants.DELETED_NO);
        
        SysMessageUser messageUser = messageUserMapper.selectOne(queryWrapper);
        if (messageUser == null) {
            return null;
        }
        
        // 构建返回对象
        MessageDetailVO vo = new MessageDetailVO();
        BeanUtils.copyProperties(message, vo);
        
        // 设置消息类型名称
        if (MessageConstants.MESSAGE_TYPE_SYSTEM.equals(message.getMessageType())) {
            vo.setMessageTypeName("系统消息");
        } else if (MessageConstants.MESSAGE_TYPE_TASK.equals(message.getMessageType())) {
            vo.setMessageTypeName("任务提醒");
        } else if (MessageConstants.MESSAGE_TYPE_SHOP.equals(message.getMessageType())) {
            vo.setMessageTypeName("店铺消息");
        }
        
        // 设置重要程度名称
        if (MessageConstants.IMPORTANCE_NORMAL.equals(message.getImportance())) {
            vo.setImportanceName("普通");
        } else if (MessageConstants.IMPORTANCE_IMPORTANT.equals(message.getImportance())) {
            vo.setImportanceName("重要");
        } else if (MessageConstants.IMPORTANCE_URGENT.equals(message.getImportance())) {
            vo.setImportanceName("紧急");
        }
        
        // 设置发送人名称
        if (message.getFromUserId() != null) {
            SysUser fromUser = userMapper.selectById(message.getFromUserId());
            if (fromUser != null) {
                vo.setFromUserName(fromUser.getNickName());
            }
        }
        
        // 如果是未读，则标记为已读
        if (MessageConstants.READ_STATUS_UNREAD.equals(messageUser.getReadStatus())) {
            markAsRead(messageId, userId);
        }
        
        return vo;
    }

    /**
     * 标记消息已读
     *
     * @param messageId 消息ID
     * @param userId    用户ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAsRead(Long messageId, Long userId) {
        LambdaUpdateWrapper<SysMessageUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysMessageUser::getMessageId, messageId)
                .eq(SysMessageUser::getUserId, userId)
                .eq(SysMessageUser::getDeleted, MessageConstants.DELETED_NO)
                .set(SysMessageUser::getReadStatus, MessageConstants.READ_STATUS_READ)
                .set(SysMessageUser::getReadTime, LocalDateTime.now())
                .set(SysMessageUser::getUpdateTime, LocalDateTime.now());
        
        return messageUserMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 批量标记已读
     *
     * @param messageIds 消息ID列表
     * @param userId     用户ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchMarkAsRead(List<Long> messageIds, Long userId) {
        if (messageIds == null || messageIds.isEmpty()) {
            return false;
        }
        
        LambdaUpdateWrapper<SysMessageUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(SysMessageUser::getMessageId, messageIds)
                .eq(SysMessageUser::getUserId, userId)
                .eq(SysMessageUser::getDeleted, MessageConstants.DELETED_NO)
                .set(SysMessageUser::getReadStatus, MessageConstants.READ_STATUS_READ)
                .set(SysMessageUser::getReadTime, LocalDateTime.now())
                .set(SysMessageUser::getUpdateTime, LocalDateTime.now());
        
        return messageUserMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 删除消息
     *
     * @param messageId 消息ID
     * @param userId    用户ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMessage(Long messageId, Long userId) {
        LambdaUpdateWrapper<SysMessageUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysMessageUser::getMessageId, messageId)
                .eq(SysMessageUser::getUserId, userId)
                .set(SysMessageUser::getDeleted, MessageConstants.DELETED_YES)
                .set(SysMessageUser::getUpdateTime, LocalDateTime.now());
        
        return messageUserMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 批量删除消息
     *
     * @param messageIds 消息ID列表
     * @param userId     用户ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteMessage(List<Long> messageIds, Long userId) {
        if (messageIds == null || messageIds.isEmpty()) {
            return false;
        }
        
        LambdaUpdateWrapper<SysMessageUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(SysMessageUser::getMessageId, messageIds)
                .eq(SysMessageUser::getUserId, userId)
                .set(SysMessageUser::getDeleted, MessageConstants.DELETED_YES)
                .set(SysMessageUser::getUpdateTime, LocalDateTime.now());
        
        return messageUserMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 获取未读消息数量
     *
     * @param userId 用户ID
     * @return 未读消息数量统计
     */
    @Override
    public UnreadCountVO getUnreadCount(Long userId) {
        return messageUserMapper.getUnreadCount(userId);
    }

    /**
     * 获取消息模板
     *
     * @param templateCode 模板编码
     * @return 消息模板
     */
    @Override
    public SysMessageTemplate getMessageTemplate(String templateCode) {
        return messageTemplateMapper.selectByTemplateCode(templateCode);
    }

    /**
     * 处理消息模板
     *
     * @param dto 发送消息DTO
     */
    private void processMessageTemplate(SendMessageDTO dto) {
        SysMessageTemplate template = messageTemplateMapper.selectByTemplateCode(dto.getTemplateCode());
        if (template == null) {
            return;
        }

        // 如果是Velocity模板，使用Velocity引擎处理
        if (template.getContentTemplate() != null && 
            (template.getContentTemplate().contains("#foreach") || 
             template.getContentTemplate().contains("$dateTool"))) {
            
            try {
                // 解析模板参数
                Map<String, Object> params = null;
                if (StringUtils.isNotBlank(dto.getTemplateParams())) {
                    ObjectMapper mapper = new ObjectMapper();
                    params = mapper.readValue(dto.getTemplateParams(), Map.class);
                }
                
                // 处理标题
                String processedTitle = processVelocityTemplate(template.getTitleTemplate(), params);
                dto.setTitle(processedTitle);
                
                // 处理内容
                String processedContent = processVelocityTemplate(template.getContentTemplate(), params);
                dto.setContent(processedContent);
                
                // 设置消息类型
                dto.setMessageType(template.getTemplateType());
                return;
            } catch (Exception e) {
                log.error("处理Velocity模板失败", e);
                // 出错时，使用原始模板
                dto.setTitle(template.getTitleTemplate());
                dto.setContent(template.getContentTemplate());
                dto.setMessageType(template.getTemplateType());
                return;
            }
        }

        // 替换标题模板中的变量
        String titleTemplate = template.getTitleTemplate();
        String contentTemplate = template.getContentTemplate();

        if (StringUtils.isNotBlank(dto.getTemplateParams())) {
            try {
                JSONObject params = JSON.parseObject(dto.getTemplateParams());
                // 替换标题和内容中的变量 ${xxx}
                Pattern pattern = Pattern.compile("\\$\\{(.*?)\\}");
                
                // 处理标题
                Matcher titleMatcher = pattern.matcher(titleTemplate);
                StringBuffer titleBuffer = new StringBuffer();
                while (titleMatcher.find()) {
                    String key = titleMatcher.group(1);
                    String value = params.getString(key);
                    titleMatcher.appendReplacement(titleBuffer, value != null ? value : "");
                }
                titleMatcher.appendTail(titleBuffer);
                dto.setTitle(titleBuffer.toString());
                
                // 处理内容
                Matcher contentMatcher = pattern.matcher(contentTemplate);
                StringBuffer contentBuffer = new StringBuffer();
                while (contentMatcher.find()) {
                    String key = contentMatcher.group(1);
                    String value = params.getString(key);
                    contentMatcher.appendReplacement(contentBuffer, value != null ? value : "");
                }
                contentMatcher.appendTail(contentBuffer);
                dto.setContent(contentBuffer.toString());
                
                // 设置消息类型
                dto.setMessageType(template.getTemplateType());
            } catch (Exception e) {
                log.error("解析模板参数失败", e);
            }
        }
    }
    
    /**
     * 使用Velocity引擎处理模板
     *
     * @param template 模板内容
     * @param params   模板参数
     * @return 处理后的内容
     */
    private String processVelocityTemplate(String template, Map<String, Object> params) {
        if (StringUtils.isBlank(template)) {
            return "";
        }
        
        VelocityContext context = new VelocityContext();
        
        // 添加日期工具
        context.put("dateTool", new DateTool());
        
        // 添加直接处理日期的工具方法
        context.put("formatDate", new FormatDateTool());
        
        // 添加其他参数
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                context.put(entry.getKey(), entry.getValue());
            }
        }
        
        // 处理日期字段，确保日期对象正确传递
        if (params != null && params.containsKey("shopGroups")) {
            Object shopGroupsObj = params.get("shopGroups");
            if (shopGroupsObj instanceof List) {
                List<?> shopGroups = (List<?>) shopGroupsObj;
                for (Object groupObj : shopGroups) {
                    if (groupObj instanceof Map) {
                        Map<?, ?> group = (Map<?, ?>) groupObj;
                        if (group.containsKey("items") && group.get("items") instanceof List) {
                            List<?> items = (List<?>) group.get("items");
                            for (Object itemObj : items) {
                                if (itemObj instanceof Map) {
                                    Map<String, Object> item = (Map<String, Object>) itemObj;
                                    // 转换日期字段为Date对象
                                    convertDateField(item, "expectLatestArrivalTime");
                                    convertDateField(item, "purchaseTime");
                                    convertDateField(item, "shippingTime");
                                }
                            }
                        }
                    }
                }
            }
        }
        
        StringWriter writer = new StringWriter();
        velocityEngine.evaluate(context, writer, "Template", template);
        
        return writer.toString();
    }
    
    /**
     * 转换日期字段为Date对象
     * @param item Map对象
     * @param fieldName 字段名
     */
    private void convertDateField(Map<String, Object> item, String fieldName) {
        if (item.containsKey(fieldName) && item.get(fieldName) != null) {
            Object dateObj = item.get(fieldName);
            if (dateObj instanceof Long) {
                // 如果是时间戳，转换为Date对象
                item.put(fieldName, new Date((Long) dateObj));
            } else if (dateObj instanceof String) {
                try {
                    // 尝试解析日期字符串
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    item.put(fieldName, sdf.parse((String) dateObj));
                } catch (Exception e) {
                    log.warn("无法解析日期字符串: {}", dateObj);
                }
            }
            // 如果已经是Date类型，则不需要转换
        }
    }
    
    /**
     * 日期格式化工具类
     */
    public static class FormatDateTool {
        /**
         * 格式化日期
         * @param pattern 日期格式
         * @param date 日期对象
         * @return 格式化后的日期字符串
         */
        public String format(String pattern, Object date) {
            if (date == null) {
                return "";
            }
            
            Date dateObj = null;
            if (date instanceof Date) {
                dateObj = (Date) date;
            } else if (date instanceof Long) {
                dateObj = new Date((Long) date);
            } else if (date instanceof String) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    dateObj = sdf.parse((String) date);
                } catch (Exception e) {
                    return String.valueOf(date);
                }
            } else {
                return String.valueOf(date);
            }
            
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            return sdf.format(dateObj);
        }
    }

    /**
     * 获取接收用户ID列表
     *
     * @param targetType 接收对象类型
     * @param targetIds  接收对象ID字符串
     * @return 用户ID列表
     */
    private List<Long> getReceiverUserIds(String targetType, String targetIds) {
        List<Long> userIds = new ArrayList<>();
        
        if (MessageConstants.TARGET_TYPE_ALL.equals(targetType)) {
            // 全部用户
            List<SysUser> allUsers = userMapper.selectList(null);
            userIds = allUsers.stream().map(SysUser::getUserId).collect(Collectors.toList());
        } else if (MessageConstants.TARGET_TYPE_USER.equals(targetType) && StringUtils.isNotBlank(targetIds)) {
            // 指定用户
            String[] ids = targetIds.split(",");
            for (String id : ids) {
                if (StringUtils.isNumeric(id)) {
                    userIds.add(Long.valueOf(id));
                }
            }
            // 记录日志以便于排查问题
            log.info("收到用户直接指定的接收者IDs: {}", userIds);
        } else if (MessageConstants.TARGET_TYPE_ROLE.equals(targetType) && StringUtils.isNotBlank(targetIds)) {
            // 指定角色
            String[] roleIds = targetIds.split(",");
            for (String roleId : roleIds) {
                if (StringUtils.isNumeric(roleId)) {
                    // 查询角色下的用户
                    List<Long> roleUserIds = userMapper.selectUserIdsByRoleId(Long.valueOf(roleId));
                    userIds.addAll(roleUserIds);
                }
            }
        } else if (MessageConstants.TARGET_TYPE_GROUP.equals(targetType) && StringUtils.isNotBlank(targetIds)) {
            // 运营组
            String[] groupIds = targetIds.split(",");
            for (String groupId : groupIds) {
                if (StringUtils.isNumeric(groupId)) {
                    // 查询运营组下的用户
                    List<Long> groupUserIds = groupMemberMapper.selectUserIdsByGroupId(Long.valueOf(groupId));
                    userIds.addAll(groupUserIds);
                    
                    // 查看这个组ID是否也存在于生产组中
                    try {
                        // 查询生产组下的用户
                        List<Long> productionGroupUserIds = productionGroupMemberMapper.selectUserIdsByGroupId(Long.valueOf(groupId));
                        if (productionGroupUserIds != null && !productionGroupUserIds.isEmpty()) {
                            log.info("找到生产组{}的{}名成员", groupId, productionGroupUserIds.size());
                            userIds.addAll(productionGroupUserIds);
                        }
                    } catch (Exception e) {
                        log.error("查询生产组{}的成员时出错: {}", groupId, e.getMessage());
                    }
                }
            }
        }
        
        // 去重
        List<Long> distinctUserIds = userIds.stream().distinct().collect(Collectors.toList());
        log.info("最终接收者人数: {}人", distinctUserIds.size());
        return distinctUserIds;
    }

    /**
     * 保存消息接收记录
     *
     * @param messageId 消息ID
     * @param userIds   用户ID列表
     */
    private void saveMessageReceivers(Long messageId, List<Long> userIds) {
        if (userIds.isEmpty()) {
            return;
        }
        
        // 分批处理，避免SQL过长
        int batchSize = 100;
        int totalSize = userIds.size();
        
        for (int i = 0; i < totalSize; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalSize);
            List<Long> batchUserIds = userIds.subList(i, endIndex);
            
            Long[] userIdArray = batchUserIds.toArray(new Long[0]);
            messageUserMapper.batchInsert(messageId, userIdArray);
        }
    }

    /**
     * 发送WebSocket通知
     *
     * @param message  消息对象
     * @param userIds  用户ID列表
     */
    private void sendWebSocketNotification(SysMessage message, List<Long> userIds) {
        if (messagingTemplate == null) {
            return;
        }
        
        Map<String, Object> payload = Map.of(
            "messageId", message.getMessageId(),
            "title", message.getTitle(),
            "content", message.getContent(),
            "messageType", message.getMessageType(),
            "importance", message.getImportance(),
            "createTime", message.getCreateTime()
        );
        
        // 针对全部用户发送广播消息
        if (MessageConstants.TARGET_TYPE_ALL.equals(message.getTargetType())) {
            messagingTemplate.convertAndSend(MessageConstants.WS_TOPIC_MESSAGE, payload);
        } else {
            // 针对特定用户发送点对点消息
            for (Long userId : userIds) {
                String destination = String.format(MessageConstants.WS_USER_DESTINATION, userId);
                messagingTemplate.convertAndSendToUser(String.valueOf(userId), destination, payload);
            }
        }
    }
} 