/**
 * 用户角色管理工具
 */

/**
 * 加载用户信息
 */
function loadUserInfo(page) {
  console.log('开始加载用户信息');
  const userInfo = wx.getStorageSync('userInfo');
  
  // 直接从缓存获取角色信息
  const roleNames = wx.getStorageSync('roleNames');
  const rolePermissions = wx.getStorageSync('rolePermissions');
  
  // 打印从缓存中读取的用户信息
  console.log('从缓存读取的用户信息:', userInfo);
  console.log('从缓存直接读取的角色名称信息:', roleNames);
  console.log('从缓存直接读取的角色权限信息:', rolePermissions);
  
  // 尝试从全局变量获取
  const app = getApp();
  const globalUserInfo = app.globalData && app.globalData.userInfo;
  const globalRoleNames = app.globalData && app.globalData.roleNames;
  const globalRolePermissions = app.globalData && app.globalData.rolePermissions;
  
  console.log('从全局变量获取的用户信息:', globalUserInfo);
  console.log('从全局变量获取的角色名称:', globalRoleNames);
  console.log('从全局变量获取的角色权限:', globalRolePermissions);
  
  // 合并所有来源的信息
  const finalUserInfo = userInfo || globalUserInfo || {};
  const finalRoleNames = roleNames || (userInfo && userInfo.roleNames) || globalRoleNames || {};
  const finalRolePermissions = rolePermissions || (userInfo && userInfo.rolePermissions) || globalRolePermissions || {};
  
  // 保存用户所有角色
  let userRoles = [];
  
  // 检查是否有完整的用户信息
  if (finalUserInfo && finalUserInfo.userId) {
    console.log('成功加载用户信息:', finalUserInfo);
    console.log('最终使用的角色名称信息:', finalRoleNames);
    console.log('最终使用的角色权限信息:', finalRolePermissions);
    
    // 处理多角色情况
    if (finalRoleNames && finalRolePermissions) {
      // 获取有效的生产进度角色列表
      const validProgressRoles = page.data.validProgressRoles;
      console.log('有效的生产进度角色列表:', validProgressRoles);
      
      // 修改：按照validProgressRoles的顺序添加角色，确保显示顺序正确
      validProgressRoles.forEach(roleName => {
        // 只添加用户拥有的角色
        if (finalRolePermissions.hasOwnProperty(roleName)) {
          userRoles.push({
            roleKey: roleName,
            roleName: finalRoleNames[roleName] || roleName,
            permissions: finalRolePermissions[roleName] || [],
            // 添加角色样式类
            styleClass: page.data.roleStyleMap[roleName] || ''
          });
        }
      });
      
      console.log('用户可用于生产进度的角色列表:', userRoles);
      
      // 如果存在角色，设置第一个为默认角色
      if (userRoles.length > 0) {
        finalUserInfo.roleName = userRoles[0].roleName;
        finalUserInfo.roleKey = userRoles[0].roleKey;
        console.log('设置默认角色:', userRoles[0].roleKey, '角色名称:', finalUserInfo.roleName);
      }
    }
    
    page.setData({
      userInfo: finalUserInfo,
      userRoles
    });
  } else {
    console.log('缓存中没有用户信息或角色信息，使用模拟数据');
    // 使用模拟数据
    const mockUserInfo = {
      workerId: 'GH001',
      userName: '张师傅',
      roleName: '烧花工',
      roleKey: 'burning',
      department: '生产部'
    };
    
    // 修改：按照指定顺序排列模拟角色数据
    const mockUserRoles = [
      {
        roleKey: 'cutting',
        roleName: '烧花/剪图',
        permissions: ['temu:purchase-order:list', 'temu:purchase-order:progressUupdate'],
        styleClass: 'cutting-role'
      },
      {
        roleKey: 'workshop',
        roleName: '车间/拣货',
        permissions: ['temu:purchase-order:list', 'temu:purchase-order:progressUupdate'],
        styleClass: 'workshop-role'
      },
      {
        roleKey: 'trimming',
        roleName: '剪线/压图',
        permissions: ['temu:purchase-order:list', 'temu:purchase-order:progressUupdate'],
        styleClass: 'trimming-role'
      },
      {
        roleKey: 'inspection',
        roleName: '查货',
        permissions: ['temu:purchase-order:list', 'temu:purchase-order:progressUupdate'],
        styleClass: 'inspection-role'
      },
      {
        roleKey: 'packaging',
        roleName: '包装',
        permissions: ['temu:purchase-order:list', 'temu:purchase-order:progressUupdate'],
        styleClass: 'packaging-role'
      },
      {
        roleKey: 'shipping',
        roleName: '发货',
        permissions: ['temu:purchase-order:list', 'temu:purchase-order:progressUupdate'],
        styleClass: 'shipping-role'
      }
    ];
    
    // 保存模拟用户信息到本地存储
    wx.setStorageSync('userInfo', mockUserInfo);
    console.log('已保存模拟用户信息到缓存');
    
    page.setData({ 
      userInfo: mockUserInfo,
      userRoles: mockUserRoles
    });
  }
  
  // 确认用户角色数据已设置
  console.log('设置后的角色数据:', {
    userInfo: page.data.userInfo,
    userRoles: page.data.userRoles
  });
}

/**
 * 检查用户是否有权限操作某个工序
 */
function checkUserProgressPermission(page, progressType) {
  try {
    console.log('检查用户操作权限:', progressType);
    const { userRoles, validProgressRoles } = page.data;
    
    // 首先检查是否是有效的生产进度工序类型
    if (!validProgressRoles.includes(progressType)) {
      console.log('非有效生产进度工序类型:', progressType);
      return false;
    }
    
    // 如果没有角色信息
    if (!userRoles || userRoles.length === 0) {
      console.log('无角色信息，默认返回有权限');
      return true; // 开发环境默认放开权限
    }
    
    // 检查用户是否有对应角色
    const hasRole = userRoles.some(role => role.roleKey === progressType);
    console.log('用户是否有此工序对应角色:', hasRole, '角色:', progressType);
    
    return hasRole;
  } catch (error) {
    console.error('检查用户权限时发生错误:', error);
    // 出错时默认返回有权限，避免界面异常
    return true;
  }
}

/**
 * 获取用户选中的角色类型
 */
function getSelectedUserRoleTypes(page) {
  try {
    console.log('获取用户选中的角色类型');
    // 从页面数据中获取选中的用户角色
    const { selectedUserRoles } = page.data;
    
    if (!selectedUserRoles || !Array.isArray(selectedUserRoles)) {
      console.log('没有选中的用户角色或数据格式错误');
      return [];
    }
    
    console.log('用户选中的角色类型:', selectedUserRoles);
    return selectedUserRoles;
  } catch (error) {
    console.error('获取用户选中的角色类型时发生错误:', error);
    return [];
  }
}

module.exports = {
  loadUserInfo,
  checkUserProgressPermission,
  getSelectedUserRoleTypes
}; 