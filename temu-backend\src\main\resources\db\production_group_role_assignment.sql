/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : temu_api

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 04/06/2025 02:02:44
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for production_group_role_assignment
-- ----------------------------
DROP TABLE IF EXISTS `production_group_role_assignment`;
CREATE TABLE `production_group_role_assignment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `group_id` bigint NOT NULL COMMENT '生产组ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `assign_time` datetime NULL DEFAULT NULL COMMENT '分配时间',
  `assign_by` bigint NULL DEFAULT NULL COMMENT '分配人ID(组长)',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态(0正常1禁用)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_group_user_role`(`group_id` ASC, `user_id` ASC, `role_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 76 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '生产组角色分配表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of production_group_role_assignment
-- ----------------------------
INSERT INTO `production_group_role_assignment` VALUES (69, 1, 34, 10, '2025-05-28 11:17:01', 40, '0');
INSERT INTO `production_group_role_assignment` VALUES (70, 1, 34, 12, '2025-05-28 11:17:01', 40, '0');
INSERT INTO `production_group_role_assignment` VALUES (71, 2, 41, 9, '2025-05-28 12:29:06', 40, '0');
INSERT INTO `production_group_role_assignment` VALUES (72, 1, 23, 9, '2025-05-29 20:28:31', 1, '0');
INSERT INTO `production_group_role_assignment` VALUES (73, 1, 23, 10, '2025-05-29 20:28:31', 1, '0');
INSERT INTO `production_group_role_assignment` VALUES (74, 1, 23, 12, '2025-05-29 20:28:31', 1, '0');
INSERT INTO `production_group_role_assignment` VALUES (75, 1, 23, 13, '2025-05-29 20:28:31', 1, '0');

SET FOREIGN_KEY_CHECKS = 1;
