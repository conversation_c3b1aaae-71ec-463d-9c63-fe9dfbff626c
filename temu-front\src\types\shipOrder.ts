// 店铺信息类型
export interface Shop {
  shopId: number
  shopName: string
  shopRemark?: string
  shopLogo?: string
  [key: string]: any
}

// 发货单明细信息
export interface ShipOrderDetail {
  productSkuId: number
  productSn: string
  productName: string
  skuName: string
  skuQuantity: number
  skuPrice: number
  skuPicture?: string
  skuBarcode?: string
  [key: string]: any
}

// 包裹信息
export interface PackageInfo {
  packageSn: string
  skcNum: number
  [key: string]: any
}

// 包裹接收信息
export interface PackageReceiveInfo {
  receiveTime: number
  packageSn: string
  [key: string]: any
}

// 发货单基础类型定义
export interface ShipOrder {
  deliveryOrderSn: string;
  subPurchaseOrderSn?: string;
  shopId: string | number;
  shopName?: string;
  shopRemark?: string;
  expressBatchSn?: string;
  deliveryMethod?: number;
  expressCompany?: string;
  expressDeliverySn?: string;
  driverName?: string;
  plateNumber?: string;
  expressWeightFeedbackStatus?: number;
  purchaseStockType?: number;
  urgencyType?: number;
  status?: number;
  productSkcId?: number;
  skcExtCode?: string;
  deliverSkcNum?: number;
  deliverPackageNum?: number;
  receivePackageNum?: number;
  deliverTime?: number;
  receiveTime?: number;
  packageList?: PackageInfo[];
  packageReceiveInfoVOList?: PackageReceiveInfo[];
  subPurchaseOrderBasicVO?: {
    subPurchaseOrderSn?: string;
    productSkcId?: number;
    productSkcPicture?: string;
    skcExtCode?: string;
    purchaseStockType?: number;
    isFirst?: boolean;
    settlementType?: number;
    urgencyType?: number;
  };
  [key: string]: any;
}

// 标签数据类型定义
export interface LabelData {
  volumeType?: number;
  supplierId?: number;
  deliveryMethod?: number;
  isCustomProduct?: boolean;
  packageIndex?: number;
  expressDeliverySn?: string;
  productName?: string;
  subWarehouseEnglishName?: string;
  isClothCat?: boolean;
  isFirst?: boolean;
  purchaseStockType?: number;
  totalPackageNum?: number;
  expressCompany?: string;
  productSkcId?: number;
  deliveryOrderSn?: string;
  settlementType?: number;
  supplierName?: string;
  skcExtCode?: string;
  productSkuIdList?: number[];
  deliverTime?: number;
  urgencyType?: number;
  subWarehouseId?: number;
  productSkcName?: string;
  packageSn?: string;
  expressEnglishCompany?: string;
  packageSkcNum?: number;
  subWarehouseName?: string;
  driverName?: string;
  purchaseTime?: number;
  subPurchaseOrderSn?: string;
  driverPhone?: string;
  storageAttrName?: string;
  deliverSkcNum?: number;
  deliveryStatus?: number;
}

// 发货单请求参数
export interface ShipOrderRequestDTO {
  shopIds?: number[] // 店铺ID列表，支持多选
  shopId?: number // 单个店铺ID
  pageNo: number // 页码
  pageSize: number // 每页条数
  subPurchaseOrderSnList?: string[] // 子采购单号列表
  shipOrderSnList?: string[] // 发货单号列表
  productLabelCodeStyle?: number // 商品条码样式，0-全选，1-旧样式，2-新样式
  [key: string]: any
}

// 店铺数据范围信息类型
export interface ShopDataRange {
  shopId: number
  shopName: string
  startIndex: number
  endIndex: number
  accessibleCount: number
  actualCount: number
} 