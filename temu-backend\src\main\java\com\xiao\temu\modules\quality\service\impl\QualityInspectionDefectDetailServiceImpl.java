package com.xiao.temu.modules.quality.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.modules.quality.entity.QualityInspectionDefectDetail;
import com.xiao.temu.modules.quality.service.DefectImageService;
import com.xiao.temu.modules.quality.service.QualityInspectionDefectDetailService;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.quality.mapper.QualityInspectionDefectDetailMapper;
import com.xiao.temu.modules.quality.mapper.QualityInspectionMapper;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.infrastructure.api.TemuApiClient;
import lombok.extern.slf4j.Slf4j;
import org.ehcache.shadow.org.terracotta.statistics.Time;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 质检不合格详情数据服务实现类
 */
@Slf4j
@Service
public class QualityInspectionDefectDetailServiceImpl implements QualityInspectionDefectDetailService {

    @Autowired
    private QualityInspectionMapper qualityInspectionMapper;

    @Autowired
    private QualityInspectionDefectDetailMapper defectDetailMapper;

    @Autowired
    private ShopService shopService;
    
    @Autowired
    private DefectImageService defectImageService;

    // 默认API调用间隔(毫秒)
    private static final long DEFAULT_API_INTERVAL = 100;
    // 默认批次大小
    private static final int DEFAULT_BATCH_SIZE = 10;
    // 默认最大线程数
    private static final int DEFAULT_MAX_THREADS = 10;

    @Override
    public String syncDefectDetailData(Long shopId) {
        return batchSyncDefectDetailData(shopId, DEFAULT_BATCH_SIZE, DEFAULT_MAX_THREADS, DEFAULT_API_INTERVAL);
    }

    @Override
    @Async
    public CompletableFuture<Void> syncDefectDetailAsync(Long shopId, Long qcBillId, List<Long> productSkuIds) {
        try {
            Shop shop = shopService.getShopById(shopId).convertToShop();
            if (shop == null) {
                log.error("同步质检不合格详情数据失败: 店铺不存在, shopId={}", shopId);
                return CompletableFuture.completedFuture(null);
            }

            // 创建通用参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.goods.qualityinspectiondetail.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());

            for (Long productSkuId : productSkuIds) {
                try {
                    // 创建业务参数
                    HashMap<String, Object> businessParams = new HashMap<>();
                    businessParams.put("qcBillId", qcBillId.toString());
                    businessParams.put("timestamp", String.valueOf(Time.time()));

                    // 调用API
                    JSONObject response = TemuApiClient.sendRequest(commonParams, businessParams);
                    
                    // 解析并保存数据
                    boolean success = saveDefectDetail(shopId, qcBillId, productSkuId, response);
                    if (success) {
                        log.info("同步质检不合格详情数据成功: shopId={}, qcBillId={}, productSkuId={}", shopId, qcBillId, productSkuId);
                    } else {
                        log.warn("同步质检不合格详情数据失败: shopId={}, qcBillId={}, productSkuId={}", shopId, qcBillId, productSkuId);
                    }
                } catch (Exception e) {
                    log.error("同步质检不合格详情数据异常: shopId={}, qcBillId={}, productSkuId={}, error={}", 
                              shopId, qcBillId, productSkuId, e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("异步同步质检不合格详情数据异常: shopId={}, qcBillId={}, error={}", 
                      shopId, qcBillId, e.getMessage(), e);
        }
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public boolean saveDefectDetail(Long shopId, Long qcBillId, Long productSkuId, JSONObject responseData) {
        try {
            if (responseData == null || !responseData.getBooleanValue("success")) {
                log.warn("API返回数据异常: shopId={}, qcBillId={}, productSkuId={}, response={}", 
                        shopId, qcBillId, productSkuId, responseData);
                return false;
            }

            // 从响应中获取结果数据
            JSONObject resultObj = responseData.getJSONObject("result");
            if (resultObj == null) {
                log.warn("API返回结果为空: shopId={}, qcBillId={}, productSkuId={}", shopId, qcBillId, productSkuId);
                return false;
            }

            // 获取历史记录
            JSONArray historyVOS = resultObj.getJSONArray("historyVOS");
            if (historyVOS == null || historyVOS.isEmpty()) {
                log.warn("API返回历史记录为空: shopId={}, qcBillId={}, productSkuId={}", shopId, qcBillId, productSkuId);
                return false;
            }

            // 获取第一条历史记录
            JSONObject firstHistory = historyVOS.getJSONObject(0);
            if (firstHistory == null) {
                return false;
            }

            // 获取质检详情
            JSONObject qcDetail = firstHistory.getJSONObject("qcDetail");
            if (qcDetail == null) {
                log.warn("API返回质检详情为空: shopId={}, qcBillId={}, productSkuId={}", shopId, qcBillId, productSkuId);
                return false;
            }
            
            // 获取productSkcId字段
            Long productSkcId = qcDetail.getLong("productSkcId");

            // 获取缺陷列表
            JSONArray flawDTOList = qcDetail.getJSONArray("flawDTOList");
            if (flawDTOList == null || flawDTOList.isEmpty()) {
                log.warn("API返回缺陷列表为空: shopId={}, qcBillId={}, productSkuId={}", shopId, qcBillId, productSkuId);
                return false;
            }

            // 处理每个缺陷
            List<QualityInspectionDefectDetail> defectDetails = new ArrayList<>();
            for (int i = 0; i < flawDTOList.size(); i++) {
                JSONObject flaw = flawDTOList.getJSONObject(i);
                if (flaw == null) continue;

                // 获取productSkuId数组，检查是否匹配
                JSONArray skuIds = flaw.getJSONArray("productSkuId");
                if (skuIds == null || skuIds.isEmpty()) continue;
                
                // 查看当前skuId是否在数组中
                boolean skuMatched = false;
                for (int j = 0; j < skuIds.size(); j++) {
                    if (productSkuId.equals(skuIds.getLong(j))) {
                        skuMatched = true;
                        break;
                    }
                }
                
                // 如果当前sku不匹配，则跳过
                if (!skuMatched) continue;

                // 创建缺陷详情对象
                QualityInspectionDefectDetail defectDetail = new QualityInspectionDefectDetail();
                defectDetail.setShopId(shopId);
                defectDetail.setQcBillId(qcBillId);
                defectDetail.setProductSkuId(productSkuId);
                defectDetail.setProductSkcId(productSkcId);
                defectDetail.setFlawNameDesc(flaw.getString("flawNameDesc"));
                defectDetail.setRemark(flaw.getString("remark"));
                
                // 处理附件(图片)
                JSONArray attachments = flaw.getJSONArray("attachments");
                if (attachments != null && !attachments.isEmpty()) {
                    // 检查是否为字符串数组，进行转换
                    if (isAttachmentStringArray(attachments)) {
                        // 将字符串数组转换为JSONObject数组
                        JSONArray formattedAttachments = convertStringArrayToObjectArray(attachments);
                        // 转存疵点图片
                        JSONArray processedAttachments = defectImageService.transferDefectImages(
                                shopId, qcBillId, productSkuId, formattedAttachments);
                        // 将处理后的附件信息保存，只保存imageKey
                        defectDetail.setAttachments(extractImageKeysAsJson(processedAttachments));
                    } else {
                        // 直接转存已经是JSONObject数组的附件
                        JSONArray processedAttachments = defectImageService.transferDefectImages(
                                shopId, qcBillId, productSkuId, attachments);
                        // 将处理后的附件信息保存，只保存imageKey
                        defectDetail.setAttachments(extractImageKeysAsJson(processedAttachments));
                    }
                }
                
                defectDetail.setSyncTime(LocalDateTime.now());
                defectDetail.setCreateTime(LocalDateTime.now());
                defectDetail.setUpdateTime(LocalDateTime.now());
                defectDetails.add(defectDetail);
            }
            
            // 批量保存缺陷详情
            if (!defectDetails.isEmpty()) {
                defectDetailMapper.batchInsertOrUpdate(defectDetails);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("保存质检不合格详情数据异常: shopId={}, qcBillId={}, productSkuId={}, error={}", 
                     shopId, qcBillId, productSkuId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<QualityInspectionDefectDetail> getDefectDetailsByShopIdAndQcBillId(Long shopId, Long qcBillId) {
        return defectDetailMapper.selectByShopIdAndQcBillId(shopId, qcBillId);
    }

    @Override
    public String batchSyncDefectDetailData(Long shopId, int batchSize, int maxThreads, long apiInterval) {
        try {
            log.info("开始批量同步质检不合格详情数据: shopId={}, batchSize={}, maxThreads={}, apiInterval={}ms", 
                     shopId, batchSize, maxThreads, apiInterval);
            
            // 查询所有不合格的质检数据
            List<Map<String, Object>> defectiveData = qualityInspectionMapper.selectDefectiveQcBillAndSkuIdsByShopId(shopId);
            if (defectiveData == null || defectiveData.isEmpty()) {
                log.info("没有找到不合格的质检数据: shopId={}", shopId);
                return "没有找到不合格的质检数据";
            }
            
            log.info("找到不合格的质检数据: shopId={}, count={}", shopId, defectiveData.size());

            // 按质检单ID分组
            Map<Long, List<Long>> qcBillSkuMap = new HashMap<>();
            for (Map<String, Object> data : defectiveData) {
                Long qcBillId = Long.valueOf(data.get("qc_bill_id").toString());
                Long productSkuId = Long.valueOf(data.get("product_sku_id").toString());
                
                if (!qcBillSkuMap.containsKey(qcBillId)) {
                    qcBillSkuMap.put(qcBillId, new ArrayList<>());
                }
                qcBillSkuMap.get(qcBillId).add(productSkuId);
            }
            
            // 查询已存在的质检单ID
            List<Long> existingQcBillIds = defectDetailMapper.selectExistingQcBillIdsByShopId(shopId);
            Set<Long> existingQcBillIdSet = new HashSet<>(existingQcBillIds != null ? existingQcBillIds : List.of());
            
            // 过滤出需要同步的质检单ID
            List<Long> qcBillIdsToSync = qcBillSkuMap.keySet().stream()
                    .filter(qcBillId -> !existingQcBillIdSet.contains(qcBillId))
                    .collect(Collectors.toList());
            
            if (qcBillIdsToSync.isEmpty()) {
                log.info("所有不合格的质检数据都已同步: shopId={}", shopId);
                return "所有不合格的质检数据都已同步";
            }
            
            log.info("需要同步的质检单数量: shopId={}, count={}", shopId, qcBillIdsToSync.size());
            
            // 创建线程池
            ExecutorService executor = Executors.newFixedThreadPool(maxThreads);
            // 创建限流器
            RateLimiter rateLimiter = new RateLimiter(apiInterval);
            
            // 创建任务列表
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (Long qcBillId : qcBillIdsToSync) {
                List<Long> skuIds = qcBillSkuMap.get(qcBillId);
                
                // 使用线程池执行异步任务
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        // 获取令牌（限流）
                        rateLimiter.acquire();
                        
                        // 执行同步
                        syncDefectDetailAsync(shopId, qcBillId, skuIds).join();
                    } catch (Exception e) {
                        log.error("同步质检不合格详情数据异常: shopId={}, qcBillId={}, error={}", 
                                 shopId, qcBillId, e.getMessage(), e);
                    }
                }, executor);
                
                futures.add(future);
                
                // 每处理一批次，等待所有任务完成
                if (futures.size() >= batchSize) {
                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                    futures.clear();
                }
            }
            
            // 等待剩余任务完成
            if (!futures.isEmpty()) {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            }
            
            // 关闭线程池
            executor.shutdown();
            executor.awaitTermination(5, TimeUnit.MINUTES);
            
            log.info("批量同步质检不合格详情数据完成: shopId={}, syncCount={}", shopId, qcBillIdsToSync.size());
            return String.format("同步完成，共同步%d条不合格质检详情数据", qcBillIdsToSync.size());
            
        } catch (Exception e) {
            log.error("批量同步质检不合格详情数据异常: shopId={}, error={}", shopId, e.getMessage(), e);
            return "同步异常: " + e.getMessage();
        }
    }
    
    /**
     * 限流器实现
     */
    private static class RateLimiter {
        private final long intervalMs;
        private long lastAcquireTime = 0;
        
        public RateLimiter(long intervalMs) {
            this.intervalMs = intervalMs;
        }
        
        public synchronized void acquire() {
            long currentTime = System.currentTimeMillis();
            long elapsedTime = currentTime - lastAcquireTime;
            
            if (elapsedTime < intervalMs) {
                try {
                    Thread.sleep(intervalMs - elapsedTime);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
            
            lastAcquireTime = System.currentTimeMillis();
        }
    }

    /**
     * 检查附件数组是否为字符串数组
     */
    private boolean isAttachmentStringArray(JSONArray attachments) {
        if (attachments.isEmpty()) {
            return false;
        }
        
        try {
            // 尝试获取第一个元素作为字符串
            Object firstElement = attachments.get(0);
            return firstElement instanceof String;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 将字符串数组转换为JSONObject数组
     */
    private JSONArray convertStringArrayToObjectArray(JSONArray stringArray) {
        JSONArray objectArray = new JSONArray();
        
        for (int i = 0; i < stringArray.size(); i++) {
            try {
                String url = stringArray.getString(i);
                if (url != null && !url.isEmpty()) {
                    JSONObject attachment = new JSONObject();
                    attachment.put("url", url);
                    objectArray.add(attachment);
                }
            } catch (Exception e) {
                log.warn("转换附件字符串到对象失败: index={}, error={}", i, e.getMessage());
            }
        }
        
        return objectArray;
    }

    /**
     * 从处理后的附件数组中提取imageKey并重新组装成JSON数组
     */
    private String extractImageKeysAsJson(JSONArray processedAttachments) {
        if (processedAttachments == null || processedAttachments.isEmpty()) {
            return "[]";
        }
        
        JSONArray imageKeysArray = new JSONArray();
        for (int i = 0; i < processedAttachments.size(); i++) {
            JSONObject attachment = processedAttachments.getJSONObject(i);
            if (attachment != null && attachment.containsKey("imageKey")) {
                JSONObject imageKeyObj = new JSONObject();
                imageKeyObj.put("imageKey", attachment.getString("imageKey"));
                imageKeysArray.add(imageKeyObj);
            }
        }
        
        return imageKeysArray.toJSONString();
    }

    @Override
    public boolean deleteDefectDetailByShopId(Long shopId) {
        try {
            log.info("开始删除店铺[{}]的质检不合格详情数据", shopId);
            
            // 构建删除条件：按店铺ID删除
            Map<String, Object> params = new HashMap<>();
            params.put("shopId", shopId);
            
            // 执行删除操作
            int count = defectDetailMapper.deleteByShopId(params);
            log.info("已删除店铺[{}]的质检不合格详情数据{}条", shopId, count);
            
            return true;
        } catch (Exception e) {
            log.error("删除店铺[{}]的质检不合格详情数据失败", shopId, e);
            return false;
        }
    }
} 