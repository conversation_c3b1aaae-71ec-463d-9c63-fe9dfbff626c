import request from '@/utils/request';

/**
 * 获取生产组成员列表
 * @param groupId 生产组ID
 * @param params 查询参数
 * @returns 
 */
export function getMemberList(groupId: number, params?: any) {
  return request({
    url: `/production/member/list/${groupId}`,
    method: 'get',
    params
  });
}

/**
 * 获取生产组所有成员（不分页）
 * @param groupId 生产组ID
 * @returns 
 */
export function getAllMembers(groupId: number) {
  return request({
    url: `/production/member/all/${groupId}`,
    method: 'get'
  });
}

/**
 * 添加生产组成员
 * @param groupId 生产组ID
 * @param userId 用户ID
 * @returns 
 */
export function addMember(groupId: number, userId: number) {
  return request({
    url: `/production/member/${groupId}/${userId}`,
    method: 'post'
  });
}

/**
 * 批量添加生产组成员
 * @param groupId 生产组ID
 * @param userIds 用户ID数组
 * @returns 
 */
export function batchAddMembers(groupId: number, userIds: number[]) {
  return request({
    url: `/production/member/batch/${groupId}`,
    method: 'post',
    data: userIds
  });
}

/**
 * 删除生产组成员（仅从组中移除）
 * @param groupId 生产组ID
 * @param userId 用户ID
 * @returns 
 */
export function removeMember(groupId: number, userId: number) {
  return request({
    url: `/production/member/${groupId}/${userId}`,
    method: 'delete'
  });
}

/**
 * 删除用户（彻底删除用户）
 * @param groupId 生产组ID
 * @param userId 用户ID
 * @returns 
 */
export function deleteUser(groupId: number, userId: number) {
  return request({
    url: `/production/member/deleteUser/${groupId}/${userId}`,
    method: 'delete'
  });
}

/**
 * 获取生产组成员列表（分页）- 别名函数，与getMemberList保持一致
 * @param params 查询参数
 * @returns 
 */
export function getGroupMembers(params: any) {
  return request({
    url: `/production/member/list/${params.groupId}`,
    method: 'get',
    params: {
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      keyword: params.keyword
    }
  });
}

/**
 * 批量添加成员 - 重命名为addGroupMember以保持一致性
 * @param data {groupId: number, userIds: number[]}
 * @returns 
 */
export function addGroupMember(data: {groupId: number, userIds: number[]}) {
  return request({
    url: `/production/member/batch/${data.groupId}`,
    method: 'post',
    data: data.userIds
  });
}

/**
 * 移除成员 - 重命名为removeGroupMember以保持一致性
 * @param data {groupId: number, userId: number}
 * @returns 
 */
export function removeGroupMember(data: {groupId: number, userId: number}) {
  return request({
    url: `/production/member/${data.groupId}/${data.userId}`,
    method: 'delete'
  });
}

/**
 * 获取可以添加到生产组的用户列表
 * @param params 查询参数
 * @returns 
 */
export function getAvailableUsers(params: any) {
  return request({
    url: `/production/member/available/${params.groupId}`,
    method: 'get',
    params: {
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      keyword: params.keyword
    }
  });
}

/**
 * 组长创建用户并添加到生产组
 * @param groupId 生产组ID
 * @param userData 用户数据
 * @returns 
 */
export function createUserAndAddToGroup(groupId: number, userData: any) {
  return request({
    url: `/production/member/create/${groupId}`,
    method: 'post',
    data: userData
  });
} 