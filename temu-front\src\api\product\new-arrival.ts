import request from '@/utils/request';

// 定义响应数据类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 定义统计数据类型
export interface ShopStats {
  shopId: number;
  shopName: string;
  shopRemark: string;
  currentMonthNewCount: number;
  currentWeekNewCount: number;
  lastWeekNewCount: number;
  yesterdayNewCount: number;
  currentWeekOnlineCount: number;
  // 销售统计数据
  todaySales: number;
  lastWeekSales: number;
  lastMonthSales: number;
  totalProducts: number;
}

// 查询店铺上新统计数据
export function getProductNewArrivalStats(data: any): Promise<ApiResponse<ShopStats[]>> {
  return request({
    url: '/product/new-arrival/stats',
    method: 'post',
    data
  });
}

// 批量保存商品上新数据
export function batchSaveProductNewArrival(data: any): Promise<ApiResponse<number>> {
  return request({
    url: '/product/new-arrival/batch-save',
    method: 'post',
    data
  });
} 