package com.xiao.temu.modules.violation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 店铺违规信息实体类
 */
@TableName("shop_violation_info")
public class ShopViolationInfo {
    
    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /** 店铺ID */
    @TableField("shop_id")
    private Long shopId;
    
    /** Temu平台店铺ID */
    @TableField("mall_id")
    private String mallId;
    
    /** 违规编号 */
    @TableField("punish_sn")
    private String punishSn;
    
    /** 备货单号 */
    @TableField("sub_purchase_order_sn")
    private String subPurchaseOrderSn;
    
    /** 违规类型代码 */
    @TableField("punish_type_code")
    private Integer punishTypeCode;
    
    /** 违规类型描述 */
    @TableField("punish_type_desc")
    private String punishTypeDesc;
    
    /** 违规一级类型代码 */
    @TableField("punish_first_type_code")
    private Integer punishFirstTypeCode;
    
    /** 违规一级类型描述 */
    @TableField("punish_first_type_desc")
    private String punishFirstTypeDesc;
    
    /** 违规二级类型描述 */
    @TableField("punish_second_type_desc")
    private String punishSecondTypeDesc;
    
    /** 违规发起时间戳(毫秒) */
    @TableField("violation_start_time")
    private Long violationStartTime;
    
    /** 违规发起时间 */
    @TableField("violation_time")
    private LocalDateTime violationTime;
    
    /** 违规金额 */
    @TableField("punish_amount")
    private BigDecimal punishAmount;
    
    /** 违规金额币种 */
    @TableField("punish_amount_currency")
    private String punishAmountCurrency;
    
    /** 违规状态码 */
    @TableField("punish_status")
    private Integer punishStatus;
    
    /** 违规状态描述 */
    @TableField("punish_status_desc")
    private String punishStatusDesc;
    
    /** 详情查看状态码 */
    @TableField("view_details_status")
    private Integer viewDetailsStatus;
    
    /** 倒计时时间戳(毫秒) */
    @TableField("countdown_time")
    private Long countdownTime;
    
    /** 同步时间 */
    @TableField("sync_time")
    private LocalDateTime syncTime;
    
    /** 创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /** 更新时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /** 处罚金额差异 */
    @TableField("punish_amount_diff")
    private String punishAmountDiff;
    
    /** 处罚第三类型代码 */
    @TableField("punish_third_type_code")
    private Integer punishThirdTypeCode;
    
    /** 处罚第三类型描述 */
    @TableField("punish_third_type_desc")
    private String punishThirdTypeDesc;
    
    /** 凭证 */
    @TableField("voucher")
    private String voucher;
    
    /** 质检缺陷图片URL列表 */
    @TableField("qc_flaw_picture_url_list")
    private String qcFlawPictureUrlList;
    
    /** 显示凭证 */
    @TableField("show_voucher")
    private String showVoucher;
    
    /** 质量事件处罚维度 */
    @TableField("quality_event_punish_dimension")
    private String qualityEventPunishDimension;
    
    /** 质量事件处罚维度描述 */
    @TableField("quality_event_punish_dimension_desc")
    private String qualityEventPunishDimensionDesc;
    
    /** 产品SKCID */
    @TableField("product_skc_id")
    private String productSkcId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getMallId() {
        return mallId;
    }

    public void setMallId(String mallId) {
        this.mallId = mallId;
    }

    public String getPunishSn() {
        return punishSn;
    }

    public void setPunishSn(String punishSn) {
        this.punishSn = punishSn;
    }

    public String getSubPurchaseOrderSn() {
        return subPurchaseOrderSn;
    }

    public void setSubPurchaseOrderSn(String subPurchaseOrderSn) {
        this.subPurchaseOrderSn = subPurchaseOrderSn;
    }

    public Integer getPunishTypeCode() {
        return punishTypeCode;
    }

    public void setPunishTypeCode(Integer punishTypeCode) {
        this.punishTypeCode = punishTypeCode;
    }

    public String getPunishTypeDesc() {
        return punishTypeDesc;
    }

    public void setPunishTypeDesc(String punishTypeDesc) {
        this.punishTypeDesc = punishTypeDesc;
    }

    public Integer getPunishFirstTypeCode() {
        return punishFirstTypeCode;
    }

    public void setPunishFirstTypeCode(Integer punishFirstTypeCode) {
        this.punishFirstTypeCode = punishFirstTypeCode;
    }

    public String getPunishFirstTypeDesc() {
        return punishFirstTypeDesc;
    }

    public void setPunishFirstTypeDesc(String punishFirstTypeDesc) {
        this.punishFirstTypeDesc = punishFirstTypeDesc;
    }

    public String getPunishSecondTypeDesc() {
        return punishSecondTypeDesc;
    }

    public void setPunishSecondTypeDesc(String punishSecondTypeDesc) {
        this.punishSecondTypeDesc = punishSecondTypeDesc;
    }

    public Long getViolationStartTime() {
        return violationStartTime;
    }

    public void setViolationStartTime(Long violationStartTime) {
        this.violationStartTime = violationStartTime;
    }

    public LocalDateTime getViolationTime() {
        return violationTime;
    }

    public void setViolationTime(LocalDateTime violationTime) {
        this.violationTime = violationTime;
    }

    public BigDecimal getPunishAmount() {
        return punishAmount;
    }

    public void setPunishAmount(BigDecimal punishAmount) {
        this.punishAmount = punishAmount;
    }

    public String getPunishAmountCurrency() {
        return punishAmountCurrency;
    }

    public void setPunishAmountCurrency(String punishAmountCurrency) {
        this.punishAmountCurrency = punishAmountCurrency;
    }

    public Integer getPunishStatus() {
        return punishStatus;
    }

    public void setPunishStatus(Integer punishStatus) {
        this.punishStatus = punishStatus;
    }

    public String getPunishStatusDesc() {
        return punishStatusDesc;
    }

    public void setPunishStatusDesc(String punishStatusDesc) {
        this.punishStatusDesc = punishStatusDesc;
    }

    public Integer getViewDetailsStatus() {
        return viewDetailsStatus;
    }

    public void setViewDetailsStatus(Integer viewDetailsStatus) {
        this.viewDetailsStatus = viewDetailsStatus;
    }

    public Long getCountdownTime() {
        return countdownTime;
    }

    public void setCountdownTime(Long countdownTime) {
        this.countdownTime = countdownTime;
    }

    public LocalDateTime getSyncTime() {
        return syncTime;
    }

    public void setSyncTime(LocalDateTime syncTime) {
        this.syncTime = syncTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getPunishAmountDiff() {
        return punishAmountDiff;
    }

    public void setPunishAmountDiff(String punishAmountDiff) {
        this.punishAmountDiff = punishAmountDiff;
    }

    public Integer getPunishThirdTypeCode() {
        return punishThirdTypeCode;
    }

    public void setPunishThirdTypeCode(Integer punishThirdTypeCode) {
        this.punishThirdTypeCode = punishThirdTypeCode;
    }

    public String getPunishThirdTypeDesc() {
        return punishThirdTypeDesc;
    }

    public void setPunishThirdTypeDesc(String punishThirdTypeDesc) {
        this.punishThirdTypeDesc = punishThirdTypeDesc;
    }

    public String getVoucher() {
        return voucher;
    }

    public void setVoucher(String voucher) {
        this.voucher = voucher;
    }

    public String getQcFlawPictureUrlList() {
        return qcFlawPictureUrlList;
    }

    public void setQcFlawPictureUrlList(String qcFlawPictureUrlList) {
        this.qcFlawPictureUrlList = qcFlawPictureUrlList;
    }

    public String getShowVoucher() {
        return showVoucher;
    }

    public void setShowVoucher(String showVoucher) {
        this.showVoucher = showVoucher;
    }
    
    /**
     * 设置显示凭证（兼容Integer类型）
     */
    public void setShowVoucher(Integer showVoucher) {
        if (showVoucher != null) {
            this.showVoucher = showVoucher.toString();
        }
    }

    public String getQualityEventPunishDimension() {
        return qualityEventPunishDimension;
    }

    public void setQualityEventPunishDimension(String qualityEventPunishDimension) {
        this.qualityEventPunishDimension = qualityEventPunishDimension;
    }

    public String getQualityEventPunishDimensionDesc() {
        return qualityEventPunishDimensionDesc;
    }

    public void setQualityEventPunishDimensionDesc(String qualityEventPunishDimensionDesc) {
        this.qualityEventPunishDimensionDesc = qualityEventPunishDimensionDesc;
    }

    public String getProductSkcId() {
        return productSkcId;
    }

    public void setProductSkcId(String productSkcId) {
        this.productSkcId = productSkcId;
    }

    public void setViolationStartTime(Date date) {
        this.violationStartTime = date != null ? date.getTime() : null;
    }
} 