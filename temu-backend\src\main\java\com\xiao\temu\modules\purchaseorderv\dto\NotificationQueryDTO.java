package com.xiao.temu.modules.purchaseorderv.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 通知查询DTO
 */
@Data
public class NotificationQueryDTO {
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 备货单号
     */
    private String subPurchaseOrderSn;
    
    /**
     * 通知类型列表
     */
    private List<Integer> notificationTypes;
    
    /**
     * 单个通知类型（兼容前端参数）
     */
    private Integer notificationType;
    
    /**
     * 通知状态
     */
    private Integer notifyStatus;
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页记录数
     */
    private Integer pageSize = 10;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
    
    /**
     * 设置单个通知类型，同时更新通知类型列表
     */
    public void setNotificationType(Integer notificationType) {
        this.notificationType = notificationType;
        if (notificationType != null) {
            if (this.notificationTypes == null) {
                this.notificationTypes = new ArrayList<>();
            }
            // 清空现有列表并添加单个类型
            this.notificationTypes.clear();
            this.notificationTypes.add(notificationType);
        }
    }
} 