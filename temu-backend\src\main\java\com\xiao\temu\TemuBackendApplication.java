package com.xiao.temu;

import com.xiao.temu.modules.sync.service.ProductSyncService;
import com.xiao.temu.modules.sync.service.QualityInspectionSyncService;
import com.xiao.temu.modules.sync.service.RefundPackageSyncService;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Temu后台应用启动类
 * 定时任务已全部迁移到Quartz框架，配置在QuartzConfig中
 */
@SpringBootApplication
@EnableTransactionManagement
@MapperScan({
    "com.xiao.temu.modules.*.mapper",
    "com.xiao.temu.infrastructure.*.mapper"
})
@Slf4j
public class TemuBackendApplication implements ApplicationRunner {

    @Autowired
    private QualityInspectionSyncService qualityInspectionSyncService;
    
    @Autowired
    private ProductSyncService productSyncService;
    
    @Autowired
    private RefundPackageSyncService refundPackageSyncService;

    public static void main(String[] args) {
        SpringApplication.run(TemuBackendApplication.class, args);
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
//        log.info("应用启动完成，执行测试代码...");
//
//        // 执行质检数据同步
//        try {
//            log.info("开始手动执行质检数据同步任务...");
//            String result = qualityInspectionSyncService.executeScheduledSync();
//            log.info("手动执行质检数据同步任务结果: {}", result);
//        } catch (Exception e) {
//            log.error("手动执行质检数据同步任务异常", e);
//        }
//
//        // 执行商品数据同步
//        try {
//            log.info("开始手动执行商品数据同步任务...");
//            String result = productSyncService.executeScheduledSync();
//            log.info("手动执行商品数据同步任务结果: {}", result);
//        } catch (Exception e) {
//            log.error("手动执行商品数据同步任务异常", e);
//        }
//
//        // 执行退货包裹数据同步
//        try {
//            log.info("开始手动执行退货包裹数据同步任务...");
//            String result = refundPackageSyncService.executeScheduledSync();
//            log.info("手动执行退货包裹数据同步任务结果: {}", result);
//        } catch (Exception e) {
//            log.error("手动执行退货包裹数据同步任务异常", e);
//        }
    }
} 