package com.xiao.temu.modules.sync.vo;

import lombok.Data;

/**
 * 备货单同步结果VO
 */
@Data
public class PurchaseOrderSyncVO {
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 错误码
     */
    private Integer errorCode;
    
    /**
     * 错误信息
     */
    private String errorMsg;
    
    /**
     * 普通备货单同步数量
     */
    private Integer normalOrderCount;
    
    /**
     * JIT备货单同步数量
     */
    private Integer jitOrderCount;
    
    /**
     * 同步耗时(毫秒)
     */
    private Long costTime;
} 