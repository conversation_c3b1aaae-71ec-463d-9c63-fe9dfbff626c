// 运营组数据类型定义
export interface OperationGroup {
  groupId?: number;
  groupName: string;
  leaderId: number;
  leaderName?: string;
  status?: string;
  createTime?: string;
  updateTime?: string;
  remark?: string;
  memberCount?: number;
}

// 运营组成员数据类型定义
export interface GroupMember {
  id?: number;
  groupId: number;
  userId: number;
  username?: string;
  nickName?: string;
  joinTime?: string;
  status?: string;
  isLeader?: boolean;
}

// 运营组统计数据类型定义
export interface GroupStatistics {
  groupId: number;
  groupName: string;
  memberCount: number;
  shopCount: number;
  ongoingTaskCount: number;
  completedTaskCount: number;
}

// 运营组查询参数类型定义
export interface QueryGroupParams {
  groupName?: string;
  leaderId?: number;
  leaderName?: string;
  status?: string;
  memberId?: number;
  pageNum?: number;
  pageSize?: number;
}

// 分页数据类型定义
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
} 