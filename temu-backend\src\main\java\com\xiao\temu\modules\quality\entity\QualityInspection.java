package com.xiao.temu.modules.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 质检结果数据实体类
 */
@Data
@TableName("quality_inspection")
public class QualityInspection {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 质检单ID
     */
    private Long qcBillId;

    /**
     * 商品SKU ID
     */
    private Long productSkuId;

    /**
     * 商品SKC ID
     */
    private Long productSkcId;

    /**
     * SPU ID
     */
    private Long spuId;

    /**
     * SKU名称
     */
    private String skuName;

    /**
     * 类目名称
     */
    private String catName;

    /**
     * 备货单号
     */
    private String purchaseNo;

    /**
     * 规格
     */
    private String spec;

    /**
     * 商品缩略图
     */
    private String thumbUrl;

    /**
     * 质检结果
     */
    private String qcResult;

    /**
     * 质检结果更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime qcResultUpdateTime;

    /**
     * 同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 