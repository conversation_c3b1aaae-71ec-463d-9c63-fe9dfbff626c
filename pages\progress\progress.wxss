.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100rpx;
  padding: 0 30rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-btn {
  margin-right: 20rpx;
  font-size: 40rpx;
}

.title {
  font-size: 34rpx;
  font-weight: 500;
}

.role-tag {
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  background-color: #1890ff;
  color: #ffffff;
  border-radius: 16rpx;
}

.content {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.order-info {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.info-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  color: #333333;
}

.info-item {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 16rpx;
  display: flex;
}

.info-value {
  color: #333333;
  font-weight: 500;
}

.progress-title {
  font-size: 32rpx;
  font-weight: 500;
  margin: 30rpx 0 20rpx;
  color: #333333;
}

.progress-list {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.progress-item {
  display: flex;
  padding: 20rpx 0;
  position: relative;
}

.progress-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 14rpx;
  top: 50rpx;
  width: 2rpx;
  height: calc(100% - 40rpx);
  background-color: #e8e8e8;
  z-index: 1;
}

.progress-dot {
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  z-index: 2;
}

.progress-dot.done {
  background-color: #52c41a;
}

.progress-dot.todo {
  background-color: #d9d9d9;
}

.progress-content {
  flex: 1;
}

.progress-name {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.progress-name.done {
  color: #52c41a;
}

.progress-name.todo {
  color: #999999;
}

.progress-time {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 16rpx;
}

.confirm-btn {
  font-size: 26rpx;
  padding: 6rpx 20rpx;
  height: 60rpx;
  line-height: 48rpx;
  background-color: #1890ff;
  color: #ffffff;
  margin: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.logs-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.logs-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.log-item {
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.log-time {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 8rpx;
}

.log-content {
  font-size: 28rpx;
  color: #333333;
}

.empty-tip {
  text-align: center;
  padding: 100rpx 0;
  color: #999999;
  font-size: 28rpx;
}

.tabbar {
  display: flex;
  height: 100rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #e8e8e8;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.tab-text {
  font-size: 22rpx;
  color: #666666;
  margin-top: 6rpx;
}

.iconfont {
  font-size: 40rpx;
}