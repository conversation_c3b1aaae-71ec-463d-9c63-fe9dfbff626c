package com.xiao.temu.modules.purchaseorderv.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.purchaseorderv.dto.PurchaseOrderRequestDTO;
import com.xiao.temu.modules.purchaseorderv.vo.PurchaseOrderVO;
import com.xiao.temu.modules.purchaseorderv.service.PurchaseOrderService;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.system.service.UserService;
import com.xiao.temu.infrastructure.api.TemuApiClient;
import org.ehcache.shadow.org.terracotta.statistics.Time;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.xiao.temu.infrastructure.api.TemuApiClient.addParameterIfPresent;

// 导入所需类
import com.alibaba.fastjson2.JSON;
import com.xiao.temu.modules.purchaseorderv.dto.QrCodeDataDTO;
import com.xiao.temu.modules.purchaseorderv.service.ProductionProgressService;

/**
 * Temu采购单服务实现
 */
@Service
public class PurchaseOrderServiceImpl implements PurchaseOrderService {

    private static final Logger log = LoggerFactory.getLogger(PurchaseOrderServiceImpl.class);

    @Autowired
    private ShopService shopService;

    @Autowired
    private UserService userService;

    // 注入 ProductionProgressService
    @Autowired
    private ProductionProgressService productionProgressService;

    /**
     * 获取采购单列表
     *
     * @param requestDTO 请求参数
     * @param userId 当前用户ID
     * @return 采购单列表
     */
    @Override
    public PurchaseOrderVO getPurchaseOrderList(PurchaseOrderRequestDTO requestDTO, Long userId) {
        log.debug("查询Temu采购单列表, 请求参数: {}, 用户ID: {}", requestDTO, userId);
        
        PurchaseOrderVO response = new PurchaseOrderVO();
        response.setSuccess(true);
        
        try {
            // 获取店铺ID列表
            List<Long> shopIds = requestDTO.getShopIds();
            Long singleShopId = requestDTO.getShopId();
            
            // 如果shopIds为空但singleShopId不为空，将singleShopId加入shopIds
            if ((shopIds == null || shopIds.isEmpty()) && singleShopId != null) {
                shopIds = new ArrayList<>();
                shopIds.add(singleShopId);
                requestDTO.setShopIds(shopIds);
            }
            
            // 验证店铺ID参数
            if (shopIds == null || shopIds.isEmpty()) {
                throw new RuntimeException("店铺ID不能为空");
            }
            
            // 检查用户是否是管理员
            boolean isAdmin = userService.isAdmin(userId);
            // 检查是否忽略权限检查
            boolean ignorePermissionCheck = Boolean.TRUE.equals(requestDTO.getIgnorePermissionCheck());
            
            // 如果不是管理员且不忽略权限检查，需要验证店铺权限
            if (!isAdmin && !ignorePermissionCheck) {
                List<Long> validShopIds = new ArrayList<>();
                for (Long id : shopIds) {
                    if (shopService.checkShopPermission(userId, id, false)) {
                        validShopIds.add(id);
                    }
                }
                
                // 更新为有权限的店铺ID列表
                requestDTO.setShopIds(validShopIds);
                shopIds = validShopIds;
                
                // 如果没有任何有效的店铺ID，返回权限错误
                if (validShopIds.isEmpty()) {
                    response.setSuccess(false);
                    response.setErrorCode(Integer.valueOf("403"));
                    response.setErrorMsg("您没有权限访问所选店铺的数据");
                    return response;
                }
            }
            
            // 保存原始分页参数
            int requestPageNo = requestDTO.getPageNo();
            int requestPageSize = requestDTO.getPageSize();
            
            // 如果是单店铺查询，直接使用原始逻辑
            if (shopIds.size() == 1) {
                Long shopId = shopIds.get(0);
                return getSingleShopPurchaseOrders(shopId, requestDTO, userId);
            }
            
            // 采用新的多店铺查询方式
            return getMultiShopPurchaseOrders(shopIds, requestDTO, userId);
        } catch (Exception e) {
            // 异常处理
            log.error("调用采购单API失败", e);
            response.setSuccess(false);
            response.setErrorCode(Integer.valueOf("500"));
            response.setErrorMsg("调用API失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 针对单个店铺的查询逻辑
     */
    private PurchaseOrderVO getSingleShopPurchaseOrders(Long shopId, PurchaseOrderRequestDTO requestDTO, Long userId) {
        PurchaseOrderVO response = new PurchaseOrderVO();
        response.setSuccess(true);
        
        try {
            // 获取店铺信息
            Shop shop = shopService.getShopById(shopId).convertToShop();
            if (shop == null) {
                response.setSuccess(false);
                response.setErrorCode(Integer.valueOf("404"));
                response.setErrorMsg("店铺不存在");
                return response;
            }

            // 调用API获取单店铺数据
            JSONObject result = callTemuApi(shop, requestDTO);
            if (result == null) {
                response.setSuccess(false);
                response.setErrorCode(Integer.valueOf("500"));
                response.setErrorMsg("调用API失败");
                return response;
            }
            
            // 设置响应信息
            response.setSuccess(result.getBoolean("success"));
            response.setErrorCode(result.getInteger("errorCode"));
            response.setErrorMsg(result.getString("errorMsg"));
            
            if (response.getSuccess()) {
                JSONObject resultData = result.getJSONObject("result");
                if (resultData != null) {
                    // 为返回结果添加店铺信息
                    List<JSONObject> orders = resultData.getJSONArray("subOrderForSupplierList").toList(JSONObject.class);
                    if (orders != null) {
                        for (JSONObject order : orders) {
                            order.put("shopId", shopId);
                            order.put("shopName", shop.getShopName());
                            order.put("shopRemark", shop.getRemark());
                            
                            // 新增：初始化生产进度和添加二维码数据
                            String subPurchaseOrderSn = order.getString("subPurchaseOrderSn");
                            if (subPurchaseOrderSn != null && !subPurchaseOrderSn.isEmpty()) {
                                // 1. 初始化生产进度
                                try {
                                    productionProgressService.initProgress(shopId, subPurchaseOrderSn);
                                } catch (Exception e) {
                                    log.error("初始化生产进度失败 for shopId={}, sn={}", shopId, subPurchaseOrderSn, e);
                                }
                                
                                // 2. 生成二维码数据
                                QrCodeDataDTO qrCodeData = QrCodeDataDTO.builder()
                                        .shopId(shopId)
                                        .subPurchaseOrderSn(subPurchaseOrderSn)
                                        .timestamp(System.currentTimeMillis())
                                        .build();
                                // 3. 添加二维码数据JSON到订单对象
                                order.put("qrCodeDataJson", JSON.toJSONString(qrCodeData));
                            }
                        }
                    }
                }
                response.setResult(resultData);
            }
            
            response.setShopId(shopId);
            response.setShopName(shop.getShopName());
            response.setShopRemark(shop.getRemark());
            
            return response;
        } catch (Exception e) {
            log.error("获取单店铺数据失败", e);
            response.setSuccess(false);
            response.setErrorCode(Integer.valueOf("500"));
            response.setErrorMsg("调用API失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 多店铺查询 - 采用新的分页逻辑处理10000条限制
     */
    private PurchaseOrderVO getMultiShopPurchaseOrders(List<Long> shopIds, PurchaseOrderRequestDTO requestDTO, Long userId) {
        PurchaseOrderVO finalResponse = new PurchaseOrderVO();
        finalResponse.setSuccess(true);
        finalResponse.setShopName("多店铺查询"); // 设置一个标识

        List<JSONObject> collectedOrders = new ArrayList<>();
        int accessibleTotalRecordsAcrossShops = 0; // 用于累加计算 *可访问* 的总数 (每店最多10000)
        int actualTotalRecordsAcrossShops = 0;   // 用于累加计算 *实际* 的总数
        final int TEMU_LIMIT_PER_SHOP = 10000; // Temu API 单店数据限制
        final int TEMU_MAX_PAGE_SIZE = 100;    // Temu API 单次请求最大页大小 (确认值为100)

        int requestPageNo = requestDTO.getPageNo();
        int requestPageSize = requestDTO.getPageSize();
        // 计算全局请求的起始索引（从0开始）
        int requestedGlobalStartIndex = (requestPageNo - 1) * requestPageSize;
        // 计算全局请求的结束索引（不包含）
        int requestedGlobalEndIndex = requestedGlobalStartIndex + requestPageSize;

        // 用于追踪当前遍历到的店铺数据在全局虚拟列表中的起始偏移量
        int currentGlobalOffset = 0;

        // 存储每个店铺的信息以便后续使用
        // 新增：存储每个店铺的范围信息
        List<Map<String, Object>> shopDataRanges = new ArrayList<>();
        Map<Long, Integer> shopAccessibleCounts = new HashMap<>(); // 存储每个店铺的可访问记录数
        Map<Long, Shop> shopInfoMap = new HashMap<>();
        // 1. 预先获取所有店铺信息和估算总数（或尝试获取准确总数）
        for (Long shopId : shopIds) {
            Shop shop = null;
            int shopTotal = 0; // 当前店铺的实际总数
            int accessibleShopTotal = 0; // 当前店铺的可访问总数

            try {
                shop = shopService.getShopById(shopId).convertToShop();
                if (shop == null) {
                    log.warn("多店铺查询：店铺不存在, ID: {}", shopId);
                    continue;
                }
                shopInfoMap.put(shopId, shop);
                
                // 尝试获取该店铺的总记录数
                // 注意: 这仍然可能受到10000条限制的影响，如果total字段不准确，这里的totalRecordsAcrossShops会有偏差
                PurchaseOrderRequestDTO countDTO = copyRequestDTO(requestDTO);
                countDTO.setPageNo(1);
                countDTO.setPageSize(1); // 查询少量数据以获取total字段
                JSONObject countResult = callTemuApi(shop, countDTO);
                if (countResult != null && countResult.getBooleanValue("success", false)) {
                    JSONObject resultData = countResult.getJSONObject("result");
                    if (resultData != null) {
                        shopTotal = resultData.getIntValue("total", 0);
                        accessibleShopTotal = Math.min(shopTotal, TEMU_LIMIT_PER_SHOP);
                        actualTotalRecordsAcrossShops += shopTotal; // 累加实际总数
                        accessibleTotalRecordsAcrossShops += accessibleShopTotal; // 累加可访问总数
                        shopAccessibleCounts.put(shopId, accessibleShopTotal); // 存储该店铺的可访问数
                        // 如果需要更精确的总数且total字段受限，则无法简单得到
                    } else {
                        // 获取total失败，无法知道实际总数，但可访问的最多是10000
                        accessibleShopTotal = TEMU_LIMIT_PER_SHOP;
                        shopTotal = -1; // 标记实际总数未知
                        accessibleTotalRecordsAcrossShops += TEMU_LIMIT_PER_SHOP;
                        shopAccessibleCounts.put(shopId, accessibleShopTotal);
                        // actualTotalRecordsAcrossShops 不增加，因为未知
                    }
                } else {
                    log.warn("多店铺查询：获取店铺 {} 总数失败", shopId);
                    // 获取total失败，无法知道实际总数，但可访问的最多是10000
                    accessibleShopTotal = TEMU_LIMIT_PER_SHOP;
                    shopTotal = -1; // 标记实际总数未知
                    accessibleTotalRecordsAcrossShops += TEMU_LIMIT_PER_SHOP;
                    shopAccessibleCounts.put(shopId, accessibleShopTotal);
                    // actualTotalRecordsAcrossShops 不增加，因为未知
                }

            } catch (Exception e) {
                 log.error("多店铺查询：处理店铺 {} 信息或总数时出错", shopId, e);
                 // 出错，无法知道实际总数，但可访问的最多是10000
                 accessibleShopTotal = TEMU_LIMIT_PER_SHOP; // 假设可访问10000
                 shopTotal = -1; // 标记实际总数未知
                 accessibleTotalRecordsAcrossShops += TEMU_LIMIT_PER_SHOP;
                 shopAccessibleCounts.put(shopId, accessibleShopTotal);
                 // actualTotalRecordsAcrossShops 不增加，因为未知
            }

            // 计算并存储当前店铺的范围信息 (即使获取总数失败也存储，accessibleShopTotal会有值)
            if (shop != null && accessibleShopTotal > 0) { // 只有店铺存在且可访问数>0才记录范围
                int rangeStart = currentGlobalOffset + 1; // 1-based start index
                int rangeEnd = currentGlobalOffset + accessibleShopTotal; // 1-based end index
                Map<String, Object> rangeInfo = new HashMap<>();
                rangeInfo.put("shopId", shop.getShopId());
                rangeInfo.put("shopName", shop.getShopName());
                rangeInfo.put("startIndex", rangeStart);
                rangeInfo.put("endIndex", rangeEnd);
                rangeInfo.put("accessibleCount", accessibleShopTotal);
                rangeInfo.put("actualCount", shopTotal); // 可能为 -1 如果获取失败
                shopDataRanges.add(rangeInfo);

                // 更新下一个店铺的起始偏移量 (0-based)
                currentGlobalOffset += accessibleShopTotal;
            } else if (shop != null) {
                // 店铺存在但可访问数为0，仍需记录，范围为空
                Map<String, Object> rangeInfo = new HashMap<>();
                rangeInfo.put("shopId", shop.getShopId());
                rangeInfo.put("shopName", shop.getShopName());
                rangeInfo.put("startIndex", currentGlobalOffset + 1);
                rangeInfo.put("endIndex", currentGlobalOffset); // start > end 表示空范围
                rangeInfo.put("accessibleCount", 0);
                rangeInfo.put("actualCount", shopTotal >= 0 ? shopTotal : 0); // 实际数已知则用，否则0
                shopDataRanges.add(rangeInfo);
                // currentGlobalOffset 不变
            }
        }

        // 2. 遍历店铺，收集所需数据片段
        currentGlobalOffset = 0; // 重置偏移量
        for (Long shopId : shopIds) {
            if (!shopInfoMap.containsKey(shopId)) continue; // 跳过无效店铺

            Shop currentShop = shopInfoMap.get(shopId);
            // 获取预先计算好的该店铺的可访问记录数
            int accessibleShopTotal = shopAccessibleCounts.getOrDefault(shopId, 0);
            if (accessibleShopTotal <= 0) {
                // 如果该店铺无数据或获取总数失败，直接跳过，并更新偏移量（虽然是0）
                // currentGlobalOffset += accessibleShopTotal; // 已经是0，无需更新
                continue;
            }

            // 计算当前店铺在全局虚拟列表中的范围 [shopGlobalStartIndex, shopGlobalEndIndex)
            int shopGlobalStartIndex = currentGlobalOffset;
            int shopGlobalEndIndex = currentGlobalOffset + accessibleShopTotal; // 使用实际可访问数

            // 计算请求范围与当前店铺范围的重叠部分 [overlapStart, overlapEnd)
            int overlapStart = Math.max(requestedGlobalStartIndex, shopGlobalStartIndex);
            int overlapEnd = Math.min(requestedGlobalEndIndex, shopGlobalEndIndex);

            // 如果有重叠，且收集到的数据还没满
            if (overlapStart < overlapEnd && collectedOrders.size() < requestPageSize) {
                // 计算需要从当前店铺获取的数据在其 *内部* 的索引范围 [localStartIndex, localEndIndex)
                int localStartIndex = overlapStart - shopGlobalStartIndex;
                int localEndIndex = overlapEnd - shopGlobalStartIndex;
                int countNeededFromShop = localEndIndex - localStartIndex;

                if (countNeededFromShop > 0) {
                    // 计算需要调用Temu API的页码范围
                    int firstPageToFetch = (localStartIndex / TEMU_MAX_PAGE_SIZE) + 1;
                    int lastPageToFetch = ((localEndIndex - 1) / TEMU_MAX_PAGE_SIZE) + 1; // 包含localEndIndex-1所在的页

                    List<JSONObject> fetchedFromShop = new ArrayList<>();
                    try {
                        // 循环调用API获取覆盖所需范围的所有页
                        for (int page = firstPageToFetch; page <= lastPageToFetch; page++) {
                            PurchaseOrderRequestDTO fetchDTO = copyRequestDTO(requestDTO);
                            fetchDTO.setPageNo(page);
                            fetchDTO.setPageSize(TEMU_MAX_PAGE_SIZE);
                    
                            JSONObject apiResult = callTemuApi(currentShop, fetchDTO);

                            if (apiResult != null && apiResult.getBooleanValue("success", false)) {
                                JSONObject resultData = apiResult.getJSONObject("result");
                        if (resultData != null) {
                                    List<JSONObject> orders = resultData.getJSONArray("subOrderForSupplierList").toList(JSONObject.class);
                                    if (orders != null && !orders.isEmpty()) {
                                        fetchedFromShop.addAll(orders);
                                    } else {
                                        // 如果某页为空，可能意味着已到末尾，提前停止
                                                break;
                                            }
                                } else {
                                    break; // 没有result数据，停止
                                }
                            } else {
                                log.warn("多店铺查询：调用店铺 {} API 第 {} 页失败", shopId, page);
                                // 可以选择中断或继续尝试下一页/店铺
                                break; // 暂时中断
                }
            }
            
                        // 从获取到的数据(fetchedFromShop)中精确截取所需的部分
                        // 计算在 fetchedFromShop 列表中的起始索引
                        int sliceStartIndex = localStartIndex - (firstPageToFetch - 1) * TEMU_MAX_PAGE_SIZE;
                        int sliceEndIndex = sliceStartIndex + countNeededFromShop;

                        // 边界检查
                        sliceStartIndex = Math.max(0, sliceStartIndex);
                        sliceEndIndex = Math.min(fetchedFromShop.size(), sliceEndIndex);

                        if (sliceStartIndex < sliceEndIndex) {
                            List<JSONObject> segment = fetchedFromShop.subList(sliceStartIndex, sliceEndIndex);
                            // 为数据添加店铺信息和二维码数据
                            for (JSONObject order : segment) {
                                // 原有逻辑：添加店铺信息
                                order.put("shopId", currentShop.getShopId());
                                order.put("shopName", currentShop.getShopName());
                                order.put("shopRemark", currentShop.getRemark());
                                
                                // 新增：初始化生产进度和添加二维码数据
                                String subPurchaseOrderSn = order.getString("subPurchaseOrderSn");
                                Long currentOrderShopId = currentShop.getShopId(); // 获取当前循环的 shopId
                                if (subPurchaseOrderSn != null && !subPurchaseOrderSn.isEmpty()) {
                                    // 1. 初始化生产进度
                                    try {
                                        productionProgressService.initProgress(currentOrderShopId, subPurchaseOrderSn);
                                    } catch (Exception e) {
                                        log.error("初始化生产进度失败 for shopId={}, sn={}", currentOrderShopId, subPurchaseOrderSn, e);
                                    }
                                    
                                    // 2. 生成二维码数据
                                    QrCodeDataDTO qrCodeData = QrCodeDataDTO.builder()
                                            .shopId(currentOrderShopId)
                                            .subPurchaseOrderSn(subPurchaseOrderSn)
                                            .timestamp(System.currentTimeMillis())
                                            .build();
                                    // 3. 添加二维码数据JSON到订单对象
                                    order.put("qrCodeDataJson", JSON.toJSONString(qrCodeData));
                                }
                            }
                            collectedOrders.addAll(segment);
                        }

                    } catch (Exception e) {
                        log.error("多店铺查询：获取店铺 {} 数据时出错 (页范围 {}-{})", shopId, firstPageToFetch, lastPageToFetch, e);
                        // 出错时，可以选择继续处理下一个店铺或直接返回错误
                    }
                }
            }

            // 更新全局偏移量，为下一个店铺做准备
            currentGlobalOffset += accessibleShopTotal; // 使用实际可访问数更新偏移量

            // 如果已经收集到足够的数据，提前结束循环
            if (collectedOrders.size() >= requestPageSize) {
                break;
                }
            }

        // 3. 组装最终结果
        JSONObject finalResultData = new JSONObject();
        finalResultData.put("total", actualTotalRecordsAcrossShops); // 使用累加的 *实际* 总数
        finalResultData.put("shopDataRanges", shopDataRanges); // 添加店铺范围信息
         // 确保返回的数据不超过请求的 pageSize
        List<JSONObject> ordersToReturn = collectedOrders.size() > requestPageSize
                ? collectedOrders.subList(0, requestPageSize)
                : collectedOrders;
        finalResultData.put("subOrderForSupplierList", ordersToReturn);


        finalResponse.setResult(finalResultData);

        return finalResponse;
    }

    /**
     * 调用Temu API的公共方法
     */
    private JSONObject callTemuApi(Shop shop, PurchaseOrderRequestDTO requestDTO) {
        try {
            // 设置API调用所需的公共参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.purchaseorderv2.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());

            // 设置业务参数
            Map<String, Object> businessParams = new HashMap<>();
            // 必要的分页参数
            businessParams.put("pageNo", requestDTO.getPageNo());
            businessParams.put("pageSize", requestDTO.getPageSize());
            businessParams.put("timestamp", String.valueOf(Time.time()));
            
            // 添加所有可选参数
            addParameterIfPresent(businessParams, "purchaseTimeFrom", requestDTO.getPurchaseTimeFrom());
            addParameterIfPresent(businessParams, "purchaseTimeTo", requestDTO.getPurchaseTimeTo());
            addParameterIfPresent(businessParams, "originalPurchaseOrderSnList", requestDTO.getOriginalPurchaseOrderSnList());
            addParameterIfPresent(businessParams, "deliverOrArrivalDelayStatusList", requestDTO.getDeliverOrArrivalDelayStatusList());
            addParameterIfPresent(businessParams, "productSnList", requestDTO.getProductSnList());
            addParameterIfPresent(businessParams, "productSkuIdList", requestDTO.getProductSkuIdList());
            addParameterIfPresent(businessParams, "supplierIdList", requestDTO.getSupplierIdList());
            addParameterIfPresent(businessParams, "skuLackSnapshot", requestDTO.getSkuLackSnapshot());
            addParameterIfPresent(businessParams, "sourceList", requestDTO.getSourceList());
            addParameterIfPresent(businessParams, "deliverOrderSnList", requestDTO.getDeliverOrderSnList());
            addParameterIfPresent(businessParams, "urgencyType", requestDTO.getUrgencyType());
            addParameterIfPresent(businessParams, "subPurchaseOrderSnList", requestDTO.getSubPurchaseOrderSnList());
            addParameterIfPresent(businessParams, "purchaseStockType", requestDTO.getPurchaseStockType());
            addParameterIfPresent(businessParams, "inventoryRegionList", requestDTO.getInventoryRegionList());
            addParameterIfPresent(businessParams, "productSkcIdList", requestDTO.getProductSkcIdList());
            addParameterIfPresent(businessParams, "settlementType", requestDTO.getSettlementType());
            addParameterIfPresent(businessParams, "isSystemAutoPurchaseSource", requestDTO.getIsSystemAutoPurchaseSource());
            addParameterIfPresent(businessParams, "isDelayDeliver", requestDTO.getIsDelayDeliver());
            addParameterIfPresent(businessParams, "isDelayArrival", requestDTO.getIsDelayArrival());
            addParameterIfPresent(businessParams, "expectLatestDeliverTimeFrom", requestDTO.getExpectLatestDeliverTimeFrom());
            addParameterIfPresent(businessParams, "expectLatestDeliverTimeTo", requestDTO.getExpectLatestDeliverTimeTo());
            addParameterIfPresent(businessParams, "expectLatestArrivalTimeFrom", requestDTO.getExpectLatestArrivalTimeFrom());
            addParameterIfPresent(businessParams, "expectLatestArrivalTimeTo", requestDTO.getExpectLatestArrivalTimeTo());
            addParameterIfPresent(businessParams, "isFirst", requestDTO.getIsFirst());
            addParameterIfPresent(businessParams, "qcReject", requestDTO.getQcReject());
            addParameterIfPresent(businessParams, "qcOption", requestDTO.getQcOption());
            addParameterIfPresent(businessParams, "qcNotPassCreate", requestDTO.getQcNotPassCreate());
            addParameterIfPresent(businessParams, "lackOrSoldOutTagList", requestDTO.getLackOrSoldOutTagList());
            addParameterIfPresent(businessParams, "hotTag", requestDTO.getHotTag());
            addParameterIfPresent(businessParams, "canDeliverStartTime", requestDTO.getCanDeliverStartTime());
            addParameterIfPresent(businessParams, "canDeliverEndTime", requestDTO.getCanDeliverEndTime());
            addParameterIfPresent(businessParams, "productLabelCodeStyle", requestDTO.getProductLabelCodeStyle());
            addParameterIfPresent(businessParams, "inboundReturn", requestDTO.getInboundReturn());
            addParameterIfPresent(businessParams, "inboundReturnCreate", requestDTO.getInboundReturnCreate());
            addParameterIfPresent(businessParams, "inFulfilmentPunish", requestDTO.getInFulfilmentPunish());
            addParameterIfPresent(businessParams, "deliverOrArrivalDelayStatusList", requestDTO.getDeliverOrArrivalDelayStatusList());
            addParameterIfPresent(businessParams, "isTodayPlatformPurchase", requestDTO.getIsTodayPlatformPurchase());
            // 添加新增的查询条件
            addParameterIfPresent(businessParams, "hotInventoryLack", requestDTO.getHotInventoryLack());
            addParameterIfPresent(businessParams, "todayCanDeliver", requestDTO.getTodayCanDeliver());
            addParameterIfPresent(businessParams, "isCloseJit", requestDTO.getIsCloseJit());
            // 添加收货仓库ID列表参数
            addParameterIfPresent(businessParams, "subWarehouseIdList", requestDTO.getSubWarehouseIdList());
            // 添加状态筛选参数
            addParameterIfPresent(businessParams, "statusList", requestDTO.getStatusList());
            
            // 添加排序参数
            if (requestDTO.getOneDimensionSort() != null) {
                Map<String, Object> sortParams = new HashMap<>();
                sortParams.put("firstOrderByParam", requestDTO.getOneDimensionSort().getFirstOrderByParam());
                sortParams.put("firstOrderByDesc", requestDTO.getOneDimensionSort().getFirstOrderByDesc());
                businessParams.put("oneDimensionSort", sortParams);
            }
            
            // 调用API获取结果
            return TemuApiClient.sendRequest(commonParams, businessParams);
        } catch (Exception e) {
            log.error("调用Temu API失败", e);
            return null;
        }
    }

    /**
     * 创建请求DTO的副本
     */
    private PurchaseOrderRequestDTO copyRequestDTO(PurchaseOrderRequestDTO original) {
        PurchaseOrderRequestDTO copy = new PurchaseOrderRequestDTO();
        // 复制分页参数
        copy.setPageNo(original.getPageNo());
        copy.setPageSize(original.getPageSize());
        // 复制其他查询参数
        copy.setPurchaseTimeFrom(original.getPurchaseTimeFrom());
        copy.setPurchaseTimeTo(original.getPurchaseTimeTo());
        copy.setOriginalPurchaseOrderSnList(original.getOriginalPurchaseOrderSnList());
        copy.setDeliverOrArrivalDelayStatusList(original.getDeliverOrArrivalDelayStatusList());
        copy.setProductSnList(original.getProductSnList());
        copy.setProductSkuIdList(original.getProductSkuIdList());
        copy.setSupplierIdList(original.getSupplierIdList());
        copy.setSkuLackSnapshot(original.getSkuLackSnapshot());
        copy.setSourceList(original.getSourceList());
        copy.setDeliverOrderSnList(original.getDeliverOrderSnList());
        copy.setUrgencyType(original.getUrgencyType());
        copy.setSubPurchaseOrderSnList(original.getSubPurchaseOrderSnList());
        copy.setPurchaseStockType(original.getPurchaseStockType());
        copy.setInventoryRegionList(original.getInventoryRegionList());
        copy.setProductSkcIdList(original.getProductSkcIdList());
        copy.setSettlementType(original.getSettlementType());
        copy.setIsSystemAutoPurchaseSource(original.getIsSystemAutoPurchaseSource());
        copy.setIsDelayDeliver(original.getIsDelayDeliver());
        copy.setIsDelayArrival(original.getIsDelayArrival());
        copy.setExpectLatestDeliverTimeFrom(original.getExpectLatestDeliverTimeFrom());
        copy.setExpectLatestDeliverTimeTo(original.getExpectLatestDeliverTimeTo());
        copy.setExpectLatestArrivalTimeFrom(original.getExpectLatestArrivalTimeFrom());
        copy.setExpectLatestArrivalTimeTo(original.getExpectLatestArrivalTimeTo());
        copy.setIsFirst(original.getIsFirst());
        copy.setQcReject(original.getQcReject());
        copy.setQcOption(original.getQcOption());
        copy.setQcNotPassCreate(original.getQcNotPassCreate());
        copy.setLackOrSoldOutTagList(original.getLackOrSoldOutTagList());
        copy.setHotTag(original.getHotTag());
        copy.setCanDeliverStartTime(original.getCanDeliverStartTime());
        copy.setCanDeliverEndTime(original.getCanDeliverEndTime());
        copy.setProductLabelCodeStyle(original.getProductLabelCodeStyle());
        copy.setInboundReturn(original.getInboundReturn());
        copy.setInboundReturnCreate(original.getInboundReturnCreate());
        copy.setInFulfilmentPunish(original.getInFulfilmentPunish());
        copy.setIsTodayPlatformPurchase(original.getIsTodayPlatformPurchase());
        copy.setHotInventoryLack(original.getHotInventoryLack());
        copy.setTodayCanDeliver(original.getTodayCanDeliver());
        copy.setIsCloseJit(original.getIsCloseJit());
        copy.setIgnorePermissionCheck(original.getIgnorePermissionCheck());
        // 复制收货仓库ID列表
        copy.setSubWarehouseIdList(original.getSubWarehouseIdList());
        // 复制状态筛选参数
        copy.setStatusList(original.getStatusList());
        // 复制排序参数
        copy.setOneDimensionSort(original.getOneDimensionSort());
        return copy;
    }

    /**
     * 全局数据位置映射类
     */
    private static class ShopDataLocation {
        final Long shopId;
        final int localIndex;
        final int globalIndex;
        
        ShopDataLocation(Long shopId, int localIndex, int globalIndex) {
            this.shopId = shopId;
            this.localIndex = localIndex;
            this.globalIndex = globalIndex;
        }
    }
} 