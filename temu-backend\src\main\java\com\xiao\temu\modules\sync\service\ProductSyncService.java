package com.xiao.temu.modules.sync.service;

import com.xiao.temu.modules.product.entity.Product;
import com.xiao.temu.modules.sync.entity.ProductSyncTask;
import com.xiao.temu.modules.sync.vo.ApiResponse;

import java.util.List;
import java.util.Set;

/**
 * 商品数据同步服务接口
 */
public interface ProductSyncService {

    /**
     * 同步指定店铺的商品数据
     *
     * @param shopId 店铺ID
     * @return 同步结果
     */
    ApiResponse.ProductSyncVO syncProductData(Long shopId);
    
    /**
     * 同步指定SKC ID列表的商品数据
     *
     * @param shopId 店铺ID
     * @param skcIds 需要同步的商品SKC ID集合
     * @return 同步的商品列表
     */
    List<Product> syncProductsBySkcIds(Long shopId, Set<Long> skcIds);

    /**
     * 初始化店铺的同步任务
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    boolean initSyncTask(Long shopId);

    /**
     * 获取同步任务列表
     *
     * @param shopIds 店铺ID列表，为空则查询所有
     * @return 同步任务列表
     */
    List<ProductSyncTask> getSyncTasks(List<Long> shopIds);

    /**
     * 根据店铺ID获取同步任务
     *
     * @param shopId 店铺ID
     * @return 同步任务
     */
    ProductSyncTask getSyncTaskByShopId(Long shopId);

    /**
     * 执行定时同步
     * 
     * @return 同步结果
     */
    String executeScheduledSync();
    
    /**
     * 获取指定店铺的商品总数
     *
     * @param shopId 店铺ID
     * @return 商品总数
     */
    Integer getProductCountByShopId(Long shopId);
    
    /**
     * 获取指定店铺的商品SKU总数
     *
     * @param shopId 店铺ID
     * @return 商品SKU总数
     */
    Integer getProductSkuCountByShopId(Long shopId);
    
    /**
     * 清空店铺的商品同步数据
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    boolean clearSyncData(Long shopId);
} 