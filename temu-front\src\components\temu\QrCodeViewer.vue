<template>
  <div class="qr-code-viewer">
    <img 
      v-if="qrCodeUrl" 
      :src="qrCodeUrl" 
      class="qr-code-image" 
      alt="备货单二维码"
      @click="handlePreview"
    />
    <div v-else class="qr-code-placeholder">
      <el-icon><Picture /></el-icon>
      <span>无二维码</span>
    </div>
    
    <!-- 二维码预览弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="备货单二维码"
      width="350px"
      align-center
      destroy-on-close
      :modal="true"
      modal-class="qr-code-dialog-modal"
      :append-to-body="true"
      :close-on-click-modal="true"
      :show-close="true"
    >
      <div class="qr-code-preview">
        <img :src="qrCodeUrl" class="qr-code-large" alt="备货单二维码" />
        <div class="qr-info">
          <p>备货单号: {{ purchaseOrderSn }}</p>
          <p>扫描此二维码可更新生产进度</p>
        </div>
        <div class="qr-actions">
          <el-button type="primary" @click="handlePrint">打印二维码</el-button>
          <el-button @click="handleDownload">下载二维码</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, withDefaults, onMounted, computed, watch, onUnmounted } from 'vue';
import { Picture } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import QRCode from 'qrcode';

const props = withDefaults(defineProps<{
  qrCodeUrl?: string;
  purchaseOrderSn?: string;
  dataString?: string;
}>(), {
  qrCodeUrl: '',
  purchaseOrderSn: '',
  dataString: ''
});

// 弹窗可见性
const dialogVisible = ref(false);
// 本地生成的二维码URL
const generatedQrCodeUrl = ref('');
// 防抖定时器
let debounceTimer: number | null = null;

// 计算属性：优先使用生成的二维码URL，其次使用传入的URL
const qrCodeUrl = computed(() => {
  return generatedQrCodeUrl.value || props.qrCodeUrl;
});

// 带防抖的二维码生成
const debouncedGenerateQrCode = () => {
  // 如果已经有定时器，清除它
  if (debounceTimer !== null) {
    clearTimeout(debounceTimer);
  }
  
  // 设置新的定时器
  debounceTimer = setTimeout(() => {
    console.log('生成二维码，数据:', props.dataString?.substring(0, 30) + '...');
    console.log('备货单号:', props.purchaseOrderSn);
    generateQrCode();
    debounceTimer = null;
  }, 300) as unknown as number;
};

// 生成二维码
const generateQrCode = async () => {
  if (!props.dataString) return;
  
  try {
    // 从JSON字符串中解析数据
    let dataToEncode = props.dataString;
    
    // 尝试解析JSON，如果是JSON字符串的话
    try {
      const jsonData = JSON.parse(props.dataString);
      // 可以选择格式化JSON或使用原始字符串
      dataToEncode = props.dataString; // 使用原始字符串
    } catch (e) {
      // 不是有效的JSON，使用原字符串
    }
    
    // 使用qrcode库生成data URL
    const dataUrl = await QRCode.toDataURL(dataToEncode, {
      width: 200,
      margin: 1,
      errorCorrectionLevel: 'M'
    });
    
    generatedQrCodeUrl.value = dataUrl;
  } catch (error) {
    console.error('生成二维码失败:', error);
    ElMessage.error('生成二维码失败');
  }
};

// 点击预览二维码
const handlePreview = () => {
  if (!qrCodeUrl.value) {
    ElMessage.warning('二维码暂未生成');
    return;
  }
  dialogVisible.value = true;
};

// 打印二维码
const handlePrint = () => {
  if (!qrCodeUrl.value) {
    ElMessage.warning('二维码暂未生成');
    return;
  }
  
  // 创建一个隐藏的iframe用于打印
  const printFrame = document.createElement('iframe');
  printFrame.style.position = 'absolute';
  printFrame.style.top = '-999px';
  printFrame.style.left = '-999px';
  printFrame.style.width = '0';
  printFrame.style.height = '0';
  document.body.appendChild(printFrame);
  
  // 设置打印内容
  const frameDoc = printFrame.contentWindow?.document;
  if (frameDoc) {
    frameDoc.open();
    frameDoc.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>备货单二维码</title>
        <style>
          body {
            text-align: center;
            padding: 20px;
            font-family: Arial, sans-serif;
          }
          .qr-container {
            margin: 0 auto;
            max-width: 300px;
          }
          .qr-image {
            width: 200px;
            height: 200px;
            margin: 10px auto;
          }
          .qr-info {
            margin-top: 10px;
            font-size: 14px;
          }
          .timestamp {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
          }
        </style>
      </head>
      <body>
        <div class="qr-container">
          <h3>备货单二维码</h3>
          <img src="${qrCodeUrl.value}" class="qr-image" alt="备货单二维码" />
          <div class="qr-info">
            <p>备货单号: ${props.purchaseOrderSn}</p>
            <p>扫描此二维码可更新生产进度</p>
            <p class="timestamp">打印时间: ${new Date().toLocaleString()}</p>
          </div>
        </div>
      </body>
      </html>
    `);
    frameDoc.close();
    
    // 等待图片加载完成后打印
    setTimeout(() => {
      printFrame.contentWindow?.print();
      // 延迟移除iframe
      setTimeout(() => {
        document.body.removeChild(printFrame);
        ElMessage.success('二维码打印成功');
      }, 500);
    }, 500);
  }
};

// 下载二维码
const handleDownload = () => {
  if (!qrCodeUrl.value) return;
  
  const link = document.createElement('a');
  link.href = qrCodeUrl.value;
  link.download = `备货单二维码_${props.purchaseOrderSn || new Date().getTime()}.png`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  ElMessage.success('二维码已下载');
};

// 组件挂载时清理资源
onMounted(() => {
  // 组件挂载时已通过watch的immediate选项触发二维码生成
});

// 组件卸载时清理资源
onUnmounted(() => {
  if (debounceTimer !== null) {
    clearTimeout(debounceTimer);
    debounceTimer = null;
  }
});

// 监听dataString的变化
watch(
  () => props.dataString, 
  (newValue) => {
    if (newValue) {
      debouncedGenerateQrCode();
    } else {
      // 如果dataString为空，清空生成的二维码URL
      generatedQrCodeUrl.value = '';
    }
  },
  { immediate: true }
);

// 监听purchaseOrderSn的变化，确保弹窗中显示正确的备货单号
watch(
  () => props.purchaseOrderSn,
  () => {
    // 备货单号变化时，如果有dataString，重新生成二维码
    if (props.dataString) {
      debouncedGenerateQrCode();
    }
  }
);
</script>

<style scoped>
.qr-code-viewer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.qr-code-image {
  width: 80px;
  height: 80px;
  cursor: pointer;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  transition: transform 0.2s;
}

.qr-code-image:hover {
  transform: scale(1.05);
}

.qr-code-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  background-color: #f5f7fa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  color: #909399;
  font-size: 12px;
}

.qr-code-placeholder .el-icon {
  font-size: 16px;
  margin-bottom: 2px;
}

.qr-code-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 20px;
}

.qr-code-large {
  width: 200px;
  height: 200px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.qr-info {
  margin: 15px 0;
  text-align: center;
}

.qr-info p {
  margin: 5px 0;
  color: #606266;
}

.qr-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
}

/* 添加弹窗遮罩样式 */
:deep(.qr-code-dialog-modal) {
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(2px);
}

:deep(.el-dialog) {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
  z-index: 2001 !important;
}
</style> 