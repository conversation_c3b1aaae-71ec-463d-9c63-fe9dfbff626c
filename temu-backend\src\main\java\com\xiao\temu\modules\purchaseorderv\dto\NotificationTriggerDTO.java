package com.xiao.temu.modules.purchaseorderv.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 通知触发DTO
 */
@Data
public class NotificationTriggerDTO {
    
    /**
     * 通知类型
     */
    @NotNull(message = "通知类型不能为空")
    private Integer notificationType;
    
    /**
     * 备货单号列表
     */
    @NotEmpty(message = "备货单号列表不能为空")
    private List<String> orderSnList;
    
    /**
     * 店铺ID
     */
    @NotNull(message = "店铺ID不能为空")
    private Long shopId;
    
    /**
     * 是否跳过小时检查（测试用）
     */
    private Boolean skipHoursCheck = false;
    
    /**
     * 是否跳过天数检查（测试用）
     */
    private Boolean skipDaysCheck = false;
    
    /**
     * 是否跳过通知次数限制（测试用）
     */
    private Boolean skipNotifyCountCheck = false;
    
    /**
     * 模拟当前时间（测试用）
     */
    private String mockCurrentTime;
    
    /**
     * 是否使用批量发送模式
     */
    private Boolean batchSend = true;
} 