package com.xiao.temu.modules.sales.controller;

import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.modules.sales.dto.SalesStatisticsQueryDTO;
import com.xiao.temu.modules.sales.service.SalesStatisticsService;
import com.xiao.temu.modules.sales.vo.ShopSalesStatisticsVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售统计控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/sales/statistics")
public class SalesStatisticsController {

    private static final Logger logger = LoggerFactory.getLogger(SalesStatisticsController.class);

    @Autowired
    private SalesStatisticsService salesStatisticsService;

    /**
     * 获取店铺销售统计数据
     *
     * @param queryDTO 查询条件
     * @return 销售统计数据
     */
    @PostMapping("/query")
    public ApiResponse<List<ShopSalesStatisticsVO>> getShopSalesStatistics(@RequestBody SalesStatisticsQueryDTO queryDTO) {
        logger.info("获取店铺销售统计数据请求: {}", queryDTO);
        
        if (queryDTO.getShopIds() == null || queryDTO.getShopIds().isEmpty()) {
            return ApiResponse.error("请选择至少一个店铺");
        }
        
        try {
            List<ShopSalesStatisticsVO> statisticsList = salesStatisticsService.getShopSalesStatistics(queryDTO);
            return ApiResponse.success(statisticsList);
        } catch (Exception e) {
            logger.error("获取销售统计数据异常", e);
            return ApiResponse.error("获取销售统计数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 手动触发同步店铺销售统计数据（带重试机制）
     *
     * @param shopId 店铺ID
     * @return 同步结果
     */
    @PostMapping("/sync/{shopId}")
    public ApiResponse<ShopSalesStatisticsVO> syncShopSalesStatistics(@PathVariable Long shopId) {
        logger.info("手动触发同步店铺销售统计数据，店铺ID: {}", shopId);
        
        if (shopId == null || shopId <= 0) {
            return ApiResponse.error("店铺ID不能为空");
        }
        
        try {
            // 使用带重试机制的方法
            return salesStatisticsService.syncShopSalesStatisticsWithRetry(shopId);
        } catch (Exception e) {
            logger.error("同步店铺销售统计数据异常", e);
            return ApiResponse.error("同步销售统计数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 手动触发所有店铺的销售统计数据同步（多线程并行处理）
     *
     * @return 同步结果
     */
    @PostMapping("/sync-all")
    public ApiResponse<String> syncAllShopSalesStatistics() {
        logger.info("手动触发所有店铺的销售统计数据同步（多线程并行处理）");
        
        try {
            String result = salesStatisticsService.executeScheduledStatistics();
            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("同步所有店铺销售统计数据异常", e);
            return ApiResponse.error("同步所有店铺销售统计数据失败: " + e.getMessage());
        }
    }
} 