package com.xiao.temu.modules.purchaseorderv.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 备货单通知配置实体类
 */
@Data
@TableName("purchase_order_notification_config")
public class PurchaseOrderNotificationConfig {
    
    /**
     * 配置ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 通知类型(1:JIT即将逾期 2:JIT已逾期 3:普通备货未发货 4:普通备货未到货)
     */
    private Integer notificationType;
    
    /**
     * 通知名称
     */
    private String notificationName;
    
    /**
     * 是否启用(false:禁用 true:启用)
     */
    private Boolean enabled;
    
    /**
     * 检查间隔(小时)
     */
    private Integer checkInterval;
    
    /**
     * 普通备货触发天数
     */
    private Integer normalTriggerDays;
    
    /**
     * JIT备货触发小时数
     */
    private Integer jitTriggerHours;
    
    /**
     * 最大通知次数
     */
    private Integer maxNotifyCount;
    
    /**
     * 通知间隔(小时)
     */
    private Integer notifyIntervalHours;
    
    /**
     * 消息模板代码
     */
    private String templateCode;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 