package com.xiao.temu.modules.sync.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.sync.entity.RefundPackageSyncTask;
import org.apache.ibatis.annotations.Mapper;

import java.util.Map;

/**
 * 退货包裹同步任务Mapper接口
 */
@Mapper
public interface RefundPackageSyncTaskMapper extends BaseMapper<RefundPackageSyncTask> {
    
    /**
     * 直接清空时间字段
     * 
     * @param params 参数，包含shopId
     * @return 更新行数
     */
    int clearTimeFields(Map<String, Object> params);
} 