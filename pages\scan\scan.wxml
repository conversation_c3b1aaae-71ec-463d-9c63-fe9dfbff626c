<view class="container">
  <page-header title="{{scanResult ? '扫描结果' : '扫描二维码'}}" showBack="{{scanResult}}" bind:back="backToScan"></page-header>
  
  <!-- 扫码界面 -->
  <block wx:if="{{!scanResult}}">
    <!-- 标签页切换 -->
    <view class="tabs">
      <view class="tab {{activeTab === 'fast' ? 'active' : ''}}" bindtap="switchTab" data-tab="fast">快速扫码</view>
      <view class="tab {{activeTab === 'normal' ? 'active' : ''}}" bindtap="switchTab" data-tab="normal">普通扫码</view>
    </view>
    
    <!-- 标签页内容包装器 -->
    <view class="tab-content-wrapper">
      <!-- 快速扫码标签页内容 -->
      <view class="content tab-content {{activeTab === 'fast' ? 'active-content' : 'hidden-content'}}" hidden="{{activeTab !== 'fast'}}">
        <view class="fast-scan-config">
          <view class="config-header">
            <view class="config-title">选择要完成的工序</view>
          </view>
          
          <view class="role-section" wx:if="{{userRoles && userRoles.length > 0}}">
            <view class="role-list">
              <block wx:for="{{userRoles}}" wx:key="roleKey">
                <view 
                  class="role-item {{selectedUserRoles.includes(item.roleKey) ? 'selected' : ''}}"
                >
                  <!-- 移除外层view的bindtap事件，将点击分为两部分处理 -->
                  <view class="role-checkbox">
                    <custom-checkbox 
                      id="checkbox-{{item.roleKey}}"
                      checked="{{selectedUserRoles.includes(item.roleKey)}}" 
                      value="{{item.roleKey}}"
                      bind:change="handleRoleCheckboxChange"
                    />
                  </view>
                  <view class="role-name {{item.styleClass}}" bindtap="toggleUserRole" data-role="{{item.roleKey}}">
                    {{item.roleName}}
                  </view>
                </view>
              </block>
            </view>
          </view>
          
          <view class="config-note">快速扫码模式下，扫描二维码将直接完成所选工序，不会显示详情页面</view>
        </view>
        
        <view class="scan-container">
          <view class="scanner-frame" bindtap="scanCode">
            <view class="scan-line"></view>
            <view class="scan-border">
              <view class="scan-corner corner-tl"></view>
              <view class="scan-corner corner-tr"></view>
              <view class="scan-corner corner-bl"></view>
              <view class="scan-corner corner-br"></view>
            </view>
          </view>
          
          <button class="scan-button" bindtap="scanCode">点击扫码</button>
        </view>
        
        <view class="album-btn-container">
          <button class="album-btn" bindtap="chooseImage">
            <text class="album-icon">🖼️</text>
            <text class="btn-text">从相册选择</text>
          </button>
        </view>
      </view>
      
      <!-- 普通扫码标签页内容 -->
      <view class="content tab-content {{activeTab === 'normal' ? 'active-content' : 'hidden-content'}}" hidden="{{activeTab !== 'normal'}}">
        <view class="normal-scan-spacer"></view>
        <view class="scan-container">
          <view class="scanner-frame" bindtap="scanCode">
            <view class="scan-line"></view>
            <view class="scan-border">
              <view class="scan-corner corner-tl"></view>
              <view class="scan-corner corner-tr"></view>
              <view class="scan-corner corner-bl"></view>
              <view class="scan-corner corner-br"></view>
            </view>
          </view>
          
          <button class="scan-button" bindtap="scanCode">点击扫码</button>
        </view>
        
        <view class="album-btn-container">
          <button class="album-btn" bindtap="chooseImage">
            <text class="album-icon">🖼️</text>
            <text class="btn-text">从相册选择</text>
          </button>
        </view>
      </view>
    </view>
  </block>
  
  <!-- 扫码结果界面 -->
  <view class="content result-content" wx:if="{{scanResult}}">
    <view class="scan-result">
      <view class="result-header">备货单信息</view>
      
      <view class="result-info">
        <view class="info-item">
          <view class="info-label">备货单号:</view>
          <view class="info-value">{{scanResult.subPurchaseOrderSn}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">店铺ID:</view>
          <view class="info-value">{{scanResult.shopId}}</view>
        </view>
      </view>
      
      <!-- 显示进度状态 - 改为可选择的形式 -->
      <view class="progress-section" wx:if="{{scanResult.progressDetail && progressItems && progressItems.length > 0}}">
        <view class="progress-title">选择要完成的工序</view>
        
        <view class="progress-list">
          <block wx:for="{{progressItems}}" wx:key="type">
            <view 
              class="progress-item {{scanResult.progressDetail[item.status] === 1 ? 'completed' : ''}} {{selectedProgressTypes.includes(item.type) ? 'selected' : ''}}" 
            >
              <!-- 分离复选框和文本区域的点击事件 -->
              <view class="progress-name">
                <view class="progress-checkbox">
                  <custom-checkbox 
                    id="progress-checkbox-{{item.type}}"
                    checked="{{selectedProgressTypes.includes(item.type)}}" 
                    value="{{item.type}}"
                    bind:change="handleProgressCheckboxChange"
                  />
                </view>
                <view class="progress-text" bindtap="toggleSelectProgress" data-type="{{item.type}}" data-name="{{item.name}}">
                  {{item.name}}
                </view>
              </view>
              <view class="progress-status">{{scanResult.progressDetail[item.status] === 1 ? '已完成' : '未完成'}}</view>
            </view>
          </block>
        </view>
      </view>
      
      <view class="action-buttons">
        <button 
          class="btn {{selectedProgressTypes.length > 0 ? 'btn-primary' : 'btn-disabled'}}" 
          bindtap="submitSelectedProgress" 
          disabled="{{selectedProgressTypes.length === 0 || processing}}"
          hover-class="{{selectedProgressTypes.length > 0 ? 'button-hover' : ''}}"
          hover-start-time="20"
          hover-stay-time="70">
          {{processing ? '处理中...' : '完成选中工序'}}
        </button>
        <button class="btn btn-outline" bindtap="goToProgress" hover-class="button-hover">查看详情</button>
      </view>
    </view>
  </view>
  
  <!-- 批量模式下的底部操作区 -->
  <view class="batch-nav-container" wx:if="{{scanResult && batchMode}}">
    <view class="compact-batch-nav">
      <view class="nav-buttons">
        <button class="nav-btn prev" bindtap="showPrevBatchOrder">
          <image class="nav-icon" src="/static/icons/left.png" mode="aspectFit"></image>
          <text>上页</text>
        </button>
        <view class="batch-counter">{{currentBatchIndex + 1}}/{{batchOrders.length}}</view>
        <button class="nav-btn next" bindtap="showNextBatchOrder">
          <text>下页</text>
        </button>
      </view>
      <button class="complete-all-btn {{!processing && selectedProgressTypes.length > 0 ? 'btn-primary' : 'btn-disabled'}}" 
              bindtap="completeAllBatchOrders" 
              disabled="{{processing || selectedProgressTypes.length === 0}}">
        批量完成
      </button>
    </view>
  </view>
</view> 