package com.xiao.temu.modules.refund.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.refund.dto.RefundRequestDTO;
import com.xiao.temu.modules.refund.vo.RefundPackageVO;
import com.xiao.temu.modules.refund.service.RefundPackageService;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.system.service.UserService;
import com.xiao.temu.infrastructure.api.TemuApiClient;
import org.ehcache.shadow.org.terracotta.statistics.Time;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.Map;
import static com.xiao.temu.infrastructure.api.TemuApiClient.addParameterIfPresent;

/**
 * Temu退货明细服务实现
 */
@Service
public class RefundPackageServiceImpl implements RefundPackageService {

    private static final Logger log = LoggerFactory.getLogger(RefundPackageServiceImpl.class);

    @Autowired
    private ShopService shopService;

    @Autowired
    private UserService userService;

    /**
     * 获取退货包裹列表
     *
     * @param requestDTO 请求参数
     * @param userId 当前用户ID
     * @return 退货包裹列表
     */
    @Override
    public RefundPackageVO getRefundPackageList(RefundRequestDTO requestDTO, Long userId) {
        log.debug("查询Temu退货明细, 请求参数: {}, 用户ID: {}", requestDTO, userId);
        
        RefundPackageVO response = new RefundPackageVO();
        response.setSuccess(true);
        
        try {
            // 验证店铺ID参数
            Long shopId = requestDTO.getShopId();
            if (shopId == null) {
                throw new RuntimeException("店铺ID不能为空");
            }
            
            // 检查用户是否是管理员
            boolean isAdmin = userService.isAdmin(userId);
            // 检查是否忽略权限检查
            boolean ignorePermissionCheck = Boolean.TRUE.equals(requestDTO.getIgnorePermissionCheck());
            
            // 如果不是管理员且不忽略权限检查，需要验证店铺权限
            if (!isAdmin && !ignorePermissionCheck) {
                boolean hasPermission = shopService.checkShopPermission(userId, shopId, false);
                if (!hasPermission) {
                    response.setSuccess(false);
                    response.setErrorCode(Integer.valueOf("403"));
                    response.setErrorMsg("您没有权限访问该店铺的数据");
                    return response;
                }
            }
            
            // 获取店铺信息
            Shop shop = shopService.getShopById(shopId).convertToShop();
            if (shop == null) {
                response.setSuccess(false);
                response.setErrorCode(Integer.valueOf("404"));
                response.setErrorMsg("店铺不存在");
                return response;
            }

            // 设置API调用所需的公共参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.refund.returnpackagelist.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());

            // 设置业务参数
            Map<String, Object> businessParams = new HashMap<>();
            businessParams.put("outboundTimeEnd", requestDTO.getOutboundTimeEnd());
            businessParams.put("outboundTimeStart", requestDTO.getOutboundTimeStart());
            businessParams.put("pageNo", requestDTO.getPageNo());
            businessParams.put("pageSize", requestDTO.getPageSize());
            businessParams.put("timestamp", String.valueOf(Time.time()));
            // 添加新增的三个查询条件
            addParameterIfPresent(businessParams, "productSkuIdList", requestDTO.getProductSkuIdList());
            addParameterIfPresent(businessParams, "returnSupplierPackageNos", requestDTO.getReturnSupplierPackageNos());
            addParameterIfPresent(businessParams, "purchaseSubOrderSns", requestDTO.getPurchaseSubOrderSns());
            // 调用API获取结果
            JSONObject result = TemuApiClient.sendRequest(commonParams, businessParams);

            // 构建返回对象
            response.setSuccess(result.getBoolean("success"));
            response.setErrorCode(result.getInteger("errorCode"));
            response.setErrorMsg(result.getString("errorMsg"));
            response.setResult(result.getJSONObject("result"));
            response.setShopId(shopId);
            response.setShopName(shop.getShopName());

            return response;
        } catch (Exception e) {
            // 异常处理
            response.setSuccess(false);
            response.setErrorCode(Integer.valueOf("500"));
            response.setErrorMsg("调用API失败: " + e.getMessage());
            return response;
        }
    }
} 