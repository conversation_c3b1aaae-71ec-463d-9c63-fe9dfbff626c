import request from '@/utils/request';

/**
 * 获取生产组成员角色分配列表
 * @param params 查询参数
 * @returns 
 */
export function getRoleAssignmentList(params?: any) {
  return request({
    url: '/production/role/list',
    method: 'get',
    params
  });
}

/**
 * 获取用户在生产组中的角色列表
 * @param groupId 生产组ID
 * @param userId 用户ID
 * @returns 
 */
export function getUserRoles(groupId: number, userId: number) {
  return request({
    url: `/production/role/user/${groupId}/${userId}`,
    method: 'get'
  });
}

/**
 * 分配角色给生产组成员
 * @param groupId 生产组ID
 * @param userId 用户ID
 * @param roleId 角色ID
 * @returns 
 */
export function assignRole(groupId: number, userId: number, roleId: number) {
  return request({
    url: '/production/role/assign',
    method: 'post',
    data: {
      groupId,
      userId,
      roleId
    }
  });
}

/**
 * 批量分配角色给用户
 * @param groupId 生产组ID
 * @param userId 用户ID
 * @param roleIds 角色ID数组
 * @returns 
 */
export function batchAssignRoles(groupId: number, userId: number, roleIds: number[]) {
  return request({
    url: '/production/role/batch-assign',
    method: 'post',
    data: {
      groupId,
      userId,
      roleIds
    }
  });
}

/**
 * 取消生产组成员的角色分配
 * @param assignmentId 分配ID
 * @returns 
 */
export function removeRole(assignmentId: number) {
  return request({
    url: `/production/role/${assignmentId}`,
    method: 'delete'
  });
}

/**
 * 获取可分配的角色列表
 * @returns 
 */
export function getAssignableRoles() {
  return request({
    url: '/production/role/available',
    method: 'get'
  });
}

/**
 * 检查用户是否有指定角色
 * @param groupId 生产组ID
 * @param userId 用户ID
 * @param roleId 角色ID
 * @returns 
 */
export function checkUserRole(groupId: number, userId: number, roleId: number) {
  return request({
    url: '/production/role/check',
    method: 'get',
    params: {
      groupId,
      userId,
      roleId
    }
  });
}

/**
 * 批量获取多个用户的角色ID列表
 * @param groupId 生产组ID
 * @param userIds 用户ID数组
 * @returns Map<userId, 角色ID列表>
 */
export function batchGetUserRoles(groupId: number, userIds: number[]) {
  return request({
    url: '/production/role/user/batch',
    method: 'post',
    data: {
      groupId,
      userIds
    }
  });
}

/**
 * 批量获取多个用户的角色分配信息
 * @param groupId 生产组ID
 * @param userIds 用户ID数组
 * @returns Map<userId, 角色分配列表>
 */
export function batchGetUserRoleAssignments(groupId: number, userIds: number[]) {
  return request({
    url: '/production/role/assignments/batch',
    method: 'post',
    data: {
      groupId,
      userIds
    }
  });
}