package com.xiao.temu.modules.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.product.entity.Product;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 商品数据Mapper
 */
@Mapper
public interface ProductMapper extends BaseMapper<Product> {
    
    /**
     * 批量保存或更新商品数据
     * 
     * @param productList 商品列表
     * @return 影响的行数
     */
    int batchSaveOrUpdate(@Param("list") List<Product> productList);
    
    /**
     * 获取指定店铺最新的更新时间
     * 
     * @param shopId 店铺ID
     * @return 最新更新时间
     */
    @Select("SELECT MAX(sync_time) FROM product WHERE shop_id = #{shopId}")
    LocalDateTime getLatestSyncTimeByShopId(@Param("shopId") Long shopId);
    
    /**
     * 根据店铺ID和商品ID查询商品
     * 
     * @param shopId 店铺ID
     * @param productId 商品ID
     * @return 商品对象
     */
    @Select("SELECT * FROM product WHERE shop_id = #{shopId} AND product_id = #{productId}")
    Product getByShopIdAndProductId(@Param("shopId") Long shopId, @Param("productId") Long productId);
    
    /**
     * 根据店铺ID和商品SKC ID列表查询商品
     * 
     * @param shopId 店铺ID
     * @param skcIds 商品SKC ID列表
     * @return 商品列表
     */
    List<Product> findBySkcIds(@Param("shopId") Long shopId, @Param("skcIds") List<Long> skcIds);
    
    /**
     * 获取指定店铺的商品总数
     * 
     * @param shopId 店铺ID
     * @return 商品总数
     */
    @Select("SELECT COUNT(*) FROM product WHERE shop_id = #{shopId}")
    Integer getProductCountByShopId(@Param("shopId") Long shopId);
} 