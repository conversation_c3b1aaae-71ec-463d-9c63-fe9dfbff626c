package com.xiao.temu.infrastructure.log.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.infrastructure.log.entity.ApiCallLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * API调用日志Mapper接口
 */
@Mapper
public interface ApiCallLogMapper extends BaseMapper<ApiCallLog> {
    
    /**
     * 分页查询API调用日志
     *
     * @param page 分页参数
     * @param shopId 店铺ID
     * @param apiName API名称
     * @param status 状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页数据
     */
    IPage<ApiCallLog> selectLogList(Page<ApiCallLog> page,
                                  @Param("shopId") Long shopId,
                                  @Param("apiName") String apiName,
                                  @Param("status") String status,
                                  @Param("startTime") String startTime,
                                  @Param("endTime") String endTime);
    
    /**
     * 查询用户的API调用日志
     *
     * @param userId 用户ID
     * @return 日志列表
     */
    List<ApiCallLog> selectLogsByUserId(@Param("userId") Long userId);
    
    /**
     * 查询店铺的API调用日志
     *
     * @param shopId 店铺ID
     * @return 日志列表
     */
    List<ApiCallLog> selectLogsByShopId(@Param("shopId") Long shopId);
} 