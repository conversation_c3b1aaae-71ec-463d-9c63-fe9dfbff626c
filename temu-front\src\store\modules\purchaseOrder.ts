import { defineStore } from 'pinia'
import { getPurchaseOrders } from '@/api/temu/purchaseOrder'
import { fetchUserAccessibleShops } from '@/utils/shop-helper'
import { ElMessage } from 'element-plus'
import type { ShopDataRange } from '@/types/purchaseOrder'

interface ApiResponse {
  success: boolean
  data: any
  errorMsg?: string
  errorCode?: number
  code?: number
  list?: any[]
}

interface PurchaseOrder {
  originalPurchaseOrderSn: string
  subPurchaseOrderSn: string
  productName: string
  productSn: string
  productId: number
  productSkcId: number
  productSkcPicture: string
  supplierId: number
  supplierName: string
  status: number
  purchaseTime: number
  skuQuantityDetailList: any[]
  skuQuantityTotalInfo: any
  [key: string]: any
}

export const usePurchaseOrderStore = defineStore('purchaseOrder', {
  state: () => ({
    loading: false,
    purchaseOrders: [] as PurchaseOrder[],
    total: 0,
    shopDataRanges: [] as ShopDataRange[],
    shops: [] as any[],
  }),

  actions: {
    // 获取备货单列表
    async getPurchaseOrderList(queryParams: any) {
      this.loading = true
      try {
        // API层已经处理了参数过滤，这里直接传递参数
        const response = await getPurchaseOrders(queryParams)
        const apiResponse = response as any
        if (apiResponse.code === 200 && apiResponse.data) {
          // 根据data.json的数据结构进行正确解析
          if (apiResponse.data.success && apiResponse.data.result) {
            const result = apiResponse.data.result
            this.purchaseOrders = result.subOrderForSupplierList || []
            this.total = result.total || 0
            this.shopDataRanges = result.shopDataRanges || []
          } else {
            this.purchaseOrders = []
            this.total = 0
            this.shopDataRanges = []
            ElMessage.error(apiResponse.data.errorMsg || '获取备货单列表失败')
          }
        } else {
          ElMessage.error(apiResponse.message || '获取备货单列表失败')
          this.purchaseOrders = []
          this.total = 0
          this.shopDataRanges = []
        }
      } catch (error) {
        console.error('获取备货单列表出错', error)
        ElMessage.error('系统异常，请稍后重试')
        this.purchaseOrders = []
        this.total = 0
        this.shopDataRanges = []
      } finally {
        this.loading = false
      }
    },

    // 获取用户可访问的店铺列表
    async getShopList() {
      try {
        this.shops = await fetchUserAccessibleShops()
        return this.shops
      } catch (error) {
        console.error('获取店铺列表出错', error)
        ElMessage.error('系统异常，请稍后重试')
        return []
      }
    },
  }
}) 