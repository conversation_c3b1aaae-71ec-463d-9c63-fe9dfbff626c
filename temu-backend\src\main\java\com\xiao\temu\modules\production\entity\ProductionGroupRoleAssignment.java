package com.xiao.temu.modules.production.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产组角色分配实体类
 */
@Data
@TableName("production_group_role_assignment")
public class ProductionGroupRoleAssignment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 生产组ID
     */
    private Long groupId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 分配时间
     */
    private Date assignTime;

    /**
     * 分配人ID(组长)
     */
    private Long assignBy;

    /**
     * 状态(0正常1禁用)
     */
    private String status;
} 