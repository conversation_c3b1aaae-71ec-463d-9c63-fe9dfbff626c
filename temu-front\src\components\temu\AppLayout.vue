<template>
  <div class="app-container">
    <slot name="search"></slot>
    <slot name="table"></slot>
    <slot name="pagination"></slot>
    <slot name="dialogs"></slot>
  </div>
</template>

<script setup>
// 通用的应用布局组件
// 使用方式：
// <AppLayout>
//   <template #search><SearchCard>...</SearchCard></template>
//   <template #table><TableCard>...</TableCard></template>
//   <template #pagination><PaginationBar ... /></template>
//   <template #dialogs><ImagePreview ... /></template>
// </AppLayout>
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  position: relative;
  overflow: hidden;
  background-color: #fff;
}
</style> 