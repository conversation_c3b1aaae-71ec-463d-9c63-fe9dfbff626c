package com.xiao.temu.modules.purchaseorderv.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderNotificationConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 备货单通知配置数据访问接口
 */
@Mapper
public interface PurchaseOrderNotificationConfigMapper extends BaseMapper<PurchaseOrderNotificationConfig> {
    
    /**
     * 根据通知类型获取配置
     *
     * @param notificationType 通知类型
     * @return 通知配置
     */
    PurchaseOrderNotificationConfig getByNotificationType(@Param("notificationType") Integer notificationType);
} 