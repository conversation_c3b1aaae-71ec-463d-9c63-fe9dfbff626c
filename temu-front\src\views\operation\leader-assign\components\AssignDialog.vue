<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    width="500px"
    append-to-body
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="运营组" prop="groupId">
        <el-select
          v-model="form.groupId"
          placeholder="请选择运营组"
          @change="handleGroupChange"
          style="width: 100%"
          :disabled="!isAdmin || !!props.groupId"
        >
          <el-option
            v-for="group in userGroups"
            :key="group.groupId"
            :label="group.groupName"
            :value="group.groupId"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="已分配店铺" v-if="userAssignedShops.length > 0">
        <div class="assigned-shops-container">
          <el-tag
            v-for="shop in userAssignedShops"
            :key="shop.shopId"
            class="assigned-shop-tag"
            size="small"
            type="info"
            effect="plain"
            style="border: 1px solid #67C23A"
          >
            {{ shop.shopName }}
          </el-tag>
        </div>
      </el-form-item>

      <el-form-item label="店铺" prop="shopIds">
        <el-select
          v-model="form.shopIds"
          placeholder="请选择店铺"
          style="width: 100%"
          filterable
          multiple
          collapse-tags
          collapse-tags-tooltip
        >
          <el-option
            v-for="shop in availableShops"
            :key="shop.shopId"
            :label="shop.shopName"
            :value="shop.shopId"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="组员" prop="userId">
        <el-select
          v-model="form.userId"
          placeholder="请选择组员"
          style="width: 100%"
          filterable
          :disabled="!!props.userId"
          @change="handleUserChange"
        >
          <el-option
            v-for="member in groupMembers"
            :key="member.userId"
            :label="member.nickName || member.username"
            :value="member.userId"
          />
        </el-select>
        <div v-if="props.userId && props.userName" class="selected-user-info">
          已选择: {{ props.userName }}
        </div>
      </el-form-item>

    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useLeaderAssignStore } from '@/store/modules/leaderAssign'
import type { FormInstance, FormRules } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  groupId: {
    type: Number,
    default: null
  },
  userId: {
    type: Number,
    default: null
  },
  userName: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'success'])

const leaderAssignStore = useLeaderAssignStore()
const formRef = ref<FormInstance>()
const isAdmin = computed(() => leaderAssignStore.isAdmin)

// 表单数据
const form = reactive({
  groupId: props.groupId,
  shopIds: [] as number[],
  userId: props.userId as number | null,
  permissionType: '0' // 默认只读权限
})

// 表单校验规则
const rules = reactive<FormRules>({
  shopIds: [
    { required: true, message: '请选择店铺', trigger: 'change', type: 'array' }
  ],
  userId: [
    { required: true, message: '请选择组员', trigger: 'change' }
  ]
})

// 计算标题
const title = computed(() => '分配店铺')

// 获取可选数据
const userGroups = computed(() => leaderAssignStore.userGroups)
const assignableShops = computed(() => leaderAssignStore.assignableShops)
const groupMembers = computed(() => leaderAssignStore.groupMembers)

// 当前用户已分配的店铺列表
const userAssignedShops = ref<any[]>([]);

// 过滤已分配的店铺后的可用店铺列表
const availableShops = computed(() => {
  if (!assignableShops.value) return [];
  
  // 如果没有已分配店铺，则返回所有可分配店铺
  if (userAssignedShops.value.length === 0) return assignableShops.value;
  
  // 过滤掉已分配的店铺
  return assignableShops.value.filter(shop => 
    !userAssignedShops.value.some(assignedShop => 
      assignedShop.shopId === shop.shopId
    )
  );
});

// 添加计算属性处理对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 监听visible和groupId变化
watch(() => props.visible, (val) => {
  if (val && props.groupId) {
    console.log('对话框显示，准备加载数据，运营组ID:', props.groupId);
    form.groupId = props.groupId;
    
    // 如果传入了用户ID，自动设置用户
    if (props.userId) {
      form.userId = props.userId;
    }
    
    // 每次打开对话框时，先重新获取最新的分配列表数据
    refreshAssignmentList().then(() => {
      loadSelectData();
    });
  }
})

watch(() => props.groupId, (val) => {
  if (val && props.visible) {
    console.log('运营组ID变化，重新加载数据:', val);
    form.groupId = val;
    loadSelectData();
  }
})

// 加载下拉框数据
async function loadSelectData() {
  if (!form.groupId) {
    console.warn('运营组ID为空，无法加载数据');
    return;
  }
  
  console.log('开始加载下拉框数据，运营组ID:', form.groupId);
  
  try {
    // 加载可分配的店铺
    console.log('正在加载可分配的店铺...');
    await leaderAssignStore.getAssignableShops(form.groupId);
    console.log('可分配的店铺加载成功:', leaderAssignStore.assignableShops);
    
    // 加载组员列表
    console.log('正在加载组员列表...');
    await leaderAssignStore.getGroupMembers(form.groupId);
    console.log('组员列表加载成功:', leaderAssignStore.groupMembers);
    
    // 如果有选中的用户，加载该用户已分配的店铺
    if (form.userId) {
      await loadUserAssignedShops();
    }
  } catch (error) {
    console.error('加载下拉框数据失败：', error);
    ElMessage.error('加载数据失败');
  }
}

// 获取用户已分配的店铺
async function loadUserAssignedShops() {
  if (!form.userId || !form.groupId) return;
  
  try {
    // 从store中获取分配列表
    const allAssignments = leaderAssignStore.assignmentList;
    
    // 过滤出当前用户在当前组的分配
    userAssignedShops.value = allAssignments
      .filter(item => item.userId === form.userId && item.groupId === form.groupId)
      .map(item => ({
        shopId: item.shopId,
        shopName: item.shopName
      }));
    
    console.log('用户已分配店铺:', userAssignedShops.value);
  } catch (error) {
    console.error('获取用户已分配店铺失败:', error);
    userAssignedShops.value = [];
  }
}

// 添加刷新分配列表的方法
async function refreshAssignmentList() {
  try {
    console.log('刷新分配列表数据...');
    // 重新获取完整的分配列表
    await leaderAssignStore.getAssignmentList({
      groupId: form.groupId,
      pageNum: 1,
      pageSize: 100 // 使用较大的页面大小以确保获取所有数据
    });
    console.log('分配列表刷新成功');
  } catch (error) {
    console.error('刷新分配列表失败:', error);
    ElMessage.error('获取最新分配数据失败');
  }
}

// 用户变更处理
async function handleUserChange(userId: number) {
  console.log('用户变更:', userId);
  form.shopIds = [];
  await loadUserAssignedShops();
}

// 运营组变更处理
function handleGroupChange(val: number) {
  console.log('运营组变更:', val);
  form.shopIds = [];
  form.userId = null;
  userAssignedShops.value = []; // 清空已分配店铺
  loadSelectData();
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return;
  
  console.log('正在提交表单...');
  await formRef.value.validate(async (valid) => {
    if (valid) {
      console.log('表单验证通过，提交数据:', form);
      try {
        // 使用批量分配接口
        await leaderAssignStore.batchAssignShops({
          groupId: form.groupId as number,
          shopIds: form.shopIds,
          userId: form.userId as number,
          permissionType: form.permissionType
        });
        
        ElMessage.success('分配成功');
        emit('success');
        handleClose();
      } catch (error: any) {
        console.error('分配失败:', error);
        ElMessage.error(error.message || '分配失败');
      }
    } else {
      console.warn('表单验证不通过');
    }
  });
}

// 关闭对话框
function handleClose() {
  console.log('关闭对话框');
  emit('update:visible', false);
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

// 监听用户ID变化
watch(() => form.userId, async (newVal) => {
  if (newVal) {
    await loadUserAssignedShops();
  } else {
    userAssignedShops.value = [];
  }
});

onMounted(() => {
  console.log('分配对话框组件挂载，groupId:', props.groupId, 'userId:', props.userId, 'visible:', props.visible);
  if (props.groupId && props.visible) {
    form.groupId = props.groupId;
    
    // 如果传入了用户ID，自动设置用户
    if (props.userId) {
      form.userId = props.userId;
    }
    
    loadSelectData();
  }
});
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.selected-user-info {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

.assigned-shops-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 100px;
  overflow-y: auto;
  padding: 5px;
  border: none;
  border-radius: 4px;
}

.assigned-shop-tag {
  margin-right: 5px;
  margin-bottom: 5px;
  border: 1px solid #67C23A !important;
  color: #303133;
}
</style> 