package com.xiao.temu.common.constant;

/**
 * 备货单通知相关常量
 */
public class PurchaseOrderNotificationConstants {
    
    /**
     * 通知类型
     */
    public static final int NOTIFY_TYPE_JIT_SOON_OVERDUE = 1; // JIT备货单到货即将逾期
    public static final int NOTIFY_TYPE_JIT_OVERDUE = 2; // JIT备货单到货已逾期
    public static final int NOTIFY_TYPE_NORMAL_NOT_DELIVERED = 3; // 普通备货未发货
    public static final int NOTIFY_TYPE_NORMAL_NOT_RECEIVED = 4; // 普通备货未到货
    
    /**
     * 通知状态
     */
    public static final int NOTIFY_STATUS_WAITING = 0; // 待通知
    public static final int NOTIFY_STATUS_NOTIFYING = 1; // 通知中
    public static final int NOTIFY_STATUS_COMPLETED = 2; // 已通知完成
    
    /**
     * 最大通知次数
     */
    public static final int MAX_NOTIFY_COUNT = 3; // 最大通知次数
    
    /**
     * 模板编码
     */
    public static final String TEMPLATE_CODE_JIT_SOON_OVERDUE = "JIT_SOON_OVERDUE"; // JIT备货单到货即将逾期模板
    public static final String TEMPLATE_CODE_JIT_OVERDUE = "JIT_OVERDUE"; // JIT备货单到货已逾期模板
    public static final String TEMPLATE_CODE_NORMAL_NOT_DELIVERED = "NORMAL_NOT_DELIVERED"; // 普通备货未发货模板
    public static final String TEMPLATE_CODE_NORMAL_NOT_RECEIVED = "NORMAL_NOT_RECEIVED"; // 普通备货未到货模板
    
    /**
     * 批量通知模板编码
     */
    public static final String TEMPLATE_CODE_JIT_SOON_OVERDUE_BATCH = "JIT_SOON_OVERDUE_BATCH"; // JIT备货单到货即将逾期批量模板
    public static final String TEMPLATE_CODE_JIT_OVERDUE_BATCH = "JIT_OVERDUE_BATCH"; // JIT备货单到货已逾期批量模板
    public static final String TEMPLATE_CODE_NORMAL_NOT_DELIVERED_BATCH = "NORMAL_NOT_DELIVERED_BATCH"; // 普通备货未发货批量模板
    public static final String TEMPLATE_CODE_NORMAL_NOT_RECEIVED_BATCH = "NORMAL_NOT_RECEIVED_BATCH"; // 普通备货未到货批量模板
    
    /**
     * 时间常量（单位：小时）
     */
    public static final int JIT_SOON_OVERDUE_HOURS = 24; // JIT备货单到货即将逾期提前小时数
    public static final int NORMAL_NOT_DELIVERED_DAYS = 5; // 普通备货未发货天数
    public static final int NORMAL_NOT_RECEIVED_DAYS = 5; // 普通备货未到货天数
} 