package com.xiao.temu.modules.sales.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.sales.entity.SalesInventoryInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 销售库存信息Mapper接口
 */
@Mapper
public interface SalesInventoryInfoMapper extends BaseMapper<SalesInventoryInfo> {

    /**
     * 获取指定店铺的销售库存信息数量
     *
     * @param shopId 店铺ID
     * @return 数量
     */
    @Select("SELECT COUNT(*) FROM sales_inventory_info WHERE shop_id = #{shopId}")
    Integer countByShopId(@Param("shopId") Long shopId);
} 