package com.xiao.temu.modules.message.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xiao.temu.modules.message.dto.MessageDetailVO;
import com.xiao.temu.modules.message.dto.MessageQueryDTO;
import com.xiao.temu.modules.message.dto.SendMessageDTO;
import com.xiao.temu.modules.message.dto.UnreadCountVO;
import com.xiao.temu.modules.message.entity.SysMessage;
import com.xiao.temu.modules.message.entity.SysMessageTemplate;

import java.util.List;

/**
 * 消息服务接口
 */
public interface MessageService {

    /**
     * 发送消息
     *
     * @param sendMessageDTO 发送消息DTO
     * @param fromUserId     发送人ID
     * @return 消息ID
     */
    Long sendMessage(SendMessageDTO sendMessageDTO, Long fromUserId);

    /**
     * 查询消息列表
     *
     * @param query  查询条件
     * @param userId 用户ID
     * @return 消息列表分页数据
     */
    IPage<SysMessage> getMessageList(MessageQueryDTO query, Long userId);

    /**
     * 获取消息详情
     *
     * @param messageId 消息ID
     * @param userId    用户ID
     * @return 消息详情
     */
    MessageDetailVO getMessageDetail(Long messageId, Long userId);

    /**
     * 标记消息已读
     *
     * @param messageId 消息ID
     * @param userId    用户ID
     * @return 是否成功
     */
    boolean markAsRead(Long messageId, Long userId);

    /**
     * 批量标记已读
     *
     * @param messageIds 消息ID列表
     * @param userId     用户ID
     * @return 是否成功
     */
    boolean batchMarkAsRead(List<Long> messageIds, Long userId);

    /**
     * 删除消息
     *
     * @param messageId 消息ID
     * @param userId    用户ID
     * @return 是否成功
     */
    boolean deleteMessage(Long messageId, Long userId);

    /**
     * 批量删除消息
     *
     * @param messageIds 消息ID列表
     * @param userId     用户ID
     * @return 是否成功
     */
    boolean batchDeleteMessage(List<Long> messageIds, Long userId);

    /**
     * 获取未读消息数量
     *
     * @param userId 用户ID
     * @return 未读消息数量统计
     */
    UnreadCountVO getUnreadCount(Long userId);

    /**
     * 获取消息模板
     *
     * @param templateCode 模板编码
     * @return 消息模板
     */
    SysMessageTemplate getMessageTemplate(String templateCode);
} 