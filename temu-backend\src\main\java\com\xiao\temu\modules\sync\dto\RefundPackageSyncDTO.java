package com.xiao.temu.modules.sync.dto;

import lombok.Data;

import java.util.List;

/**
 * 退货包裹同步请求参数DTO
 */
@Data
public class RefundPackageSyncDTO {
    
    /**
     * 店铺ID列表
     */
    private List<Long> shopIds;
    
    /**
     * 页码
     */
    private Integer pageNum;
    
    /**
     * 每页条数
     */
    private Integer pageSize;
    
    /**
     * 出库开始时间（毫秒时间戳）
     */
    private String outboundTimeStart;
    
    /**
     * 出库结束时间（毫秒时间戳）
     */
    private String outboundTimeEnd;
    
    /**
     * 产品SKU ID列表
     */
    private List<Long> productSkuIdList;
    
    /**
     * 退货包裹号列表
     */
    private List<String> returnSupplierPackageNos;
    
    /**
     * 备货单号列表
     */
    private List<String> purchaseSubOrderSns;
    
    /**
     * 排序字段
     */
    private String sortField;
    
    /**
     * 排序方向：asc-升序，desc-降序
     */
    private String sortOrder;
} 