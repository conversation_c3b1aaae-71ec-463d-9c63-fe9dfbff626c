package com.xiao.temu.modules.sync.dto;

import lombok.Data;

import java.util.List;

/**
 * 质检数据同步请求DTO
 */
@Data
public class QualityInspectionSyncDTO {
    
    /**
     * 店铺ID列表
     */
    private List<Long> shopIds;
    
    /**
     * 同步模式：0-手动触发，1-自动同步
     */
    private Integer syncMode;
    
    /**
     * 页码
     */
    private Integer pageNum;
    
    /**
     * 每页记录数
     */
    private Integer pageSize;
    
    /**
     * 批量同步批次大小
     */
    private Integer batchSize;
    
    /**
     * 最大线程数
     */
    private Integer maxThreads;
    
    /**
     * API调用间隔(毫秒)
     */
    private Long apiInterval;
    
    /**
     * 排序字段
     */
    private String sortField;
    
    /**
     * 排序方向：asc-升序，desc-降序
     */
    private String sortOrder;
} 