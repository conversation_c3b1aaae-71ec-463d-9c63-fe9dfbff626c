// 违规信息store模块
import { defineStore } from 'pinia'
import type { ViolationQueryParams, ViolationQueryResult, ViolationInfoItem, CompleteViolationInfo } from '@/types/local/violation'
import { getViolationList, getViolationDetail } from '@/api/local/violation'
import type { Shop } from '@/types/shop'
import { fetchUserAccessibleShops } from '@/utils/shop-helper'

export const useViolationStore = defineStore('violation', {
  state: () => ({
    loading: false,
    violationResult: null as ViolationQueryResult | null,
    violationDetail: null as CompleteViolationInfo | null,
    shops: [] as Shop[],
    shopIds: [] as number[],
    timeRange: null as [string, string] | null
  }),

  actions: {
    // 获取违规信息列表
    async getViolationList(params: ViolationQueryParams) {
      this.loading = true
      try {
        const response = await getViolationList(params)
        this.violationResult = response.data
        return response.data
      } finally {
        this.loading = false
      }
    },

    // 获取违规详情
    async getViolationDetail(id: number) {
      this.loading = true
      try {
        const response = await getViolationDetail(id)
        this.violationDetail = response.data
        return response.data
      } finally {
        this.loading = false
      }
    },

    // 加载店铺列表
    async loadShops() {
      try {
        const shops = await fetchUserAccessibleShops()
        // 使用类型断言来解决可能的类型不匹配问题
        this.shops = shops as unknown as Shop[]
        return this.shops
      } catch (error) {
        console.error('获取店铺列表失败:', error)
        return []
      }
    },

    // 设置店铺ID
    setShopIds(shopIds: number[]) {
      this.shopIds = shopIds
    },

    // 设置时间范围
    setTimeRange(timeRange: [string, string] | null) {
      this.timeRange = timeRange
    },

    // 重置查询条件
    resetQuery() {
      this.shopIds = []
      this.timeRange = null
    }
  }
}) 