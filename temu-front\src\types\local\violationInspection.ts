// 违规信息和质检结果综合数据类型定义
import type { PageParams, PageResult } from '../api'

// 违规信息和质检结果综合数据项
export interface ViolationInspectionItem {
  violationId: number
  shopId: number
  shopName: string
  shopRemark?: string
  punishSn: string
  subPurchaseOrderSn: string
  violationTime: string
  punishAmount: number
  punishTypeDesc: string
  punishReasonDesc: string
  productSkuId?: number
  extCode?: string
  lackQuantity?: number
  unqualifiedQuantity?: number
  stockQuantity?: number
  flawNameDesc?: string
  attachments?: string
  attachmentList?: string[]
}

// 查询参数
export interface ViolationInspectionQueryParams extends PageParams {
  shopId?: number
  shopIds?: number[]
  punishSn?: string
  subPurchaseOrderSn?: string
  violationTimeStart?: string
  violationTimeEnd?: string
  productSkuId?: number
  extCode?: string
  punishTypeCode?: number
  punishStatus?: number | null
  groupId?: number
  pageNum?: number
  pageSize?: number
}

// 查询结果
export interface ViolationInspectionResult extends PageResult<ViolationInspectionItem> {
  items: ViolationInspectionItem[]
  total: number
  pageNum: number
  pageSize: number
  rows?: ViolationInspectionItem[] // 后端可能使用rows返回数据
} 