<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>销售数据同步管理</span>
        </div>
      </template>
      
      <!-- 搜索条件 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="100px">
        <el-form-item label="店铺选择" prop="shopIds">
          <el-select
            v-model="selectValue"
            multiple
            filterable
            placeholder="请选择店铺"
            style="width: 360px"
          >
            <el-option
              key="all"
              label="全选"
              :value="-1"
            />
            <el-option
              v-for="item in shopOptions"
              :key="item.shopId"
              :label="item.shopName"
              :value="item.shopId"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>查询
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            @click="handleRefreshList"
          >
            <el-icon><Refresh /></el-icon>刷新
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            @click="handleBatchInit"
            :disabled="selectValue.length === 0 || (selectValue.length === 1 && selectValue[0] === -1)"
          >
            <el-icon><Plus /></el-icon>批量初始化
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            @click="handleBatchSync"
            :disabled="selectValue.length === 0 || (selectValue.length === 1 && selectValue[0] === -1)"
          >
            <el-icon><Connection /></el-icon>批量同步
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            @click="handleBatchClear"
            :disabled="selectValue.length === 0 || (selectValue.length === 1 && selectValue[0] === -1)"
          >
            <el-icon><Delete /></el-icon>批量清空数据
          </el-button>
        </el-col>
      </el-row>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="syncTaskList" @sort-change="handleSortChange">
        <el-table-column label="店铺ID" align="center" prop="shopId" sortable="custom" :sort-orders="['ascending', 'descending']" />
        <el-table-column label="店铺名称" align="center" prop="shopName" :show-overflow-tooltip="true" />
        <el-table-column label="店铺备注" align="center" prop="shopRemark" :show-overflow-tooltip="true" />
        <el-table-column label="上次同步时间" align="center" prop="lastSyncTime" width="160">
          <template #default="scope">
            <span>{{ formatDateTime(scope.row.lastSyncTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="数据最新时间" align="center" prop="lastUpdateTime" width="160">
          <template #default="scope">
            <span>{{ formatDateTime(scope.row.lastUpdateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="同步状态" align="center" prop="syncStatus">
          <template #default="scope">
            <el-tag v-if="scope.row.syncStatus === 0" type="info">未同步</el-tag>
            <el-tag v-else-if="scope.row.syncStatus === 1" type="warning">同步中</el-tag>
            <el-tag v-else-if="scope.row.syncStatus === 2" type="success">同步成功</el-tag>
            <el-tag v-else-if="scope.row.syncStatus === 3" type="danger">同步失败</el-tag>
            <el-tag v-else type="info">未知状态</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="订单记录数" align="center" prop="totalRecords" width="100" />
        <el-table-column label="SKU记录数" align="center" prop="skuTotalRecords" width="100" />
        <el-table-column label="仓库记录数" align="center" prop="warehouseTotalRecords" width="100" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="handleTriggerSync(scope.row)"
              :disabled="scope.row.syncStatus === 1"
            >
              手动同步
            </el-button>
            <el-button
              type="success"
              link
              @click="handleInitTask(scope.row)"
              :disabled="scope.row.syncStatus === 1"
            >
              初始化任务
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleClearData(scope.row)"
              :disabled="scope.row.syncStatus === 1"
            >
              清空数据
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Connection, Delete } from '@element-plus/icons-vue'
import { getSyncTasks, triggerSync, initSyncTask, batchInitSyncTasks, batchTriggerSync, 
  clearSyncData, batchClearSyncData, getSyncTask } from '@/api/salesSync'
import type { SalesSyncParams, SalesSyncTask } from '@/api/salesSync'
import { getUserShops } from '@/api/shop'
import Pagination from '@/components/Pagination/index.vue'

// 接口返回类型
interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 加载状态
const loading = ref(false)
// 总数
const total = ref(0)
// 同步任务列表
const syncTaskList = ref<SalesSyncTask[]>([])
// 店铺选项
const shopOptions = ref<any[]>([])

// 选择值（包含全选选项）
const selectValue = ref<number[]>([])

// 是否已全选
const isAllSelected = ref(false)

// 查询参数
const queryParams = reactive<SalesSyncParams & { pageNum: number, pageSize: number }>({
  shopIds: [] as number[],
  pageNum: 1,
  pageSize: 10,
  sortField: 'shopId',
  sortOrder: 'asc'
})

// 监听选择变化
watch(selectValue, (newVal) => {
  if (newVal.includes(-1)) {
    // 当选择了"全选"选项
    if (!isAllSelected.value) {
      // 全选所有店铺
      isAllSelected.value = true
      selectValue.value = [-1, ...shopOptions.value.map(shop => shop.shopId)]
    }
  } else {
    // 取消全选
    isAllSelected.value = false
  }
  
  // 更新查询参数中的shopIds，排除-1
  queryParams.shopIds = selectValue.value.filter(val => val !== -1)
}, { deep: true })

// 格式化日期时间
const formatDateTime = (dateTime: string | null | undefined) => {
  if (!dateTime) {
    return '未同步'
  }
  return dateTime
}

// 查询任务列表
const getList = async () => {
  loading.value = true
  try {
    const res = await getSyncTasks(queryParams) as unknown as ApiResponse<any>
    if (res.code === 200) {
      syncTaskList.value = res.data.list || []
      total.value = res.data.total
    } else {
      ElMessage.error(res.message || '查询同步任务列表失败')
    }
  } catch (error) {
    console.error('查询同步任务列表错误:', error)
    ElMessage.error('查询同步任务列表出现错误')
  } finally {
    loading.value = false
  }
}

// 查询店铺列表
const getShopList = async () => {
  try {
    const res = await getUserShops() as unknown as ApiResponse<any>
    if (res.code === 200) {
      shopOptions.value = res.data || []
    } else {
      ElMessage.error(res.message || '获取店铺列表失败')
    }
  } catch (error) {
    console.error('获取店铺列表错误:', error)
    ElMessage.error('获取店铺列表出现错误')
  }
}

// 处理排序变化
const handleSortChange = (column: any) => {
  if (column.prop && column.order) {
    queryParams.sortField = column.prop
    queryParams.sortOrder = column.order === 'ascending' ? 'asc' : 'desc'
  } else {
    queryParams.sortField = 'shopId'
    queryParams.sortOrder = 'asc'
  }
  getList()
}

// 查询按钮
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置按钮
const resetQuery = () => {
  selectValue.value = []
  queryParams.shopIds = []
  queryParams.pageNum = 1
  queryParams.pageSize = 10
  queryParams.sortField = 'shopId'
  queryParams.sortOrder = 'asc'
  getList()
}

// 刷新列表
const handleRefreshList = () => {
  getList()
}

// 轮询定时器映射表，用于存储每个店铺的轮询定时器ID
const pollingTimers = ref<Record<number, any>>({})
// 轮询间隔（毫秒）
const pollingIntervals = ref<Record<number, number>>({})
// 轮询状态
const pollingStatus = ref<Record<number, string>>({})

// 针对特定店铺开始轮询
const startPollingTaskStatus = (shopId: number) => {
  // 如果已经有定时器在运行，先清除
  if (pollingTimers.value[shopId]) {
    clearInterval(pollingTimers.value[shopId])
  }
  
  // 初始化轮询状态
  pollingStatus.value[shopId] = 'polling'
  
  // 初始化轮询间隔为5秒
  pollingIntervals.value[shopId] = 5000
  
  // 定义轮询函数
  const pollTaskStatus = async () => {
    try {
      // 获取单个任务状态
      const res = await getSyncTask(shopId) as unknown as ApiResponse<SalesSyncTask>
      if (res.code === 200) {
        const task = res.data
        
        // 更新列表中的任务状态
        const index = syncTaskList.value.findIndex(item => item.shopId === shopId)
        if (index !== -1) {
          syncTaskList.value[index] = { ...syncTaskList.value[index], ...task }
        }
        
        // 如果同步状态不是"同步中"，停止轮询
        if (task.syncStatus !== 1) {
          clearInterval(pollingTimers.value[shopId])
          delete pollingTimers.value[shopId]
          delete pollingIntervals.value[shopId]
          
          // 根据最终状态提示用户
          if (task.syncStatus === 2) {
            ElMessage.success(`店铺"${task.shopName || task.shopId}"同步任务已完成`)
          } else if (task.syncStatus === 3) {
            ElMessage.error(`店铺"${task.shopName || task.shopId}"同步任务失败: ${task.errorMessage || '未知错误'}`)
          }
          
          // 重置轮询状态
          pollingStatus.value[shopId] = 'completed'
        } else {
          // 如果仍在同步中，并且请求成功，重置轮询间隔为5秒
          pollingIntervals.value[shopId] = 5000
        }
      }
    } catch (error) {
      console.error(`店铺ID ${shopId} 获取任务状态失败`, error)
      
      // 当请求失败时，使用指数退避策略增加轮询间隔
      pollingIntervals.value[shopId] = Math.min(pollingIntervals.value[shopId] * 1.5, 30000)
      
      // 设置轮询状态为错误但不取消轮询
      pollingStatus.value[shopId] = 'error'
    }
    
    // 重置定时器，使用最新的轮询间隔
    if (pollingStatus.value[shopId] !== 'completed') {
      clearInterval(pollingTimers.value[shopId])
      pollingTimers.value[shopId] = setTimeout(() => {
        pollTaskStatus() // 递归调用，但通过setTimeout而不是直接递归
      }, pollingIntervals.value[shopId])
    }
  }
  
  // 首次立即执行
  pollTaskStatus()
  
  // 最多轮询10分钟后自动停止
  setTimeout(() => {
    if (pollingTimers.value[shopId]) {
      clearTimeout(pollingTimers.value[shopId])
      delete pollingTimers.value[shopId]
      delete pollingIntervals.value[shopId]
      pollingStatus.value[shopId] = 'timeout'
      
      // 查询一次最新状态
      getList()
    }
  }, 10 * 60 * 1000)
}

// 手动触发同步
const handleTriggerSync = async (row: SalesSyncTask) => {
  try {
    // 显示加载中的消息
    const loadingMessage = ElMessage({
      message: '正在提交同步请求...',
      type: 'info',
      duration: 0
    })
    
    // 设置超时处理
    const timeoutId = setTimeout(() => {
      loadingMessage.close()
      ElMessage.warning('同步请求耗时较长，已在后台处理中，请稍后查看结果')
    }, 10000) // 10秒超时
    
    const res = await triggerSync(row.shopId) as unknown as ApiResponse<any>
    
    // 清除超时计时器
    clearTimeout(timeoutId)
    // 关闭加载中消息
    loadingMessage.close()
    
    if (res.code === 200) {
      ElMessage.success('触发同步成功，数据同步进行中')
      
      // 更新当前行的状态为"同步中"
      row.syncStatus = 1
      
      // 开始针对这个店铺的轮询
      startPollingTaskStatus(row.shopId)
    } else {
      ElMessage.error(res.message || '触发同步失败')
    }
  } catch (error) {
    console.error('触发同步错误:', error)
    ElMessage.error('触发同步出现错误，请稍后重试')
    
    // 刷新列表，获取最新状态
    getList()
  }
}

// 初始化任务
const handleInitTask = async (row: SalesSyncTask) => {
  try {
    ElMessageBox.confirm('确认要初始化该店铺的同步任务吗？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const res = await initSyncTask(row.shopId) as unknown as ApiResponse<any>
      if (res.code === 200) {
        ElMessage.success('初始化任务成功')
        getList() // 刷新列表
      } else {
        ElMessage.error(res.message || '初始化任务失败')
      }
    }).catch(() => {
      // 用户取消操作
    })
  } catch (error) {
    console.error('初始化任务错误:', error)
    ElMessage.error('初始化任务出现错误')
  }
}

// 批量初始化
const handleBatchInit = async () => {
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请选择需要初始化的店铺')
    return
  }
  
  try {
    ElMessageBox.confirm(`确认要批量初始化选中的${queryParams.shopIds.length}个店铺的同步任务吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const res = await batchInitSyncTasks(queryParams) as unknown as ApiResponse<any>
      if (res.code === 200) {
        ElMessage.success(`批量初始化成功，成功：${res.data.successCount}，失败：${res.data.failCount}`)
        getList() // 刷新列表
      } else {
        ElMessage.error(res.message || '批量初始化失败')
      }
    }).catch(() => {
      // 用户取消操作
    })
  } catch (error) {
    console.error('批量初始化错误:', error)
    ElMessage.error('批量初始化出现错误')
  }
}

// 批量同步
const handleBatchSync = async () => {
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请选择需要同步的店铺')
    return
  }
  
  try {
    ElMessageBox.confirm(`确认要批量同步选中的${queryParams.shopIds.length}个店铺的数据吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      // 显示加载中的消息
      const loadingMessage = ElMessage({
        message: '正在提交批量同步请求...',
        type: 'info',
        duration: 0
      })
      
      // 设置超时处理
      const timeoutId = setTimeout(() => {
        loadingMessage.close()
        ElMessage.warning('批量同步请求耗时较长，已在后台处理中，请稍后查看结果')
      }, 10000) // 10秒超时
      
      try {
        const res = await batchTriggerSync(queryParams) as unknown as ApiResponse<any>
        
        // 清除超时计时器
        clearTimeout(timeoutId)
        // 关闭加载中消息
        loadingMessage.close()
        
        if (res.code === 200) {
          ElMessage.success('批量同步任务已启动')
          
          // 更新所有相关行的状态为"同步中"并启动轮询
          const shopIdsToUpdate = queryParams.shopIds || []
          syncTaskList.value.forEach(task => {
            if (shopIdsToUpdate.includes(task.shopId)) {
              task.syncStatus = 1
              
              // 为每个店铺启动单独的轮询
              startPollingTaskStatus(task.shopId)
            }
          })
        } else {
          ElMessage.error(res.message || '批量同步失败')
        }
      } catch (error) {
        // 清除超时计时器
        clearTimeout(timeoutId)
        // 关闭加载中消息
        loadingMessage.close()
        
        console.error('批量同步请求错误:', error)
        ElMessage.error('批量同步请求出现错误，请稍后重试')
        
        // 刷新列表，获取最新状态
        getList()
      }
    }).catch(() => {
      // 用户取消操作
    })
  } catch (error) {
    console.error('批量同步错误:', error)
    ElMessage.error('批量同步出现错误')
  }
}

// 清空数据
const handleClearData = async (row: SalesSyncTask) => {
  try {
    ElMessageBox.confirm('确认要清空该店铺的全部同步数据吗？此操作将无法恢复！', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const res = await clearSyncData(row.shopId) as unknown as ApiResponse<any>
      if (res.code === 200) {
        ElMessage.success('清空数据成功')
        getList() // 刷新列表
      } else {
        ElMessage.error(res.message || '清空数据失败')
      }
    }).catch(() => {
      // 用户取消操作
    })
  } catch (error) {
    console.error('清空数据错误:', error)
    ElMessage.error('清空数据出现错误')
  }
}

// 批量清空数据
const handleBatchClear = async () => {
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请选择需要清空数据的店铺')
    return
  }
  
  try {
    ElMessageBox.confirm(`确认要批量清空选中的${queryParams.shopIds.length}个店铺的同步数据吗？此操作将无法恢复！`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const res = await batchClearSyncData(queryParams) as unknown as ApiResponse<any>
      if (res.code === 200) {
        ElMessage.success(`批量清空数据成功，成功：${res.data.successCount}，失败：${res.data.failCount}`)
        getList() // 刷新列表
      } else {
        ElMessage.error(res.message || '批量清空数据失败')
      }
    }).catch(() => {
      // 用户取消操作
    })
  } catch (error) {
    console.error('批量清空数据错误:', error)
    ElMessage.error('批量清空数据出现错误')
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getShopList()
  getList()
})

// 组件卸载前清除定时器
onBeforeUnmount(() => {
  // 清除所有轮询定时器
  Object.keys(pollingTimers.value).forEach(key => {
    const timerId = pollingTimers.value[Number(key)]
    if (timerId) {
      // 检查定时器类型并使用正确的清除方法
      clearTimeout(timerId)
    }
  })
  pollingTimers.value = {}
  pollingIntervals.value = {}
  pollingStatus.value = {}
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.mb8 {
  margin-bottom: 8px;
}
</style>