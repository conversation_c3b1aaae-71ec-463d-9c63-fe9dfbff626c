package com.xiao.temu.modules.quality.service;

import com.alibaba.fastjson2.JSONArray;

/**
 * 疵点图片转存服务接口
 */
public interface DefectImageService {
    
    /**
     * 转存疵点图片
     * 
     * @param shopId 店铺ID
     * @param qcBillId 质检单ID
     * @param productSkuId 商品SKUID
     * @param attachments 附件JSON数组
     * @return 处理后的附件JSON数组
     */
    JSONArray transferDefectImages(Long shopId, Long qcBillId, Long productSkuId, JSONArray attachments);


    /**
     * 获取疵点图片URL
     * 
     * @param imageKey 图片存储键
     * @return 图片URL
     */
    String getDefectImageUrl(String imageKey);
} 