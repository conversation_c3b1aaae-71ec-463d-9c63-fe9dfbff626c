package com.xiao.temu.modules.sync.controller;


import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.sync.dto.SalesSyncDTO;
import com.xiao.temu.modules.sync.entity.SalesSyncTask;
import com.xiao.temu.modules.sync.vo.ApiResponse;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.system.service.UserService;
import com.xiao.temu.modules.sync.service.SalesSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 销售数据同步控制器
 */
@Slf4j
@RestController
@RequestMapping("/temu/sales/sync")
public class SalesSyncController {

    @Autowired
    private SalesSyncService salesSyncService;

    @Autowired
    private ShopService shopService;

    @Autowired
    private UserService userService;

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        // 通过用户名查询用户信息获取用户ID
        SysUser user = userService.getUserByUsername(username);
        if (user != null) {
            return user.getUserId();
        }
        throw new RuntimeException("用户未找到: " + username);
    }

    /**
     * 获取同步任务列表
     *
     * @param syncDTO 同步请求参数
     * @return API响应
     */
    @PostMapping("/tasks")
    public ApiResponse getSyncTasks(@RequestBody(required = false) SalesSyncDTO syncDTO) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        List<Long> shopIds;
        if (isAdmin(userId)) {
            // 管理员可以获取指定店铺或所有店铺的任务
            if (syncDTO != null && syncDTO.getShopIds() != null && !syncDTO.getShopIds().isEmpty()) {
                shopIds = new ArrayList<>(syncDTO.getShopIds());
            } else {
                // 获取所有店铺ID
                shopIds = shopService.listAllShops().stream()
                        .map(shop -> shop.getShopId())
                        .collect(Collectors.toList());
            }
        } else {
            // 非管理员用户
            if (syncDTO != null && syncDTO.getShopIds() != null && !syncDTO.getShopIds().isEmpty()) {
                // 过滤出用户有权限的店铺
                shopIds = syncDTO.getShopIds().stream()
                        .filter(shopId -> shopService.checkShopPermission(userId, shopId, false))
                        .collect(Collectors.toList());
            } else {
                // 获取用户有权限的所有店铺
                shopIds = shopService.listUserShops(userId).stream()
                        .map(shop -> shop.getShopId())
                        .collect(Collectors.toList());
            }
        }
        
        List<SalesSyncTask> tasks = salesSyncService.getSyncTasks(shopIds);
        
        // 获取所有相关店铺信息，并创建映射
        Map<Long, String> shopNameMap = new HashMap<>();
        Map<Long, String> shopRemarkMap = new HashMap<>();
        List<Shop> shops = shopService.listAllShops();
        for (Shop shop : shops) {
            shopNameMap.put(shop.getShopId(), shop.getShopName());
            shopRemarkMap.put(shop.getShopId(), shop.getRemark());
        }
        
        // 为每个任务设置店铺名称和更新销售子订单总数
        for (SalesSyncTask task : tasks) {
            task.setShopName(shopNameMap.getOrDefault(task.getShopId(), "未知店铺"));
            task.setShopRemark(shopRemarkMap.getOrDefault(task.getShopId(), ""));
            // 更新销售子订单总数
            Integer orderCount = salesSyncService.getSalesSubOrderCountByShopId(task.getShopId());
            if (orderCount != null) {
                task.setTotalRecords(orderCount);
            }
            // 更新销售SKU总数
            Integer skuCount = salesSyncService.getSalesSkuCountByShopId(task.getShopId());
            if (skuCount != null) {
                task.setSkuTotalRecords(skuCount);
            }
            // 更新仓库信息总数
            Integer warehouseCount = salesSyncService.getWarehouseInfoCountByShopId(task.getShopId());
            if (warehouseCount != null) {
                task.setWarehouseTotalRecords(warehouseCount);
            }
        }
        
        // 处理排序
        if (syncDTO != null && syncDTO.getSortField() != null && !syncDTO.getSortField().isEmpty()) {
            String sortField = syncDTO.getSortField();
            boolean isAsc = "asc".equalsIgnoreCase(syncDTO.getSortOrder());
            
            // 根据排序字段对任务列表进行排序
            if ("shopId".equals(sortField)) {
                if (isAsc) {
                    tasks.sort(Comparator.comparing(SalesSyncTask::getShopId));
                } else {
                    tasks.sort(Comparator.comparing(SalesSyncTask::getShopId).reversed());
                }
            }
            // 可以根据需要添加更多字段的排序支持
        }
        
        // 处理分页
        Integer pageNum = (syncDTO != null && syncDTO.getPageNum() != null) ? syncDTO.getPageNum() : 1;
        Integer pageSize = (syncDTO != null && syncDTO.getPageSize() != null) ? syncDTO.getPageSize() : 10;
        
        // 计算分页数据
        int total = tasks.size();
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);
        
        // 防止索引越界
        if (startIndex >= total) {
            startIndex = 0;
            endIndex = 0;
        }
        
        // 获取当前页数据
        List<SalesSyncTask> pageData = startIndex < endIndex ? 
            tasks.subList(startIndex, endIndex) : 
            List.of();
        
        // 构建分页结果
        Map<String, Object> result = new HashMap<>();
        result.put("total", total);
        result.put("list", pageData);
        result.put("pageNum", pageNum);
        result.put("pageSize", pageSize);
        
        return ApiResponse.success(result);
    }

    /**
     * 手动触发同步
     *
     * @param shopId 店铺ID
     * @param threadCount 线程数量,可选参数
     * @return API响应
     */
    @PostMapping("/trigger/{shopId}")
    public ApiResponse triggerSync(@PathVariable Long shopId,
                                  @RequestParam(required = false) Integer threadCount) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        // 如果是管理员，直接允许操作；否则检查权限
        if (!isAdmin(userId) && !shopService.checkShopPermission(userId, shopId, false)) {
            return ApiResponse.error(403, "您没有访问该店铺的权限");
        }
        
        // 检查当前同步状态
        SalesSyncTask currentTask = salesSyncService.getSyncTaskByShopId(shopId);
        if (currentTask != null && currentTask.getSyncStatus() == 1) {
            // 如果任务已经在同步中状态超过30分钟，可能是因为前一次同步卡住，重置状态
            if (currentTask.getLastSyncTime() != null) {
                LocalDateTime thirtyMinutesAgo = LocalDateTime.now().minusMinutes(30);
                if (currentTask.getLastSyncTime().isBefore(thirtyMinutesAgo)) {
                    log.warn("店铺ID: {} 的同步任务已处于同步中状态超过30分钟，重置状态并重新开始同步", shopId);
                    // 重置任务状态
                    currentTask.setSyncStatus(3); // 设为失败状态
                    currentTask.setErrorMessage("同步任务超时，自动重置");
                    currentTask.setLastUpdateTime(LocalDateTime.now());
                    salesSyncService.updateById(currentTask);
                } else {
                    // 正常在同步中，返回状态
                    ApiResponse.SalesSyncVO result = new ApiResponse.SalesSyncVO();
                    result.setSuccess(true);
                    result.setSyncStatus(1);
                    result.setMessage("同步任务正在进行中");
                    return ApiResponse.success(result);
                }
            } else {
                // 正常在同步中，返回状态
                ApiResponse.SalesSyncVO result = new ApiResponse.SalesSyncVO();
                result.setSuccess(true);
                result.setSyncStatus(1);
                result.setMessage("同步任务正在进行中");
                return ApiResponse.success(result);
            }
        }
        
        // 创建新线程执行同步，而不是直接同步调用
        new Thread(() -> {
            try {
                // 默认全量同步限制为10000条
                salesSyncService.syncSalesDataFullAsync(shopId, 10000, threadCount);
            } catch (Exception e) {
                log.error("同步出错, 店铺ID: {}, 错误: {}", shopId, e.getMessage(), e);
                
                // 确保任务状态更新为失败
                try {
                    SalesSyncTask task = salesSyncService.getSyncTaskByShopId(shopId);
                    if (task != null && task.getSyncStatus() == 1) {
                        task.setSyncStatus(3); // 设置为失败状态
                        task.setErrorMessage(e.getMessage());
                        task.setLastUpdateTime(LocalDateTime.now());
                        salesSyncService.updateById(task);
                    }
                } catch (Exception ex) {
                    log.error("更新任务状态失败: {}", ex.getMessage());
                }
            }
        }, "single-sync-thread-" + shopId).start();
        
        // 立即返回响应，不等待同步完成
        ApiResponse.SalesSyncVO result = new ApiResponse.SalesSyncVO();
        result.setSuccess(true);
        result.setSyncStatus(1); // 设置为同步中状态
        result.setMessage("同步任务已启动，请稍后查看结果");
        
        // 更新任务状态为同步中
        if (currentTask == null) {
            // 如果任务不存在，先初始化
            salesSyncService.initSyncTask(shopId);
            currentTask = salesSyncService.getSyncTaskByShopId(shopId);
        }
        
        if (currentTask != null && currentTask.getSyncStatus() != 1) {
            currentTask.setSyncStatus(1); // 设置为同步中状态
            currentTask.setLastSyncTime(LocalDateTime.now());
            currentTask.setErrorMessage(null);
            salesSyncService.updateById(currentTask);
        }
        
        return ApiResponse.success(result);
    }

    /**
     * 检查用户是否为管理员
     */
    private boolean isAdmin(Long userId) {
        return userService.isAdmin(userId);
    }

    /**
     * 初始化同步任务
     *
     * @param shopId 店铺ID
     * @return API响应
     */
    @PostMapping("/init/{shopId}")
    public ApiResponse initSyncTask(@PathVariable Long shopId) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        // 如果是管理员，直接允许操作；否则检查权限
        if (!isAdmin(userId) && !shopService.checkShopPermission(userId, shopId, true)) {
            return ApiResponse.error(403, "您没有操作该店铺的权限");
        }
        
        // 初始化同步任务
        boolean result = salesSyncService.initSyncTask(shopId);
        
        if (result) {
            return ApiResponse.success("初始化同步任务成功");
        } else {
            return ApiResponse.error(500, "初始化同步任务失败");
        }
    }

    /**
     * 获取同步任务详情
     *
     * @param shopId 店铺ID
     * @return API响应
     */
    @GetMapping("/task/{shopId}")
    public ApiResponse getSyncTask(@PathVariable Long shopId) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        // 如果是管理员，直接允许操作；否则检查权限
        if (!isAdmin(userId) && !shopService.checkShopPermission(userId, shopId, false)) {
            return ApiResponse.error(403, "您没有访问该店铺的权限");
        }
        
        // 获取同步任务
        SalesSyncTask task = salesSyncService.getSyncTaskByShopId(shopId);
        if (task == null) {
            return ApiResponse.error(404, "同步任务不存在");
        }
        
        // 获取店铺信息
        Shop shop = shopService.getShopById(shopId).convertToShop();
        if (shop == null) {
            return ApiResponse.error(404, "店铺不存在");
        }
        
        // 更新销售子订单总数
        Integer orderCount = salesSyncService.getSalesSubOrderCountByShopId(shopId);
        if (orderCount != null) {
            task.setTotalRecords(orderCount);
        }
        
        // 设置店铺名称
        task.setShopName(shop.getShopName());
        task.setShopRemark(shop.getRemark());
        
        return ApiResponse.success(task);
    }

    /**
     * 批量初始化同步任务
     *
     * @param syncDTO 同步请求参数
     * @return API响应
     */
    @PostMapping("/batch/init")
    public ApiResponse batchInitSyncTasks(@RequestBody SalesSyncDTO syncDTO) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        // 获取要初始化的店铺ID列表
        List<Long> shopIds = syncDTO.getShopIds();
        if (shopIds == null || shopIds.isEmpty()) {
            return ApiResponse.error(400, "请选择要初始化的店铺");
        }
        
        // 过滤出有权限的店铺
        List<Long> authorizedShopIds;
        if (isAdmin(userId)) {
            authorizedShopIds = shopIds;
        } else {
            authorizedShopIds = shopIds.stream()
                    .filter(shopId -> shopService.checkShopPermission(userId, shopId, true))
                    .collect(Collectors.toList());
        }
        
        if (authorizedShopIds.isEmpty()) {
            return ApiResponse.error(403, "您没有操作任何选中店铺的权限");
        }
        
        // 初始化同步任务
        int successCount = 0;
        int failCount = 0;
        for (Long shopId : authorizedShopIds) {
            boolean result = salesSyncService.initSyncTask(shopId);
            if (result) {
                successCount++;
            } else {
                failCount++;
            }
        }
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("totalCount", authorizedShopIds.size());
        
        if (failCount == 0) {
            return ApiResponse.success("批量初始化同步任务成功", result);
        } else {
            return ApiResponse.success("部分店铺初始化同步任务失败", result);
        }
    }

    /**
     * 批量触发同步
     *
     * @param syncDTO 同步请求参数
     * @return API响应
     */
    @PostMapping("/batch/trigger")
    public ApiResponse batchTriggerSync(@RequestBody SalesSyncDTO syncDTO) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        // 获取要同步的店铺ID列表
        List<Long> shopIds = syncDTO.getShopIds();
        if (shopIds == null || shopIds.isEmpty()) {
            return ApiResponse.error(400, "请选择要同步的店铺");
        }
        
        // 获取线程数量
        Integer threadCount = syncDTO.getThreadCount();
        
        // 获取同步类型
        Boolean isFullSync = syncDTO.getIsFullSync();
        boolean fullSync = isFullSync != null && isFullSync;
        
        // 获取同步限制
        Integer limit = syncDTO.getLimit();
        int syncLimit = limit != null && limit > 0 ? limit : 10000;
        
        // 过滤出有权限的店铺
        List<Long> authorizedShopIds;
        if (isAdmin(userId)) {
            authorizedShopIds = shopIds;
        } else {
            authorizedShopIds = shopIds.stream()
                    .filter(shopId -> shopService.checkShopPermission(userId, shopId, false))
                    .collect(Collectors.toList());
        }
        
        if (authorizedShopIds.isEmpty()) {
            return ApiResponse.error(403, "您没有操作任何选中店铺的权限");
        }
        
        // 检查各店铺的同步状态，如果有店铺任务状态为同步中但超过30分钟，则重置状态
        LocalDateTime thirtyMinutesAgo = LocalDateTime.now().minusMinutes(30);
        List<Long> shopIdsToSync = new ArrayList<>();
        
        for (Long shopId : authorizedShopIds) {
            SalesSyncTask task = salesSyncService.getSyncTaskByShopId(shopId);
            if (task != null && task.getSyncStatus() == 1) {
                // 检查是否超时
                if (task.getLastSyncTime() != null && task.getLastSyncTime().isBefore(thirtyMinutesAgo)) {
                    log.warn("店铺ID: {} 的同步任务已处于同步中状态超过30分钟，重置状态", shopId);
                    // 重置任务状态
                    task.setSyncStatus(3); // 设为失败状态
                    task.setErrorMessage("同步任务超时，自动重置");
                    task.setLastUpdateTime(LocalDateTime.now());
                    salesSyncService.updateById(task);
                    shopIdsToSync.add(shopId); // 可以重新同步
                } else {
                    // 忽略正在同步中的店铺
                    log.info("店铺ID: {} 正在同步中，跳过本次批量同步", shopId);
                }
            } else {
                // 可以进行同步
                shopIdsToSync.add(shopId);
            }
        }
        
        if (shopIdsToSync.isEmpty()) {
            return ApiResponse.error(400, "所有选中的店铺都在同步中，请稍后再试");
        }
        
        // 在启动同步线程前，先将所有任务状态设置为"同步中"
        for (Long shopId : shopIdsToSync) {
            SalesSyncTask task = salesSyncService.getSyncTaskByShopId(shopId);
            if (task == null) {
                // 如果任务不存在，先初始化
                salesSyncService.initSyncTask(shopId);
                task = salesSyncService.getSyncTaskByShopId(shopId);
            }
            
            if (task != null && task.getSyncStatus() != 1) {
                task.setSyncStatus(1); // 设置为同步中状态
                task.setLastSyncTime(LocalDateTime.now());
                task.setErrorMessage(null);
                salesSyncService.updateById(task);
            }
        }
        
        // 创建新线程执行，但不使用CompletableFuture，以确保可以正确更新状态
        new Thread(() -> {
            try {
                String result = salesSyncService.batchSyncSalesDataAsync(shopIdsToSync, fullSync, syncLimit, threadCount);
                log.info("批量同步完成: {}", result);
            } catch (Exception e) {
                log.error("批量同步出错: {}", e.getMessage(), e);
                
                // 确保更新所有店铺的任务状态
                for (Long shopId : shopIdsToSync) {
                    try {
                        SalesSyncTask task = salesSyncService.getSyncTaskByShopId(shopId);
                        if (task != null && task.getSyncStatus() == 1) {
                            task.setSyncStatus(3); // 设置为失败状态
                            task.setErrorMessage("批量同步出错: " + e.getMessage());
                            task.setLastUpdateTime(LocalDateTime.now());
                            salesSyncService.updateById(task);
                        }
                    } catch (Exception ex) {
                        log.error("更新店铺 {} 任务状态失败: {}", shopId, ex.getMessage());
                    }
                }
            }
        }, "batch-sync-thread").start();
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", shopIdsToSync.size());
        result.put("message", "已启动批量同步任务，" + 
                  (fullSync ? "全量同步限制: " + syncLimit : "增量同步") + 
                  (threadCount != null ? "，线程数: " + threadCount : ""));
        
        return ApiResponse.success(result);
    }

    /**
     * 清空同步数据
     *
     * @param shopId 店铺ID
     * @return API响应
     */
    @PostMapping("/clear/{shopId}")
    public ApiResponse clearSyncData(@PathVariable Long shopId) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        // 如果是管理员，直接允许操作；否则检查权限
        if (!isAdmin(userId) && !shopService.checkShopPermission(userId, shopId, true)) {
            return ApiResponse.error(403, "您没有操作该店铺的权限");
        }
        
        // 清空同步数据
        boolean result = salesSyncService.clearSyncData(shopId);
        
        if (result) {
            return ApiResponse.success("清空同步数据成功");
        } else {
            return ApiResponse.error(500, "清空同步数据失败");
        }
    }

    /**
     * 批量清空同步数据
     *
     * @param syncDTO 同步请求参数
     * @return API响应
     */
    @PostMapping("/batch/clear")
    public ApiResponse batchClearSyncData(@RequestBody SalesSyncDTO syncDTO) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        // 获取要清空的店铺ID列表
        List<Long> shopIds = syncDTO.getShopIds();
        if (shopIds == null || shopIds.isEmpty()) {
            return ApiResponse.error(400, "请选择要清空数据的店铺");
        }
        
        // 过滤出有权限的店铺
        List<Long> authorizedShopIds;
        if (isAdmin(userId)) {
            authorizedShopIds = shopIds;
        } else {
            authorizedShopIds = shopIds.stream()
                    .filter(shopId -> shopService.checkShopPermission(userId, shopId, true))
                    .collect(Collectors.toList());
        }
        
        if (authorizedShopIds.isEmpty()) {
            return ApiResponse.error(403, "您没有操作任何选中店铺的权限");
        }
        
        // 清空同步数据
        int successCount = 0;
        int failCount = 0;
        for (Long shopId : authorizedShopIds) {
            boolean result = salesSyncService.clearSyncData(shopId);
            if (result) {
                successCount++;
            } else {
                failCount++;
            }
        }
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("totalCount", authorizedShopIds.size());
        
        if (failCount == 0) {
            return ApiResponse.success("批量清空同步数据成功", result);
        } else {
            return ApiResponse.success("部分店铺清空同步数据失败", result);
        }
    }

    /**
     * 执行全量同步(供定时任务调用)
     * 每天晚上12点执行,最多同步前10000条数据
     */
    public void syncSalesDataFull() {
        try {
            // 获取所有店铺ID
            List<Long> shopIds = shopService.listAllShops().stream()
                    .map(Shop::getShopId)
                    .collect(Collectors.toList());
            
            // 使用多线程批量同步
            salesSyncService.batchSyncSalesDataAsync(shopIds, true, 10000, null);
            
            log.info("销售数据全量同步任务已启动, 店铺数量: {}", shopIds.size());
        } catch (Exception e) {
            log.error("启动销售数据全量同步失败: {}", e.getMessage(), e);
            throw new RuntimeException("销售数据全量同步失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 执行增量同步(供定时任务调用)
     * 白天每15分钟执行一次
     */
    public void syncSalesDataIncremental() {
        try {
            // 获取所有店铺ID
            List<Long> shopIds = shopService.listAllShops().stream()
                    .map(Shop::getShopId)
                    .collect(Collectors.toList());
            
            // 使用多线程批量同步(增量同步)
            salesSyncService.batchSyncSalesDataAsync(shopIds, false, 0, null);
            
            log.info("销售数据增量同步任务已启动, 店铺数量: {}", shopIds.size());
        } catch (Exception e) {
            log.error("启动销售数据增量同步失败: {}", e.getMessage(), e);
            throw new RuntimeException("销售数据增量同步失败: " + e.getMessage(), e);
        }
    }
}