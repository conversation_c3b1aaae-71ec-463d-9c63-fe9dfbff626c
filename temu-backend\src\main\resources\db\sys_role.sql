/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : temu_api

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 06/06/2025 16:25:45
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE,
  UNIQUE INDEX `uk_role_key`(`role_key` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'admin', 1, '0', '2025-03-20 19:07:49', '2025-03-21 13:07:26', '超级管理员，拥有所有权限');
INSERT INTO `sys_role` VALUES (3, '运营人员', 'operation', 3, '0', '2025-03-21 13:09:27', '2025-03-22 12:25:11', '1');
INSERT INTO `sys_role` VALUES (6, '运营组长', 'operationLeader', 3, '0', '2025-03-22 13:01:41', NULL, '');
INSERT INTO `sys_role` VALUES (8, '运营管理员', 'operationAdministrator', 2, '0', '2025-04-03 09:51:14', '2025-04-03 09:51:33', '运营管理员');
INSERT INTO `sys_role` VALUES (9, '烧花/剪图', 'cutting', 5, '0', '2025-05-06 11:20:43', '2025-05-08 21:11:22', '负责烧花/剪图工序的操作员');
INSERT INTO `sys_role` VALUES (10, '车间/拣货', 'workshop', 6, '0', '2025-05-06 11:20:43', '2025-05-08 21:11:26', '负责车间/拣货工序的操作员');
INSERT INTO `sys_role` VALUES (11, '剪线/压图', 'trimming', 7, '0', '2025-05-06 11:20:43', '2025-05-08 21:11:29', '负责剪线/压图工序的操作员');
INSERT INTO `sys_role` VALUES (12, '发货专员', 'shipping', 8, '0', '2025-05-06 11:20:43', '2025-05-08 21:11:32', '负责发货的工作人员');
INSERT INTO `sys_role` VALUES (13, '包装', 'packaging', 9, '0', '2025-05-06 11:20:43', '2025-05-08 21:11:35', '负责包装工序的操作员');
INSERT INTO `sys_role` VALUES (14, '生产组长', 'productionLeader', 4, '0', '2025-05-09 11:45:16', NULL, '负责管理生产组的组长');
INSERT INTO `sys_role` VALUES (16, '查货', 'inspection', 8, '0', '2025-06-06 15:13:14', NULL, '负责查货工序的操作员');

SET FOREIGN_KEY_CHECKS = 1;
