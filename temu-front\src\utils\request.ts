import axios from 'axios'
import type { AxiosRequestConfig } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { getToken } from '@/utils/auth'
import router from '@/router'

// 扩展AxiosRequestConfig类型，添加retryCount属性
declare module 'axios' {
  interface AxiosRequestConfig {
    retryCount?: number;
  }
}

// 定义最大重试次数
const MAX_RETRIES = 0;
// 重试延迟时间(ms)
const RETRY_DELAY = 1000;

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api', // API 基础URL，从环境变量获取
  timeout: 30000 // 请求超时时间增加到30秒
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    const token = getToken()
    if (token) {
      // 让每个请求携带token
      config.headers['Authorization'] = `Bearer ${token}`
    }
    
    // 添加重试配置
    if (!config.retryCount) {
      config.retryCount = 0;
    }
    
    return config
  },
  error => {
    // 对请求错误做些什么
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 如果是文件下载或其他特殊请求，直接返回
    if (response.config.responseType === 'blob') {
      // 检查是否出错（如果是错误的blob，它可能包含json错误信息）
      const contentType = response.headers['content-type']
      
      // 记录响应类型和大小信息，便于调试
      console.log('Blob响应:', {
        contentType: contentType,
        size: response.data?.size || 0,
        status: response.status,
        url: response.config.url
      })
      
      // 检查是否是JSON响应（错误消息）
      if (contentType && contentType.includes('application/json')) {
        // 可能是错误响应被包装成了blob
        return new Promise((resolve, reject) => {
          const reader = new FileReader()
          reader.onload = () => {
            try {
              const errorJson = JSON.parse(reader.result as string)
              console.log('解析Blob中的JSON:', errorJson)
              
              if (errorJson.code !== 200 && errorJson.code !== 0) {
                ElMessage({
                  message: errorJson.message || '下载失败',
                  type: 'error',
                  duration: 5 * 1000
                })
                reject(new Error(errorJson.message || '下载失败'))
              } else {
                // 虽然是JSON格式，但是成功响应
                resolve(response)
              }
            } catch (e) {
              console.log('Blob不是有效的JSON，视为文件内容', e)
              // 如果不是有效的JSON，则可能是真正的文件
              resolve(response)
            }
          }
          reader.onerror = () => {
            console.log('读取Blob内容失败，返回原始响应')
            resolve(response)
          }
          reader.readAsText(response.data)
        })
      }
      
      // 检查是否是空响应或太小的响应（可能是错误）
      if (response.data.size < 50) {
        console.warn('警告：下载的文件过小，可能不是预期的文件数据', response.data.size)
        // 读取小文件内容，检查是否有错误信息
        return new Promise((resolve, reject) => {
          const reader = new FileReader()
          reader.onload = () => {
            try {
              const content = reader.result as string
              console.log('小文件内容:', content)
              if (content.includes('error') || content.includes('错误')) {
                reject(new Error('下载失败: ' + content))
              } else {
                resolve(response)
              }
            } catch (e) {
              resolve(response)
            }
          }
          reader.onerror = () => resolve(response)
          reader.readAsText(response.data)
        })
      }
      
      return response
    }
    
    const res = response.data
    
    // 根据后端API统一响应格式判断请求是否成功
    if (res.code !== 200 && res.code !== 0) {
      let msg = res.message;
      if (msg === 'SYSTEM_EXCEPTION') {
        msg = 'temu平台系统异常,可以多重试几次';
      }
      ElMessage({
        message: msg || '系统错误',
        type: 'error',
        duration: 5 * 1000
      })
      
      // 401: 未授权；token过期或无效
      if (res.code === 401) {
        // 重新登录
        ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const userStore = useUserStore()
          userStore.resetToken() // 先重置token
          router.push('/login') // 跳转到登录页
        }).catch(() => {
          // 取消操作
        })
      }
      return Promise.reject(new Error(res.message || '系统错误'))
    } else {
      return res
    }
  },
  async error => {
    // 打印详细错误信息
    console.error('请求错误:', {
      message: error.message,
      config: error.config,
      response: error.response,
      request: error.request
    })
    
    const config = error.config;
    
    // 处理重试逻辑
    if (config && config.retryCount < MAX_RETRIES && 
        (error.message.includes('timeout') || 
         error.message.includes('Network Error') || 
         error.code === 'ECONNABORTED' || 
         !error.response || 
         error.response.status >= 500)) {
      
      // 增加重试计数
      config.retryCount += 1;
      
      console.log(`请求重试 (${config.retryCount}/${MAX_RETRIES}): ${config.url}`);
      
      // 创建新的Promise来处理延迟
      const delayRetry = new Promise(resolve => {
        setTimeout(() => {
          console.log(`重试请求: ${config.url}`);
          resolve(null);
        }, RETRY_DELAY * config.retryCount); // 每次重试增加延迟
      });
      
      // 等待延迟后重试请求
      await delayRetry;
      return service(config);
    }
    
    const { response } = error
    
    // 处理HTTP状态码错误
    if (response) {
      const { status, data } = response
      let message = data?.message || '请求失败'
      if (message === 'SYSTEM_EXCEPTION') {
        message = 'temu平台系统异常,请稍后重试';
      }
      
      // 针对特定状态码的处理
      switch (status) {
        case 400:
          message = message || '请求错误'
          break
        case 401:
          message = message || '未授权，请重新登录'
          // 清除token并跳转登录页
          const userStore = useUserStore()
          userStore.resetToken()
          router.push('/login')
          break
        case 403:
          message = message || '拒绝访问'
          break
        case 404:
          message = message || '请求地址不存在'
          break
        case 500:
          message = message || '服务器内部错误'
          break
        default:
          message = message || `连接错误${status}`
      }
      
      ElMessage({
        message,
        type: 'error',
        duration: 5 * 1000
      })
    } else if (error.request) {
      // 请求已发送但未收到响应
      ElMessage({
        message: '服务器无响应，请稍后重试',
        type: 'error',
        duration: 5 * 1000
      })
    } else {
      // 请求设置时发生错误
      ElMessage({
        message: '网络异常，请检查您的网络连接',
        type: 'error',
        duration: 5 * 1000
      })
    }
    
    return Promise.reject(error)
  }
)

export default service 