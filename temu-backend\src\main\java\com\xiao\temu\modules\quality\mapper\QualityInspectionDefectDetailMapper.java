package com.xiao.temu.modules.quality.mapper;

import com.xiao.temu.modules.quality.entity.QualityInspectionDefectDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 质检不合格详情数据Mapper接口
 */
@Mapper
public interface QualityInspectionDefectDetailMapper {
    
    /**
     * 插入质检不合格详情数据(有则更新)
     *
     * @param defectDetail 质检不合格详情数据
     * @return 影响行数
     */
    int insertOrUpdate(QualityInspectionDefectDetail defectDetail);
    
    /**
     * 批量插入质检不合格详情数据(有则更新)
     *
     * @param defectDetails 质检不合格详情数据列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(List<QualityInspectionDefectDetail> defectDetails);
    
    /**
     * 根据店铺ID和质检单ID查询质检不合格详情数据
     *
     * @param shopId 店铺ID
     * @param qcBillId 质检单ID
     * @return 质检不合格详情数据列表
     */
    List<QualityInspectionDefectDetail> selectByShopIdAndQcBillId(@Param("shopId") Long shopId, @Param("qcBillId") Long qcBillId);
    
    /**
     * 根据店铺ID查询已存在的质检单ID列表
     *
     * @param shopId 店铺ID
     * @return 质检单ID列表
     */
    List<Long> selectExistingQcBillIdsByShopId(@Param("shopId") Long shopId);
    
    /**
     * 根据店铺ID删除质检不合格详情数据
     *
     * @param params 参数，包含shopId
     * @return 影响行数
     */
    int deleteByShopId(Map<String, Object> params);
} 