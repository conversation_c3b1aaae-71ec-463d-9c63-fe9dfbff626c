<template>
  <!-- 操作栏 -->
  <div class="pagination-wrapper">
    <PaginationBar 
      :current-page="Number(queryParams.pageNo)" 
      :page-size="Number(queryParams.pageSize)"
      :total="total"
      :shop-ranges="shopRanges"
      @update:current-page="updateCurrentPage"
      @update:page-size="updatePageSize" 
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange" 
    />
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import { PaginationBar } from '@/components/temu'
import type { PurchaseOrderRequestDTO, ShopDataRange } from '@/types/purchaseOrder'

const props = defineProps({
  // 查询参数
  queryParams: {
    type: Object as () => PurchaseOrderRequestDTO,
    required: true
  },
  // 总记录数
  total: {
    type: Number,
    required: true
  },
  // 店铺数据范围
  shopRanges: {
    type: Array as () => ShopDataRange[],
    default: () => []
  }
})

const emit = defineEmits([
  'update:queryParams',
  'size-change',
  'current-change'
])

// 更新当前页码
const updateCurrentPage = (val: number) => {
  emit('update:queryParams', { 
    ...props.queryParams, 
    pageNo: Number(val)
  })
}

// 更新每页大小
const updatePageSize = (val: number) => {
  emit('update:queryParams', { 
    ...props.queryParams, 
    pageSize: Number(val)
  })
}

// 处理每页显示条数变化
const handleSizeChange = (val: number) => {
  emit('size-change', val)
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  emit('current-change', val)
}
</script>

<style scoped>
.pagination-wrapper {
  margin-top: 0;
  display: flex;
  justify-content: flex-end;
  background-color: #fff;
  padding: 10px 16px;
  border-radius: 0 0 4px 4px;
  border: 1px solid #ebeef5;
  border-top: none;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12);
  flex-shrink: 0;
}
</style> 