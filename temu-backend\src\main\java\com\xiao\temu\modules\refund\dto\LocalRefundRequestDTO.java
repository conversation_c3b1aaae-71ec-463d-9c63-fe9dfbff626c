package com.xiao.temu.modules.refund.dto;

import lombok.Data;
import java.util.List;
import java.time.LocalDateTime;

/**
 * 本地退货包裹查询请求DTO
 */
@Data
public class LocalRefundRequestDTO {
    
    /**
     * 页码
     */
    private Integer pageNo;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
    
    /**
     * 店铺ID列表,支持多店铺查询
     */
    private List<Long> shopIds;
    
    /**
     * 出库开始时间
     */
    private LocalDateTime outboundTimeStart;
    
    /**
     * 出库结束时间
     */
    private LocalDateTime outboundTimeEnd;
    
    /**
     * SKU ID列表
     */
    private List<Long> productSkuIdList;
    
    /**
     * 退货包裹号列表
     */
    private List<String> returnSupplierPackageNos;
    
    /**
     * 备货单号列表
     */
    private List<String> purchaseSubOrderSns;
    
    /**
     * 是否忽略权限检查
     * 为true时,超级管理员或拥有全部数据权限的用户可以查看所有数据
     */
    private Boolean ignorePermissionCheck = false;
} 