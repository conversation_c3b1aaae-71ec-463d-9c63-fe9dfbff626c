package com.xiao.temu.modules.quality.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xiao.temu.infrastructure.storage.CosStorageService;
import com.xiao.temu.modules.quality.service.DefectImageService;
import com.xiao.temu.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 疵点图片转存服务实现类
 */
@Slf4j
@Service
public class DefectImageServiceImpl implements DefectImageService {

    @Autowired
    private CosStorageService cosStorageService;
    
    // 自定义字段名，用于标识已转存的图片
    private static final String TRANSFERRED_FIELD = "imageKey";
    // 默认的URL字段名
    private static final String URL_FIELD = "url";
    
    @Override
    public JSONArray transferDefectImages(Long shopId, Long qcBillId, Long productSkuId, JSONArray attachments) {
        if (attachments == null || attachments.isEmpty()) {
            return attachments;
        }
        
        // 检查第一个元素类型，确定处理方式
        boolean isStringArray = isAttachmentStringArray(attachments);
        
        if (isStringArray) {
            // 如果是字符串数组，先转换为JSONObject数组
            JSONArray objAttachments = convertStringArrayToObjectArray(attachments);
            return transferJsonObjectAttachments(shopId, qcBillId, productSkuId, objAttachments);
        } else {
            // 如果已经是JSONObject数组，直接处理
            return transferJsonObjectAttachments(shopId, qcBillId, productSkuId, attachments);
        }
    }
    
    /**
     * 处理JSONObject数组形式的附件
     */
    private JSONArray transferJsonObjectAttachments(Long shopId, Long qcBillId, Long productSkuId, JSONArray attachments) {
        // 创建新的附件数组
        JSONArray newAttachments = new JSONArray();
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        for (int i = 0; i < attachments.size(); i++) {
            JSONObject attachment = attachments.getJSONObject(i);
            if (attachment == null) {
                continue;
            }
            
            // 检查是否已经转存过
            if (attachment.containsKey(TRANSFERRED_FIELD)) {
                newAttachments.add(attachment);
                continue;
            }
            
            // 获取图片URL
            String imageUrl = attachment.getString(URL_FIELD);
            if (imageUrl == null || imageUrl.isEmpty()) {
                continue;
            }
            
            // 异步处理图片转存
            final int index = i;
            CompletableFuture<Void> future = transferImageAsync(shopId, qcBillId, productSkuId, imageUrl)
                .thenAccept(result -> {
                    if (result != null) {
                        // 修改原始对象，添加imageKey字段
                        attachment.put(TRANSFERRED_FIELD, result);
                        // 使用同步操作将对象添加到新数组中
                        synchronized (newAttachments) {
                            newAttachments.add(attachment);
                        }
                        log.info("疵点图片转存成功: shopId={}, qcBillId={}, productSkuId={}, index={}", 
                                shopId, qcBillId, productSkuId, index);
                    } else {
                        // 如果转存失败，保留原始URL
                        synchronized (newAttachments) {
                            newAttachments.add(attachment);
                        }
                        log.warn("疵点图片转存失败: shopId={}, qcBillId={}, productSkuId={}, index={}", 
                                shopId, qcBillId, productSkuId, index);
                    }
                });
            
            futures.add(future);
        }
        
        // 等待所有异步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        return newAttachments;
    }
    
    /**
     * 检查附件数组是否为字符串数组
     */
    private boolean isAttachmentStringArray(JSONArray attachments) {
        if (attachments.isEmpty()) {
            return false;
        }
        
        try {
            // 尝试获取第一个元素作为字符串
            String url = attachments.getString(0);
            return url != null;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 将字符串数组转换为JSONObject数组
     */
    private JSONArray convertStringArrayToObjectArray(JSONArray stringArray) {
        JSONArray objectArray = new JSONArray();
        
        for (int i = 0; i < stringArray.size(); i++) {
            try {
                String url = stringArray.getString(i);
                if (url != null && !url.isEmpty()) {
                    JSONObject attachment = new JSONObject();
                    attachment.put(URL_FIELD, url);
                    objectArray.add(attachment);
                }
            } catch (Exception e) {
                log.warn("转换附件字符串到对象失败: index={}, error={}", i, e.getMessage());
            }
        }
        
        return objectArray;
    }
    
    @Override
    public String getDefectImageUrl(String imageKey) {
        if (imageKey == null || imageKey.isEmpty()) {
            return null;
        }
        
        // 默认生成24小时有效期的URL
        return cosStorageService.generateImageUrl(imageKey, 24);
    }
    
    /**
     * 异步转存图片
     * 
     * @param shopId 店铺ID
     * @param qcBillId 质检单ID
     * @param productSkuId 商品SKUID
     * @param imageUrl 图片URL
     * @return 转存后的图片Key
     */
    @Async
    public CompletableFuture<String> transferImageAsync(Long shopId, Long qcBillId, Long productSkuId, String imageUrl) {
        File localFile = null;
        try {
            // 记录原始URL
            log.info("开始下载疵点图片: shopId={}, qcBillId={}, productSkuId={}, url={}", 
                    shopId, qcBillId, productSkuId, imageUrl);
            
            // 下载图片到本地临时文件
            localFile = cosStorageService.downloadImage(imageUrl);
            if (localFile == null) {
                log.error("下载疵点图片失败: shopId={}, qcBillId={}, productSkuId={}, url={}", 
                        shopId, qcBillId, productSkuId, imageUrl);
                return CompletableFuture.completedFuture(null);
            }
            
            log.info("疵点图片下载成功: shopId={}, qcBillId={}, productSkuId={}, 文件大小={}字节", 
                    shopId, qcBillId, productSkuId, localFile.length());
            
            // 生成随机文件名，避免覆盖同名文件
            // 使用时间戳+UUID作为文件名
            String timestamp = DateUtils.formatLocalDateTime(LocalDateTime.now()).replace(" ", "").replace(":", "").replace("-", "");
            String uuid = UUID.randomUUID().toString().substring(0, 8);
            String fileName = String.format("%s_%s_%s_%s_%s.jpg", 
                    timestamp, uuid, shopId, qcBillId, productSkuId);
            
            // 生成存储Key
            String key = cosStorageService.generateDefectImageKey(shopId, qcBillId, productSkuId, fileName);
            
            // 上传图片
            boolean success = cosStorageService.uploadImage(localFile, key);
            if (success) {
                log.info("疵点图片上传成功: shopId={}, qcBillId={}, productSkuId={}, key={}", 
                        shopId, qcBillId, productSkuId, key);
                return CompletableFuture.completedFuture(key);
            } else {
                log.error("疵点图片上传失败: shopId={}, qcBillId={}, productSkuId={}", 
                        shopId, qcBillId, productSkuId);
                return CompletableFuture.completedFuture(null);
            }
        } catch (Exception e) {
            log.error("转存疵点图片异常: shopId={}, qcBillId={}, productSkuId={}, url={}, error={}", 
                    shopId, qcBillId, productSkuId, imageUrl, e.getMessage(), e);
            return CompletableFuture.completedFuture(null);
        } finally {
            // 删除临时文件
            if (localFile != null && localFile.exists()) {
                boolean deleted = localFile.delete();
                log.info("临时文件删除{}: {}", deleted ? "成功" : "失败", localFile.getAbsolutePath());
            }
        }
    }
} 