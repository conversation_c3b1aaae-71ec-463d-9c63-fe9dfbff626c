<template>
  <div 
    class="sidebar-container" 
    :class="{ 'is-collapse': isCollapse }"
  >
    <logo v-if="showLogo" :collapse="isCollapse" />
    
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :active-text-color="variables.menuActiveText"
        :unique-opened="true"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="route in routes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import Logo from './Logo.vue'
import SidebarItem from './SidebarItem.vue'
import { useAppStore } from '@/store/modules/app'
import { usePermissionStore } from '@/store/modules/permission'

// 菜单样式变量
const variables = {
  menuBg: '#22a699',
  menuText: '#ffffff',
  menuActiveText: '#ffffff'
}

const permissionStore = usePermissionStore()
const appStore = useAppStore()
const route = useRoute()

// 是否折叠
const isCollapse = computed(() => !appStore.sidebar.opened)
// 是否显示Logo
const showLogo = computed(() => true)
// 路由列表
const routes = computed(() => {
  const routesList = permissionStore.routes
  
  // 检查路由列表数据结构
  if (!routesList || routesList.length === 0) {
    return []
  }
  
  // 检查是否是首页路由
  const isDashboardRoute = (route) => {
    if (!route || !route.path) return false
    return route.path === '/' || 
           route.path === '/dashboard' || 
           route.path.endsWith('/dashboard') ||
           (route.name && 
            (String(route.name) === 'Dashboard' || 
             String(route.name) === '首页' || 
             String(route.name).toLowerCase().includes('dashboard')))
  }
  
  // 递归清理路由树，移除多余项
  const cleanupRoutes = (routes) => {
    if (!routes || !Array.isArray(routes)) return []
    
    // 先筛选出首页路由
    const dashboardRoutes = routes.filter(route => isDashboardRoute(route))
    
    // 只保留第一个首页路由
    const finalDashboardRoute = dashboardRoutes.length > 0 ? [dashboardRoutes[0]] : []
    
    // 清理非首页路由
    const nonDashboardRoutes = routes
      .filter(route => !isDashboardRoute(route))
      // 过滤掉包含hidden:true的路由项
      .filter(route => !(route.meta && route.meta.hidden === true))
      .map(route => {
        // 递归处理子路由
        if (route.children && route.children.length > 0) {
          const children = cleanupRoutes(route.children)
          return {...route, children}
        }
        return route
      })
      // 过滤掉空目录 (目录类型且无子菜单)
      .filter(route => {
        const isEmptyDirectory = 
          route.meta?.menuType === 'M' && 
          (!route.children || route.children.length === 0)
        
        if (isEmptyDirectory) {
          return false
        }
        return true
      })
      
    // 合并首页和其他路由
    return [...finalDashboardRoute, ...nonDashboardRoutes]
  }
  
  // 深度处理路由
  const cleanedRoutes = cleanupRoutes(routesList)
  
  // 最终结果：过滤掉登录、404等纯静态路由和需要隐藏的路由
  return cleanedRoutes.filter(route => {
    if (route.path === '/login' || route.path === '/404') {
      return false
    }
    
    // 过滤需要隐藏的路由
    if (route.meta && route.meta.hidden === true) {
      return false
    }
    
    return true
  })
})
// 当前激活的菜单
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})

// 挂载时打印路由
onMounted(() => {
  // 递归遍历并打印路由树，方便调试
  const printRouteTree = (routes, level = 0) => {
    if (!routes) return
    
    routes.forEach(route => {
      const indent = '  '.repeat(level)
      
      if (route.children && route.children.length > 0) {
        printRouteTree(route.children, level + 1)
      }
    })
  }
})

// 监听菜单数据变化
watch(() => permissionStore.routes, (newRoutes) => {
  // 监听菜单变化
}, { deep: true })
</script>

<style lang="scss" scoped>
.sidebar-container {
  height: 100%;
  width: 210px;
  overflow: hidden;
  transition: all 0.28s ease-out;
  background-color: #22a699;

  &.is-collapse {
    width: 54px !important;
    
    :deep(.el-menu) {
      border-right: none;
    }

    :deep(.el-menu-item), :deep(.el-sub-menu__title) {
      padding: 0 !important;
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      
      .el-icon {
        margin: 0 !important;
        padding: 0 !important;
        width: auto !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
      }
      
      span {
        display: none !important;
      }

      .el-sub-menu__icon-arrow {
        display: none !important;
      }
    }

    :deep(.el-menu--collapse) {
      width: 54px !important;
    }
  }
  
  :deep(.el-menu) {
    border-right: none;
  }
  
  :deep(.el-menu-item), :deep(.el-sub-menu__title) {
    padding: 0 20px;
    height: 50px;
    line-height: 50px;
    display: flex;
    align-items: center;
    
    .el-icon {
      margin-right: 16px;
      width: 24px;
      text-align: center;
      font-size: 18px;
      transition: all 0.28s;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    span {
      display: inline-block;
      opacity: 1;
      transition: opacity 0.28s;
    }

    .el-sub-menu__icon-arrow {
      position: absolute;
      right: 20px;
      margin-top: -3px;
      transition: transform 0.28s;
      font-size: 12px;
      color: #ffffff;
    }
  }
  
  :deep(.el-menu-item.is-active) {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }
  
  :deep(.el-sub-menu.is-active .el-sub-menu__title) {
    color: #ffffff !important;
  }
  
  :deep(.el-menu-item:hover), :deep(.el-sub-menu__title:hover) {
    background-color: rgba(255, 255, 255, 0.2) !important;
  }
}
</style> 