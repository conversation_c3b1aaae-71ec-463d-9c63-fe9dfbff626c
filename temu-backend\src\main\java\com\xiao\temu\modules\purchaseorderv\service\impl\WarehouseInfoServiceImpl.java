package com.xiao.temu.modules.purchaseorderv.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiao.temu.modules.purchaseorderv.dto.WarehouseInfoBatchDTO;
import com.xiao.temu.modules.purchaseorderv.dto.WarehouseInfoDTO;
import com.xiao.temu.modules.purchaseorderv.entity.WarehouseInfo;
import com.xiao.temu.modules.purchaseorderv.mapper.WarehouseInfoMapper;
import com.xiao.temu.modules.purchaseorderv.service.WarehouseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 仓库信息服务实现类
 */
@Slf4j
@Service
public class WarehouseInfoServiceImpl extends ServiceImpl<WarehouseInfoMapper, WarehouseInfo> implements WarehouseInfoService {
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveWarehouseInfo(WarehouseInfoBatchDTO warehouseInfoBatchDTO) {
        if (warehouseInfoBatchDTO == null || warehouseInfoBatchDTO.getResult() == null || warehouseInfoBatchDTO.getResult().isEmpty()) {
            log.warn("批量保存仓库信息时数据为空");
            return false;
        }
        
        // 获取所有DTO中的subWid列表
        List<Long> subWidList = warehouseInfoBatchDTO.getResult().stream()
                .map(WarehouseInfoDTO::getSubWid)
                .collect(Collectors.toList());
        
        log.info("开始批量保存仓库信息，数据量: {}", subWidList.size());
        
        // 查询数据库中已经存在的仓库信息
        LambdaQueryWrapper<WarehouseInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WarehouseInfo::getSubWid, subWidList);
        List<WarehouseInfo> existingWarehouseList = this.list(queryWrapper);
        
        // 将已存在的仓库信息转换为Map，方便后续使用subWid快速查找
        Map<Long, WarehouseInfo> existingWarehouseMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(existingWarehouseList)) {
            existingWarehouseMap = existingWarehouseList.stream()
                    .collect(Collectors.toMap(WarehouseInfo::getSubWid, warehouse -> warehouse));
        }
        
        // 用于存储需要新增的仓库信息
        List<WarehouseInfo> warehouseInfoToInsert = new ArrayList<>();
        // 用于存储需要更新的仓库信息
        List<WarehouseInfo> warehouseInfoToUpdate = new ArrayList<>();
        
        LocalDateTime now = LocalDateTime.now();
        
        // 遍历DTO，分别处理新增和更新的情况
        for (WarehouseInfoDTO warehouseInfoDTO : warehouseInfoBatchDTO.getResult()) {
            Long subWid = warehouseInfoDTO.getSubWid();
            
            // 判断是否已存在
            if (existingWarehouseMap.containsKey(subWid)) {
                // 更新已存在的记录
                WarehouseInfo existingWarehouse = existingWarehouseMap.get(subWid);
                BeanUtils.copyProperties(warehouseInfoDTO, existingWarehouse, "id", "createTime");
                existingWarehouse.setUpdateTime(now);
                warehouseInfoToUpdate.add(existingWarehouse);
            } else {
                // 新增记录
                WarehouseInfo newWarehouse = new WarehouseInfo();
                BeanUtils.copyProperties(warehouseInfoDTO, newWarehouse);
                newWarehouse.setCreateTime(now);
                newWarehouse.setUpdateTime(now);
                warehouseInfoToInsert.add(newWarehouse);
            }
        }
        
        // 执行批量更新和插入
        boolean updateResult = true;
        boolean insertResult = true;
        
        if (!warehouseInfoToUpdate.isEmpty()) {
            log.info("更新已存在的仓库信息，数量: {}", warehouseInfoToUpdate.size());
            updateResult = this.updateBatchById(warehouseInfoToUpdate);
        }
        
        if (!warehouseInfoToInsert.isEmpty()) {
            log.info("插入新的仓库信息，数量: {}", warehouseInfoToInsert.size());
            insertResult = this.saveBatch(warehouseInfoToInsert);
        }
        
        log.info("批量保存仓库信息完成，更新结果: {}，新增结果: {}", updateResult, insertResult);
        
        // 两者都成功才返回true
        return updateResult && insertResult;
    }
} 