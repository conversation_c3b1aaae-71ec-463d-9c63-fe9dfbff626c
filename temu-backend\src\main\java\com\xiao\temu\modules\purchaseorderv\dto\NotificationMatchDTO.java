package com.xiao.temu.modules.purchaseorderv.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * 通知匹配查询DTO
 */
@Data
public class NotificationMatchDTO {
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 通知类型
     */
    @NotNull(message = "通知类型不能为空")
    private Integer notificationType;
    
    /**
     * 备货单号（模糊匹配）
     */
    private String orderSnLike;
    
    /**
     * 商品名称（模糊匹配）
     */
    private String productNameLike;
    
    /**
     * 供应商ID
     */
    private Long supplierId;
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页记录数
     */
    private Integer pageSize = 10;
} 