.header-container {
  width: 100%;
}

.status-bar {
  width: 100%;
  background-color: #ffffff;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 30rpx;
  padding-right: 30rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #eeeeee;
  box-sizing: border-box;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-btn {
  padding-right: 20rpx;
}

.back-btn-img {
  width: 40rpx;
  height: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}