package com.xiao.temu.modules.production.controller;

import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.modules.production.dto.ProductionGroupRoleAssignmentDTO;
import com.xiao.temu.modules.production.entity.ProductionGroupRoleAssignment;
import com.xiao.temu.modules.production.service.ProductionGroupMemberService;
import com.xiao.temu.modules.production.service.ProductionGroupRoleAssignmentService;
import com.xiao.temu.modules.production.service.ProductionGroupService;
import com.xiao.temu.modules.system.entity.SysRole;
import com.xiao.temu.modules.system.service.RoleService;
import com.xiao.temu.security.annotation.RequiresPermission;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;

/**
 * 生产组角色控制器
 */
@Slf4j
@RestController
@RequestMapping("/production/role")
@RequiredArgsConstructor
public class ProductionGroupRoleController {
    private final ProductionGroupRoleAssignmentService roleAssignmentService;
    private final ProductionGroupService productionGroupService;
    private final ProductionGroupMemberService memberService;
    private final RoleService roleService;

    /**
     * 获取角色分配列表
     */
    @GetMapping("/list")
    @RequiresPermission("production:role:list")
    public ApiResponse listAssignments(
            @RequestParam Long groupId,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) Long roleId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        
        // 校验生产组是否存在
        if (!productionGroupService.existsById(groupId)) {
            return ApiResponse.error("生产组不存在");
        }
        
        // 检查当前用户是否有权限
        checkGroupPermission(groupId);
        
        // 查询角色分配列表
        return ApiResponse.success(roleAssignmentService.listAssignments(groupId, userId, roleId, pageNum, pageSize));
    }

    /**
     * 获取用户在生产组中的角色列表
     */
    @GetMapping("/user/{groupId}/{userId}")
    @RequiresPermission("production:role:list")
    public ApiResponse getUserRoles(@PathVariable Long groupId, @PathVariable Long userId) {
        // 校验生产组是否存在
        if (!productionGroupService.existsById(groupId)) {
            return ApiResponse.error("生产组不存在");
        }
        
        // 检查当前用户是否有权限
        checkGroupPermission(groupId);
        
        // 检查用户是否为该组成员
        if (!memberService.checkUserInGroup(groupId, userId)) {
            return ApiResponse.error("该用户不是生产组成员");
        }
        
        // 查询用户角色ID列表
        List<Long> roleIds = roleAssignmentService.getUserRoleIds(groupId, userId);
        
        return ApiResponse.success(roleIds);
    }
    
    /**
     * 获取生产角色列表
     */
    @GetMapping("/available")
    @RequiresPermission("production:role:list")
    public ApiResponse getAvailableRoles() {
        // 获取生产相关角色（烧花/剪图cutting 车间/抹货workshop 剪线/压图trimming 发货专员 shipping 包装 packaging 查货inspection）
        SysRole queryRole = new SysRole();
        List<SysRole> roleList = roleService.getAllRoles();
        
        // 过滤出生产角色（通过roleKey筛选）
        List<SysRole> productionRoles = roleList.stream()
                .filter(role -> "cutting".equals(role.getRoleKey()) || 
                                "workshop".equals(role.getRoleKey()) || 
                                "trimming".equals(role.getRoleKey()) || 
                                "shipping".equals(role.getRoleKey()) || 
                                "packaging".equals(role.getRoleKey()) ||
                                "inspection".equals(role.getRoleKey()))
                .collect(java.util.stream.Collectors.toList());
        
        return ApiResponse.success(productionRoles);
    }

    /**
     * 分配角色给用户
     */
    @PostMapping("/assign")
    @RequiresPermission("production:role:assign")
    public ApiResponse assignRole(@RequestBody Map<String, Object> params) {
        Long groupId = Long.valueOf(params.get("groupId").toString());
        Long userId = Long.valueOf(params.get("userId").toString());
        Long roleId = Long.valueOf(params.get("roleId").toString());
        
        // 校验生产组是否存在
        if (!productionGroupService.existsById(groupId)) {
            return ApiResponse.error("生产组不存在");
        }
        
        // 检查当前用户是否是该组的组长
        Long currentUserId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        if (!isAdmin && !productionGroupService.isGroupLeader(currentUserId, groupId)) {
            return ApiResponse.error("只有生产组组长才能分配角色");
        }
        
        // 检查用户是否为该组成员
        if (!memberService.checkUserInGroup(groupId, userId)) {
            return ApiResponse.error("该用户不是生产组成员");
        }
        
        // 检查角色是否已经分配
        if (roleAssignmentService.checkUserHasRole(groupId, userId, roleId)) {
            return ApiResponse.error("角色已经分配给该用户");
        }
        
        // 创建角色分配
        ProductionGroupRoleAssignment assignment = new ProductionGroupRoleAssignment();
        assignment.setGroupId(groupId);
        assignment.setUserId(userId);
        assignment.setRoleId(roleId);
        assignment.setAssignTime(new Date());
        assignment.setAssignBy(currentUserId);
        assignment.setStatus("0");
        
        boolean result = roleAssignmentService.addAssignment(assignment);
        
        return result ? ApiResponse.success() : ApiResponse.error("分配角色失败");
    }

    /**
     * 批量分配角色给用户
     */
    @PostMapping("/batch-assign")
    @RequiresPermission("production:role:assign")
    public ApiResponse batchAssignRoles(@RequestBody Map<String, Object> params) {
        Long groupId = Long.valueOf(params.get("groupId").toString());
        Long userId = Long.valueOf(params.get("userId").toString());
        
        // 从 params 中获取 roleIds
        Object roleIdsObject = params.get("roleIds");
        List<Long> roleIds = new ArrayList<>();

        if (roleIdsObject instanceof List) {
            List<?> rawList = (List<?>) roleIdsObject;
            for (Object item : rawList) {
                if (item instanceof Number) {
                    roleIds.add(((Number) item).longValue());
                } else if (item instanceof String) {
                    try {
                        roleIds.add(Long.valueOf((String) item));
                    } catch (NumberFormatException e) {
                        // 处理无法转换为Long的字符串的情况，例如记录日志或返回错误
                        log.warn("Invalid roleId format in batch assign: {}", item);
                        return ApiResponse.error("角色ID格式无效");
                    }
                } else {
                    // 处理非数字或非字符串的情况
                    log.warn("Unexpected type for roleId in batch assign: {}", item.getClass().getName());
                    return ApiResponse.error("角色ID类型错误");
                }
            }
        }
        
        // 校验生产组是否存在
        if (!productionGroupService.existsById(groupId)) {
            return ApiResponse.error("生产组不存在");
        }
        
        // 检查当前用户是否是该组的组长
        Long currentUserId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        if (!isAdmin && !productionGroupService.isGroupLeader(currentUserId, groupId)) {
            return ApiResponse.error("只有生产组组长才能分配角色");
        }
        
        // 检查用户是否为该组成员
        if (!memberService.checkUserInGroup(groupId, userId)) {
            return ApiResponse.error("该用户不是生产组成员");
        }
        
        // 批量分配角色
        boolean result = roleAssignmentService.batchAssignRoles(groupId, userId, roleIds, currentUserId);
        
        return result ? ApiResponse.success() : ApiResponse.error("批量分配角色失败");
    }

    /**
     * 取消用户角色
     */
    @DeleteMapping("/{id}")
    @RequiresPermission("production:role:unassign")
    public ApiResponse removeRole(@PathVariable Long id) {
        // 获取角色分配详情
        ProductionGroupRoleAssignmentDTO assignment = roleAssignmentService.getAssignmentById(id);
        if (assignment == null) {
            return ApiResponse.error("角色分配记录不存在");
        }
        
        // 检查当前用户是否是该组的组长
        Long currentUserId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        if (!isAdmin && !productionGroupService.isGroupLeader(currentUserId, assignment.getGroupId())) {
            return ApiResponse.error("只有生产组组长才能取消角色");
        }
        
        // 删除角色分配
        boolean result = roleAssignmentService.deleteAssignment(id);
        
        return result ? ApiResponse.success() : ApiResponse.error("取消角色失败");
    }

    /**
     * 检查用户在生产组中是否有指定角色
     */
    @GetMapping("/check")
    public ApiResponse checkUserRole(
            @RequestParam Long groupId,
            @RequestParam Long userId,
            @RequestParam Long roleId) {
        
        boolean hasRole = roleAssignmentService.checkUserHasRole(groupId, userId, roleId);
        
        Map<String, Boolean> result = new HashMap<>();
        result.put("hasRole", hasRole);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 检查用户对生产组的权限
     * 
     * @param groupId 生产组ID
     */
    private void checkGroupPermission(Long groupId) {
        Long currentUserId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        // 如果是管理员账号直接返回
        if (isAdmin) {
            return;
        }
        
        // 如果不是管理员，则检查是否是该组的组长或成员
        boolean isLeader = productionGroupService.isGroupLeader(currentUserId, groupId);
        boolean isMember = memberService.checkUserInGroup(groupId, currentUserId);
        
        if (!isLeader && !isMember) {
            throw new RuntimeException("没有权限访问该生产组");
        }
    }

    /**
     * 批量获取多个用户的角色ID列表
     */
    @PostMapping("/user/batch")
    @RequiresPermission("production:role:list")
    public ApiResponse batchGetUserRoles(@RequestBody Map<String, Object> params) {
        Long groupId = Long.valueOf(params.get("groupId").toString());
        List<Long> userIds = new ArrayList<>();
        
        // 解析用户ID列表
        Object userIdsObj = params.get("userIds");
        if (userIdsObj instanceof List) {
            List<?> rawList = (List<?>) userIdsObj;
            for (Object item : rawList) {
                if (item instanceof Number) {
                    userIds.add(((Number) item).longValue());
                } else if (item instanceof String) {
                    try {
                        userIds.add(Long.valueOf((String) item));
                    } catch (NumberFormatException e) {
                        log.warn("Invalid userId format in batch get: {}", item);
                    }
                }
            }
        }
        
        if (userIds.isEmpty()) {
            return ApiResponse.error("用户ID列表不能为空");
        }
        
        // 校验生产组是否存在
        if (!productionGroupService.existsById(groupId)) {
            return ApiResponse.error("生产组不存在");
        }
        
        // 检查当前用户是否有权限
        checkGroupPermission(groupId);
        
        // 批量获取用户角色ID列表
        Map<Long, List<Long>> userRolesMap = roleAssignmentService.batchGetUserRoleIds(groupId, userIds);
        
        return ApiResponse.success(userRolesMap);
    }
    
    /**
     * 批量获取多个用户的角色分配信息
     */
    @PostMapping("/assignments/batch")
    @RequiresPermission("production:role:list")
    public ApiResponse batchGetUserRoleAssignments(@RequestBody Map<String, Object> params) {
        Long groupId = Long.valueOf(params.get("groupId").toString());
        List<Long> userIds = new ArrayList<>();
        
        // 解析用户ID列表
        Object userIdsObj = params.get("userIds");
        if (userIdsObj instanceof List) {
            List<?> rawList = (List<?>) userIdsObj;
            for (Object item : rawList) {
                if (item instanceof Number) {
                    userIds.add(((Number) item).longValue());
                } else if (item instanceof String) {
                    try {
                        userIds.add(Long.valueOf((String) item));
                    } catch (NumberFormatException e) {
                        log.warn("Invalid userId format in batch get: {}", item);
                    }
                }
            }
        }
        
        if (userIds.isEmpty()) {
            return ApiResponse.error("用户ID列表不能为空");
        }
        
        // 校验生产组是否存在
        if (!productionGroupService.existsById(groupId)) {
            return ApiResponse.error("生产组不存在");
        }
        
        // 检查当前用户是否有权限
        checkGroupPermission(groupId);
        
        // 批量获取用户角色分配信息
        Map<Long, List<ProductionGroupRoleAssignmentDTO>> userAssignmentsMap = 
                roleAssignmentService.batchGetUserRoleAssignments(groupId, userIds);
        
        return ApiResponse.success(userAssignmentsMap);
    }
} 