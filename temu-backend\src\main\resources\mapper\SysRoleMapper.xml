<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.system.mapper.SysRoleMapper">

    <!-- 根据用户ID查询角色列表 -->
    <select id="selectRolesByUserId" parameterType="Long" resultType="com.xiao.temu.modules.system.entity.SysRole">
        select r.*
        from sys_role r
        inner join sys_user_role ur on r.role_id = ur.role_id
        where ur.user_id = #{userId}
        and r.status = '0'
        order by r.role_sort
    </select>
    
    <!-- 根据用户ID查询角色权限标识 -->
    <select id="selectRoleKeysByUserId" parameterType="Long" resultType="String">
        select r.role_key
        from sys_role r
        inner join sys_user_role ur on r.role_id = ur.role_id
        where ur.user_id = #{userId}
        and r.status = '0'
        order by r.role_sort
    </select>
    
    <!-- 检查角色名称是否唯一 -->
    <select id="checkRoleNameUnique" parameterType="String" resultType="int">
        select count(1)
        from sys_role
        where role_name = #{roleName}
    </select>
    
    <!-- 检查角色权限标识是否唯一 -->
    <select id="checkRoleKeyUnique" parameterType="String" resultType="int">
        select count(1)
        from sys_role
        where role_key = #{roleKey}
    </select>

    <!-- 更新角色状态 -->
    <update id="updateStatus">
        update sys_role
        set status = #{status},
            update_time = now()
        where role_id = #{roleId}
    </update>
    
    <!-- 根据权限标识查询角色ID列表 -->
    <select id="selectRoleIdsByPermission" parameterType="String" resultType="Long">
        select role_id
        from sys_role
        where role_key = #{permission}
        and status = '0'
    </select>
    
    <!-- 根据角色标识查询角色ID列表 -->
    <select id="selectRoleIdsByRoleKey" parameterType="String" resultType="Long">
        select role_id
        from sys_role
        where role_key = #{roleKey}
        and status = '0'
    </select>
    
    <!-- 根据角色权限标识查询角色ID -->
    <select id="selectRoleIdByRoleKey" parameterType="String" resultType="Long">
        select role_id
        from sys_role
        where role_key = #{roleKey}
        and status = '0'
        limit 1
    </select>
    
    <!-- 根据用户ID查询角色权限标识和角色名称的映射 -->
    <select id="selectRoleKeysAndNamesByUserId" parameterType="Long" resultType="java.util.Map">
        select r.role_key as roleKey, r.role_name as roleName
        from sys_role r
        inner join sys_user_role ur on r.role_id = ur.role_id
        where ur.user_id = #{userId}
        and r.status = '0'
        order by r.role_sort
    </select>
    
    <!-- 根据角色标识查询所有拥有此角色的用户ID列表 -->
    <select id="selectUserIdsByRoleKey" parameterType="String" resultType="Long">
        select ur.user_id
        from sys_role r
        inner join sys_user_role ur on r.role_id = ur.role_id
        where r.role_key = #{roleKey}
        and r.status = '0'
    </select>
    
</mapper> 