<template>
<div>
  <AppLayout>
    <!-- 搜索区域 -->
    <template #search>
      <SearchCard>
        <div class="search-form">
          <!-- 店铺 -->
          <div class="search-item">
            <div class="search-label">店铺</div>
            <el-select v-model="queryParams.shopIds" placeholder="请选择店铺" clearable @change="handleShopChange"
              @clear="handleClearShops" size="small" class="search-input" multiple collapse-tags filterable
              :filter-method="filterShops">
              <!-- 添加全选选项 -->
              <el-option key="select-all" label="全选" :value="-1" />
              <el-option v-for="shop in filteredShops" :key="shop.shopId" :label="shop.shopName" :value="shop.shopId" />
            </el-select>
          </div>

          <!-- 违规单号 -->
          <div class="search-item">
            <div class="search-label">违规单号</div>
            <el-input v-model="queryParams.punishSn" placeholder="请输入违规单号" clearable size="small" class="search-input" />
          </div>

          <!-- 备货单号 -->
          <div class="search-item">
            <div class="search-label">备货单号</div>
            <el-input v-model="queryParams.subPurchaseOrderSn" placeholder="请输入备货单号" clearable size="small" class="search-input" />
          </div>

          <!-- 商品ID/货号 -->
          <div class="search-item">
            <div class="search-label">商品ID/货号</div>
            <el-input v-model="idOrCodeInput" placeholder="输入SKU ID/货号" clearable size="small" class="search-input" />
          </div>
        </div>

        <!-- 第二行：违规时间 + 按钮 -->
        <div class="bottom-row">
          <!-- 违规时间 -->
          <div class="left-side">
            <div class="search-label">违规时间</div>
            <el-date-picker v-model="timeRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" value-format="YYYY-MM-DD" @change="handleTimeRangeChange" size="small"
              class="date-picker-input" />
          </div>

          <!-- 按钮组 -->
          <div class="right-side">
            <div class="search-buttons">
              <el-button type="primary" @click="handleQuery" size="small" class="action-button">
                <el-icon>
                  <Search />
                </el-icon> 搜索
              </el-button>
              <el-button @click="resetQuery" size="small" class="action-button">
                <el-icon>
                  <Refresh />
                </el-icon> 重置
              </el-button>
              <el-button type="success" @click="handleExport" size="small" class="action-button">
                <el-icon>
                  <Download />
                </el-icon> 导出Excel
              </el-button>
            </div>
          </div>
        </div>
      </SearchCard>
    </template>

    <!-- 表格内容区域 -->
    <template #table>
      <TableCard>
        <!-- 表格容器 -->
        <el-table v-loading="loading" :data="sortedDataList" border style="width: 100%" size="small"
          class="compact-table" @sort-change="handleTableSortChange" fit>
          <template #empty>
            <EmptyTips message="请选择店铺后点击搜索按钮查询数据（已默认设置最近7天）" />
          </template>
          <el-table-column type="index" label="序号" width="50" min-width="50" align="center" />
          <el-table-column prop="shopName" label="店铺信息" min-width="120" show-overflow-tooltip>
            <template #default="scope">
              <div class="shop-info">
                <div class="shop-name">{{ scope.row.shopName }}</div>
                <div v-if="scope.row.shopRemark" class="shop-remark">{{ scope.row.shopRemark }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="punishSn" label="违规编号" min-width="140" show-overflow-tooltip />
          <el-table-column prop="subPurchaseOrderSn" label="备货单号" min-width="140" show-overflow-tooltip />
          <el-table-column prop="productSkuId" label="商品SKU ID" min-width="120" show-overflow-tooltip />
          <el-table-column prop="extCode" label="货号" min-width="100" show-overflow-tooltip />
          <el-table-column prop="stockQuantity" label="备货件数" min-width="80" width="80" align="center" />
          <el-table-column prop="lackQuantity" label="缺货件数" min-width="80" width="80" align="center" />
          <el-table-column prop="unqualifiedQuantity" label="质量问题件数" min-width="80" width="80" align="center" />
          <!-- <el-table-column prop="punishTypeDesc" label="违规类型" min-width="120" show-overflow-tooltip /> -->
          <el-table-column prop="punishReasonDesc" label="违规类型" min-width="120" show-overflow-tooltip />
          <el-table-column prop="violationTime" label="违规时间" min-width="140" sortable="custom">
            <template #default="scope">
              {{ scope.row.violationTime }}
            </template>
          </el-table-column>
          <el-table-column label="违规金额(CNY)" min-width="120" sortable="custom" prop="punishAmount" align="right">
            <template #default="scope">
              <span class="amount">¥{{ formatAmount(scope.row.punishAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="具体原因" min-width="200">
            <template #default="scope">
              <div v-if="scope.row.flawNameDesc" class="flaw-desc-container">
                <div class="flaw-desc-content">
                  {{ scope.row.flawNameDesc }}
                </div>
              </div>
              <span v-else class="no-data">暂无质检信息</span>
            </template>
          </el-table-column>
          <el-table-column label="疵点图片" min-width="150">
            <template #default="scope">
              <div v-if="scope.row.attachmentList && scope.row.attachmentList.length > 0" class="attachment-preview">
                <el-image 
                  v-for="(url, index) in scope.row.attachmentList" 
                  :key="index"
                  :src="url"
                  :preview-src-list="scope.row.attachmentList"
                  :initial-index="0"
                  preview-teleported
                  class="attachment-image"
                  fit="cover"
                />
              </div>
              <span v-else class="no-data">无图片</span>
            </template>
          </el-table-column>
        </el-table>
      </TableCard>
    </template>

    <!-- 固定在底部的分页区域 -->
    <template #pagination>
      <PaginationBar :current-page="Number(queryParams.pageNum)" :page-size="Number(queryParams.pageSize)" :total="total"
        @update:current-page="(val) => queryParams.pageNum = Number(val)"
        @update:page-size="(val) => queryParams.pageSize = Number(val)" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </template>

    <!-- 右侧边栏区域 -->
    <template #sidebar>
      <div class="export-sidebar">
        <div class="export-panel-header">
          <h3>导出任务</h3>
        </div>
        <div class="export-panel-content">
          <!-- 显示最近的导出任务记录 -->
          <div class="export-history">
            <div class="export-task-list">
              <div v-for="task in exportTaskStore.recentTasks" 
                  :key="task.id" 
                  class="export-task-item">
                <div class="task-info">
                  <div class="task-name">{{ task.name }}</div>
                  <div class="task-meta">
                    <span class="task-time">{{ formatTaskTime(task.createdAt) }}</span>
                    <span :class="['task-status', `status-${task.status}`]">{{ getTaskStatusText(task.status) }}</span>
                  </div>
                </div>
                
                <div class="task-actions">
                  <template v-if="task.status === 'completed'">
                    <el-button type="primary" size="small" @click="handleRedownload(task)">
                      下载文件
                    </el-button>
                  </template>
                </div>
              </div>
            </div>
            
            <div v-if="exportTaskStore.recentTasks.filter(t => t.status === 'completed').length > 0" class="export-task-actions">
              <el-button type="primary" link size="small" @click="exportTaskStore.clearCompletedTasks">
                清除已完成任务
              </el-button>
            </div>
          </div>
          
          <!-- 如果没有任何导出任务，显示空提示 -->
          <div v-if="exportTaskStore.recentTasks.length === 0" class="export-empty-tip">
            暂无导出任务
          </div>
        </div>
      </div>
    </template>
  </AppLayout>
  
  <!-- 导出对话框 -->
  <ExportDialog
    v-model:visible="exportDialogVisible"
    data-type="violation"
    :default-file-name="exportFileName"
    :current-page-data="dataList"
    :total="total"
    :fetch-data-fn="fetchExportData"
    :query-params="queryParams"
    :use-backend-export="true"
  />
</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { Search, Refresh, Download, Picture } from '@element-plus/icons-vue'
import type { ViolationInspectionQueryParams, ViolationInspectionItem } from '@/types/local/violationInspection'
import { getViolationInspectionList, createViolationExportTask, getExportProgress, getExportDownloadUrl } from '@/api/local/violationInspection'
import { fetchUserAccessibleShops } from '@/utils/shop-helper'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { DateModelType } from 'element-plus'
import { getToken } from '@/utils/auth'
import { useExportTaskStore } from '@/store'

// 导入Temu组件
import {
  AppLayout,
  SearchCard,
  TableCard,
  PaginationBar,
  EmptyTips,
  ExportDialog
} from '@/components/temu'

// 店铺列表
const shops = ref<{ shopId: number; shopName: string }[]>([])

// 添加全选状态跟踪变量
const isAllSelected = ref(false)

// 添加过滤后的店铺列表
const filteredShops = ref<{ shopId: number; shopName: string }[]>([])
// 用于存储过滤搜索关键词
const filterKeyword = ref('')

// 查询参数
const queryParams = reactive<ViolationInspectionQueryParams>({
  pageNum: 1,
  pageSize: 10,
  shopIds: [],
  punishSn: '',
  subPurchaseOrderSn: '',
  violationTimeStart: '',
  violationTimeEnd: '',
})

// SKU ID或货号输入框
const idOrCodeInput = ref('')

// 日期范围选择器
const timeRange = ref<[string, string] | null>(null)

// 列表数据
const dataList = ref<ViolationInspectionItem[]>([])
const total = ref(0)
const loading = ref(false)

// 排序
const sortOption = ref('violationTime')
const sortDirection = ref<'ascending' | 'descending'>('descending')

// 格式化金额
const formatAmount = (amount: number) => {
  if (!amount && amount !== 0) return '-'
  // 将分转换为元（除以100）
  const yuanAmount = amount / 100
  return yuanAmount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 排序后的数据
const sortedDataList = computed(() => {
  if (!dataList.value.length) return []
  
  const list = [...dataList.value]
  
  list.sort((a, b) => {
    let valueA, valueB
    
    switch (sortOption.value) {
      case 'violationTime':
        valueA = new Date(a.violationTime).getTime()
        valueB = new Date(b.violationTime).getTime()
        break
      case 'shopName':
        valueA = a.shopName || ''
        valueB = b.shopName || ''
        break
      case 'punishAmount':
        valueA = a.punishAmount || 0
        valueB = b.punishAmount || 0
        break
      case 'stockQuantity':
        valueA = a.stockQuantity || 0
        valueB = b.stockQuantity || 0
        break
      case 'lackQuantity':
        valueA = a.lackQuantity || 0
        valueB = b.lackQuantity || 0
        break
      case 'unqualifiedQuantity':
        valueA = a.unqualifiedQuantity || 0
        valueB = b.unqualifiedQuantity || 0
        break
      default:
        valueA = a[sortOption.value as keyof ViolationInspectionItem]
        valueB = b[sortOption.value as keyof ViolationInspectionItem]
    }
    
    const result = sortDirection.value === 'ascending' 
      ? (valueA > valueB ? 1 : -1)
      : (valueA < valueB ? 1 : -1)
      
    return result
  })
  
  return list
})

// 过滤店铺方法
const filterShops = (query: string) => {
  filterKeyword.value = query
  if (query) {
    filteredShops.value = shops.value.filter(shop => 
      shop.shopName.toLowerCase().includes(query.toLowerCase())
    )
    
    // 当过滤条件发生变化时，重置全选状态
    isAllSelected.value = false
    
    // 如果当前已选中店铺，保留它们（如果它们在过滤结果中）
    if (queryParams.shopIds && queryParams.shopIds.length > 0) {
      // 保留那些在过滤结果中的店铺ID
      queryParams.shopIds = queryParams.shopIds.filter(id => 
        filteredShops.value.some(shop => shop.shopId === id)
      )
    }
  } else {
    filteredShops.value = [...shops.value]
    
    // 检查是否全部店铺都被选中
    if (queryParams.shopIds && shops.value.length > 0) {
      const validShopIds = queryParams.shopIds.filter(id => id !== null) as number[]
      isAllSelected.value = validShopIds.length === shops.value.length &&
        shops.value.every(shop => shop.shopId !== null && validShopIds.includes(shop.shopId))
    }
  }
}

// 处理清除所有店铺选择
const handleClearShops = () => {
  // 重置全选状态
  isAllSelected.value = false
  queryParams.shopIds = []
}

// 获取店铺列表
const getShops = async () => {
  try {
    shops.value = await fetchUserAccessibleShops()
    // 初始化过滤后的店铺列表
    filteredShops.value = [...shops.value]
    
    if (shops.value.length === 0) {
      console.warn('未获取到店铺数据');
    }
  } catch (error) {
    console.error('获取店铺列表失败', error);
    ElMessage.error('获取店铺列表失败，请稍后重试');
  }
}

// 查询数据
const getList = async () => {
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请至少选择一个店铺')
    return
  }
  
  // 处理SKU ID或货号
  if (idOrCodeInput.value) {
    if (/^\d+$/.test(idOrCodeInput.value)) {
      queryParams.productSkuId = parseInt(idOrCodeInput.value)
      queryParams.extCode = undefined
    } else {
      queryParams.extCode = idOrCodeInput.value
      queryParams.productSkuId = undefined
    }
  } else {
    queryParams.productSkuId = undefined
    queryParams.extCode = undefined
  }
  
  loading.value = true
  try {
    const res = await getViolationInspectionList(queryParams)
    // 处理后端可能使用rows或items返回数据
    dataList.value = res.rows || res.items || []
    total.value = res.total || 0
  } catch (error) {
    console.error('查询数据失败', error)
  } finally {
    loading.value = false
  }
}

// 处理店铺变更
const handleShopChange = (shopIds: number[]) => {
  // 检查是否点击了全选选项
  if (shopIds.includes(-1)) {
    // 如果当前不是全选状态，则进行全选
    if (!isAllSelected.value) {
      // 标记为全选状态
      isAllSelected.value = true
      
      // 全选所有店铺（过滤后的）
      let allShopIds: number[] = []
      
      // 如果有搜索关键词，只选择过滤后的店铺
      if (filterKeyword.value) {
        // 过滤掉可能的null值
        allShopIds = filteredShops.value
          .filter(shop => shop.shopId !== null)
          .map(shop => shop.shopId as number)
      } else {
        // 否则选择所有店铺
        allShopIds = shops.value
          .filter(shop => shop.shopId !== null)
          .map(shop => shop.shopId as number)
      }
      
      // 更新选中的店铺，并移除全选选项值(-1)
      queryParams.shopIds = allShopIds
      
      // 避免-1被包含在选中项中
      setTimeout(() => {
        if (queryParams.shopIds && queryParams.shopIds.includes(-1)) {
          const index = queryParams.shopIds.indexOf(-1)
          if (index > -1) {
            queryParams.shopIds.splice(index, 1)
          }
        }
      }, 0)
    } else {
      // 如果当前已经是全选状态，则取消全选
      isAllSelected.value = false
      queryParams.shopIds = []
    }
  } else {
    // 如果选择了具体的店铺且数量与总店铺数相同，标记为全选状态
    const totalShops = filterKeyword.value ? 
      filteredShops.value.filter(shop => shop.shopId !== null).length : 
      shops.value.filter(shop => shop.shopId !== null).length
    isAllSelected.value = shopIds.length > 0 && shopIds.length === totalShops
  }
  
  // 如果清空了店铺选择，重置查询结果
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    dataList.value = []
    total.value = 0
  }
}

// 处理日期范围变更
const handleTimeRangeChange = (val: DateModelType) => {
  if (val && Array.isArray(val) && val.length === 2) {
    queryParams.violationTimeStart = val[0] + ' 00:00:00'
    queryParams.violationTimeEnd = val[1] + ' 23:59:59'
  } else {
    queryParams.violationTimeStart = ''
    queryParams.violationTimeEnd = ''
  }
}

// 处理排序变更
const handleSortChange = () => {
  getList()
}

// 处理表格排序变更
const handleTableSortChange = (column: { prop: string; order: string | null }) => {
  if (column.prop && column.order) {
    sortOption.value = column.prop
    sortDirection.value = column.order as 'ascending' | 'descending'
  }
}

// 执行查询
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置查询条件
const resetQuery = () => {
  timeRange.value = null
  idOrCodeInput.value = ''
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    punishSn: '',
    subPurchaseOrderSn: '',
    violationTimeStart: '',
    violationTimeEnd: '',
    productSkuId: undefined,
    extCode: undefined,
  })
  // 重置全选状态
  isAllSelected.value = false
  // 保留已选店铺
}

// 导出状态管理
const exportDialogVisible = ref(false)
const exportTaskStore = useExportTaskStore()

// 添加导出文件名的ref变量
const exportFileName = ref('违规检查数据')

// 格式化任务时间
const formatTaskTime = (date: Date) => {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  
  if (diffMins < 1) {
    return '刚刚'
  } else if (diffMins < 60) {
    return `${diffMins} 分钟前`
  } else if (diffMins < 24 * 60) {
    const hours = Math.floor(diffMins / 60)
    return `${hours} 小时前`
  } else {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    
    return `${year}-${month}-${day} ${hour}:${minute}`
  }
}

// 获取任务状态文本
const getTaskStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return '准备中'
    case 'processing':
      return '进行中'
    case 'completed':
      return '已完成'
    case 'failed':
      return '失败'
    default:
      return status
  }
}

// 处理重新下载
const handleRedownload = async (task: any) => {
  try {
    // 从任务数据中获取服务器任务ID
    const serverTaskId = task.serverTaskId
    
    if (!serverTaskId) {
      ElMessage.warning('无法找到下载信息，请重新导出')
      return
    }
    
    // 下载文件
    downloadExportFile(serverTaskId)
  } catch (error: any) {
    console.error('重新下载失败:', error)
    ElMessage.error('重新下载失败: ' + (error.message || '未知错误'))
  }
}

// 导出功能
const handleExport = () => {
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请至少选择一个店铺')
    return
  }
  
  if (dataList.value.length === 0 && total.value === 0) {
    ElMessage.warning('暂无数据可导出，请先执行查询')
    return
  }
  
  // 生成默认文件名：店铺备注 + 日期时间
  let fileName = '违规检查数据';
  
  // 获取选中店铺的备注
  if (shops.value && shops.value.length > 0 && queryParams.shopIds && queryParams.shopIds.length > 0) {
    // 查找选中店铺的备注
    const selectedShops = shops.value.filter(shop => 
      shop.shopId !== null && queryParams.shopIds!.includes(shop.shopId)
    );
    if (selectedShops.length > 0) {
      // 提取店铺名称作为文件名（因为这里的shop对象没有remark属性）
      const shopNames = selectedShops.slice(0, 3).map(shop => shop.shopName);
      fileName = shopNames.join('-');
      
      // 如果选择的店铺超过3个，添加省略标记
      if (selectedShops.length > 3) {
        fileName += '等';
      }
    }
  }
  
  // 添加日期时间后缀
  const now = new Date();
  const dateStr = now.getFullYear() + 
    (now.getMonth() + 1).toString().padStart(2, '0') + 
    now.getDate().toString().padStart(2, '0');
  const timeStr = now.getHours().toString().padStart(2, '0') + 
    now.getMinutes().toString().padStart(2, '0');
  
  fileName += '_' + dateStr + timeStr;
  
  console.log('导出文件名:', fileName); // 添加调试输出
  
  // 更新文件名并打开导出对话框
  exportFileName.value = fileName;
  exportDialogVisible.value = true;
}

// 封装获取违规检查数据的函数供导出组件使用
const fetchExportData = async (params: any) => {
  try {
    // 构造导出请求参数
    const exportParams = {
      ...params,
      queryParams: {
        ...queryParams,
        ...(params.queryParams || {})
      }
    }
    
    // 创建导出任务
    const response = await createViolationExportTask(exportParams)
    
    // 检查响应
    if (!response || !response.taskId) {
      if (params.taskId) {
        exportTaskStore.failTask(params.taskId, '创建导出任务失败')
      }
      return { success: false }
    }
    
    // 获取服务器任务ID
    const serverTaskId = response.taskId
    
    // 更新任务信息，保存服务器端taskId用于后续下载
    if (params.taskId) {
      const task = exportTaskStore.tasks.find(t => t.id === params.taskId)
      if (task) {
        task.serverTaskId = serverTaskId
      }
    }
    
    // 开始轮询任务进度
    startExportProgressPolling(serverTaskId, params.taskId)
    
    return { success: true, data: { taskId: serverTaskId } }
  } catch (error: any) {
    console.error('导出失败:', error)
    if (params.taskId) {
      exportTaskStore.failTask(params.taskId, error.message || '导出请求失败')
    }
    return { success: false }
  }
}

// 轮询导出进度
const startExportProgressPolling = (serverTaskId: string, clientTaskId?: string) => {
  if (!clientTaskId) return
  
  let pollingTimer: number | null = null
  let failedAttempts = 0
  const MAX_FAILED_ATTEMPTS = 3
  
  // 创建轮询检查进度的函数
  const checkExportProgress = () => {
    getExportProgress(serverTaskId)
      .then(progress => {
        // 重置失败计数
        failedAttempts = 0
        
        // 更新任务进度
        if (clientTaskId) {
          exportTaskStore.updateTaskProgress(clientTaskId, progress.progress)
        }
        
        // 根据状态处理
        if (progress.status === 'completed') {
          // 下载文件
          downloadExportFile(serverTaskId, clientTaskId)
        } else if (progress.status === 'failed') {
          if (clientTaskId) {
            exportTaskStore.failTask(clientTaskId, progress.message || '导出失败')
          }
          if (pollingTimer) {
            clearTimeout(pollingTimer)
            pollingTimer = null
          }
        } else {
          // 继续轮询
          pollingTimer = window.setTimeout(() => {
            checkExportProgress()
          }, 1000) as unknown as number
        }
      })
      .catch(err => {
        failedAttempts++
        console.error('检查导出进度失败:', err)
        
        if (failedAttempts >= MAX_FAILED_ATTEMPTS) {
          if (clientTaskId) {
            exportTaskStore.failTask(clientTaskId, '无法获取导出进度')
          }
          if (pollingTimer) {
            clearTimeout(pollingTimer)
            pollingTimer = null
          }
        } else {
          // 失败后等待稍长时间再重试
          pollingTimer = window.setTimeout(() => {
            checkExportProgress()
          }, 2000) as unknown as number
        }
      })
  }
  
  // 开始检查进度
  checkExportProgress()
}

// 下载导出的文件
const downloadExportFile = (serverTaskId: string, clientTaskId?: string) => {
  // 使用fetch API下载文件并携带token
  const token = getToken()
  const downloadUrl = getExportDownloadUrl(serverTaskId)
  
  fetch(downloadUrl, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`)
    }
    return response.blob()
  })
  .then(blob => {
    // 标记任务完成（如果有客户端任务ID）
    if (clientTaskId) {
      exportTaskStore.completeTask(clientTaskId)
    }
    
    // 获取文件名 - 从Content-Disposition获取或使用默认名称
    let filename = '违规检查数据.xlsx'
    
    // 创建下载链接并触发下载
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.style.display = 'none'
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    
    // 清理资源
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
    
    ElMessage.success('文件下载已开始')
  })
  .catch(error => {
    if (clientTaskId) {
      exportTaskStore.failTask(clientTaskId, '下载文件失败: ' + error.message)
    }
    ElMessage.error('下载文件失败: ' + error.message)
  })
}

// 页面大小变更
const handleSizeChange = (val: number) => {
  queryParams.pageSize = val
  getList()
}

// 页码变更
const handleCurrentChange = (val: number) => {
  queryParams.pageNum = val
  getList()
}

// 初始化
onMounted(async () => {
  await getShops()
  
  // 移除默认时间设置
})
</script>

<style scoped>
/* 搜索表单样式 - 四列布局 */
.search-form {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 15px;
  align-items: start;
  margin-bottom: 15px;
}

.search-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.search-label {
  font-size: 13px;
  color: #606266;
}

.search-input {
  width: 100%;
}

/* 底部行样式 */
.bottom-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: 5px;
}

/* 左侧时间选择器 */
.left-side {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 380px;
}

.date-picker-input {
  width: 100%;
}

/* 右侧按钮组 */
.right-side {
  display: flex;
  justify-content: flex-end;
}

/* 按钮组 */
.search-buttons {
  display: flex;
  gap: 8px;
}

.shop-info {
  display: flex;
  flex-direction: column;
}

.shop-name {
  font-weight: bold;
}

.shop-remark {
  font-size: 12px;
  color: #909399;
}

.amount {
  font-weight: bold;
}

.inspection-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.flaw-desc-container {
  max-height: 100px;
  overflow-y: auto;
  padding: 5px 0;
}

.flaw-desc-content {
  line-height: 1.5;
  white-space: normal;
  word-break: break-all;
  font-size: 13px;
}

.label {
  color: #909399;
  margin-right: 5px;
}

.attachment-preview {
  display: flex;
  gap: 5px;
  margin-top: 5px;
}

.attachment-image {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
}

.no-data {
  color: #909399;
  font-size: 12px;
}

.detail-content {
  padding: 10px;
}

.detail-section {
  margin-top: 20px;
}

.detail-section-title {
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  border-left: 3px solid #409EFF;
}

.attachment-gallery {
  margin-top: 15px;
}

.attachment-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
}

.attachment-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.attachment-item {
  width: 120px;
  height: 120px;
}

.detail-image {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.image-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
}

.image-preview-dialog :deep(.el-dialog__body) {
  padding: 0;
  background-color: #fff;
}

.image-preview-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  min-height: 400px;
}

.image-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 20px;
}

.preview-image {
  max-width: 100%;
  max-height: 500px;
  object-fit: contain;
}

.image-navigation {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.3);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s;
}

.image-navigation:hover {
  background-color: rgba(0, 0, 0, 0.5);
}

.image-navigation.left {
  left: 20px;
}

.image-navigation.right {
  right: 20px;
}

:deep(.el-image-viewer__wrapper) {
  background-color: rgba(0, 0, 0, 0.9);
}

:deep(.el-image-viewer__img) {
  background-color: white;
  padding: 20px;
  border-radius: 4px;
  max-height: 80vh;
}

:deep(.el-image-viewer__actions) {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.5);
}

:deep(.el-image-viewer__btn) {
  color: white;
}

:deep(.el-image-viewer__close) {
  color: white;
}

:deep(.el-image-viewer__prev), :deep(.el-image-viewer__next) {
  color: white;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 10px;
  width: 44px;
  height: 44px;
}

.export-progress-container {
  padding: 20px 0;
  text-align: center;
}

.progress-text {
  margin-bottom: 15px;
  color: #606266;
}

.export-options {
  padding: 10px 0;
}

.export-option {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.export-option label {
  margin-left: 5px;
}

.custom-option {
  display: flex;
  align-items: center;
  gap: 5px;
}

.export-sidebar {
  background-color: #fff;
  border-radius: 4px;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.export-panel-header {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
}

.export-panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.export-panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.export-empty-tip {
  color: #909399;
  text-align: center;
  padding: 30px 0;
}

.export-task-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.export-task-item {
  padding: 12px;
  border-radius: 4px;
  background-color: #f8f8f8;
  border: 1px solid #ebeef5;
}

.task-info {
  margin-bottom: 10px;
}

.task-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.task-status {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
}

.status-completed {
  background-color: #f0f9eb;
  color: #67c23a;
}

.status-processing, .status-pending {
  background-color: #ecf5ff;
  color: #409eff;
}

.status-failed {
  background-color: #fef0f0;
  color: #f56c6c;
}

.task-actions {
  display: flex;
  justify-content: flex-end;
}

.export-task-actions {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .search-form {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .bottom-row {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .right-side {
    width: 100%;
    justify-content: flex-start;
    margin-top: 10px;
  }
  
  .left-side {
    width: 100%;
  }
  
  .search-buttons {
    flex-wrap: wrap;
  }
}
</style> 