{"appid": "wx2e095e25019548e0", "projectname": "temu_wx", "compileType": "miniprogram", "libVersion": "3.7.4", "packOptions": {"ignore": [], "include": []}, "condition": {}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "compileHotReLoad": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}