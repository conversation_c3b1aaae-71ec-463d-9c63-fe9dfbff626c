import request from '@/utils/request'
import type { Shop, ShopQuery } from '@/types/shop'

/**
 * 获取所有店铺列表（不分页）
 * @returns 所有店铺列表
 */
export function listShops() {
  return request({
    url: '/operation/shop/listAll',
    method: 'get'
  })
}

/**
 * 获取店铺列表（分页）
 * @param query 查询参数
 * @returns 店铺列表数据
 */
export function getShopList(query: ShopQuery) {
  return request({
    url: '/operation/shop/list',
    method: 'get',
    params: query
  })
}

/**
 * 获取店铺详情
 * @param shopId 店铺ID
 * @returns 店铺详情数据
 */
export function getShop(shopId: number) {
  return request({
    url: `/operation/shop/${shopId}`,
    method: 'get'
  })
}

/**
 * 新增店铺
 * @param data 店铺数据
 * @returns 操作结果
 */
export function addShop(data: Shop) {
  return request({
    url: '/operation/shop',
    method: 'post',
    data
  })
}

/**
 * 修改店铺
 * @param data 店铺数据
 * @returns 操作结果
 */
export function updateShop(data: Shop) {
  return request({
    url: '/operation/shop',
    method: 'put',
    data
  })
}

/**
 * 删除店铺
 * @param shopId 店铺ID
 * @returns 操作结果
 */
export function deleteShop(shopId: number) {
  return request({
    url: `/operation/shop/${shopId}`,
    method: 'delete'
  })
}

/**
 * 获取当前用户可操作的店铺列表
 * @returns 店铺列表数据
 */
export function getUserShops() {
  return request({
    url: '/operation/shop/user/shops',
    method: 'get'
  })
}

/**
 * 获取运营组下的店铺列表
 * @param groupId 运营组ID
 * @returns 店铺列表数据
 */
export function getGroupShops(groupId: number) {
  return request({
    url: `/operation/shop/group/${groupId}`,
    method: 'get'
  })
}

/**
 * 导出店铺数据
 * @param data 导出参数
 * @returns 导出的Excel文件
 */
export function exportShops(data: any) {
  return request({
    url: '/operation/shop/export',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

/**
 * 获取店铺导入模板
 * @returns 导入模板Excel文件
 */
export function getShopImportTemplate() {
  return request({
    url: '/operation/shop/importTemplate',
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 导入店铺数据
 * @param file 导入的Excel文件
 * @returns 导入结果
 */
export function importShops(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/operation/shop/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 批量删除店铺
 * @param shopIds 店铺ID数组
 * @returns 操作结果
 */
export function deleteShops(shopIds: number[]) {
  return request({
    url: '/operation/shop/batchDelete',
    method: 'delete',
    data: shopIds
  })
}

/**
 * 获取用户可访问的店铺列表
 * @returns Promise 返回店铺列表数据
 */
export function getUserAccessibleShops() {
  return request({
    url: '/operation/shop/accessible/shops',
    method: 'get'
  })
} 