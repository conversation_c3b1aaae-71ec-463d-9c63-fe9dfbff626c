<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.purchaseorderv.mapper.ProductionProgressLogMapper">

    <!-- 查询结果映射 -->
    <resultMap id="BaseResultMap" type="com.xiao.temu.modules.purchaseorderv.entity.ProductionProgressLog">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="sub_purchase_order_sn" property="subPurchaseOrderSn"/>
        <result column="progress_type" property="progressType"/>
        <result column="operation_type" property="operationType"/>
        <result column="operator_id" property="operatorId"/>
        <result column="operator_name" property="operatorName"/>
        <result column="operation_time" property="operationTime"/>
        <result column="remarks" property="remarks"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

</mapper> 