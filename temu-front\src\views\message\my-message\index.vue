<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>我的消息</span>
          <div class="message-actions">
            <el-button type="primary" @click="markAllAsRead" :disabled="selectedMessages.length === 0">
              标记已读
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
        <el-form-item label="消息类型" prop="messageType">
          <el-select v-model="queryParams.messageType" placeholder="全部类型" clearable style="width: 120px">
            <el-option label="系统消息" value="1" />
            <el-option label="任务提醒" value="2" />
            <el-option label="店铺消息" value="3" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="已读状态" prop="readStatus">
          <el-select v-model="queryParams.readStatus" placeholder="全部状态" clearable style="width: 120px">
            <el-option label="未读" value="0" />
            <el-option label="已读" value="1" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="重要程度" prop="importance">
          <el-select v-model="queryParams.importance" placeholder="全部重要程度" clearable style="width: 120px">
            <el-option label="普通" value="1" />
            <el-option label="重要" value="2" />
            <el-option label="紧急" value="3" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="时间范围" prop="timeRange">
          <el-date-picker
            v-model="timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleTimeRangeChange"
          />
        </el-form-item>
        
        <el-form-item label="关键词" prop="keyword">
          <el-input
            v-model="queryParams.keyword"
            placeholder="标题/内容关键词"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon> 搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon> 重置
          </el-button>
        </el-form-item>
      </el-form>
      
      <!-- 消息列表 -->
      <el-table
        v-loading="loading"
        :data="messageList"
        @selection-change="handleSelectionChange"
        border
        style="width: 100%"
      >
        <el-table-column type="selection" width="50" />
        
        <el-table-column label="状态" width="60" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.readStatus === '0'" type="danger" effect="plain">未读</el-tag>
            <el-tag v-else type="info" effect="plain">已读</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="重要度" width="70" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.importance === '3'" type="danger">紧急</el-tag>
            <el-tag v-else-if="scope.row.importance === '2'" type="warning">重要</el-tag>
            <el-tag v-else type="info">普通</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="content" label="内容" min-width="300" show-overflow-tooltip>
          <template #default="scope">
            {{ stripHtmlTags(scope.row.content) }}
          </template>
        </el-table-column>
        
        <el-table-column label="类型" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.messageType === '1'" type="primary">系统</el-tag>
            <el-tag v-else-if="scope.row.messageType === '2'" type="success">任务</el-tag>
            <el-tag v-else-if="scope.row.messageType === '3'" type="warning">店铺</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="发送时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="180" align="center">
          <template #default="scope">
            <el-button
              v-if="scope.row.readStatus === '0'"
              type="primary"
              link
              @click="markAsRead(scope.row)"
            >标为已读</el-button>
            <el-button
              type="primary"
              link
              @click="viewMessage(scope.row)"
            >查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      
      <!-- 消息详情对话框 -->
      <el-dialog
        v-model="dialogVisible"
        title="消息详情"
        width="60%"
        :max-width="1200"
        append-to-body
      >
        <div class="message-detail" v-if="currentMessage">
          <div class="message-detail-header">
            <h2>{{ currentMessage.title }}</h2>
            <div class="message-tags">
              <el-tag v-if="currentMessage.importance === '3'" type="danger">紧急</el-tag>
              <el-tag v-else-if="currentMessage.importance === '2'" type="warning">重要</el-tag>
              
              <el-tag v-if="currentMessage.messageType === '1'" type="primary" style="margin-left: 8px">系统消息</el-tag>
              <el-tag v-else-if="currentMessage.messageType === '2'" type="success" style="margin-left: 8px">任务提醒</el-tag>
              <el-tag v-else-if="currentMessage.messageType === '3'" type="warning" style="margin-left: 8px">店铺消息</el-tag>
            </div>
          </div>
          
          <div class="message-meta">
            <div><i class="el-icon-time"></i> 发送时间: {{ formatTime(currentMessage.createTime) }}</div>
            <div v-if="currentMessage.readStatus === '1'">
              <i class="el-icon-view"></i> 已读时间: {{ currentMessage.readTime ? formatTime(currentMessage.readTime) : '-' }}
            </div>
          </div>
          
          <div class="message-content-card">
            <div class="message-content" v-html="formatMessageContent(currentMessage.content)">
            </div>
            
            <div v-if="currentMessage.shopId" class="message-shop">
              相关店铺: {{ currentMessage.shopName || `ID: ${currentMessage.shopId}` }}
            </div>
          </div>
        </div>
        
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">关闭</el-button>
            <el-button 
              v-if="currentMessage && currentMessage.readStatus === '0'" 
              type="primary" 
              @click="markAsRead(currentMessage)"
            >
              标为已读
            </el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { Message, MessageQueryDTO } from '@/types/message'
import { useMessageStore } from '@/store'
import { formatTime, formatDateTime } from '@/utils/format'  // 导入新的日期时间格式化函数

// 消息store
const messageStore = useMessageStore()

// 加载状态
const loading = ref(false)

// 消息列表和总数
const messageList = ref<Message[]>([])
const total = ref(0)

// 选中的消息
const selectedMessages = ref<Message[]>([])

// 对话框显示状态和当前消息
const dialogVisible = ref(false)
const currentMessage = ref<Message>()

// 时间范围
const timeRange = ref<string[]>([])

// 查询参数
const queryParams = reactive<MessageQueryDTO>({
  pageNum: 1,
  pageSize: 10,
  messageType: undefined,
  readStatus: undefined,
  importance: undefined,
  startTime: undefined,
  endTime: undefined,
  keyword: undefined
})

// HTML标签去除函数
const stripHtmlTags = (html: string): string => {
  if (!html) return '';
  // 创建临时元素
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  // 获取文本内容
  const textContent = tempDiv.textContent || tempDiv.innerText || '';
  // 返回删减后的文本（最多显示100个字符）
  return textContent.length > 100 ? textContent.substring(0, 100) + '...' : textContent;
};

// 加载消息列表
const loadMessageList = async () => {
  loading.value = true
  try {
    const result = await messageStore.getMessageList(queryParams)
    if (result && result.data) {
      // 后端返回的是records数组，需要映射到messageList
      messageList.value = result.data.records || []
      total.value = result.data.total || 0
    } else {
      messageList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('加载消息列表失败', error)
    messageList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理查询
const handleQuery = () => {
  queryParams.pageNum = 1
  loadMessageList()
}

// 重置查询条件
const resetQuery = () => {
  timeRange.value = []
  queryParams.messageType = undefined
  queryParams.readStatus = undefined
  queryParams.importance = undefined
  queryParams.startTime = undefined
  queryParams.endTime = undefined
  queryParams.keyword = undefined
  queryParams.pageNum = 1
  loadMessageList()
}

// 处理时间范围变化
const handleTimeRangeChange = (val: string[]) => {
  if (val && val.length > 0) {
    queryParams.startTime = formatDateTime(val[0])
  } else {
    queryParams.startTime = undefined
  }
  
  if (val && val.length > 1) {
    queryParams.endTime = formatDateTime(val[1])
  } else {
    queryParams.endTime = undefined
  }
}

// 处理分页变化
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  loadMessageList()
}

const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page
  loadMessageList()
}

// 处理选择变化
const handleSelectionChange = (selection: Message[]) => {
  selectedMessages.value = selection
}

// 查看消息详情
const viewMessage = (message: Message) => {
  currentMessage.value = message
  dialogVisible.value = true
  
  // 如果是未读消息，标记为已读
  if (message.readStatus === '0') {
    markAsRead(message)
  }
}

// 标记消息为已读
const markAsRead = async (message: Message) => {
  try {
    await messageStore.markMessageRead(Number(message.messageId))
    // 刷新列表
    loadMessageList()
    
    // 如果是当前消息，更新状态
    if (currentMessage.value && currentMessage.value.messageId === message.messageId) {
      currentMessage.value.readStatus = '1'
      currentMessage.value.readTime = new Date().toISOString()
    }
    
    ElMessage.success('已标记为已读')
  } catch (error) {
    console.error('标记已读失败', error)
    ElMessage.error('标记已读失败')
  }
}

// 批量标记为已读
const markAllAsRead = async () => {
  if (selectedMessages.value.length === 0) return
  
  try {
    // 获取所有选中的未读消息ID
    const unreadIds = selectedMessages.value
      .filter(msg => msg.readStatus === '0')
      .map(msg => Number(msg.messageId))
    
    if (unreadIds.length === 0) {
      ElMessage.info('没有选中未读消息')
      return
    }
    
    await messageStore.batchMarkRead(unreadIds)
    loadMessageList()
    ElMessage.success(`已将 ${unreadIds.length} 条消息标记为已读`)
  } catch (error) {
    console.error('批量标记已读失败', error)
    ElMessage.error('批量标记已读失败')
  }
}

// 时间戳转换函数 - 将消息内容中的时间戳转换为正常日期格式
const formatContentTimestamps = (content: string): string => {
  if (!content) return '';
  
  // 匹配内容中的时间戳（通常是13位数字）
  const timestampRegex = /(：|:)(\s*)(\d{13})/g;
  
  // 处理时间戳
  const processedContent = content.replace(timestampRegex, (match, prefix, space, timestamp) => {
    try {
      // 将时间戳转换为日期对象
      const date = new Date(parseInt(timestamp));
      if (isNaN(date.getTime())) {
        return match; // 如果转换失败则保持原样
      }
      
      // 直接格式化为 YYYY-MM-DD HH:mm:ss
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      
      const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      return `${prefix}${space}${formattedDate}`;
    } catch (error) {
      console.error('时间戳转换失败', error);
      return match; // 出错时保持原样
    }
  });
  
  return processedContent;
};

// 格式化消息内容 - 处理HTML并优化展示
const formatMessageContent = (content: string): string => {
  if (!content) return '';
  
  // 先处理时间戳
  let processedContent = formatContentTimestamps(content);
  
  // 处理HTML，使其更加美观
  // 如果是HTML内容，进行格式化处理
  if (processedContent.includes('<')) {
    try {
      // 清理一些可能导致显示问题的HTML元素
      processedContent = processedContent
        // 添加基础样式到表格
        .replace(/<table/g, '<table class="el-table"')
        .replace(/<th/g, '<th class="el-table__header-cell"')
        .replace(/<td/g, '<td class="el-table__cell"')
        // 确保链接在新窗口打开
        .replace(/<a /g, '<a target="_blank" rel="noopener noreferrer" class="el-link el-link--primary" ')
        // 强调元素添加主色调
        .replace(/<strong/g, '<strong class="primary-text"');
    } catch (error) {
      console.error('HTML处理失败', error);
    }
  }
  
  return processedContent;
};

// 初始化
onMounted(() => {
  loadMessageList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.message-detail {
  padding: 10px;
}

.message-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
}

.message-detail-header h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.message-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 14px;
  color: #606266;
  margin-bottom: 16px;
}

.message-content-card {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.message-content {
  background: #ffffff;
  padding: 20px;
  min-height: 100px;
  white-space: pre-wrap;
  word-break: break-all;
  line-height: 1.6;
  font-size: 14px;
  color: #303133;
}

/* 新增的HTML内容样式 */
.message-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 10px 0;
}

.message-content :deep(th),
.message-content :deep(td) {
  border: 1px solid #dcdfe6;
  padding: 8px;
  text-align: left;
}

.message-content :deep(th) {
  background-color: #f5f7fa;
  font-weight: bold;
}

.message-content :deep(h3) {
  font-size: 16px;
  margin: 15px 0 10px;
  color: #303133;
}

.message-content :deep(p) {
  margin: 10px 0;
  line-height: 1.6;
}

.message-content :deep(strong) {
  font-weight: bold;
}

/* 优化HTML内容的额外样式 */
.message-content :deep(.primary-text) {
  color: var(--el-color-primary);
}

.message-content :deep(ul), .message-content :deep(ol) {
  padding-left: 20px;
  margin: 10px 0;
}

.message-content :deep(li) {
  margin-bottom: 5px;
}

.message-content :deep(a) {
  text-decoration: none;
}

.message-content :deep(a:hover) {
  text-decoration: underline;
}

.message-content :deep(img) {
  max-width: 100%;
  height: auto;
  margin: 10px 0;
  border-radius: 4px;
}

.message-content :deep(pre) {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  overflow: auto;
  font-family: monospace;
  margin: 10px 0;
}

.message-content :deep(code) {
  background-color: #f5f7fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
}

.message-shop {
  font-size: 14px;
  color: #606266;
  padding: 10px 20px;
  background: #f5f7fa;
  border-top: 1px solid #e4e7ed;
}

.message-tags {
  display: flex;
  gap: 8px;
}
</style> 