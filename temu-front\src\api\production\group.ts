import request from '@/utils/request';

/**
 * 获取生产组列表
 * @param params 查询参数
 * @returns 
 */
export function getGroupList(params?: any) {
  return request({
    url: '/production/group/list',
    method: 'get',
    params
  });
}

/**
 * 获取生产组详情
 * @param groupId 生产组ID
 * @returns 
 */
export function getGroupDetail(groupId: number) {
  return request({
    url: `/production/group/${groupId}`,
    method: 'get'
  });
}

/**
 * 新增生产组
 * @param data 生产组数据
 * @returns 
 */
export function addGroup(data: any) {
  return request({
    url: '/production/group',
    method: 'post',
    data
  });
}

/**
 * 修改生产组
 * @param data 生产组数据
 * @returns 
 */
export function updateGroup(data: any) {
  return request({
    url: '/production/group',
    method: 'put',
    data
  });
}

/**
 * 删除生产组
 * @param groupIds 生产组ID数组
 * @returns 
 */
export function deleteGroup(groupIds: string) {
  return request({
    url: `/production/group/${groupIds}`,
    method: 'delete'
  });
}

/**
 * 修改生产组状态
 * @param groupId 生产组ID
 * @param status 状态（0正常 1禁用）
 * @returns 
 */
export function changeGroupStatus(groupId: number, status: string) {
  return request({
    url: '/production/group/changeStatus',
    method: 'put',
    params: { groupId, status }
  });
}

/**
 * 设置生产组负责人
 * @param groupId 生产组ID
 * @param leaderId 负责人ID
 * @returns 
 */
export function setGroupLeader(groupId: number, leaderId: number) {
  return request({
    url: '/production/group/leader',
    method: 'put',
    params: { groupId, leaderId }
  });
}

/**
 * 获取当前用户作为负责人的生产组列表
 * @returns 
 */
export function getLeaderGroups() {
  return request({
    url: '/production/group/leader',
    method: 'get'
  });
}

/**
 * 获取当前用户所属的生产组列表
 * @returns 
 */
export function getMemberGroups() {
  return request({
    url: '/production/group/member',
    method: 'get'
  });
}

/**
 * 获取未分配给生产组的店铺列表
 * @param groupId 生产组ID
 * @returns 
 */
export function getUnassignedShops(groupId: number) {
  return request({
    url: `/production/group/unassignedShops/${groupId}`,
    method: 'get'
  });
}

/**
 * 获取生产组关联的店铺列表
 * @param groupId 生产组ID
 * @returns 
 */
export function getGroupShops(groupId: number) {
  return request({
    url: `/production/group/shops/${groupId}`,
    method: 'get'
  });
}

/**
 * 为生产组分配店铺
 * @param groupId 生产组ID
 * @param shopIds 店铺ID数组
 * @returns 
 */
export function assignShopsToGroup(groupId: number, shopIds: number[]) {
  return request({
    url: `/production/group/assignShops/${groupId}`,
    method: 'post',
    data: shopIds
  });
}

/**
 * 从生产组中移除店铺
 * @param groupId 生产组ID
 * @param shopId 店铺ID
 * @returns 
 */
export function removeShopFromGroup(groupId: number, shopId: number) {
  return request({
    url: `/production/group/shop/${groupId}/${shopId}`,
    method: 'delete'
  });
} 