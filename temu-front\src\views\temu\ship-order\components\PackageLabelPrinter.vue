<template>
  <div class="package-label-printer">
    <el-button type="primary" size="small" @click="handlePrint" :loading="loading">
      <el-icon class="button-icon"><Printer /></el-icon>
      批量打印商品打包标签 {{ progressText }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Printer } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { jsPDF } from 'jspdf'
import { printShipOrderLabels } from '@/api/temu/shipOrder'
import type { ShipOrder, LabelData } from '@/types/shipOrder'
import QRCode from 'qrcode'
import JsBarcode from 'jsbarcode'
// 导入中文字体
import '@/assets/fonts/msyh-normal.js'
import '@/assets/fonts/msyh-bold.js'

// 定义props
const props = defineProps({
  selectedRows: {
    type: Array as () => ShipOrder[],
    default: () => []
  }
})

// 加载状态
const loading = ref(false)
// 标签数据
const labelData = ref<LabelData[]>([])
// 当前处理的批次
const currentBatch = ref(0)
// 每批处理的标签数量 - 增加批处理量
const BATCH_SIZE = 20
// 批处理总数
const totalBatches = ref(0)
// PDF对象
const pdfInstance = ref<jsPDF | null>(null)
// 进度文本
const progressText = computed(() => {
  if (totalBatches.value > 0 && loading.value) {
    return `(${currentBatch.value}/${totalBatches.value})`
  }
  return ''
})

// 格式化时间
const formatTime = (timestamp?: number) => {
  if (!timestamp) return '-'
  const date = new Date(timestamp)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
}

// 绘制PDF标签
const drawPdfLabel = async (pdf: jsPDF, label: LabelData) => {
  // 设置字体为微软雅黑
  pdf.setFont('msyh')
  
  // 定义标签位置和尺寸 - 增加少量边距确保边框完全可见
  const x = 1 // 左边距改为3mm
  const y = 1 // 上边距改为3mm
  const width = 98 // 宽度减少至94mm
  const height = 98 // 高度减少至94mm
  
  const isVMI = label.purchaseStockType === 0
  
  // 设置统一的线宽
  pdf.setLineWidth(0.4)
  
  // ===== 绘制框线部分 =====
  // 先绘制所有的边框线，避免重复绘制
  pdf.setDrawColor(0)
  
  // 外框 - 调整高度
  const mainFrameHeight = 75; // 从69mm增加到75mm，减少底部留白空间
  pdf.rect(x, y, width, mainFrameHeight) // 上半部分边框
  
  // 水平线
  pdf.line(x, y + 10, x + width * 0.7, y + 10) // 仓库名称下边框 - 不延伸到二维码区域
  pdf.line(x, y + 18, x + width * 0.7, y + 18) // 类型标识下边框
  pdf.line(x, y + 30, x + width, y + 30) // 店铺信息下边框
  
  // 垂直线
  pdf.line(x + width * 0.7, y, x + width * 0.7, y + 30) // 右侧垂直分隔线
  
  // 只有JIT类型才绘制中间的垂直分隔线
  if (!isVMI) {
    pdf.line(x + width * 0.35, y + 10, x + width * 0.35, y + 18) // 类型区域的垂直分隔线
  }
  
  // ===== 绘制内容部分 =====
  // 1. 仓库名称区域
  pdf.setFont('msyh', 'bold')
  pdf.setFontSize(14)
  pdf.setTextColor(0, 0, 0)
  const warehouseName = label.subWarehouseName || '仓库名称'
  const warehouseTextWidth = pdf.getStringUnitWidth(warehouseName) * 14 / pdf.internal.scaleFactor
  pdf.text(warehouseName, x + (width * 0.7 - warehouseTextWidth) / 2, y + 6.5)
  
  // 2. 二维码区域
  // 生成二维码并添加到PDF
  if (label.packageSn) {
    const qrDataUrl = await generateQRCodeDataURL(label.packageSn)
    if (qrDataUrl) {
      pdf.addImage(qrDataUrl, 'PNG', x + width * 0.7 + 2.5, y + 2.5, 25, 25)
    }
  }
  
  // 3. 类型标识区域
  pdf.setFont('msyh', 'bold')
  pdf.setFontSize(14)
  
  if (isVMI) {
    // VMI类型
    const typeText = 'VMI'
    const typeTextWidth = pdf.getStringUnitWidth(typeText) * 14 / pdf.internal.scaleFactor
    pdf.text(typeText, x + (width * 0.7 - typeTextWidth) / 2, y + 15.5)
  } else {
    // JIT类型，分两部分
    const typeJIT = 'JIT'
    const typeJITWidth = pdf.getStringUnitWidth(typeJIT) * 14 / pdf.internal.scaleFactor
    pdf.text(typeJIT, x + (width * 0.35 - typeJITWidth) / 2, y + 15.5)
    
    if (label.urgencyType === 1) {
      // 加急单元格设置黑色背景
      pdf.setFillColor(0, 0, 0)
      pdf.rect(x + width * 0.35, y + 10, width * 0.35, 8, 'F')
      
      // 加急文字设置为白色
      pdf.setTextColor(255, 255, 255)
      const typeUrgent = '加急'
      const typeUrgentWidth = pdf.getStringUnitWidth(typeUrgent) * 14 / pdf.internal.scaleFactor
      pdf.text(typeUrgent, x + width * 0.35 + (width * 0.35 - typeUrgentWidth) / 2, y + 15.5)
      
      // 恢复文字颜色为黑色
      pdf.setTextColor(0, 0, 0)
    }
  }
  
  // 4. 绘制店铺名称和时间区域
  pdf.setFont('msyh', 'normal')
  pdf.setFontSize(12)
  const shopName = label.supplierName || '商家名称'
  const shopNameWidth = pdf.getStringUnitWidth(shopName) * 12 / pdf.internal.scaleFactor
  pdf.text(shopName, x + (width * 0.7 - shopNameWidth) / 2, y + 22.5)
  
  // 时间文字
  const timeText = formatTime(label.deliverTime)
  const timeTextWidth = pdf.getStringUnitWidth(timeText) * 12 / pdf.internal.scaleFactor
  pdf.text(timeText, x + (width * 0.7 - timeTextWidth) / 2, y + 27.5)
  
  // 5. 绘制商品信息和SKC信息区域
  // 商品名称
  pdf.setFont('msyh', 'bold')
  pdf.setFontSize(12)
  const productName = label.productName || '商品名称'
  
  // 处理商品名称换行（最多3行）
  const maxLineWidth = width - 4 // 留2mm左右边距
  const productLines = splitTextToLines(pdf, productName, maxLineWidth, 3)
  
  // 计算各元素位置并确保间距合理
  // 先计算底部元素位置
  const bottomLineY = y + mainFrameHeight; // 底部框线Y坐标
  
  // SKC货号和数量 - 贴近底部框线
  const skcCodeY = bottomLineY - 3; // 底部框线上方3mm
  
  // SKC ID - 在SKC货号上方
  const skcIdY = skcCodeY - 5; // SKC货号上方5mm
  
  // 绘制体积类型和仓储属性标签 - 在SKC ID上方
  const badgeY = skcIdY - 10; // 增加间距至10mm，避免与SKC编号重叠
  
  // 添加备货单号 - 在体积标签上方
  const stockOrderY = badgeY - 3; // 原来是5mm，现在改为3mm，减少与体积标签的间距
  
  // 计算商品名称占用的空间
  const productNameEndY = y + 35 + ((productLines.length - 1) * 5);
  
  // 先计算尺码和数量组合的位置 - 在商品名称下方
  const sizeQuantityY = productNameEndY + 4;
  
  // 计算商品名称与备货单号之间的间距（现在是商品名称+尺码数量与备货单号的间距）
  const spaceBetween = stockOrderY - sizeQuantityY;
  
  // 如果间距太小，调整商品名称显示的行数
  let adjustedProductLines = productLines;
  if (spaceBetween < 15) { // 增加最小间距要求，考虑尺码数量占用的空间
    // 限制为最多2行显示
    adjustedProductLines = productLines.slice(0, Math.min(2, productLines.length));
    if (productLines.length > 2) {
      // 在第2行末尾添加省略号
      adjustedProductLines[1] = adjustedProductLines[1].substring(0, adjustedProductLines[1].length - 3) + '...';
    }
  }
  
  // 绘制商品名称
  for (let i = 0; i < adjustedProductLines.length; i++) {
    pdf.text(adjustedProductLines[i], x + 2, y + 35 + (i * 5))
  }
  
  // 添加尺码和数量组合信息 - 在商品名称下方
  const skuDetailList = (label as any).skuQuantityDetailList;
  if (skuDetailList && Array.isArray(skuDetailList) && skuDetailList.length > 0) {
    pdf.setFont('msyh', 'normal')
    pdf.setFontSize(10)
    
    // 获取所有SKU的尺码和数量组合
    const sizeQuantityItems = skuDetailList.map((sku: any) => {
      const className = sku.className || '-';
      const quantity = sku.deliverQuantity || 0;
      return `${className}-${quantity}`;
    });
    
    // 计算每行最大可显示宽度
    const maxLineWidth = width - 4; // 留2mm左右边距
    let currentLine = '';
    const lines: string[] = [];
    
    // 处理尺码-数量组合文本，根据宽度自动换行
    for (const item of sizeQuantityItems) {
      // 检查当前行加上这个项目是否会超出宽度
      const testLine = currentLine ? `${currentLine}, ${item}` : item;
      const testWidth = pdf.getStringUnitWidth(testLine) * 10 / pdf.internal.scaleFactor;
      
      if (testWidth <= maxLineWidth) {
        // 如果不超出，继续添加到当前行
        currentLine = testLine;
      } else {
        // 如果超出，保存当前行并开始新行
        lines.push(currentLine);
        currentLine = item;
      }
    }
    
    // 添加最后一行
    if (currentLine) {
      lines.push(currentLine);
    }
    
    // 限制最多显示的行数，确保不会占用太多空间
    const maxLines = Math.min(lines.length, 2);
    
    // 绘制尺码-数量文本 - 直接在商品名称下方
    for (let i = 0; i < maxLines; i++) {
      pdf.text(lines[i], x + 2, sizeQuantityY + (i * 3.5));
    }
    
    // 如果有更多行未显示，添加省略号指示
    if (lines.length > maxLines) {
      pdf.text('...', x + 2, sizeQuantityY + (maxLines * 3.5));
    }
  }
  
  // 添加备货单号 - 现在放在尺码数量组合的下方
  pdf.setFont('msyh', 'normal')
  pdf.setFontSize(11)
  const stockOrderText = `备货单号：${label.subPurchaseOrderSn || '-'}`
  pdf.text(stockOrderText, x + 2, stockOrderY)
  
  // 绘制体积类型和仓储属性标签
  pdf.setFont('msyh', 'bold')
  pdf.setFontSize(10) // 减小字体大小
  
  // 绘制第一个标签（体积类型：小/大）
  const volumeText = label.volumeType === 10 ? '小' : '大'
  const volumeTextWidth = pdf.getStringUnitWidth(volumeText) * 10 / pdf.internal.scaleFactor
  const volumeBadgeWidth = volumeTextWidth + 6 // 文本宽度加左右各3mm的边距
  const volumeBadgeHeight = 5 // 减小高度
  const volumeBadgeRadius = 2 // 圆角半径
  
  // 绘制第一个黑色背景圆角矩形
  pdf.setFillColor(0, 0, 0)
  // 使用圆角矩形绘制方法
  drawRoundedRect(pdf, x + 2, badgeY, volumeBadgeWidth, volumeBadgeHeight, volumeBadgeRadius)
  
  // 设置文字为白色
  pdf.setTextColor(255, 255, 255)
  pdf.text(volumeText, x + 2 + 3, badgeY + 3.5) // 调整文字垂直位置
  
  // 绘制第二个标签（仓储属性：服装等）
  const storageText = label.storageAttrName || '服装'
  const storageTextWidth = pdf.getStringUnitWidth(storageText) * 10 / pdf.internal.scaleFactor
  const storageBadgeWidth = storageTextWidth + 6 // 文本宽度加左右各3mm的边距
  
  // 绘制第二个黑色背景圆角矩形（位于第一个标签右侧）
  pdf.setFillColor(0, 0, 0)
  drawRoundedRect(pdf, x + 2 + volumeBadgeWidth + 3, badgeY, storageBadgeWidth, volumeBadgeHeight, volumeBadgeRadius)
  
  // 保持文字为白色
  pdf.text(storageText, x + 2 + volumeBadgeWidth + 6, badgeY + 3.5) // 调整文字垂直位置
  
  // 恢复文字颜色为黑色
  pdf.setTextColor(0, 0, 0)
  
  // SKC ID
  pdf.setFont('msyh', 'bold')
  pdf.setFontSize(10) // 减小字体大小，避免与标签重叠
  const skcIdText = `SKC${label.productSkcId}`
  pdf.text(skcIdText, x + 2, skcIdY)
  
  // SKC货号和数量
  pdf.setFontSize(11) // 恢复原来的字体大小
  const skcCodeText = `SKC货号${label.skcExtCode}`
  pdf.text(skcCodeText, x + 2, skcCodeY)
  
  // SKC数量
  pdf.setFontSize(14)
  const skcCountText = `${label.packageSkcNum || 1}件`
  const skcCountWidth = pdf.getStringUnitWidth(skcCountText) * 14 / pdf.internal.scaleFactor
  pdf.text(skcCountText, x + width - 2 - skcCountWidth, skcCodeY)
  
  // 6. 绘制下半部分（没有边框）
  // 调整底部区域的间距，避免和边框重合
  const bottomY = y + mainFrameHeight + 5; // 从2mm增加到5mm，确保不与商品信息框重叠
  
  // PC信息
  pdf.setFont('msyh', 'normal')
  pdf.setFontSize(10)
  pdf.text(label.packageSn || '', x + 2, bottomY)
  
  // 包裹索引信息
  const packageIndexText = `第${label.packageIndex || 1}包 (共${label.totalPackageNum || 1}包)`
  const packageIndexWidth = pdf.getStringUnitWidth(packageIndexText) * 10.5 / pdf.internal.scaleFactor
  pdf.setFontSize(10.5)
  pdf.text(packageIndexText, x + width - 2 - packageIndexWidth, bottomY)
  
  // 条形码 - 确保与上方内容左右对齐
  if (label.packageSn) {
    const barcodeDataUrl = await generateBarcodeDataURL(label.packageSn, isVMI)
    if (barcodeDataUrl) {
      // 调整条形码位置和尺寸，与上方框线对齐
      pdf.addImage(barcodeDataUrl, 'PNG', x, bottomY + 2, width, 10)
    }
  }
  
  // 配送信息
  pdf.setFontSize(10)
  const deliveryText = `自行配送 · 司机${label.driverName} · 手机号：${label.driverPhone || '19830702617'}`
  pdf.text(deliveryText, x + 2, bottomY + 16)
}

// 辅助函数：将文本按最大宽度拆分为多行
const splitTextToLines = (pdf: jsPDF, text: string, maxWidth: number, maxLines: number): string[] => {
  // 如果原始文本能放入单行，直接返回
  if (pdf.getStringUnitWidth(text) * pdf.getFontSize() / pdf.internal.scaleFactor <= maxWidth) {
    return [text]
  }
  
  const chars = text.split('')
  const lines: string[] = []
  let currentLine = ''
  
  for (let i = 0; i < chars.length; i++) {
    // 尝试添加当前字符
    const testLine = currentLine + chars[i]
    // 计算尝试后的宽度
    const testWidth = pdf.getStringUnitWidth(testLine) * pdf.getFontSize() / pdf.internal.scaleFactor
    
    if (testWidth <= maxWidth) {
      // 如果在宽度范围内，继续添加字符
      currentLine = testLine
    } else {
      // 超出宽度，将当前行添加到结果中，然后重新开始新行
      lines.push(currentLine)
      currentLine = chars[i]
      
      // 如果已经达到最大行数限制，但还有剩余文本，添加省略号并返回
      if (lines.length === maxLines - 1) {
        const remainingText = text.substring(i)
        // 确保最后一行包括省略号也能放入最大宽度
        if (remainingText.length > 3) { // 至少有3个字符可以替换为省略号
          // 逐字符尝试，找到可以放入的最大子串
          let j = 0
          while (j < remainingText.length) {
            const testSubstring = remainingText.substring(0, j) + '...'
            const testSubWidth = pdf.getStringUnitWidth(testSubstring) * pdf.getFontSize() / pdf.internal.scaleFactor
            if (testSubWidth > maxWidth) {
              // 回退一个字符
              const finalSubstring = remainingText.substring(0, j - 1) + '...'
              lines.push(finalSubstring)
              return lines
            }
            j++
          }
          // 整个剩余文本加省略号可以放入
          lines.push(remainingText + '...')
        } else {
          // 剩余文本太短，不添加省略号
          lines.push(remainingText)
        }
        return lines
      }
    }
  }
  
  // 添加最后一行
  if (currentLine) {
    lines.push(currentLine)
  }
  
  return lines
}

// 生成二维码为dataURL
const generateQRCodeDataURL = async (text: string): Promise<string> => {
  try {
    return await QRCode.toDataURL(text, {
      width: 100,
      margin: 0,
      errorCorrectionLevel: 'H'
    })
  } catch (error) {
    console.error('生成二维码失败:', error)
    return ''
  }
}

// 辅助函数：绘制圆角矩形（使用PDF内置的addPage方法添加一个圆角矩形）
const drawRoundedRect = (pdf: jsPDF, x: number, y: number, width: number, height: number, radius: number) => {
  try {
    // 尝试使用jsPDF的内部方法直接绘制矩形
    // 注意：新版本jsPDF可能直接支持圆角矩形，但旧版本可能不支持
    const style = {
      style: 'F',
      borderRadius: radius
    };
    
    const saveFillColor = pdf.getFillColor();
    pdf.setFillColor(0, 0, 0); // 确保填充为黑色
    
    // 尝试调用roundedRect方法（如果存在）
    if (typeof (pdf as any).roundedRect === 'function') {
      (pdf as any).roundedRect(x, y, width, height, radius, radius, 'F');
    } else {
      // 备选方案：使用普通矩形，但利用视觉效果模拟圆角
      // 先绘制主矩形
      pdf.rect(x, y, width, height, 'F');
      
      // 使用白色矩形擦除四个角
      pdf.setFillColor(255, 255, 255); // 白色
      
      // 左上角 - 使用斜切的方式
      pdf.triangle(
        x, y, // 左上角
        x + radius * 0.8, y, // 右上
        x, y + radius * 0.8, // 左下
        'F'
      );
      
      // 右上角
      pdf.triangle(
        x + width, y, // 右上角
        x + width - radius * 0.8, y, // 左上
        x + width, y + radius * 0.8, // 右下
        'F'
      );
      
      // 左下角
      pdf.triangle(
        x, y + height, // 左下角
        x + radius * 0.8, y + height, // 右下
        x, y + height - radius * 0.8, // 左上
        'F'
      );
      
      // 右下角
      pdf.triangle(
        x + width, y + height, // 右下角
        x + width - radius * 0.8, y + height, // 左下
        x + width, y + height - radius * 0.8, // 右上
        'F'
      );
    }
    
    // 恢复原来的填充颜色
    pdf.setFillColor(saveFillColor);
  } catch (error) {
    // 出错时使用简单矩形作为备选方案
    console.error('圆角矩形绘制失败，使用普通矩形代替', error);
    pdf.rect(x, y, width, height, 'F');
  }
}

// 生成条形码为dataURL
const generateBarcodeDataURL = (text: string, isVMI: boolean): Promise<string> => {
  return new Promise((resolve, reject) => {
    try {
      // 创建临时SVG元素
      const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
      
      // 根据类型设置条形码宽度
      const barcodeWidth = isVMI ? 1.5 : Math.max(1.0, 2.0 - (text.length * 0.03))
      
      // 生成条形码
      JsBarcode(svg, text, {
        format: 'CODE128',
        width: barcodeWidth,
        height: isVMI ? 35 : 30,
          displayValue: false,
          margin: 0,
        background: '#FFFFFF',
        lineColor: '#000000'
      })
      
      // 将SVG转换为字符串
      const svgString = new XMLSerializer().serializeToString(svg)
      
      // 创建图像对象加载SVG
      const img = new Image()
      img.onload = () => {
        // 创建canvas并绘制图像
        const canvas = document.createElement('canvas')
        canvas.width = isVMI ? 300 : 250 // 足够宽以显示完整条形码
        canvas.height = isVMI ? 40 : 35
        const ctx = canvas.getContext('2d')
        if (ctx) {
          ctx.fillStyle = '#FFFFFF'
          ctx.fillRect(0, 0, canvas.width, canvas.height)
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
          
          // 转换为PNG
          resolve(canvas.toDataURL('image/png'))
        } else {
          reject(new Error('无法获取canvas上下文'))
        }
      }
      img.onerror = () => reject(new Error('加载SVG图像失败'))
      
      // 设置图像源为SVG
      img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgString)))
    } catch (error) {
      console.error('生成条形码失败:', error)
      reject(error)
    }
  })
}

// 分批处理标签
const processBatches = async () => {
  if (!pdfInstance.value) return
  
  // 处理剩余批次
  if (currentBatch.value < totalBatches.value) {
    // 计算当前批次的起始索引和结束索引
    const start = currentBatch.value * BATCH_SIZE
    const end = Math.min(start + BATCH_SIZE, labelData.value.length)
    
    // 获取当前批次的标签数据
    const currentBatchLabels = labelData.value.slice(start, end)
    
    // 改为顺序处理标签，而不是并行处理
    for (let i = 0; i < currentBatchLabels.length; i++) {
      const labelIndex = start + i
      const label = currentBatchLabels[i]
      
      // 如果不是第一个标签，添加新页面
      if (labelIndex > 0) {
        pdfInstance.value.addPage()
      }
      
      // 直接绘制PDF标签 - 等待绘制完成再处理下一个
      await drawPdfLabel(pdfInstance.value, label)
    }
    
    // 增加批次计数
    currentBatch.value++
    
    // 使用setTimeout延迟处理下一批次，给UI更新留出时间
    setTimeout(() => {
      processBatches()
    }, 10)
  } else {
    // 所有批次处理完成，保存PDF
    pdfInstance.value.save(`标签打印_${new Date().toISOString().split('T')[0]}.pdf`)
    pdfInstance.value = null
    ElMessage.success('标签PDF生成成功')
    loading.value = false
  }
}

// 处理打印
const handlePrint = async () => {
  if (props.selectedRows.length === 0) {
    ElMessage.warning('请先选择要打印标签的发货单')
    return
  }
  
  if (loading.value) {
    ElMessage.warning('正在处理，请稍候')
    return
  }
  
  try {
    loading.value = true
    
    // 收集所需数据
    const printData = props.selectedRows.map(row => ({
      deliveryOrderSn: row.deliveryOrderSn,
      subPurchaseOrderSn: row.subPurchaseOrderBasicVO?.subPurchaseOrderSn || row.subPurchaseOrderSn,
      shopId: Number(row.shopId)
    }))
    
    // 调用API获取标签数据
    const res = await printShipOrderLabels(printData)
    
    // 使用类型断言来处理响应
    type ApiResponse = { code: number; message: string; data: { labelData: LabelData[] } }
    const apiRes = res as unknown as ApiResponse
    
    if (apiRes && apiRes.code === 200 && apiRes.data?.labelData) {
      // 设置标签数据
      labelData.value = apiRes.data.labelData
      
      // 计算总批次数
      totalBatches.value = Math.ceil(labelData.value.length / BATCH_SIZE)
      
      // 重置批次计数器
      currentBatch.value = 0
      
      // 创建PDF实例
      pdfInstance.value = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: [100, 100], // 修改为100mm×100mm的自定义尺寸，而不是A4
        compress: true
      })
      
      // 添加中文字体支持
      pdfInstance.value.addFont('msyh-normal.ttf', 'msyh', 'normal')
      pdfInstance.value.addFont('msyh-bold.ttf', 'msyh', 'bold')
      
      // 开始分批处理
      processBatches()
    } else {
      ElMessage.error((apiRes && apiRes.message) || '获取标签数据失败')
      loading.value = false
    }
  } catch (error) {
    console.error('打印标签出错:', error)
    ElMessage.error('生成标签PDF失败')
    loading.value = false
  }
}
</script>

<style scoped>
.package-label-printer {
  /* 移除margin: 10px 0 */
}

.button-icon {
  margin-right: 4px;
}
</style>