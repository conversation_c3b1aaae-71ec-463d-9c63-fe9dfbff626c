import request from '@/utils/request'
import type { 
  ShopAssignmentQuery, 
  AssignShopParams, 
  UnassignShopParams, 
  UpdatePermissionParams,
  BatchAssignShopParams,
  CreateMemberParams
} from '@/types/leaderAssign'
import { useUserStore } from '@/store/modules/user'

/**
 * 获取当前用户ID，如果不存在则抛出错误
 */
function getCurrentUserId(): number {
  const userStore = useUserStore()
  const userId = userStore.userId
  
  if (userId === null || userId === undefined) {
    throw new Error('用户未登录或ID不存在')
  }
  
  return userId
}

/**
 * 获取店铺分配列表（分页）
 * @param query 查询参数
 * @returns 店铺分配列表数据
 */
export function getShopAssignmentList(query: ShopAssignmentQuery) {
  console.log('调用API: 获取店铺分配列表, 参数:', query);
  return request({
    url: '/leader/assign/list',
    method: 'get',
    params: query
  })
}

/**
 * 获取运营组的所有店铺分配数据（不分页）
 * @param groupId 运营组ID
 * @returns 该运营组的所有分配数据列表
 */
export function getAllShopAssignmentsByGroupId(groupId: number) {
  console.log('调用API: 获取运营组所有分配数据, 运营组ID:', groupId);
  return request({
    url: `/leader/assign/list/all/${groupId}`,
    method: 'get'
  })
}

/**
 * 分配店铺给组员
 * @param data 分配参数
 * @returns 操作结果
 */
export function assignShop(data: AssignShopParams) {
  // 设置当前用户为分配者
  if (!data.assignBy) {
    data.assignBy = getCurrentUserId()
  }
  
  // 强制设置为只读权限
  data.permissionType = '0'
  
  console.log('调用API: 分配店铺, 参数:', data);
  return request({
    url: '/leader/assign',
    method: 'post',
    data
  })
}

/**
 * 取消店铺分配
 * @param id 分配记录ID
 * @returns 操作结果
 */
export function unassignShop(id: number) {
  const operatorId = getCurrentUserId()
  
  console.log('调用API: 取消店铺分配, ID:', id, '操作者:', operatorId);
  return request({
    url: `/leader/assign/${id}`,
    method: 'delete',
    params: {
      operatorId
    }
  })
}

/**
 * 修改分配权限类型
 * @param data 权限修改参数
 * @returns 操作结果
 */
export function updatePermissionType(data: UpdatePermissionParams) {
  // 设置当前用户为操作者
  if (!data.operatorId) {
    data.operatorId = getCurrentUserId()
  }
  
  console.log('调用API: 修改权限类型, 参数:', data);
  return request({
    url: `/leader/assign/${data.id}`,
    method: 'put',
    params: {
      permissionType: data.permissionType,
      assignBy: data.operatorId
    }
  })
}

/**
 * 获取已分配店铺的用户列表
 * @param shopId 店铺ID
 * @returns 用户列表数据
 */
export function getShopAssignedUsers(shopId: number) {
  console.log('调用API: 获取已分配店铺的用户列表, 店铺ID:', shopId);
  return request({
    url: `/leader/assign/shop/${shopId}`,
    method: 'get'
  })
}

/**
 * 获取用户被分配的店铺列表
 * @param userId 用户ID
 * @returns 店铺列表数据
 */
export function getUserAssignedShops(userId: number) {
  console.log('调用API: 获取用户被分配的店铺列表, 用户ID:', userId);
  return request({
    url: `/leader/assign/user/${userId}`,
    method: 'get'
  })
}

/**
 * 获取运营组长可分配的店铺列表
 * @param groupId 运营组ID
 * @returns 店铺列表数据
 */
export function getLeaderAssignableShops(groupId: number) {
  console.log('调用API: 获取可分配店铺, 运营组ID:', groupId);
  return request({
    url: `/leader/assign/assignableShops/${groupId}`,
    method: 'get'
  })
}

/**
 * 获取运营组长的组员列表
 * @param groupId 运营组ID
 * @returns 组员列表数据
 */
export function getLeaderGroupMembers(groupId: number) {
  console.log('调用API: 获取组员列表, 运营组ID:', groupId);
  return request({
    url: `/leader/assign/groupMembers/${groupId}`,
    method: 'get'
  })
}

/**
 * 批量取消分配
 * @param ids 分配记录ID列表
 * @returns 操作结果
 */
export function batchUnassignShops(ids: number[]) {
  const operatorId = getCurrentUserId()
  
  console.log('调用API: 批量取消分配, IDs:', ids);
  return request({
    url: '/leader/assign/batch',
    method: 'delete',
    data: ids,
    params: {
      operatorId
    }
  })
}

/**
 * 批量分配店铺给组员
 * @param data 批量分配参数
 * @returns 操作结果
 */
export function batchAssignShops(data: BatchAssignShopParams) {
  // 设置当前用户为分配者
  if (!data.assignBy) {
    data.assignBy = getCurrentUserId()
  }
  
  // 强制设置为只读权限
  data.permissionType = '0'
  
  console.log('调用API: 批量分配店铺, 参数:', data);
  return request({
    url: '/leader/assign/batch',
    method: 'post',
    params: {
      groupId: data.groupId,
      shopIds: data.shopIds.join(','),
      userId: data.userId,
      permissionType: data.permissionType,
      assignBy: data.assignBy
    }
  })
}

/**
 * 组长创建组员
 * @param data 创建组员参数
 * @returns 操作结果
 */
export function createMember(data: CreateMemberParams) {
  console.log('调用API: 组长创建组员, 参数:', data);
  return request({
    url: '/leader/assign/createMember',
    method: 'post',
    data
  })
} 