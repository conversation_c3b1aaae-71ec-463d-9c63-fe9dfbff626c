<template>
  <el-popover
    placement="bottom"
    :width="350"
    trigger="click"
    popper-class="message-popover"
  >
    <template #reference>
      <div class="message-badge" @click="loadUnreadMessages">
        <el-badge :value="unreadCount.totalCount || 0" :max="99" :hidden="unreadCount.totalCount <= 0">
          <el-icon size="20" color="#606266">
            <Bell />
          </el-icon>
        </el-badge>
      </div>
    </template>
    
    <div class="message-header">
      <span class="title">消息通知</span>
      <el-link type="primary" @click="goToMessageCenter">查看全部</el-link>
    </div>
    
    <div class="message-search">
      <el-input
        v-model="keyword"
        placeholder="搜索消息"
        clearable
        prefix-icon="Search"
        @input="handleSearch"
      >
        <template #append>
          <el-button :icon="Search" @click="handleSearch" />
        </template>
      </el-input>
      
      <!-- 高级筛选 -->
      <div class="advanced-search" v-if="showAdvanced">
        <div class="filter-item">
          <label>消息类型:</label>
          <el-select v-model="filters.messageType" placeholder="全部类型" clearable @change="handleSearch">
            <el-option label="系统消息" :value="MessageType.SYSTEM" />
            <el-option label="任务消息" :value="MessageType.TASK" />
            <el-option label="店铺消息" :value="MessageType.SHOP" />
          </el-select>
        </div>
        <div class="filter-item">
          <label>阅读状态:</label>
          <el-select v-model="filters.readStatus" placeholder="全部状态" clearable @change="handleSearch">
            <el-option label="未读" value="0" />
            <el-option label="已读" value="1" />
          </el-select>
        </div>
        <div class="filter-item">
          <label>重要程度:</label>
          <el-select v-model="filters.importance" placeholder="全部" clearable @change="handleSearch">
            <el-option label="普通" value="1" />
            <el-option label="重要" value="2" />
            <el-option label="紧急" value="3" />
          </el-select>
        </div>
      </div>
      
      <div class="toggle-advanced">
        <el-button type="text" @click="showAdvanced = !showAdvanced">
          {{ showAdvanced ? '收起' : '高级筛选' }}
          <el-icon>
            <component :is="showAdvanced ? 'ArrowUp' : 'ArrowDown'" />
          </el-icon>
        </el-button>
        
        <el-button v-if="hasFilters" type="text" @click="clearFilters">
          清除筛选
          <el-icon><Delete /></el-icon>
        </el-button>
      </div>
    </div>
    
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="全部" name="all">
        <div class="message-list-container">
          <div class="message-list-content" ref="allMessagesRef" @scroll="(e) => handleScroll(e, 'all')">
            <MessageList :messages="messages" :loading="isInitialLoading" type="preview" @view-detail="viewMessage" @reload="handleSearch" />
            <div v-if="loadingMore.all" class="loading-more">加载中...</div>
            <div v-if="!hasMore.all && messages.length > 0" class="no-more">没有更多数据了</div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="系统消息" name="system">
        <div class="message-list-container">
          <div class="message-list-content" ref="systemMessagesRef" @scroll="(e) => handleScroll(e, 'system')">
            <MessageList :messages="systemMessages" :loading="isInitialLoading" type="preview" @view-detail="viewMessage" @reload="handleSearch" />
            <div v-if="loadingMore.system" class="loading-more">加载中...</div>
            <div v-if="!hasMore.system && systemMessages.length > 0" class="no-more">没有更多数据了</div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="任务消息" name="task">
        <div class="message-list-container">
          <div class="message-list-content" ref="taskMessagesRef" @scroll="(e) => handleScroll(e, 'task')">
            <MessageList :messages="taskMessages" :loading="isInitialLoading" type="preview" @view-detail="viewMessage" @reload="handleSearch" />
            <div v-if="loadingMore.task" class="loading-more">加载中...</div>
            <div v-if="!hasMore.task && taskMessages.length > 0" class="no-more">没有更多数据了</div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <!-- <div class="message-footer">
      <el-button type="primary" link @click="markAllAsRead">全部已读</el-button>
    </div> -->

    <!-- 消息详情对话框 - 使用与my-message/index.vue一致的样式 -->
    <el-dialog
      v-model="dialogVisible"
      title="消息详情"
      width="1000px"
      append-to-body
    >
      <div class="message-detail" v-if="currentMessage">
        <div class="message-detail-header">
          <h2>{{ currentMessage.title }}</h2>
          <div class="message-tags">
            <el-tag v-if="currentMessage.importance === '3'" type="danger">紧急</el-tag>
            <el-tag v-else-if="currentMessage.importance === '2'" type="warning">重要</el-tag>
            
            <el-tag v-if="currentMessage.messageType === '1'" type="primary" style="margin-left: 8px">系统消息</el-tag>
            <el-tag v-else-if="currentMessage.messageType === '2'" type="success" style="margin-left: 8px">任务提醒</el-tag>
            <el-tag v-else-if="currentMessage.messageType === '3'" type="warning" style="margin-left: 8px">店铺消息</el-tag>
          </div>
        </div>
        
        <div class="message-meta">
          <div><i class="el-icon-time"></i> 发送时间: {{ formatTime(currentMessage.createTime) }}</div>
          <div v-if="currentMessage.readStatus === '1'">
            <i class="el-icon-view"></i> 已读时间: {{ currentMessage.readTime ? formatTime(currentMessage.readTime) : '-' }}
          </div>
        </div>
        
        <div class="message-content-card">
          <div class="message-content" v-html="formatContentTimestamps(currentMessage.content)">
          </div>
          
          <div v-if="currentMessage.shopId" class="message-shop">
            相关店铺: {{ currentMessage.shopName || `ID: ${currentMessage.shopId}` }}
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button 
            v-if="currentMessage && currentMessage.readStatus === '0'" 
            type="primary" 
            @click="markAsRead(currentMessage)"
          >
            标为已读
          </el-button>
        </span>
      </template>
    </el-dialog>
  </el-popover>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { Bell, Search, ArrowUp, ArrowDown, Delete } from '@element-plus/icons-vue'
import { useMessageStore } from '@/store'
import type { Message, MessageQueryDTO, MessageListResult } from '@/types/message'
import { MessageType } from '@/types/message'
import MessageList from '@/components/MessageList/index.vue'
import { formatTime } from '@/utils/format'  // 导入日期时间格式化函数

const router = useRouter()
const messageStore = useMessageStore()

// 消息列表
const messages = ref<Message[]>([])
const isInitialLoading = ref(false)
const activeTab = ref('all')
const keyword = ref('') // 搜索关键词
const showAdvanced = ref(false)
const filters = ref({
  messageType: '' as string | undefined,
  readStatus: '' as string | undefined,
  importance: '' as string | undefined
})

// 消息详情弹窗相关
const dialogVisible = ref(false)
const currentMessage = ref<Message>()

// 分页和滚动相关参数
type TabName = 'all' | 'system' | 'task'
const currentPage = ref<Record<TabName, number>>({
  all: 1,
  system: 1,
  task: 1
})
const pageSize = ref(5)
const total = ref<Record<TabName, number>>({
  all: 0,
  system: 0,
  task: 0
})
const hasMore = ref<Record<TabName, boolean>>({
  all: true,
  system: true,
  task: true
})
const loadingMore = ref<Record<TabName, boolean>>({
  all: false,
  system: false,
  task: false
})
const scrollPositions = ref<Record<TabName, number>>({
  all: 0,
  system: 0,
  task: 0
})

// 每个标签页的消息列表元素引用
const allMessagesRef = ref<HTMLElement | null>(null)
const systemMessagesRef = ref<HTMLElement | null>(null)
const taskMessagesRef = ref<HTMLElement | null>(null)

// 获取当前活动标签页的消息列表元素
const getActiveListRef = (): HTMLElement | null => {
  switch (activeTab.value) {
    case 'all': return allMessagesRef.value
    case 'system': return systemMessagesRef.value
    case 'task': return taskMessagesRef.value
    default: return null
  }
}

// 计算未读消息数量
const unreadCount = computed(() => messageStore.unreadCount)

// 根据类型筛选消息
const systemMessages = computed(() => 
  messages.value.filter(msg => msg.messageType === MessageType.SYSTEM)
)
const taskMessages = computed(() => 
  messages.value.filter(msg => msg.messageType === MessageType.TASK)
)

// 加载未读消息
const loadUnreadMessages = async () => {
  // 重置过滤条件
  keyword.value = ''
  filters.value.messageType = ''
  filters.value.readStatus = ''
  filters.value.importance = ''
  
  // 重置分页
  currentPage.value = {
    all: 1,
    system: 1,
    task: 1
  }
  messages.value = []
  hasMore.value = {
    all: true,
    system: true,
    task: true
  }
  
  // 先获取未读消息数量
  await messageStore.getUnreadCount()
  
  // 使用handleSearch加载消息
  await handleSearch(true)
}

// 搜索消息
const handleSearch = async (reset = false) => {
  const currentTab = activeTab.value as TabName
  
  if (reset) {
    currentPage.value[currentTab] = 1
    
    if (currentTab === 'all') {
      messages.value = []
    }
    
    hasMore.value[currentTab] = true
    
    isInitialLoading.value = true
  } else {
    // 如果没有更多数据且不是重置操作，则直接返回
    if (!hasMore.value[currentTab]) return
    
    // 记录当前滚动位置
    const listRef = getActiveListRef()
    if (listRef) {
      scrollPositions.value[currentTab] = listRef.scrollTop
    }
    
    loadingMore.value[currentTab] = true
  }
  
  try {
    const query: MessageQueryDTO = {
      pageNum: currentPage.value[currentTab],
      pageSize: pageSize.value,
      keyword: keyword.value,
      messageType: currentTab === 'all' ? filters.value.messageType : 
                  currentTab === 'system' ? MessageType.SYSTEM : MessageType.TASK,
      readStatus: filters.value.readStatus,
      importance: filters.value.importance
    }
    
    const result = await messageStore.getMessageList(query)
    if (result && result.code === 200 && result.data) {
      const messageData = result.data as {
        records: Message[];
        total: number;
        current: number;
        pages: number;
        size: number;
      }
      
      if (messageData.records) {
        // 更新消息列表
        if (reset) {
          messages.value = messageData.records
        } else {
          messages.value = [...messages.value, ...messageData.records]
        }
        
        // 更新分页信息
        total.value[currentTab] = messageData.total
        hasMore.value[currentTab] = currentPage.value[currentTab] < messageData.pages
        
        // 等待DOM更新后恢复滚动位置
        await nextTick()
        if (!reset) {
          const listRef = getActiveListRef()
          if (listRef) {
            listRef.scrollTop = scrollPositions.value[currentTab]
          }
        }
      }
    }
  } catch (error) {
    console.error('搜索消息失败', error)
  } finally {
    if (reset) {
      isInitialLoading.value = false
    } else {
      loadingMore.value[currentTab] = false
    }
  }
}

// 处理滚动事件
const handleScroll = (event: Event, tab: TabName) => {
  // 如果正在加载，则不处理滚动事件
  if (loadingMore.value[tab]) return
  
  const target = event.target as HTMLElement
  
  // 记录当前滚动位置
  scrollPositions.value[tab] = target.scrollTop
  
  // 判断是否滚动到底部
  // 当距离底部小于50px时，加载更多数据
  const bottomDistance = target.scrollHeight - target.scrollTop - target.clientHeight
  if (bottomDistance < 50 && hasMore.value[tab]) {
    loadMore(tab)
  }
}

// 加载更多数据
const loadMore = (tab: TabName) => {
  if (loadingMore.value[tab] || !hasMore.value[tab]) return
  
  currentPage.value[tab]++
  handleSearch(false)
}

// 切换标签页
const handleTabClick = () => {
  const currentTab = activeTab.value as TabName
  
  // 如果当前标签对应的列表为空且有更多数据，则加载数据
  if (
    (currentTab === 'system' && systemMessages.value.length === 0 && hasMore.value.system) ||
    (currentTab === 'task' && taskMessages.value.length === 0 && hasMore.value.task) ||
    (currentTab === 'all' && messages.value.length === 0 && hasMore.value.all)
  ) {
    // 重置当前标签的分页状态
    currentPage.value[currentTab] = 1
    hasMore.value[currentTab] = true
    
    // 加载数据
    handleSearch(true)
  }
}

// 标记全部为已读
const markAllAsRead = async () => {
  try {
    // 获取当前展示消息中所有未读消息的ID
    const unreadMessageIds = messages.value
      .filter(msg => msg.readStatus === '0')
      .map(msg => typeof msg.messageId === 'string' ? parseInt(msg.messageId, 10) : msg.messageId)
    
    if (unreadMessageIds.length === 0) return
    
    // 批量标记已读
    await messageStore.batchMarkRead(unreadMessageIds)
    
    // 刷新消息列表
    loadUnreadMessages()
  } catch (error) {
    console.error('标记已读失败', error)
  }
}

// 跳转到消息中心
const goToMessageCenter = () => {
  router.push('/my-message')
}

// 查看消息详情 - 修改为打开弹窗而不是跳转
const viewMessage = (message: Message) => {
  currentMessage.value = message
  dialogVisible.value = true
  
  // 如果是未读消息，标记为已读
  if (message.readStatus === '0') {
    markAsRead(message)
  }
}

// 标记消息为已读
const markAsRead = async (message: Message) => {
  try {
    const messageId = typeof message.messageId === 'string' ? parseInt(message.messageId, 10) : message.messageId
    await messageStore.markMessageRead(messageId)
    
    // 刷新列表
    handleSearch(true)
    
    // 如果是当前消息，更新状态
    if (currentMessage.value && currentMessage.value.messageId === message.messageId) {
      currentMessage.value.readStatus = '1'
      currentMessage.value.readTime = new Date().toISOString()
    }
    
    // 更新未读消息数量
    await messageStore.getUnreadCount()
    
  } catch (error) {
    console.error('标记已读失败', error)
  }
}

// 时间戳转换函数 - 将消息内容中的时间戳转换为正常日期格式
const formatContentTimestamps = (content: string): string => {
  if (!content) return '';
  
  // 匹配内容中的时间戳（通常是13位数字）
  const timestampRegex = /(：|:)(\s*)(\d{13})/g;
  
  // 处理时间戳
  const processedContent = content.replace(timestampRegex, (match, prefix, space, timestamp) => {
    try {
      // 将时间戳转换为日期对象
      const date = new Date(parseInt(timestamp));
      if (isNaN(date.getTime())) {
        return match; // 如果转换失败则保持原样
      }
      
      // 直接格式化为 YYYY-MM-DD HH:mm:ss
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      
      const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      return `${prefix}${space}${formattedDate}`;
    } catch (error) {
      console.error('时间戳转换失败', error);
      return match; // 出错时保持原样
    }
  });
  
  return processedContent;
};

// 保留原有的goToMessagePage函数重命名为goToMessagePage，用于"查看全部"的跳转
const goToMessagePage = (message: Message) => {
  const messageId = typeof message.messageId === 'string' ? parseInt(message.messageId, 10) : message.messageId
  router.push(`/message/my-message/detail/${messageId}`)
}

// 组件挂载时加载未读消息数量
onMounted(async () => {
  await messageStore.getUnreadCount()
})

// 检查是否有过滤条件
const hasFilters = computed(() => {
  return filters.value.messageType || filters.value.readStatus || filters.value.importance
})

// 清除过滤条件
const clearFilters = () => {
  // 重置过滤条件
  keyword.value = ''
  filters.value.messageType = ''
  filters.value.readStatus = ''
  filters.value.importance = ''
  
  // 执行搜索以应用过滤
  handleSearch()
}
</script>

<style scoped>
.message-badge {
  cursor: pointer;
  padding: 0 12px;
}

.message-header {
  padding: 8px 12px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-header .title {
  font-size: 15px;
  font-weight: 500;
}

.message-search {
  padding: 8px 12px;
  border-bottom: 1px solid #ebeef5;
}

.advanced-search {
  margin-top: 8px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.filter-item {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.filter-item label {
  width: 70px;
  font-size: 12px;
}

.filter-item .el-select {
  width: 100%;
}

.toggle-advanced {
  text-align: right;
  margin-top: 4px;
  font-size: 12px;
}

.message-list-container {
  position: relative;
  height: 300px;
}

.message-list-content {
  height: 100%;
  overflow-y: auto;
  position: relative;
  scroll-behavior: smooth;
}

.loading-more {
  text-align: center;
  padding: 6px 0;
  color: #909399;
  font-size: 12px;
}

.no-more {
  text-align: center;
  padding: 6px 0;
  color: #909399;
  font-size: 12px;
}

.message-footer {
  padding: 8px 12px;
  border-top: 1px solid #ebeef5;
  text-align: center;
}

/* 调整MessageList组件中消息项的样式 */
:deep(.message-item) {
  padding: 6px 12px;
  font-size: 13px;
}

:deep(.message-item .message-title) {
  font-size: 13px;
  margin-bottom: 2px;
}

:deep(.message-item .message-content) {
  font-size: 12px;
  line-height: 1.4;
}

:deep(.message-item .message-time) {
  font-size: 11px;
  margin-top: 2px;
}

:deep(.message-item .el-icon) {
  font-size: 14px;
}

/* 未读消息的样式 */
:deep(.message-item[data-read-status="0"]) {
  background-color: #f0f9eb;
  border-left: 3px solid #67c23a;
  position: relative;
}

:deep(.message-item[data-read-status="0"] .message-title) {
  font-weight: bold;
  color: #303133;
}

:deep(.message-item[data-read-status="0"] .message-content) {
  color: #0a0a0a;
}

:deep(.message-item[data-read-status="0"]::before) {
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #67c23a;
  position: absolute;
  right: 8px;
  top: 8px;
}

/* 已读消息的样式 */
:deep(.message-item[data-read-status="1"]) {
  background-color: #ffffff;
  border-left: 3px solid #dcdfe6;
}

:deep(.message-item[data-read-status="1"] .message-title) {
  color: #606266;
}

:deep(.message-item[data-read-status="1"] .message-content) {
  color: #909399;
}

/* 调整消息项内部间距和图标大小 */
:deep(.message-icon) {
  width: 32px;
  height: 32px;
  font-size: 16px;
  line-height: 32px;
}

/* 添加消息详情弹窗样式 - 与my-message/index.vue保持一致 */
.message-detail {
  padding: 10px;
}

.message-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
}

.message-detail-header h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.message-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 14px;
  color: #606266;
  margin-bottom: 16px;
}

.message-content-card {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.message-content {
  background: #ffffff;
  padding: 20px;
  min-height: 100px;
  white-space: pre-wrap;
  word-break: break-all;
  line-height: 1.6;
  font-size: 14px;
  color: #303133;
}

/* HTML内容样式 */
.message-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 10px 0;
}

.message-content :deep(th),
.message-content :deep(td) {
  border: 1px solid #dcdfe6;
  padding: 8px;
  text-align: left;
}

.message-content :deep(th) {
  background-color: #f5f7fa;
  font-weight: bold;
}

.message-content :deep(h3) {
  font-size: 16px;
  margin: 15px 0 10px;
  color: #303133;
}

.message-content :deep(p) {
  margin: 10px 0;
  line-height: 1.6;
}

.message-content :deep(strong) {
  font-weight: bold;
}

.message-shop {
  font-size: 14px;
  color: #606266;
  padding: 10px 20px;
  background: #f5f7fa;
  border-top: 1px solid #e4e7ed;
}

.message-tags {
  display: flex;
  gap: 8px;
}
</style>

<style>
.message-popover {
  padding: 0;
  min-width: 350px;
}

.message-popover .el-tabs__nav {
  width: 100%;
  display: flex;
}

.message-popover .el-tabs__item {
  flex: 1;
  text-align: center;
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
  font-size: 13px;
}

/* 调整弹出框内部元素的紧凑度 */
.message-popover .el-tabs__header {
  margin-bottom: 8px;
}

.message-popover .el-tabs__content {
  padding-bottom: 6px;
}

.message-popover .el-input__inner {
  height: 32px;
  line-height: 32px;
}

.message-popover .el-input__prefix,
.message-popover .el-input__suffix {
  line-height: 32px;
}

.message-popover .el-button--small {
  padding: 8px 12px;
  font-size: 12px;
}
</style> 