<template>
  <AppLayout>
    <!-- 搜索区域 -->
    <template #search>
      <SearchCard>
        <!-- 搜索表单 -->
        <div class="search-container">
          <!-- 第一行查询条件 -->
          <div class="search-row">
            <!-- 店铺选择 -->
            <div class="search-item">
              <span class="search-label">店铺</span>
              <el-select 
                v-model="queryParams.shopIds" 
                placeholder="请选择店铺" 
                clearable 
                @change="handleShopChange" 
                @clear="handleClearShops"
                size="small"
                class="search-input"
                multiple
                collapse-tags
                filterable
                :filter-method="filterShops"
              >
                <!-- 添加全选选项 -->
                <el-option 
                  key="select-all" 
                  label="全选" 
                  :value="-1"
                />
                <el-option 
                  v-for="shop in filteredShops" 
                  :key="shop.shopId" 
                  :label="shop.shopName" 
                  :value="shop.shopId"
                />
              </el-select>
            </div>
          
            <!-- 商品ID查询 -->
            <div class="search-item">
              <span class="search-label">商品ID查询</span>
              <TagInput
                :model-value="idType === 'sku' ? queryParams.skuIdList : queryParams.skcIdList"
                @update:model-value="val => idType === 'sku' ? queryParams.skuIdList = val : queryParams.skcIdList = val"
                :is-numeric="true"
                :show-type-select="true"
                v-model:type-value="idType"
                :type-options="[
                  { label: 'SKU', value: 'sku' },
                  { label: 'SKC', value: 'skc' }
                ]"
                placeholder="多个查询请空格或逗号等依次输入"
                class="search-input"
              />
            </div>
            
            <!-- 备货单号 -->
            <div class="search-item">
              <span class="search-label">备货单号</span>
              <TagInput
                v-model="queryParams.purchaseNo"
                placeholder="多个以空格，逗号等分隔"
                :is-numeric="false"
                class="search-input"
              />
            </div>
            
            <!-- 最新抽检时间 -->
            <div class="search-item">
              <span class="search-label">最新抽检时间</span>
              <el-date-picker
                v-model="timeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD"
                @change="handleTimeRangeChange"
                size="small"
                class="search-input date-picker"
              />
            </div>
          </div>
          
          <!-- 第二行查询条件 -->
          <div class="search-row second-row">
            <!-- 商品名称 -->
            <div class="search-item product-name-item">
              <span class="search-label">商品名称</span>
              <el-input
                v-model="queryParams.skuNameKeyword"
                placeholder="输入商品名称关键词"
                clearable
                size="small"
                class="search-input"
              />
            </div>
            
            <!-- 按钮组 -->
            <div class="search-buttons">
              <el-button type="primary" @click="handleQuery" size="small">
                查询
              </el-button>
              <el-button @click="resetQuery" size="small">
                重置
              </el-button>
              <el-button type="success" @click="handleExport" size="small">
                <el-icon><Download /></el-icon> 导出Excel
              </el-button>
            </div>
          </div>
        </div>
      </SearchCard>
    </template>

    <!-- 表格内容区域 -->
    <template #table>
      <TableCard>
        <!-- 添加标签页 -->
        <template #tabs>
          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane label="抽检不合格" name="failed"></el-tab-pane>
            <el-tab-pane label="抽检合格" name="completed"></el-tab-pane>
          </el-tabs>
        </template>
        
        <!-- 表格容器 -->
        <el-table
          v-loading="loading"
          :data="qualityInspectionList"
          border
          style="width: 100%;"
          size="small"
          class="compact-table"
          fit
        >
          <template #empty>
            <EmptyTips message="请选择店铺后点击搜索按钮查询数据" />
          </template>
            <!-- 序号列 -->
            <el-table-column type="index" label="序号" width="50" min-width="50" align="center" />
            <!-- 店铺名称列 -->
            <el-table-column label="店铺信息" min-width="120">
            <template #default="scope">
              <div class="shop-info">
                <div class="shop-name">{{ scope.row.shopName }}</div>
                <div v-if="scope.row.shopRemark" class="shop-remark">{{ scope.row.shopRemark }}</div>
              </div>
            </template>
          </el-table-column>
          <!-- 商品信息列 -->
          <el-table-column prop="skuName" label="商品信息" min-width="300">
            <template #default="scope">
              <div class="product-info">
                <img 
                  :src="scope.row.thumbUrl" 
                  class="product-image" 
                  alt="商品图片"
                  @click="handlePreviewImage(scope.row.thumbUrl)"
                >
                <div class="product-details">
                  <div class="product-name">{{ scope.row.skuName }}</div>
                  <div class="product-category">类目：{{ scope.row.catName }}</div>
                  <div class="product-id">
                    <div>SPU ID：{{ scope.row.spuId }}</div>
                    <div>SKC ID：{{ scope.row.productSkcId }}</div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          
        
          <!-- SKU信息列 -->
          <el-table-column label="SKU信息" min-width="150">
            <template #default="scope">
              <div class="sku-info">
                <div class="sku-attribute">属性：{{ scope.row.spec }}</div>
                <div class="sku-id">SKU ID：{{ scope.row.productSkuId }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="货号" min-width="120" show-overflow-tooltip>
            <template #default="scope">
                <div v-if="scope.row.extCode" class="sku-ext-code">{{ scope.row.extCode }}</div>
            </template>
          </el-table-column>
          <!-- 备货单号列 -->
          <el-table-column prop="purchaseNo" label="备货单号" min-width="120" show-overflow-tooltip />
          
          <!-- 最新抽检时间列 -->
          <el-table-column label="最新抽检时间" min-width="150">
            <template #default="scope">
              {{ scope.row.qcResultUpdateTime }}
            </template>
          </el-table-column>
          
          <!-- 抽检详情 -->
          <el-table-column label="抽检详情" min-width="250">
            <template #default="scope">
              <div v-if="scope.row.qcResult === '2'">
                <div v-if="scope.row.flawNameDesc">
                  <span class="defect-label">疵点描述：</span>
                  <span class="defect-content">{{ scope.row.flawNameDesc }}</span>
                </div>
                <div v-if="scope.row.remark">
                  <span class="defect-label">问题备注：</span>
                  <span class="defect-content">{{ scope.row.remark }}</span>
                </div>
                <div v-if="scope.row.attachments && scope.row.attachments.length > 0">
                  <span class="defect-label">证明图片：</span>
                  <div class="attachment-list">
                    <img 
                      v-for="(img, index) in scope.row.attachments" 
                      :key="index" 
                      :src="img" 
                      @click="handlePreviewImage(img)" 
                      class="attachment-image"
                    />
                  </div>
                </div>
              </div>
              <span v-else>抽检合格</span>
            </template>
          </el-table-column>
        </el-table>
      </TableCard>
    </template>
    
    <!-- 固定在底部的分页区域 -->
    <template #pagination>
      <PaginationBar
        v-model:current-page="queryParams.pageNo"
        v-model:page-size="queryParams.pageSize"
        :total="total"
        :show-limit-tip="true"
        :max-limit="10000"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </template>
  
    <!-- 右侧边栏区域 -->
    <template #sidebar>
      <div class="export-sidebar">
        <div class="export-panel-header">
          <h3>导出任务</h3>
        </div>
        <div class="export-panel-content">
          <!-- 如果有正在进行的导出任务，始终显示在最顶部 -->
          <div v-if="exportStatus.exporting" class="export-progress-card">
            <div class="export-progress-header">
              <i class="el-icon-loading"></i>
              <span>正在导出数据</span>
            </div>
            <el-progress 
              :percentage="exportStatus.progress" 
              :format="() => exportStatus.progress + '%'"
              status="primary"
            ></el-progress>
            <div class="export-progress-message">{{ exportStatus.message }}</div>
          </div>
          
          <!-- 显示最近的导出任务记录 -->
          <div class="export-history" v-if="!exportStatus.exporting || exportTaskStore.recentTasks.length > 1">
            <div class="export-task-list">
              <div v-for="task in exportTaskStore.recentTasks.filter(t => !(exportStatus.exporting && t.status === 'processing'))" 
                  :key="task.id" 
                  class="export-task-item">
                <div class="task-info">
                  <div class="task-name">{{ task.name }}</div>
                  <div class="task-meta">
                    <span class="task-time">{{ formatTaskTime(task.createdAt) }}</span>
                    <span :class="['task-status', `status-${task.status}`]">{{ getTaskStatusText(task.status) }}</span>
                  </div>
                </div>
                
                <div class="task-actions">
                  <template v-if="task.status === 'completed'">
                    <el-button type="primary" size="small" @click="handleRedownload(task)">
                      下载文件
                    </el-button>
                  </template>
                </div>
              </div>
            </div>
            
            <div v-if="exportTaskStore.recentTasks.filter(t => t.status === 'completed').length > 0" class="export-task-actions">
              <el-button type="primary" link size="small" @click="exportTaskStore.clearCompletedTasks">
                清除已完成任务
              </el-button>
            </div>
          </div>
          
          <!-- 如果没有任何导出任务且不在导出中，显示空提示 -->
          <div v-if="!exportStatus.exporting && exportTaskStore.recentTasks.length === 0" class="export-empty-tip">
            暂无导出任务
          </div>
        </div>
      </div>
    </template>

    <!-- 弹窗区域 -->
    <template #dialogs>
      <ImagePreview
        v-model:visible="imagePreviewVisible"
        :image-url="currentPreviewImage"
      />

      <!-- 使用统一的导出对话框组件 -->
      <ExportDialog
        v-model:visible="exportDialog.visible"
        data-type="qc"
        :default-file-name="exportDialog.fileName"
        :current-page-data="qualityInspectionList"
        :total="total"
        :fetch-data-fn="fetchQcListData"
        :query-params="queryParams"
        :use-backend-export="true"
      />
    </template>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, onUnmounted, h } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import type { TabsPaneContext } from 'element-plus'
import type { Shop } from '@/types/refund'
import { 
  AppLayout, 
  SearchCard, 
  TableCard, 
  PaginationBar, 
  ImagePreview, 
  EmptyTips,
  TagInput,
  ExportDialog
} from '@/components/temu'
import { useLocalQualityInspectionStore } from '@/store/modules/localQcInspection'
import { useExportTaskStore } from '@/store/modules/exportTask'
import type { ExportTask } from '@/store/modules/exportTask'
import type { 
  LocalQualityInspectionRequestParams,
  LocalQualityInspectionItem,
  LocalQualityInspectionResponse
} from '@/types/local/qcInspection'
import { Download, Check, Close } from '@element-plus/icons-vue'
import axios from 'axios'
import { getToken } from '@/utils/auth'

// 抽检结果store
const qcStore = useLocalQualityInspectionStore()

// 导出任务store
const exportTaskStore = useExportTaskStore()

// 加载状态
const loading = computed(() => qcStore.loading)

// 店铺列表
const shops = ref<Shop[]>([])

// 图片预览相关
const imagePreviewVisible = ref(false)
const currentPreviewImage = ref('')

// 多值输入相关
const idType = ref<'sku' | 'skc'>('sku')

// 抽检结果列表
const qualityInspectionList = ref<LocalQualityInspectionItem[]>([])

// 总记录数
const total = ref(0)

// 时间范围，默认为最近7天
const timeRange = ref<[string, string] | null>(null)

// 查询参数
const queryParams = reactive<LocalQualityInspectionRequestParams>({
  shopIds: [],
  qcResultUpdateTimeBegin: undefined,
  qcResultUpdateTimeEnd: undefined,
  pageNo: 1,
  pageSize: 10,
  skuIdList: [],
  skcIdList: [],
  purchaseNo: [],
  qcResult: '2', // 默认查询抽检不合格的记录
  skuNameKeyword: undefined
})

// 标签页相关
const activeTab = ref('failed')

// 导出相关
const exportDialog = reactive({
  visible: false,
  fileName: ''
})

// 导出状态管理
const exportStatus = reactive({
  exporting: false,
  progress: 0,
  message: ''
})

// 添加一个变量用于跟踪全选状态
const isAllSelected = ref(false);

// 添加过滤后的店铺列表
const filteredShops = ref<Shop[]>([]);
// 用于存储过滤搜索关键词
const filterKeyword = ref('');

// 初始化
onMounted(() => {
  // 只加载店铺列表，不自动加载抽检结果列表
  loadShops()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  // 组件销毁时移除监听器
  window.removeEventListener('resize', handleResize)
})

// 处理窗口大小变化
const handleResize = () => {
  // 窗口大小变化时的处理逻辑
}

// 加载店铺列表
const loadShops = async () => {
  const result = await qcStore.getUserShops()
  shops.value = result || []
  filteredShops.value = [...shops.value] // 初始化过滤后的店铺列表
}

// 过滤店铺方法
const filterShops = (query: string) => {
  filterKeyword.value = query
  if (query) {
    filteredShops.value = shops.value.filter(shop => 
      shop.shopName.toLowerCase().includes(query.toLowerCase())
    )
    
    // 当过滤条件发生变化时，重置全选状态
    isAllSelected.value = false;
    
    // 如果当前已选中店铺，保留它们（如果它们在过滤结果中）
    if (queryParams.shopIds && queryParams.shopIds.length > 0) {
      // 保留那些在过滤结果中的店铺ID
      queryParams.shopIds = queryParams.shopIds.filter(id => 
        filteredShops.value.some(shop => shop.shopId === id)
      );
    }
  } else {
    filteredShops.value = [...shops.value]
    
    // 检查是否全部店铺都被选中
    if (queryParams.shopIds && shops.value.length > 0) {
      isAllSelected.value = queryParams.shopIds.length === shops.value.length &&
        shops.value.every(shop => queryParams.shopIds!.includes(shop.shopId));
    }
  }
}

// 加载抽检结果列表
const loadQualityInspectionList = async () => {
  // 验证分页参数，不允许查询超过10000条数据
  const totalRequested = queryParams.pageNo * queryParams.pageSize
  if (totalRequested > 10000) {
    ElMessage.warning('只能查询前10000条数据')
    return
  }
  
  try {
    const response = await qcStore.getLocalQualityInspectionList(queryParams)
    console.log('API响应数据:', response) // 添加日志调试
    
    // 使用类型断言处理响应
    const result = response as unknown as LocalQualityInspectionResponse
    
    if (result && result.code === 200) {
      if (result.data) {
        qualityInspectionList.value = result.data.items || []
        total.value = result.data.total || 0
        
        // 调试输出数据
        console.log('解析后的数据列表:', qualityInspectionList.value)
        console.log('总记录数:', total.value)
      } else {
        resetListData()
        console.warn('API返回成功但没有data数据')
      }
    } else {
      resetListData()
      if (result && result.message) {
        ElMessage.error(result.message)
      } else {
        ElMessage.error('获取抽检结果明细失败')
      }
    }
  } catch (error) {
    resetListData()
    ElMessage.error('获取抽检结果明细失败')
    console.error('获取抽检明细出错:', error)
  }
}

// 重置列表数据
const resetListData = () => {
  qualityInspectionList.value = []
  total.value = 0
}

// 处理查询
const handleQuery = () => {
  // 验证必须选择店铺
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请选择店铺')
    return
  }
  
  queryParams.pageNo = 1
  qcStore.setShopIds(queryParams.shopIds)
  loadQualityInspectionList()
}

// 处理清除所有店铺选择
const handleClearShops = () => {
  // 重置全选状态
  isAllSelected.value = false;
  queryParams.shopIds = [];
  qcStore.setShopIds([]);
}

// 重置查询
const resetQuery = () => {
  queryParams.shopIds = []
  // 重置全选状态
  isAllSelected.value = false;
  
  // 清空时间范围
  timeRange.value = null
  queryParams.qcResultUpdateTimeBegin = undefined
  queryParams.qcResultUpdateTimeEnd = undefined
  
  // 重置多值输入
  queryParams.skuIdList = []
  queryParams.skcIdList = []
  queryParams.purchaseNo = []
  queryParams.skuNameKeyword = undefined
  idType.value = 'sku'
  
  // 保留当前标签页对应的查询条件
  if (activeTab.value === 'completed') {
    queryParams.qcResult = '1'
  } else if (activeTab.value === 'failed') {
    queryParams.qcResult = '2'
  }
  
  queryParams.pageNo = 1
  
  // 重置store中的查询条件
  qcStore.resetQuery()
  
  // 清空当前数据，不重新加载
  resetListData()
}

// 处理时间范围变化
const handleTimeRangeChange = (val: [string, string] | null) => {
  if (val && val.length === 2) {
    timeRange.value = val
    queryParams.qcResultUpdateTimeBegin = val[0]
    queryParams.qcResultUpdateTimeEnd = val[1]
    qcStore.setTimeRange(val)
  } else {
    // 如果手动清除，清空时间范围
    timeRange.value = null
    queryParams.qcResultUpdateTimeBegin = undefined
    queryParams.qcResultUpdateTimeEnd = undefined
    qcStore.setTimeRange(null)
  }
}

// 处理每页显示数量变化
const handleSizeChange = (size: number) => {
  // 检查是否会超出10000条数据限制
  if (queryParams.pageNo * size > 10000) {
    // 如果会超出限制，调整pageNo使其不超过限制
    const maxPageNo = Math.floor(10000 / size)
    queryParams.pageNo = maxPageNo
    ElMessage.warning(`每页${size}条时，最多只能查看到第${maxPageNo}页（共10000条数据）`)
  }
  
  queryParams.pageSize = size
  loadQualityInspectionList()
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  // 检查是否会超出10000条数据限制
  if (page * queryParams.pageSize > 10000) {
    ElMessage.warning('只能查询前10000条数据')
    // 不更新页码，保持在当前页
    return
  }
  
  queryParams.pageNo = page
  loadQualityInspectionList()
}

// 处理店铺选择
const handleShopChange = (shopIds: number[]) => {
  // 检查是否点击了全选选项
  if (shopIds.includes(-1)) {
    // 如果当前不是全选状态，则进行全选
    if (!isAllSelected.value) {
      // 标记为全选状态
      isAllSelected.value = true;
      
      // 全选所有店铺（过滤后的）
      let allShopIds: number[] = []
      
      // 如果有搜索关键词，只选择过滤后的店铺
      if (filterKeyword.value) {
        allShopIds = filteredShops.value.map(shop => shop.shopId)
      } else {
        // 否则选择所有店铺
        allShopIds = shops.value.map(shop => shop.shopId)
      }
      
      // 移除全选选项值(-1)
      queryParams.shopIds = allShopIds;
      
      // 避免-1被包含在选中项中
      setTimeout(() => {
        if (queryParams.shopIds && queryParams.shopIds.includes(-1)) {
          const index = queryParams.shopIds.indexOf(-1);
          if (index > -1) {
            queryParams.shopIds.splice(index, 1);
          }
        }
      }, 0);
    } else {
      // 如果当前已经是全选状态，则取消全选
      isAllSelected.value = false;
      queryParams.shopIds = [];
    }
  } else {
    // 如果选择了具体的店铺且数量与总店铺数相同，标记为全选状态
    const totalShops = filterKeyword.value ? filteredShops.value.length : shops.value.length;
    isAllSelected.value = shopIds.length > 0 && shopIds.length === totalShops;
    
    // 更新选中的店铺
    queryParams.shopIds = shopIds;
  }
  
  // 调用store方法时确保传入的是数组
  qcStore.setShopIds(queryParams.shopIds || []);
  console.log('选择店铺:', queryParams.shopIds, '全选状态:', isAllSelected.value);
}

// 处理图片预览
const handlePreviewImage = (imageUrl: string) => {
  currentPreviewImage.value = imageUrl
  imagePreviewVisible.value = true
}

// 处理标签页切换
const handleTabClick = (tab: TabsPaneContext) => {
  if (tab.props.name === 'completed') {
    queryParams.qcResult = '1' // 合格
  } else if (tab.props.name === 'failed') {
    queryParams.qcResult = '2' // 不合格
  }
  // 如果有选中店铺，自动刷新数据
  if (queryParams.shopIds && queryParams.shopIds.length > 0) {
    loadQualityInspectionList()
  }
}

// 封装获取抽检数据的函数供导出组件使用
const fetchQcListData = async (params: any) => {
  try {
    // 构造导出请求参数
    const exportParams = {
      ...params,
      queryParams: {
        ...queryParams,
        ...(params.queryParams || {})
      }
    };
    
    // 创建导出任务
    const token = getToken();
    const response = await axios({
      method: 'post',
      url: '/api/local/qualityInspection/exportExcel',
      data: exportParams,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
      }
    });
    
    // 检查响应状态
    if (response.data.code !== 200) {
      if (params.taskId) {
        exportTaskStore.failTask(params.taskId, response.data.message || '创建导出任务失败');
      }
      return { success: false };
    }
    
    // 获取任务ID
    const serverTaskId = response.data.data.taskId;
    if (!serverTaskId) {
      if (params.taskId) {
        exportTaskStore.failTask(params.taskId, '获取导出任务ID失败');
      }
      return { success: false };
    }
    
    // 开始轮询任务进度
    pollExportProgress(serverTaskId, params.taskId);
    
    return { success: true, data: { taskId: serverTaskId } };
  } catch (error: any) {
    console.error('导出失败:', error);
    if (params.taskId) {
      exportTaskStore.failTask(params.taskId, error.message || '导出请求失败');
    }
    return { success: false };
  }
};

// 导出按钮点击事件
const handleExport = () => {
  if (!queryParams.shopIds || queryParams.shopIds.length === 0) {
    ElMessage.warning('请选择至少一个店铺')
    return
  }
  
  if (qualityInspectionList.value.length === 0) {
    ElMessage.warning('没有可导出的数据')
    return
  }

  // 生成默认文件名：店铺备注 + 日期时间
  let fileName = '本地抽检明细';
  
  // 获取选中店铺的备注
  if (shops.value && shops.value.length > 0) {
    // 使用非空断言，因为前面已经检查过shopIds是否存在
    const shopIds = queryParams.shopIds!;
    // 查找选中店铺的备注
    const selectedShops = shops.value.filter(shop => shopIds.includes(shop.shopId));
    if (selectedShops.length > 0) {
      // 提取店铺备注，最多使用3个店铺备注，避免文件名过长
      const shopRemarks = selectedShops.slice(0, 3).map(shop => shop.remark || shop.shopName);
      fileName = shopRemarks.join('-');
      
      // 如果选择的店铺超过3个，添加省略标记
      if (selectedShops.length > 3) {
        fileName += '等';
      }
    }
  }
  
  // 添加日期时间后缀
  const now = new Date();
  const dateStr = now.getFullYear() + 
    (now.getMonth() + 1).toString().padStart(2, '0') + 
    now.getDate().toString().padStart(2, '0');
  const timeStr = now.getHours().toString().padStart(2, '0') + 
    now.getMinutes().toString().padStart(2, '0');
  
  fileName += '_' + dateStr + timeStr;
  
  console.log('导出文件名:', fileName); // 添加调试输出
  
  exportDialog.fileName = fileName;
  exportDialog.visible = true;
};

// 轮询导出进度
const pollExportProgress = async (serverTaskId: string, exportTaskId: string) => {
  try {
    const token = getToken();
    let completed = false;
    let failedAttempts = 0; // 连续失败计数
    const MAX_FAILED_ATTEMPTS = 3; // 最大连续失败次数
    
    // 轮询函数
    while (!completed && failedAttempts < MAX_FAILED_ATTEMPTS) {
      try {
        // 请求进度
        const progressResponse = await axios({
          method: 'get',
          url: `/api/local/qualityInspection/exportProgress/${serverTaskId}`,
          headers: {
            'Authorization': 'Bearer ' + token
          }
        });
        
        // 检查响应状态
        if (progressResponse.data.code !== 200) {
          failedAttempts++;
          await new Promise(resolve => setTimeout(resolve, 1000)); // 失败后等待1秒重试
          continue;
        }
        
        // 重置失败计数
        failedAttempts = 0;
        
        // 获取进度数据
        const progressData = progressResponse.data.data;
        
        // 更新任务列表中的进度
        exportTaskStore.updateTaskProgress(exportTaskId, progressData.progress);
        
        // 检查任务状态
        if (progressData.status === 'completed') {
          // 任务完成，触发下载
          ElMessage.success('导出完成，正在下载文件...');
          
          // 下载文件
          await downloadExcelFile(serverTaskId);
          
          // 标记完成
          completed = true;
          exportTaskStore.completeTask(exportTaskId);
          break;
        } else if (progressData.status === 'failed') {
          // 任务失败
          ElMessage.error('导出失败: ' + progressData.message);
          exportTaskStore.failTask(exportTaskId, progressData.message || '导出失败');
          break;
        }
        
        // 等待一段时间后继续轮询
        // 根据进度设置轮询间隔，进度越大，轮询间隔越长
        const pollInterval = Math.min(2000, 500 + Math.floor(progressData.progress / 10) * 300);
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      } catch (error: any) {
        console.error('轮询进度失败:', error);
        failedAttempts++;
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    // 如果连续失败超过最大次数
    if (failedAttempts >= MAX_FAILED_ATTEMPTS) {
      ElMessage.error('无法获取导出进度，请稍后在系统中查看导出结果');
      exportTaskStore.failTask(exportTaskId, '无法获取导出进度');
    }
    
  } catch (error: any) {
    ElMessage.error('轮询导出进度失败: ' + (error.message || '未知错误'));
    exportTaskStore.failTask(exportTaskId, error.message || '轮询进度失败');
  }
}

// 添加下载文件的函数，使用fetch API在请求头中携带认证token
const downloadExcelFile = async (taskId: string) => {
  try {
    const token = getToken();
    
    ElMessage.success('正在准备下载，请稍候...');
    
    // 创建下载链接
    const downloadUrl = `/api/local/qualityInspection/downloadExcel/${taskId}`;
    
    // 使用fetch API发起带认证头的请求
    const response = await fetch(downloadUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    // 检查响应状态
    if (!response.ok) {
      // 尝试解析错误响应
      try {
        const errorData = await response.json();
        ElMessage.error(errorData.message || `下载失败: ${response.status} ${response.statusText}`);
      } catch (parseError) {
        ElMessage.error(`下载失败: ${response.status} ${response.statusText}`);
      }
      return;
    }
    
    // 获取文件名 - 改进文件名处理
    let filename = '本地抽检明细.xlsx'; // 默认文件名
    
    // 1. 优先使用导出对话框中设置的文件名
    if (exportDialog.fileName && exportDialog.fileName.trim() !== '') {
      filename = `${exportDialog.fileName}.xlsx`;
    }
    
    // 2. 尝试从Content-Disposition获取文件名并正确解码
    const contentDisposition = response.headers.get('Content-Disposition');
    if (contentDisposition) {
      // 匹配filename或filename*=utf-8''格式
      const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
      const filenameUtf8Regex = /filename\*=UTF-8''([^;]*)/i;
      
      // 先尝试获取UTF-8编码的文件名
      const utf8Match = filenameUtf8Regex.exec(contentDisposition);
      if (utf8Match && utf8Match[1]) {
        try {
          // 对URL编码的文件名进行解码
          filename = decodeURIComponent(utf8Match[1]);
          console.log('从Content-Disposition获取到UTF-8文件名:', filename);
        } catch (e) {
          console.error('解码文件名失败:', e);
        }
      } else {
        // 尝试获取普通文件名
        const match = filenameRegex.exec(contentDisposition);
        if (match && match[1]) {
          let extractedName = match[1].replace(/['"]/g, '');
          
          // 尝试处理可能的URL编码
          try {
            if (extractedName.includes('%')) {
              extractedName = decodeURIComponent(extractedName);
            }
            filename = extractedName;
            console.log('从Content-Disposition获取到文件名:', filename);
          } catch (e) {
            console.error('处理文件名失败:', e);
          }
        }
      }
    }
    
    // 确保文件名以.xlsx结尾
    if (!filename.toLowerCase().endsWith('.xlsx')) {
      filename += '.xlsx';
    }
    
    console.log('最终使用的文件名:', filename);
    
    // 将响应转换为blob
    const blob = await response.blob();
    
    // 创建下载链接并触发下载
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    
    // 清理资源
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    
    ElMessage.success('文件下载已开始，请查看浏览器下载管理器');
  } catch (error: any) {
    console.error('下载文件失败:', error);
    ElMessage.error('下载文件失败: ' + (error.message || '未知错误'));
  }
};

// 格式化导出任务时间
const formatTaskTime = (date: Date) => {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  
  if (diffMins < 1) {
    return '刚刚'
  } else if (diffMins < 60) {
    return `${diffMins} 分钟前`
  } else if (diffMins < 24 * 60) {
    const hours = Math.floor(diffMins / 60)
    return `${hours} 小时前`
  } else {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    
    return `${year}-${month}-${day} ${hour}:${minute}`
  }
}

// 获取任务状态文本
const getTaskStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return '准备中'
    case 'processing':
      return '进行中'
    case 'completed':
      return '已完成'
    case 'failed':
      return '失败'
    default:
      return status
  }
}

// 处理任务列表中的下载按钮
const handleRedownload = (task: any) => {
  try {
    ElMessage.info('正在下载文件...');
    // 我们需要从task中提取服务器端的taskId才能下载
    // 通常这需要额外的数据结构，但由于前面我们没有实际保存这个映射关系
    // 这里先使用简单的提示信息
    ElMessage.warning('请使用正在进行的导出任务进行下载');
  } catch (error) {
    console.error('下载失败:', error);
    ElMessage.error('下载失败');
  }
}
</script>

<style scoped>
/* 搜索表单样式 */
.search-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.search-row {
  display: flex;
  gap: 20px;
  align-items: flex-end;
}

/* 第二行特殊处理 */
.search-row.second-row {
  margin-top: 10px;
  justify-content: space-between;
}

.search-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.search-label {
  color: #606266;
  margin-bottom: 5px;
  font-size: 13px;
}

.search-input {
  width: 100%;
}

.date-picker {
  width: 100%;
}

/* 商品名称项单独设置宽度 */
.product-name-item {
  flex: 0 0 auto;
  width: 400px;
}

/* 按钮组样式 */
.search-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  align-items: center;
}

.search-buttons .el-button {
  padding: 7px 15px;
}

/* 表格相关样式 */
.product-info {
  display: flex;
  align-items: flex-start;
  padding: 5px 0;
}

.product-image {
  width: 60px;
  height: 60px;
  margin-right: 15px;
  object-fit: contain;
  cursor: pointer;
  transition: opacity 0.3s;
  border: 1px solid #eee;
}

.product-image:hover {
  opacity: 0.8;
}

.product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.product-name {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 兼容WebKit浏览器 */
  line-clamp: 2; /* 标准属性，未来兼容 */
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 2.8em; /* 回退方案：限制最大高度 */
}

.product-category {
  font-size: 12px;
  color: #909399;
}

.product-id {
  font-size: 12px;
  color: #909399;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.sku-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding: 5px 0;
}

.sku-attribute {
  font-size: 14px;
  font-weight: 500;
}

.sku-id {
  font-size: 12px;
  color: #909399;
}

.sku-ext-code {
  font-size: 12px;
  color: #909399;
}

/* 添加自适应表格样式 */
.compact-table {
  font-size: 12px;
}

.compact-table :deep(.el-table__cell) {
  padding: 6px 0;
}

.compact-table :deep(.cell) {
  line-height: 1.3;
  padding-left: 5px;
  padding-right: 5px;
  white-space: normal;
}

.compact-table :deep(.el-table__header th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
  text-align: center;
}

.compact-table :deep(.el-table--border th) {
  padding: 8px 0;
}

/* 确保表格能够自适应容器 */
:deep(.el-table) {
  width: 100% !important;
  table-layout: fixed;
}

:deep(.el-table__body-wrapper) {
  overflow-x: auto;
}

/* 抽检详情相关样式 */
.defect-label {
  font-weight: bold;
  margin-right: 4px;
  color: #606266;
}

.defect-content {
  color: #f56c6c;
  word-break: break-all;
}

.attachment-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 4px;
}

.attachment-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #eee;
}

.attachment-image:hover {
  opacity: 0.8;
}

/* 导出提示样式 */
.export-tip {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  gap: 5px;
}

/* 导出信息块样式 */
.export-info-block {
  margin-top: 15px;
}

.shop-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.shop-name {
  font-size: 14px;
  font-weight: bold;
}

.shop-remark {
  font-size: 14px;
  color: #409EFF;
  line-height: 1.3;
  word-break: break-all;
}

/* 导出状态提示样式 */
:deep(.export-progress-message) {
  min-width: 240px;
}

:deep(.export-notification) {
  display: flex;
  align-items: center;
}

:deep(.export-notification .el-icon-loading) {
  font-size: 16px;
  margin-right: 8px;
}

/* 导出进度指示器样式 */
.export-progress-indicator {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
  width: 300px;
}

.export-progress-card {
  padding: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background-color: white;
  border-radius: 6px;
}

.export-progress-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-weight: bold;
  font-size: 16px;
}

.export-progress-header i {
  margin-right: 10px;
  font-size: 20px;
  color: #409EFF;
}

.export-progress-message {
  margin-top: 10px;
  font-size: 14px;
  color: #606266;
}

/* 添加导出侧边栏样式 */
.export-sidebar {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.export-panel-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
}

.export-panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.export-panel-content {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

.export-panel-content .export-progress-card {
  box-shadow: none;
  border: 1px solid #f0f0f0;
  padding: 16px;
}

/* 导出历史记录样式 */
.export-history {
  margin-top: 16px;
}

.export-task-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.export-task-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
  background-color: #fafafa;
}

.task-info {
  margin-bottom: 12px;
}

.task-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.task-status {
  font-weight: 500;
}

.status-completed {
  color: #67c23a;
}

.status-processing {
  color: #409eff;
}

.status-pending {
  color: #e6a23c;
}

.status-failed {
  color: #f56c6c;
}

.task-actions {
  display: flex;
  justify-content: flex-end;
}

.export-task-actions {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.export-empty-tip {
  text-align: center;
  color: #909399;
  padding: 32px 0;
  font-size: 14px;
}
</style> 