package com.xiao.temu.modules.system.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.system.entity.SysRole;
import com.xiao.temu.infrastructure.exporter.dto.ExportRequestDTO;
import com.xiao.temu.modules.system.dto.UserExportDTO;
import com.xiao.temu.security.annotation.RequiresPermission;
import com.xiao.temu.security.model.UserDetailsImpl;
import com.xiao.temu.modules.system.service.RoleService;
import com.xiao.temu.modules.system.service.UserService;
import com.xiao.temu.infrastructure.excel.ExcelUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/system/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;
    private final RoleService roleService;

    /**
     * 获取用户列表
     */
    @GetMapping("/list")
    @RequiresPermission("system:user:list")
    public ApiResponse getUserList(SysUser user,
                                  @RequestParam(defaultValue = "1") Integer pageNum,
                                  @RequestParam(defaultValue = "10") Integer pageSize,
                                  @RequestParam(required = false, defaultValue = "createTime") String orderBy,
                                  @RequestParam(required = false, defaultValue = "desc") String orderDir) {
        Page<SysUser> page = new Page<>(pageNum, pageSize);
        return ApiResponse.success(userService.getUserList(user, page, orderBy, orderDir));
    }

    /**
     * 获取用户详细信息
     */
    @GetMapping("/{userId}")
    @RequiresPermission("system:user:query")
    public ApiResponse getUser(@PathVariable Long userId) {
        SysUser user = userService.getUserById(userId);
        if (user == null) {
            return ApiResponse.error("用户不存在");
        }
        
        // 查询用户的角色ID列表
        List<Long> roleIds = userService.getUserRoleIds(userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("user", user);
        result.put("roleIds", roleIds);
        
        return ApiResponse.success(result);
    }

    /**
     * 新增用户
     */
    @PostMapping
    @RequiresPermission("system:user:add")
    public ApiResponse addUser(@Validated @RequestBody SysUser user) {
        // 校验用户名是否唯一
        if (!userService.checkUsernameUnique(user.getUsername())) {
            return ApiResponse.error("新增用户'" + user.getUsername() + "'失败，登录账号已存在");
        }
        
        // 插入用户信息
        int rows = userService.insertUser(user);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("新增用户失败");
    }

    /**
     * 修改用户
     */
    @PutMapping
    @RequiresPermission("system:user:edit")
    public ApiResponse updateUser(@Validated @RequestBody SysUser user) {
        // 更新用户信息
        int rows = userService.updateUser(user);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("修改用户失败");
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{userIds}")
    @RequiresPermission("system:user:remove")
    public ApiResponse deleteUser(@PathVariable Long[] userIds) {
        // 删除用户
        int rows = userService.deleteUsers(userIds);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("删除用户失败");
    }

    /**
     * 重置密码
     */
    @PutMapping("/resetPassword")
    @RequiresPermission("system:user:resetPassword")
    public ApiResponse resetPassword(@RequestParam Long userId, @RequestParam String password) {
        // 重置密码
        int rows = userService.resetPassword(userId, password);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("重置密码失败");
    }

    /**
     * 修改用户状态
     */
    @PutMapping("/changeStatus")
    @RequiresPermission("system:user:edit")
    public ApiResponse changeStatus(@RequestParam Long userId, @RequestParam String status) {
        // 修改用户状态
        int rows = userService.changeStatus(userId, status);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("修改用户状态失败");
    }

    /**
     * 分配角色
     */
    @PutMapping("/assignRoles")
    @RequiresPermission("system:user:edit")
    public ApiResponse assignRoles(@RequestParam Long userId, @RequestBody Long[] roleIds) {
        // 分配角色
        int rows = userService.assignRoles(userId, roleIds);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("分配角色失败");
    }

    /**
     * 获取用户所有角色
     */
    @GetMapping("/roles/{userId}")
    @RequiresPermission("system:user:query")
    public ApiResponse getUserRoles(@PathVariable Long userId) {
        // 查询用户的角色列表
        return ApiResponse.success(roleService.getRolesByUserId(userId));
    }

    /**
     * 获取个人资料
     */
    @GetMapping("/profile")
    public ApiResponse getUserProfile() {
        // 获取当前登录用户ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !(authentication.getPrincipal() instanceof UserDetailsImpl)) {
            return ApiResponse.error("用户未登录");
        }
        
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        Long userId = userDetails.getUserId();
        
        // 获取用户信息
        SysUser user = userService.getUserById(userId);
        if (user == null) {
            return ApiResponse.error("用户不存在");
        }
        
        // 出于安全考虑，不返回密码
        user.setPassword(null);
        
        // 获取用户角色信息
        List<SysRole> roles = roleService.getRolesByUserId(userId);
        
        // 创建包含用户信息和角色的Map
        Map<String, Object> result = new HashMap<>();
        result.put("user", user);
        result.put("roles", roles);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 更新个人资料
     */
    @PutMapping("/profile")
    public ApiResponse updateUserProfile(@RequestBody SysUser user) {
        // 获取当前登录用户ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !(authentication.getPrincipal() instanceof UserDetailsImpl)) {
            return ApiResponse.error("用户未登录");
        }
        
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        Long userId = userDetails.getUserId();
        
        // 设置用户ID，确保只能修改自己的资料
        user.setUserId(userId);
        
        // 不允许修改用户名和密码
        user.setUsername(null);
        user.setPassword(null);
        
        // 更新用户资料
        int rows = userService.updateUser(user);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("更新个人资料失败");
    }

    /**
     * 更新密码
     */
    @PutMapping("/updatePassword")
    public ApiResponse updatePassword(@RequestParam String oldPassword, @RequestParam String newPassword) {
        // 获取当前登录用户ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !(authentication.getPrincipal() instanceof UserDetailsImpl)) {
            return ApiResponse.error("用户未登录");
        }
        
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        Long userId = userDetails.getUserId();
        
        // 检查旧密码是否正确
        if (!userService.checkPassword(userId, oldPassword)) {
            return ApiResponse.error("旧密码不正确");
        }
        
        // 更新密码
        int rows = userService.resetPassword(userId, newPassword);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("修改密码失败");
    }

    /**
     * 获取具有特定权限的用户列表
     */
    @GetMapping("/listByPermission")
    @RequiresPermission("system:user:list")
    public ApiResponse getUsersByPermission(@RequestParam String permission) {
        // 获取具有特定权限的用户列表
        List<SysUser> users = userService.getUsersByPermission(permission);
        return ApiResponse.success(users);
    }

    /**
     * 获取具有特定角色的用户列表
     */
    @GetMapping("/listByRoleKey")
    @RequiresPermission("system:user:list")
    public ApiResponse getUsersByRoleKey(@RequestParam String roleKey) {
        // 获取具有特定角色的用户列表
        List<SysUser> users = userService.getUsersByRoleKey(roleKey);
        return ApiResponse.success(users);
    }

    /**
     * 获取生产组长角色的用户列表（针对生产组管理的特殊接口）
     * 该接口主要用于允许生产组长在创建生产组时选择自己作为负责人
     */
    @GetMapping("/production/listByRoleKey")
    @RequiresPermission("production:leader:list")
    public ApiResponse getProductionLeaderUsers(@RequestParam String roleKey) {
        // 获取当前用户ID
        Long currentUserId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        // 获取具有特定角色的用户列表
        List<SysUser> users = userService.getUsersByRoleKey(roleKey);
        
        // 如果不是管理员，则过滤用户列表，只保留当前用户自己
        if (!isAdmin) {
            users = users.stream()
                .filter(user -> user.getUserId().equals(currentUserId))
                .collect(java.util.stream.Collectors.toList());
        }
        
        return ApiResponse.success(users);
    }

    /**
     * 获取运营组长角色的用户列表（针对运营组管理的特殊接口）
     * 该接口主要用于允许运营组长在创建运营组时选择自己作为负责人
     */
    @GetMapping("/operation/listByRoleKey")
    @RequiresPermission("operation:leader:list")
    public ApiResponse getOperationLeaderUsers(@RequestParam String roleKey) {
        // 获取当前用户ID
        Long currentUserId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        // 获取具有特定角色的用户列表
        List<SysUser> users = userService.getUsersByRoleKey(roleKey);
        
        // 如果不是管理员，则过滤用户列表，只保留当前用户自己
        if (!isAdmin) {
            users = users.stream()
                .filter(user -> user.getUserId().equals(currentUserId))
                .collect(java.util.stream.Collectors.toList());
        }
        
        return ApiResponse.success(users);
    }

    /**
     * 导出用户数据
     */
    @PostMapping("/export")
    @RequiresPermission("system:user:export")
    public void exportUsers(@Validated @RequestBody ExportRequestDTO exportDTO, HttpServletResponse response) throws IOException {
        // 获取导出数据
        List<UserExportDTO> userList = userService.getExportData(exportDTO);
        
        // 设置文件名和工作表名
        String fileName = exportDTO.getFileName() != null ? exportDTO.getFileName() : "用户数据";
        String sheetName = exportDTO.getSheetName() != null ? exportDTO.getSheetName() : "用户列表";
        
        // 导出Excel
        ExcelUtils.export(response, userList, fileName, sheetName, UserExportDTO.class);
    }
    
    /**
     * 获取用户导入模板
     */
    @GetMapping("/importTemplate")
    @RequiresPermission("system:user:import")
    public void getImportTemplate(HttpServletResponse response) throws IOException {
        userService.getImportTemplate(response);
    }
    
    /**
     * 导入用户数据
     */
    @PostMapping("/import")
    @RequiresPermission("system:user:import")
    public ApiResponse importUsers(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return ApiResponse.error("请选择要上传的文件");
        }
        
        try {
            // 导入Excel
            Map<String, Object> result = userService.importUserFromExcel(file.getInputStream());
            
            // 格式化结果消息
            Integer successCount = (Integer) result.get("successCount");
            Integer failCount = (Integer) result.get("failCount");
            List<String> errorMsgs = (List<String>) result.get("errorMsgs");
            
            StringBuilder message = new StringBuilder();
            message.append("导入成功: ").append(successCount).append("条; ");
            message.append("导入失败: ").append(failCount).append("条");
            
            // 如果存在错误信息，返回错误信息
            if (failCount > 0 && errorMsgs != null && !errorMsgs.isEmpty()) {
                result.put("message", message.toString());
                return ApiResponse.success(result);
            }
            
            return ApiResponse.success(message.toString());
        } catch (IOException e) {
            log.error("导入用户数据出错", e);
            return ApiResponse.error("导入用户数据出错: " + e.getMessage());
        }
    }
} 