// Excel Worker 文件
// 导入所需的库
importScripts('./exceljs.min.js');
importScripts('./FileSaver.min.js');

// 图片缓存
const imageCache = new Map();

// 从图片URL加载图片(带缓存)
async function loadImageFromUrlWithCache(url) {
  // 检查缓存
  if (imageCache.has(url)) {
    return imageCache.get(url);
  }
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      mode: 'cors',
      cache: 'no-cache',
      headers: {
        'Accept': 'image/jpeg,image/png,image/*',
        'Referer': self.origin || 'https://www.temu.com'
      }
    });
    
    if (!response.ok) {
      throw new Error(`无法加载图片: ${response.statusText}`);
    }
    
    const imageData = await response.arrayBuffer();
    
    // 添加到缓存
    imageCache.set(url, imageData);
    
    return imageData;
  } catch (error) {
    console.error('加载图片失败:', error);
    throw error;
  }
}

// 收集所有需要处理的图片任务
function collectImageTasks(data, columns) {
  const imageTasks = [];
  
  for (let rowIndex = 0; rowIndex < data.length; rowIndex++) {
    const row = data[rowIndex];
    
    for (let colIndex = 0; colIndex < columns.length; colIndex++) {
      const column = columns[colIndex];
      
      if (column.isImage && row[column.key]) {
        const imageUrl = row[column.key];
        if (!imageUrl) continue;
        
        imageTasks.push({
          url: imageUrl,
          rowIndex,
          colIndex,
          cellReference: `${String.fromCharCode(65 + colIndex)}${rowIndex + 2}`
        });
      }
    }
  }
  
  return imageTasks;
}

// 并行下载图片，控制并发数
async function downloadImagesInParallel(imageTasks, onProgress) {
  const results = [];
  const CONCURRENT_LIMIT = 10; // 控制并发数量
  let completed = 0;
  
  // 分批处理所有图片任务
  for (let i = 0; i < imageTasks.length; i += CONCURRENT_LIMIT) {
    const batch = imageTasks.slice(i, i + CONCURRENT_LIMIT);
    
    // 并行下载当前批次的所有图片
    const batchPromises = batch.map(async (task) => {
      try {
        const imageData = await loadImageFromUrlWithCache(task.url);
        return {
          ...task,
          imageData,
          success: true
        };
      } catch (error) {
        return {
          ...task, 
          success: false,
          error: error.message
        };
      }
    });
    
    // 等待当前批次完成
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
    
    // 更新进度
    completed += batch.length;
    const progress = Math.floor((completed / imageTasks.length) * 70); // 图片下载占总进度的70%
    onProgress(progress + 20); // 从20%开始计算
  }
  
  return results;
}

// 将数据导出为Excel，包含图片
async function exportToExcelWithImages(data, columns, fileName, sheetName = 'Sheet1') {
  // 创建工作簿
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet(sheetName);

  // 设置列
  worksheet.columns = columns.map(col => ({
    header: col.header,
    key: col.key,
    width: col.width || 15
  }));

  // 添加数据
  worksheet.addRows(data);
  
  // 通知开始处理
  self.postMessage({ type: 'progress', progress: 10, message: '准备导出数据...' });
  
  // 设置行高和表头样式
  const IMAGE_ROW_HEIGHT = 100; // 包含图片的行高度
  
  // 设置表头行样式
  worksheet.getRow(1).height = 30;
  worksheet.getRow(1).font = { bold: true };
  worksheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' };
  
  // 处理所有数据行的高度和对齐方式
  for (let rowIndex = 0; rowIndex < data.length; rowIndex++) {
    const excelRowIndex = rowIndex + 2; // Excel行索引从1开始，第1行是表头
    
    // 设置行高
    worksheet.getRow(excelRowIndex).height = IMAGE_ROW_HEIGHT;
    
    // 设置所有单元格垂直居中
    worksheet.getRow(excelRowIndex).alignment = { vertical: 'middle' };
    
    // 处理带格式化器的列
    for (let colIndex = 0; colIndex < columns.length; colIndex++) {
      const column = columns[colIndex];
      if (column.formatter && !column.isImage && data[rowIndex][column.key] !== undefined) {
        worksheet.getCell(`${String.fromCharCode(65 + colIndex)}${rowIndex + 2}`).value = 
          column.formatter(data[rowIndex][column.key]);
      }
    }
  }
  
  // 通知进入图片处理阶段
  self.postMessage({ type: 'progress', progress: 20, message: '正在并行下载图片...' });
  
  // 收集所有图片任务
  const imageTasks = collectImageTasks(data, columns);
  
  // 并行下载图片
  const imageResults = await downloadImagesInParallel(imageTasks, (progress) => {
    self.postMessage({ type: 'progress', progress });
  });
  
  // 通知图片处理完成，开始添加图片到Excel
  self.postMessage({ type: 'progress', progress: 90, message: '正在添加图片到Excel...' });
  
  // 处理图片
  const IMAGE_WIDTH = 80; // 图片宽度
  const IMAGE_HEIGHT = 100; // 图片高度
  
  // 将下载好的图片添加到Excel
  for (const result of imageResults) {
    if (result.success) {
      // 添加图片到工作表
      const imageId = workbook.addImage({
        buffer: result.imageData,
        extension: 'png', // 假设图片是PNG格式
      });
      
      // 在Excel中插入图片
      worksheet.addImage(imageId, {
        tl: { col: result.colIndex, row: result.rowIndex + 1 },
        ext: { width: IMAGE_WIDTH, height: IMAGE_HEIGHT },
        editAs: 'oneCell' // 图片随单元格调整
      });
      
      // 图片成功下载后，清空单元格内的URL文本
      worksheet.getCell(result.cellReference).value = null;
    }
  }
  
  // 通知Excel生成阶段
  self.postMessage({ type: 'progress', progress: 95, message: '正在生成Excel文件...' });
  
  // 生成Excel二进制数据
  const buffer = await workbook.xlsx.writeBuffer();
  
  // 返回生成的Excel二进制数据
  return buffer;
}

// 处理本地抽检明细数据导出
async function handleQualityInspectionExport(data, fileName) {
  // 设置列配置，与主线程中的函数保持一致
  const columns = [
    { header: '序号', key: 'index', width: 8 },
    { header: '店铺名称', key: 'shopName', width: 15 },
    { header: 'SPU ID', key: 'spuId', width: 12 },
    { header: 'SKU ID', key: 'productSkuId', width: 12 },
    { header: 'SKC ID', key: 'productSkcId', width: 15 },
    { 
      header: '商品图片', 
      key: 'thumbUrl', 
      width: 15,
      isImage: true 
    },
    { header: '商品名称', key: 'skuName', width: 30 },
    { header: '商品类目', key: 'catName', width: 15 },
    { header: '属性规格', key: 'spec', width: 20 },
    { header: '货号', key: 'extCode', width: 15 },
    { header: '备货单号', key: 'purchaseNo', width: 20 },
    { 
      header: '抽检结果', 
      key: 'qcResult', 
      width: 10,
      formatter: (value) => value === '1' ? '合格' : '不合格'
    },
    { header: '最新抽检时间', key: 'qcResultUpdateTime', width: 18 },
    { 
      header: '疵点描述', 
      key: 'flawNameDesc', 
      width: 25 
    },
    { 
      header: '问题备注', 
      key: 'remark', 
      width: 25 
    },
    { 
      header: '疵点图片1', 
      key: 'attachment1', 
      width: 20,
      isImage: true 
    },
    { 
      header: '疵点图片2', 
      key: 'attachment2', 
      width: 20,
      isImage: true 
    },
    { 
      header: '疵点图片3', 
      key: 'attachment3', 
      width: 20,
      isImage: true 
    }
  ];

  // 计算最大的附件图片数量
  let maxAttachmentCount = 0;
  data.forEach(item => {
    if (item.attachments && Array.isArray(item.attachments)) {
      maxAttachmentCount = Math.max(maxAttachmentCount, item.attachments.length);
    }
  });

  // 如果没有附件图片，移除相关列
  if (maxAttachmentCount === 0) {
    columns.splice(-3, 3); // 移除最后三列（疵点图片1/2/3）
  } else if (maxAttachmentCount === 1) {
    columns.splice(-2, 2); // 只保留疵点图片1
  } else if (maxAttachmentCount === 2) {
    columns.splice(-1, 1); // 只保留疵点图片1和2
  }

  // 预处理数据
  const processedData = data.map((item, index) => {
    // 处理抽检结果格式化
    const processedItem = {
      ...item,
      index: index + 1
    };

    // 处理附件图片
    if (item.attachments && Array.isArray(item.attachments)) {
      const validAttachments = item.attachments.filter(url => url && typeof url === 'string');
      for (let i = 0; i < Math.min(validAttachments.length, 3); i++) {
        processedItem[`attachment${i+1}`] = validAttachments[i];
      }
    }

    return processedItem;
  });

  // 导出Excel
  return await exportToExcelWithImages(processedData, columns, fileName);
}

// 处理本地退货明细数据导出
async function handleLocalRefundExport(data, fileName) {
  // 设置列配置，与主线程中的函数保持一致
  const columns = [
    { header: '序号', key: 'index', width: 8 },
    { header: '店铺名称', key: 'shopName', width: 15 },
    { header: 'SPU', key: 'productSpuId', width: 12 },
    { header: 'SKU', key: 'productSkuId', width: 12 },
    { 
      header: '商品图片', 
      key: 'thumbUrl', 
      width: 15,
      isImage: true 
    },
    { 
      header: 'SKC', 
      key: 'productSkcId', 
      width: 15 
    },
    { 
      header: '属性集', 
      key: 'attributes', 
      width: 20
    },
    { header: '货号', key: 'extCode', width: 15 },
    { header: '备货单号', key: 'purchaseSubOrderSn', width: 20 },
    { 
      header: '退货原因', 
      key: 'reasonDesc', 
      width: 20,
      formatter: (value) => {
        if (!value) return '暂无原因';
        if (Array.isArray(value)) return value.join('，');
        
        if (typeof value === 'string' && value.startsWith('[') && value.endsWith(']')) {
          try {
            const parsedArray = JSON.parse(value);
            if (Array.isArray(parsedArray)) {
              return parsedArray.join('，');
            }
          } catch (e) {
            // 解析失败，返回原字符串
          }
        }
        
        return String(value);
      }
    },
    { header: '退货包裹号', key: 'packageSn', width: 22 },
    { header: 'SKU件数', key: 'quantity', width: 10 },
    { header: '出库时间', key: 'outboundTime', width: 18 }
  ];

  // 预处理数据
  const processedData = data.map((item, index) => {
    const processedItem = {
      ...item,
      index: index + 1
    };
    
    // 处理属性集
    if (item.mainSaleSpec !== undefined && item.secondarySaleSpec !== undefined) {
      processedItem.attributes = `${item.mainSaleSpec}-${item.secondarySaleSpec}`;
    }
    
    // 预处理退货原因
    if (item.reasonDesc) {
      processedItem.reasonDesc = columns.find(col => col.key === 'reasonDesc')?.formatter?.(item.reasonDesc) || item.reasonDesc;
    }
    
    return processedItem;
  });

  // 导出Excel
  return await exportToExcelWithImages(processedData, columns, fileName);
}

// 导出违规信息
async function exportViolation(data) {
  try {
    // 创建工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('店铺违规信息');

    // 设置列
    worksheet.columns = [
      { header: '序号', key: 'index', width: 10 },
      { header: '店铺名称', key: 'shopName', width: 20 },
      { header: '违规编号', key: 'punishSn', width: 20 },
      { header: '备货单号', key: 'subPurchaseOrderSn', width: 20 },
      { header: '商品SKU ID', key: 'productSkuId', width: 20 },
      { header: '库存数量', key: 'stockQuantity', width: 15 },
      { header: '缺货数量', key: 'lackQuantity', width: 15 },
      { header: '不合格数量', key: 'unqualifiedQuantity', width: 15 },
      { header: '违规类型', key: 'punishFirstTypeDesc', width: 20 },
      { header: '具体原因', key: 'punishSecondTypeDesc', width: 30 },
      { header: '违规时间', key: 'violationTime', width: 20 },
      { header: '罚款金额(CNY)', key: 'punishAmount', width: 15 },
      { header: '状态', key: 'punishStatus', width: 15 }
    ];

    // 添加数据
    data.forEach((item, index) => {
      worksheet.addRow({
        index: index + 1,
        shopName: item.shopName,
        punishSn: item.punishSn,
        subPurchaseOrderSn: item.subPurchaseOrderSn,
        productSkuId: item.productSkuId,
        stockQuantity: item.stockQuantity || 0,
        lackQuantity: item.lackQuantity || 0,
        unqualifiedQuantity: item.unqualifiedQuantity || 0,
        punishFirstTypeDesc: item.punishFirstTypeDesc,
        punishSecondTypeDesc: item.punishSecondTypeDesc,
        violationTime: item.violationTime,
        punishAmount: formatAmount(item.punishAmount),
        punishStatus: getPunishStatusText(item.punishStatus)
      });
    });

    // 设置表头样式
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' };

    // 设置数据行样式
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) {
        row.alignment = { vertical: 'middle', horizontal: 'left' };
        // 金额列右对齐
        row.getCell('punishAmount').alignment = { horizontal: 'right' };
        // 数量列右对齐
        row.getCell('stockQuantity').alignment = { horizontal: 'right' };
        row.getCell('lackQuantity').alignment = { horizontal: 'right' };
        row.getCell('unqualifiedQuantity').alignment = { horizontal: 'right' };
      }
    });

    // 生成二进制数据
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  } catch (error) {
    console.error('生成Excel文件失败:', error);
    throw error;
  }
}

// 格式化金额
function formatAmount(amount) {
  if (amount === undefined || amount === null) return '0.00';
  // 将角转换为元（除以100）
  const amountInYuan = amount / 100;
  return amountInYuan.toFixed(2);
}

// 获取违规状态文本
function getPunishStatusText(status) {
  switch (status) {
    case 0: return '公示中(待申诉)';
    case 1: return '已取消违规处理';
    case 3: return '已按违规处理';
    case 4: return '逾期未申诉';
    case 5: return '已申诉(处理中)';
    case 6: return '申诉驳回';
    default: return '未知';
  }
}

// 清除图片缓存
function clearImageCache() {
  imageCache.clear();
  return true;
}

// 监听来自主线程的消息
self.addEventListener('message', async (e) => {
  try {
    const { action, data, fileName, customColumns } = e.data;
    
    // 发送开始处理的消息
    self.postMessage({ type: 'progress', progress: 5, message: '开始处理数据...' });
    
    let buffer;
    
    // 根据不同的动作执行不同的导出逻辑
    if (action === 'exportQualityInspection') {
      buffer = await handleQualityInspectionExport(data, fileName);
    } else if (action === 'exportLocalRefund') {
      buffer = await handleLocalRefundExport(data, fileName);
    } else if (action === 'exportCustom') {
      buffer = await exportToExcelWithImages(data, customColumns, fileName);
    } else if (action === 'exportViolation') {
      buffer = await exportViolation(data);
    } else if (action === 'clearCache') {
      const success = clearImageCache();
      self.postMessage({ 
        type: 'cacheCleared', 
        success 
      });
      return;
    }
    
    // 发送完成进度
    self.postMessage({ type: 'progress', progress: 100, message: '导出完成!' });
    
    // 返回生成的Excel文件buffer
    self.postMessage({ 
      type: 'complete', 
      buffer: buffer,
      fileName: fileName
    });
  } catch (error) {
    console.error('Worker处理错误:', error);
    // 发送错误信息
    self.postMessage({ 
      type: 'error', 
      error: error.message || '导出Excel时发生错误' 
    });
  }
}); 