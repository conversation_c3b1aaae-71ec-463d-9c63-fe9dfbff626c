package com.xiao.temu.modules.purchaseorderv.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xiao.temu.common.params.CommonParams;
import com.xiao.temu.infrastructure.api.TemuApiClient;
import com.xiao.temu.modules.purchaseorderv.dto.ProductLabelRequestDTO;
import com.xiao.temu.modules.purchaseorderv.service.ProductLabelService;
import com.xiao.temu.modules.purchaseorderv.vo.ProductLabelVO;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 商品标签服务实现
 */
@Slf4j
@Service
public class ProductLabelServiceImpl implements ProductLabelService {

    @Autowired
    private ShopService shopService;

    @Autowired
    private UserService userService;

    /**
     * 获取商品标签数据
     *
     * @param requestDTO 请求参数
     * @param userId 当前用户ID
     * @return 商品标签数据
     */
    @Override
    public ProductLabelVO getProductLabels(ProductLabelRequestDTO requestDTO, Long userId) {
        log.debug("查询商品标签数据, 请求参数: {}, 用户ID: {}", requestDTO, userId);
        
        ProductLabelVO response = new ProductLabelVO();
        response.setSuccess(true);
        
        try {
            // 获取店铺与商品的映射
            Map<Long, List<String>> shopProductMap = requestDTO.getShopProductMap();
            
            // 验证参数
            if (shopProductMap == null || shopProductMap.isEmpty()) {
                throw new RuntimeException("店铺与商品映射不能为空");
            }
            
            // 检查用户是否是管理员
            boolean isAdmin = userService.isAdmin(userId);
            
            // 验证店铺权限
            Set<Long> shopIds = shopProductMap.keySet();
            List<Long> validShopIds = new ArrayList<>();
            
            for (Long shopId : shopIds) {
                if (isAdmin || shopService.checkShopPermission(userId, shopId, false)) {
                    validShopIds.add(shopId);
                }
            }
            
            // 如果没有任何有效的店铺ID，返回权限错误
            if (validShopIds.isEmpty()) {
                response.setSuccess(false);
                response.setErrorCode(403);
                response.setErrorMsg("您没有权限访问所选店铺的数据");
                return response;
            }
            
            // 存储每个店铺的API结果
            Map<Long, JSONObject> shopResultMap = new HashMap<>();
            
            // 遍历有效的店铺ID
            for (Long shopId : validShopIds) {
                // 获取该店铺的商品ID列表
                List<String> productSkuIds = shopProductMap.get(shopId);
                if (productSkuIds == null || productSkuIds.isEmpty()) {
                    continue;
                }
                
                // 获取店铺信息
                Shop shop = shopService.getShopById(shopId).convertToShop();
                if (shop == null) {
                    log.warn("店铺不存在: {}", shopId);
                    continue;
                }
                
                // 调用API获取标签数据，批量处理（每批最多20个）
                int batchSize = 20;
                List<String> currentBatch = new ArrayList<>();
                
                // 初始化简化后的结果结构
                JSONObject simplifiedResult = new JSONObject();
                simplifiedResult.put("data", new ArrayList<>());
                simplifiedResult.put("totalCount", 0);
                
                int totalCount = 0;
                boolean hasError = false;
                int errorCode = 0;
                String errorMsg = "";
                
                for (int i = 0; i < productSkuIds.size(); i++) {
                    currentBatch.add(productSkuIds.get(i));
                    
                    // 当前批次已满或是最后一个元素，调用API
                    if (currentBatch.size() == batchSize || i == productSkuIds.size() - 1) {
                        JSONObject batchResult = callLabelApi(shop, currentBatch);

                        // 解析结果
                        if (batchResult != null && batchResult.getBooleanValue("success")) {
                            try {
                                JSONObject batchResultData = batchResult.getJSONObject("result");
                                if (batchResultData != null && batchResultData.containsKey("labelCodePageResult")) {
                                    JSONObject labelCodePage = batchResultData.getJSONObject("labelCodePageResult");
                                    if (labelCodePage != null) {
                                        List<Object> combinedData = (List<Object>) simplifiedResult.get("data");
                                        
                                        List<Object> batchData = labelCodePage.getObject("data", List.class);
                                        if (batchData != null && !batchData.isEmpty()) {
                                            combinedData.addAll(batchData);
                                            int batchCount = labelCodePage.getIntValue("totalCount");
                                            totalCount += batchCount;
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                log.error("解析批次结果失败: {}", e.getMessage(), e);
                            }
                        } else if (batchResult != null) {
                            // 如果有任何批次失败，记录错误信息
                            log.error("获取商品标签数据失败: shopId={}, errorCode={}, errorMsg={}", 
                                     shopId, batchResult.getInteger("errorCode"), batchResult.getString("errorMsg"));
                            hasError = true;
                            errorCode = batchResult.getInteger("errorCode");
                            errorMsg = batchResult.getString("errorMsg");
                            break;
                        }
                        
                        // 清空当前批次，准备下一批
                        currentBatch.clear();
                    }
                }
                
                // 更新总数量
                simplifiedResult.put("totalCount", totalCount);
                
                // 如果有错误，添加错误信息
                if (hasError) {
                    simplifiedResult.put("success", false);
                    simplifiedResult.put("errorCode", errorCode);
                    simplifiedResult.put("errorMsg", errorMsg);
                } else {
                    simplifiedResult.put("success", true);
                }
                
                // 保存该店铺的结果
                shopResultMap.put(shopId, simplifiedResult);
                
                // 设置第一个有效店铺的信息到响应中
                if (response.getShopId() == null) {
                    response.setShopId(shopId);
                    response.setShopName(shop.getShopName());
                    response.setShopRemark(shop.getRemark());
                }
            }
            
            response.setShopResultMap(shopResultMap);
            return response;
            
        } catch (Exception e) {
            log.error("获取商品标签数据失败", e);
            response.setSuccess(false);
            response.setErrorCode(500);
            response.setErrorMsg("获取商品标签数据失败: " + e.getMessage());
            return response;
        }
    }
    
    /**
     * 调用Temu API获取标签数据
     *
     * @param shop 店铺信息
     * @param productSkuIds 商品SkuID列表
     * @return API响应结果
     */
    private JSONObject callLabelApi(Shop shop, List<String> productSkuIds) {
        try {
            // 设置API通用参数
            CommonParams commonParams = new CommonParams();
            commonParams.setAccessToken(shop.getAccessToken());
            commonParams.setType("bg.goods.labelv2.get");
            commonParams.setAppKey(shop.getApiKey());
            commonParams.setAppSecret(shop.getApiSecret());
            
            // 设置业务参数
            HashMap<String, Object> businessParams = new HashMap<>();
            businessParams.put("productSkuIdList", productSkuIds.toArray(new String[0]));

            // 调用API
            String apiResponse = null;
            try {
                JSONObject result = TemuApiClient.sendRequest(commonParams, businessParams);
                log.info("API调用结果: {}", result);
                return result;

            } catch (Exception e) {
                log.error("API调用异常: {}", e.getMessage(), e);
                
                // 尝试将响应解析为字符串
                if (apiResponse != null && StringUtils.hasText(apiResponse)) {
                    try {
                        return JSON.parseObject(apiResponse);
                    } catch (Exception ex) {
                        log.error("解析API响应失败: {}", ex.getMessage(), ex);
                    }
                }
                
                throw e;
            }
        } catch (Exception e) {
            log.error("调用商品标签API失败: shopId={}, error={}", shop.getShopId(), e.getMessage(), e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("errorCode", 500);
            errorResult.put("errorMsg", "调用API失败: " + e.getMessage());
            return errorResult;
        }
    }
} 