package com.xiao.temu.modules.sales.service;

import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.modules.sales.dto.SalesStatisticsQueryDTO;
import com.xiao.temu.modules.sales.entity.SalesStatistics;
import com.xiao.temu.modules.sales.vo.ShopSalesStatisticsVO;
import java.time.LocalDate;
import java.util.List;

/**
 * 销售统计服务接口
 */
public interface SalesStatisticsService {
    
    /**
     * 获取指定日期范围内的店铺销售统计数据
     * 
     * @param queryDTO 查询条件
     * @return 销售统计数据列表
     */
    List<ShopSalesStatisticsVO> getShopSalesStatistics(SalesStatisticsQueryDTO queryDTO);
    
    /**
     * 执行销售数据统计定时任务
     * 
     * @return 任务执行结果
     */
    String executeScheduledStatistics();
    
    /**
     * 同步指定店铺的销售统计数据
     * 
     * @param shopId 店铺ID
     * @return 同步结果
     */
    ApiResponse<ShopSalesStatisticsVO> syncShopSalesStatistics(Long shopId);
    
    /**
     * 同步指定店铺的销售统计数据（带重试机制）
     * 
     * @param shopId 店铺ID
     * @return 同步结果
     */
    ApiResponse<ShopSalesStatisticsVO> syncShopSalesStatisticsWithRetry(Long shopId);
    
    /**
     * 获取指定店铺和日期的销售统计数据
     * 
     * @param shopId 店铺ID
     * @param date 日期
     * @return 销售统计数据
     */
    SalesStatistics getSalesStatisticsByShopIdAndDate(Long shopId, LocalDate date);
} 