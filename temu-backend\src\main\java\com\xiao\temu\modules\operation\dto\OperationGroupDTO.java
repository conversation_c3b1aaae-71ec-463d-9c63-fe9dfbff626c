package com.xiao.temu.modules.operation.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

/**
 * 运营组数据传输对象
 */
@Data
public class OperationGroupDTO {

    /**
     * 运营组ID（更新时必填）
     */
    @ExcelProperty(value = "运营组ID", index = 0)
    private Long groupId;

    /**
     * 运营组名称
     */
    @NotBlank(message = "运营组名称不能为空")
    @ExcelProperty(value = "运营组名称", index = 1)
    private String groupName;

    /**
     * 负责人ID
     */
    @NotNull(message = "负责人不能为空")
    @ExcelIgnore
    private Long leaderId;

    /**
     * 负责人姓名（查询结果显示）
     */
    @ExcelProperty(value = "负责人", index = 2)
    private String leaderName;

    /**
     * 状态（0正常 1禁用）
     */
    @ExcelProperty(value = "状态", index = 3)
    private String status;

    /**
     * 创建时间
     */
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间", index = 4)
    private Date createTime;

    /**
     * 更新时间
     */
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "更新时间", index = 5)
    private Date updateTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 6)
    private String remark;

    /**
     * 成员数量（查询结果显示）
     */
    @ExcelProperty(value = "成员数量", index = 7)
    private Integer memberCount;
} 