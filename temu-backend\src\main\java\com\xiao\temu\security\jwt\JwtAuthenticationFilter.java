package com.xiao.temu.security.jwt;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * JWT认证过滤器，用于拦截请求并验证JWT
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final UserDetailsService userDetailsService;
    private final JwtTokenUtil jwtTokenUtil;
    private final RedisTemplate<String, Object> redisTemplate;
    
    // Token黑名单前缀
    private static final String TOKEN_BLACKLIST_PREFIX = "temu:token:blacklist:";

    @Value("${jwt.header}")
    private String tokenHeader;

    @Value("${jwt.tokenPrefix}")
    private String tokenPrefix;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {

        // 获取Authorization头
        final String header = request.getHeader(tokenHeader);

        String username = null;
        String jwt = null;

        // 检查Authorization头是否存在且格式正确
        if (header != null && header.startsWith(tokenPrefix)) {
            // 提取token
            jwt = header.substring(tokenPrefix.length()).trim();
            
            // 检查token是否在黑名单中
            if (isTokenBlacklisted(jwt)) {
                log.debug("JWT Token在黑名单中，拒绝访问");
                chain.doFilter(request, response);
                return;
            }
            
            try {
                // 从token中提取用户名
                username = jwtTokenUtil.getUsernameFromToken(jwt);
            } catch (Exception e) {
                log.error("JWT Token解析失败", e);
            }
        }

        // 验证token并设置认证信息到上下文
        if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
            try {
                // 加载用户详情
                UserDetails userDetails = userDetailsService.loadUserByUsername(username);

                // 验证token
                if (jwtTokenUtil.validateToken(jwt, userDetails)) {
                    // 创建认证对象
                    UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(
                            userDetails, jwt, userDetails.getAuthorities());
                    authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                    // 设置认证信息到上下文
                    SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                    
                    log.debug("用户 '{}' 授权成功", username);
                } else {
                    log.debug("无效的JWT token");
                }
            } catch (Exception e) {
                log.error("无法设置用户认证: {}", e.getMessage());
            }
        }

        chain.doFilter(request, response);
    }
    
    /**
     * 检查token是否在黑名单中
     */
    private boolean isTokenBlacklisted(String token) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(TOKEN_BLACKLIST_PREFIX + token));
        } catch (Exception e) {
            log.error("检查token黑名单失败", e);
            return false;
        }
    }
} 