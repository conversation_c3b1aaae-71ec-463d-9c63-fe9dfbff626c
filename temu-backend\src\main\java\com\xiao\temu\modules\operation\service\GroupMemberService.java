package com.xiao.temu.modules.operation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.operation.dto.GroupMemberDTO;

import java.util.List;

/**
 * 运营组成员服务接口
 */
public interface GroupMemberService {

    /**
     * 分页查询运营组成员列表
     *
     * @param groupId 运营组ID
     * @param page 分页参数
     * @return 成员分页列表
     */
    IPage<GroupMemberDTO> getMemberList(Long groupId, Page<GroupMemberDTO> page);

    /**
     * 查询运营组成员列表（不分页）
     *
     * @param groupId 运营组ID
     * @return 成员列表
     */
    List<GroupMemberDTO> getAllMembers(Long groupId);

    /**
     * 添加运营组成员
     *
     * @param groupId 运营组ID
     * @param userId 用户ID
     * @return 结果
     */
    int addMember(Long groupId, Long userId);

    /**
     * 批量添加运营组成员
     *
     * @param groupId 运营组ID
     * @param userIds 用户ID列表
     * @return 结果
     */
    int batchAddMembers(Long groupId, List<Long> userIds);

    /**
     * 删除运营组成员
     *
     * @param groupId 运营组ID
     * @param userId 用户ID
     * @return 结果
     */
    int removeMember(Long groupId, Long userId);

    /**
     * 删除运营组的所有成员
     *
     * @param groupId 运营组ID
     * @return 结果
     */
    int removeAllMembers(Long groupId);

    /**
     * 检查用户是否为运营组成员
     *
     * @param groupId 运营组ID
     * @param userId 用户ID
     * @return 结果 true-是 false-否
     */
    boolean checkUserInGroup(Long groupId, Long userId);

    /**
     * 统计运营组成员数量
     *
     * @param groupId 运营组ID
     * @return 成员数量
     */
    int countGroupMembers(Long groupId);
} 