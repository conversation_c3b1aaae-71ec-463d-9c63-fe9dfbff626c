package com.xiao.temu.infrastructure.task.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导出任务进度VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExportTaskProgressVO {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 进度值(0-100)
     */
    private int progress;
    
    /**
     * 状态(waiting, processing, completed, failed)
     */
    private String status;
    
    /**
     * 进度消息
     */
    private String message;
    
    /**
     * 文件名
     */
    private String fileName;
} 