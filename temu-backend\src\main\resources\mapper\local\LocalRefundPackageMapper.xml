<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.refund.mapper.LocalRefundPackageMapper">

    <!-- 查询结果映射 -->
    <resultMap id="RefundPackageItemResultMap"
               type="com.xiao.temu.modules.refund.vo.LocalRefundPackageVO$LocalRefundPackageItemVO">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="shop_name" property="shopName"/>
        <result column="shop_remark" property="shopRemark"/>
        <result column="package_sn" property="packageSn"/>
        <result column="product_sku_id" property="productSkuId"/>
        <result column="product_skc_id" property="productSkcId"/>
        <result column="product_spu_id" property="productSpuId"/>
        <result column="purchase_sub_order_sn" property="purchaseSubOrderSn"/>
        <result column="quantity" property="quantity"/>
        <result column="main_sale_spec" property="mainSaleSpec"/>
        <result column="secondary_sale_spec" property="secondarySaleSpec"/>
        <result column="thumb_url" property="thumbUrl"/>
        <result column="order_type_desc" property="orderTypeDesc"/>
        <result column="reason_desc" property="reasonDesc"/>
        <result column="remark" property="remark"/>
        <result column="outbound_time" property="outboundTime"/>
        <result column="ext_code" property="extCode"/>
    </resultMap>

    <!-- 分页查询退货包裹数据 -->
    <select id="selectRefundPackagePage" resultMap="RefundPackageItemResultMap">
        SELECT
        rpd.id,
        rpd.shop_id,
        s.shop_name,
        s.remark as shop_remark,
        rpd.package_sn,
        rpd.product_sku_id,
        rpd.product_skc_id,
        rpd.product_spu_id,
        rpd.purchase_sub_order_sn,
        rpd.quantity,
        rpd.main_sale_spec,
        rpd.secondary_sale_spec,
        rpd.thumb_url,
        rpd.order_type_desc,
        rpd.reason_desc,
        rpd.remark,
        FROM_UNIXTIME(rpd.outbound_time/1000, '%Y-%m-%d %H:%i:%s') as outbound_time,
        p.ext_code
        FROM
        refund_package_detail rpd
        LEFT JOIN product p ON rpd.shop_id = p.shop_id AND rpd.product_skc_id = p.product_skc_id
        LEFT JOIN shop s ON rpd.shop_id = s.shop_id
        <where>
            <if test="shopIds != null and shopIds.size() > 0">
                AND rpd.shop_id IN
                <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test="outboundTimeStart != null">
                AND rpd.outbound_time &gt;= UNIX_TIMESTAMP(#{outboundTimeStart}) * 1000
            </if>
            <if test="outboundTimeEnd != null">
                AND rpd.outbound_time &lt;= UNIX_TIMESTAMP(#{outboundTimeEnd}) * 1000
            </if>
            <if test="productSkuIdList != null and productSkuIdList.size() > 0">
                AND rpd.product_sku_id IN
                <foreach collection="productSkuIdList" item="skuId" open="(" separator="," close=")">
                    #{skuId}
                </foreach>
            </if>
            <if test="returnSupplierPackageNosList != null and returnSupplierPackageNosList.size() > 0">
                AND rpd.package_sn IN
                <foreach collection="returnSupplierPackageNosList" item="packageNo" open="(" separator="," close=")">
                    #{packageNo}
                </foreach>
            </if>
            <if test="purchaseSubOrderSnsList != null and purchaseSubOrderSnsList.size() > 0">
                AND rpd.purchase_sub_order_sn IN
                <foreach collection="purchaseSubOrderSnsList" item="orderSn" open="(" separator="," close=")">
                    #{orderSn}
                </foreach>
            </if>
        </where>
        ORDER BY rpd.outbound_time DESC
    </select>

</mapper> 