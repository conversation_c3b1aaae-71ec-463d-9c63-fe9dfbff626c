package com.xiao.temu.infrastructure.exporter;

import com.xiao.temu.modules.refund.dto.LocalRefundRequestDTO;
import com.xiao.temu.modules.refund.vo.LocalRefundPackageVO;
import com.xiao.temu.modules.system.service.SysDataPermissionService;
import com.xiao.temu.modules.system.service.UserService;
import com.xiao.temu.modules.refund.service.LocalRefundPackageService;
import com.xiao.temu.infrastructure.excel.ExcelExportService;
import com.xiao.temu.infrastructure.excel.ExcelExportUtils;
import com.xiao.temu.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 退货包裹Excel导出实现类
 */
@Slf4j
@Service
public class RefundPackageExcelExporter implements ExcelExportService.ExcelExporter {

    @Autowired
    private ExcelExportService excelExportService;
    
    @Autowired
    private LocalRefundPackageService localRefundPackageService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private SysDataPermissionService dataPermissionService;
    
    @Override
    public void export(Map<String, Object> exportParams, String taskId) {
        // 定义表头和列宽
        String[] headers = {"序号", "店铺代号", "店铺名称", "SPU", "SKU", "商品缩略图", "SKC", "属性集", "货号", "备货单号", "退货原因", "退货包裹号", "SKU件数", "出库时间"};
        
        int[] columnWidths = {5, 20, 15, 15, 15, 10, 15, 20, 15, 15, 30, 15, 10, 20};
        
        // 获取文件名
        String fileName = (String) exportParams.get("fileName");
        if (fileName == null || fileName.trim().isEmpty()) {
            fileName = "退货明细数据";
        }

        // 执行导出
        excelExportService.exportExcel(
            taskId,
            fileName,
            "退货明细",
            headers,
            columnWidths,
            new RefundPackageDataProcessor(exportParams)
        );
    }
    
    /**
     * 退货包裹数据处理器
     */
    private class RefundPackageDataProcessor implements ExcelExportService.ExcelDataProcessor {
        
        private final Map<String, Object> exportParams;
        
        public RefundPackageDataProcessor(Map<String, Object> exportParams) {
            this.exportParams = exportParams;
        }
        
        @Override
        public void prepareData(ExcelExportService.ExcelExportContext context) {
            try {
                // 从导出参数中获取用户ID
                Object userIdObj = exportParams.get("userId");
                if (userIdObj == null) {
                    throw new RuntimeException("导出参数中缺少用户ID");
                }
                Long userId = Long.valueOf(userIdObj.toString());
                
                // 判断用户是否为管理员或拥有全部数据权限
                boolean isAdmin = userService.isAdmin(userId);
                
                // 获取用户的最高数据权限
                String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
                boolean hasFullDataPermission = "2".equals(permissionType);
                
                // 解析导出参数
                Map<String, Object> queryParams = new HashMap<>();
            
                // 如果有单独的queryParams字段，则使用它
                if (exportParams.containsKey("queryParams")) {
                    queryParams = (Map<String, Object>) exportParams.get("queryParams");
                } else {
                    // 兼容旧版本，直接使用exportParams
                    queryParams = exportParams;
                }
                
                // 获取导出类型
                String exportType = (String) exportParams.get("exportType");
                // 默认使用当前页导出
                if (exportType == null) {
                    exportType = "current";
                }
                
                // 构建查询参数
                LocalRefundRequestDTO requestDTO = new LocalRefundRequestDTO();
                
                // 设置查询参数
                if (queryParams != null) {
                    // 处理shopIds参数
                    if (queryParams.get("shopIds") != null) {
                        List<?> rawShopIds = (List<?>) queryParams.get("shopIds");
                        if (!rawShopIds.isEmpty()) {
                            List<Long> shopIdList = new ArrayList<>();
                            for (Object id : rawShopIds) {
                                if (id instanceof Integer) {
                                    shopIdList.add(((Integer) id).longValue());
                                } else if (id instanceof Long) {
                                    shopIdList.add((Long) id);
                                } else if (id instanceof String) {
                                    shopIdList.add(Long.valueOf((String) id));
                                } else {
                                    shopIdList.add(Long.valueOf(id.toString()));
                                }
                            }
                            requestDTO.setShopIds(shopIdList);
                        } else {
                            log.warn("shopIds列表为空");
                            throw new RuntimeException("店铺ID不能为空");
                        }
                    } else {
                        log.warn("请求参数中未找到shopIds，尝试从exportParams直接获取");
                        // 尝试从exportParams直接获取shopIds
                        if (exportParams.get("shopIds") != null) {
                            List<?> rawShopIds = (List<?>) exportParams.get("shopIds");
                            if (!rawShopIds.isEmpty()) {
                                List<Long> shopIdList = new ArrayList<>();
                                for (Object id : rawShopIds) {
                                    if (id instanceof Integer) {
                                        shopIdList.add(((Integer) id).longValue());
                                    } else if (id instanceof Long) {
                                        shopIdList.add((Long) id);
                                    } else if (id instanceof String) {
                                        shopIdList.add(Long.valueOf((String) id));
                                    } else {
                                        shopIdList.add(Long.valueOf(id.toString()));
                                    }
                                }
                                requestDTO.setShopIds(shopIdList);
                            } else {
                                log.error("shopIds列表为空");
                                throw new RuntimeException("店铺ID不能为空");
                            }
                        } else {
                            log.error("未能找到shopIds参数");
                            throw new RuntimeException("店铺ID不能为空");
                        }
                    }
                    
                    // 处理日期参数
                    if (queryParams.get("outboundTimeStart") != null) {
                        String timeStart = queryParams.get("outboundTimeStart").toString();
                        requestDTO.setOutboundTimeStart(DateUtils.parseLocalDateTime(timeStart));
                    }
                    
                    if (queryParams.get("outboundTimeEnd") != null) {
                        String timeEnd = queryParams.get("outboundTimeEnd").toString();
                        requestDTO.setOutboundTimeEnd(DateUtils.parseLocalDateTime(timeEnd));
                    }
                    
                    // 处理SKU ID列表
                    if (queryParams.get("productSkuIdList") != null) {
                        List<?> rawSkuIds = (List<?>) queryParams.get("productSkuIdList");
                        List<Long> skuIdList = new ArrayList<>();
                        for (Object id : rawSkuIds) {
                            if (id instanceof Integer) {
                                skuIdList.add(((Integer) id).longValue());
                            } else if (id instanceof Long) {
                                skuIdList.add((Long) id);
                            } else if (id instanceof String) {
                                skuIdList.add(Long.valueOf((String) id));
                            } else {
                                skuIdList.add(Long.valueOf(id.toString()));
                            }
                        }
                        requestDTO.setProductSkuIdList(skuIdList);
                    }
                    
                    // 处理其他参数
                    if (queryParams.get("returnSupplierPackageNos") != null) {
                        requestDTO.setReturnSupplierPackageNos((List<String>) queryParams.get("returnSupplierPackageNos"));
                    }
                    
                    if (queryParams.get("purchaseSubOrderSns") != null) {
                        requestDTO.setPurchaseSubOrderSns((List<String>) queryParams.get("purchaseSubOrderSns"));
                    }
                    
                    // 设置分页参数 - 兼容pageNo和pageNum两种命名
                    int pageNo = 1;
                    if (queryParams.get("pageNo") != null) {
                        pageNo = Math.toIntExact(Long.valueOf(queryParams.get("pageNo").toString()));
                    } else if (queryParams.get("pageNum") != null) {
                        pageNo = Math.toIntExact(Long.valueOf(queryParams.get("pageNum").toString()));
                    }
                    requestDTO.setPageNo(pageNo);
                    
                    int pageSize = Integer.MAX_VALUE;
                    if (queryParams.get("pageSize") != null) {
                        pageSize = Math.toIntExact(Long.valueOf(queryParams.get("pageSize").toString()));
                    }
                    requestDTO.setPageSize(pageSize);
                }
                
                // 如果是管理员或拥有全部数据权限，设置忽略权限检查标志
                if (isAdmin || hasFullDataPermission) {
                    requestDTO.setIgnorePermissionCheck(true);
                }
                
                // 处理不同导出类型
                List<LocalRefundPackageVO.LocalRefundPackageItemVO> allItems = new ArrayList<>();
                
                // 处理不同的导出类型
                if ("current".equals(exportType) || "page".equals(exportType)) {
                    // 导出当前页数据
                    LocalRefundPackageVO result = localRefundPackageService.getLocalRefundPackageList(requestDTO, userId);
                    if (result.getItems() != null) {
                        allItems.addAll(result.getItems());
                    }
                } else if ("custom".equals(exportType)) {
                    // 导出自定义数量数据 - 多页
                    int exportPageCount = 1;
                    int customNum = requestDTO.getPageSize(); // 默认使用pageSize作为自定义数量
                    
                    // 优先使用显式设置的exportPageCount（页码范围导出）
                    if (queryParams.get("exportPageCount") != null) {
                        exportPageCount = Math.toIntExact(Long.valueOf(queryParams.get("exportPageCount").toString()));
                    } 
                    // 其次使用customNum（兼容旧版本自定义数量导出）
                    else if (queryParams.get("customNum") != null) {
                        customNum = Math.toIntExact(Long.valueOf(queryParams.get("customNum").toString()));
                        
                        // 限制导出数量，防止过大
                        if (customNum > 10000) {
                            customNum = 10000;
                        }
                        
                        // 计算需要的页数
                        if (requestDTO.getPageSize() > 0) {
                            exportPageCount = (int) Math.ceil((double) customNum / requestDTO.getPageSize());
                        }
                    }
                    
                    // 限制查询总页数，防止过大
                    if (exportPageCount > 100) {
                        exportPageCount = 100;
                    }
                    
                    // 获取起始页码，默认为当前页
                    int startPage = requestDTO.getPageNo();
                    
                    // 如果前端指定了明确的startPage，则使用前端传入的值
                    if (queryParams.get("pageNum") != null) {
                        startPage = Math.toIntExact(Long.valueOf(queryParams.get("pageNum").toString()));
                    }
                    
                    // 设置每页条数（确保不为0）
                    int pageSize = requestDTO.getPageSize() > 0 ? requestDTO.getPageSize() : 100;
                    
                    // 分页查询数据
                    for (int i = 0; i < exportPageCount; i++) {
                        int currentPage = startPage + i;
                        requestDTO.setPageNo(currentPage);
                        requestDTO.setPageSize(pageSize);
                        
                        LocalRefundPackageVO result = localRefundPackageService.getLocalRefundPackageList(requestDTO, userId);
                        
                        if (result.getItems() != null && !result.getItems().isEmpty()) {
                            // 如果是按自定义数量导出且有customNum参数，则需要检查是否超过customNum
                            if (queryParams.get("customNum") != null && allItems.size() + result.getItems().size() > customNum) {
                                int remaining = customNum - allItems.size();
                                if (remaining > 0) {
                                    allItems.addAll(result.getItems().subList(0, remaining));
                                }
                                break;
                            } else {
                                // 正常添加所有结果
                                allItems.addAll(result.getItems());
                            }
                        }
                        
                        // 如果返回的数据条数少于pageSize，说明已经没有更多数据，跳出循环
                        if (result.getItems() == null || result.getItems().size() < pageSize) {
                            break;
                        }
                    }
                } else if ("all".equals(exportType)) {
                    // 导出全部数据 - 分页查询所有数据
                    
                    // 设置合理的分页大小
                    int pageSize = 1000;
                    requestDTO.setPageSize(pageSize);
                    
                    // 设置起始页码
                    int pageNo = 1;
                    requestDTO.setPageNo(pageNo);
                    
                    // 最大允许导出10万条数据
                    int maxTotalRecords = 100000;
                    int totalRecords = 0;
                    boolean hasMoreData = true;
                    
                    while (hasMoreData && totalRecords < maxTotalRecords) {
                        LocalRefundPackageVO result = localRefundPackageService.getLocalRefundPackageList(requestDTO, userId);
                        
                        if (result.getItems() != null && !result.getItems().isEmpty()) {
                            allItems.addAll(result.getItems());
                            totalRecords += result.getItems().size();
                            
                            // 如果返回的数据条数少于pageSize，说明已经没有更多数据
                            if (result.getItems().size() < pageSize) {
                                hasMoreData = false;
                            } else {
                                // 继续查询下一页
                                pageNo++;
                                requestDTO.setPageNo(pageNo);
                            }
                        } else {
                            hasMoreData = false;
                        }
                    }
                }
                
                // 设置导出上下文
                context.setData(allItems);
                context.setDataSize(allItems.size());
                
            } catch (Exception e) {
                log.error("准备导出数据失败", e);
                throw new RuntimeException("准备导出数据失败: " + e.getMessage(), e);
            }
        }
        
        @Override
        public void prepareImageTasks(ExcelExportService.ExcelExportContext context, File tempDir, List<ExcelExportUtils.ImageDownloadTask> downloadTasks) {
            @SuppressWarnings("unchecked")
            List<LocalRefundPackageVO.LocalRefundPackageItemVO> items = (List<LocalRefundPackageVO.LocalRefundPackageItemVO>) context.getData();
            
            // 收集缩略图
            for (int i = 0; i < items.size(); i++) {
                LocalRefundPackageVO.LocalRefundPackageItemVO item = items.get(i);
                final int rowIndex = i;
                
                // 处理商品缩略图
                if (item.getThumbUrl() != null && !item.getThumbUrl().isEmpty()) {
                    File thumbFile = new File(tempDir, "thumb_" + i + "_" + UUID.randomUUID() + ".jpg");
                    downloadTasks.add(new ExcelExportUtils.ImageDownloadTask(item.getThumbUrl(), thumbFile, 5, rowIndex));
                }
            }
        }
        
        @Override
        public void fillExcelData(ExcelExportService.ExcelExportContext context, Sheet sheet, CellStyle contentStyle) {
            @SuppressWarnings("unchecked")
            List<LocalRefundPackageVO.LocalRefundPackageItemVO> items = (List<LocalRefundPackageVO.LocalRefundPackageItemVO>) context.getData();
            
            // 填充数据
            for (int i = 0; i < items.size(); i++) {
                LocalRefundPackageVO.LocalRefundPackageItemVO item = items.get(i);
                Row row = sheet.createRow(i + 1);
                row.setHeight((short) (80 * 20)); // 设置行高为80点
                
                // 设置单元格内容
                ExcelExportUtils.setCellValue(row, 0, String.valueOf(i + 1), contentStyle);
                ExcelExportUtils.setCellValue(row, 1, item.getShopRemark(), contentStyle);
                ExcelExportUtils.setCellValue(row, 2, item.getShopName(), contentStyle);
                ExcelExportUtils.setCellValue(row, 3, item.getProductSpuId() != null ? String.valueOf(item.getProductSpuId()) : "", contentStyle);
                ExcelExportUtils.setCellValue(row, 4, item.getProductSkuId() != null ? String.valueOf(item.getProductSkuId()) : "", contentStyle);
                // 商品缩略图列，留空，后面插入图片
                ExcelExportUtils.setCellValue(row, 5, "", contentStyle);
                ExcelExportUtils.setCellValue(row, 6, item.getProductSkcId() != null ? String.valueOf(item.getProductSkcId()) : "", contentStyle);
                ExcelExportUtils.setCellValue(row, 7, item.getMainSaleSpec() + "-" + item.getSecondarySaleSpec(), contentStyle);
                ExcelExportUtils.setCellValue(row, 8, item.getExtCode() != null ? item.getExtCode() : "", contentStyle);
                ExcelExportUtils.setCellValue(row, 9, item.getPurchaseSubOrderSn(), contentStyle);
                ExcelExportUtils.setCellValue(row, 10, formatReasonDesc(item.getReasonDesc()), contentStyle);
                ExcelExportUtils.setCellValue(row, 11, item.getPackageSn(), contentStyle);
                ExcelExportUtils.setCellValue(row, 12, item.getQuantity() != null ? String.valueOf(item.getQuantity()) : "0", contentStyle);
                ExcelExportUtils.setCellValue(row, 13, item.getOutboundTime() != null ? item.getOutboundTime() : "", contentStyle);
            }
        }
        
        /**
         * 格式化退货原因，将数组格式转为文本
         */
        private String formatReasonDesc(String reasonDesc) {
            if (reasonDesc == null) return "暂无原因";
    
            // 如果是数组，转换为逗号分隔的字符串
            if (reasonDesc.startsWith("[") && reasonDesc.endsWith("]")) {
                try {
                    com.alibaba.fastjson2.JSONArray jsonArray = com.alibaba.fastjson2.JSON.parseArray(reasonDesc);
                    if (jsonArray != null && !jsonArray.isEmpty()) {
                        return jsonArray.stream()
                            .map(String::valueOf)
                            .collect(java.util.stream.Collectors.joining("，"));
                    }
                } catch (Exception e) {
                    // 解析失败，返回原字符串
                }
            }
    
            // 其他情况直接返回原内容
            return reasonDesc;
        }
    }
} 