package com.xiao.temu.config.web;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;

/**
 * Jackson配置类
 * 用于配置JSON序列化和反序列化，特别是日期时间类型的处理
 */
@Configuration
public class JacksonConfig {

    // 标准日期格式：yyyy-MM-dd
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    
    // 标准日期时间格式：yyyy-MM-dd HH:mm:ss
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    // ISO标准日期时间格式：带T的ISO格式
    public static final DateTimeFormatter ISO_DATE_TIME_FORMATTER = DateTimeFormatter.ISO_DATE_TIME;
    
    // 标准日期格式（SimpleDateFormat用于java.util.Date）
    public static final String STANDARD_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        
        // 注册Java8时间模块
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        
        // 创建自定义模块
        SimpleModule simpleModule = new SimpleModule();
        
        // 注册LocalDateTime自定义反序列化器和序列化器
        simpleModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer());
        simpleModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer());
        
        // 注册java.util.Date自定义反序列化器和序列化器
        simpleModule.addDeserializer(Date.class, new DateDeserializer());
        simpleModule.addSerializer(Date.class, new DateSerializer());
        
        // 注册模块
        objectMapper.registerModule(javaTimeModule);
        objectMapper.registerModule(simpleModule);
        
        // 禁用日期时间转换为时间戳
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        
        return objectMapper;
    }
    
    /**
     * LocalDateTime反序列化器，支持多种日期时间格式的转换
     * 优先使用标准格式，同时兼容其他格式
     */
    public static class LocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {
        @Override
        public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
            String text = p.getText().trim();
            
            if (text == null || text.isEmpty()) {
                return null;
            }
            
            try {
                // 1. 尝试使用标准日期时间格式解析 (yyyy-MM-dd HH:mm:ss)
                return LocalDateTime.parse(text, DATE_TIME_FORMATTER);
            } catch (DateTimeParseException e) {
                try {
                    // 2. 尝试使用标准ISO格式解析 (带T的格式)
                    return LocalDateTime.parse(text, ISO_DATE_TIME_FORMATTER);
                } catch (DateTimeParseException e1) {
                    try {
                        // 3. 尝试解析纯日期格式 (yyyy-MM-dd)，自动补充时间部分为00:00:00
                        return LocalDate.parse(text, DATE_FORMATTER).atStartOfDay();
                    } catch (DateTimeParseException e2) {
                        try {
                            // 4. 尝试兼容其他可能的格式 (用于特殊情况)
                            if (text.contains("/")) {
                                // 处理类似 yyyy/MM/dd 的格式
                                String normalizedText = text.replace("/", "-");
                                return LocalDate.parse(normalizedText, DATE_FORMATTER).atStartOfDay();
                            }
                            
                            // 可以根据需要添加更多格式支持
                            
                            throw new IOException("无法解析日期时间字符串：" + text);
                        } catch (Exception e3) {
                            throw new IOException("无法将日期字符串 '" + text + "' 转换为LocalDateTime", e3);
                        }
                    }
                }
            }
        }
    }
    
    /**
     * LocalDateTime序列化器
     * 将LocalDateTime统一序列化为标准格式：yyyy-MM-dd HH:mm:ss
     */
    public static class LocalDateTimeSerializer extends JsonSerializer<LocalDateTime> {
        @Override
        public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            if (value == null) {
                gen.writeNull();
            } else {
                gen.writeString(value.format(DATE_TIME_FORMATTER));
            }
        }
    }
    
    /**
     * Date反序列化器，支持多种日期时间格式的转换
     * 优先使用标准格式，同时兼容其他格式
     */
    public static class DateDeserializer extends JsonDeserializer<Date> {
        @Override
        public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
            String text = p.getText().trim();
            
            if (text == null || text.isEmpty()) {
                return null;
            }
            
            try {
                // 尝试使用标准日期时间格式解析 (yyyy-MM-dd HH:mm:ss)
                SimpleDateFormat dateFormat = new SimpleDateFormat(STANDARD_DATE_FORMAT);
                return dateFormat.parse(text);
            } catch (ParseException e) {
                try {
                    // 尝试ISO格式 (带T的格式)
                    if (text.contains("T")) {
                        SimpleDateFormat isoFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                        return isoFormat.parse(text);
                    }
                    // 尝试解析纯日期格式 (yyyy-MM-dd)
                    SimpleDateFormat dateOnlyFormat = new SimpleDateFormat("yyyy-MM-dd");
                    return dateOnlyFormat.parse(text);
                } catch (ParseException e1) {
                    try {
                        // 尝试其他常见格式
                        if (text.contains("/")) {
                            SimpleDateFormat slashFormat = new SimpleDateFormat("yyyy/MM/dd");
                            return slashFormat.parse(text);
                        }
                        throw new IOException("无法解析日期时间字符串：" + text);
                    } catch (Exception e2) {
                        throw new IOException("无法将日期字符串 '" + text + "' 转换为Date", e2);
                    }
                }
            }
        }
    }
    
    /**
     * Date序列化器
     * 将Date统一序列化为标准格式：yyyy-MM-dd HH:mm:ss
     */
    public static class DateSerializer extends JsonSerializer<Date> {
        @Override
        public void serialize(Date value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            if (value == null) {
                gen.writeNull();
            } else {
                SimpleDateFormat dateFormat = new SimpleDateFormat(STANDARD_DATE_FORMAT);
                gen.writeString(dateFormat.format(value));
            }
        }
    }
} 