package com.xiao.temu.modules.purchaseorderv.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderV;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 备货单数据访问接口
 */
@Mapper
public interface PurchaseOrderVMapper extends BaseMapper<PurchaseOrderV> {
    
    /**
     * 批量插入备货单数据
     *
     * @param purchaseOrders 备货单列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<PurchaseOrderV> purchaseOrders);
    
    /**
     * 批量更新备货单数据
     *
     * @param purchaseOrders 备货单列表
     * @return 影响行数
     */
    int batchUpdate(@Param("list") List<PurchaseOrderV> purchaseOrders);
    
    /**
     * 根据店铺ID和备货单号查询备货单
     *
     * @param shopId 店铺ID
     * @param subPurchaseOrderSn 备货单号
     * @return 备货单信息
     */
    PurchaseOrderV getByShopIdAndOrderSn(@Param("shopId") Long shopId, @Param("subPurchaseOrderSn") String subPurchaseOrderSn);
    
    /**
     * 根据店铺ID和备货单号查询备货单(包括已删除的记录)
     *
     * @param shopId 店铺ID
     * @param subPurchaseOrderSn 备货单号
     * @return 备货单信息
     */
    PurchaseOrderV getByShopIdAndOrderSnIncludeDeleted(@Param("shopId") Long shopId, @Param("subPurchaseOrderSn") String subPurchaseOrderSn);
    
    /**
     * 根据店铺ID和订单编号强制更新备货单数据(不检查is_deleted状态)
     *
     * @param shopId 店铺ID
     * @param subPurchaseOrderSn 备货单编号
     * @param order 备货单数据
     * @return 影响行数
     */
    int updateByShopIdAndOrderSnForce(@Param("shopId") Long shopId, @Param("subPurchaseOrderSn") String subPurchaseOrderSn, @Param("order") PurchaseOrderV order);
    
    /**
     * 获取指定店铺的备货单数量
     *
     * @param shopId 店铺ID
     * @param purchaseStockType 备货类型，null表示全部
     * @return 备货单数量
     */
    int countByShopId(@Param("shopId") Long shopId, @Param("purchaseStockType") Integer purchaseStockType);
    
    /**
     * 根据店铺ID和备货类型删除备货单数据
     *
     * @param shopId 店铺ID
     * @param purchaseStockType 备货类型，null表示全部
     * @return 影响行数
     */
    int deleteByShopId(@Param("shopId") Long shopId, @Param("purchaseStockType") Integer purchaseStockType);
    
    /**
     * 根据店铺ID和备货类型物理删除备货单数据
     *
     * @param shopId 店铺ID
     * @param purchaseStockType 备货类型，null表示全部
     * @return 影响行数
     */
    int physicalDeleteByShopId(@Param("shopId") Long shopId, @Param("purchaseStockType") Integer purchaseStockType);
    
    /**
     * 查询JIT即将逾期的备货单（到货时间在未来24小时内）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 备货单列表
     */
    List<PurchaseOrderV> findJitOrdersAboutToExpire(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
    
    /**
     * 查询已逾期的JIT备货单（昨天已过期）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 备货单列表
     */
    List<PurchaseOrderV> findExpiredJitOrders(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
    
    /**
     * 查询长时间未发货的普通备货单（创建时间超过5天仍未发货）
     *
     * @param createdBefore 创建时间早于此时间
     * @return 备货单列表
     */
    List<PurchaseOrderV> findLongTimeNotShippedOrders(@Param("createdBefore") Date createdBefore);
    
    /**
     * 查询已发货但长时间未到货的普通备货单（发货时间超过5天仍未收货）
     *
     * @param shippedBefore 发货时间早于此时间
     * @return 备货单列表
     */
    List<PurchaseOrderV> findShippedButNotReceivedOrders(@Param("shippedBefore") Date shippedBefore);
} 