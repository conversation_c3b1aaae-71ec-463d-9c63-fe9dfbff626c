// 店铺违规信息类型定义

import type { PageParams, PageResult } from '../api'

// 店铺违规信息项
export interface ViolationInfoItem {
  id: number
  shopId: number
  shopName: string
  shopRemark?: string
  punishSn: string
  subPurchaseOrderSn: string
  punishTypeCode: number
  punishTypeDesc: string
  punishFirstTypeCode: number
  punishFirstTypeDesc: string
  punishSecondTypeDesc: string
  violationStartTime: number
  violationTime: string
  punishAmount: number
  punishAmountCurrency: string
  punishStatus: number
  punishStatusDesc: string
  viewDetailsStatus: number
  countdownTime: number
  syncTime: string
  appealStatus?: number
  appealStatusDesc?: string
  productSkuId?: number
  stockQuantity?: number
  lackQuantity?: number
  unqualifiedQuantity?: number
}

// 店铺违规详情项
export interface ViolationDetailItem {
  id: number
  shopId: number
  punishSn: string
  appealSn?: string
  appealStatus?: number
  appealTime?: number
  appealReason?: string
  appealMaterial?: string[] // 申诉材料图片URL列表
  productSkuId?: number
  stockQuantity?: number
  lackQuantity?: number
  unqualifiedQuantity?: number
  subPurchaseOrderSn?: string
  operateResult?: string
  operateTime?: number
  operateRemark?: string
}

// 完整的违规信息，包含详情
export interface CompleteViolationInfo extends ViolationInfoItem {
  detail?: ViolationDetailItem
}

// 查询参数
export interface ViolationQueryParams extends PageParams {
  shopId?: number
  shopIds?: number[]
  punishSn?: string
  subPurchaseOrderSn?: string
  punishStatus?: number | null
  // ISO日期时间格式 YYYY-MM-DDThh:mm:ss
  violationTimeBegin?: string
  // ISO日期时间格式 YYYY-MM-DDThh:mm:ss
  violationTimeEnd?: string
  groupId?: number
  pageNum?: number
  pageSize?: number
}

// 查询结果
export interface ViolationQueryResult extends PageResult<ViolationInfoItem> {
  items: ViolationInfoItem[]
  total: number
  pageNum: number
  pageSize: number
} 