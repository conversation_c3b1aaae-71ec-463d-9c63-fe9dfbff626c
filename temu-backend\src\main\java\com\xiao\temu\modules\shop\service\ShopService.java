package com.xiao.temu.modules.shop.service;

import com.xiao.temu.common.response.PageResult;
import com.xiao.temu.modules.shop.dto.ShopDTO;
import com.xiao.temu.modules.shop.dto.ShopQueryDTO;
import com.xiao.temu.modules.shop.entity.Shop;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 店铺服务接口
 */
public interface ShopService {
    
    /**
     * 分页查询店铺列表
     *
     * @param queryDTO 查询条件
     * @return 店铺分页数据
     */
    PageResult<ShopDTO> listShops(ShopQueryDTO queryDTO);
    
    /**
     * 根据ID获取店铺详情
     *
     * @param shopId 店铺ID
     * @return 店铺详情
     */
    ShopDTO getShopById(Long shopId);
    
    /**
     * 根据Temu平台店铺ID获取店铺详情
     *
     * @param mallId Temu平台店铺ID
     * @return 店铺详情
     */
    Shop getShopByMallId(String mallId);
    
    /**
     * 新增店铺
     *
     * @param shop 店铺信息
     * @return 是否成功
     */
    boolean addShop(Shop shop);
    
    /**
     * 新增店铺（带创建者和所属运营组）
     *
     * @param shop 店铺信息
     * @param userId 创建者ID
     * @param groupId 所属运营组ID
     * @return 是否成功
     */
    boolean addShop(Shop shop, Long userId, Long groupId);
    
    /**
     * 修改店铺
     *
     * @param shop 店铺信息
     * @return 是否成功
     */
    boolean updateShop(Shop shop);
    
    /**
     * 删除店铺
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    boolean deleteShop(Long shopId);
    
    /**
     * 查询用户有权限的店铺列表
     *
     * @param userId 用户ID
     * @return 店铺列表
     */
    List<Shop> listUserShops(Long userId);

    /**
     * 查询组长有权限的店铺列表
     * 
     * @param leaderId 组长ID
     * @return 店铺列表
     */
    List<Shop> listLeaderShops(Long leaderId);
    
    /**
     * 查询运营组的店铺列表
     * 
     * @param groupId 运营组ID
     * @return 店铺列表
     */
    List<Shop> listGroupShops(Long groupId);
    
    /**
     * 查询运营组的所有关联店铺列表，包括那些被移除了belongGroupId的店铺
     * 
     * @param groupId 运营组ID
     * @return 店铺列表
     */
    List<Shop> listAllGroupShops(Long groupId);
    
    /**
     * 检查用户是否有店铺操作权限
     *
     * @param userId 用户ID
     * @param shopId 店铺ID
     * @param requireWrite 是否需要写权限
     * @return 是否有权限
     */
    boolean checkShopPermission(Long userId, Long shopId, boolean requireWrite);

    /**
     * 获取用户在店铺的权限类型
     * 
     * @param userId 用户ID
     * @param shopId 店铺ID
     * @return 权限类型 null-无权限 0-只读 1-读写
     */
    String getUserShopPermissionType(Long userId, Long shopId);
    
    /**
     * 检查店铺是否属于指定运营组
     *
     * @param shopId 店铺ID
     * @param groupId 运营组ID
     * @return 是否属于
     */
    boolean checkShopInGroup(Long shopId, Long groupId);

    /**
     * 检查店铺名称是否唯一
     *
     * @param shopName 店铺名称
     * @return 是否唯一 true-唯一 false-不唯一
     */
    boolean checkShopNameUnique(String shopName);
    
    /**
     * 批量导入店铺
     *
     * @param shopList 店铺列表
     * @param shopGroupMap 店铺与运营组映射关系 (店铺名称 -> 运营组ID列表)
     * @return 导入成功的数量
     */
    int batchImportShops(List<Shop> shopList, Map<String, List<Long>> shopGroupMap);
    
    /**
     * 从Excel导入店铺数据
     *
     * @param inputStream Excel文件输入流
     * @return 导入结果，包括成功数量、失败数量、错误信息等
     */
    Map<String, Object> importShopsFromExcel(InputStream inputStream);
    
    /**
     * 获取店铺导入Excel模板
     *
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    void getImportTemplate(HttpServletResponse response) throws IOException;

    /**
     * 批量删除店铺
     *
     * @param shopIds 店铺ID列表
     * @return 是否成功
     */
    boolean batchDeleteShops(List<Long> shopIds);

    /**
     * 获取所有店铺
     *
     * @return 所有店铺列表
     */
    List<Shop> listAllShops();

    /**
     * 获取用户作为组员所在组的店铺列表
     *
     * @param userId 用户ID
     * @return 店铺列表
     */
    List<Shop> listMemberShops(Long userId);
} 