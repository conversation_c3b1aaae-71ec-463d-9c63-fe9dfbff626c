package com.xiao.temu.modules.sync.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.sync.entity.SalesSyncTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 销售数据同步任务Mapper接口
 */
@Mapper
public interface SalesSyncTaskMapper extends BaseMapper<SalesSyncTask> {
    
    /**
     * 根据店铺ID查询同步任务
     *
     * @param shopId 店铺ID
     * @return 同步任务
     */
    @Select("SELECT * FROM sales_sync_task WHERE shop_id = #{shopId}")
    SalesSyncTask findByShopId(@Param("shopId") Long shopId);

    /**
     * 根据店铺ID列表查询同步任务
     *
     * @param shopIds 店铺ID列表
     * @return 同步任务列表
     */
    List<SalesSyncTask> findByShopIds(@Param("shopIds") List<Long> shopIds);
} 