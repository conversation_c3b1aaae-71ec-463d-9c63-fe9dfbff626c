package com.xiao.temu.modules.shiporderv.service;

import com.xiao.temu.modules.shiporderv.dto.ShipOrderRequestDTO;
import com.xiao.temu.modules.shiporderv.vo.ShipOrderVO;

/**
 * Temu发货单服务接口
 */
public interface ShipOrderService {
    /**
     * 获取发货单列表
     *
     * @param requestDTO 请求参数
     * @param userId 当前用户ID
     * @return 发货单列表
     */
    ShipOrderVO getShipOrderList(ShipOrderRequestDTO requestDTO, Long userId);
} 