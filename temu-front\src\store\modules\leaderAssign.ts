import { defineStore } from 'pinia'
import { 
  getShopAssignmentList,
  getAllShopAssignmentsByGroupId, 
  assignShop, 
  unassignShop, 
  updatePermissionType,
  getLeaderAssignableShops,
  getLeaderGroupMembers,
  batchAssignShops,
  createMember
} from '@/api/leaderAssign'
import type { ShopAssignmentQuery, AssignShopParams, UpdatePermissionParams, BatchAssignShopParams, CreateMemberParams } from '@/types/leaderAssign'
import { getUserGroups, getGroupList } from '@/api/group'
import { useUserStore } from '@/store/modules/user'

interface LeaderAssignState {
  assignmentList: any[];
  total: number;
  loading: boolean;
  assignableShops: any[];
  groupMembers: any[];
  userGroups: any[];
  selectedGroupId: number | null;
  isAdmin: boolean;
}

export const useLeaderAssignStore = defineStore('leaderAssign', {
  state: (): LeaderAssignState => ({
    assignmentList: [],
    total: 0,
    loading: false,
    assignableShops: [],
    groupMembers: [],
    userGroups: [],
    selectedGroupId: null,
    isAdmin: false
  }),
  actions: {
    // 获取分配列表
    async getAssignmentList(query: ShopAssignmentQuery) {
      this.loading = true
      try {
        console.log('正在调用获取分配列表API，参数:', query);
        const response = await getShopAssignmentList(query)
        console.log('API返回数据:', response);
        
        if (response && response.data) {
          this.assignmentList = response.data.list || [];
          this.total = response.data.total || 0;
          console.log('更新store状态: assignmentList=', this.assignmentList, 'total=', this.total);
        } else {
          console.error('API返回数据格式不正确:', response);
          this.assignmentList = [];
          this.total = 0;
        }
        
        return response
      } catch (error) {
        console.error('获取分配列表失败:', error);
        this.assignmentList = [];
        this.total = 0;
        throw error;
      } finally {
        this.loading = false
      }
    },
    
    // 获取运营组的所有分配数据（不分页）
    async getAllAssignmentsByGroupId(groupId: number) {
      this.loading = true
      try {
        console.log('正在调用获取运营组所有分配数据API，运营组ID:', groupId);
        const response = await getAllShopAssignmentsByGroupId(groupId)
        console.log('API返回数据:', response);
        
        if (response && response.data) {
          this.assignmentList = response.data || [];
          this.total = this.assignmentList.length;
          console.log('更新store状态: assignmentList=', this.assignmentList, 'total=', this.total);
        } else {
          console.error('API返回数据格式不正确:', response);
          this.assignmentList = [];
          this.total = 0;
        }
        
        return response
      } catch (error) {
        console.error('获取运营组所有分配数据失败:', error);
        this.assignmentList = [];
        this.total = 0;
        throw error;
      } finally {
        this.loading = false
      }
    },
    
    // 分配店铺
    async assignShop(data: AssignShopParams) {
      try {
        console.log('正在调用分配店铺API，参数:', data);
        const response = await assignShop(data)
        console.log('分配店铺成功:', response);
        return response
      } catch (error) {
        console.error('分配店铺失败:', error);
        throw error;
      }
    },
    
    // 批量分配店铺
    async batchAssignShops(data: BatchAssignShopParams) {
      try {
        console.log('正在调用批量分配店铺API，参数:', data);
        const response = await batchAssignShops(data)
        console.log('批量分配店铺成功:', response);
        return response
      } catch (error) {
        console.error('批量分配店铺失败:', error);
        throw error;
      }
    },
    
    // 取消分配
    async unassignShop(id: number) {
      try {
        console.log('正在取消分配，ID:', id);
        const response = await unassignShop(id)
        console.log('取消分配成功:', response);
        return response
      } catch (error) {
        console.error('取消分配失败:', error);
        throw error;
      }
    },
    
    // 修改权限
    async updatePermission(data: UpdatePermissionParams) {
      try {
        console.log('正在修改权限，参数:', data);
        const response = await updatePermissionType(data)
        console.log('修改权限成功:', response);
        return response
      } catch (error) {
        console.error('修改权限失败:', error);
        throw error;
      }
    },
    
    // 检查当前用户是否为管理员
    checkIsAdmin() {
      const userStore = useUserStore();
      this.isAdmin = userStore.roles.includes('admin');
      console.log('当前用户是否为管理员:', this.isAdmin);
      return this.isAdmin;
    },
    
    // 获取用户所在的运营组
    async getUserGroups() {
      try {
        console.log('正在获取用户运营组');
        this.loading = true;
        
        // 检查是否为管理员
        const isAdmin = this.checkIsAdmin();
        
        // 管理员获取所有运营组，普通用户获取自己所在的运营组
        let response;
        if (isAdmin) {
          console.log('管理员用户，获取所有运营组');
          response = await getGroupList({ pageSize: 100 }); // 获取所有运营组
          
          if (response && response.data) {
            this.userGroups = response.data.records || [];
            console.log('管理员获取到的运营组:', this.userGroups);
          }
        } else {
          response = await getUserGroups();
          
          if (response && response.data) {
            this.userGroups = response.data || [];
          }
        }
        
        console.log('获取用户运营组成功:', this.userGroups);
        
        // 如果有运营组，默认选择第一个
        if (this.userGroups.length > 0) {
          this.selectedGroupId = this.userGroups[0].groupId;
          console.log('自动选择第一个运营组:', this.selectedGroupId);
        }
        
        return response;
      } catch (error) {
        console.error('获取用户运营组失败:', error);
        this.userGroups = [];
        throw error;
      } finally {
        this.loading = false;
      }
    },
    
    // 设置当前选中的运营组
    setSelectedGroup(groupId: number) {
      console.log('设置当前选中的运营组:', groupId);
      this.selectedGroupId = groupId;
    },
    
    // 获取可分配的店铺
    async getAssignableShops(groupId: number) {
      try {
        console.log('正在获取可分配的店铺，运营组ID:', groupId);
        const response = await getLeaderAssignableShops(groupId)
        console.log('获取可分配店铺成功:', response);
        
        if (response && response.data) {
          this.assignableShops = response.data || [];
        } else {
          console.error('获取可分配店铺返回数据格式不正确:', response);
          this.assignableShops = [];
        }
        
        return response
      } catch (error) {
        console.error('获取可分配店铺失败:', error);
        this.assignableShops = [];
        throw error;
      }
    },
    
    // 获取组员列表
    async getGroupMembers(groupId: number) {
      try {
        console.log('正在获取组员列表，运营组ID:', groupId);
        const response = await getLeaderGroupMembers(groupId)
        console.log('获取组员列表成功:', response);
        
        if (response && response.data) {
          this.groupMembers = response.data || [];
        } else {
          console.error('获取组员列表返回数据格式不正确:', response);
          this.groupMembers = [];
        }
        
        return response
      } catch (error) {
        console.error('获取组员列表失败:', error);
        this.groupMembers = [];
        throw error;
      }
    },
    
    // 创建新组员
    async createMember(data: CreateMemberParams) {
      try {
        console.log('正在创建新组员，参数:', data);
        const response = await createMember(data);
        console.log('创建组员成功:', response);
        
        // 刷新组员列表
        await this.getGroupMembers(data.groupId);
        
        return response;
      } catch (error) {
        console.error('创建组员失败:', error);
        throw error;
      }
    },
    
    // 重置状态
    resetState() {
      console.log('重置store状态');
      this.assignmentList = [];
      this.total = 0;
      this.assignableShops = [];
      this.groupMembers = [];
      this.selectedGroupId = null;
      // 不重置isAdmin状态
    }
  }
}) 