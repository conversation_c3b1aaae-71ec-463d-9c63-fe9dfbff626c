package com.xiao.temu.infrastructure.exporter;


import cn.hutool.core.lang.UUID;
import com.xiao.temu.modules.sales.dto.LocalSalesRequestDTO;
import com.xiao.temu.modules.sales.vo.*;
import com.xiao.temu.modules.system.service.SysDataPermissionService;
import com.xiao.temu.modules.system.service.UserService;
import com.xiao.temu.modules.sales.service.LocalSalesService;
import com.xiao.temu.infrastructure.excel.ExcelExportService;
import com.xiao.temu.infrastructure.excel.ExcelExportUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 本地销售数据Excel导出实现类
 */
@Slf4j
@Service
public class SalesExcelExporter implements ExcelExportService.ExcelExporter {

    @Autowired
    private ExcelExportService excelExportService;
    
    @Autowired
    private LocalSalesService localSalesService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private SysDataPermissionService dataPermissionService;
    
    // 将headers和columnWidths定义为类级别变量，解决作用域问题
    private static final String[] HEADERS = {
        "店铺代号", "店铺名", "商品图片", "商品信息", "SKU信息", "申报价格(CNY)", "开款价格状态", "缺货数量", 
        "销售数据-今日", "销售数据-近7天", "销售数据-近30天", 
        "仓内可用库存", "已发货库存", "待发货库存",
        "备货逻辑", "建议备货量", "库存可售天数", "仓内库存可售天数", "可售天数"
    };
    
    private static final int[] COLUMN_WIDTHS = {
        15, 15, 25, 40, 20, 15, 15, 10, 10, 10, 10, 15, 15, 15, 15, 15, 15, 15, 15
    };
    
    @Override
    public void export(Map<String, Object> exportParams, String taskId) {
        // 获取文件名
        String fileName = (String) exportParams.get("fileName");
        if (fileName == null || fileName.trim().isEmpty()) {
            fileName = "本地销售数据";
        }

        // 执行导出
        excelExportService.exportExcel(
            taskId,
            fileName,
            "销售数据",
            HEADERS,
            COLUMN_WIDTHS,
            new SalesDataProcessor(exportParams)
        );
    }
    
    /**
     * 销售数据处理器
     */
    private class SalesDataProcessor implements ExcelExportService.ExcelDataProcessor {
        
        private final Map<String, Object> exportParams;
        
        public SalesDataProcessor(Map<String, Object> exportParams) {
            this.exportParams = exportParams;
        }
        
        @Override
        public void prepareData(ExcelExportService.ExcelExportContext context) {
            try {
                // 从导出参数中获取用户ID
                Object userIdObj = exportParams.get("userId");
                if (userIdObj == null) {
                    throw new RuntimeException("导出参数中缺少用户ID");
                }
                Long userId = Long.valueOf(userIdObj.toString());
                
                // 判断用户是否为管理员或拥有全部数据权限
                boolean isAdmin = userService.isAdmin(userId);
                
                // 获取用户的最高数据权限
                String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
                boolean hasFullDataPermission = "2".equals(permissionType);
                
                // 解析导出参数
                Map<String, Object> queryParams = (Map<String, Object>) exportParams.get("queryParams");
                String exportType = (String) exportParams.get("exportType");
                
                // 构建查询参数
                LocalSalesRequestDTO requestDTO = new LocalSalesRequestDTO();
                
                // 设置查询参数
                if (queryParams != null) {
                    // 处理店铺ID列表
                    if (queryParams.get("shopIds") != null) {
                        List<?> rawShopIds = (List<?>) queryParams.get("shopIds");
                        List<Long> shopIdList = new ArrayList<>();
                        for (Object id : rawShopIds) {
                            if (id instanceof Integer) {
                                shopIdList.add(((Integer) id).longValue());
                            } else if (id instanceof Long) {
                                shopIdList.add((Long) id);
                            } else if (id instanceof String) {
                                shopIdList.add(Long.valueOf((String) id));
                            } else {
                                shopIdList.add(Long.valueOf(id.toString()));
                            }
                        }
                        requestDTO.setShopIds(shopIdList);
                    }
                    
                    // 处理快速筛选
                    if (queryParams.get("quickFilter") != null) {
                        requestDTO.setQuickFilter((String) queryParams.get("quickFilter"));
                    }
                    
                    // 处理SPU ID列表
                    if (queryParams.get("spuIdList") != null) {
                        if (queryParams.get("spuIdList") instanceof List) {
                            requestDTO.setSpuIdList((List<Long>) queryParams.get("spuIdList"));
                        } else if (queryParams.get("spuIdList") instanceof String) {
                            requestDTO.setSpuIdListStr((String) queryParams.get("spuIdList"));
                        }
                    }
                    
                    // 处理SKC ID列表
                    if (queryParams.get("skcIdList") != null) {
                        if (queryParams.get("skcIdList") instanceof List) {
                            requestDTO.setSkcIdList((List<Long>) queryParams.get("skcIdList"));
                        } else if (queryParams.get("skcIdList") instanceof String) {
                            requestDTO.setSkcIdListStr((String) queryParams.get("skcIdList"));
                        }
                    }
                    
                    // 处理其他基本查询参数
                    if (queryParams.get("productNameKeyword") != null) {
                        requestDTO.setProductNameKeyword((String) queryParams.get("productNameKeyword"));
                    }
                    
                    if (queryParams.get("isCustomGoods") != null) {
                        requestDTO.setIsCustomGoods((Boolean) queryParams.get("isCustomGoods"));
                    }
                    
                    if (queryParams.get("settlementType") != null) {
                        requestDTO.setSettlementType(Integer.valueOf(queryParams.get("settlementType").toString()));
                    }
                    
                    if (queryParams.get("purchaseStockType") != null) {
                        requestDTO.setPurchaseStockType(Integer.valueOf(queryParams.get("purchaseStockType").toString()));
                    }
                    
                    if (queryParams.get("hotTag") != null) {
                        requestDTO.setHotTag(Integer.valueOf(queryParams.get("hotTag").toString()));
                    }
                    
                    // 处理高级查询参数
                    if (queryParams.get("inventoryRegionList") != null) {
                        requestDTO.setInventoryRegionList((List<Integer>) queryParams.get("inventoryRegionList"));
                    }
                    
                    if (queryParams.get("warehouseGroupId") != null) {
                        requestDTO.setWarehouseGroupId(Long.valueOf(queryParams.get("warehouseGroupId").toString()));
                    }
                    
                    if (queryParams.get("closeJitStatus") != null) {
                        requestDTO.setCloseJitStatus((List<Integer>) queryParams.get("closeJitStatus"));
                    }
                    
                    if (queryParams.get("autoCloseJit") != null) {
                        requestDTO.setAutoCloseJit((Boolean) queryParams.get("autoCloseJit"));
                    }
                    
                    if (queryParams.get("pictureAuditStatusList") != null) {
                        requestDTO.setPictureAuditStatusList((List<Integer>) queryParams.get("pictureAuditStatusList"));
                    }
                    
                    if (queryParams.get("skcExtCodeList") != null) {
                        requestDTO.setSkcExtCode((String) queryParams.get("skcExtCodeList"));
                    }
                    
                    if (queryParams.get("skuExtCodeList") != null) {
                        requestDTO.setSkuExtCodeList((String) queryParams.get("skuExtCodeList"));
                    }
                    
                    if (queryParams.get("minAvailableSaleDays") != null) {
                        requestDTO.setMinAvailableSaleDays(Integer.valueOf(queryParams.get("minAvailableSaleDays").toString()));
                    }
                    
                    if (queryParams.get("maxAvailableSaleDays") != null) {
                        requestDTO.setMaxAvailableSaleDays(Integer.valueOf(queryParams.get("maxAvailableSaleDays").toString()));
                    }
                    
                    if (queryParams.get("supplyStatusList") != null) {
                        requestDTO.setSupplyStatusList((List<Integer>) queryParams.get("supplyStatusList"));
                    }
                    
                    if (queryParams.get("onSalesDurationOfflineGte") != null) {
                        requestDTO.setOnSalesDurationOfflineGte(Integer.valueOf(queryParams.get("onSalesDurationOfflineGte").toString()));
                    }
                    
                    if (queryParams.get("onSalesDurationOfflineLte") != null) {
                        requestDTO.setOnSalesDurationOfflineLte(Integer.valueOf(queryParams.get("onSalesDurationOfflineLte").toString()));
                    }
                    
                    // 处理销量相关参数
                    if (queryParams.get("saleVolumeTimeRange") != null) {
                        requestDTO.setSaleVolumeTimeRange(Integer.valueOf(queryParams.get("saleVolumeTimeRange").toString()));
                    }
                    
                    if (queryParams.get("todaySaleVolumMin") != null) {
                        requestDTO.setTodaySaleVolumMin(Integer.valueOf(queryParams.get("todaySaleVolumMin").toString()));
                    }
                    
                    if (queryParams.get("todaySaleVolumMax") != null) {
                        requestDTO.setTodaySaleVolumMax(Integer.valueOf(queryParams.get("todaySaleVolumMax").toString()));
                    }
                    
                    if (queryParams.get("lastSevenDaysSaleVolumeMin") != null) {
                        requestDTO.setLastSevenDaysSaleVolumeMin(Integer.valueOf(queryParams.get("lastSevenDaysSaleVolumeMin").toString()));
                    }
                    
                    if (queryParams.get("lastSevenDaysSaleVolumeMax") != null) {
                        requestDTO.setLastSevenDaysSaleVolumeMax(Integer.valueOf(queryParams.get("lastSevenDaysSaleVolumeMax").toString()));
                    }
                    
                    if (queryParams.get("lastThirtyDaysSaleVolumeMin") != null) {
                        requestDTO.setLastThirtyDaysSaleVolumeMin(Integer.valueOf(queryParams.get("lastThirtyDaysSaleVolumeMin").toString()));
                    }
                    
                    if (queryParams.get("lastThirtyDaysSaleVolumeMax") != null) {
                        requestDTO.setLastThirtyDaysSaleVolumeMax(Integer.valueOf(queryParams.get("lastThirtyDaysSaleVolumeMax").toString()));
                    }
                    
                    // 添加排序参数处理
                    if (queryParams.get("sortField") != null) {
                        requestDTO.setSortField(Integer.valueOf(queryParams.get("sortField").toString()));
                        log.info("设置排序字段: {}", requestDTO.getSortField());
                    }
                    
                    if (queryParams.get("sortDirection") != null) {
                        requestDTO.setSortDirection((String) queryParams.get("sortDirection"));
                        log.info("设置排序方向: {}", requestDTO.getSortDirection());
                    }
                    
                    // 设置分页参数
                    if (queryParams.get("pageNo") != null) {
                        requestDTO.setPageNo(Math.toIntExact(Long.valueOf(queryParams.get("pageNo").toString())));
                    } else {
                        requestDTO.setPageNo(1); // 默认第一页
                    }
                    
                    if (queryParams.get("pageSize") != null) {
                        requestDTO.setPageSize(Math.toIntExact(Long.valueOf(queryParams.get("pageSize").toString())));
                    } else {
                        requestDTO.setPageSize(Integer.MAX_VALUE); // 默认导出全部
                    }
                }
                
                // 如果是管理员或拥有全部数据权限，设置忽略权限检查标志
                if (isAdmin || hasFullDataPermission) {
                    requestDTO.setIgnorePermissionCheck(true);
                }
                
                // 处理不同导出类型
                List<LocalSalesItemVO> allItems = new ArrayList<>();
                
                if ("page".equals(exportType) || "current".equals(exportType)) {
                    // 导出当前页数据
                    log.info("导出当前页数据，页码: {}, 每页条数: {}", requestDTO.getPageNo(), requestDTO.getPageSize());
                    LocalSalesVO result = localSalesService.getLocalSalesList(requestDTO, userId);
                    allItems.addAll(result.getList());
                } else if ("custom".equals(exportType)) {
                    // 导出自定义数量数据 - 多页
                    Integer exportPageCount = 1;
                    if (queryParams.get("exportPageCount") != null) {
                        exportPageCount = Math.toIntExact(Long.valueOf(queryParams.get("exportPageCount").toString()));
                    }
                    
                    // 初始页码
                    int startPage = requestDTO.getPageNo();
                    // 设置每页条数
                    int pageSize = requestDTO.getPageSize();
                    
                    // 分页查询数据
                    for (int i = 0; i < exportPageCount; i++) {
                        requestDTO.setPageNo(startPage + i);
                        LocalSalesVO result = localSalesService.getLocalSalesList(requestDTO, userId);
                        if (result.getList() != null && !result.getList().isEmpty()) {
                            allItems.addAll(result.getList());
                        }
                        
                        // 如果返回的数据条数少于pageSize，说明已经没有更多数据，跳出循环
                        if (result.getList() == null || result.getList().size() < pageSize) {
                            break;
                        }
                    }
                } else {
                    // 导出全部数据 - 单次大查询
                    requestDTO.setPageSize(Integer.MAX_VALUE); // 设置为最大值
                    LocalSalesVO result = localSalesService.getLocalSalesList(requestDTO, userId);
                    allItems.addAll(result.getList());
                }
                
                // 设置导出上下文
                context.setData(allItems);
                context.setDataSize(allItems.size());
                
            } catch (Exception e) {
                log.error("准备导出数据失败", e);
                throw new RuntimeException("准备导出数据失败: " + e.getMessage(), e);
            }
        }
        
        @Override
        public void prepareImageTasks(ExcelExportService.ExcelExportContext context, File tempDir, List<ExcelExportUtils.ImageDownloadTask> downloadTasks) {
            @SuppressWarnings("unchecked")
            List<LocalSalesItemVO> items = (List<LocalSalesItemVO>) context.getData();
            
            int totalEstimatedImages = 0;
            int adjustedRowIndex = 0; // 添加行索引调整变量，考虑SKU合并
            
            for (int i = 0; i < items.size(); i++) {
                LocalSalesItemVO item = items.get(i);
                
                // 商品缩略图 - 正确设置图片位置，确保不占用其他列
                if (item.getProductSkcPicture() != null && !item.getProductSkcPicture().isEmpty()) {
                    // 为商品缩略图创建下载任务
                    String imageUrl = item.getProductSkcPicture();
                    String fileExtension = imageUrl.substring(imageUrl.lastIndexOf('.') + 1);
                    if (fileExtension.length() > 4) fileExtension = "jpg"; // 默认jpg
                    
                    String fileName = "product_" + item.getProductSkcId() + "_" + UUID.randomUUID() + "." + fileExtension;
                    File targetFile = new File(tempDir, fileName);
                    
                    // 设置自定义图片任务，确保不占用其他列 - 图片放在第3列（索引2）
                    ExcelExportUtils.ImageDownloadTask task = new ExcelExportUtils.ImageDownloadTask(imageUrl, targetFile, 2, adjustedRowIndex);
                    
                    // 设置图片跨行数，但不跨列(colSpan设为1)
                    int numRows = item.getSkuList() != null && !item.getSkuList().isEmpty() ? item.getSkuList().size() : 1;
                    task.setRowSpan(numRows);  // 跨行数量取决于SKU的数量
                    task.setColSpan(1);        // 不跨列，保持在列范围内
                    
                    // 添加下载任务
                    downloadTasks.add(task);
                    totalEstimatedImages++;
                }
                
                // 调整下一个商品的行索引，如果有SKU列表则加上SKU列表的大小，否则加1
                if (item.getSkuList() != null && !item.getSkuList().isEmpty()) {
                    adjustedRowIndex += item.getSkuList().size();
                } else {
                    adjustedRowIndex += 1;
                }
            }
            
            log.info("准备了{}个图片下载任务", totalEstimatedImages);
        }
        
        @Override
        public void fillExcelData(ExcelExportService.ExcelExportContext context, Sheet sheet, CellStyle contentStyle) {
            @SuppressWarnings("unchecked")
            List<LocalSalesItemVO> items = (List<LocalSalesItemVO>) context.getData();
            Workbook workbook = sheet.getWorkbook();
            
            // 创建一个单元格样式用于商品信息列，允许文本换行
            CellStyle wrapStyle = workbook.createCellStyle();
            wrapStyle.cloneStyleFrom(contentStyle);
            wrapStyle.setWrapText(true);
            
            // 创建一个单元格样式用于数字列，靠右对齐
            CellStyle numberStyle = workbook.createCellStyle();
            numberStyle.cloneStyleFrom(contentStyle);
            numberStyle.setAlignment(HorizontalAlignment.RIGHT);
            
            // 创建一个单元格样式用于图片列，增大行高
            CellStyle imageStyle = workbook.createCellStyle();
            imageStyle.cloneStyleFrom(contentStyle);
            
            // 设置图片列宽度（避免图片溢出）
            sheet.setColumnWidth(2, 25 * 256); // 设置第三列(图片列)宽度为25个字符宽
            
            // 处理数据行
            int rowIndex = 1; // 从第2行开始，第1行是表头
            
            for (LocalSalesItemVO item : items) {
                Row row = sheet.createRow(rowIndex);
                
                // 设置行高 - 所有单元格行高设置为40
                row.setHeightInPoints(40);
                
                int colIndex = 0;
                
                // 店铺备注 - 第1列
                Cell shopRemarkCell = row.createCell(colIndex++);
                if (item.getShopRemark() != null && !item.getShopRemark().isEmpty()) {
                    shopRemarkCell.setCellValue(item.getShopRemark());
                } else {
                    shopRemarkCell.setCellValue("-");
                }
                shopRemarkCell.setCellStyle(contentStyle);
                
                // 店铺名 - 第2列
                Cell shopNameCell = row.createCell(colIndex++);
                shopNameCell.setCellValue(item.getShopName());
                shopNameCell.setCellStyle(contentStyle);
                
                // 图片列 - 第3列，留空，图片由ExcelExportService处理
                Cell imageCell = row.createCell(colIndex++);
                imageCell.setCellStyle(imageStyle);
                
                // 商品信息
                Cell productCell = row.createCell(colIndex++);
                StringBuilder productInfo = new StringBuilder();
                if (item.getProductName() != null) {
                    productInfo.append("商品名称: ").append(item.getProductName()).append("\n");
                }
                productInfo.append("SPU ID: ").append(item.getProductId()).append("\n");
                productInfo.append("SKC ID: ").append(item.getProductSkcId()).append("\n");
                if (item.getSkcExtCode() != null) {
                    productInfo.append("SKC货号: ").append(item.getSkcExtCode()).append("\n");
                }
                if (item.getOnSalesDurationOffline() > 0) {
                    productInfo.append("加入站点时长: ").append(item.getOnSalesDurationOffline()).append(" 天\n");
                }
                
                // 添加商品标签信息
                List<String> tags = new ArrayList<>();
                if (item.getInventoryRegion() != null) {
                    switch (item.getInventoryRegion()) {
                        case 1:
                            tags.add("国内备货");
                            break;
                        case 2:
                            tags.add("海外备货");
                            break;
                        case 3:
                            tags.add("保税仓备货");
                            break;
                    }
                }
                
                if (item.getPurchaseStockType() != null && item.getPurchaseStockType() == 1) {
                    tags.add("JIT");
                }
                
                // 修复hotTag获取方式，直接使用属性值而不是try-catch
                if (item.getHotTag() != null && item.getHotTag() == 1) {
                    tags.add("热销款");
                }
                
                if (item.getSupplyStatus() != null) {
                    switch (item.getSupplyStatus()) {
                        case 1:
                            tags.add("已缺货");
                            break;
                        case 2:
                            tags.add("停产");
                            break;
                    }
                }
                
                if (item.getCloseJitStatus() != null) {
                    switch (item.getCloseJitStatus()) {
                        case 1:
                            tags.add("JIT转备货-待调价");
                            break;
                        case 2:
                            tags.add("JIT转备货-备货中");
                            break;
                        case 4:
                            tags.add("JIT已关闭");
                            break;
                        case 5:
                            tags.add("JIT调价失败");
                            break;
                        case 6:
                            tags.add("JIT备货失败");
                            break;
                        case 7:
                            tags.add("JIT降价后涨价");
                            break;
                    }
                }
                
                if (!tags.isEmpty()) {
                    productInfo.append("标签: ").append(String.join(", ", tags));
                }
                
                productCell.setCellValue(productInfo.toString());
                productCell.setCellStyle(wrapStyle);
                
                // 如果有SKU信息，生成子行，否则直接在SKC行展示汇总信息
                List<SkuInfo> skuList = item.getSkuList();
                if (skuList != null && !skuList.isEmpty()) {
                    // 先合并SKC行的前三列（店铺备注列、店铺名列和图片列）- 注意行合并范围
                    // 如果有多个SKU，合并范围为当前行到当前行+SKU列表大小-1
                    if (skuList.size() > 1) {
                        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + skuList.size() - 1, 0, 0)); // 合并店铺备注列
                        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + skuList.size() - 1, 1, 1)); // 合并店铺名列
                        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + skuList.size() - 1, 2, 2)); // 合并图片列
                        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + skuList.size() - 1, 3, 3)); // 合并商品信息列
                    }
                    
                    // 初始化第一行的剩余列为空
                    for (int i = 4; i < HEADERS.length; i++) {
                        Cell emptyCell = row.createCell(i);
                        emptyCell.setCellStyle(contentStyle);
                    }
                    
                    // 创建SKU子行
                    for (int i = 0; i < skuList.size(); i++) {
                        SkuInfo sku = skuList.get(i);
                        Row skuRow;
                        
                        if (i == 0) {
                            // 第一个SKU使用已创建的行
                            skuRow = row;
                        } else {
                            // 创建新行
                            skuRow = sheet.createRow(rowIndex + i);
                            // 设置行高为40
                            skuRow.setHeightInPoints(40);
                            
                            // 为新行创建前四列的空单元格（这些会被合并覆盖）
                            for (int j = 0; j < 4; j++) {
                                Cell cell = skuRow.createCell(j);
                                cell.setCellStyle(contentStyle);
                            }
                        }
                        
                        // 开始填充SKU信息
                        int skuColIndex = 4; // 从第5列开始，前四列已经合并
                        
                        // SKU信息
                        Cell skuInfoCell = skuRow.createCell(skuColIndex++);
                        StringBuilder skuInfoStr = new StringBuilder();
                        if (sku.getClassName() != null) {
                            skuInfoStr.append(sku.getClassName()).append("\n");
                        }
                        skuInfoStr.append("SKU ID: ").append(sku.getProductSkuId()).append("\n");
                        if (sku.getSkuExtCode() != null) {
                            skuInfoStr.append("SKU货号: ").append(sku.getSkuExtCode());
                        }
                        skuInfoCell.setCellValue(skuInfoStr.toString());
                        skuInfoCell.setCellStyle(wrapStyle);
                        
                        // 申报价格(CNY)
                        Cell priceCell = skuRow.createCell(skuColIndex++);
                        priceCell.setCellValue("-"); // 平台未返回
                        priceCell.setCellStyle(contentStyle);
                        
                        // 开款价格状态
                        Cell priceStatusCell = skuRow.createCell(skuColIndex++);
                        priceStatusCell.setCellValue("-"); // 平台未返回
                        priceStatusCell.setCellStyle(contentStyle);
                        
                        // 缺货数量
                        Cell lackQuantityCell = skuRow.createCell(skuColIndex++);
                        if (sku.getLackQuantity() != null) {
                            lackQuantityCell.setCellValue(sku.getLackQuantity());
                        } else {
                            lackQuantityCell.setCellValue(0);
                        }
                        lackQuantityCell.setCellStyle(numberStyle);
                        
                        // 销售数据-今日
                        Cell todaySaleCell = skuRow.createCell(skuColIndex++);
                        if (sku.getTodaySaleVolume() != null) {
                            todaySaleCell.setCellValue(sku.getTodaySaleVolume());
                        } else {
                            todaySaleCell.setCellValue(0);
                        }
                        todaySaleCell.setCellStyle(numberStyle);
                        
                        // 销售数据-近7天
                        Cell sevenDaySaleCell = skuRow.createCell(skuColIndex++);
                        if (sku.getLastSevenDaysSaleVolume() != null) {
                            sevenDaySaleCell.setCellValue(sku.getLastSevenDaysSaleVolume());
                        } else {
                            sevenDaySaleCell.setCellValue(0);
                        }
                        sevenDaySaleCell.setCellStyle(numberStyle);
                        
                        // 销售数据-近30天
                        Cell thirtyDaySaleCell = skuRow.createCell(skuColIndex++);
                        if (sku.getLastThirtyDaysSaleVolume() != null) {
                            thirtyDaySaleCell.setCellValue(sku.getLastThirtyDaysSaleVolume());
                        } else {
                            thirtyDaySaleCell.setCellValue(0);
                        }
                        thirtyDaySaleCell.setCellStyle(numberStyle);
                        
                        // 查找对应的库存信息
                        InventoryInfo inventory = null;
                        if (item.getInventoryList() != null) {
                            for (InventoryInfo inv : item.getInventoryList()) {
                                if (inv.getProductSkuId().equals(sku.getProductSkuId())) {
                                    inventory = inv;
                                    break;
                                }
                            }
                        }
                        
                        // 仓内可用库存
                        Cell warehouseInventoryCell = skuRow.createCell(skuColIndex++);
                        if (inventory != null && inventory.getWarehouseInventoryNum() != null) {
                            warehouseInventoryCell.setCellValue(inventory.getWarehouseInventoryNum());
                        } else {
                            warehouseInventoryCell.setCellValue(0);
                        }
                        warehouseInventoryCell.setCellStyle(numberStyle);
                        
                        // 已发货库存
                        Cell waitReceiveCell = skuRow.createCell(skuColIndex++);
                        if (inventory != null && inventory.getWaitReceiveNum() != null) {
                            waitReceiveCell.setCellValue(inventory.getWaitReceiveNum());
                        } else {
                            waitReceiveCell.setCellValue(0);
                        }
                        waitReceiveCell.setCellStyle(numberStyle);
                        
                        // 待发货库存
                        Cell waitDeliveryCell = skuRow.createCell(skuColIndex++);
                        if (inventory != null && inventory.getWaitDeliveryInventoryNum() != null) {
                            waitDeliveryCell.setCellValue(inventory.getWaitDeliveryInventoryNum());
                        } else {
                            waitDeliveryCell.setCellValue(0);
                        }
                        waitDeliveryCell.setCellStyle(numberStyle);
                        
                        // 查找对应的仓库信息
                        WarehouseInfo warehouse = null;
                        if (item.getWarehouseList() != null) {
                            for (WarehouseInfo wh : item.getWarehouseList()) {
                                if (wh.getProductSkuId().equals(sku.getProductSkuId())) {
                                    warehouse = wh;
                                    break;
                                }
                            }
                        }
                        
                        // 备货逻辑
                        Cell purchaseConfigCell = skuRow.createCell(skuColIndex++);
                        if (warehouse != null && warehouse.getPurchaseConfig() != null) {
                            purchaseConfigCell.setCellValue(warehouse.getPurchaseConfig());
                        } else {
                            purchaseConfigCell.setCellValue("-");
                        }
                        purchaseConfigCell.setCellStyle(contentStyle);
                        
                        // 建议备货量
                        Cell adviceQuantityCell = skuRow.createCell(skuColIndex++);
                        if (warehouse != null && warehouse.getAdviceQuantity() != null) {
                            adviceQuantityCell.setCellValue(warehouse.getAdviceQuantity());
                        } else {
                            adviceQuantityCell.setCellValue(0);
                        }
                        adviceQuantityCell.setCellStyle(numberStyle);
                        
                        // 库存可售天数
                        Cell availableSaleDaysFromInventoryCell = skuRow.createCell(skuColIndex++);
                        if (warehouse != null && warehouse.getAvailableSaleDaysFromInventory() != null) {
                            availableSaleDaysFromInventoryCell.setCellValue(warehouse.getAvailableSaleDaysFromInventory());
                        } else {
                            availableSaleDaysFromInventoryCell.setCellValue("-");
                        }
                        availableSaleDaysFromInventoryCell.setCellStyle(contentStyle);
                        
                        // 仓内库存可售天数
                        Cell warehouseAvailableSaleDaysCell = skuRow.createCell(skuColIndex++);
                        if (warehouse != null && warehouse.getWarehouseAvailableSaleDays() != null) {
                            warehouseAvailableSaleDaysCell.setCellValue(warehouse.getWarehouseAvailableSaleDays());
                        } else {
                            warehouseAvailableSaleDaysCell.setCellValue("-");
                        }
                        warehouseAvailableSaleDaysCell.setCellStyle(contentStyle);
                        
                        // 可售天数
                        Cell availableSaleDaysCell = skuRow.createCell(skuColIndex++);
                        if (warehouse != null && warehouse.getAvailableSaleDays() != null) {
                            availableSaleDaysCell.setCellValue(warehouse.getAvailableSaleDays());
                        } else {
                            availableSaleDaysCell.setCellValue("-");
                        }
                        availableSaleDaysCell.setCellStyle(contentStyle);
                    }
                    
                    // 更新行索引，跳过已创建的SKU行
                    rowIndex += skuList.size();
                } else {
                    // 没有SKU信息的情况下，直接在SKC行展示汇总信息
                    
                    // SKU信息
                    Cell skuInfoCell = row.createCell(colIndex++);
                    skuInfoCell.setCellValue("-");
                    skuInfoCell.setCellStyle(contentStyle);
                    
                    // 申报价格(CNY)
                    Cell priceCell = row.createCell(colIndex++);
                    priceCell.setCellValue("-");
                    priceCell.setCellStyle(contentStyle);
                    
                    // 开款价格状态
                    Cell priceStatusCell = row.createCell(colIndex++);
                    priceStatusCell.setCellValue("-");
                    priceStatusCell.setCellStyle(contentStyle);
                    
                    // 缺货数量
                    Cell lackQuantityCell = row.createCell(colIndex++);
                    lackQuantityCell.setCellValue("-");
                    lackQuantityCell.setCellStyle(contentStyle);
                    
                    // 销售数据-今日
                    Cell todaySaleCell = row.createCell(colIndex++);
                    todaySaleCell.setCellValue("-");
                    todaySaleCell.setCellStyle(contentStyle);
                    
                    // 销售数据-近7天
                    Cell sevenDaySaleCell = row.createCell(colIndex++);
                    sevenDaySaleCell.setCellValue("-");
                    sevenDaySaleCell.setCellStyle(contentStyle);
                    
                    // 销售数据-近30天
                    Cell thirtyDaySaleCell = row.createCell(colIndex++);
                    thirtyDaySaleCell.setCellValue("-");
                    thirtyDaySaleCell.setCellStyle(contentStyle);
                    
                    // 库存相关列和备货计划列
                    for (int i = 0; i < 7; i++) {
                        Cell emptyCell = row.createCell(colIndex++);
                        emptyCell.setCellValue("-");
                        emptyCell.setCellStyle(contentStyle);
                    }
                    
                    // 增加行索引
                    rowIndex++;
                }
            }
        }
    }
} 