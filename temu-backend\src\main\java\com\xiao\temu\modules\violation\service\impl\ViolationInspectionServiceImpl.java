package com.xiao.temu.modules.violation.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.violation.dto.ViolationInspectionDTO;
import com.xiao.temu.modules.violation.dto.ViolationInspectionQueryDTO;
import com.xiao.temu.modules.violation.vo.ViolationInspectionVO;
import com.xiao.temu.modules.violation.mapper.local.LocalViolationInspectionMapper;
import com.xiao.temu.modules.violation.service.ViolationInspectionService;
import com.xiao.temu.infrastructure.storage.CosStorageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 违规信息和质检结果综合查询Service实现类
 */
@Slf4j
@Service
public class ViolationInspectionServiceImpl implements ViolationInspectionService {

    @Autowired
    private LocalViolationInspectionMapper violationInspectionMapper;
    
    @Autowired
    private CosStorageService cosStorageService;

    @Override
    public ViolationInspectionVO getViolationInspectionData(ViolationInspectionQueryDTO queryDTO) {
        // 构造分页对象
        Page<ViolationInspectionDTO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        
        // 调用Mapper查询数据
        List<ViolationInspectionDTO> list = violationInspectionMapper.selectViolationInspectionList(page, queryDTO);
        
        // 去除可能存在的重复数据
        list = removeDuplicates(list);
        
        // 处理图片附件格式
        processAttachments(list);
        
        // 构造返回结果
        ViolationInspectionVO vo = new ViolationInspectionVO();
        vo.setTotal(page.getTotal());
        vo.setPages(page.getPages());
        vo.setPageNum(queryDTO.getPageNum());
        vo.setPageSize(queryDTO.getPageSize());
        vo.setRows(list);
        
        return vo;
    }


    /**
     * 处理图片附件数据，将JSON字符串转为列表，并处理COS图片URL
     */
    private void processAttachments(List<ViolationInspectionDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        
        for (ViolationInspectionDTO dto : list) {
            if (StringUtils.isNotBlank(dto.getAttachments())) {
                try {
                    // 解析原始附件JSON
                    List<String> originalAttachments = JSON.parseArray(dto.getAttachments(), String.class);
                    List<String> processedAttachments = new ArrayList<>();
                    
                    // 处理每个附件
                    for (String attachment : originalAttachments) {
                        try {
                            // 尝试解析为JSON对象
                            JSONObject attachmentJson = com.alibaba.fastjson2.JSON.parseObject(attachment);
                            if (attachmentJson != null && attachmentJson.containsKey("imageKey")) {
                                // 获取图片在COS中的Key
                                String imageKey = attachmentJson.getString("imageKey");
                                // 使用CosStorageService生成URL，设置24小时过期
                                String imageUrl = cosStorageService.generateImageUrl(imageKey, 24);
                                if (imageUrl != null) {
                                    processedAttachments.add(imageUrl);
                                } else {
                                    log.warn("生成图片URL失败: {}", imageKey);
                                }
                            } else {
                                // 如果不是标准格式，尝试从其他格式中提取URL
                                String extractedUrl = cosStorageService.extractActualUrl(attachment);
                                if (extractedUrl != null) {
                                    processedAttachments.add(extractedUrl);
                                } else {
                                    // 无法提取则保留原始值
                                    processedAttachments.add(attachment);
                                }
                            }
                        } catch (Exception e) {
                            log.warn("处理单个附件失败: {}, 错误: {}", attachment, e.getMessage());
                            // 解析失败时保留原始值
                            processedAttachments.add(attachment);
                        }
                    }
                    
                    // 设置处理后的图片URL列表
                    dto.setAttachmentList(processedAttachments);
                    
                } catch (Exception e) {
                    log.error("解析疵点图片数据失败: {}", e.getMessage());
                    dto.setAttachmentList(new ArrayList<>());
                }
            } else {
                dto.setAttachmentList(new ArrayList<>());
            }
        }
    }
    
    /**
     * 去除列表中的重复数据，使用违规ID和商品SKU ID作为唯一键
     */
    private List<ViolationInspectionDTO> removeDuplicates(List<ViolationInspectionDTO> list) {
        if (list == null || list.isEmpty()) {
            return list;
        }
        
        Map<String, ViolationInspectionDTO> uniqueMap = new LinkedHashMap<>();
        
        for (ViolationInspectionDTO dto : list) {
            // 使用违规ID和商品SKU ID组合作为唯一键
            String key = dto.getViolationId() + "_" + dto.getProductSkuId();
            uniqueMap.putIfAbsent(key, dto);
        }
        
        return new ArrayList<>(uniqueMap.values());
    }
}