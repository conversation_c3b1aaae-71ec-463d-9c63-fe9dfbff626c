package com.xiao.temu.modules.purchaseorderv.controller;

import com.xiao.temu.modules.purchaseorderv.dto.WarehouseInfoBatchDTO;
import com.xiao.temu.modules.purchaseorderv.entity.WarehouseInfo;
import com.xiao.temu.modules.purchaseorderv.service.WarehouseInfoService;
import com.xiao.temu.modules.sync.vo.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 仓库信息控制器
 */
@Slf4j
@RestController
@RequestMapping("/warehouse")
public class WarehouseInfoController {
    
    @Autowired
    private WarehouseInfoService warehouseInfoService;
    
    @PostMapping("/batch-save")
    public ApiResponse batchSaveWarehouseInfo(@RequestBody WarehouseInfoBatchDTO warehouseInfoBatchDTO) {
        log.info("接收到批量保存仓库信息请求");
        boolean result = warehouseInfoService.batchSaveWarehouseInfo(warehouseInfoBatchDTO);
        if (result) {
            log.info("批量保存仓库信息成功");
            return ApiResponse.success("true", "批量保存仓库信息成功");
        } else {
            log.error("批量保存仓库信息失败");
            return ApiResponse.error("批量保存仓库信息失败");
        }
    }
    
    /**
     * 查询所有仓库信息
     *
     * @return 仓库信息列表
     */
    @GetMapping("/list")
    public ApiResponse listAllWarehouses() {
        log.info("接收到查询所有仓库信息请求");
        try {
            List<WarehouseInfo> warehouseList = warehouseInfoService.list();
            log.info("查询所有仓库信息成功，共{}条记录", warehouseList.size());
            return ApiResponse.success("查询仓库信息成功",warehouseList);
        } catch (Exception e) {
            log.error("查询所有仓库信息异常", e);
            return ApiResponse.error("查询仓库信息失败：" + e.getMessage());
        }
    }
} 