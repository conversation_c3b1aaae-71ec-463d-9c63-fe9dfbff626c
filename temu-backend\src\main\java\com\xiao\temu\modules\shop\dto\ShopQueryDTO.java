package com.xiao.temu.modules.shop.dto;

import com.xiao.temu.common.response.PageRequest;
import lombok.Data;

/**
 * 店铺查询条件
 */
@Data
public class ShopQueryDTO extends PageRequest {
    
    /**
     * 当前页码
     */
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    private Integer pageSize = 10;

    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺名称
     */
    private String shopName;
    
    /**
     * 店铺Temu ID
     */
    private String shopTemuId;
    
    /**
     * 运营组ID
     */
    private Long groupId;
    
    /**
     * 状态（0正常 1禁用）
     */
    private String status;
    
    /**
     * 数据权限SQL
     */
    private String data_scope;
} 