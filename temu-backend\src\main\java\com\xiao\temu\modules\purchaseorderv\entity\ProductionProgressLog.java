package com.xiao.temu.modules.purchaseorderv.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产进度日志实体类
 */
@Data
@TableName("production_progress_log")
public class ProductionProgressLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 备货单号
     */
    private String subPurchaseOrderSn;

    /**
     * 进度类型(cutting:烧花/剪图,workshop:车间/拣货,trimming:剪线/压图,inspection:查货,packaging:包装,shipping:发货)
     */
    private String progressType;

    /**
     * 操作类型(1:完成 2:撤销)
     */
    private String operationType;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建时间
     */
    private Date createTime;
} 