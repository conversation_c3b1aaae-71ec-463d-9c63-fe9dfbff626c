<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.system.mapper.SysMenuMapper">

    <!-- 根据用户ID查询权限 -->
    <select id="selectPermissionsByUserId" parameterType="Long" resultType="String">
        select distinct m.perms
        from sys_menu m
        left join sys_role_menu rm on m.menu_id = rm.menu_id
        left join sys_user_role ur on rm.role_id = ur.role_id
        left join sys_role r on ur.role_id = r.role_id
        where ur.user_id = #{userId}
        and m.perms is not null
        and m.perms != ''
    </select>
    
    <!-- 获取所有权限标识 -->
    <select id="selectAllPermissions" resultType="String">
        select distinct perms
        from sys_menu
        where perms is not null
        and perms != ''
    </select>
    
    <!-- 根据角色ID查询菜单树信息 -->
    <select id="selectMenusByRoleId" parameterType="Long" resultType="com.xiao.temu.modules.system.entity.SysMenu">
        select m.*
        from sys_menu m
        left join sys_role_menu rm on m.menu_id = rm.menu_id
        where rm.role_id = #{roleId}
        order by m.parent_id, m.order_num
    </select>
    
    <!-- 根据用户ID查询菜单 -->
    <select id="selectMenusByUserId" parameterType="Long" resultType="com.xiao.temu.modules.system.entity.SysMenu">
        select distinct m.*
        from sys_menu m
        left join sys_role_menu rm on m.menu_id = rm.menu_id
        left join sys_user_role ur on rm.role_id = ur.role_id
        left join sys_role r on ur.role_id = r.role_id
        where ur.user_id = #{userId}
        order by m.parent_id, m.order_num
    </select>
    
    <!-- 查询所有菜单 -->
    <select id="selectMenuAll" resultType="com.xiao.temu.modules.system.entity.SysMenu">
        select *
        from sys_menu
        order by parent_id, order_num
    </select>
    
    <!-- 根据父ID查询子菜单数量 -->
    <select id="selectCountMenuByParentId" parameterType="Long" resultType="int">
        select count(1)
        from sys_menu
        where parent_id = #{parentId}
    </select>
    
    <!-- 根据角色ID查询权限 -->
    <select id="selectPermissionsByRoleId" parameterType="Long" resultType="String">
        select distinct m.perms
        from sys_menu m
        left join sys_role_menu rm on m.menu_id = rm.menu_id
        where rm.role_id = #{roleId}
        and m.perms is not null
        and m.perms != ''
    </select>
    
</mapper> 