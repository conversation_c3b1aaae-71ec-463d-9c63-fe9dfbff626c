/**
 * 登录页面
 */
const userApi = require('../../api/userApi');
const loginUtils = require('../../utils/loginUtils');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    username: '',
    password: '',
    rememberMe: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function () {
    // 检查是否有保存的登录信息
    try {
      // 获取保存的登录信息
      const savedUserInfo = loginUtils.getSavedLoginInfo();
      
      if (savedUserInfo) {
        // 将保存的用户信息填充到表单
        this.setData({
          username: savedUserInfo.username || '',
          password: savedUserInfo.password || '',
          rememberMe: true
        });
      }
    } catch (error) {
      console.error('读取缓存的登录信息失败:', error);
    }
  },

  /**
   * 输入用户名
   */
  inputUsername: function (e) {
    this.setData({
      username: e.detail.value
    });
  },

  /**
   * 输入密码
   */
  inputPassword: function (e) {
    this.setData({
      password: e.detail.value
    });
  },

  /**
   * 记住密码选项切换
   */
  toggleRememberMe: function (e) {
    const rememberMeValue = !!e.detail.value.length;
    
    this.setData({
      rememberMe: rememberMeValue
    });
    
    // 如果取消勾选"记住密码"，清除已保存的登录信息
    if (!rememberMeValue) {
      loginUtils.clearSavedLoginInfo();
    }
  },

  /**
   * 登录
   */
  login: function () {
    const { username, password, rememberMe } = this.data;
    
    if (!username || !password) {
      wx.showToast({
        title: '请输入用户名和密码',
        icon: 'none'
      });
      return;
    }
    
    // 显示加载状态
    wx.showLoading({
      title: '登录中...',
    });

    userApi.login(username, password, rememberMe)
      .then(res => {
        console.log('登录成功，返回数据:', res);
        
        // 正确处理返回的token格式
        // 完整token由tokenType和token组成，如"Bearer eyJhbGciOiJIUzUxMiJ9..."
        wx.setStorageSync('token', `${res.tokenType} ${res.token}`);
        
        // 保存所有用户信息
        wx.setStorageSync('userId', res.userId);
        wx.setStorageSync('username', res.username);
        wx.setStorageSync('nickName', res.nickName);
        wx.setStorageSync('roles', res.roles);
        wx.setStorageSync('permissions', res.permissions);
        wx.setStorageSync('rolePermissions', res.rolePermissions);
        wx.setStorageSync('roleNames', res.roleNames);
        
        // 处理记住密码功能
        if (rememberMe) {
          // 保存用户登录信息
          loginUtils.saveLoginInfo(username, password);
        } else {
          // 如果用户取消了记住密码，清除之前保存的信息
          loginUtils.clearSavedLoginInfo();
        }
        
        // 保存完整的用户信息对象
        const userInfo = {
          userId: res.userId,
          username: res.username,
          nickName: res.nickName,
          roles: res.roles,
          permissions: res.permissions,
          rolePermissions: res.rolePermissions,
          roleNames: res.roleNames
        };
        wx.setStorageSync('userInfo', userInfo);
        
        // 登录成功提示
        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500,
          success: () => {
            // 跳转到首页
            setTimeout(() => {
              wx.reLaunch({
                url: '/pages/home/<USER>'
              });
            }, 1500);
          }
        });
      })
      .catch(err => {
        console.error('登录失败:', err);
        
        // 根据错误类型显示不同的错误信息
        let errorMessage = '未知错误';
        
        // 详细输出错误信息以便调试
        console.log('错误类型:', typeof err);
        console.log('错误详情:', JSON.stringify(err));
        
        // 尝试解析错误对象
        if (err && typeof err === 'object') {
          // 账号密码错误 (code: 401)
          if (err.code === 401) {
            errorMessage = '账号或密码错误';
          } 
          // 使用返回的错误信息
          else if (err.message) {
            errorMessage = err.message;
          }
        } 
        // 如果错误是字符串
        else if (typeof err === 'string') {
          errorMessage = err;
        }
        
        console.log('显示的错误信息:', errorMessage);
        
        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 2000
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  }
}) 