.container {
  min-height: 100vh;
  background-color: #f7f7f7;
  box-sizing: border-box;
}

.content {
  padding: 30rpx;
}

.order-card {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
  padding: 32rpx 28rpx 24rpx 28rpx;
  margin-bottom: 28rpx;
  border: 1rpx solid #f0f0f0;
  position: relative;
}

.order-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.order-step {
  font-size: 24rpx;
  color: var(--text-light-color);
}

.empty-tip {
  text-align: center;
  color: var(--text-light-color);
  font-size: 28rpx;
  padding: 60rpx 0;
}