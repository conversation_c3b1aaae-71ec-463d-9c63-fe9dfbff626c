import router, { constantRoutes, pageNotFoundRoute } from '@/router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'
import { getToken } from '@/utils/auth'
import { getUserInfo } from '@/api/user'
import { getRouters } from '@/api/menu'
import { getViewComponent } from './importRouter'

/**
 * 检查用户是否有指定权限
 * @param permission 权限字符串或数组
 * @returns 是否具有权限
 */
export function hasPermission(permission: string | string[]): boolean {
  const userStore = useUserStore()
  const permissions = userStore.permissions
  
  if (!permission || !permissions) {
    return false
  }
  
  // 超级管理员拥有所有权限
  if (permissions.includes('*:*:*')) {
    return true
  }
  
  if (typeof permission === 'string') {
    return permissions.includes(permission)
  } else {
    return permission.some(p => permissions.includes(p))
  }
}

/**
 * 检查用户是否有指定角色
 * @param role 角色字符串或数组
 * @returns 是否具有角色
 */
export function hasRole(role: string | string[]): boolean {
  const userStore = useUserStore()
  const roles = userStore.roles
  
  if (!role || !roles) {
    return false
  }
  
  // 超级管理员拥有所有角色
  if (roles.includes('admin')) {
    return true
  }
  
  if (typeof role === 'string') {
    return roles.includes(role)
  } else {
    return role.some(r => roles.includes(r))
  }
}

/**
 * 创建权限指令的方法
 */
export const checkPermission = {
  // 检查是否有权限的方法，用于自定义指令
  check: (el: HTMLElement, binding: any) => {
    const { value } = binding
    
    if (!value) {
      throw new Error('need permission value')
    }
    
    // 获取用户权限
    const hasAuth = hasPermission(value)
    
    if (!hasAuth) {
      el.parentNode?.removeChild(el)
    }
  }
}

// 动态加载对应的组件
const loadComponent = (component: string) => {
  return getViewComponent(component)
}

// 处理后端返回的菜单数据生成路由
export const generateRoutes = async () => {
  try {
    // 获取用户菜单树 
    const res = await getRouters()
    
    const menuTree = res.data || []
    
    if (!menuTree || menuTree.length === 0) {
      return []
    }
    
    // 生成路由配置
    const routes = filterAsyncRoutes(menuTree)

    // 先清除所有已添加的动态路由
    try {
      const existingRoutes = router.getRoutes()
      existingRoutes.forEach(route => {
        if (route.name && route.name !== 'login' && route.name !== 'Dashboard' && route.name !== 'DashboardIndex' && !constantRoutes.some(r => r.name === route.name)) {
          router.removeRoute(route.name)
        }
      })
    } catch (e) {
      // 清除现有路由失败
    }
    
    // 动态添加路由
    routes.forEach(route => {
      router.addRoute(route)
    })
    
    // 最后添加404页面的路由
    router.addRoute(pageNotFoundRoute)
    
    return routes
  } catch (error) {
    return []
  }
}

// 根据权限过滤和构造路由
export const filterAsyncRoutes = (menus: any[], parentPath = ''): RouteRecordRaw[] => {
  const res: RouteRecordRaw[] = []
  
  menus.forEach(menu => {
    // 跳过按钮类型的菜单
    if (menu.menuType === 'F') return
    
    // 构造路由path
    const routePath = menu.path.startsWith('/') 
      ? menu.path 
      : (parentPath ? `${parentPath}/${menu.path}` : `/${menu.path}`)
    
    const route: RouteRecordRaw = {
      path: routePath,
      name: menu.menuName || menu.path,
      component: loadComponent(menu.component || 'Layout'),
      meta: {
        title: menu.menuName,
        icon: menu.icon,
        hidden: menu.visible === '1',
        permissions: menu.perms ? [menu.perms] : []
      },
      children: []
    }
    
    // 处理子菜单
    if (menu.children && menu.children.length > 0) {
      // 目录类型
      if (menu.menuType === 'M') {
        route.children = filterAsyncRoutes(menu.children, routePath)
        
        // 如果目录下只有一个子路由，可以考虑自动重定向
        if (route.children.length === 1) {
          route.redirect = route.children[0].path
        }
      }
      // 菜单类型
      else if (menu.menuType === 'C') {
        // 对于有component的菜单，直接作为路由
        route.children = []
      }
    }
    
    res.push(route)
  })
  
  return res
}

// 初始化路由 - 在用户登录后调用
export const initRoutes = async () => {
  if (getToken()) {
    const userStore = useUserStore()
    const permissionStore = usePermissionStore()
    
    // 获取用户信息
    await userStore.getInfo()
    
    // 生成路由
    const routes = await generateRoutes()
    permissionStore.setRoutes(routes)
  }
}

// 路由权限控制 - 设置路由守卫
export const setupRouterGuard = () => {
  router.beforeEach(async (to, from, next) => {
    // 获取token
    const token = getToken()
    
    // 已登录
    if (token) {
      if (to.path === '/login') {
        // 如果已登录，跳转到首页
        next({ path: '/' })
      } else {
        const userStore = useUserStore()
        // 判断是否已加载用户信息
        const hasRoles = userStore.roles && userStore.roles.length > 0
        
        if (hasRoles) {
          next()
        } else {
          try {
            // 获取用户信息
            await userStore.getInfo()
            
            // 动态生成路由
            await generateRoutes()
            
            // 确保路由已添加
            next({ ...to, replace: true })
          } catch (error) {
            // 出错时重置token并跳转登录页
            await userStore.resetToken()
            next(`/login?redirect=${to.path}`)
          }
        }
      }
    } else {
      // 未登录
      if (to.path === '/login') {
        next()
      } else {
        next(`/login?redirect=${to.path}`)
      }
    }
  })
}

export default { setupRouterGuard, initRoutes, generateRoutes } 