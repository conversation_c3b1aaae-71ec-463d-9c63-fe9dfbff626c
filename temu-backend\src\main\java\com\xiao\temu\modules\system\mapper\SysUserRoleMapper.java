package com.xiao.temu.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.system.entity.SysUserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户与角色关联Mapper接口
 */
@Mapper
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {

    /**
     * 批量删除用户和角色关联
     *
     * @param userIds 用户IDs
     * @return 结果
     */
    int deleteUserRoleByUserIds(@Param("userIds") List<Long> userIds);

    /**
     * 通过用户ID删除用户和角色关联
     *
     * @param userId 用户ID
     * @return 结果
     */
    int deleteUserRoleByUserId(@Param("userId") Long userId);

    /**
     * 通过角色ID删除用户和角色关联
     *
     * @param roleId 角色ID
     * @return 结果
     */
    int deleteUserRoleByRoleId(@Param("roleId") Long roleId);

    /**
     * 批量删除用户和角色关联
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteUserRole(Long[] ids);

    /**
     * 批量新增用户角色信息
     *
     * @param userRoleList 用户角色列表
     * @return 结果
     */
    int batchInsertUserRole(List<SysUserRole> userRoleList);

    /**
     * 查询用户拥有的角色ID列表
     *
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> selectRoleIdsByUserId(@Param("userId") Long userId);

    /**
     * 查询角色拥有的用户ID列表
     *
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    List<Long> selectUserIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据角色ID列表查询用户ID列表
     *
     * @param roleIds 角色ID列表
     * @return 用户ID列表
     */
    List<Long> selectUserIdsByRoleIds(@Param("roleIds") List<Long> roleIds);

    /**
     * 检查用户是否拥有指定角色
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 结果数量，大于0表示有该角色
     */
    int checkUserHasRole(@Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 删除用户指定角色
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 结果
     */
    int deleteUserRoleByUserIdAndRoleId(@Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 新增用户角色信息
     *
     * @param userRole 用户角色信息
     * @return 结果
     */
    int insertUserRole(SysUserRole userRole);
} 