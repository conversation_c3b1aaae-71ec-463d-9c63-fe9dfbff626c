import request from '@/utils/request'

// 备货单同步任务参数
export interface PurchaseOrderSyncParams {
  shopIds?: number[]
  pageNum?: number
  pageSize?: number
  sortField?: string
  sortOrder?: string
}

// 备货单同步任务
export interface PurchaseOrderSyncTask {
  shopId: number
  shopName?: string
  shopRemark?: string
  lastSyncTime?: string
  lastUpdateTime?: string
  syncStatus: number // 0-未同步 1-同步中 2-同步成功 3-同步失败
  totalRecords: number
  normalPurchaseOrderCount: number
  jitPurchaseOrderCount: number
  errorMessage?: string
}

/**
 * 获取备货单同步任务列表
 * @param query 查询参数
 * @returns 请求Promise
 */
export function getSyncTasks(query?: PurchaseOrderSyncParams) {
  return request({
    url: '/temu/purchaseOrder/sync/list',
    method: 'post',
    data: query
  })
}

/**
 * 获取单个同步任务
 * @param shopId 店铺ID
 * @returns 请求Promise
 */
export function getSyncTask(shopId: number) {
  return request({
    url: `/temu/purchaseOrder/sync/status/${shopId}`,
    method: 'get'
  })
}

/**
 * 手动触发同步
 * @param shopId 店铺ID
 * @returns 请求Promise
 */
export function triggerSync(shopId: number) {
  return request({
    url: `/temu/purchaseOrder/sync/execute/${shopId}`,
    method: 'post'
  })
}

/**
 * 初始化同步任务
 * @param shopId 店铺ID
 * @returns 请求Promise
 */
export function initSyncTask(shopId: number) {
  return request({
    url: `/temu/purchaseOrder/sync/init/${shopId}`,
    method: 'post'
  })
}

/**
 * 批量初始化同步任务
 * @param params 包含店铺ID数组的参数
 * @returns 请求Promise
 */
export function batchInitSyncTasks(params: PurchaseOrderSyncParams) {
  // 仅保留shopIds参数
  const { shopIds } = params
  return request({
    url: '/temu/purchaseOrder/sync/batch/init',
    method: 'post',
    data: { shopIds }
  })
}

/**
 * 批量触发同步
 * @param params 包含店铺ID数组的参数
 * @returns 请求Promise
 */
export function batchTriggerSync(params: PurchaseOrderSyncParams) {
  // 仅保留shopIds参数
  const { shopIds } = params
  return request({
    url: '/temu/purchaseOrder/sync/batch/execute',
    method: 'post',
    data: { shopIds }
  })
}

/**
 * 清空同步数据
 * @param shopId 店铺ID
 * @returns 请求Promise
 */
export function clearSyncData(shopId: number) {
  return request({
    url: `/temu/purchaseOrder/sync/clear/${shopId}`,
    method: 'post'
  })
}

/**
 * 批量清空同步数据
 * @param params 包含店铺ID数组的参数
 * @returns 请求Promise
 */
export function batchClearSyncData(params: PurchaseOrderSyncParams) {
  // 仅保留shopIds参数
  const { shopIds } = params
  return request({
    url: '/temu/purchaseOrder/sync/batch/clear',
    method: 'post',
    data: { shopIds }
  })
}

/**
 * 获取备货单数量
 * @param shopId 店铺ID
 * @param type 备货单类型（0:普通备货 1:JIT备货 null:全部）
 * @returns 请求Promise
 */
export function getPurchaseOrderCount(shopId: number, type?: number) {
  return request({
    url: `/temu/purchaseOrder/sync/count/${shopId}`,
    method: 'get',
    params: { type }
  })
} 