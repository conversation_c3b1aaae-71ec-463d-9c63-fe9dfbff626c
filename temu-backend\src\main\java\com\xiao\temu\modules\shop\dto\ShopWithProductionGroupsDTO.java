package com.xiao.temu.modules.shop.dto;

import com.xiao.temu.modules.production.entity.ProductionGroup;
import com.xiao.temu.modules.shop.entity.Shop;

import java.util.List;

/**
 * 店铺及其所属生产组DTO
 */
public class ShopWithProductionGroupsDTO extends Shop {
    
    /**
     * 店铺所属的生产组列表
     */
    private List<ProductionGroup> groups;
    
    /**
     * 生产组名称列表（格式化后的字符串）
     */
    private String groupNames;

    public List<ProductionGroup> getGroups() {
        return groups;
    }

    public void setGroups(List<ProductionGroup> groups) {
        this.groups = groups;
    }

    public String getGroupNames() {
        return groupNames;
    }

    public void setGroupNames(String groupNames) {
        this.groupNames = groupNames;
    }
} 