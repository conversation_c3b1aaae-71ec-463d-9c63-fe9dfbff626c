.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #ffffff;
}

.header {
  text-align: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #e5e5e5;
}

.header-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
}

.content {
  flex: 1;
  padding: 0 60rpx;
}

.logo-area {
  display: flex;
  justify-content: center;
  padding: 80rpx 0;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
}

.form-area {
  padding: 20rpx 0;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 20rpx;
}

.form-input {
  width: 100%;
  height: 90rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  box-sizing: border-box;
}

.checkbox-item {
  display: flex;
  align-items: center;
}

.checkbox-label {
  font-size: 28rpx;
  color: #666666;
  margin-left: 10rpx;
}

.login-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #07c160;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 8rpx;
  margin-top: 60rpx;
}

.footer {
  padding: 40rpx 0;
  text-align: center;
  font-size: 24rpx;
  color: #999999;
} 