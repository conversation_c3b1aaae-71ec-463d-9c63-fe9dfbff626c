package com.xiao.temu.modules.production.dto;

import lombok.Data;

import java.util.List;

/**
 * 创建用户DTO
 * 用于生产组长创建用户并添加到组时的参数传递
 */
@Data
public class CreateUserDTO {
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户昵称
     */
    private String nickName;
    
    /**
     * 手机号码
     */
    private String phone;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 状态（0正常 1停用）
     */
    private String status;
    
    /**
     * 角色ID列表
     */
    private List<Long> roleIds;
    
    /**
     * 备注
     */
    private String remark;
} 