package com.xiao.temu.modules.message.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.modules.message.dto.MessageDetailVO;
import com.xiao.temu.modules.message.dto.MessageQueryDTO;
import com.xiao.temu.modules.message.dto.SendMessageDTO;
import com.xiao.temu.modules.message.dto.UnreadCountVO;
import com.xiao.temu.modules.message.entity.SysMessage;
import com.xiao.temu.modules.message.entity.SysMessageTemplate;
import com.xiao.temu.security.utils.SecurityUtils;
import com.xiao.temu.modules.message.service.MessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 消息管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/messages")
@Tag(name = "消息管理", description = "消息管理相关接口")
public class MessageController {

    @Autowired
    private MessageService messageService;

    /**
     * 发送消息
     */
    @PostMapping
    @Operation(summary = "发送消息", description = "发送系统消息、任务提醒或店铺消息")
    @PreAuthorize("hasAuthority('message:send:add')")
    public ApiResponse<Long> sendMessage(@RequestBody @Valid SendMessageDTO sendMessageDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        if (userId == null) {
            return ApiResponse.error("未获取到当前用户ID");
        }
        Long messageId = messageService.sendMessage(sendMessageDTO, userId);
        return ApiResponse.success(messageId);
    }

    /**
     * 查询消息列表
     */
    @GetMapping
    @Operation(summary = "查询消息列表", description = "分页查询当前用户的消息列表")
    @PreAuthorize("hasAuthority('message:my:query')")
    public ApiResponse<IPage<SysMessage>> getMessageList(MessageQueryDTO query) {
        Long userId = SecurityUtils.getCurrentUserId();
        if (userId == null) {
            return ApiResponse.error("未获取到当前用户ID");
        }
        IPage<SysMessage> page = messageService.getMessageList(query, userId);
        return ApiResponse.success(page);
    }

    /**
     * 获取消息详情
     */
    @GetMapping("/{messageId}")
    @Operation(summary = "获取消息详情", description = "获取指定消息的详细信息")
    @PreAuthorize("hasAuthority('message:my:query')")
    public ApiResponse<MessageDetailVO> getMessageDetail(
            @Parameter(description = "消息ID") @PathVariable Long messageId) {
        Long userId = SecurityUtils.getCurrentUserId();
        if (userId == null) {
            return ApiResponse.error("未获取到当前用户ID");
        }
        MessageDetailVO detail = messageService.getMessageDetail(messageId, userId);
        return ApiResponse.success(detail);
    }

    /**
     * 标记消息已读
     */
    @PutMapping("/{messageId}/read")
    @Operation(summary = "标记消息已读", description = "将指定消息标记为已读状态")
    @PreAuthorize("hasAuthority('message:my:read')")
    public ApiResponse<Boolean> markAsRead(
            @Parameter(description = "消息ID") @PathVariable Long messageId) {
        Long userId = SecurityUtils.getCurrentUserId();
        if (userId == null) {
            return ApiResponse.error("未获取到当前用户ID");
        }
        boolean result = messageService.markAsRead(messageId, userId);
        return ApiResponse.success(result);
    }

    /**
     * 批量标记已读
     */
    @PutMapping("/batch-read")
    @Operation(summary = "批量标记已读", description = "批量将指定消息标记为已读状态")
    @PreAuthorize("hasAuthority('message:my:read')")
    public ApiResponse<Boolean> batchMarkAsRead(
            @Parameter(description = "消息ID列表") @RequestBody List<Long> messageIds) {
        Long userId = SecurityUtils.getCurrentUserId();
        if (userId == null) {
            return ApiResponse.error("未获取到当前用户ID");
        }
        boolean result = messageService.batchMarkAsRead(messageIds, userId);
        return ApiResponse.success(result);
    }

    /**
     * 删除消息
     */
    @DeleteMapping("/{messageId}")
    @Operation(summary = "删除消息", description = "删除指定消息")
    @PreAuthorize("hasAuthority('message:my:remove')")
    public ApiResponse<Boolean> deleteMessage(
            @Parameter(description = "消息ID") @PathVariable Long messageId) {
        Long userId = SecurityUtils.getCurrentUserId();
        if (userId == null) {
            return ApiResponse.error("未获取到当前用户ID");
        }
        boolean result = messageService.deleteMessage(messageId, userId);
        return ApiResponse.success(result);
    }

    /**
     * 批量删除消息
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除消息", description = "批量删除指定消息")
    @PreAuthorize("hasAuthority('message:my:remove')")
    public ApiResponse<Boolean> batchDeleteMessage(
            @Parameter(description = "消息ID列表") @RequestBody List<Long> messageIds) {
        Long userId = SecurityUtils.getCurrentUserId();
        if (userId == null) {
            return ApiResponse.error("未获取到当前用户ID");
        }
        boolean result = messageService.batchDeleteMessage(messageIds, userId);
        return ApiResponse.success(result);
    }

    /**
     * 获取未读消息数量
     */
    @GetMapping("/unread-count")
    @Operation(summary = "获取未读消息数量", description = "获取当前用户的未读消息数量统计")
    @PreAuthorize("hasAuthority('message:my:query')")
    public ApiResponse<UnreadCountVO> getUnreadCount() {
        Long userId = SecurityUtils.getCurrentUserId();
        if (userId == null) {
            return ApiResponse.error("未获取到当前用户ID");
        }
        UnreadCountVO countVO = messageService.getUnreadCount(userId);
        return ApiResponse.success(countVO);
    }

    /**
     * 获取消息模板
     */
    @GetMapping("/templates/{templateCode}")
    @Operation(summary = "获取消息模板", description = "根据模板编码获取消息模板详情")
    @PreAuthorize("hasAuthority('message:template:query')")
    public ApiResponse<SysMessageTemplate> getMessageTemplate(
            @Parameter(description = "模板编码") @PathVariable String templateCode) {
        SysMessageTemplate template = messageService.getMessageTemplate(templateCode);
        return ApiResponse.success(template);
    }
} 