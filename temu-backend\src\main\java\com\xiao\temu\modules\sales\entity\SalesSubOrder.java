package com.xiao.temu.modules.sales.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 销售子订单实体类
 * 对应数据库表：sales_sub_order
 * 基于Temu API返回的销售数据
 */
@Data
@TableName("sales_sub_order")
public class SalesSubOrder {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品SKC ID
     */
    private Long productSkcId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * SKC货号
     */
    private String skcExtCode;

    /**
     * 商品类目
     */
    private String category;

    /**
     * 商品SKC图片
     */
    private String productSkcPicture;

    /**
     * 是否定制商品：0-否，1-是
     */
    private Boolean isCustomGoods;

    /**
     * 库存区域：1-国内备货，2-海外备货，3-保税仓备货
     */
    private Integer inventoryRegion;

    /**
     * 供应状态
     */
    private Integer supplyStatus;

    /**
     * 供应状态备注
     */
    private String supplyStatusRemark;

    /**
     * 是否在备货黑名单内
     */
    private Boolean inBlackList;

    /**
     * 图片审核状态：1-未完成，2-已完成
     */
    private Integer pictureAuditStatus;

    /**
     * 加入站点时长
     */
    private Integer onSalesDurationOffline;

    /**
     * JIT关闭状态：0-未申请 1-调价中 2-待备货 3-备货完成，待关闭JIT 4-JIT已关闭 
     * 5-调价失败，流程结束 6-备货失败，流程结束 7-降价后又涨价，流程结束
     */
    private Integer closeJitStatus;

    /**
     * 是否自动关闭JIT
     */
    private Boolean autoCloseJit;

    /**
     * 是否热销款
     */
    private Boolean hotTag;

    /**
     * 是否存在爆旺款sku
     */
    private Boolean hasHotSku;

    /**
     * 是否备货充足无需下单
     */
    private Boolean isEnoughStock;

    /**
     * 采购库存类型：0-普通，1-JIT备货
     */
    private Integer purchaseStockType;

    /**
     * 结算类型：0-非vmi，1-vmi
     */
    private Integer settlementType;

    /**
     * 同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 