import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import { getLocalQualityInspectionList } from '@/api/local/qualityInspection'
import { fetchUserAccessibleShops } from '@/utils/shop-helper'
import type { LocalQualityInspectionRequestParams } from '@/types/local/qcInspection'
import type { Shop } from '@/types/refund'

interface LocalQualityInspectionState {
  loading: boolean
  shopIds: number[]
  timeRange: [string, string] | null
}

export const useLocalQualityInspectionStore = defineStore('localQualityInspection', {
  state: (): LocalQualityInspectionState => ({
    loading: false,
    shopIds: [],
    timeRange: null
  }),

  actions: {
    // 设置加载状态
    setLoading(loading: boolean) {
      this.loading = loading
    },

    // 设置店铺ID（兼容原有方法）
    setShopId(shopId: number | null) {
      if (shopId) {
        this.shopIds = [shopId]
      } else {
        this.shopIds = []
      }
    },
    
    // 设置多个店铺ID
    setShopIds(shopIds: number[]) {
      this.shopIds = shopIds
    },

    // 设置时间范围
    setTimeRange(timeRange: [string, string] | null) {
      this.timeRange = timeRange
    },

    // 重置查询条件
    resetQuery() {
      this.shopIds = []
      this.timeRange = null
    },

    // 获取用户店铺列表
    async getUserShops(): Promise<Shop[]> {
      try {
        this.setLoading(true)
        const shops = await fetchUserAccessibleShops()
        return shops
      } catch (error) {
        console.error('获取店铺列表出错:', error)
        ElMessage.error('获取店铺列表失败')
        return []
      } finally {
        this.setLoading(false)
      }
    },

    // 获取本地质检结果列表
    async getLocalQualityInspectionList(params: LocalQualityInspectionRequestParams) {
      try {
        this.setLoading(true)
        const response = await getLocalQualityInspectionList(params)
        console.log('API原始响应:', response)
        return response
      } catch (error) {
        console.error('获取本地质检结果列表出错:', error)
        throw error
      } finally {
        this.setLoading(false)
      }
    }
  }
}) 