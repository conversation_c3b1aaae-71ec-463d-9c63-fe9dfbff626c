package com.xiao.temu.modules.quality.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 质检不合格详情实体类
 */
@Data
public class QualityInspectionDefectDetail {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 质检单ID
     */
    private Long qcBillId;
    
    /**
     * 产品SKU ID
     */
    private Long productSkuId;
    
    /**
     * 产品SKC ID
     */
    private Long productSkcId;
    
    /**
     * 疵点描述
     */
    private String flawNameDesc;
    
    /**
     * 问题备注
     */
    private String remark;
    
    /**
     * 疵点证明图片链接，JSON格式
     */
    private String attachments;
    
    /**
     * 同步时间
     */
    private LocalDateTime syncTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 