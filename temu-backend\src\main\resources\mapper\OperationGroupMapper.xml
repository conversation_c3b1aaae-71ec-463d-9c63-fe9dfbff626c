<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.operation.mapper.OperationGroupMapper">

    <!-- 结果映射 -->
    <resultMap id="GroupResultMap" type="com.xiao.temu.modules.operation.dto.OperationGroupDTO">
        <id property="groupId" column="group_id"/>
        <result property="groupName" column="group_name"/>
        <result property="leaderId" column="leader_id"/>
        <result property="leaderName" column="leader_name"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="memberCount" column="member_count"/>
    </resultMap>
    
    <!-- 分页查询运营组列表 -->
    <select id="selectGroupList" resultMap="GroupResultMap">
        SELECT g.*, 
               u.nick_name AS leader_name,
               (SELECT COUNT(1) FROM group_member gm WHERE gm.group_id = g.group_id AND gm.status = '0') AS member_count
        FROM operation_group g
        LEFT JOIN sys_user u ON g.leader_id = u.user_id
        <where>
            <if test="query.groupName != null and query.groupName != ''">
                AND g.group_name LIKE CONCAT('%', #{query.groupName}, '%')
            </if>
            <if test="query.leaderId != null">
                AND g.leader_id = #{query.leaderId}
            </if>
            <if test="query.leaderName != null and query.leaderName != ''">
                AND u.nick_name LIKE CONCAT('%', #{query.leaderName}, '%')
            </if>
            <if test="query.status != null and query.status != ''">
                AND g.status = #{query.status}
            </if>
            <if test="query.memberId != null">
                AND EXISTS (
                    SELECT 1 FROM group_member gm 
                    WHERE gm.group_id = g.group_id 
                    AND gm.user_id = #{query.memberId}
                    AND gm.status = '0'
                )
            </if>
        </where>
        ORDER BY g.create_time DESC
    </select>
    
    <!-- 查询运营组详情 -->
    <select id="selectGroupById" resultMap="GroupResultMap">
        SELECT g.*, 
               u.nick_name AS leader_name,
               (SELECT COUNT(1) FROM group_member gm WHERE gm.group_id = g.group_id AND gm.status = '0') AS member_count
        FROM operation_group g
        LEFT JOIN sys_user u ON g.leader_id = u.user_id
        WHERE g.group_id = #{groupId}
    </select>
    
    <!-- 检查运营组名称是否唯一 -->
    <select id="checkGroupNameUnique" resultType="int">
        SELECT COUNT(1)
        FROM operation_group
        WHERE group_name = #{groupName}
        <if test="groupId != null">
            AND group_id != #{groupId}
        </if>
    </select>
    
    <!-- 更新运营组状态 -->
    <update id="updateStatus">
        UPDATE operation_group
        SET status = #{status},
            update_time = NOW()
        WHERE group_id = #{groupId}
    </update>
    
    <!-- 根据负责人ID查询运营组列表 -->
    <select id="selectGroupsByLeaderId" resultType="com.xiao.temu.modules.operation.entity.OperationGroup">
        SELECT *
        FROM operation_group
        WHERE leader_id = #{leaderId}
    </select>
    
    <!-- 查询成员所在的运营组列表 -->
    <select id="selectGroupsByMemberId" resultMap="GroupResultMap">
        SELECT g.*, 
               u.nick_name AS leader_name,
               (SELECT COUNT(1) FROM group_member gm2 WHERE gm2.group_id = g.group_id AND gm2.status = '0') AS member_count
        FROM operation_group g
        INNER JOIN group_member gm ON g.group_id = gm.group_id
        LEFT JOIN sys_user u ON g.leader_id = u.user_id
        WHERE gm.user_id = #{userId}
          AND gm.status = '0'
        ORDER BY g.create_time DESC
    </select>
    
    <!-- 统计用户所属的运营组数量 -->
    <select id="countUserGroups" resultType="int">
        SELECT COUNT(1)
        FROM group_member
        WHERE user_id = #{userId}
          AND status = '0'
    </select>
    
</mapper> 