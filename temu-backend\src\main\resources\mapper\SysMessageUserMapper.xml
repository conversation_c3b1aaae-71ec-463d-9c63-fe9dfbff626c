<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xiao.temu.modules.message.mapper.SysMessageUserMapper">

    <!-- 批量插入消息用户关系 -->
    <insert id="batchInsert">
        INSERT INTO sys_message_user (message_id, user_id, read_status, deleted, create_time)
        VALUES
        <foreach collection="userIds" item="userId" separator=",">
            (#{messageId}, #{userId}, '0', '0', NOW())
        </foreach>
    </insert>

    <!-- 获取用户未读消息数量 -->
    <select id="getUnreadCount" resultType="com.xiao.temu.modules.message.dto.UnreadCountVO">
        SELECT
            COUNT(1) AS totalCount,
            SUM(CASE WHEN m.message_type = '1' THEN 1 ELSE 0 END) AS systemCount,
            SUM(CASE WHEN m.message_type = '2' THEN 1 ELSE 0 END) AS taskCount,
            SUM(CASE WHEN m.message_type = '3' THEN 1 ELSE 0 END) AS shopCount,
            SUM(CASE WHEN m.importance = '3' THEN 1 ELSE 0 END) AS urgentCount
        FROM
            sys_message_user mu
        INNER JOIN sys_message m ON mu.message_id = m.message_id
        WHERE
            mu.user_id = #{userId}
            AND mu.read_status = '0'
            AND mu.deleted = '0'
            AND m.deleted = '0'
    </select>

</mapper> 