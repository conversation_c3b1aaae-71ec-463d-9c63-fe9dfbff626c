// 自定义复选框组组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 选中的值数组
    value: {
      type: Array,
      value: []
    }
  },
  
  /**
   * 组件的初始数据
   */
  data: {
    checkedValues: []
  },
  
  /**
   * 数据监听器
   */
  observers: {
    'value': function(value) {
      // 确保数据是数组
      this.setData({
        checkedValues: Array.isArray(value) ? value : []
      });
    }
  },
  
  /**
   * 组件生命周期
   */
  lifetimes: {
    attached: function() {
      // 确保初始值是数组
      const values = Array.isArray(this.data.value) ? this.data.value : [];
      this.setData({
        checkedValues: values
      });
      console.log('复选框组初始化值:', values);
    }
  },
  
  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 处理子复选框变更
     */
    handleCheckboxChange: function(e) {
      const { value, checked } = e.detail;
      let checkedValues = [...this.data.checkedValues];
      
      if (checked) {
        // 添加选中项
        if (!checkedValues.includes(value)) {
          checkedValues.push(value);
        }
      } else {
        // 移除选中项
        checkedValues = checkedValues.filter(v => v !== value);
      }
      
      this.setData({
        checkedValues
      });
      
      console.log('复选框组值变化:', checkedValues);
      
      // 触发自定义事件
      this.triggerEvent('change', {
        value: checkedValues
      });
    }
  }
}) 