<template>
  <div class="production-progress-viewer">
    <div v-if="progressData" class="progress-container">
      <div class="progress-steps-flow">
        <!-- 烧花/剪图 -->
        <el-tooltip placement="top" effect="dark">
          <template #content>
            <div>烧花/剪图</div>
            <div v-if="progressData.cuttingStatus === 1">
              <div>操作员: {{ progressData.cuttingOperatorName || '系统' }}</div>
              <div>时间: {{ formatTime(progressData.cuttingTime) }}</div>
            </div>
            <div v-else>状态: 待处理</div>
          </template>
          <div class="flow-step" :class="{ 'is-completed': progressData.cuttingStatus === 1 }">
            <div class="step-icon">
              <el-icon v-if="progressData.cuttingStatus === 1"><Check /></el-icon>
              <el-icon v-else><More /></el-icon>
            </div>
            <div class="step-info">
              <div class="step-title">烧花/剪图</div>
            </div>
          </div>
        </el-tooltip>
        
        <!-- 车间/拣货 -->
        <el-tooltip placement="top" effect="dark">
          <template #content>
            <div>车间/拣货</div>
            <div v-if="progressData.workshopStatus === 1">
              <div>操作员: {{ progressData.workshopOperatorName || '系统' }}</div>
              <div>时间: {{ formatTime(progressData.workshopTime) }}</div>
            </div>
            <div v-else>状态: 待处理</div>
          </template>
          <div class="flow-step" :class="{ 'is-completed': progressData.workshopStatus === 1 }">
            <div class="step-icon">
              <el-icon v-if="progressData.workshopStatus === 1"><Check /></el-icon>
              <el-icon v-else><More /></el-icon>
            </div>
            <div class="step-info">
              <div class="step-title">车间/拣货</div>
            </div>
          </div>
        </el-tooltip>
        
        <!-- 剪线/压图 -->
        <el-tooltip placement="top" effect="dark">
          <template #content>
            <div>剪线/压图</div>
            <div v-if="progressData.trimmingStatus === 1">
              <div>操作员: {{ progressData.trimmingOperatorName || '系统' }}</div>
              <div>时间: {{ formatTime(progressData.trimmingTime) }}</div>
            </div>
            <div v-else>状态: 待处理</div>
          </template>
          <div class="flow-step" :class="{ 'is-completed': progressData.trimmingStatus === 1 }">
            <div class="step-icon">
              <el-icon v-if="progressData.trimmingStatus === 1"><Check /></el-icon>
              <el-icon v-else><More /></el-icon>
            </div>
            <div class="step-info">
              <div class="step-title">剪线/压图</div>
            </div>
          </div>
        </el-tooltip>
        
        <!-- 查货 -->
        <el-tooltip placement="top" effect="dark">
          <template #content>
            <div>查货</div>
            <div v-if="progressData.inspectionStatus === 1">
              <div>操作员: {{ progressData.inspectionOperatorName || '系统' }}</div>
              <div>时间: {{ formatTime(progressData.inspectionTime) }}</div>
            </div>
            <div v-else>状态: 待处理</div>
          </template>
          <div class="flow-step" :class="{ 'is-completed': progressData.inspectionStatus === 1 }">
            <div class="step-icon">
              <el-icon v-if="progressData.inspectionStatus === 1"><Check /></el-icon>
              <el-icon v-else><More /></el-icon>
            </div>
            <div class="step-info">
              <div class="step-title">查货</div>
            </div>
          </div>
        </el-tooltip>
        
        <!-- 包装 -->
        <el-tooltip placement="top" effect="dark">
          <template #content>
            <div>包装</div>
            <div v-if="progressData.packagingStatus === 1">
              <div>操作员: {{ progressData.packagingOperatorName || '系统' }}</div>
              <div>时间: {{ formatTime(progressData.packagingTime) }}</div>
            </div>
            <div v-else>状态: 待处理</div>
          </template>
          <div class="flow-step" :class="{ 'is-completed': progressData.packagingStatus === 1 }">
            <div class="step-icon">
              <el-icon v-if="progressData.packagingStatus === 1"><Check /></el-icon>
              <el-icon v-else><More /></el-icon>
            </div>
            <div class="step-info">
              <div class="step-title">包装</div>
            </div>
          </div>
        </el-tooltip>
        
        <!-- 发货 -->
        <el-tooltip placement="top" effect="dark">
          <template #content>
            <div>发货</div>
            <div v-if="progressData.shippingStatus === 1">
              <div>操作员: {{ progressData.shippingOperatorName || '系统' }}</div>
              <div>时间: {{ formatTime(progressData.shippingTime) }}</div>
            </div>
            <div v-else>状态: 待处理</div>
          </template>
          <div class="flow-step" :class="{ 'is-completed': progressData.shippingStatus === 1 }">
            <div class="step-icon">
              <el-icon v-if="progressData.shippingStatus === 1"><Check /></el-icon>
              <el-icon v-else><More /></el-icon>
            </div>
            <div class="step-info">
              <div class="step-title">发货</div>
            </div>
          </div>
        </el-tooltip>
      </div>
    </div>
    
    <div v-else class="progress-not-found">
      <el-icon><Warning /></el-icon>
      <span>暂无生产进度</span>
    </div>
    
    <!-- 管理员操作抽屉 -->
    <el-drawer
      v-if="isAdmin && progressData"
      v-model="drawerVisible"
      title="生产进度管理"
      direction="rtl"
      size="350px"
      :modal="true"
      modal-class="progress-drawer-modal"
      :append-to-body="true"
      :close-on-click-modal="true"
      :show-close="true"
    >
      <div class="admin-panel">
        <div class="panel-title">备货单 {{ subPurchaseOrderSn }} 生产进度管理</div>
        
        <el-divider content-position="left">进度操作</el-divider>
        
        <div class="operation-list">
          <div 
            v-for="(step, index) in progressSteps" 
            :key="index"
            class="operation-item"
          >
            <span class="operation-name">{{ step.label }}</span>
            <div class="operation-actions">
              <el-button
                type="success"
                size="small"
                :disabled="getStepStatus(step.key) === 1"
                @click="updateProgress(step.key, 1)"
              >
                标记完成
              </el-button>
              <el-button
                type="danger"
                size="small"
                :disabled="getStepStatus(step.key) === 0"
                @click="updateProgress(step.key, 0)"
              >
                撤销完成
              </el-button>
            </div>
          </div>
        </div>
        
        <el-divider content-position="left">操作日志</el-divider>
        
        <div v-if="progressLogs.length > 0" class="operation-logs">
          <div v-for="(log, index) in progressLogs" :key="index" class="log-item">
            <div class="log-time">{{ formatTime(log.operationTime) }}</div>
            <div class="log-content">
              <span class="log-operator">{{ log.operatorName }}</span>
              <span>{{ getLogActionText(log) }}</span>
            </div>
          </div>
        </div>
        <div v-else class="no-logs">暂无操作日志</div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, withDefaults, onMounted } from 'vue';
import { ElMessage, ElTooltip } from 'element-plus';
import { Check, More, Warning } from '@element-plus/icons-vue';
import { formatTime as formatTimeUtil } from '@/utils/format';
import { getProductionProgress, updateProductionProgress, getProgressLogs, cancelProductionProgress } from '@/api/temu/productionProgress';

// 定义组件属性
const props = withDefaults(defineProps<{
  subPurchaseOrderSn: string;
  shopId?: number;
  isAdmin?: boolean;
  autoFetch?: boolean;
}>(), {
  shopId: 0,
  isAdmin: false,
  autoFetch: false
});

// 组件状态
const drawerVisible = ref(false);
const progressData = ref<any>(null);
const progressLogs = ref<any[]>([]);

// 工序步骤定义
const progressSteps = [
  { key: 'cutting', label: '烧花/剪图' },
  { key: 'workshop', label: '车间/拣货' },
  { key: 'trimming', label: '剪线/压图' },
  { key: 'inspection', label: '查货' },
  { key: 'packaging', label: '包装' },
  { key: 'shipping', label: '发货' }
];

// 格式化时间
const formatTime = (time: string | number) => {
  if (!time) return '--';
  return formatTimeUtil(time);
};

// 获取某个工序的状态
const getStepStatus = (stepKey: string) => {
  if (!progressData.value) return 0;
  return progressData.value[`${stepKey}Status`] || 0;
};

// 获取日志操作文本
const getLogActionText = (log: any) => {
  if (!log) return '';
  
  const stepMap: {[key: string]: string} = {
    'cutting': '烧花/剪图',
    'workshop': '车间/拣货',
    'trimming': '剪线/压图',
    'inspection': '查货',
    'packaging': '包装',
    'shipping': '发货',
    // 兼容旧格式
    'burning': '烧花/剪图',
    'sewing': '车间/拣货',
    'tail': '剪线/压图',
    'delivery': '包装'
  };
  
  const operationMap: {[key: string]: string} = {
    '1': '完成了',
    '2': '撤销了'
  };
  
  const stepName = stepMap[log.progressType] || log.progressType;
  const operationName = operationMap[log.operationType] || '操作了';
  
  return `${operationName}${stepName}工序`;
};

// 将旧格式数据转换为新格式
const adaptLegacyData = (data: any): any => {
  if (!data) return null;
  
  // 创建基础对象
  const adapted: any = { ...data };
  
  // 映射旧字段到新字段（如果存在）
  if (data.burningStatus !== undefined) adapted.cuttingStatus = data.burningStatus;
  if (data.burningTime !== undefined) adapted.cuttingTime = data.burningTime;
  if (data.burningOperatorId !== undefined) adapted.cuttingOperatorId = data.burningOperatorId;
  if (data.burningOperatorName !== undefined) adapted.cuttingOperatorName = data.burningOperatorName;
  
  if (data.sewingStatus !== undefined) adapted.workshopStatus = data.sewingStatus;
  if (data.sewingTime !== undefined) adapted.workshopTime = data.sewingTime;
  if (data.sewingOperatorId !== undefined) adapted.workshopOperatorId = data.sewingOperatorId;
  if (data.sewingOperatorName !== undefined) adapted.workshopOperatorName = data.sewingOperatorName;
  
  if (data.tailStatus !== undefined) adapted.trimmingStatus = data.tailStatus;
  if (data.tailTime !== undefined) adapted.trimmingTime = data.tailTime;
  if (data.tailOperatorId !== undefined) adapted.trimmingOperatorId = data.tailOperatorId;
  if (data.tailOperatorName !== undefined) adapted.trimmingOperatorName = data.tailOperatorName;
  
  if (data.deliveryStatus !== undefined) adapted.packagingStatus = data.deliveryStatus;
  if (data.deliveryTime !== undefined) adapted.packagingTime = data.deliveryTime;
  if (data.deliveryOperatorId !== undefined) adapted.packagingOperatorId = data.deliveryOperatorId;
  if (data.deliveryOperatorName !== undefined) adapted.packagingOperatorName = data.deliveryOperatorName;
  
  // 确保查货字段存在
  if (adapted.inspectionStatus === undefined) adapted.inspectionStatus = 0;
  if (adapted.inspectionTime === undefined) adapted.inspectionTime = null;
  if (adapted.inspectionOperatorId === undefined) adapted.inspectionOperatorId = null;
  if (adapted.inspectionOperatorName === undefined) adapted.inspectionOperatorName = null;
  
  return adapted;
};

// 获取生产进度数据
const fetchProgressData = async () => {
  if (!props.subPurchaseOrderSn) return;
  
  try {
    const res = await getProductionProgress({
      subPurchaseOrderSn: props.subPurchaseOrderSn,
      shopId: props.shopId || 0
    });
    
    console.log('生产进度API响应:', res);
    
    let responseData = null;
    let isSuccess = false;
    const anyRes = res as any;
    
    if (res?.data?.code === 200 && res.data.data) {
      responseData = res.data.data;
      isSuccess = true;
    } else if (anyRes?.code === 200 && anyRes.data) {
      responseData = anyRes.data;
      isSuccess = true;
    } else if (res && typeof res === 'object' && anyRes.code === 200 && anyRes.data) {
      responseData = anyRes.data;
      isSuccess = true;
    }
    
    if (isSuccess && responseData) {
      // 使用适配器处理可能的旧格式数据
      progressData.value = adaptLegacyData(responseData);
    } else {
      console.error('获取生产进度失败:', res);
      progressData.value = {
        cuttingStatus: 0, workshopStatus: 0, trimmingStatus: 0, 
        inspectionStatus: 0, packagingStatus: 0, shippingStatus: 0
      };
    }
  } catch (error) {
    console.error('获取生产进度异常:', error);
    progressData.value = {
      cuttingStatus: 0, workshopStatus: 0, trimmingStatus: 0, 
      inspectionStatus: 0, packagingStatus: 0, shippingStatus: 0
    };
  }
};

// 获取进度操作日志
const fetchProgressLogs = async () => {
  if (!props.subPurchaseOrderSn || !props.isAdmin) return;
  
  try {
    const res = await getProgressLogs({
      subPurchaseOrderSn: props.subPurchaseOrderSn,
      shopId: props.shopId || 0
    });
    
    const anyRes = res as any;
    let logsData = null;
    let isSuccess = false;
    
    if (res?.data?.code === 200 && Array.isArray(res.data.data)) {
      logsData = res.data.data;
      isSuccess = true;
    } else if (anyRes?.code === 200 && Array.isArray(anyRes.data)) {
      logsData = anyRes.data;
      isSuccess = true;
    }
    
    if (isSuccess && logsData) {
      progressLogs.value = logsData;
    } else {
      console.error('获取生产进度日志失败:', res);
      progressLogs.value = [];
    }
  } catch (error) {
    console.error('获取生产进度日志异常:', error);
    progressLogs.value = [];
  }
};

// 更新生产进度
const updateProgress = async (stepKey: string, status: number) => {
  if (!props.subPurchaseOrderSn || !props.isAdmin) {
    ElMessage.warning('没有管理权限或备货单号为空');
    return;
  }
  
  try {
    let res;
    if (status === 1) {
      res = await updateProductionProgress({
        subPurchaseOrderSn: props.subPurchaseOrderSn,
        shopId: props.shopId || 0,
        progressType: stepKey,
        operationType: "1",
        remarks: ''
      });
    } else {
      res = await cancelProductionProgress({
        subPurchaseOrderSn: props.subPurchaseOrderSn,
        shopId: props.shopId || 0,
        progressType: stepKey,
        remarks: ''
      });
    }
    
    const anyRes = res as any;
    let isSuccess = false;
    
    if (res?.data?.code === 200 || anyRes?.code === 200) {
      isSuccess = true;
    }
    
    if (isSuccess) {
      ElMessage.success(status === 1 ? '标记完成成功' : '撤销完成成功');
      await fetchProgressData();
      await fetchProgressLogs();
    } else {
      let errorMsg = res?.data?.message || anyRes?.message || '操作失败';
      console.error('更新生产进度失败:', errorMsg);
      ElMessage.error(errorMsg);
    }
  } catch (error) {
    console.error('更新生产进度异常:', error);
    ElMessage.error('更新生产进度失败，请稍后重试');
  }
};

// 打开管理抽屉
const openManagementDrawer = async () => {
  if (!props.isAdmin) {
    ElMessage.warning('没有管理权限');
    return;
  }
  drawerVisible.value = true;
  await fetchProgressData();
  await fetchProgressLogs();
};

onMounted(() => {
  if (props.autoFetch) {
    fetchProgressData();
  }
});

// 直接更新进度数据
const updateProgressData = (data: any) => {
  if (!data) {
    console.warn('尝试更新生产进度数据，但数据为空');
    progressData.value = { 
      cuttingStatus: 0, workshopStatus: 0, trimmingStatus: 0, 
      inspectionStatus: 0, packagingStatus: 0, shippingStatus: 0 
    };
    return;
  }
  
  try {
    // 使用适配器处理可能的旧格式数据
    const adaptedData = adaptLegacyData(data);
    
    const safeData = {
      cuttingStatus: adaptedData.cuttingStatus ?? 0,
      workshopStatus: adaptedData.workshopStatus ?? 0, 
      trimmingStatus: adaptedData.trimmingStatus ?? 0,
      inspectionStatus: adaptedData.inspectionStatus ?? 0,
      packagingStatus: adaptedData.packagingStatus ?? 0,
      shippingStatus: adaptedData.shippingStatus ?? 0,
      ...adaptedData,
      cuttingTime: adaptedData.cuttingTime || null,
      cuttingOperatorName: adaptedData.cuttingOperatorName || null,
      workshopTime: adaptedData.workshopTime || null,
      workshopOperatorName: adaptedData.workshopOperatorName || null,
      trimmingTime: adaptedData.trimmingTime || null,
      trimmingOperatorName: adaptedData.trimmingOperatorName || null,
      inspectionTime: adaptedData.inspectionTime || null,
      inspectionOperatorName: adaptedData.inspectionOperatorName || null,
      packagingTime: adaptedData.packagingTime || null,
      packagingOperatorName: adaptedData.packagingOperatorName || null,
      shippingTime: adaptedData.shippingTime || null,
      shippingOperatorName: adaptedData.shippingOperatorName || null
    };
    progressData.value = safeData;
  } catch (error) {
    console.error('更新生产进度数据时出错:', error);
    progressData.value = { 
      cuttingStatus: 0, workshopStatus: 0, trimmingStatus: 0, 
      inspectionStatus: 0, packagingStatus: 0, shippingStatus: 0 
    };
  }
};

// 暴露组件方法
defineExpose({
  refresh: fetchProgressData,
  openManagementDrawer,
  updateProgressData
});
</script>

<style scoped>
.production-progress-viewer {
  width: 100%;
}

.progress-container {
  width: 100%;
  border: none;
  border-radius: 0;
  overflow: hidden;
  background-color: transparent;
}

.progress-steps-flow {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0;
  white-space: nowrap;
  scrollbar-width: none;
  -ms-overflow-style: none;
  overflow-x: auto;
}

.progress-steps-flow::-webkit-scrollbar {
  display: none;
}

.flow-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 35px;
  width: auto;
  padding: 0 2px;
  position: relative;
  margin-right: 5px;
  cursor: default;
}

.flow-step:last-child {
  margin-right: 0;
}

.step-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #f0f2f5;
  color: #909399;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 3px;
  z-index: 1;
  font-size: 12px;
  border: 1px solid #e0e0e0;
}

.flow-step.is-completed .step-icon {
  background-color: #67c23a;
  color: #fff;
  border-color: #67c23a;
}

.step-info {
  text-align: center;
  width: 100%;
}

.step-title {
  font-weight: 500;
  font-size: 11px;
  margin-bottom: 0;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 55px;
}

.progress-not-found {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px 0;
  color: #909399;
  font-size: 12px;
  background-color: transparent;
  border: none;
}

.progress-not-found .el-icon {
  font-size: 14px;
  margin-right: 4px;
}

/* 管理员面板样式 */
.admin-panel {
  padding: 0 16px;
}

.panel-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #303133;
}

.operation-list {
  margin-bottom: 20px;
}

.operation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px dashed #ebeef5;
}

.operation-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.operation-name {
  font-weight: 500;
  color: #303133;
}

.operation-actions {
  display: flex;
  gap: 8px;
}

.operation-logs {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px dashed #ebeef5;
}

.log-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.log-time {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.log-content {
  font-size: 13px;
  color: #606266;
}

.log-operator {
  color: #409eff;
  margin-right: 4px;
}

.no-logs {
  text-align: center;
  color: #909399;
  padding: 20px 0;
}

/* 管理员抽屉遮罩样式 */
:deep(.progress-drawer-modal) {
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(2px);
}

:deep(.el-drawer) {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  z-index: 2001 !important;
}
</style> 