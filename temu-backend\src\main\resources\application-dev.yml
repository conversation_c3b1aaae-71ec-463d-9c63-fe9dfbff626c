# 开发环境配置

# 服务器端口配置
server:
  port: 8080  # 开发环境端口

spring:
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************
    username: root
    password: 123456
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 5000
      lettuce:
        pool:
          max-active: 8
          max-wait: -1
          max-idle: 8
          min-idle: 0
  # Quartz定时任务配置
  quartz:
    job-store-type: memory
    scheduler-name: TemuApiScheduler
    startup-delay: 1s
    auto-startup: true
    wait-for-jobs-to-complete-on-shutdown: true
    overwrite-existing-jobs: true

# 日志配置
logging:
  level:
    root: info
    com.xiao.temu: debug
    # 减少销售数据同步相关的SQL日志输出
    com.xiao.temu.mapper.com.xiao.temu.modules.sales.mapper.SalesSubOrderMapper: warn
    com.xiao.temu.mapper.com.xiao.temu.modules.sales.mapper.SalesSkuQuantityMapper: warn
    com.xiao.temu.mapper.com.xiao.temu.modules.sales.mapper.SalesWarehouseInfoMapper: warn
    com.xiao.temu.mapper.com.xiao.temu.modules.sales.mapper.SalesInventoryInfoMapper: warn
    com.xiao.temu.mapper.com.xiao.temu.modules.sales.mapper.SalesCustomInfoMapper: warn
    com.xiao.temu.mapper.com.xiao.temu.modules.sales.mapper.SalesWarehouseInventoryMapper: warn
    com.xiao.temu.mapper.com.xiao.temu.modules.sync.mapper.SalesSyncTaskMapper: warn
  file:
    name: logs/temu-backend.log

# 腾讯云对象存储配置
tencent:
  cos:
    secret-id: AKIDxpY4MRIBCtNyNfnkX7UdZLxQgpnbGk14
    secret-key: tZ6RZTCj3A7tSnwyqU2ThldHk3B5QXR8
    region: ap-guangzhou
    bucket-name: temu-api-1302872665 