import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
// 不再导入组件映射
// import { componentMap as systemComponentMap } from './modules/system'
// import { componentMap as synchronousComponentMap } from './modules/synchronous'
// import { componentMap as localComponentMap } from './modules/local'
// import { componentMap as temuComponentMap } from './modules/temu'

// 不再需要合并组件映射
// export const componentMap = {
//   ...systemComponentMap,
//   ...synchronousComponentMap,
//   ...localComponentMap,
//   ...temuComponentMap
// }

// 静态路由 - 所有用户都可以访问的路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录', hidden: true }
  },
  {
    path: '/',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        name: 'Dashboard',
        meta: { title: '首页', icon: 'House', affix: true }
      }
    ]
  },
  {
    path: '/dashboard',
    component: () => import('@/layout/index.vue'),
    children: [
      {
        path: '',
        component: () => import('@/views/dashboard/index.vue'),
        name: 'DashboardIndex',
        meta: { title: '首页', icon: 'House', affix: true }
      }
    ]
  },
  // 个人中心路由
  {
    path: '/profile',
    component: () => import('@/layout/index.vue'),
    meta: { hidden: true },
    children: [
      {
        path: '',
        component: () => import('@/views/profile/index.vue'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'User', hidden: true }
      }
    ]
  },
  // 重定向路由
  {
    path: '/redirect',
    component: () => import('@/layout/index.vue'),
    meta: { hidden: true },
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue'),
        meta: { hidden: true }
      }
    ]
  },
  {
    path: '/404',
    component: () => import('@/views/error/404.vue'),
    meta: { hidden: true }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior: () => ({ top: 0 })
})

// 重置路由
export function resetRouter() {
  const newRouter = createRouter({
    history: createWebHistory(),
    routes: constantRoutes,
    scrollBehavior: () => ({ top: 0 })
  })
  // @ts-ignore
  router.matcher = newRouter.matcher
}

// 动态路由错误页面 - 保持在最后添加
export const pageNotFoundRoute: RouteRecordRaw = {
  path: '/:pathMatch(.*)*',
  redirect: '/dashboard',
  meta: { hidden: true }
}

export default router 