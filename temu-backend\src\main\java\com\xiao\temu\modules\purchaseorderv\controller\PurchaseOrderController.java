package com.xiao.temu.modules.purchaseorderv.controller;

import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.purchaseorderv.dto.PurchaseOrderRequestDTO;
import com.xiao.temu.modules.sync.vo.ApiResponse;
import com.xiao.temu.modules.purchaseorderv.vo.PurchaseOrderVO;
import com.xiao.temu.modules.purchaseorderv.service.PurchaseOrderService;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.system.service.SysDataPermissionService;
import com.xiao.temu.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

import com.xiao.temu.infrastructure.task.ExportTaskManager;
import com.xiao.temu.infrastructure.excel.ExcelExportService;
import com.xiao.temu.infrastructure.exporter.PurchaseOrderExcelExporter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.File;
import java.io.BufferedOutputStream;
import java.io.OutputStream;
import java.io.RandomAccessFile;
import java.net.SocketTimeoutException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;

/**
 * Temu采购单控制器
 */
@Slf4j
@RestController
@RequestMapping("/temu/purchaseOrder")
public class PurchaseOrderController {

    @Autowired
    private PurchaseOrderService purchaseOrderService;
    
    @Autowired
    private ShopService shopService;
    
    @Autowired
    private UserService userService;

    @Autowired
    private SysDataPermissionService dataPermissionService;

    @Autowired
    private ExportTaskManager exportTaskManager;

    @Autowired
    private ExcelExportService excelExportService;

    @Autowired
    private PurchaseOrderExcelExporter purchaseOrderExcelExporter;

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        // 通过用户名查询用户信息获取用户ID
        SysUser user = userService.getUserByUsername(username);
        if (user != null) {
            return user.getUserId();
        }
        throw new RuntimeException("用户未找到: " + username);
    }

    /**
     * 获取采购单列表
     *
     * @param requestDTO 请求参数
     * @return API响应
     */
    @PostMapping("/list")
    public ApiResponse getPurchaseOrders(@RequestBody PurchaseOrderRequestDTO requestDTO) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        
        // 判断用户是否为管理员或拥有全部数据权限
        boolean isAdmin = userService.isAdmin(userId);
        
        // 获取用户的最高数据权限
        String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
        boolean hasFullDataPermission = "2".equals(permissionType);
        
        // 如果是管理员或拥有全部数据权限，设置忽略权限检查标志
        if (isAdmin || hasFullDataPermission) {
            requestDTO.setIgnorePermissionCheck(true);
        }
        
        // 调用服务获取结果
        PurchaseOrderVO response = purchaseOrderService.getPurchaseOrderList(requestDTO, userId);
        
        if (response.getSuccess()) {
            return ApiResponse.success(response);
        } else {
            return ApiResponse.error(response.getErrorCode(), response.getErrorMsg(), response);
        }
    }
    


    /**
     * 创建导出任务并返回任务ID
     * @param exportParams 导出参数
     * @return API响应，包含任务ID
     */
    @PostMapping("/createExportTask")
    public ApiResponse createExportTask(@RequestBody Map<String, Object> exportParams) {
        try {
            // 获取当前用户ID
            Long userId = getCurrentUserId();
            
            // 获取文件名
            String fileName = (String) exportParams.get("fileName");
            if (fileName == null || fileName.trim().isEmpty()) {
                fileName = "采购单数据";
            }
            
            // 创建导出任务
            String taskId = exportTaskManager.createTask(fileName);
            
            // 将导出参数与任务ID关联起来
            exportParams.put("taskId", taskId);
            exportParams.put("userId", userId); // 保存当前用户ID到导出参数
            
            // 记录日志
            String exportType = (String) exportParams.get("exportType");
            Map<String, Object> exportConfig = (Map<String, Object>) exportParams.get("exportConfig");
            Map<String, Object> queryParams = (Map<String, Object>) exportParams.get("queryParams");
            
            log.info("创建导出任务: taskId={}, 导出类型={}, 文件名={}", 
                    taskId, exportType, fileName);
            
            if (exportConfig != null) {
                log.info("导出配置: pageSize={}, pageCount={}", 
                        exportConfig.get("pageSize"), exportConfig.get("pageCount"));
            }
            
            // 确保查询参数中包含pageNo和pageSize
            if (queryParams != null && queryParams.get("pageNo") != null) {
                log.info("当前页码: {}", queryParams.get("pageNo"));
            }
            
            // 异步执行导出操作
            excelExportService.asyncExportExcel(exportParams, purchaseOrderExcelExporter);
            
            // 返回任务ID
            Map<String, String> result = new HashMap<>();
            result.put("taskId", taskId);
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("创建导出任务失败", e);
            return ApiResponse.error("创建导出任务失败: " + e.getMessage());
        }
    }

    /**
     * 导出采购单数据到Excel
     */
    @PostMapping("/export")
    public ApiResponse exportPurchaseOrderData(@RequestBody Map<String, Object> exportParams) {
        try {
            // 获取当前用户ID
            Long userId = getCurrentUserId();
            
            // 获取文件名
            String fileName = (String) exportParams.get("fileName");
            if (fileName == null || fileName.trim().isEmpty()) {
                fileName = "采购单数据";
            }
            
            // 创建导出任务
            String taskId = exportTaskManager.createTask(fileName);
            
            // 将导出参数与任务ID关联起来
            exportParams.put("taskId", taskId);
            exportParams.put("userId", userId); // 保存当前用户ID到导出参数
            
            // 记录日志
            String exportType = (String) exportParams.get("exportType");
            Map<String, Object> exportConfig = (Map<String, Object>) exportParams.get("exportConfig");
            Map<String, Object> queryParams = (Map<String, Object>) exportParams.get("queryParams");
            
            log.info("创建导出任务: taskId={}, 导出类型={}, 文件名={}", 
                    taskId, exportType, fileName);
            
            if (exportConfig != null) {
                log.info("导出配置: pageSize={}, pageCount={}", 
                        exportConfig.get("pageSize"), exportConfig.get("pageCount"));
            }
            
            // 确保查询参数中包含pageNo和pageSize
            if (queryParams != null && queryParams.get("pageNo") != null) {
                log.info("当前页码: {}", queryParams.get("pageNo"));
            }
            
            // 异步执行导出操作
            excelExportService.asyncExportExcel(exportParams, purchaseOrderExcelExporter);
            
            // 返回任务ID
            Map<String, String> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("message", "导出任务已创建，请稍候");
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("导出采购单数据失败", e);
            return ApiResponse.error("导出采购单数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取导出任务进度
     * @param taskId 任务ID
     * @return 导出任务进度
     */
    @GetMapping("/exportProgress/{taskId}")
    public ApiResponse getExportProgress(@PathVariable String taskId) {
        ExportTaskManager.TaskInfo taskInfo = exportTaskManager.getTaskInfo(taskId);
        if (taskInfo == null) {
            return ApiResponse.error("任务不存在");
        }
        
        Map<String, Object> progressData = new HashMap<>();
        progressData.put("taskId", taskInfo.getTaskId());
        progressData.put("progress", taskInfo.getProgress());
        progressData.put("status", taskInfo.getStatus());
        progressData.put("message", taskInfo.getMessage());
        progressData.put("fileName", taskInfo.getFileName());
        
        return ApiResponse.success(progressData);
    }

    /**
     * 下载已生成的Excel文件
     * @param taskId 任务ID
     * @param response HTTP响应对象
     */
    @GetMapping("/downloadExcel/{taskId}")
    public void downloadExcelFile(@PathVariable String taskId, 
                                 HttpServletRequest request,
                                 HttpServletResponse response) {
        try {
            // 获取当前用户ID
            Long userId = getCurrentUserId();
            log.info("用户 [{}] 正在下载文件，任务ID: {}", userId, taskId);
            
            // 获取任务信息
            ExportTaskManager.TaskInfo taskInfo = exportTaskManager.getTaskInfo(taskId);
            if (taskInfo == null) {
                writeJsonResponse(response, 500, "任务不存在");
                return;
            }
            
            if (!"completed".equals(taskInfo.getStatus())) {
                writeJsonResponse(response, 500, "任务未完成，无法下载");
                return;
            }
            
            // 查找导出文件
            File outputDir = new File(System.getProperty("java.io.tmpdir"), "temu_export_output");
            File[] files = outputDir.listFiles((dir, name) -> name.startsWith(taskInfo.getFileName() + "_") && name.endsWith(".xlsx"));
            
            if (files == null || files.length == 0) {
                writeJsonResponse(response, 500, "找不到导出文件");
                return;
            }
            
            // 找到最新的文件
            File latestFile = files[0];
            for (File file : files) {
                if (file.lastModified() > latestFile.lastModified()) {
                    latestFile = file;
                }
            }
            
            // 记录文件路径到任务信息中，以便定时任务清理
            if (taskInfo.getFilePath() == null) {
                exportTaskManager.completeTaskWithFilePath(taskId, "导出完成，正在下载", latestFile.getAbsolutePath());
            }
            
            // 获取文件信息
            long fileSize = latestFile.length();
            log.info("开始下载文件: {}, 大小: {} MB", latestFile.getName(), fileSize / (1024.0 * 1024.0));
            
            // 设置响应头
            String encodedFileName = URLEncoder.encode(taskInfo.getFileName(), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");
            
            // 设置内容长度，帮助客户端预先知道文件大小
            response.setContentLengthLong(fileSize);
            
            // 添加特殊响应头，提高大文件下载的稳定性
            response.setHeader("Cache-Control", "private, max-age=86400");
            response.setHeader("Pragma", "private");
            response.setHeader("Accept-Ranges", "bytes");
            
            // 处理范围请求（支持断点续传）
            String rangeHeader = request.getHeader("Range");
            if (rangeHeader != null && rangeHeader.startsWith("bytes=")) {
                // 这是一个范围请求，处理断点续传
                handleRangeRequest(response, latestFile, rangeHeader, fileSize, taskId, userId);
            } else {
                // 执行常规文件传输
                transferFile(response, latestFile, fileSize, taskId, userId);
            }
            
        } catch (Exception e) {
            log.error("下载处理主流程异常: {}", e.getMessage(), e);
            if (!response.isCommitted()) {
                try {
                    writeJsonResponse(response, 500, "处理下载请求失败: " + e.getMessage());
                } catch (IOException ex) {
                    log.error("设置错误响应失败", ex);
                }
            }
        }
    }
    
    /**
     * 处理范围请求（断点续传）
     */
    private void handleRangeRequest(HttpServletResponse response, File file, String rangeHeader, 
                                  long fileSize, String taskId, Long userId) throws IOException {
        try {
            // 解析请求范围（例如："bytes=0-1023"）
            String[] ranges = rangeHeader.substring(6).split("-");
            long startByte = Long.parseLong(ranges[0]);
            long endByte = ranges.length > 1 && !ranges[1].isEmpty() 
                ? Long.parseLong(ranges[1]) 
                : fileSize - 1;
            
            // 验证范围有效性
            if (startByte >= fileSize || endByte >= fileSize || startByte > endByte) {
                response.setStatus(HttpServletResponse.SC_REQUESTED_RANGE_NOT_SATISFIABLE);
                response.setHeader("Content-Range", "bytes */" + fileSize);
                return;
            }
            
            // 计算要发送的字节长度
            long contentLength = endByte - startByte + 1;
            
            // 设置206状态码（部分内容）和相关响应头
            response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
            response.setHeader("Content-Range", "bytes " + startByte + "-" + endByte + "/" + fileSize);
            response.setContentLengthLong(contentLength);
            
            log.info("处理范围请求: bytes {}-{}/{}, 大小: {} MB", 
                     startByte, endByte, fileSize, contentLength / (1024.0 * 1024.0));
            
            // 传输指定范围的字节
            try (RandomAccessFile raf = new RandomAccessFile(file, "r");
                 OutputStream out = new BufferedOutputStream(response.getOutputStream(), 65536)) {
                
                byte[] buffer = new byte[65536];
                long remaining = contentLength;
                raf.seek(startByte);
                
                while (remaining > 0) {
                    int read = raf.read(buffer, 0, (int) Math.min(buffer.length, remaining));
                    if (read == -1) break;
                    
                    out.write(buffer, 0, read);
                    remaining -= read;
                    
                    // 每写入1MB数据刷新一次，保持数据流动
                    if (remaining % (1024 * 1024) < buffer.length) {
                        out.flush();
                    }
                }
                
                out.flush();
            }
            
            log.info("范围请求处理完成，用户: {}, 任务ID: {}, 范围: bytes {}-{}/{}", 
                     userId, taskId, startByte, endByte, fileSize);
            
        } catch (NumberFormatException e) {
            log.error("解析Range头失败: {}", rangeHeader, e);
            // 如果范围解析失败，回退到完整传输
            transferFile(response, file, fileSize, taskId, userId);
        }
    }
    
    /**
     * 以分块方式传输大文件，防止内存溢出和网络超时
     */
    private void transferFile(HttpServletResponse response, File file, long fileSize, String taskId, Long userId) {
        // 对于大文件使用较大的缓冲区，但避免过大导致内存问题
        final int BUFFER_SIZE;
        if (fileSize < 100 * 1024 * 1024) { // < 100MB
            BUFFER_SIZE = 65536; // 64KB
        } else if (fileSize < 1024 * 1024 * 1024) { // < 1GB
            BUFFER_SIZE = 262144; // 256KB
        } else {
            BUFFER_SIZE = 524288; // 512KB
        }
        
        // 块大小也根据文件大小调整
        final long CHUNK_SIZE;
        if (fileSize < 100 * 1024 * 1024) { // < 100MB
            CHUNK_SIZE = 5 * 1024 * 1024; // 5MB
        } else if (fileSize < 1024 * 1024 * 1024) { // < 1GB
            CHUNK_SIZE = 10 * 1024 * 1024; // 10MB
        } else {
            CHUNK_SIZE = 20 * 1024 * 1024; // 20MB
        }
        
        log.info("文件传输开始 - 文件大小: {} MB, 缓冲区大小: {} KB, 块大小: {} MB", 
                fileSize / (1024.0 * 1024.0), 
                BUFFER_SIZE / 1024.0,
                CHUNK_SIZE / (1024.0 * 1024.0));
        
        try (RandomAccessFile raf = new RandomAccessFile(file, "r");
             OutputStream os = new BufferedOutputStream(response.getOutputStream(), BUFFER_SIZE)) {
            
            byte[] buffer = new byte[BUFFER_SIZE];
            long bytesRemaining = fileSize;
            long position = 0;
            int blockCount = 0;
            long startTime = System.currentTimeMillis();
            long lastLogTime = startTime;
            
            while (bytesRemaining > 0) {
                blockCount++;
                // 计算当前块大小
                int chunkSize = (int) Math.min(CHUNK_SIZE, bytesRemaining);
                int bytesRead = 0;
                
                // 读取并发送当前块数据
                while (bytesRead < chunkSize) {
                    raf.seek(position + bytesRead);
                    int read = raf.read(buffer, 0, (int) Math.min(buffer.length, chunkSize - bytesRead));
                    if (read == -1) break;
                    
                    os.write(buffer, 0, read);
                    bytesRead += read;
                }
                
                // 更新位置和剩余字节数
                position += bytesRead;
                bytesRemaining -= bytesRead;
                
                // 每次传输一定量的数据后刷新输出流，确保数据及时发送
                if (blockCount % 5 == 0) {
                    os.flush();
                    
                    // 每5秒记录一次进度日志，避免日志过多
                    long currentTime = System.currentTimeMillis();
                    if (currentTime - lastLogTime > 5000) {
                        lastLogTime = currentTime;
                        double percent = Math.round((position * 10000.0) / fileSize) / 100.0;
                        double elapsed = (currentTime - startTime) / 1000.0;
                        double speed = (position / (1024.0 * 1024.0)) / Math.max(0.001, elapsed);
                        double eta = bytesRemaining > 0 ? (bytesRemaining / (position / elapsed)) / 1000.0 : 0;
                        
                        log.info("文件传输进度: {} MB / {} MB ({}%), 速度: {} MB/s, 已用时: {}秒, 预计剩余: {}秒", 
                                String.format("%.2f", position / (1024.0 * 1024.0)), 
                                String.format("%.2f", fileSize / (1024.0 * 1024.0)),
                                String.format("%.2f", percent),
                                String.format("%.2f", speed),
                                String.format("%.1f", elapsed),
                                String.format("%.1f", eta));
                    }
                }
            }
            
            // 最后刷新确保所有数据发送
            os.flush();
            
            long endTime = System.currentTimeMillis();
            double totalTime = (endTime - startTime) / 1000.0;
            double totalSpeed = (fileSize / (1024.0 * 1024.0)) / Math.max(0.001, totalTime);
            
            log.info("文件下载成功: {}, 大小: {} MB, 耗时: {} 秒, 平均速率: {} MB/s, 用户: {}, 任务ID: {}", 
                    file.getName(), 
                    String.format("%.2f", fileSize / (1024.0 * 1024.0)),
                    String.format("%.1f", totalTime),
                    String.format("%.2f", totalSpeed),
                    userId,
                    taskId);
            
        } catch (IOException e) {
            handleTransferException(response, e);
        }
    }
    
    /**
     * 处理文件传输异常
     */
    private void handleTransferException(HttpServletResponse response, IOException e) {
        log.error("文件传输失败: {}", e.getMessage(), e);
        
        // 检查是否是客户端断开连接
        if (e instanceof SocketTimeoutException || 
            (e.getMessage() != null && (
                e.getMessage().contains("Connection reset") || 
                e.getMessage().contains("Broken pipe") ||
                e.getMessage().contains("连接重置") ||
                e.getMessage().contains("连接中断") ||
                e.getMessage().contains("connection abort") || 
                e.getMessage().contains("Socket closed") ||
                e.getMessage().contains("Connection closed") ||
                e.getMessage().contains("connection was aborted") ||
                e.getMessage().contains("Socket write error") ||
                e.getMessage().contains("SocketTimeoutException")
            )) ||
            (e.getCause() instanceof SocketTimeoutException)) {
            
            log.warn("客户端可能已断开连接，传输中断: {}", e.getMessage());
            return; // 客户端已断开，无需进一步处理
        }
        
        // 对于其他IO错误，如果响应尚未提交，尝试发送错误响应
        if (!response.isCommitted()) {
            try {
                writeJsonResponse(response, 500, "文件传输失败: " + e.getMessage());
            } catch (IOException ex) {
                log.error("无法发送错误响应: {}", ex.getMessage());
            }
        } else {
            log.error("无法发送错误响应，响应已提交");
        }
    }
    
    /**
     * 写入JSON格式的响应
     */
    private void writeJsonResponse(HttpServletResponse response, int code, String message) throws IOException {
        response.setContentType("application/json");
        response.setCharacterEncoding("utf-8");
        response.getWriter().write("{\"code\":" + code + ",\"message\":\"" + message + "\"}");
    }
} 