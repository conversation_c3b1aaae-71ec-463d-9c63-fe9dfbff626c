package com.xiao.temu.modules.violation.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 违规信息和质检结果综合数据DTO
 */
@Data
public class ViolationInspectionDTO {
    
    /**
     * 违规信息ID
     */
    private Long violationId;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺名称
     */
    private String shopName;
    
    /**
     * 店铺备注
     */
    private String shopRemark;
    
    /**
     * 违规编号
     */
    private String punishSn;
    
    /**
     * 备货单号
     */
    private String subPurchaseOrderSn;
    
    /**
     * 违规发起时间
     */
    private LocalDateTime violationTime;
    
    /**
     * 违规金额
     */
    private BigDecimal punishAmount;
    
    /**
     * 违规类型
     */
    private String punishTypeDesc;
    
    /**
     * 具体原因（一级+二级类型描述）
     */
    private String punishReasonDesc;
    
    /**
     * 商品SKU ID
     */
    private Long productSkuId;
    
    /**
     * 商品SKC ID
     */
    private Long productSkcId;
    
    /**
     * 商品名称
     */
    private String productName;
    
    /**
     * 商品外部编码
     */
    private String extCode;
    
    /**
     * 商品图片URL
     */
    private String thumbUrl;
    
    /**
     * 缺货件数
     */
    private Integer lackQuantity;
    
    /**
     * 质量问题件数
     */
    private Integer unqualifiedQuantity;
    
    /**
     * 备货件数
     */
    private Integer stockQuantity;
    
    /**
     * 疵点描述
     */
    private String flawNameDesc;
    
    /**
     * 疵点证明图片，JSON格式
     */
    private String attachments;
    
    /**
     * 疵点图片列表
     */
    private List<String> attachmentList;
} 