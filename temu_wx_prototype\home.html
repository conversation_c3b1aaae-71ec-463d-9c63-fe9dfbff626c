<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主页 - TEMU备货单追踪</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f7f7f7;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }
        .wx-container {
            max-width: 414px;
            min-height: 100vh;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 0;
            box-shadow: 0 0 10px rgba(0,0,0,0.05);
            overflow: hidden;
            position: relative;
        }
        @media (min-width: 500px) {
            .wx-container {
                min-height: 90vh;
                margin: 20px auto;
                border-radius: 30px;
            }
        }
        .wx-btn-primary {
            background-color: #07c160;
            color: white;
        }
        .wx-btn-primary:hover {
            background-color: #06ad56;
        }
    </style>
</head>
<body>
    <div class="wx-container">
        <div class="flex flex-col h-screen">
            <header class="py-4 px-4 flex justify-between items-center border-b border-gray-200">
                <h1 class="text-xl font-medium">TEMU备货单追踪</h1>
                <div class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full">烧花工</div>
            </header>
            
            <div class="flex-grow p-6">
                <div class="mb-8">
                    <h2 class="text-lg font-medium mb-4">欢迎，李师傅</h2>
                    <p class="text-gray-600">今日待处理：<span class="font-bold text-red-500">12</span> 单</p>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <a href="scan.html" class="flex flex-col items-center justify-center bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                        <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mb-3">
                            <i class="fas fa-qrcode text-2xl text-green-600"></i>
                        </div>
                        <span class="text-gray-800 font-medium">扫码处理</span>
                    </a>
                    
                    <a href="worklist.html" class="flex flex-col items-center justify-center bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                        <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mb-3">
                            <i class="fas fa-list-ul text-2xl text-blue-600"></i>
                        </div>
                        <span class="text-gray-800 font-medium">工作列表</span>
                    </a>
                </div>
                
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="font-medium mb-2">近期活动</h3>
                    <div class="space-y-3">
                        <div class="flex items-center text-sm">
                            <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                            <span class="text-gray-600">已完成：备货单 #TM20230615 处理</span>
                            <span class="ml-auto text-xs text-gray-400">10:30</span>
                        </div>
                        <div class="flex items-center text-sm">
                            <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                            <span class="text-gray-600">已完成：备货单 #TM20230614 处理</span>
                            <span class="ml-auto text-xs text-gray-400">09:15</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <footer class="bg-white border-t border-gray-200">
                <div class="grid grid-cols-3 text-center">
                    <a href="home.html" class="py-3 flex flex-col items-center text-green-600">
                        <i class="fas fa-home text-xl"></i>
                        <span class="text-xs mt-1">首页</span>
                    </a>
                    <a href="scan.html" class="py-3 flex flex-col items-center text-gray-500">
                        <i class="fas fa-qrcode text-xl"></i>
                        <span class="text-xs mt-1">扫码</span>
                    </a>
                    <a href="worklist.html" class="py-3 flex flex-col items-center text-gray-500">
                        <i class="fas fa-list-ul text-xl"></i>
                        <span class="text-xs mt-1">列表</span>
                    </a>
                </div>
            </footer>
        </div>
    </div>
</body>
</html> 