package com.xiao.temu.modules.purchaseorderv.constant;

/**
 * 操作类型枚举
 */
public enum OperationTypeEnum {

    /**
     * 完成
     */
    COMPLETE("1", "完成"),

    /**
     * 撤销
     */
    CANCEL("2", "撤销");

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型标题
     */
    private final String title;

    OperationTypeEnum(String code, String title) {
        this.code = code;
        this.title = title;
    }

    public String getCode() {
        return code;
    }

    public String getTitle() {
        return title;
    }

    /**
     * 根据编码获取枚举
     */
    public static OperationTypeEnum getByCode(String code) {
        for (OperationTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 