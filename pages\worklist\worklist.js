/**
 * 工作记录页
 */
const worklistApi = require('../../api/worklistApi');
const productionProgressApi = require('../../api/productionProgressApi');
const progressConfig = require('../../utils/progressConfig');
const pageRefreshUtils = require('../../utils/pageRefreshUtils');

// 定义页面路径常量
const PAGE_PATH = 'pages/worklist/worklist';

Page({
  data: {
    userInfo: {},
    recordList: [],
    loading: false,
    // 工序类型映射
    progressTypeMap: progressConfig.PROGRESS_TYPE_MAP
  },

  onLoad() {
    // 记录页面访问
    pageRefreshUtils.recordPageVisit(PAGE_PATH);
    
    this.loadUserInfo();
    // 实际API调用
    this.loadList();
  },

  onShow() {
    // 记录页面访问
    pageRefreshUtils.recordPageVisit(PAGE_PATH);
    
    // 检查页面是否需要刷新
    const needRefresh = pageRefreshUtils.needRefresh(PAGE_PATH) || 
                        pageRefreshUtils.isMarkedForRefresh(PAGE_PATH);
                        
    if (needRefresh) {
      console.log('工作列表页面需要刷新数据');
      this.loadList();
      pageRefreshUtils.clearRefreshMark(PAGE_PATH);
    }
  },
  
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    // 如果从工作列表页前往扫码页，标记扫码页需要刷新
    pageRefreshUtils.markPageForRefresh('pages/scan/scan');
  },

  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({ userInfo });
    } else {
      // 如果没有存储的用户信息，使用模拟数据
      const mockUserInfo = {
        workerId: 'GH001',
        userName: '张师傅',
        roleName: '烧花工',
        department: '生产部'
      };
      this.setData({ userInfo: mockUserInfo });
    }
  },
  
  // 获取工序中文名称
  getProgressTypeName(progressType) {
    return this.data.progressTypeMap[progressType] || progressType;
  },
  
  // 格式化时间为年-月-日 时:分:秒
  formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';
    
    // 处理ISO格式日期
    if (dateTimeStr.includes('T') && dateTimeStr.includes('+')) {
      const date = new Date(dateTimeStr);
      return date.getFullYear() + '-' + 
        this.padZero(date.getMonth() + 1) + '-' + 
        this.padZero(date.getDate()) + ' ' + 
        this.padZero(date.getHours()) + ':' + 
        this.padZero(date.getMinutes()) + ':' + 
        this.padZero(date.getSeconds());
    }
    
    // 如果已经是标准格式，直接返回
    return dateTimeStr;
  },
  
  // 数字补零
  padZero(num) {
    return num < 10 ? '0' + num : num;
  },

  // 加载模拟数据（备用）
  loadMockData() {
    this.setData({ loading: true });
    
    // 模拟操作记录
    const mockRecordList = [
      {
        orderNumber: 'TM20230616003',
        subPurchaseOrderSn: 'WB2505071696183',
        shopId: 6,
        progressType: 'burning',
        operationTime: '2025-05-23T02:17:21.000+00:00',
        status: 'pending'
      },
      {
        orderNumber: 'TM20230616002',
        subPurchaseOrderSn: 'WB2505071696182',
        shopId: 6,
        progressType: 'sewing',
        operationTime: '2025-05-14T08:02:38.000+00:00',
        status: 'pending'
      },
      {
        orderNumber: 'TM20230616001',
        subPurchaseOrderSn: 'WB2505071696181',
        shopId: 6,
        progressType: 'delivery',
        operationTime: '2025-05-14T08:02:38.000+00:00',
        status: 'pending'
      }
    ];
    
    // 按照操作时间倒序排序并格式化时间
    setTimeout(() => {
      const recordList = mockRecordList.sort((a, b) => {
        // 按照时间倒序排列
        return new Date(b.operationTime) - new Date(a.operationTime);
      }).map(item => {
        // 格式化时间和工序名称
        return {
          ...item,
          operationTime: this.formatDateTime(item.operationTime),
          progressTypeName: this.getProgressTypeName(item.progressType)
        };
      });
      
      this.setData({ 
        recordList,
        loading: false 
      });
    }, 500); // 模拟网络延迟
  },

  // 实际获取数据的方法
  loadList() {
    this.setData({ loading: true });
    
    // 获取用户操作记录
    worklistApi.getOperationRecords()
      .then(recordList => {
        if (!recordList || recordList.length === 0) {
          // 如果API没有返回数据，使用模拟数据
          this.loadMockData();
          return;
        }
        
        // 处理API返回的数据，添加状态标识并格式化时间
        const processedRecords = (recordList || []).map(item => {
          return {
            ...item,
            operationTime: this.formatDateTime(item.operationTime),
            progressTypeName: this.getProgressTypeName(item.progressType),
            status: item.operationType === '待处理' ? 'pending' : 'completed'
          };
        });
        
        this.setData({ recordList: processedRecords, loading: false });
      })
      .catch(err => {
        console.error('获取工作记录失败:', err);
        // 出错时显示提示并使用模拟数据
        wx.showToast({
          title: '获取记录失败',
          icon: 'none'
        });
        
        // 使用模拟数据
        this.loadMockData();
      });
  },

  goToProgress(e) {
    const item = e.currentTarget.dataset.item;
    wx.navigateTo({
      url: `/pages/progress/progress?subPurchaseOrderSn=${item.subPurchaseOrderSn}&shopId=${item.shopId}`
    });
  },
  
  goToHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },
  
  goToScan() {
    wx.switchTab({
      url: '/pages/scan/scan'
    });
  }
}) 