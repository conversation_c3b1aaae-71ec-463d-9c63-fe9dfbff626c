package com.xiao.temu.modules.quality.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.quality.dto.LocalQualityInspectionRequestDTO;
import com.xiao.temu.modules.quality.vo.LocalQualityInspectionVO;
import com.xiao.temu.modules.quality.vo.LocalQualityInspectionVO.LocalQualityInspectionItemVO;
import com.xiao.temu.modules.quality.mapper.LocalQualityInspectionMapper;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.system.service.UserService;
import com.xiao.temu.modules.quality.service.LocalQualityInspectionService;
import com.xiao.temu.infrastructure.storage.CosStorageService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 本地抽检结果明细服务实现类
 */
@Slf4j
@Service
public class LocalQualityInspectionServiceImpl implements LocalQualityInspectionService {

    @Autowired
    private LocalQualityInspectionMapper localQualityInspectionMapper;
    
    @Autowired
    private ShopService shopService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private CosStorageService cosStorageService;

    @Override
    public LocalQualityInspectionVO getLocalQualityInspectionList(LocalQualityInspectionRequestDTO requestDTO, Long userId) {
        log.info("查询本地抽检结果明细, 请求参数: {}, 用户ID: {}", requestDTO, userId);
        
        LocalQualityInspectionVO response = new LocalQualityInspectionVO();
        
        try {
            // 验证店铺权限
            List<Long> shopIds = requestDTO.getShopIds();
            if (shopIds == null || shopIds.isEmpty()) {
                throw new RuntimeException("店铺ID不能为空");
            }
            
            // 检查用户是否是管理员
            boolean isAdmin = userService.isAdmin(userId);
            // 检查是否忽略权限检查
            boolean ignorePermissionCheck = Boolean.TRUE.equals(requestDTO.getIgnorePermissionCheck());
            
            // 如果不是管理员且不忽略权限检查，需要验证店铺权限
            List<Shop> validShops = new ArrayList<>();
            if (!isAdmin && !ignorePermissionCheck) {
                for (Long shopId : shopIds) {
                    // 非管理员需要检查店铺权限
                    boolean hasPermission = shopService.checkShopPermission(userId, shopId, false);
                    if (!hasPermission) {
                        log.warn("用户 {} 没有权限访问店铺 {}", userId, shopId);
                        continue; // 跳过无权限的店铺
                    }
                    
                    // 查询店铺信息
                    Shop shop = shopService.getShopById(shopId).convertToShop();
                    if (shop != null) {
                        validShops.add(shop);
                    } else {
                        log.warn("店铺不存在: {}", shopId);
                    }
                }
                
                // 如果没有有效的店铺，则返回空结果
                if (validShops.isEmpty()) {
                    throw new RuntimeException("没有权限访问所选店铺或店铺不存在");
                }
                
                // 获取有效的店铺ID列表
                List<Long> validShopIds = validShops.stream()
                        .map(Shop::getShopId)
                        .collect(Collectors.toList());
                
                // 更新请求中的shopIds
                requestDTO.setShopIds(validShopIds);
            } else {
                // 管理员或忽略权限检查，可以查看所有店铺
                for (Long shopId : shopIds) {
                    Shop shop = shopService.getShopById(shopId).convertToShop();
                    if (shop != null) {
                        validShops.add(shop);
                    }
                }
            }
            
            // 获取有效的店铺ID列表
            List<Long> validShopIds = validShops.stream()
                    .map(Shop::getShopId)
                    .collect(Collectors.toList());
            
            // 设置分页参数
            Integer pageNo = requestDTO.getPageNo() != null ? requestDTO.getPageNo() : 1;
            Integer pageSize = requestDTO.getPageSize() != null ? requestDTO.getPageSize() : 10;
            Page<LocalQualityInspectionItemVO> page = new Page<>(pageNo, pageSize);
            
            // 查询数据 - 使用validShopIds而不是单个shopId
            IPage<LocalQualityInspectionItemVO> resultPage = localQualityInspectionMapper.selectMultiShopQualityInspectionPage(
                    page,
                    validShopIds,
                    requestDTO.getQcResult(),
                    requestDTO.getPurchaseNo(),
                    requestDTO.getSkuIdList(),
                    requestDTO.getSkcIdList(),
                    requestDTO.getQcResultUpdateTimeBegin(),
                    requestDTO.getQcResultUpdateTimeEnd(),
                    requestDTO.getSkuNameKeyword()
            );
            
            // 处理返回数据，转换疵点图片URL
            List<LocalQualityInspectionItemVO> processedItems = new ArrayList<>();
            if (resultPage.getRecords() != null) {
                processedItems = resultPage.getRecords().stream()
                        .map(item -> {
                            // 处理疵点图片URL
                            if (item.getAttachments() != null && !item.getAttachments().isEmpty()) {
                                List<String> processedAttachments = new ArrayList<>();
                                for (String attachment : item.getAttachments()) {
                                    try {
                                        // 尝试解析为JSON对象
                                        JSONObject attachmentJson = JSON.parseObject(attachment);
                                        if (attachmentJson.containsKey("imageKey")) {
                                            String imageKey = attachmentJson.getString("imageKey");
                                            // 使用CosStorageService生成URL，设置24小时过期
                                            String imageUrl = cosStorageService.generateImageUrl(imageKey, 24);
                                            processedAttachments.add(imageUrl);
                                        } else {
                                            // 如果不是标准格式，直接添加原始值
                                            processedAttachments.add(attachment);
                                        }
                                    } catch (Exception e) {
                                        log.warn("处理疵点图片URL失败: {}", attachment, e);
                                        // 解析失败时保留原始值
                                        processedAttachments.add(attachment);
                                    }
                                }
                                // 设置处理后的图片URLs
                                item.setAttachments(processedAttachments);
                            }
                            
                            // 为每个结果项设置店铺名称（如果没有）
                            if (item.getShopName() == null && item.getShopId() != null) {
                                for (Shop shop : validShops) {
                                    if (shop.getShopId().equals(item.getShopId())) {
                                        item.setShopName(shop.getShopName());
                                        item.setShopRemark(shop.getRemark());
                                        break;
                                    }
                                }
                            }
                            
                            return item;
                        })
                        .collect(Collectors.toList());
            }
            
            // 构建返回结果
            response.setTotal(resultPage.getTotal());
            response.setPageNo(pageNo);
            response.setPageSize(pageSize);
            response.setItems(processedItems);
            
            // 多店铺查询时不设置单个店铺信息
            response.setShopId(null);
            response.setShopName(null);
            
            log.info("查询本地抽检结果明细成功, 共查询到 {} 条记录", response.getTotal());
            
        } catch (Exception e) {
            log.error("查询本地抽检结果明细失败", e);
            throw new RuntimeException("查询抽检结果明细失败: " + e.getMessage());
        }
        
        return response;
    }
} 