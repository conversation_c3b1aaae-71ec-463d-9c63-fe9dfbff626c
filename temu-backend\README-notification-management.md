# 备货单通知测试与管理模块

## 功能概述

备货单通知测试与管理模块用于管理和测试备货单的四种通知功能：
1. JIT即将逾期通知(到货前24小时)
2. JIT已逾期通知(逾期后连续3天)
3. 普通备货-发货提醒(创建5天后未发货，连续提醒3天)
4. 普通备货-未到货通知(发货5天后未收货，连续提醒3天)

本模块提供以下功能：
- 通知配置管理：可配置通知参数（触发条件、通知次数等）
- 通知记录查询：查询已发送的通知记录
- 通知测试工具：预览符合条件的订单并手动触发通知

## 技术实现

### 数据库表

```sql
CREATE TABLE `purchase_order_notification_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `notification_type` int NOT NULL COMMENT '通知类型(1:JIT即将逾期 2:JIT已逾期 3:普通备货未发货 4:普通备货未到货)',
  `notification_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知名称',
  `enabled` int NOT NULL DEFAULT 1 COMMENT '是否启用(0:禁用 1:启用)',
  `check_interval` int DEFAULT 24 COMMENT '检查间隔(小时)',
  `normal_trigger_days` int DEFAULT NULL COMMENT '普通备货触发天数',
  `jit_trigger_hours` int DEFAULT NULL COMMENT 'JIT备货触发小时数',
  `max_notify_count` int NOT NULL DEFAULT 3 COMMENT '最大通知次数',
  `notify_interval_hours` int DEFAULT 24 COMMENT '通知间隔(小时)',
  `template_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息模板代码',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_notification_type`(`notification_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '备货单通知配置表' ROW_FORMAT = DYNAMIC;
```

### 主要类

1. 实体类：
   - `PurchaseOrderNotificationConfig`：通知配置实体类

2. DTO类：
   - `NotificationQueryDTO`：通知查询DTO
   - `NotificationMatchDTO`：通知匹配查询DTO
   - `NotificationTriggerDTO`：通知触发DTO

3. VO类：
   - `NotificationConfigVO`：通知配置VO
   - `NotificationRecordVO`：通知记录VO
   - `NotificationMatchOrderVO`：通知匹配订单VO

4. Mapper接口：
   - `PurchaseOrderNotificationConfigMapper`：通知配置数据访问接口

5. 服务接口：
   - `PurchaseOrderNotificationConfigService`：通知配置服务接口
   - `PurchaseOrderNotificationService`：通知服务接口（增加单个订单处理方法）

6. 控制器：
   - `PurchaseOrderNotificationConfigController`：通知配置控制器
   - `PurchaseOrderNotificationManageController`：通知管理控制器

## 接口说明

### 通知配置管理接口

1. 获取所有通知配置
   - 请求方式: GET
   - 请求路径: `/api/v1/purchase-order/notification/config/list`

2. 获取单个通知配置
   - 请求方式: GET
   - 请求路径: `/api/v1/purchase-order/notification/config/{id}`

3. 更新通知配置
   - 请求方式: PUT
   - 请求路径: `/api/v1/purchase-order/notification/config/update`

4. 启用/禁用通知配置
   - 请求方式: PUT
   - 请求路径: `/api/v1/purchase-order/notification/config/toggle/{id}/{enabled}`

### 通知记录查询接口

1. 获取通知记录列表
   - 请求方式: POST
   - 请求路径: `/api/v1/purchase-order/notification/record/list`

### 通知测试工具接口

1. 获取通知类型列表
   - 请求方式: GET
   - 请求路径: `/api/v1/purchase-order/notification-manage/notification-types`

2. 获取通知配置信息
   - 请求方式: GET
   - 请求路径: `/api/v1/purchase-order/notification-manage/notification-config/{notificationType}`

3. 预览匹配订单
   - 请求方式: POST
   - 请求路径: `/api/v1/purchase-order/notification-manage/match-orders`

4. 手动发送通知
   - 请求方式: POST
   - 请求路径: `/api/v1/purchase-order/notification-manage/send-notification`

5. 测试特定通知类型的匹配逻辑
   - 请求方式: POST
   - 请求路径: `/api/v1/purchase-order/notification-manage/test-match/{notificationType}`

## 导入说明

1. 导入SQL脚本
   - `temu-backend/src/main/resources/db/temu_api_notification_config.sql`：创建通知配置表
   - `temu-backend/src/main/resources/db/notification_menu.sql`：添加菜单配置

2. 重启应用，访问系统即可看到新增的菜单项：
   - 备货单管理 → 通知配置管理
   - 备货单管理 → 通知记录查询
   - 备货单管理 → 通知测试工具 