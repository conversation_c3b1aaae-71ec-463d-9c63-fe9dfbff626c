<template>
  <div class="dashboard-container">
    <el-card class="welcome-card">
      <div class="welcome-content">
        <h1>欢迎使用TEMU管理系统</h1>
        <p>您可以在此查看店铺上新及销售数据统计</p>
      </div>
    </el-card>
    
    <el-card class="stats-card">
      <div class="stats-header">
        <h2>店铺数据统计</h2>
        <div class="stats-actions">
          <el-select
            v-model="selectedShopIds"
            multiple
            collapse-tags
            placeholder="选择店铺"
            style="width: 300px; margin-right: 16px;"
          >
            <el-option
              v-for="shop in shopOptions"
              :key="shop.value"
              :label="shop.label"
              :value="shop.value"
            />
          </el-select>
          <el-button type="primary" @click="refreshData">刷新数据</el-button>
        </div>
      </div>
      
      <el-table 
        :data="paginatedData" 
        border 
        stripe 
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <!-- 店铺信息 -->
        <el-table-column label="店铺信息" align="center">
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="groupNames" label="所属运营组" min-width="150" align="center" :show-overflow-tooltip="true" sortable="custom" />
          <el-table-column prop="shopName" label="店铺名称" min-width="120" align="center" sortable="custom" />
          <el-table-column prop="shopRemark" label="备注" min-width="120" align="center" sortable="custom" />
        </el-table-column>
        
        <!-- 上新数据 -->
        <el-table-column label="上新数据" align="center">
          <el-table-column prop="currentMonthNewCount" label="本月上新" width="90" align="right" sortable="custom" />
          <el-table-column prop="currentWeekNewCount" label="本周上新" width="90" align="right" sortable="custom" />
          <el-table-column prop="lastWeekNewCount" label="上周上新" width="90" align="right" sortable="custom" />
          <el-table-column prop="yesterdayNewCount" label="昨日上新" width="90" align="right" sortable="custom" />
          <el-table-column prop="currentWeekOnlineCount" label="本周上架" width="90" align="right" sortable="custom" />
        </el-table-column>
        
        <!-- 销售数据 -->
        <el-table-column label="销售数据" align="center">
          <el-table-column prop="todaySales" label="今日销量" width="90" align="right" sortable="custom" />
          <el-table-column prop="lastWeekSales" label="7天销量" width="90" align="right" sortable="custom" />
          <el-table-column prop="lastMonthSales" label="30天销量" width="90" align="right" sortable="custom" />
        </el-table-column>
      </el-table>
      
      <div class="stats-pagination">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next"
          :total="totalItems"
          :page-size="pageSize"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { getProductNewArrivalStats } from '@/api/product/new-arrival';
import type { ShopStats } from '@/api/product/new-arrival';

// 分页参数
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);

// 店铺选择
const selectedShopIds = ref<number[]>([]);
const shopOptions = ref<{ value: number; label: string }[]>([]);

// 排序参数
const sortConfig = ref({
  prop: '',
  order: ''
});

// 店铺统计数据
const shopStatsData = ref<ShopStats[]>([]);

// 计算分页后的数据
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return shopStatsData.value.slice(start, end);
});

// 查询店铺上新统计数据
const fetchShopNewArrivalStats = async () => {
  try {
    const response = await getProductNewArrivalStats({
      shopIds: selectedShopIds.value.length > 0 ? selectedShopIds.value : undefined,
      orderByColumn: sortConfig.value.prop || undefined,
      orderByType: sortConfig.value.order === 'descending' ? 'desc' : 
                   sortConfig.value.order === 'ascending' ? 'asc' : undefined
    });
    
    if (response.code === 200) {
      const data = response.data || [];
      shopStatsData.value = data;
      totalItems.value = data.length;
      
      // 更新店铺下拉选项
      updateShopOptions(data);
    } else {
      ElMessage.error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取店铺上新统计数据失败:', error);
    ElMessage.error('获取数据失败，请检查网络或联系管理员');
  }
};

// 处理排序事件
const handleSortChange = (event: any) => {
  if (event.column && event.prop) {
    sortConfig.value.prop = event.prop;
    sortConfig.value.order = event.order;
    currentPage.value = 1; // 重置到第一页
    fetchShopNewArrivalStats();
  } else {
    sortConfig.value.prop = '';
    sortConfig.value.order = '';
  }
};

// 更新店铺选项
const updateShopOptions = (data: ShopStats[]) => {
  // 只在初次加载或选项为空时更新
  if (shopOptions.value.length === 0) {
    const options = data.map(item => ({
      value: item.shopId,
      label: item.shopName + (item.shopRemark ? `(${item.shopRemark})` : '')
    }));
    shopOptions.value = options;
  }
};

// 刷新数据
const refreshData = () => {
  currentPage.value = 1; // 重置到第一页
  fetchShopNewArrivalStats();
  ElMessage.success('数据已刷新');
};

// 分页切换
const handlePageChange = (page: number) => {
  currentPage.value = page;
};

// 修改每页显示数量
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1; // 重置到第一页
};

// 页面加载后获取数据
onMounted(() => {
  fetchShopNewArrivalStats();
});
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 16px;
  max-width: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  .welcome-card {
    width: 100%;
    transition: all 0.3s;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .welcome-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    
    h1 {
      font-size: 24px;
      margin-bottom: 16px;
      color: #303133;
    }
    
    p {
      font-size: 14px;
      color: #606266;
    }
  }
  
  .stats-card {
    width: 100%;
    
    .stats-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h2 {
        font-size: 18px;
        margin: 0;
      }
      
      .stats-actions {
        display: flex;
        align-items: center;
      }
    }
    
    .stats-pagination {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style> 