<template>
  <AppLayout class="purchase-order-layout">
    <!-- 表格内容区域 -->
    <template #table>
      <!-- 标签页区域 -->
      <el-tabs v-model="activeTab" @tab-click="handleTabChange" class="purchase-tabs">
        <el-tab-pane label="紧急备货建议" name="urgent">
          <UrgentPurchaseTab 
            :shops="shops"
            @preview-image="handlePreviewImage"
            @view-detail="handleViewDetail"
          />
        </el-tab-pane>

        <el-tab-pane label="普通备货建议" name="normal">
          <NormalPurchaseTab 
            :shops="shops"
            @preview-image="handlePreviewImage"
            @view-detail="handleViewDetail"
          />
        </el-tab-pane>
      </el-tabs>
    </template>
  
    <!-- 弹窗区域 -->
    <template #dialogs>
      <ImagePreview
        v-model:visible="imagePreviewVisible"
        :image-url="currentPreviewImage"
      />
    </template>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { usePurchaseOrderStore } from '@/store'
import type { Shop, PurchaseOrder } from '@/types/purchaseOrder'
import { AppLayout, ImagePreview } from '@/components/temu'
import UrgentPurchaseTab from './UrgentPurchaseTab.vue'
import NormalPurchaseTab from './NormalPurchaseTab.vue'

// 备货单store
const purchaseOrderStore = usePurchaseOrderStore()

// 当前激活的标签页
const activeTab = ref('urgent')

// 店铺列表
const shops = ref<Shop[]>([])

// 图片预览相关
const imagePreviewVisible = ref(false)
const currentPreviewImage = ref('')

// 处理标签页切换
const handleTabChange = () => {
  // 仅在标签页改变时触发，不需要立即加载数据
  // 子组件会自行处理数据加载
}

// 图片预览
const handlePreviewImage = (url: string) => {
  currentPreviewImage.value = url
  imagePreviewVisible.value = true
}

// 查看详情
const handleViewDetail = (row: PurchaseOrder) => {
  ElMessage.info('查看备货单详情功能待开发')
  console.log('查看备货单详情:', row)
}

// 获取店铺列表
const fetchShops = async () => {
  try {
    const shopList = await purchaseOrderStore.getShopList()
    shops.value = shopList || []
    
    if (shops.value.length === 0) {
      ElMessage.warning('获取店铺列表为空，请检查您是否有店铺权限')
    }
  } catch (error) {
    console.error('组件获取店铺列表失败')
    ElMessage.error('获取店铺信息失败，请刷新页面重试')
    shops.value = []
  }
}

// 组件挂载时初始化数据
onMounted(() => {
  fetchShops()
})
</script>

<style scoped>
.purchase-order-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.purchase-order-layout :deep(.app-layout-content) {
  overflow: auto;
  height: 100%;
  flex: 1;
}

.purchase-tabs {
  margin-top: 15px;
}

.purchase-tabs :deep(.el-tabs__header) {
  margin-bottom: 15px;
}

.purchase-tabs :deep(.el-tabs__nav) {
  border-radius: 4px;
}

.purchase-tabs :deep(.el-tab-pane) {
  height: 100%;
  overflow: visible;
}
</style> 