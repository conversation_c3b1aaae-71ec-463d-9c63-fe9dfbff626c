import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { setupPermission } from './directives/permission'
import { getToken } from './utils/auth'
import { useUserStore, usePermissionStore } from './store'
import './permission' // 权限控制
import './style.css'
import './assets/styles/index.scss' // 全局样式
import './assets/styles/tags-view.scss' // 标签页样式

const app = createApp(App)
const pinia = createPinia()
app.use(pinia)

// 初始化pinia
const initStore = async () => {
  try {
    // 如果有token，预加载用户信息和菜单
    const token = getToken()
    if (token) {
      // 移除这里的用户信息和菜单加载，避免与permission.ts中的加载重复
      // 用户信息和路由将只在router守卫中加载一次
    }
  } catch (e) {
    // 初始化Store失败
  }
}

// 初始化存储
initStore()

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(router)
app.use(ElementPlus)

// 注册权限指令
setupPermission(app)

app.mount('#app')
