<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.purchaseorderv.mapper.PurchaseOrderNotificationMapper">

    <resultMap id="BaseResultMap" type="com.xiao.temu.modules.purchaseorderv.entity.PurchaseOrderNotification">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="sub_purchase_order_sn" property="subPurchaseOrderSn"/>
        <result column="notification_type" property="notificationType"/>
        <result column="notify_count" property="notifyCount"/>
        <result column="last_notify_time" property="lastNotifyTime"/>
        <result column="notify_status" property="notifyStatus"/>
        <result column="group_id" property="groupId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="selectPurchaseOrderNotificationVo">
        SELECT id, shop_id, sub_purchase_order_sn, notification_type, notify_count, last_notify_time,
               notify_status, group_id, create_time, update_time
        FROM purchase_order_notification
    </sql>

    <select id="getByShopIdAndOrderSn" resultMap="BaseResultMap">
        <include refid="selectPurchaseOrderNotificationVo"/>
        WHERE shop_id = #{shopId}
        AND sub_purchase_order_sn = #{subPurchaseOrderSn}
        AND notification_type = #{notificationType}
    </select>

    <select id="findNeedToContinueNotify" resultMap="BaseResultMap">
        <include refid="selectPurchaseOrderNotificationVo"/>
        WHERE notification_type = #{notificationType}
        AND notify_count &lt; #{maxNotifyCount}
        AND notify_status != 2
        ORDER BY last_notify_time ASC
    </select>

    <select id="countByShopIdAndType" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM purchase_order_notification
        WHERE shop_id = #{shopId} AND notification_type = #{notificationType}
    </select>

    <delete id="deleteByShopId">
        DELETE FROM purchase_order_notification
        WHERE shop_id = #{shopId}
    </delete>
</mapper> 