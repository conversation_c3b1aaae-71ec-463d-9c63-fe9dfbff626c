import { defineStore } from 'pinia'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { login, logout, getUserInfo } from '@/api/user'
import { resetRoutersCache } from '@/api/menu'

interface UserState {
  token: string | null
  tokenType: string
  expiration: number | null
  userId: number | null
  name: string
  avatar: string
  nickName: string
  roles: string[]
  permissions: string[]
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: getToken(),
    tokenType: 'Bearer',
    expiration: null,
    userId: null,
    name: '',
    avatar: '',
    nickName: '',
    roles: [],
    permissions: []
  }),
  actions: {
    // 登录
    async login(userInfo: { username: string; password: string; rememberMe: boolean }) {
      const { username, password, rememberMe } = userInfo
      try {
        const res = await login(username.trim(), password, rememberMe)
        const { token, tokenType, expiration, userId, username: name, nickName, roles, permissions } = res.data
        
        // 保存token到localStorage
        setToken(token)
        
        // 更新状态
        this.token = token
        this.tokenType = tokenType || 'Bearer'
        this.expiration = expiration
        this.userId = userId
        this.name = name
        this.nickName = nickName
        this.roles = roles || []
        this.permissions = permissions || []
        
        return Promise.resolve()
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 获取用户信息
    async getInfo() {
      try {
        const res = await getUserInfo()
        
        const { userId, username, nickName, avatar, roles, permissions } = res.data
        
        // 验证返回的roles是否是一个非空数组
        if (!roles || roles.length === 0) {
          throw new Error('用户角色不能为空')
        }
        
        this.userId = userId
        this.name = username || nickName
        this.nickName = nickName
        this.avatar = avatar || ''
        this.roles = roles
        this.permissions = permissions || []
        
        return Promise.resolve(res.data)
      } catch (error) {
        this.resetToken() // 出错时自动清除token
        return Promise.reject(error)
      }
    },
    
    // 退出登录
    async logout() {
      try {
        await logout()
        this.token = null
        this.roles = []
        this.permissions = []
        removeToken()
        // 重置菜单路由缓存
        resetRoutersCache()
        return Promise.resolve()
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 重置token
    resetToken() {
      this.token = null
      this.tokenType = 'Bearer'
      this.expiration = null
      this.userId = null
      this.name = ''
      this.nickName = ''
      this.avatar = ''
      this.roles = []
      this.permissions = []
      removeToken()
      // 重置菜单路由缓存
      resetRoutersCache()
    },
    
    // 更新用户信息
    updateUserInfo(userInfo: Partial<UserState>) {
      if (userInfo.name) this.name = userInfo.name
      if (userInfo.nickName) this.nickName = userInfo.nickName
      if (userInfo.avatar) this.avatar = userInfo.avatar
    }
  }
}) 