/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : temu_api

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 03/06/2025 15:41:56
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for purchase_order_v
-- ----------------------------
DROP TABLE IF EXISTS `purchase_order_v`;
CREATE TABLE `purchase_order_v`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint NOT NULL COMMENT '店铺ID',
  `sub_purchase_order_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '备货单号',
  `original_purchase_order_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原始采购单号',
  `product_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `product_id` bigint NULL DEFAULT NULL COMMENT '商品ID',
  `product_skc_id` bigint NULL DEFAULT NULL COMMENT '商品SKC ID',
  `supplier_id` bigint NOT NULL COMMENT '供应商ID',
  `supplier_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商名称',
  `purchase_time` datetime NULL DEFAULT NULL COMMENT '备货单创建时间',
  `deliver_time` datetime NULL DEFAULT NULL COMMENT '发货时间',
  `delivery_order_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发货单号',
  `expect_latest_deliver_time` datetime NULL DEFAULT NULL COMMENT '预计最晚发货时间',
  `expect_latest_arrival_time` datetime NULL DEFAULT NULL COMMENT '预计最晚到货时间',
  `receive_warehouse_id` bigint NULL DEFAULT NULL COMMENT '收货仓库ID',
  `receive_warehouse_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收货仓库名称',
  `status` tinyint NULL DEFAULT NULL COMMENT '备货单状态(0-待接单；1-已接单，待发货；2-已送货；3-已收货；4-已拒收；5-已验收，全部退回；6-已验收；7-已入库；8-作废；9-已超时)',
  `purchase_stock_type` tinyint NULL DEFAULT 0 COMMENT '备货类型(0:普通备货 1:JIT备货)',
  `receive_time` datetime NULL DEFAULT NULL COMMENT '实际收货时间',
  `purchase_quantity` int NULL DEFAULT 0 COMMENT '采购数量',
  `deliver_quantity` int NULL DEFAULT 0 COMMENT '实际发货数量',
  `receive_quantity` int NULL DEFAULT 0 COMMENT '实际收货数量',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除(0:否 1:是)',
  `sync_time` datetime NOT NULL COMMENT '同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_order_sn`(`shop_id` ASC, `sub_purchase_order_sn` ASC) USING BTREE,
  INDEX `idx_shop_id`(`shop_id` ASC) USING BTREE,
  INDEX `idx_sub_purchase_order_sn`(`sub_purchase_order_sn` ASC) USING BTREE,
  INDEX `idx_supplier_id`(`supplier_id` ASC) USING BTREE,
  INDEX `idx_purchase_time`(`purchase_time` ASC) USING BTREE,
  INDEX `idx_deliver_time`(`deliver_time` ASC) USING BTREE,
  INDEX `idx_expect_latest_arrival_time`(`expect_latest_arrival_time` ASC) USING BTREE,
  INDEX `idx_status_type`(`status` ASC, `purchase_stock_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41476 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '备货单信息表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
