package com.xiao.temu.modules.shop.controller;

import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.common.response.PageResult;
import com.xiao.temu.modules.shop.dto.ShopDTO;
import com.xiao.temu.modules.shop.dto.ShopExportDTO;
import com.xiao.temu.modules.shop.dto.ShopQueryDTO;
import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.security.annotation.DataScope;
import com.xiao.temu.security.annotation.RequiresPermission;
import com.xiao.temu.security.exception.NotPermittedException;
import com.xiao.temu.security.utils.SecurityUtils;
import com.xiao.temu.modules.operation.service.GroupLeaderShopAssignmentService;
import com.xiao.temu.modules.operation.service.OperationGroupService;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.system.service.SysDataPermissionService;
import com.xiao.temu.infrastructure.excel.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 店铺管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/operation/shop")
public class ShopController {

    @Autowired
    private ShopService shopService;
    
    @Autowired
    private OperationGroupService groupService;
    
    @Autowired
    private GroupLeaderShopAssignmentService assignmentService;

    @Autowired
    private SysDataPermissionService dataPermissionService;

    /**
     * 分页查询店铺列表
     */
    @GetMapping("/list")
    @RequiresPermission("operation:shop:list")
    @DataScope(tableAlias = "sga", userIdColumn = "shop_id", groupIdColumn = "group_id")
    public ApiResponse<PageResult<ShopDTO>> list(ShopQueryDTO queryDTO) {
        // 超级管理员可查看所有店铺
        if (!SecurityUtils.isAdmin()) {
            Long userId = SecurityUtils.getCurrentUserId();
            
            // 获取用户的最高数据权限
            String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
            
            // 如果用户有全部数据权限，不进行额外限制
            if (!"2".equals(permissionType)) {
                // 对于本组数据权限或本人数据权限的用户，进行额外的权限控制
                
                // 判断是否为组长，如果是组长，设置其负责的运营组ID
                if (queryDTO.getGroupId() == null) {
                    List<Long> leaderGroupIds = groupService.getGroupIdsByLeaderId(userId);
                    if (!leaderGroupIds.isEmpty()) {
                        queryDTO.setGroupId(leaderGroupIds.get(0));
                    }
                } else {
                    // 验证是否有权限查看该组
                    boolean isGroupLeader = assignmentService.isGroupLeader(userId, queryDTO.getGroupId());
                    if (!isGroupLeader) {
                        return ApiResponse.error("无权查看该运营组的店铺");
                    }
                }
            }
        }
        
        PageResult<ShopDTO> pageResult = shopService.listShops(queryDTO);
        return ApiResponse.success(pageResult);
    }

    /**
     * 获取店铺详情
     */
    @GetMapping("/{shopId}")
    @RequiresPermission("operation:shop:query")
    public ApiResponse<ShopDTO> getInfo(@PathVariable Long shopId) {
        // 检查访问权限
        checkShopAccessPermission(shopId, false);
        
        ShopDTO shop = shopService.getShopById(shopId);
        // 此处不屏蔽敏感信息，因为详情页和编辑页需要显示这些信息
        return ApiResponse.success(shop);
    }

    /**
     * 新增店铺
     */
    @PostMapping
    @RequiresPermission("operation:shop:add")
    public ApiResponse<Boolean> add(@RequestBody Shop shop) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getCurrentUserId();
        
        // 检查是否为管理员
        boolean isAdmin = SecurityUtils.isAdmin();
        
        // 获取用户的最高数据权限
        String permissionType = dataPermissionService.getUserMaxDataPermission(currentUserId);
        boolean hasFullDataPermission = "2".equals(permissionType);
        
        // 运营组ID，默认为null
        Long groupId = null;
        
        // 如果不是管理员也没有全部数据权限，则需要设置所属运营组
        if (!isAdmin && !hasFullDataPermission) {
            // 获取用户所负责的运营组
            List<Long> leaderGroupIds = groupService.getGroupIdsByLeaderId(currentUserId);
            if (!leaderGroupIds.isEmpty()) {
                // 如果用户是运营组长，则店铺归属于他负责的第一个运营组
                groupId = leaderGroupIds.get(0);
            } else {
                // 如果不是运营组长，则没有权限添加店铺
                return ApiResponse.error("您没有权限添加店铺，只有管理员和运营组组长可以添加店铺");
            }
        }
        
        // 通过带有用户ID和运营组ID参数的addShop方法添加店铺
        boolean result = shopService.addShop(shop, currentUserId, groupId);
        
        return result ? ApiResponse.success() : ApiResponse.error("添加店铺失败");
    }

    /**
     * 修改店铺
     */
    @PutMapping
    @RequiresPermission("operation:shop:edit")
    public ApiResponse<Boolean> update(@RequestBody Shop shop) {
        // 检查操作权限
        checkShopAccessPermission(shop.getShopId(), true);
        
        boolean result = shopService.updateShop(shop);
        return result ? ApiResponse.success() : ApiResponse.error("修改店铺失败");
    }

    /**
     * 删除店铺
     */
    @DeleteMapping("/{shopId}")
    @RequiresPermission("operation:shop:remove")
    public ApiResponse<Boolean> remove(@PathVariable Long shopId) {
        // 检查操作权限
        checkShopAccessPermission(shopId, true);
        
        boolean result = shopService.deleteShop(shopId);
        return result ? ApiResponse.success() : ApiResponse.error("删除店铺失败");
    }

    /**
     * 获取当前用户的店铺列表（不分页）
     */
    @GetMapping("/user/shops")
    public ApiResponse<List<Shop>> getUserShops() {
        // 从工具类获取当前登录用户ID
        Long currentUserId = SecurityUtils.getCurrentUserId();
        
        // 根据用户角色获取不同范围的店铺
        List<Shop> shops;
        
        if (SecurityUtils.isAdmin()) {
            // 管理员可以查看所有店铺
            ShopQueryDTO queryDTO = new ShopQueryDTO();
            queryDTO.setPageSize(Integer.MAX_VALUE); // 不分页
            List<ShopDTO> shopDTOs = shopService.listShops(queryDTO).getList();
            shops = new ArrayList<>();
            
            // 手动转换DTO为实体对象
            for (ShopDTO dto : shopDTOs) {
                Shop shop = new Shop();
                shop.setShopId(dto.getShopId());
                shop.setShopName(dto.getShopName());
                shop.setShopTemuId(dto.getShopTemuId());
                shop.setStatus(dto.getStatus());
                shops.add(shop);
            }
        } else {
            // 获取用户的最高数据权限
            String permissionType = dataPermissionService.getUserMaxDataPermission(currentUserId);
            
            if ("2".equals(permissionType)) {
                // 全部数据权限，可以查看所有店铺
                ShopQueryDTO queryDTO = new ShopQueryDTO();
                queryDTO.setPageSize(Integer.MAX_VALUE); // 不分页
                List<ShopDTO> shopDTOs = shopService.listShops(queryDTO).getList();
                shops = new ArrayList<>();
                
                // 手动转换DTO为实体对象
                for (ShopDTO dto : shopDTOs) {
                    Shop shop = new Shop();
                    shop.setShopId(dto.getShopId());
                    shop.setShopName(dto.getShopName());
                    shop.setShopTemuId(dto.getShopTemuId());
                    shop.setStatus(dto.getStatus());
                    shops.add(shop);
                }
            } else if ("1".equals(permissionType)) {
                // 本组数据权限
                // 检查用户是否是某个运营组的组长
                boolean isLeader = groupService.isUserGroupLeader(currentUserId);
                
                if (isLeader) {
                    // 组长可以查看其负责的运营组下的所有店铺
                    shops = shopService.listLeaderShops(currentUserId);
                } else {
                    // 普通组员只能查看所在组的店铺
                    shops = shopService.listMemberShops(currentUserId);
                }
            } else {
                // 本人数据权限，只能查看被分配的店铺
                shops = shopService.listUserShops(currentUserId);
            }
            
            // 屏蔽敏感信息
            for (Shop shop : shops) {
                shop.setAccessToken(null);
                shop.setApiKey(null);
                shop.setApiSecret(null);
            }
        }
        
        return ApiResponse.success(shops);
    }

    /**
     * 获取运营组下的店铺列表（不分页）
     */
    @GetMapping("/group/{groupId}")
    @RequiresPermission("operation:group:query")
    public ApiResponse<List<Shop>> getGroupShops(@PathVariable Long groupId) {
        // 检查是否有权限访问此运营组
        if (!SecurityUtils.isAdmin()) {
            Long userId = SecurityUtils.getCurrentUserId();
            
            // 获取用户的最高数据权限
            String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
            
            // 如果用户没有全部数据权限，需要验证其是否有权限访问此运营组
            if (!"2".equals(permissionType)) {
                boolean hasPermission = assignmentService.isGroupLeader(userId, groupId) || 
                                        groupService.checkUserInGroup(userId, groupId);
                if (!hasPermission) {
                    return ApiResponse.error("无权查看该运营组的店铺");
                }
            }
        }
        
        List<Shop> shops = shopService.listGroupShops(groupId);
        
        // 屏蔽敏感信息
        for (Shop shop : shops) {
            shop.setAccessToken(null);
            shop.setApiKey(null);
            shop.setApiSecret(null);
        }
        
        return ApiResponse.success(shops);
    }
    
    /**
     * 获取当前用户对店铺的权限类型
     */
    @GetMapping("/permission/{shopId}")
    public ApiResponse<String> getShopPermission(@PathVariable Long shopId) {
        Long userId = SecurityUtils.getCurrentUserId();
        String permissionType = shopService.getUserShopPermissionType(userId, shopId);
        return ApiResponse.success(permissionType);
    }

    /**
     * 导出店铺数据
     */
    @PostMapping("/export")
    @RequiresPermission("operation:shop:export")
    public void export(@RequestBody Map<String, Object> params, HttpServletResponse response) throws IOException {
        String exportType = (String) params.get("exportType");
        String fileName = params.containsKey("fileName") ? (String) params.get("fileName") : "店铺数据";
        String sheetName = params.containsKey("sheetName") ? (String) params.get("sheetName") : "店铺列表";
        
        // 根据导出类型处理不同的导出逻辑
        List<ShopDTO> shopDTOList = new ArrayList<>();
        
        if ("all".equals(exportType)) {
            // 导出全部，获取查询条件
            @SuppressWarnings("unchecked")
            Map<String, Object> queryParamsMap = (Map<String, Object>) params.get("queryParams");
            ShopQueryDTO queryDTO = convertMapToShopQuery(queryParamsMap);
            queryDTO.setPageNum(1);
            queryDTO.setPageSize(Integer.MAX_VALUE); // 设置一个足够大的数值以获取所有数据
            
            // 执行查询
            PageResult<ShopDTO> pageResult = shopService.listShops(queryDTO);
            shopDTOList = pageResult.getList();
        } else if ("page".equals(exportType)) {
            // 导出当前页
            int pageNum = (int) params.get("pageNum");
            int pageSize = (int) params.get("pageSize");
            
            @SuppressWarnings("unchecked")
            Map<String, Object> queryParamsMap = (Map<String, Object>) params.get("queryParams");
            ShopQueryDTO queryDTO = convertMapToShopQuery(queryParamsMap);
            queryDTO.setPageNum(pageNum);
            queryDTO.setPageSize(pageSize);
            
            // 执行查询
            PageResult<ShopDTO> pageResult = shopService.listShops(queryDTO);
            shopDTOList = pageResult.getList();
        } else if ("selected".equals(exportType)) {
            // 导出选中项
            @SuppressWarnings("unchecked")
            List<Integer> selectedIds = (List<Integer>) params.get("selectedIds");
            for (Integer id : selectedIds) {
                ShopDTO shop = shopService.getShopById(id.longValue());
                if (shop != null) {
                    shopDTOList.add(shop);
                }
            }
        }
        
        // 转换为导出DTO
        List<ShopExportDTO> exportList = convertToExportDTO(shopDTOList);
        
        // 导出数据
        ExcelUtils.export(response, exportList, fileName, sheetName, ShopExportDTO.class);
    }
    
    /**
     * 将Map参数转换为ShopQueryDTO对象
     */
    private ShopQueryDTO convertMapToShopQuery(Map<String, Object> params) {
        ShopQueryDTO queryDTO = new ShopQueryDTO();
        if (params != null) {
            if (params.containsKey("shopName")) {
                queryDTO.setShopName((String) params.get("shopName"));
            }
            if (params.containsKey("shopTemuId")) {
                queryDTO.setShopTemuId((String) params.get("shopTemuId"));
            }
            if (params.containsKey("groupId") && params.get("groupId") != null) {
                queryDTO.setGroupId(Long.valueOf(params.get("groupId").toString()));
            }
            if (params.containsKey("status")) {
                queryDTO.setStatus((String) params.get("status"));
            }
            if (params.containsKey("pageNum")) {
                queryDTO.setPageNum((Integer) params.get("pageNum"));
            }
            if (params.containsKey("pageSize")) {
                queryDTO.setPageSize((Integer) params.get("pageSize"));
            }
        }
        return queryDTO;
    }
    
    /**
     * 将ShopDTO列表转换为ShopExportDTO列表
     */
    private List<ShopExportDTO> convertToExportDTO(List<ShopDTO> shopDTOList) {
        List<ShopExportDTO> exportList = new ArrayList<>();
        
        for (ShopDTO shopDTO : shopDTOList) {
            ShopExportDTO exportDTO = new ShopExportDTO();
            exportDTO.setShopId(shopDTO.getShopId());
            exportDTO.setShopName(shopDTO.getShopName());
            exportDTO.setShopTemuId(shopDTO.getShopTemuId());
            
            // 处理运营组名称，将List转为逗号分隔的字符串
            StringBuilder groupNames = new StringBuilder();
            if (shopDTO.getGroups() != null && !shopDTO.getGroups().isEmpty()) {
                for (int i = 0; i < shopDTO.getGroups().size(); i++) {
                    groupNames.append(shopDTO.getGroups().get(i).getGroupName());
                    if (i < shopDTO.getGroups().size() - 1) {
                        groupNames.append(", ");
                    }
                }
            }
            exportDTO.setGroupNames(groupNames.toString());
            
            // 处理状态描述
            exportDTO.setStatusDesc("0".equals(shopDTO.getStatus()) ? "正常" : "禁用");
            
            exportDTO.setCreateTime(shopDTO.getCreateTime());
            exportDTO.setUpdateTime(shopDTO.getUpdateTime());
            exportDTO.setRemark(shopDTO.getRemark());
            
            exportList.add(exportDTO);
        }
        
        return exportList;
    }
    
    /**
     * 检查用户是否有店铺访问权限
     *
     * @param shopId 店铺ID
     * @param requireWrite 是否需要写权限
     */
    private void checkShopAccessPermission(Long shopId, boolean requireWrite) {
        // 超级管理员跳过权限检查
        if (SecurityUtils.isAdmin()) {
            return;
        }
        
        // 获取当前用户ID
        Long userId = SecurityUtils.getCurrentUserId();
        
        // 获取用户的最高数据权限
        String permissionType = dataPermissionService.getUserMaxDataPermission(userId);
        
        // 如果用户有全部数据权限，直接通过
        if ("2".equals(permissionType)) {
            return;
        }
        
        // 对于需要写权限的操作，或者没有全部数据权限的用户，检查具体权限
        if (!shopService.checkShopPermission(userId, shopId, requireWrite)) {
            throw new NotPermittedException("您没有权限操作该店铺");
        }
    }
    
    /**
     * 获取店铺导入模板
     */
    @GetMapping("/importTemplate")
    @RequiresPermission("operation:shop:import")
    public void getImportTemplate(HttpServletResponse response) throws IOException {
        shopService.getImportTemplate(response);
    }
    
    /**
     * 导入店铺数据
     */
    @PostMapping("/import")
    @RequiresPermission("operation:shop:import")
    public ApiResponse<Map<String, Object>> importShops(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return ApiResponse.error("请选择要上传的文件");
        }
        
        try {
            // 导入Excel
            Map<String, Object> result = shopService.importShopsFromExcel(file.getInputStream());
            
            // 格式化结果消息
            Integer successCount = (Integer) result.get("successCount");
            Integer failCount = (Integer) result.get("failCount");
            List<String> errorMsgs = (List<String>) result.get("errorMsgs");
            
            StringBuilder message = new StringBuilder();
            message.append("导入成功: ").append(successCount).append("条; ");
            message.append("导入失败: ").append(failCount).append("条");
            
            // 如果存在错误信息，返回错误信息
            if (failCount > 0 && errorMsgs != null && !errorMsgs.isEmpty()) {
                result.put("message", message.toString());
                return ApiResponse.success(result);
            }
            
            return ApiResponse.success(message.toString());
        } catch (IOException e) {
            log.error("导入店铺数据出错", e);
            return ApiResponse.error("导入店铺数据出错: " + e.getMessage());
        }
    }

    /**
     * 批量删除店铺
     */
    @DeleteMapping("/batchDelete")
    @RequiresPermission("operation:shop:remove")
    public ApiResponse<Boolean> batchDelete(@RequestBody List<Long> shopIds) {
        if (shopIds == null || shopIds.isEmpty()) {
            return ApiResponse.error("未选择要删除的店铺");
        }
        
        // 检查每个店铺的操作权限
        for (Long shopId : shopIds) {
            checkShopAccessPermission(shopId, true);
        }
        
        boolean result = shopService.batchDeleteShops(shopIds);
        return result ? ApiResponse.success() : ApiResponse.error("批量删除店铺失败");
    }

    /**
     * 获取用户可访问的店铺列表（公共接口）
     * 管理员可获取所有店铺，普通用户只能查看被分配的店铺
     */
    @GetMapping("/accessible/shops")
    public ApiResponse<List<Shop>> getUserAccessibleShops() {
        // 获取当前登录用户ID
        Long currentUserId = SecurityUtils.getCurrentUserId();
        
        // 判断用户是否为管理员或拥有全部数据权限
        boolean isAdmin = SecurityUtils.isAdmin();
        
        // 获取用户的最高数据权限
        String permissionType = dataPermissionService.getUserMaxDataPermission(currentUserId);
        boolean hasFullDataPermission = "2".equals(permissionType);
        
        List<Shop> shops;
        
        // 如果是管理员或拥有全部数据权限，可以查看所有店铺
        if (isAdmin || hasFullDataPermission) {
            shops = shopService.listAllShops();
        } else {
            // 检查用户是否是某个运营组的组长
            boolean isLeader = groupService.isUserGroupLeader(currentUserId);
            
            if (isLeader) {
                // 组长可以查看其负责的运营组下的所有店铺
                shops = shopService.listLeaderShops(currentUserId);
            } else {
                // 普通组员只能查看分配给自己的店铺
                shops = shopService.listUserShops(currentUserId);
            }
        }
        
        // 屏蔽敏感信息
        for (Shop shop : shops) {
            shop.setAccessToken(null);
            shop.setApiKey(null);
            shop.setApiSecret(null);
        }
        
        return ApiResponse.success(shops);
    }
} 