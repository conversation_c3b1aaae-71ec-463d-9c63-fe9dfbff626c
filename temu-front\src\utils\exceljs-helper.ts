import * as ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';

/**
 * 从图片URL加载图片
 * @param url 图片URL
 * @returns Promise<ArrayBuffer> 图片数据
 */
export async function loadImageFromUrl(url: string): Promise<ArrayBuffer> {
  try {
    const response = await fetch(url, {
      method: 'GET',
      mode: 'cors',
      cache: 'no-cache',
      headers: {
        'Accept': 'image/jpeg,image/png,image/*',
        'Referer': window.location.origin
      }
    });
    
    if (!response.ok) {
      throw new Error(`无法加载图片: ${response.statusText}`);
    }
    return await response.arrayBuffer();
  } catch (error) {
    console.error('加载图片失败:', error);
    throw error;
  }
}

/**
 * 将数据导出为Excel文件，包含图片
 * @param data 要导出的数据
 * @param columns 列配置
 * @param fileName 文件名
 * @param sheetName 工作表名
 */
export async function exportToExcelWithImages(
  data: any[],
  columns: {
    header: string;
    key: string;
    width?: number;
    isImage?: boolean;
    formatter?: (value: any) => any;
  }[],
  fileName: string = '导出数据',
  sheetName: string = 'Sheet1'
): Promise<void> {
  // 创建工作簿
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet(sheetName);

  // 设置列
  worksheet.columns = columns.map(col => ({
    header: col.header,
    key: col.key,
    width: col.width || 15
  }));

  // 添加数据
  worksheet.addRows(data);
  
  // 设置行高 - 先为所有数据行设置统一高度
  const IMAGE_ROW_HEIGHT = 100; // 包含图片的行高度
  const STANDARD_ROW_HEIGHT = 25; // 普通行的高度
  
  // 设置表头行样式
  worksheet.getRow(1).height = 30;
  worksheet.getRow(1).font = { bold: true };
  worksheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' };
  
  // 处理所有数据行的高度和对齐方式
  for (let rowIndex = 0; rowIndex < data.length; rowIndex++) {
    const excelRowIndex = rowIndex + 2; // Excel行索引从1开始，第1行是表头
    
    // 设置行高
    worksheet.getRow(excelRowIndex).height = IMAGE_ROW_HEIGHT;
    
    // 设置所有单元格垂直居中
    worksheet.getRow(excelRowIndex).alignment = { vertical: 'middle' };
  }

  // 处理图片
  const IMAGE_WIDTH = 80; // 图片宽度
  const IMAGE_HEIGHT = 100; // 图片高度
  
  for (let rowIndex = 0; rowIndex < data.length; rowIndex++) {
    const row = data[rowIndex];
    for (let colIndex = 0; colIndex < columns.length; colIndex++) {
      const column = columns[colIndex];
      if (column.isImage && row[column.key]) {
        try {
          // 跳过空值
          if (!row[column.key]) continue;
          
          // 加载图片
          const imageUrl = row[column.key];
          const imageData = await loadImageFromUrl(imageUrl);
          
          // 添加图片到工作表
          const imageId = workbook.addImage({
            buffer: imageData,
            extension: 'png', // 假设图片是PNG格式，可以根据实际情况调整
          });

          // 在Excel中插入图片 - 使用简单的方式定位图片
          const excelRowIndex = rowIndex + 2; // Excel行索引从1开始
          
          // 插入图片并设置合理的尺寸
          worksheet.addImage(imageId, {
            tl: { col: colIndex, row: rowIndex + 1 },
            ext: { width: IMAGE_WIDTH, height: IMAGE_HEIGHT },
            editAs: 'oneCell' // 图片随单元格调整
          });
          
          // 图片成功下载后，清空单元格内的URL文本
          worksheet.getCell(`${String.fromCharCode(65 + colIndex)}${rowIndex + 2}`).value = null;
        } catch (error) {
          console.error(`处理第${rowIndex + 1}行图片失败:`, error);
          // 图片加载失败时，保留URL文本
          // 这里不需要修改，因为失败时单元格中已有URL
        }
      } else if (column.formatter && row[column.key]) {
        // 应用格式化器
        worksheet.getCell(`${String.fromCharCode(65 + colIndex)}${rowIndex + 2}`).value = 
          column.formatter(row[column.key]);
      }
    }
  }

  // 导出为Excel文件
  const buffer = await workbook.xlsx.writeBuffer();
  saveAs(new Blob([buffer]), `${fileName}.xlsx`);
}

/**
 * 本地退货明细数据导出
 * @param data 要导出的数据
 * @param fileName 文件名
 */
export async function exportLocalRefundData(data: any[], fileName: string = '本地退货明细'): Promise<void> {
  // 设置列配置
  const columns = [
    { header: '序号', key: 'index', width: 8 },
    { header: '店铺名称', key: 'shopName', width: 15 },
    { header: 'SPU', key: 'productSpuId', width: 12 },
    { header: 'SKU', key: 'productSkuId', width: 12 },
    { 
      header: '商品图片', 
      key: 'thumbUrl', 
      width: 15,
      isImage: true 
    },
    { 
      header: 'SKC', 
      key: 'productSkcId', 
      width: 15 
    },
    { 
      header: '属性集', 
      key: 'attributes', 
      width: 20,
      formatter: (value: any) => value 
    },
    { header: '货号', key: 'extCode', width: 15 },
    { header: '备货单号', key: 'purchaseSubOrderSn', width: 20 },
    { 
      header: '退货原因', 
      key: 'reasonDesc', 
      width: 20,
      formatter: (value: any) => {
        if (!value) return '暂无原因';
        if (Array.isArray(value)) return value.join('，');
        
        if (typeof value === 'string' && value.startsWith('[') && value.endsWith(']')) {
          try {
            const parsedArray = JSON.parse(value);
            if (Array.isArray(parsedArray)) {
              return parsedArray.join('，');
            }
          } catch (e) {
            // 解析失败，返回原字符串
          }
        }
        
        return String(value);
      }
    },
    { header: '退货包裹号', key: 'packageSn', width: 22 },
    { header: 'SKU件数', key: 'quantity', width: 10 },
    { header: '出库时间', key: 'outboundTime', width: 18 }
  ];

  // 预处理数据
  const processedData = data.map((item, index) => ({
    ...item,
    index: index + 1,
    attributes: `${item.mainSaleSpec}-${item.secondarySaleSpec}`
  }));

  // 导出Excel
  await exportToExcelWithImages(processedData, columns, fileName);
}

/**
 * 本地抽检明细数据导出
 * @param data 要导出的数据
 * @param fileName 文件名
 */
export async function exportLocalQualityInspectionData(data: any[], fileName: string = '本地抽检明细'): Promise<void> {
  // 设置列配置
  const columns = [
    { header: '序号', key: 'index', width: 8 },
    { header: '店铺名称', key: 'shopName', width: 15 },
    { header: 'SPU ID', key: 'spuId', width: 12 },
    { header: 'SKU ID', key: 'productSkuId', width: 12 },
    { header: 'SKC ID', key: 'productSkcId', width: 15 },
    { 
      header: '商品图片', 
      key: 'thumbUrl', 
      width: 15,
      isImage: true 
    },
    { header: '商品名称', key: 'skuName', width: 30 },
    { header: '商品类目', key: 'catName', width: 15 },
    { header: '属性规格', key: 'spec', width: 20 },
    { header: '货号', key: 'extCode', width: 15 },
    { header: '备货单号', key: 'purchaseNo', width: 20 },
    { 
      header: '抽检结果', 
      key: 'qcResult', 
      width: 10,
      formatter: (value: any) => value === '1' ? '合格' : '不合格'
    },
    { header: '最新抽检时间', key: 'qcResultUpdateTime', width: 18 },
    { 
      header: '疵点描述', 
      key: 'flawNameDesc', 
      width: 25 
    },
    { 
      header: '问题备注', 
      key: 'remark', 
      width: 25 
    },
    { 
      header: '疵点图片1', 
      key: 'attachment1', 
      width: 20,
      isImage: true 
    },
    { 
      header: '疵点图片2', 
      key: 'attachment2', 
      width: 20,
      isImage: true 
    },
    { 
      header: '疵点图片3', 
      key: 'attachment3', 
      width: 20,
      isImage: true 
    }
  ];

  // 计算最大的附件图片数量
  let maxAttachmentCount = 0;
  data.forEach(item => {
    if (item.attachments && Array.isArray(item.attachments)) {
      maxAttachmentCount = Math.max(maxAttachmentCount, item.attachments.length);
    }
  });

  // 如果没有附件图片，移除相关列
  if (maxAttachmentCount === 0) {
    columns.splice(-3, 3); // 移除最后三列（疵点图片1/2/3）
  } else if (maxAttachmentCount === 1) {
    columns.splice(-2, 2); // 只保留疵点图片1
  } else if (maxAttachmentCount === 2) {
    columns.splice(-1, 1); // 只保留疵点图片1和2
  }

  // 预处理数据
  const processedData = data.map((item, index) => {
    // 处理附件图片，最多支持3张
    const processedItem: any = {
      ...item,
      index: index + 1
    };

    // 处理附件图片，确保安全访问
    if (item.attachments && Array.isArray(item.attachments)) {
      // 只处理有效的URL
      const validAttachments = item.attachments.filter(url => url && typeof url === 'string');
      
      // 最多添加3张图片
      for (let i = 0; i < Math.min(validAttachments.length, 3); i++) {
        processedItem[`attachment${i+1}`] = validAttachments[i];
      }
    }

    return processedItem;
  });

  // 导出Excel
  await exportToExcelWithImages(processedData, columns, fileName);
} 