// 组长店铺分配数据类型定义
export interface ShopAssignment {
  id: number;
  groupId: number;
  groupName?: string;
  shopId: number;
  shopName?: string;
  userId: number;
  userName?: string;
  nickName?: string;
  permissionType: string; // '0'只读, '1'读写
  assignTime: string;
  assignBy: number;
  assignByName?: string;
  status: string; // '0'正常, '1'禁用
}

// 组长店铺分配查询参数
export interface ShopAssignmentQuery {
  pageNum: number;
  pageSize: number;
  groupId?: number;
  shopId?: number;
  shopName?: string;
  userId?: number;
  userName?: string;
  permissionType?: string;
  status?: string;
}

// 组长店铺分配分页结果
export interface ShopAssignmentPageResult {
  total: number;
  pageNum: number;
  pageSize: number;
  list: ShopAssignment[];
}

// 分配店铺参数
export interface AssignShopParams {
  groupId: number;
  shopId: number;
  userId: number;
  permissionType: string;
  assignBy?: number; // 分配者ID
}

// 批量分配店铺参数
export interface BatchAssignShopParams {
  groupId: number;
  shopIds: number[];
  userId: number;
  permissionType: string;
  assignBy?: number; // 分配者ID
}

// 取消分配参数
export interface UnassignShopParams {
  id: number;
  operatorId?: number; // 操作者ID（可选，API中会自动添加）
}

// 修改权限参数
export interface UpdatePermissionParams {
  id: number;
  permissionType: string;
  operatorId?: number; // 操作者ID（可选，API中会自动添加）
}

// 创建组员参数
export interface CreateMemberParams {
  username: string;
  password: string;
  nickName?: string;
  email?: string;
  phone?: string;
  groupId: number;
} 