package com.xiao.temu.modules.operation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.modules.operation.dto.GroupLeaderShopAssignmentDTO;
import com.xiao.temu.modules.operation.dto.GroupLeaderShopAssignmentQueryDTO;
import com.xiao.temu.modules.operation.entity.GroupLeaderShopAssignment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 组长店铺分配Mapper接口
 */
@Mapper
public interface GroupLeaderShopAssignmentMapper extends BaseMapper<GroupLeaderShopAssignment> {

    /**
     * 分页查询分配列表
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 分配信息列表
     */
    IPage<GroupLeaderShopAssignmentDTO> selectAssignmentList(Page<GroupLeaderShopAssignmentDTO> page, 
                                                           @Param("query") GroupLeaderShopAssignmentQueryDTO queryDTO);

    /**
     * 获取指定运营组的所有店铺分配数据（不分页）
     *
     * @param groupId 运营组ID
     * @return 该运营组的所有店铺分配数据列表
     */
    List<GroupLeaderShopAssignmentDTO> selectAllAssignmentsByGroupId(@Param("groupId") Long groupId);

    /**
     * 根据ID查询分配详情
     *
     * @param id 主键ID
     * @return 分配详情
     */
    GroupLeaderShopAssignmentDTO selectAssignmentById(@Param("id") Long id);

    /**
     * 查询用户在指定店铺的分配信息
     *
     * @param userId 用户ID
     * @param shopId 店铺ID
     * @return 分配信息
     */
    GroupLeaderShopAssignmentDTO selectByUserAndShop(@Param("userId") Long userId, @Param("shopId") Long shopId);

    /**
     * 查询用户的所有分配信息
     *
     * @param userId 用户ID
     * @return 分配信息列表
     */
    List<GroupLeaderShopAssignmentDTO> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询店铺的所有分配信息
     *
     * @param shopId 店铺ID
     * @return 分配信息列表
     */
    List<GroupLeaderShopAssignmentDTO> selectByShopId(@Param("shopId") Long shopId);

    /**
     * 查询运营组的所有分配信息
     *
     * @param groupId 运营组ID
     * @return 分配信息列表
     */
    List<GroupLeaderShopAssignmentDTO> selectByGroupId(@Param("groupId") Long groupId);
} 