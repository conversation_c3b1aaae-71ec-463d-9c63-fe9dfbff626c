<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiao.temu.modules.sync.mapper.RefundPackageSyncTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xiao.temu.modules.sync.entity.RefundPackageSyncTask">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="last_sync_time" property="lastSyncTime" />
        <result column="last_update_time" property="lastUpdateTime" />
        <result column="sync_status" property="syncStatus" />
        <result column="error_message" property="errorMessage" />
        <result column="total_records" property="totalRecords" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 清空时间字段 -->
    <update id="clearTimeFields" parameterType="java.util.Map">
        UPDATE refund_package_sync_task
        SET last_sync_time = NULL,
            last_update_time = NULL,
            sync_status = 0,
            error_message = NULL,
            total_records = 0,
            update_time = NOW()
        WHERE shop_id = #{shopId}
    </update>

</mapper> 