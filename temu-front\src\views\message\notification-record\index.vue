<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>备货单通知记录查询</span>
        </div>
      </template>
      
      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
        <el-form-item label="店铺ID" prop="shopId">
          <el-input v-model="queryParams.shopId" placeholder="请输入店铺ID" clearable style="width: 180px" />
        </el-form-item>
        
        <el-form-item label="备货单号" prop="subPurchaseOrderSn">
          <el-input v-model="queryParams.subPurchaseOrderSn" placeholder="请输入备货单号" clearable style="width: 180px" />
        </el-form-item>
        
        <el-form-item label="通知类型" prop="notificationType">
          <el-select v-model="queryParams.notificationType" placeholder="请选择通知类型" clearable style="width: 180px">
            <el-option v-for="(name, type) in NOTIFICATION_TYPE_MAP" :key="type" :label="name" :value="Number(type)" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="通知状态" prop="notifyStatus">
          <el-select v-model="queryParams.notifyStatus" placeholder="请选择通知状态" clearable style="width: 180px">
            <el-option v-for="(name, status) in NOTIFY_STATUS_MAP" :key="status" :label="name" :value="Number(status)" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="通知时间" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery" v-if="hasPermission('purchase:notification:record:query')">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button type="success" icon="Download" @click="handleExport" v-if="hasPermission('purchase:notification:record:export')">导出</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="recordList"
        style="width: 100%"
      >
        <el-table-column type="index" width="50" align="center" />
        <el-table-column prop="shopId" label="店铺ID" width="80" />
        <el-table-column prop="shopName" label="店铺名称" width="120" :show-overflow-tooltip="true" />
        <el-table-column prop="subPurchaseOrderSn" label="备货单号" width="180" :show-overflow-tooltip="true" />
        <el-table-column label="通知类型" width="150" align="center">
          <template #default="scope">
            <notification-type-tag :type="scope.row.notificationType" />
          </template>
        </el-table-column>
        <el-table-column prop="notifyCount" label="通知次数" width="80" align="center" />
        <el-table-column label="通知状态" width="100" align="center">
          <template #default="scope">
            <el-tag size="small" :type="getStatusType(scope.row.notifyStatus)">
              {{ NOTIFY_STATUS_MAP[scope.row.notifyStatus] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastNotifyTime" label="最后通知时间" width="180" />
        <el-table-column prop="groupName" label="运营组" width="120" :show-overflow-tooltip="true" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
      </el-table>
      
      <!-- 分页组件 -->
      <pagination
        v-if="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getRecordList"
      />
    </el-card>
  </div>
</template>

<script lang="ts" setup name="NotificationRecord">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getNotificationRecordList, exportNotificationRecord } from '@/api/notification'
import NotificationTypeTag from '@/components/NotificationTypeTag/index.vue'
import { NotificationType, NotifyStatus, NOTIFICATION_TYPE_MAP, NOTIFY_STATUS_MAP, type NotificationRecordQuery, type NotificationRecord } from '@/types/notification'
import { hasPermission } from '@/utils/permission'
import Pagination from '@/components/Pagination/index.vue'

// 查询参数
const queryParams = reactive<NotificationRecordQuery>({
  pageNum: 1,
  pageSize: 10
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 表格数据
const recordList = ref<NotificationRecord[]>([])
const loading = ref(false)
const total = ref(0)

// 监听日期范围变化，更新查询参数
watch(dateRange, (val) => {
  if (val) {
    queryParams.startTime = val[0]
    queryParams.endTime = val[1]
  } else {
    queryParams.startTime = undefined
    queryParams.endTime = undefined
  }
})

// 获取通知状态对应的标签类型
const getStatusType = (status: NotifyStatus) => {
  switch (status) {
    case NotifyStatus.PENDING:
      return 'info'
    case NotifyStatus.NOTIFYING:
      return 'warning'
    case NotifyStatus.COMPLETED:
      return 'success'
    default:
      return ''
  }
}

// 查询记录列表
const getRecordList = async () => {
  loading.value = true
  try {
    const res = await getNotificationRecordList(queryParams)
    recordList.value = res.data.records || []
    total.value = res.data.total || 0
  } catch (error) {
    console.error('获取通知记录列表失败', error)
    ElMessage.error('获取通知记录列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索按钮点击事件
const handleQuery = () => {
  queryParams.pageNum = 1
  getRecordList()
}

// 重置按钮点击事件
const resetQuery = () => {
  dateRange.value = null
  // 重置表单
  Object.keys(queryParams).forEach(key => {
    if (key !== 'pageNum' && key !== 'pageSize') {
      // @ts-ignore
      queryParams[key] = undefined
    }
  })
  queryParams.pageNum = 1
  getRecordList()
}

// 导出按钮点击事件
const handleExport = () => {
  ElMessageBox.confirm('确认导出所有数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      loading.value = true
      try {
        await exportNotificationRecord(queryParams)
        ElMessage.success('导出成功')
      } catch (error) {
        console.error('导出失败', error)
        ElMessage.error('导出失败')
      } finally {
        loading.value = false
      }
    })
    .catch(() => {})
}

onMounted(() => {
  getRecordList()
})
</script>

<style scoped>
.search-form {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style> 