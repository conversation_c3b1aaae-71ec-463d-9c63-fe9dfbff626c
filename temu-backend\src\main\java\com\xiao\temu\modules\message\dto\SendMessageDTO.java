package com.xiao.temu.modules.message.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 发送消息DTO
 */
@Data
public class SendMessageDTO {

    /**
     * 消息标题
     */
    @NotBlank(message = "消息标题不能为空")
    @Size(max = 100, message = "消息标题不能超过100个字符")
    private String title;

    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    private String content;

    /**
     * 消息类型（1系统消息 2任务提醒 3店铺消息）
     */
    @NotBlank(message = "消息类型不能为空")
    private String messageType;

    /**
     * 接收对象类型(0全部用户 1指定用户 2指定角色 3运营组)
     */
    @NotBlank(message = "接收对象类型不能为空")
    private String targetType;

    /**
     * 接收对象ID列表,逗号分隔
     */
    private String targetIds;

    /**
     * 相关店铺ID
     */
    private Long shopId;

    /**
     * 重要程度(1普通 2重要 3紧急)
     */
    private String importance = "1";

    /**
     * 发布时间，为空则立即发布
     */
    private LocalDateTime publishTime;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 是否使用模板
     */
    private Boolean useTemplate = false;

    /**
     * 模板编码
     */
    private String templateCode;

    /**
     * 模板参数，JSON格式
     */
    private String templateParams;
} 