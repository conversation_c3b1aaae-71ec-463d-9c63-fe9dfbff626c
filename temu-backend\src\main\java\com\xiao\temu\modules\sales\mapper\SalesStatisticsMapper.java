package com.xiao.temu.modules.sales.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiao.temu.modules.sales.entity.SalesStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.time.LocalDate;
import java.util.List;

/**
 * 销售统计数据Mapper接口
 */
@Mapper
public interface SalesStatisticsMapper extends BaseMapper<SalesStatistics> {
    
    /**
     * 根据店铺ID和日期范围查询销售统计数据
     * 
     * @param shopIds 店铺ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 销售统计数据列表
     */
    List<SalesStatistics> findByShopIdsAndDateBetween(
        @Param("shopIds") List<Long> shopIds, 
        @Param("startDate") LocalDate startDate, 
        @Param("endDate") LocalDate endDate
    );
    
    /**
     * 根据店铺ID和日期查询销售统计数据
     * 
     * @param shopId 店铺ID
     * @param date 日期
     * @return 销售统计数据
     */
    SalesStatistics findByShopIdAndDate(
        @Param("shopId") Long shopId, 
        @Param("date") LocalDate date
    );
} 