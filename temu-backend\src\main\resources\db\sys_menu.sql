/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : temu_api

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 03/06/2025 23:39:08
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件路径',
  `is_frame` int NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7135 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '菜单权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 0, 1, 'system', NULL, 1, 'M', '0', '', 'Tools', '2025-03-20 19:07:49', '2025-03-21 15:04:11', '系统管理目录');
INSERT INTO `sys_menu` VALUES (2, '运营管理', 0, 1, 'operation', NULL, 1, 'M', '0', '', 'Memo', '2025-03-20 19:07:49', '2025-05-12 14:19:30', '运营管理目录');
INSERT INTO `sys_menu` VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', 1, 'C', '0', 'system:user:list', 'user', '2025-03-20 19:07:49', NULL, '用户管理菜单');
INSERT INTO `sys_menu` VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', 1, 'C', '0', 'system:role:list', 'UserFilled', '2025-03-20 19:07:49', '2025-03-21 15:03:19', '角色管理菜单');
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', 1, 'C', '0', 'system:menu:list', 'Menu', '2025-03-20 19:07:49', '2025-03-21 14:51:58', '菜单管理菜单');
INSERT INTO `sys_menu` VALUES (200, '运营组管理', 2, 1, 'group', 'operation/group/index', 1, 'C', '0', 'operation:group:list', 'Avatar', '2025-03-20 19:07:49', '2025-03-21 14:52:12', '运营组管理菜单');
INSERT INTO `sys_menu` VALUES (201, '店铺管理', 2, 2, 'shop', 'operation/shop/index', 1, 'C', '0', 'operation:shop:list', 'shop', '2025-03-20 19:07:49', NULL, '店铺管理菜单');
INSERT INTO `sys_menu` VALUES (202, '任务管理', 2, 3, 'task', 'operation/task/index', 1, 'C', '0', 'operation:task:list', 'Checked', '2025-03-20 19:07:49', '2025-03-21 14:52:25', '任务管理菜单');
INSERT INTO `sys_menu` VALUES (400, '消息中心', 0, 5, 'message', NULL, 1, 'M', '0', '', 'message', '2025-03-24 08:48:20', '2025-03-25 15:01:33', '消息中心目录');
INSERT INTO `sys_menu` VALUES (401, '我的消息', 400, 1, 'my-message', 'message/my-message/index', 1, 'C', '0', 'message:my:list', 'email', '2025-03-24 08:48:20', NULL, '我的消息菜单');
INSERT INTO `sys_menu` VALUES (402, '发送消息', 400, 2, 'send-message', 'message/send-message/index', 1, 'C', '0', 'message:send:add', 'edit', '2025-03-24 08:48:20', NULL, '发送消息菜单');
INSERT INTO `sys_menu` VALUES (403, '消息模板', 400, 3, 'message-template', 'message/message-template/index', 1, 'C', '0', 'message:template:list', 'form', '2025-03-24 08:48:20', NULL, '消息模板菜单');
INSERT INTO `sys_menu` VALUES (404, '通知配置管理', 400, 4, 'notification-config', 'message/notification-config/index', 1, 'C', '0', 'purchase:notification:config', 'Setting', '2025-05-31 22:01:24', '2025-05-31 22:01:24', '备货单通知配置管理页面');
INSERT INTO `sys_menu` VALUES (405, '通知记录查询', 400, 5, 'notification-record', 'message/notification-record/index', 1, 'C', '0', 'purchase:notification:record', 'Document', '2025-05-31 22:01:24', '2025-05-31 22:01:24', '备货单通知记录查询页面');
INSERT INTO `sys_menu` VALUES (406, '通知测试工具', 400, 6, 'notification-test', 'message/notification-test/index', 1, 'C', '0', 'purchase:notification:test', 'MessageBox', '2025-05-31 22:01:24', '2025-05-31 22:01:24', '备货单通知测试工具页面');
INSERT INTO `sys_menu` VALUES (1001, '用户查询', 100, 1, '', '', 1, 'F', '0', 'system:user:query', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (1002, '用户新增', 100, 2, '', '', 1, 'F', '0', 'system:user:add', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (1003, '用户修改', 100, 3, '', '', 1, 'F', '0', 'system:user:edit', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (1004, '用户删除', 100, 4, '', '', 1, 'F', '0', 'system:user:remove', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (1005, '用户导出', 100, 5, '', '', 1, 'F', '0', 'system:user:export', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (1006, '用户导入', 100, 6, '', '', 1, 'F', '0', 'system:user:import', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (1007, '重置密码', 100, 7, '', '', 1, 'F', '0', 'system:user:resetPassword', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (1008, '角色查询', 101, 1, '', '', 1, 'F', '0', 'system:role:query', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (1009, '角色新增', 101, 2, '', '', 1, 'F', '0', 'system:role:add', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (1010, '角色修改', 101, 3, '', '', 1, 'F', '0', 'system:role:edit', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (1011, '角色删除', 101, 4, '', '', 1, 'F', '0', 'system:role:remove', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (1012, '角色导出', 101, 5, '', '', 1, 'F', '0', 'system:role:export', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (1013, '菜单查询', 102, 1, '', '', 1, 'F', '0', 'system:menu:query', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (1014, '菜单新增', 102, 2, '', '', 1, 'F', '0', 'system:menu:add', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (1015, '菜单修改', 102, 3, '', '', 1, 'F', '0', 'system:menu:edit', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (1016, '菜单删除', 102, 4, '', '', 1, 'F', '0', 'system:menu:remove', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (2001, '运营组查询', 200, 1, '', '', 1, 'F', '0', 'operation:group:query', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (2002, '运营组新增', 200, 2, '', '', 1, 'F', '0', 'operation:group:add', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (2003, '运营组修改', 200, 3, '', '', 1, 'F', '0', 'operation:group:edit', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (2004, '运营组删除', 200, 4, '', '', 1, 'F', '0', 'operation:group:remove', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (2005, '运营组导出', 200, 5, '', '', 1, 'F', '0', 'operation:group:export', '#', '2025-03-25 10:30:00', NULL, '');
INSERT INTO `sys_menu` VALUES (2101, '店铺查询', 201, 1, '', '', 1, 'F', '0', 'operation:shop:query', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (2102, '店铺新增', 201, 2, '', '', 1, 'F', '0', 'operation:shop:add', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (2103, '店铺修改', 201, 3, '', '', 1, 'F', '0', 'operation:shop:edit', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (2104, '店铺删除', 201, 4, '', '', 1, 'F', '0', 'operation:shop:remove', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (2105, '店铺导出', 201, 5, '', '', 1, 'F', '0', 'operation:shop:export', '#', '2025-03-25 10:30:00', NULL, '');
INSERT INTO `sys_menu` VALUES (2201, '任务查询', 202, 1, '', '', 1, 'F', '0', 'operation:task:query', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (2202, '任务新增', 202, 2, '', '', 1, 'F', '0', 'operation:task:add', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (2203, '任务修改', 202, 3, '', '', 1, 'F', '0', 'operation:task:edit', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (2204, '任务删除', 202, 4, '', '', 1, 'F', '0', 'operation:task:remove', '#', '2025-03-20 19:07:49', NULL, '');
INSERT INTO `sys_menu` VALUES (2205, '任务导出', 202, 5, '', '', 1, 'F', '0', 'operation:task:export', '#', '2025-03-25 10:30:00', NULL, '');
INSERT INTO `sys_menu` VALUES (2220, '组长店铺分配', 2, 5, 'leader-assign', 'operation/leader-assign/index', 1, 'C', '0', 'operation:leader:assign', 'Place', '2025-03-22 20:55:20', '2025-03-22 20:56:53', '组长店铺分配菜单');
INSERT INTO `sys_menu` VALUES (2221, '分配查询', 2220, 1, '', '', 1, 'F', '0', 'operation:leader:list', '#', '2025-03-22 20:55:20', NULL, '');
INSERT INTO `sys_menu` VALUES (2222, '分配店铺', 2220, 2, '', '', 1, 'F', '0', 'operation:leader:add', '#', '2025-03-22 20:55:20', NULL, '');
INSERT INTO `sys_menu` VALUES (2223, '取消分配', 2220, 3, '', '', 1, 'F', '0', 'operation:leader:remove', '#', '2025-03-22 20:55:20', NULL, '');
INSERT INTO `sys_menu` VALUES (2224, '修改权限', 2220, 4, '', '', 1, 'F', '0', 'operation:leader:update', '#', '2025-03-22 20:55:20', NULL, '');
INSERT INTO `sys_menu` VALUES (4001, '消息查询', 401, 1, '', '', 1, 'F', '0', 'message:my:query', '#', '2025-03-24 08:48:20', NULL, '');
INSERT INTO `sys_menu` VALUES (4002, '消息已读', 401, 2, '', '', 1, 'F', '0', 'message:my:read', '#', '2025-03-24 08:48:20', NULL, '');
INSERT INTO `sys_menu` VALUES (4003, '消息删除', 401, 3, '', '', 1, 'F', '0', 'message:my:remove', '#', '2025-03-24 08:48:20', NULL, '');
INSERT INTO `sys_menu` VALUES (4004, '消息发送', 402, 1, '', '', 1, 'F', '0', 'message:send:add', '#', '2025-03-24 08:48:20', NULL, '');
INSERT INTO `sys_menu` VALUES (4005, '模板查询', 403, 1, '', '', 1, 'F', '0', 'message:template:query', '#', '2025-03-24 08:48:20', NULL, '');
INSERT INTO `sys_menu` VALUES (4006, '模板新增', 403, 2, '', '', 1, 'F', '0', 'message:template:add', '#', '2025-03-24 08:48:20', NULL, '');
INSERT INTO `sys_menu` VALUES (4007, '模板修改', 403, 3, '', '', 1, 'F', '0', 'message:template:edit', '#', '2025-03-24 08:48:20', NULL, '');
INSERT INTO `sys_menu` VALUES (4008, '模板删除', 403, 4, '', '', 1, 'F', '0', 'message:template:remove', '#', '2025-03-24 08:48:20', NULL, '');
INSERT INTO `sys_menu` VALUES (4009, 'TEMU平台接口', 0, 2, 'temu', '', 0, 'M', '0', '', 'DataAnalysis', '2025-03-25 15:01:12', '2025-03-25 15:13:17', 'TEMU平台接口相关页面主目录');
INSERT INTO `sys_menu` VALUES (4010, '退货明细', 4009, 0, 'returnDetails', 'temu/returnDetails/index', 0, 'C', '0', 'temu:returnDetails:list', '', '2025-03-25 15:12:54', '2025-03-25 16:40:06', '退货明细接口');
INSERT INTO `sys_menu` VALUES (4011, '退货明细查询', 4010, 0, '', '', 0, 'F', '0', 'temu:returnDetails:query', '', '2025-03-25 15:17:00', '2025-03-25 15:15:28', '退货明细查询按钮');
INSERT INTO `sys_menu` VALUES (4012, '退货明细导出', 4010, 0, '', '', 0, 'F', '0', 'temu:returnDetails:export', '', '2025-03-25 15:17:47', '2025-03-25 15:15:28', '退货明细导出按钮');
INSERT INTO `sys_menu` VALUES (4013, '抽检结果明细', 4009, 1, 'qc-detail', 'temu/qc-detail/index', 0, 'C', '0', 'temu:qc-detail:list', '', '2025-03-25 20:49:56', '2025-03-25 21:20:05', '抽检结果明细\n');
INSERT INTO `sys_menu` VALUES (4014, '抽检结果明细查询', 4013, 0, '', '', 0, 'F', '0', 'temu:qc-detail:query', '', '2025-03-25 21:08:49', NULL, '抽检结果明细查询按钮\n');
INSERT INTO `sys_menu` VALUES (4015, '抽检结果明细导出', 4013, 0, '', '', 0, 'F', '0', 'temu:qc-detail:export', '', '2025-03-25 21:11:40', NULL, '抽检结果明细导出');
INSERT INTO `sys_menu` VALUES (4040, '配置查询', 404, 1, '', '', 1, 'F', '0', 'purchase:notification:config:query', '#', '2025-05-31 22:01:24', '2025-05-31 22:01:24', '通知配置查询按钮');
INSERT INTO `sys_menu` VALUES (4041, '配置修改', 404, 2, '', '', 1, 'F', '0', 'purchase:notification:config:edit', '#', '2025-05-31 22:01:24', '2025-05-31 22:01:24', '通知配置修改按钮');
INSERT INTO `sys_menu` VALUES (4042, '配置启用/禁用', 404, 3, '', '', 1, 'F', '0', 'purchase:notification:config:toggle', '#', '2025-05-31 22:01:24', '2025-05-31 22:01:24', '通知配置启用/禁用按钮');
INSERT INTO `sys_menu` VALUES (4050, '记录查询', 405, 1, '', '', 1, 'F', '0', 'purchase:notification:record:query', '#', '2025-05-31 22:01:24', '2025-05-31 22:01:24', '通知记录查询按钮');
INSERT INTO `sys_menu` VALUES (4051, '记录导出', 405, 2, '', '', 1, 'F', '0', 'purchase:notification:record:export', '#', '2025-05-31 22:01:24', '2025-05-31 22:01:24', '通知记录导出按钮');
INSERT INTO `sys_menu` VALUES (4060, '匹配预览', 406, 1, '', '', 1, 'F', '0', 'purchase:notification:test:preview', '#', '2025-05-31 22:01:24', '2025-05-31 22:01:24', '通知匹配预览按钮');
INSERT INTO `sys_menu` VALUES (4061, '手动触发', 406, 2, '', '', 1, 'F', '0', 'purchase:notification:test:trigger', '#', '2025-05-31 22:01:24', '2025-05-31 22:01:24', '通知手动触发按钮');
INSERT INTO `sys_menu` VALUES (4062, '测试设置', 406, 3, '', '', 1, 'F', '0', 'purchase:notification:test:setting', '#', '2025-05-31 22:01:24', '2025-05-31 22:01:24', '通知测试设置按钮');
INSERT INTO `sys_menu` VALUES (5000, '数据同步', 0, 7, 'synchronous', NULL, 1, 'M', '0', '', 'RefreshRight', '2025-03-26 16:26:26', NULL, '数据同步功能主目录');
INSERT INTO `sys_menu` VALUES (5001, '抽检数据同步', 5000, 1, 'quality-inspection-sync', 'synchronous/quality-inspection-sync/index', 1, 'C', '0', 'synchronous:quality:list', 'Connection', '2025-03-26 16:26:26', NULL, '抽检数据同步页面');
INSERT INTO `sys_menu` VALUES (5002, '商品数据同步', 5000, 2, 'product-sync', 'synchronous/product-sync/index', 1, 'C', '0', 'synchronous:product:list', 'Goods', '2025-03-27 14:27:19', NULL, '商品数据同步页面');
INSERT INTO `sys_menu` VALUES (5101, '数据同步查询', 5001, 1, '', NULL, 1, 'F', '0', 'synchronous:quality:query', '#', '2025-03-26 16:26:26', NULL, '查询抽检数据同步任务列表');
INSERT INTO `sys_menu` VALUES (5102, '触发同步', 5001, 2, '', NULL, 1, 'F', '0', 'synchronous:quality:trigger', '#', '2025-03-26 16:26:26', NULL, '手动触发抽检数据同步');
INSERT INTO `sys_menu` VALUES (5103, '初始化任务', 5001, 3, '', NULL, 1, 'F', '0', 'synchronous:quality:init', '#', '2025-03-26 16:26:26', NULL, '初始化抽检数据同步任务');
INSERT INTO `sys_menu` VALUES (5201, '数据同步查询', 5002, 1, '', NULL, 1, 'F', '0', 'synchronous:product:query', '#', '2025-03-27 14:27:19', NULL, '查询商品数据同步任务列表');
INSERT INTO `sys_menu` VALUES (5202, '触发同步', 5002, 2, '', NULL, 1, 'F', '0', 'synchronous:product:trigger', '#', '2025-03-27 14:27:19', NULL, '手动触发商品数据同步');
INSERT INTO `sys_menu` VALUES (5203, '初始化任务', 5002, 3, '', NULL, 1, 'F', '0', 'synchronous:product:init', '#', '2025-03-27 14:27:19', NULL, '初始化商品数据同步任务');
INSERT INTO `sys_menu` VALUES (5204, '本地数据', 0, 4, 'local', '', 1, 'M', '0', '', 'Coin', '2025-03-27 17:31:15', '2025-05-29 10:38:26', '本地数据查询主菜单');
INSERT INTO `sys_menu` VALUES (5205, '本地抽检明细', 5204, 1, 'local-qc-detail', 'local/qc-detail/index', 1, 'C', '0', 'local:qc-detail:list', 'List', '2025-03-27 17:31:15', '2025-03-27 17:31:15', '本地抽检结果明细页面');
INSERT INTO `sys_menu` VALUES (5206, '抽检明细查询', 5205, 1, '', '', 1, 'F', '0', 'local:qc-detail:query', '#', '2025-03-27 17:31:15', '2025-03-27 17:31:15', '本地抽检明细查询按钮');
INSERT INTO `sys_menu` VALUES (5207, '抽检明细导出', 5205, 2, '', '', 1, 'F', '0', 'local:qc-detail:export', '#', '2025-03-27 17:31:15', '2025-03-27 17:31:15', '本地抽检明细导出按钮');
INSERT INTO `sys_menu` VALUES (5208, '本地退货明细', 5204, 2, 'refund-detail', 'local/refund-detail/index', 1, 'C', '0', 'local:refund-detail:list', 'Goods', '2025-03-29 00:48:36', '2025-03-29 00:48:36', '本地退货包裹明细页面');
INSERT INTO `sys_menu` VALUES (5209, '退货明细查询', 5208, 1, '', '', 1, 'F', '0', 'local:refund-detail:query', '#', '2025-03-29 00:48:36', '2025-03-29 00:48:36', '本地退货明细查询按钮');
INSERT INTO `sys_menu` VALUES (5210, '退货明细导出', 5208, 2, '', '', 1, 'F', '0', 'local:refund-detail:export', '#', '2025-03-29 00:48:36', '2025-03-29 00:48:36', '本地退货明细导出按钮');
INSERT INTO `sys_menu` VALUES (5211, '店铺违规信息', 5204, 3, 'violation', 'local/violation/index', 1, 'C', '0', 'operation:violation:list', 'Warning', '2025-03-31 15:06:47', '2025-03-31 15:06:47', '店铺违规信息页面');
INSERT INTO `sys_menu` VALUES (5212, '违规信息查询', 5211, 1, '', '', 1, 'F', '0', 'operation:violation:query', '#', '2025-03-31 15:06:47', '2025-03-31 15:06:47', '违规信息查询按钮');
INSERT INTO `sys_menu` VALUES (5213, '违规信息新增', 5211, 2, '', '', 1, 'F', '0', 'operation:violation:add', '#', '2025-03-31 15:06:47', '2025-03-31 15:06:47', '违规信息新增按钮');
INSERT INTO `sys_menu` VALUES (5214, '违规信息修改', 5211, 3, '', '', 1, 'F', '0', 'operation:violation:edit', '#', '2025-03-31 15:06:47', '2025-03-31 15:06:47', '违规信息修改按钮');
INSERT INTO `sys_menu` VALUES (5215, '违规信息删除', 5211, 4, '', '', 1, 'F', '0', 'operation:violation:remove', '#', '2025-03-31 15:06:47', '2025-03-31 15:06:47', '违规信息删除按钮');
INSERT INTO `sys_menu` VALUES (5216, '违规信息导出', 5211, 5, '', '', 1, 'F', '0', 'operation:violation:export', '#', '2025-03-31 15:06:47', '2025-03-31 15:06:47', '违规信息导出按钮');
INSERT INTO `sys_menu` VALUES (5300, '退货包裹同步', 5000, 3, 'refund-package-sync', 'synchronous/refund-package-sync/index', 1, 'C', '0', 'synchronous:refund:list', 'Box', '2025-03-29 00:01:09', '2025-03-29 00:01:09', '退货包裹数据同步页面');
INSERT INTO `sys_menu` VALUES (5301, '数据同步查询', 5300, 1, '', NULL, 1, 'F', '0', 'synchronous:refund:query', '#', '2025-03-29 00:01:09', '2025-03-29 00:01:09', '查询退货包裹数据同步任务列表');
INSERT INTO `sys_menu` VALUES (5302, '触发同步', 5300, 2, '', NULL, 1, 'F', '0', 'synchronous:refund:trigger', '#', '2025-03-29 00:01:09', '2025-03-29 00:01:09', '手动触发退货包裹数据同步');
INSERT INTO `sys_menu` VALUES (5303, '初始化任务', 5300, 3, '', NULL, 1, 'F', '0', 'synchronous:refund:init', '#', '2025-03-29 00:01:09', '2025-03-29 00:01:09', '初始化退货包裹数据同步任务');
INSERT INTO `sys_menu` VALUES (5400, '违规质检综合查询', 5204, 4, 'violation-inspection', 'local/violation-inspection/index', 1, 'C', '0', 'operation:violation:list', 'Search', '2025-04-05 13:38:33', '2025-04-05 13:38:33', '违规信息和质检结果综合查询页面');
INSERT INTO `sys_menu` VALUES (5401, '综合查询', 5400, 1, '', '', 1, 'F', '0', 'operation:violation:query', '#', '2025-04-05 13:38:33', '2025-04-05 13:38:33', '违规质检综合查询按钮');
INSERT INTO `sys_menu` VALUES (5402, '详情查看', 5400, 2, '', '', 1, 'F', '0', 'operation:violation:detail', '#', '2025-04-05 13:38:33', '2025-04-05 13:38:33', '违规质检综合查询详情查看按钮');
INSERT INTO `sys_menu` VALUES (5403, '导出数据', 5400, 3, '', '', 1, 'F', '0', 'operation:violation:export', '#', '2025-04-05 13:38:33', '2025-04-05 13:38:33', '违规质检综合查询导出按钮');
INSERT INTO `sys_menu` VALUES (5500, '销售数据同步', 5000, 4, 'sales-sync', 'synchronous/sales-sync/index', 1, 'C', '0', 'synchronous:sales:list', 'ShoppingCart', '2025-04-08 10:55:18', '2025-04-08 10:55:18', '销售数据同步页面');
INSERT INTO `sys_menu` VALUES (5501, '数据同步查询', 5500, 1, '', NULL, 1, 'F', '0', 'synchronous:sales:query', '#', '2025-04-08 10:55:18', '2025-04-08 10:55:18', '查询销售数据同步任务列表');
INSERT INTO `sys_menu` VALUES (5502, '触发同步', 5500, 2, '', NULL, 1, 'F', '0', 'synchronous:sales:trigger', '#', '2025-04-08 10:55:18', '2025-04-08 10:55:18', '手动触发销售数据同步');
INSERT INTO `sys_menu` VALUES (5503, '初始化任务', 5500, 3, '', NULL, 1, 'F', '0', 'synchronous:sales:init', '#', '2025-04-08 10:55:18', '2025-04-08 10:55:18', '初始化销售数据同步任务');
INSERT INTO `sys_menu` VALUES (5504, '本地销售数据', 5204, 5, 'sales-data', 'local/sales-data/index', 1, 'C', '0', 'local:sales-data:list', 'ShoppingCart', '2025-04-08 19:05:40', '2025-04-08 19:05:40', '本地商品销售信息查询页面');
INSERT INTO `sys_menu` VALUES (5505, '销售数据查询', 5504, 1, '', '', 1, 'F', '0', 'local:sales-data:query', '#', '2025-04-08 19:05:40', '2025-04-08 19:05:40', '本地销售数据查询按钮');
INSERT INTO `sys_menu` VALUES (5506, '销售数据导出', 5504, 2, '', '', 1, 'F', '0', 'local:sales-data:export', '#', '2025-04-08 19:05:40', '2025-04-08 19:05:40', '本地销售数据导出按钮');
INSERT INTO `sys_menu` VALUES (5600, '销售数据查询', 4009, 2, 'temu-sales-data', 'temu/sales-data/index', 1, 'C', '0', 'temu:sales-data:list', 'ShoppingCart', '2025-04-10 08:44:39', '2025-04-10 08:44:39', 'TEMU平台销售数据查询页面');
INSERT INTO `sys_menu` VALUES (5601, '销售数据查询', 5600, 1, '', '', 1, 'F', '0', 'temu:sales-data:query', '#', '2025-04-10 08:44:39', '2025-04-10 08:44:39', '销售数据查询按钮');
INSERT INTO `sys_menu` VALUES (5602, '销售数据导出', 5600, 2, '', '', 1, 'F', '0', 'temu:sales-data:export', '#', '2025-04-10 08:44:39', '2025-04-10 08:44:39', '销售数据导出按钮');
INSERT INTO `sys_menu` VALUES (5603, '销售数据详情', 5600, 3, '', '', 1, 'F', '0', 'temu:sales-data:detail', '#', '2025-04-10 08:44:39', '2025-04-10 08:44:39', '销售数据详情查看按钮');
INSERT INTO `sys_menu` VALUES (5700, '我的备货单', 4009, 3, 'purchase-order', 'temu/purchase-order/index', 1, 'C', '0', 'temu:purchase-order:list', 'List', '2025-04-24 14:34:07', '2025-05-28 09:22:30', 'TEMU平台备货单管理页面');
INSERT INTO `sys_menu` VALUES (5701, '备货单查询', 5700, 1, '', '', 1, 'F', '0', 'temu:purchase-order:query', '#', '2025-04-24 14:34:07', '2025-04-24 14:34:07', '备货单查询按钮');
INSERT INTO `sys_menu` VALUES (5702, '备货单导出', 5700, 2, '', '', 1, 'F', '0', 'temu:purchase-order:export', '#', '2025-04-24 14:34:07', '2025-04-24 14:34:07', '备货单导出按钮');
INSERT INTO `sys_menu` VALUES (5703, '备货单详情', 5700, 3, '', '', 1, 'F', '0', 'temu:purchase-order:detail', '#', '2025-04-24 14:34:07', '2025-04-24 14:34:07', '备货单详情查看按钮');
INSERT INTO `sys_menu` VALUES (5704, '生产进度更新', 5700, 0, '', '', 0, 'F', '0', 'temu:purchase-order:progressUupdate', '', '2025-05-08 21:10:46', NULL, '');
INSERT INTO `sys_menu` VALUES (5800, '发货单列表', 4009, 4, 'ship-order', 'temu/ship-order/index', 1, 'C', '0', 'temu:ship-order:list', 'Box', '2025-05-16 15:01:18', '2025-05-28 09:22:36', 'TEMU平台发货单管理页面');
INSERT INTO `sys_menu` VALUES (5801, '发货单查询', 5800, 1, '', '', 1, 'F', '0', 'temu:ship-order:query', '#', '2025-05-16 15:01:18', '2025-05-16 15:01:18', '发货单查询按钮');
INSERT INTO `sys_menu` VALUES (5802, '发货单导出', 5800, 2, '', '', 1, 'F', '0', 'temu:ship-order:export', '#', '2025-05-16 15:01:18', '2025-05-16 15:01:18', '发货单导出按钮');
INSERT INTO `sys_menu` VALUES (5803, '发货单详情', 5800, 3, '', '', 1, 'F', '0', 'temu:ship-order:detail', '#', '2025-05-16 15:01:18', '2025-05-16 15:01:18', '发货单详情查看按钮');
INSERT INTO `sys_menu` VALUES (5900, '备货单同步', 5000, 5, 'purchase-order-sync', 'synchronous/purchase-order-sync/index', 1, 'C', '0', 'synchronous:purchase:list', 'Document', '2025-05-30 09:00:00', '2025-05-30 09:00:00', '备货单数据同步页面');
INSERT INTO `sys_menu` VALUES (5901, '数据同步查询', 5900, 1, '', NULL, 1, 'F', '0', 'synchronous:purchase:query', '#', '2025-05-30 09:00:00', '2025-05-30 09:00:00', '查询备货单数据同步任务列表');
INSERT INTO `sys_menu` VALUES (5902, '触发同步', 5900, 2, '', NULL, 1, 'F', '0', 'synchronous:purchase:trigger', '#', '2025-05-30 09:00:00', '2025-05-30 09:00:00', '手动触发备货单数据同步');
INSERT INTO `sys_menu` VALUES (5903, '初始化任务', 5900, 3, '', NULL, 1, 'F', '0', 'synchronous:purchase:init', '#', '2025-05-30 09:00:00', '2025-05-30 09:00:00', '初始化备货单数据同步任务');
INSERT INTO `sys_menu` VALUES (5904, '清空数据', 5900, 4, '', NULL, 1, 'F', '0', 'synchronous:purchase:clear', '#', '2025-05-30 09:00:00', '2025-05-30 09:00:00', '清空备货单同步数据');
INSERT INTO `sys_menu` VALUES (6000, '生产组管理', 0, 1, 'production', NULL, 1, 'M', '0', '', 'Document', '2025-05-09 11:45:16', '2025-05-12 14:19:24', '');
INSERT INTO `sys_menu` VALUES (6001, '生产组列表', 6000, 1, 'production-group', 'production/group/index', 1, 'C', '0', 'production:group:list', 'list', '2025-05-09 11:45:16', NULL, '');
INSERT INTO `sys_menu` VALUES (6002, '生产组新增', 6001, 1, '', '', 1, 'F', '0', 'production:group:add', '#', '2025-05-09 11:45:17', NULL, '');
INSERT INTO `sys_menu` VALUES (6003, '生产组修改', 6001, 2, '', '', 1, 'F', '0', 'production:group:edit', '#', '2025-05-09 11:45:17', NULL, '');
INSERT INTO `sys_menu` VALUES (6004, '生产组删除', 6001, 3, '', '', 1, 'F', '0', 'production:group:remove', '#', '2025-05-09 11:45:17', NULL, '');
INSERT INTO `sys_menu` VALUES (6005, '生产组查询', 6001, 4, '', '', 1, 'F', '0', 'production:group:query', '#', '2025-05-09 11:45:17', NULL, '');
INSERT INTO `sys_menu` VALUES (6010, '成员管理', 6000, 2, 'production-member', 'production/member/index', 1, 'C', '0', 'production:member:list', 'user', '2025-05-09 11:45:17', NULL, '');
INSERT INTO `sys_menu` VALUES (6011, '成员新增', 6010, 1, '', '', 1, 'F', '0', 'production:member:add', '#', '2025-05-09 11:45:17', NULL, '');
INSERT INTO `sys_menu` VALUES (6012, '成员删除', 6010, 2, '', '', 1, 'F', '0', 'production:member:remove', '#', '2025-05-09 11:45:17', NULL, '');
INSERT INTO `sys_menu` VALUES (6013, '角色分配', 6010, 3, '', '', 1, 'F', '0', 'production:role:assign', '#', '2025-05-12 00:00:00', NULL, '');
INSERT INTO `sys_menu` VALUES (6014, '角色列表查询', 6010, 4, '', '', 1, 'F', '0', 'production:role:list', '#', '2025-05-12 01:21:08', NULL, '生产角色列表查询按钮');
INSERT INTO `sys_menu` VALUES (6015, '生产组长查询', 6001, 5, '', '', 1, 'F', '0', 'production:leader:list', '#', '2025-06-03 16:37:00', NULL, '查询生产组长用户按钮');

SET FOREIGN_KEY_CHECKS = 1;
