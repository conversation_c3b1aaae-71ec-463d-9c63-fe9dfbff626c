<template>
  <el-dialog
    :title="title"
    v-model="visible"
    width="600px"
    append-to-body
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="店铺名称" prop="shopName">
        <el-input v-model="form.shopName" placeholder="请输入店铺名称" />
      </el-form-item>
      <el-form-item label="Temu店铺ID" prop="shopTemuId">
        <el-input v-model="form.shopTemuId" placeholder="请输入Temu平台店铺ID" />
      </el-form-item>
      
      <!-- API相关字段 -->
      <div class="api-fields-header">
        <h4>API设置</h4>
        <el-button type="primary" link size="small" @click="toggleAllApiFields">
          {{ isAnyApiFieldVisible ? '全部隐藏' : '全部显示' }}
        </el-button>
      </div>
      
      <el-form-item label="API密钥" prop="apiKey">
        <div class="api-field-container">
          <el-input 
            v-model="form.apiKey" 
            placeholder="请输入API密钥" 
            :prefix-icon="Key"
            :type="showApiKey ? 'text' : 'password'"
            class="api-input"
          />
          <el-button 
            class="toggle-visibility" 
            link 
            type="primary" 
            @click="showApiKey = !showApiKey"
          >
            <el-icon><component :is="showApiKey ? 'Hide' : 'View'" /></el-icon>
          </el-button>
        </div>
      </el-form-item>
      
      <el-form-item label="密钥Secret" prop="apiSecret">
        <div class="api-field-container">
          <el-input 
            v-model="form.apiSecret" 
            placeholder="请输入API密钥Secret" 
            :prefix-icon="Lock"
            :type="showApiSecret ? 'text' : 'password'"
            class="api-input"
          />
          <el-button 
            class="toggle-visibility" 
            link 
            type="primary" 
            @click="showApiSecret = !showApiSecret"
          >
            <el-icon><component :is="showApiSecret ? 'Hide' : 'View'" /></el-icon>
          </el-button>
        </div>
      </el-form-item>
      
      <el-form-item label="Access Token" prop="accessToken">
        <div class="api-field-container">
          <el-input 
            v-model="form.accessToken" 
            placeholder="请输入Access Token" 
            :prefix-icon="Connection"
            :type="showAccessToken ? 'text' : 'password'"
            class="api-input"
          />
          <el-button 
            class="toggle-visibility" 
            link 
            type="primary" 
            @click="showAccessToken = !showAccessToken"
          >
            <el-icon><component :is="showAccessToken ? 'Hide' : 'View'" /></el-icon>
          </el-button>
        </div>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio label="0">正常</el-radio>
          <el-radio label="1">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="店铺代号" prop="remark">
        <el-input
          v-model="form.remark"
          placeholder="请输入店铺代号"
          :rows="3"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { addShop, updateShop } from '@/api/shop'
import type { Shop } from '@/types/shop'
import { Key, Lock, Connection, View, Hide } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  shopId: {
    type: Number as () => number | null,
    default: null
  },
  shopData: {
    type: Object as () => Partial<Shop> | null,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 表单引用
const formRef = ref<FormInstance>()

// 对话框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 对话框标题
const title = computed(() => {
  return props.shopId ? '编辑店铺' : '新增店铺'
})

// 表单数据
const form = reactive<Shop>({
  shopId: null,
  shopName: '',
  shopTemuId: '',
  apiKey: '',
  apiSecret: '',
  accessToken: null,
  status: '0',
  createTime: null,
  updateTime: null,
  remark: null
})

// 运营组选项
const groupOptions = ref<any[]>([])

// API字段的显示/隐藏状态
const showApiKey = ref(false)
const showApiSecret = ref(false)
const showAccessToken = ref(false)

// 判断是否有任何API字段可见
const isAnyApiFieldVisible = computed(() => {
  return showApiKey.value || showApiSecret.value || showAccessToken.value
})

// 切换所有API字段的显示/隐藏状态
const toggleAllApiFields = () => {
  if (isAnyApiFieldVisible.value) {
    // 如果有任何字段可见，则全部隐藏
    showApiKey.value = false
    showApiSecret.value = false
    showAccessToken.value = false
  } else {
    // 否则全部显示
    showApiKey.value = true
    showApiSecret.value = true
    showAccessToken.value = true
  }
}

// 表单验证规则
const rules = reactive<FormRules>({
  shopName: [
    { required: true, message: '请输入店铺名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  shopTemuId: [
    { required: true, message: '请输入Temu平台店铺ID', trigger: 'blur' }
  ],
  apiKey: [
    { required: true, message: '请输入API密钥', trigger: 'blur' }
  ],
  apiSecret: [
    { required: true, message: '请输入API密钥Secret', trigger: 'blur' }
  ],
  remark: [
    { required: true, message: '请输入店铺代号', trigger: 'blur' }
  ]
})

// 监听shopData变化，填充表单
watch(
  () => props.shopData,
  (val) => {
    if (val) {
      console.log('ShopForm收到数据:', val) // 添加日志
      // 重置表单
      Object.assign(form, {
        shopId: null,
        shopName: '',
        shopTemuId: '',
        apiKey: '',
        apiSecret: '',
        accessToken: null,
        status: '0',
        createTime: null,
        updateTime: null,
        remark: null
      })
      
      // 将接收到的数据赋值给表单
      Object.keys(val).forEach(key => {
        if (key in form && val[key] !== undefined) {
          // @ts-ignore
          form[key] = val[key]
        }
      })
    }
  },
  { immediate: true, deep: true }
)

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (!valid) return
    
    try {
      // 准备提交数据，确保类型正确
      const submitData = { ...form }
      
      // 删除可能存在的groups和groupId字段，确保不会传递给后端
      delete (submitData as any).groupId
      delete (submitData as any).groups
      
      // 记录提交的数据
      console.log('提交数据:', submitData)
      
      const submitFunc = props.shopId ? updateShop : addShop
      const res: any = await submitFunc(submitData)
      
      if (res.code === 200) {
        ElMessage.success('操作成功')
        emit('success')
        visible.value = false
      } else {
        ElMessage.error(res.message || '操作失败')
      }
    } catch (error: any) {
      console.error('提交失败:', error)
      ElMessage.error(error.message || '操作失败')
    }
  })
}

// 关闭对话框时重置表单
const handleClose = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    shopId: null,
    shopName: '',
    shopTemuId: '',
    apiKey: '',
    apiSecret: '',
    accessToken: null,
    status: '0',
    createTime: null,
    updateTime: null,
    remark: null
  })
  
  // 重置API字段的显示状态
  showApiKey.value = false
  showApiSecret.value = false
  showAccessToken.value = false
}

onMounted(() => {
  // 不再自动加载运营组数据
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.api-fields-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.api-field-container {
  display: flex;
  align-items: center;
}

.toggle-visibility {
  margin-left: 10px;
}

.api-input {
  width: 430px;
}
</style> 