package com.xiao.temu.modules.production.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiao.temu.common.response.ApiResponse;
import com.xiao.temu.modules.production.service.ProductionGroupMemberService;
import com.xiao.temu.modules.production.service.ProductionGroupService;
import com.xiao.temu.modules.production.service.ProductionGroupRoleAssignmentService;
import com.xiao.temu.security.annotation.RequiresPermission;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.system.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产组成员控制器
 */
@Slf4j
@RestController
@RequestMapping("/production/member")
@RequiredArgsConstructor
public class ProductionGroupMemberController {
    private final ProductionGroupMemberService productionGroupMemberService;
    private final ProductionGroupService productionGroupService;
    private final UserService userService;
    private final ProductionGroupRoleAssignmentService roleAssignmentService;

    /**
     * 获取生产组成员列表
     */
    @GetMapping("/list/{groupId}")
    @RequiresPermission("production:member:list")
    public ApiResponse getMemberList(
            @PathVariable Long groupId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(value = "keyword", required = false) String keyword) {
        
        // 校验生产组是否存在
        if (!productionGroupService.existsById(groupId)) {
            return ApiResponse.error("生产组不存在");
        }
        
        // 检查当前用户是否有权限
        checkGroupPermission(groupId);
        
        // 查询成员列表
        Page<com.xiao.temu.modules.production.dto.ProductionGroupMemberDTO> page = new Page<>(pageNum, pageSize);
        return ApiResponse.success(productionGroupMemberService.getMemberList(groupId, page, keyword));
    }
    
    /**
     * 获取生产组所有成员（不分页）
     */
    @GetMapping("/all/{groupId}")
    @RequiresPermission("production:member:list")
    public ApiResponse getAllMembers(@PathVariable Long groupId) {
        // 校验生产组是否存在
        if (!productionGroupService.existsById(groupId)) {
            return ApiResponse.error("生产组不存在");
        }
        
        // 检查当前用户是否有权限
        checkGroupPermission(groupId);
        
        // 查询所有成员
        return ApiResponse.success(productionGroupMemberService.getAllMembers(groupId));
    }

    /**
     * 添加生产组成员
     */
    @PostMapping("/{groupId}/{userId}")
    @RequiresPermission("production:member:add")
    public ApiResponse addMember(@PathVariable Long groupId, @PathVariable Long userId) {
        // 校验生产组是否存在
        if (!productionGroupService.existsById(groupId)) {
            return ApiResponse.error("生产组不存在");
        }
        
        // 检查当前用户是否是该组的组长
        Long currentUserId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        if (!isAdmin && !productionGroupService.isGroupLeader(currentUserId, groupId)) {
            return ApiResponse.error("只有生产组组长才能添加成员");
        }
        
        // 检查用户是否已经是该组成员
        if (productionGroupMemberService.checkUserInGroup(groupId, userId)) {
            return ApiResponse.error("该用户已经是生产组成员");
        }
        
        // 添加成员
        int rows = productionGroupMemberService.addMember(groupId, userId);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("添加成员失败");
    }

    /**
     * 批量添加生产组成员
     */
    @PostMapping("/batch/{groupId}")
    @RequiresPermission("production:member:add")
    public ApiResponse batchAddMembers(@PathVariable Long groupId, @RequestBody List<Long> userIds) {
        // 校验生产组是否存在
        if (!productionGroupService.existsById(groupId)) {
            return ApiResponse.error("生产组不存在");
        }
        
        // 检查当前用户是否是该组的组长
        Long currentUserId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        if (!isAdmin && !productionGroupService.isGroupLeader(currentUserId, groupId)) {
            return ApiResponse.error("只有生产组组长才能添加成员");
        }
        
        // 批量添加成员
        int rows = productionGroupMemberService.batchAddMembers(groupId, userIds);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("批量添加成员失败");
    }

    /**
     * 删除生产组成员（从组中移除）
     */
    @DeleteMapping("/{groupId}/{userId}")
    @RequiresPermission("production:member:remove")
    public ApiResponse removeMember(@PathVariable Long groupId, @PathVariable Long userId) {
        // 校验生产组是否存在
        if (!productionGroupService.existsById(groupId)) {
            return ApiResponse.error("生产组不存在");
        }
        
        // 检查当前用户是否是该组的组长
        Long currentUserId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        if (!isAdmin && !productionGroupService.isGroupLeader(currentUserId, groupId)) {
            return ApiResponse.error("只有生产组组长才能删除成员");
        }
        
        // 检查是否删除的是组长自己
        if (productionGroupService.isGroupLeader(userId, groupId)) {
            return ApiResponse.error("不能删除生产组组长");
        }
        
        // 删除成员
        int rows = productionGroupMemberService.removeMember(groupId, userId);
        
        return rows > 0 ? ApiResponse.success() : ApiResponse.error("删除成员失败");
    }
    
    /**
     * 删除用户及其所有角色（彻底删除用户）
     */
    @DeleteMapping("/deleteUser/{groupId}/{userId}")
    @RequiresPermission("production:member:remove")
    public ApiResponse deleteUser(@PathVariable Long groupId, @PathVariable Long userId) {
        // 校验生产组是否存在
        if (!productionGroupService.existsById(groupId)) {
            return ApiResponse.error("生产组不存在");
        }
        
        // 检查当前用户是否是该组的组长
        Long currentUserId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        if (!isAdmin && !productionGroupService.isGroupLeader(currentUserId, groupId)) {
            return ApiResponse.error("只有生产组组长才能删除用户");
        }
        
        // 检查是否删除的是组长自己
        if (productionGroupService.isGroupLeader(userId, groupId)) {
            return ApiResponse.error("不能删除生产组组长");
        }
        
        // 确保用户是当前组的成员
        if (!productionGroupMemberService.checkUserInGroup(groupId, userId)) {
            return ApiResponse.error("该用户不是当前生产组的成员");
        }
        
        try {
            log.info("开始删除用户，用户ID: {}, 组ID: {}", userId, groupId);
            
            // 1. 删除用户的生产组角色分配
            boolean roleRemoved = roleAssignmentService.deleteUserRoles(groupId, userId);
            log.info("删除用户的生产组角色分配 - {}", roleRemoved ? "成功" : "失败或无角色");
            
            // 2. 从所有生产组中移除用户
            List<Long> userGroups = productionGroupMemberService.getGroupIdsByUserId(userId);
            for (Long gId : userGroups) {
                productionGroupMemberService.removeMember(gId, userId);
            }
            log.info("从所有生产组移除用户成功");
            
            // 3. 删除用户
            int rows = userService.deleteUser(userId);
            if (rows <= 0) {
                return ApiResponse.error("删除用户失败");
            }
            
            log.info("删除用户成功，用户ID: {}", userId);
            return ApiResponse.success();
        } catch (Exception e) {
            log.error("删除用户失败", e);
            return ApiResponse.error("删除用户失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查用户对生产组的权限
     * 
     * @param groupId 生产组ID
     */
    private void checkGroupPermission(Long groupId) {
        Long currentUserId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        // 如果是管理员账号直接返回
        if (isAdmin) {
            return;
        }
        
        // 如果不是管理员，则检查是否是该组的组长或成员
        boolean isLeader = productionGroupService.isGroupLeader(currentUserId, groupId);
        boolean isMember = productionGroupMemberService.checkUserInGroup(groupId, currentUserId);
        
        if (!isLeader && !isMember) {
            throw new RuntimeException("没有权限访问该生产组");
        }
    }

    /**
     * 获取可以添加到生产组的用户列表
     * 返回的用户应具有烧花、车缝、尾部、发货、送货这五个角色之一
     */
    @GetMapping("/available/{groupId}")
    @RequiresPermission("production:member:add")
    public ApiResponse getAvailableUsers(
            @PathVariable Long groupId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(value = "keyword", required = false) String keyword) {
        
        // 校验生产组是否存在
        if (!productionGroupService.existsById(groupId)) {
            return ApiResponse.error("生产组不存在");
        }
        
        // 检查当前用户是否有权限
        checkGroupPermission(groupId);
        
        // 查询可用用户列表
        Page<com.xiao.temu.modules.system.entity.SysUser> page = new Page<>(pageNum, pageSize);
        return ApiResponse.success(productionGroupMemberService.getAvailableUsers(groupId, page, keyword));
    }

    /**
     * 组长创建用户并添加到生产组
     * 简化组长直接创建新用户的流程
     */
    @PostMapping("/create/{groupId}")
    @RequiresPermission("production:member:add")
    public ApiResponse createUserAndAddToGroup(
            @PathVariable Long groupId, 
            @RequestBody com.xiao.temu.modules.production.dto.CreateUserDTO userDTO) {
        
        // 校验生产组是否存在
        if (!productionGroupService.existsById(groupId)) {
            return ApiResponse.error("生产组不存在");
        }
        
        // 检查当前用户是否是该组的组长
        Long currentUserId = com.xiao.temu.security.utils.SecurityUtils.getCurrentUserId();
        boolean isAdmin = com.xiao.temu.security.utils.SecurityUtils.isAdmin();
        
        if (!isAdmin && !productionGroupService.isGroupLeader(currentUserId, groupId)) {
            return ApiResponse.error("只有生产组组长才能创建用户并添加到生产组");
        }
        
        // 从DTO提取用户基本信息
        SysUser user = new SysUser();
        user.setUsername(userDTO.getUsername());
        user.setNickName(userDTO.getNickName());
        user.setPhone(userDTO.getPhone());
        user.setEmail(userDTO.getEmail());
        user.setPassword(userDTO.getPassword());
        user.setStatus(userDTO.getStatus() != null ? userDTO.getStatus() : "0"); // 默认状态为正常
        user.setRemark(userDTO.getRemark());
        
        // 记录角色ID
        List<Long> roleIds = userDTO.getRoleIds();
        log.info("创建用户时指定的角色IDs: {}", roleIds);
        
        // 校验用户名是否唯一
        if (!userService.checkUsernameUnique(user.getUsername())) {
            return ApiResponse.error("新增用户'" + user.getUsername() + "'失败，登录账号已存在");
        }
        
        try {
            // 1. 创建用户
            int rows = userService.insertUser(user);
            if (rows <= 0) {
                return ApiResponse.error("创建用户失败");
            }
            
            Long userId = user.getUserId();
            log.info("用户创建成功，用户ID: {}", userId);
            
            // 2. 如果有指定角色，分配角色给用户
            if (roleIds != null && !roleIds.isEmpty()) {
                // 使用系统角色分配（添加到sys_user_role表）
                try {
                    Long[] roleIdsArray = roleIds.toArray(new Long[0]);
                    log.info("分配系统角色，角色数量: {}, 角色IDs: {}", roleIdsArray.length, roleIdsArray);
                    rows = userService.assignRoles(userId, roleIdsArray);
                    if (rows <= 0) {
                        log.warn("系统角色分配失败，userId: {}, roleIds: {}", userId, roleIds);
                    } else {
                        log.info("系统角色分配成功");
                    }
                } catch (Exception e) {
                    log.error("系统角色分配异常", e);
                }
                
                // 使用生产组角色分配（添加到production_group_role_assignment表）
                try {
                    log.info("开始分配生产组角色，角色数量: {}, 角色IDs: {}", roleIds.size(), roleIds);
                    boolean success = roleAssignmentService.batchAssignRoles(groupId, userId, roleIds, currentUserId);
                    if (!success) {
                        log.warn("生产组角色分配失败，groupId: {}, userId: {}, roleIds: {}", groupId, userId, roleIds);
                    } else {
                        log.info("生产组角色分配成功");
                    }
                } catch (Exception e) {
                    log.error("生产组角色分配异常", e);
                    // 继续执行，不要因为角色分配失败就中断整个流程
                }
            } else {
                log.info("没有指定角色，跳过角色分配");
            }
            
            // 3. 将用户添加到生产组
            rows = productionGroupMemberService.addMember(groupId, userId);
            if (rows <= 0) {
                return ApiResponse.error("将用户添加到生产组失败");
            }
            
            log.info("用户成功添加到生产组, groupId: {}, userId: {}", groupId, userId);
            return ApiResponse.success(userId);
        } catch (Exception e) {
            log.error("创建用户并添加到生产组失败", e);
            return ApiResponse.error("创建用户并添加到生产组失败: " + e.getMessage());
        }
    }
} 