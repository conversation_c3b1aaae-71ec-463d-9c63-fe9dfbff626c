package com.xiao.temu.modules.sync.controller;

import com.xiao.temu.modules.shop.entity.Shop;
import com.xiao.temu.modules.shop.service.ShopService;
import com.xiao.temu.modules.sync.dto.PurchaseOrderSyncDTO;
import com.xiao.temu.modules.sync.entity.PurchaseOrderSyncTask;
import com.xiao.temu.modules.sync.service.PurchaseOrderSyncService;
import com.xiao.temu.modules.sync.vo.ApiResponse;
import com.xiao.temu.modules.system.entity.SysUser;
import com.xiao.temu.modules.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 备货单同步控制器
 */
@Slf4j
@RestController
@RequestMapping("/temu/purchaseOrder/sync")
public class PurchaseOrderSyncController {

    @Autowired
    private PurchaseOrderSyncService purchaseOrderSyncService;

    @Autowired
    private ShopService shopService;

    @Autowired
    private UserService userService;

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        // 通过用户名查询用户信息获取用户ID
        SysUser user = userService.getUserByUsername(username);
        if (user != null) {
            return user.getUserId();
        }
        throw new RuntimeException("用户未找到: " + username);
    }
    
    /**
     * 检查用户是否为管理员
     */
    private boolean isAdmin(Long userId) {
        return userService.isAdmin(userId);
    }

    /**
     * 获取同步任务列表
     *
     * @param syncDTO 同步请求参数
     * @return API响应
     */
    @PostMapping("/list")
    public ApiResponse getSyncTasks(@RequestBody PurchaseOrderSyncDTO syncDTO) {
        List<Long> shopIds = syncDTO.getShopIds();
        Long userId = getCurrentUserId();
        
        // 判断是否为管理员
        boolean isAdmin = isAdmin(userId);
        
        // 获取可访问的店铺列表
        List<Shop> accessibleShops;
        if (isAdmin) {
            // 管理员可以访问所有店铺
            accessibleShops = shopService.listAllShops();
        } else {
            // 普通用户只能访问有权限的店铺
            accessibleShops = shopService.listUserShops(userId);
        }
        
        // 创建店铺ID到详细信息的映射
        Map<Long, Shop> shopIdToInfoMap = accessibleShops.stream()
                .collect(Collectors.toMap(Shop::getShopId, shop -> shop));
        
        if (shopIds != null && !shopIds.isEmpty()) {
            shopIds = shopIds.stream()
                    .filter(shopIdToInfoMap::containsKey)
                    .collect(Collectors.toList());
        } else {
            shopIds = accessibleShops.stream()
                    .map(Shop::getShopId)
                    .collect(Collectors.toList());
        }
        
        if (shopIds.isEmpty()) {
            return ApiResponse.error("没有可访问的店铺");
        }
        
        List<PurchaseOrderSyncTask> tasks = purchaseOrderSyncService.getSyncTasks(shopIds);
        List<ApiResponse.PurchaseOrderSyncVO> vos = new ArrayList<>();
        
        for (PurchaseOrderSyncTask task : tasks) {
            Shop shop = shopIdToInfoMap.get(task.getShopId());
            if (shop != null) {
                ApiResponse.PurchaseOrderSyncVO vo = ApiResponse.PurchaseOrderSyncVO.fromTask(task, shop.getShopName());
                // 设置店铺备注
                vo.setShopRemark(shop.getRemark());
                vos.add(vo);
            }
        }

        // 构建分页结果
        Map<String, Object> result = new HashMap<>();
        result.put("list", vos);
        result.put("total", vos.size());
        
        return ApiResponse.success(result);
    }
    
    /**
     * 获取单个同步任务详情
     *
     * @param shopId 店铺ID
     * @return API响应
     */
    @GetMapping("/status/{shopId}")
    public ApiResponse getSyncTask(@PathVariable("shopId") Long shopId) {
        Long userId = getCurrentUserId();
        
        // 管理员或有权限的用户可以查看任务状态
        if (!isAdmin(userId) && !shopService.checkShopPermission(userId, shopId, false)) {
            return ApiResponse.error(403, "没有权限访问该店铺");
        }
        
        PurchaseOrderSyncTask task = purchaseOrderSyncService.getSyncTaskByShopId(shopId);
        if (task == null) {
            return ApiResponse.error("同步任务不存在");
        }
        
        Shop shop = shopService.getShopById(shopId).convertToShop();
        ApiResponse.PurchaseOrderSyncVO vo = ApiResponse.PurchaseOrderSyncVO.fromTask(task, shop.getShopName());
        
        // 设置店铺备注
        vo.setShopRemark(shop.getRemark());
        
        return ApiResponse.success(vo);
    }
    
    /**
     * 同步指定店铺的备货单数据
     *
     * @param shopId 店铺ID
     * @return API响应
     */
    @PostMapping("/execute/{shopId}")
    public ApiResponse syncPurchaseOrderData(@PathVariable("shopId") Long shopId) {
        Long userId = getCurrentUserId();
        
        // 管理员或有权限的用户可以执行同步操作
        if (!isAdmin(userId) && !shopService.checkShopPermission(userId, shopId, false)) {
            return ApiResponse.error(403, "没有权限访问该店铺");
        }
        
        // 获取店铺信息
        Shop shop = shopService.getShopById(shopId).convertToShop();
        if (shop == null) {
            return ApiResponse.error("店铺不存在");
        }
        
        // 执行同步
        ApiResponse.PurchaseOrderSyncVO result = purchaseOrderSyncService.syncPurchaseOrderData(shopId);
        
        // 设置店铺备注
        result.setShopRemark(shop.getRemark());
        
        return ApiResponse.success(result);
    }
    
    /**
     * 清空店铺的同步数据
     * 注意：此操作会从数据库中永久删除备货单数据
     *
     * @param shopId 店铺ID
     * @return API响应
     */
    @PostMapping("/clear/{shopId}")
    public ApiResponse clearSyncData(@PathVariable("shopId") Long shopId) {
        Long userId = getCurrentUserId();
        
        // 管理员或有写权限的用户可以执行清空操作
        if (!isAdmin(userId) && !shopService.checkShopPermission(userId, shopId, true)) {
            return ApiResponse.error(403, "没有权限执行此操作");
        }
        
        boolean success = purchaseOrderSyncService.clearSyncData(shopId);
        if (success) {
            return ApiResponse.success("清空备货单同步数据成功");
        } else {
            return ApiResponse.error("清空备货单同步数据失败");
        }
    }
    
    /**
     * 获取备货单数量
     *
     * @param shopId 店铺ID
     * @param type 备货单类型（0:普通备货 1:JIT备货 null:全部）
     * @return API响应
     */
    @GetMapping("/count/{shopId}")
    public ApiResponse getPurchaseOrderCount(@PathVariable("shopId") Long shopId, 
                                           @RequestParam(value = "type", required = false) Integer type) {
        Long userId = getCurrentUserId();
        
        // 管理员或有权限的用户可以查看备货单数量
        if (!isAdmin(userId) && !shopService.checkShopPermission(userId, shopId, false)) {
            return ApiResponse.error(403, "没有权限访问该店铺");
        }
        
        int count = purchaseOrderSyncService.getPurchaseOrderCountByShopId(shopId, type);
        return ApiResponse.success(count);
    }
    
    /**
     * 初始化店铺同步任务
     *
     * @param shopId 店铺ID
     * @return API响应
     */
    @PostMapping("/init/{shopId}")
    public ApiResponse initSyncTask(@PathVariable("shopId") Long shopId) {
        Long userId = getCurrentUserId();
        
        // 管理员或有写权限的用户可以执行初始化操作
        if (!isAdmin(userId) && !shopService.checkShopPermission(userId, shopId, true)) {
            return ApiResponse.error(403, "没有权限执行此操作");
        }
        
        boolean success = purchaseOrderSyncService.initSyncTask(shopId);
        if (success) {
            return ApiResponse.success("初始化备货单同步任务成功");
        } else {
            return ApiResponse.error("初始化备货单同步任务失败");
        }
    }

    /**
     * 批量初始化同步任务
     *
     * @param syncDTO 同步请求参数
     * @return API响应
     */
    @PostMapping("/batch/init")
    public ApiResponse batchInitSyncTasks(@RequestBody PurchaseOrderSyncDTO syncDTO) {
        Long userId = getCurrentUserId();
        
        // 获取要初始化的店铺ID列表
        List<Long> shopIds = syncDTO.getShopIds();
        if (shopIds == null || shopIds.isEmpty()) {
            return ApiResponse.error(400, "请选择要初始化的店铺");
        }
        
        // 获取有访问权限的店铺
        List<Long> authorizedShopIds;
        if (isAdmin(userId)) {
            // 管理员可以初始化所有店铺
            authorizedShopIds = new ArrayList<>(shopIds);
        } else {
            // 非管理员需要过滤有写权限的店铺
            authorizedShopIds = shopIds.stream()
                    .filter(shopId -> shopService.checkShopPermission(userId, shopId, true))
                    .collect(Collectors.toList());
        }
        
        if (authorizedShopIds.isEmpty()) {
            return ApiResponse.error(403, "没有操作权限");
        }
        
        // 批量初始化
        int successCount = 0;
        int failCount = 0;
        
        for (Long shopId : authorizedShopIds) {
            try {
                boolean success = purchaseOrderSyncService.initSyncTask(shopId);
                if (success) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                log.error("批量初始化店铺{}失败: {}", shopId, e.getMessage(), e);
                failCount++;
            }
        }
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("totalCount", authorizedShopIds.size());
        
        if (failCount == 0) {
            return ApiResponse.success(result);
        } else {
            return ApiResponse.success("部分店铺初始化失败", result);
        }
    }

    /**
     * 批量触发同步
     *
     * @param syncDTO 同步请求参数
     * @return API响应
     */
    @PostMapping("/batch/execute")
    public ApiResponse batchTriggerSync(@RequestBody PurchaseOrderSyncDTO syncDTO) {
        Long userId = getCurrentUserId();
        
        // 获取要同步的店铺ID列表
        List<Long> shopIds = syncDTO.getShopIds();
        if (shopIds == null || shopIds.isEmpty()) {
            return ApiResponse.error(400, "请选择要同步的店铺");
        }
        
        // 获取有访问权限的店铺
        List<Long> authorizedShopIds;
        if (isAdmin(userId)) {
            // 管理员可以同步所有店铺
            authorizedShopIds = new ArrayList<>(shopIds);
        } else {
            // 非管理员需要过滤有权限的店铺
            authorizedShopIds = shopIds.stream()
                    .filter(shopId -> shopService.checkShopPermission(userId, shopId, false))
                    .collect(Collectors.toList());
        }
        
        if (authorizedShopIds.isEmpty()) {
            return ApiResponse.error(403, "没有操作权限");
        }
        
        // 执行批量同步
        int successCount = 0;
        List<Long> syncingShopIds = new ArrayList<>();
        
        for (Long shopId : authorizedShopIds) {
            try {
                // 启动同步
                ApiResponse.PurchaseOrderSyncVO result = purchaseOrderSyncService.syncPurchaseOrderData(shopId);
                if (result.getSuccess()) {
                    successCount++;
                }
                syncingShopIds.add(shopId);
            } catch (Exception e) {
                log.error("批量同步店铺{}失败: {}", shopId, e.getMessage(), e);
            }
        }
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("successCount", successCount);
        result.put("totalCount", authorizedShopIds.size());
        result.put("message", "已启动批量同步任务");
        
        return ApiResponse.success(result);
    }

    /**
     * 批量清空同步数据
     * 注意：此操作会从数据库中永久删除备货单数据
     *
     * @param syncDTO 同步请求参数
     * @return API响应
     */
    @PostMapping("/batch/clear")
    public ApiResponse batchClearSyncData(@RequestBody PurchaseOrderSyncDTO syncDTO) {
        Long userId = getCurrentUserId();
        
        // 获取要清空的店铺ID列表
        List<Long> shopIds = syncDTO.getShopIds();
        if (shopIds == null || shopIds.isEmpty()) {
            return ApiResponse.error(400, "请选择要清空数据的店铺");
        }
        
        // 获取有访问权限的店铺
        List<Long> authorizedShopIds;
        if (isAdmin(userId)) {
            // 管理员可以清空所有店铺数据
            authorizedShopIds = new ArrayList<>(shopIds);
        } else {
            // 非管理员需要过滤有写权限的店铺
            authorizedShopIds = shopIds.stream()
                    .filter(shopId -> shopService.checkShopPermission(userId, shopId, true))
                    .collect(Collectors.toList());
        }
        
        if (authorizedShopIds.isEmpty()) {
            return ApiResponse.error(403, "没有操作权限");
        }
        
        // 批量清空数据
        int successCount = 0;
        int failCount = 0;
        
        for (Long shopId : authorizedShopIds) {
            try {
                boolean success = purchaseOrderSyncService.clearSyncData(shopId);
                if (success) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                log.error("批量清空店铺{}数据失败: {}", shopId, e.getMessage(), e);
                failCount++;
            }
        }
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("totalCount", authorizedShopIds.size());
        
        if (failCount == 0) {
            return ApiResponse.success(result);
        } else {
            return ApiResponse.success("部分店铺清空数据失败", result);
        }
    }
} 