package com.xiao.temu.modules.product.dto;

import java.util.List;

/**
 * 商品上新统计查询条件DTO
 */
public class ProductNewArrivalStatsQueryDTO {
    
    /**
     * 商城ID
     */
    private Long mallId;
    
    /**
     * 店铺ID列表，为空则查询所有店铺
     */
    private List<Long> shopIds;
    
    /**
     * 数据权限SQL
     */
    private String data_scope;
    
    /**
     * 排序字段
     */
    private String orderByColumn;
    
    /**
     * 排序方式（asc/desc）
     */
    private String orderByType;

    public Long getMallId() {
        return mallId;
    }

    public void setMallId(Long mallId) {
        this.mallId = mallId;
    }

    public List<Long> getShopIds() {
        return shopIds;
    }

    public void setShopIds(List<Long> shopIds) {
        this.shopIds = shopIds;
    }
    
    public String getData_scope() {
        return data_scope;
    }

    public void setData_scope(String data_scope) {
        this.data_scope = data_scope;
    }
    
    public String getOrderByColumn() {
        return orderByColumn;
    }
    
    public void setOrderByColumn(String orderByColumn) {
        this.orderByColumn = orderByColumn;
    }
    
    public String getOrderByType() {
        return orderByType;
    }
    
    public void setOrderByType(String orderByType) {
        this.orderByType = orderByType;
    }
} 