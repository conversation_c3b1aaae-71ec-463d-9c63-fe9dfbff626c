/**
 * 抽检结果明细查询请求参数
 */
export interface QualityInspectionRequestParams {
  shopId?: number
  qcResultUpdateTimeBegin?: number
  qcResultUpdateTimeEnd?: number
  pageNo: number
  pageSize: number
  skuIdList: number[]
  skcIdList: number[]
  purchaseNo: string[]
  skuQcResult?: number
}

/**
 * 抽检结果明细数据
 */
export interface QualityInspectionDetail {
  productSkuId: number
  productSkcId: number
  thumbUrl: string
  mainSaleSpec: string
  secondarySaleSpec: string
  purchaseNo: string
  skuQcResult: number
  qcResultUpdateTimeBegin: number
  qcResultUpdateTimeEnd: number
}

/**
 * 抽检结果明细分页数据
 */
export interface QualityInspectionResult {
  total: number
  list: QualityInspectionDetail[]
}

/**
 * 抽检结果明细响应
 */
export interface QualityInspectionResponse {
  success: boolean
  errorCode?: string
  errorMsg?: string
  result?: QualityInspectionResult
} 