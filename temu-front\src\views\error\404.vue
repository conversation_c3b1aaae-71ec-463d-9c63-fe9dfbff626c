<template>
  <div class="error-container">
    <div class="error-content">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <div class="pic-404">
            <img class="pic-404__parent" src="@/assets/404_images/404.svg" alt="404">
            <img class="pic-404__child left" src="@/assets/404_images/404_cloud.svg" alt="404">
            <img class="pic-404__child mid" src="@/assets/404_images/404_cloud.svg" alt="404">
            <img class="pic-404__child right" src="@/assets/404_images/404_cloud.svg" alt="404">
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <div class="bullshit">
            <div class="bullshit__headline">抱歉，您访问的页面不存在</div>
            <div class="bullshit__info">
              请检查您输入的网址是否正确，或点击下方按钮返回首页
            </div>
            <router-link to="/" class="bullshit__return-home">
              <el-button type="primary">返回首页</el-button>
            </router-link>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
// 404页面
</script>

<style lang="scss" scoped>
.error-container {
  width: 100%;
  height: 100%;
  background: #f0f2f5;

  .error-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .pic-404 {
    position: relative;
    width: 100%;
    padding-top: 100px;
    overflow: hidden;

    &__parent {
      width: 100%;
    }

    &__child {
      position: absolute;
      width: 80px;

      &.left {
        top: 17px;
        left: 15px;
        opacity: 0;
        animation-name: cloudLeft;
        animation-duration: 2s;
        animation-timing-function: linear;
        animation-delay: 1s;
        animation-fill-mode: forwards;
      }

      &.mid {
        top: 10px;
        left: 30%;
        opacity: 0;
        animation-name: cloudMid;
        animation-duration: 2s;
        animation-timing-function: linear;
        animation-delay: 1.2s;
        animation-fill-mode: forwards;
      }

      &.right {
        top: 100px;
        right: 15px;
        opacity: 0;
        animation-name: cloudRight;
        animation-duration: 2s;
        animation-timing-function: linear;
        animation-delay: 1s;
        animation-fill-mode: forwards;
      }

      @keyframes cloudLeft {
        0% {
          top: 17px;
          left: 15px;
          opacity: 0;
        }
        20% {
          top: 33px;
          left: 55px;
          opacity: 1;
        }
        80% {
          top: 81px;
          left: 105px;
          opacity: 1;
        }
        100% {
          top: 97px;
          left: 155px;
          opacity: 0;
        }
      }

      @keyframes cloudMid {
        0% {
          top: 10px;
          left: 30%;
          opacity: 0;
        }
        20% {
          top: 40px;
          left: 35%;
          opacity: 1;
        }
        70% {
          top: 130px;
          left: 40%;
          opacity: 1;
        }
        100% {
          top: 180px;
          left: 45%;
          opacity: 0;
        }
      }

      @keyframes cloudRight {
        0% {
          top: 100px;
          right: 15px;
          opacity: 0;
        }
        20% {
          top: 120px;
          right: 55px;
          opacity: 1;
        }
        70% {
          top: 180px;
          right: 95px;
          opacity: 1;
        }
        100% {
          top: 230px;
          right: 135px;
          opacity: 0;
        }
      }
    }
  }

  .bullshit {
    position: relative;
    padding: 30px 0;
    overflow: hidden;

    &__headline {
      font-size: 20px;
      line-height: 24px;
      color: #1f2d3d;
      font-weight: bold;
      margin-bottom: 10px;
    }

    &__info {
      font-size: 13px;
      line-height: 21px;
      color: #808080;
      margin-bottom: 30px;
    }

    &__return-home {
      display: block;
      float: left;
    }
  }
}
</style> 