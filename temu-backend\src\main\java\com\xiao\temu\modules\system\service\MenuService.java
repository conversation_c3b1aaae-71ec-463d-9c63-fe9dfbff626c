package com.xiao.temu.modules.system.service;

import com.xiao.temu.modules.system.entity.SysMenu;

import java.util.List;

/**
 * 菜单服务接口
 */
public interface MenuService {

    /**
     * 根据菜单ID查询菜单
     *
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    SysMenu getMenuById(Long menuId);

    /**
     * 查询菜单列表
     *
     * @param menu 菜单查询条件
     * @return 菜单列表
     */
    List<SysMenu> getMenuList(SysMenu menu);

    /**
     * 查询所有菜单
     *
     * @return 菜单列表
     */
    List<SysMenu> getAllMenus();

    /**
     * 根据用户ID查询菜单
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<SysMenu> getMenusByUserId(Long userId);

    /**
     * 根据角色ID查询菜单
     *
     * @param roleId 角色ID
     * @return 菜单列表
     */
    List<SysMenu> getMenusByRoleId(Long roleId);

    /**
     * 构建菜单树
     *
     * @param menus 菜单列表
     * @return 菜单树
     */
    List<SysMenu> buildMenuTree(List<SysMenu> menus);

    /**
     * 新增菜单
     *
     * @param menu 菜单信息
     * @return 结果
     */
    int insertMenu(SysMenu menu);

    /**
     * 修改菜单
     *
     * @param menu 菜单信息
     * @return 结果
     */
    int updateMenu(SysMenu menu);

    /**
     * 删除菜单
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    int deleteMenu(Long menuId);

    /**
     * 检查菜单是否有子菜单
     *
     * @param menuId 菜单ID
     * @return 结果 true-有 false-没有
     */
    boolean hasChildMenu(Long menuId);

    /**
     * 检查菜单是否已分配给角色
     *
     * @param menuId 菜单ID
     * @return 结果 true-已分配 false-未分配
     */
    boolean isMenuAssigned(Long menuId);
} 