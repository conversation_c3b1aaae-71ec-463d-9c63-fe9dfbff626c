<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xiao.temu.modules.sync.mapper.ProductSyncTaskMapper">
    
    <!-- 根据店铺ID列表获取同步任务 -->
    <select id="getByShopIds" resultType="com.xiao.temu.modules.sync.entity.ProductSyncTask">
        SELECT *
        FROM product_sync_task
        WHERE shop_id IN
        <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        ORDER BY last_sync_time DESC
    </select>
    
    <!-- 清空时间字段 -->
    <update id="clearTimeFields" parameterType="java.util.Map">
        UPDATE product_sync_task
        SET last_sync_time = NULL,
            last_update_time = NULL,
            sync_status = 0,
            error_message = NULL,
            total_records = 0,
            sku_total_records = 0,
            update_time = NOW()
        WHERE shop_id = #{shopId}
    </update>
    
</mapper> 