package com.xiao.temu.modules.sync.service;

import com.xiao.temu.modules.sync.entity.RefundPackageSyncTask;
import com.xiao.temu.modules.sync.vo.ApiResponse;

import java.util.List;

/**
 * 退货包裹数据同步服务接口
 */
public interface RefundPackageSyncService {

    /**
     * 同步指定店铺的退货包裹数据
     *
     * @param shopId 店铺ID
     * @return 同步结果
     */
    ApiResponse.RefundPackageSyncVO syncRefundPackageData(Long shopId);

    /**
     * 初始化店铺的同步任务
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    boolean initSyncTask(Long shopId);

    /**
     * 获取同步任务列表
     *
     * @param shopIds 店铺ID列表，为空则查询所有
     * @return 同步任务列表
     */
    List<RefundPackageSyncTask> getSyncTasks(List<Long> shopIds);

    /**
     * 根据店铺ID获取同步任务
     *
     * @param shopId 店铺ID
     * @return 同步任务
     */
    RefundPackageSyncTask getSyncTaskByShopId(Long shopId);

    /**
     * 执行定时同步
     * 
     * @return 同步结果
     */
    String executeScheduledSync();

    /**
     * 获取指定店铺的退货包裹记录总数
     *
     * @param shopId 店铺ID
     * @return 退货包裹记录总数
     */
    Integer getRefundPackageCountByShopId(Long shopId);

    /**
     * 清空店铺的同步数据
     *
     * @param shopId 店铺ID
     * @return 是否成功
     */
    boolean clearSyncData(Long shopId);
} 